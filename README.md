# Run with Docker

## Prerequisite
- Docker and docker compose must be installed in the machine.

## DEV
- To run: `docker-compose up dsr-api-dev`
- To stop: `Ctrl + C` or `Command + C`

## PROD
- To run: `docker-compose up dsr-api-prod`
- To stop: `Ctrl + C` or `Command + C`

## Note
```yml
volumes:
  mongo_data:
    external: true
  redis_data:
    external: true
```
- To use volumes outside of container, must set `external` is `true`
- Create volumes: `docker volume create mongo_data` and `docker volume create redis_data`
- To show volumes: `docker volume ls`
