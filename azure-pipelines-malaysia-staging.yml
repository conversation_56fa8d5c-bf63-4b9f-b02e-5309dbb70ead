trigger:
  - malaysia/staging

pool:
  vmImage: ubuntu-latest

variables:
  - group: GIT

jobs:
  - deployment: VMDeploy
    displayName: Deploy to VM
    environment:
      name: 20_212_53_4
      resourceType: VirtualMachine
      tags: UMenuBackendQA-DSR
    strategy:
      runOnce:
        deploy:
          steps:
            - task: NodeTool@0
              inputs:
                versionSpec: "20.x"
              displayName: "Install node version 20.x"
            - script: |
                echo "Deploy malaysia/staging"
                cd /datadrive/www/DSR_BE_Malaysia_Staging
                node -v
                git remote set-url origin https://$(API_GIT_TOKEN)@dev.azure.com/heineken/DSR%20App/_git/DSR%20-%20Backend
                git remote prune origin
                git pull https://$(API_GIT_TOKEN)@dev.azure.com/heineken/DSR%20App/_git/DSR%20-%20Backend
                git branch
                git checkout malaysia/staging
                git fetch --all
                git reset --hard origin/malaysia/staging
                git pull
                npm install --production
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                npm run build
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                pm2 reload ecosystem.config.js --only DSR_BE_Malaysia_Staging
              displayName: Run Script in VM
