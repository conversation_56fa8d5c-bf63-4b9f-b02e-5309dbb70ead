trigger:
  tags:
    include:
      - 'kh/*'

pool:
  vmImage: 'ubuntu-latest'

variables:
  - group: hei-rep-cambodia

steps:
  # 1. Extract Tag Name from Build.SourceBranch
  - script: |
      if [[ "$(Build.SourceBranch)" == refs/tags/kh/* ]]; then
        TAG_NAME=${BUILD_SOURCEBRANCH#refs/tags/kh/}
        echo "Extracted Docker tag: $TAG_NAME"
        echo "##vso[task.setvariable variable=imageTag]$TAG_NAME"
      else
        echo "Pipeline was not triggered by a matching tag. Exiting."
        exit 1
      fi
    displayName: 'Extract Git Tag for Docker'

  # 2. Build Docker Image with Extracted Tag
  - task: Docker@2
    displayName: 'Build Docker image'
    inputs:
      repository: $(imageName)
      command: build
      Dockerfile: Dockerfile
      tags: $(imageTag)

  # 3. Save Docker Image to tar file
  - script: |
      docker save -o $(Build.ArtifactStagingDirectory)/$(imageName).tar $(imageName):$(imageTag)
    displayName: 'Save Docker image as tar file'

  # 4. Publish tar file as Build Artifact
  - task: PublishBuildArtifacts@1
    inputs:
      artifactName: docker-images
      targetPath: $(Build.ArtifactStagingDirectory)
      publishLocation: 'Container'
    displayName: 'Publish Docker image tar file to Artifacts'
