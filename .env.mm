NODE_ENV=develop
DEFAULT_LANGUAGE=en
DEBUG_MODE=true
PORT=8007
APP_TIME_OUT=300000
JWT_PRIVATE_KEY=dsr_20220718
EXPIRED_TIME_REMEMBER_LOGGED_IN=3d
JWT_EXPIRED_TIME=1d
MONGODB_URL=****************************************
MONGO_DB_NAME="dsr-mm-development"
#MONGODB_URL=***********************************************************************************************************************************************************************************************************************************************
#MONGO_DB_NAME="dsr_cosmosdb_cam_production"

MONGO_DB_USER=
MONGO_DB_PASS=


#REDIS
CACHE_MODE=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=UMenu@2022
REDIS_TTL=36000

TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=1ec6f72a06ab54d82eef77ed23892c68
TWILIO_PHONE_NUMBER=+***********

# JOB EXPRESSION
MISSED_VISIT_OUTLET_EXPRESSION=59 59 23 * * *
#CREATE_STATISTIC_SALES_EXPRESSION=50 59 */1 * * *
CREATE_STATISTIC_SALES_EXPRESSION=*/10 * * * *
DELETE_EXPIRED_FILES_EXPRESSION=0 0 * * *
CLONE_JOURNEY_PLAN_EXPRESSION=0 0 0 * * *
CRON_CACHE_OMS=0 0 */12 * * *
GENARATE_CYCLE_TIME=0 0 1 12 *
TIME_CACHE_OMS=600 #seconds

# APP INSIGHTS
APPINSIGHTS_CONNECTIONSTRING=

#Mail Service
SENDGRID_API_KEY=
SENDGRID_SENDER_EMAIL=<EMAIL>

OPCO=MM
BASE_URL=https://hei-rep-qa-mm-api-d-sa.niteco.dev
BASE_WEB_URL=https://hei-rep-qa-mm-d-sa.niteco.dev
OPEN_APP_LINK=https://hei-rep-qa-mm-d-sa.niteco.dev/open-app.html
MONGODB_URL=****************************************/
TZ=Asia/Yangon
COUNTRY_CODE=MM
PHONE_COUNTRY_CODE_DEFAULT=+95
PHONE_COUNTRY_CODES=MM|+95,VN|+84,SG|+65

FIREBASE_PROJECT_ID=rep-myanmar-qa-acc
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>

GOOGLE_MAPS_API_KEY=AIzaSyADz5g1IPhCKemKmEaXkBhYf_2Ajx0koqg
OMS_API_BASE_URL=https://hei-oms-apac-qa-mm-backend.azurewebsites.net
OMS_EMAIL=<EMAIL>
OMS_PASSWORD=P@ssword1234
AZURE_S3_CONNECTION="DefaultEndpointsProtocol=https;AccountName=rep2camqastorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
APPINSIGHTS_CONNECTIONSTRING=InstrumentationKey=df6e10b4-ed52-4a02-a1e8-c917a8b2606e;IngestionEndpoint=https://eastasia-0.in.applicationinsights.azure.com/;LiveEndpoint=https://eastasia.livediagnostics.monitor.azure.com/;ApplicationId=9dcaa613-2898-4cc1-b57b-774d135599a5
LOCATION_RANGE_CONFIG=100
MUST_HAVE_SKU_EXCEL_URL=https://rep2camqastorage.blob.core.windows.net/files/2024/MustHaveSKU-Template.xlsx
CONFIG_EXCEL_URL=https://rep2camqastorage.blob.core.windows.net/files/2024/Config-Template.xlsx

SOLACE_URL=tcps://heineken-test.messaging.solace.cloud:55443
SOLACE_VPN_NAME=heineken-test
SOLACE_USERNAME="mm-dsr"
SOLACE_PASSWORD=9eb27e6e6ee81b4a
SOLACE_OUTLET_QUEUE_NAME="HNK/MDM/BUSINESSPARTNER/CUSTOMER/CUSTOMER/CRT/REP/APAC/MM/OMS"
SOLACE_SALES_REP_QUEUE_NAME="HNK/MDM/BUSINESSPARTNER/CONTACT/SALESREP/CRT/REP/APAC/MM/OMS"
