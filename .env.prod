NODE_ENV=production
TZ=Asia/Kuala_Lumpur
DEFAULT_LANGUAGE=en
DEBUG_MODE=false

# PUSH NOTIFICATION
FIREBASE_PROJECT_ID=dsr-app-c5bb7
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>

# DOT PROD
DOT_API_BASE_URL=https://www.heinekendot.my/storefrontapi
DOT_API_SEARCH_ORDER=https://admin.heinekendot.my
DOT_API_SEARCH_ORDER_KEY=0647d9ec-6762-4061-b31a-32e6b66b4139

TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=1ec6f72a06ab54d82eef77ed23892c68
TWILIO_PHONE_NUMBER=+***********

PHONE_COUNTRY_CODE_DEFAULT=+60
PHONE_COUNTRY_CODES=MY|+60,VN|+84,SG|+65

BASE_URL=https://api.tigertribe.dev
FE_URL=https://tigertribe.dev

OPEN_APP_LINK=https://www.tigertribe.dev/open-app.html


# JOB EXPRESSION
MISSED_VISIT_OUTLET_EXPRESSION=59 59 23 * * *
CREATE_STATISTIC_SALES_EXPRESSION=0 */4 * * *
DELETE_EXPIRED_FILES_EXPRESSION=0 0 * * *
CLONE_JOURNEY_PLAN_EXPRESSION=0 0 0 * * *
CREATE_CYCLE_TIME=0 0 0 1 11 *
CRON_DOT_ERROR=0 0 * * * *
CRON_SYSTEM_REPORT=0 0 8 * * *
CRON_CACHE_OMS=0 0 */2 * * *


# APP INSIGHTS
APPINSIGHTS_CONNECTIONSTRING=

# PLASGATE
PLASGATE_PRIVATE_KEY=
PLASGATE_X_SECRET=
PLASGATE_ENDPOINT_URL=

# OMS
OMS_API_BASE_URL=https://hei-oms-apac-qa-kh-backend.azurewebsites.net
OMS_EMAIL=
OMS_PASSWORD=

# S3
AZURE_S3_CONNECTION=