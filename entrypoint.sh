#!/bin/sh
( grep -q '#{NODE_ENV}' .env ) &&  sed -i -e "s|#{NODE_ENV}|$NODE_ENV|g" .env
( grep -q '#{TZ}' .env ) &&  sed -i -e "s|#{TZ}|$TZ|g" .env
( grep -q '#{DEFAULT_LANGUAGE}' .env ) &&  sed -i -e "s|#{DEFAULT_LANGUAGE}|$DEFAULT_LANGUAGE|g" .env
( grep -q '#{DEBUG_MODE}' .env ) &&  sed -i -e "s|#{DEBUG_MODE}|$DEBUG_MODE|g" .env
( grep -q '#{PORT}' .env ) &&  sed -i -e "s|#{PORT}|$PORT|g" .env
( grep -q '#{APP_TIME_OUT}' .env ) &&  sed -i -e "s|#{APP_TIME_OUT}|$APP_TIME_OUT|g" .env
( grep -q '#{EXPIRED_TIME_REMEMBER_LOGGED_IN}' .env ) &&  sed -i -e "s|#{EXPIRED_TIME_REMEMBER_LOGGED_IN}|$EXPIRED_TIME_REMEMBER_LOGGED_IN|g" .env
( grep -q '#{JWT_PRIVATE_KEY}' .env ) &&  sed -i -e "s|#{JWT_PRIVATE_KEY}|$JWT_PRIVATE_KEY|g" .env
( grep -q '#{JWT_EXPIRED_TIME}' .env ) &&  sed -i -e "s|#{JWT_EXPIRED_TIME}|$JWT_EXPIRED_TIME|g" .env
( grep -q '#{MONGODB_URL}' .env ) &&  sed -i -e "s|#{MONGODB_URL}|$MONGODB_URL|g" .env
( grep -q '#{MONGO_DB_NAME}' .env ) &&  sed -i -e "s|#{MONGO_DB_NAME}|$MONGO_DB_NAME|g" .env
( grep -q '#{MONGO_DB_USER}' .env ) &&  sed -i -e "s|#{MONGO_DB_USER}|$MONGO_DB_USER|g" .env
( grep -q '#{MONGO_DB_PASS}' .env ) &&  sed -i -e "s|#{MONGO_DB_PASS}|$MONGO_DB_PASS|g" .env
( grep -q '#{MONGO_POOL_SIZE}' .env ) &&  sed -i -e "s|#{MONGO_POOL_SIZE}|$MONGO_POOL_SIZE|g" .env
( grep -q '#{FIREBASE_PROJECT_ID}' .env ) &&  sed -i -e "s|#{FIREBASE_PROJECT_ID}|$FIREBASE_PROJECT_ID|g" .env
( grep -q '#{FIREBASE_PRIVATE_KEY}' .env ) &&  sed -i -e "s|#{FIREBASE_PRIVATE_KEY}|$FIREBASE_PRIVATE_KEY|g" .env
( grep -q '#{FIREBASE_CLIENT_EMAIL}' .env ) &&  sed -i -e "s|#{FIREBASE_CLIENT_EMAIL}|$FIREBASE_CLIENT_EMAIL|g" .env
( grep -q '#{CACHE_MODE}' .env ) &&  sed -i -e "s|#{CACHE_MODE}|$CACHE_MODE|g" .env
( grep -q '#{REDIS_HOST}' .env ) &&  sed -i -e "s|#{REDIS_HOST}|$REDIS_HOST|g" .env
( grep -q '#{REDIS_PORT}' .env ) &&  sed -i -e "s|#{REDIS_PORT}|$REDIS_PORT|g" .env
( grep -q '#{REDIS_PASSWORD}' .env ) &&  sed -i -e "s|#{REDIS_PASSWORD}|$REDIS_PASSWORD|g" .env
( grep -q '#{REDIS_TLS}' .env ) &&  sed -i -e "s|#{REDIS_TLS}|$REDIS_TLS|g" .env
( grep -q '#{OMS_API_BASE_URL}' .env ) &&  sed -i -e "s|#{OMS_API_BASE_URL}|$OMS_API_BASE_URL|g" .env
( grep -q '#{OMS_EMAIL}' .env ) &&  sed -i -e "s|#{OMS_EMAIL}|$OMS_EMAIL|g" .env
( grep -q '#{OMS_PASSWORD}' .env ) &&  sed -i -e "s|#{OMS_PASSWORD}|$OMS_PASSWORD|g" .env
( grep -q '#{TWILIO_ACCOUNT_SID}' .env ) &&  sed -i -e "s|#{TWILIO_ACCOUNT_SID}|$TWILIO_ACCOUNT_SID|g" .env
( grep -q '#{TWILIO_AUTH_TOKEN}' .env ) &&  sed -i -e "s|#{TWILIO_AUTH_TOKEN}|$TWILIO_AUTH_TOKEN|g" .env
( grep -q '#{TWILIO_PHONE_NUMBER}' .env ) &&  sed -i -e "s|#{TWILIO_PHONE_NUMBER}|$TWILIO_PHONE_NUMBER|g" .env
( grep -q '#{PHONE_COUNTRY_CODE_DEFAULT}' .env ) &&  sed -i -e "s|#{PHONE_COUNTRY_CODE_DEFAULT}|$PHONE_COUNTRY_CODE_DEFAULT|g" .env
( grep -q '#{PHONE_COUNTRY_CODES}' .env ) &&  sed -i -e "s|#{PHONE_COUNTRY_CODES}|$PHONE_COUNTRY_CODES|g" .env
( grep -q '#{BASE_URL}' .env ) &&  sed -i -e "s|#{BASE_URL}|$BASE_URL|g" .env
( grep -q '#{APPINSIGHTS_CONNECTIONSTRING}' .env ) &&  sed -i -e "s|#{APPINSIGHTS_CONNECTIONSTRING}|$APPINSIGHTS_CONNECTIONSTRING|g" .env
( grep -q '#{MUST_HAVE_SKU_EXCEL_URL}' .env ) &&  sed -i -e "s|#{MUST_HAVE_SKU_EXCEL_URL}|$MUST_HAVE_SKU_EXCEL_URL|g" .env
( grep -q '#{AZURE_S3_CONNECTION}' .env ) &&  sed -i -e "s|#{AZURE_S3_CONNECTION}|$AZURE_S3_CONNECTION|g" .env
( grep -q '#{GOOGLE_MAPS_API_KEY}' .env ) &&  sed -i -e "s|#{GOOGLE_MAPS_API_KEY}|$GOOGLE_MAPS_API_KEY|g" .env
( grep -q '#{LOCATION_RANGE_CONFIG}' .env ) &&  sed -i -e "s|#{LOCATION_RANGE_CONFIG}|$LOCATION_RANGE_CONFIG|g" .env
( grep -q '#{CONFIG_EXCEL_URL}' .env ) &&  sed -i -e "s|#{CONFIG_EXCEL_URL}|$CONFIG_EXCEL_URL|g" .env
( grep -q '#{SOLACE_URL}' .env ) &&  sed -i -e "s|#{SOLACE_URL}|$SOLACE_URL|g" .env
( grep -q '#{SOLACE_VPN_NAME}' .env ) &&  sed -i -e "s|#{SOLACE_VPN_NAME}|$SOLACE_VPN_NAME|g" .env
( grep -q '#{SOLACE_USERNAME}' .env ) &&  sed -i -e "s|#{SOLACE_USERNAME}|$SOLACE_USERNAME|g" .env
( grep -q '#{SOLACE_PASSWORD}' .env ) &&  sed -i -e "s|#{SOLACE_PASSWORD}|$SOLACE_PASSWORD|g" .env
( grep -q '#{SOLACE_OUTLET_QUEUE_NAME}' .env ) &&  sed -i -e "s|#{SOLACE_OUTLET_QUEUE_NAME}|$SOLACE_OUTLET_QUEUE_NAME|g" .env
( grep -q '#{SOLACE_SALES_REP_QUEUE_NAME}' .env ) &&  sed -i -e "s|#{SOLACE_SALES_REP_QUEUE_NAME}|$SOLACE_SALES_REP_QUEUE_NAME|g" .env
( grep -q '#{SMSPOH_ENDPOINT_URL}' .env ) &&  sed -i -e "s|#{SMSPOH_ENDPOINT_URL}|$SMSPOH_ENDPOINT_URL|g" .env
( grep -q '#{SMSPOH_SENDER_NAME}' .env ) &&  sed -i -e "s|#{SMSPOH_SENDER_NAME}|$SMSPOH_SENDER_NAME|g" .env
( grep -q '#{SMSPOH_API_KEY}' .env ) &&  sed -i -e "s|#{SMSPOH_API_KEY}|$SMSPOH_API_KEY|g" .env
( grep -q '#{SMSPOH_SECRET}' .env ) &&  sed -i -e "s|#{SMSPOH_SECRET}|$SMSPOH_SECRET|g" .env
( grep -q '#{POSTGRES_HOST}' .env ) &&  sed -i -e "s|#{POSTGRES_HOST}|$POSTGRES_HOST|g" .env
( grep -q '#{POSTGRES_PORT}' .env ) &&  sed -i -e "s|#{POSTGRES_PORT}|$POSTGRES_PORT|g" .env
( grep -q '#{POSTGRES_USER}' .env ) &&  sed -i -e "s|#{POSTGRES_USER}|$POSTGRES_USER|g" .env
ESCAPED_POSTGRES_PASSWORD=$(printf '%s\n' "$POSTGRES_PASSWORD" | sed -e 's/[\/&|]/\\&/g')
( grep -q '#{POSTGRES_PASSWORD}' .env ) &&  sed -i -e "s|#{POSTGRES_PASSWORD}|$ESCAPED_POSTGRES_PASSWORD|g" .env
( grep -q '#{POSTGRES_DATABASE}' .env ) &&  sed -i -e "s|#{POSTGRES_DATABASE}|$POSTGRES_DATABASE|g" .env
( grep -q '#{POSTGRES_CERT_PATH}' .env ) &&  sed -i -e "s|#{POSTGRES_CERT_PATH}|$POSTGRES_CERT_PATH|g" .env
( grep -q '#{POSTGRES_POOL_SIZE}' .env ) &&  sed -i -e "s|#{POSTGRES_POOL_SIZE}|$POSTGRES_POOL_SIZE|g" .env
( grep -q '#{TWILIO_TWIML_APP_ID}' .env ) &&  sed -i -e "s|#{TWILIO_TWIML_APP_ID}|$TWILIO_TWIML_APP_ID|g" .env
( grep -q '#{TWILIO_API_TOKEN}' .env ) &&  sed -i -e "s|#{TWILIO_API_TOKEN}|$TWILIO_API_TOKEN|g" .env
( grep -q '#{TWILIO_API_SECRET}' .env ) &&  sed -i -e "s|#{TWILIO_API_SECRET}|$TWILIO_API_SECRET|g" .env
( grep -q '#{TWILIO_RECORD_STATUS_CALLBACK}' .env ) &&  sed -i -e "s|#{TWILIO_RECORD_STATUS_CALLBACK}|$TWILIO_RECORD_STATUS_CALLBACK|g" .env
( grep -q '#{TWILIO_TWIML_CALLBACK}' .env ) &&  sed -i -e "s|#{TWILIO_TWIML_CALLBACK}|$TWILIO_TWIML_CALLBACK|g" .env
( grep -q '#{TWILIO_ACTION_URL}' .env ) &&  sed -i -e "s|#{TWILIO_ACTION_URL}|$TWILIO_ACTION_URL|g" .env
( grep -q '#{OOREDOO_SBC_IP}' .env ) &&  sed -i -e "s|#{OOREDOO_SBC_IP}|$OOREDOO_SBC_IP|g" .env
( grep -q '#{OOREDOO_SBC_REGION}' .env ) &&  sed -i -e "s|#{OOREDOO_SBC_REGION}|$OOREDOO_SBC_REGION|g" .env
( grep -q '#{PLASGATE_ENDPOINT_URL}' .env ) &&  sed -i -e "s|#{PLASGATE_ENDPOINT_URL}|$PLASGATE_ENDPOINT_URL|g" .env
( grep -q '#{PLASGATE_PRIVATE_KEY}' .env ) &&  sed -i -e "s|#{PLASGATE_PRIVATE_KEY}|$PLASGATE_PRIVATE_KEY|g" .env
( grep -q '#{PLASGATE_SENDER}' .env ) &&  sed -i -e "s|#{PLASGATE_SENDER}|$PLASGATE_SENDER|g" .env
( grep -q '#{PLASGATE_X_SECRET}' .env ) &&  sed -i -e "s|#{PLASGATE_X_SECRET}|$PLASGATE_X_SECRET|g" .env
( grep -q '#{SYNC_OMS_METRICS}' .env ) &&  sed -i -e "s|#{SYNC_OMS_METRICS}|$SYNC_OMS_METRICS|g" .env
echo "Starting Node Server"
pm2-runtime ecosystem-prod.config.js
