trigger: none # will disable CI builds entirely
#  - malaysia/production

pool:
  vmImage: ubuntu-latest

variables:
  - group: GIT

jobs:
  - deployment: VMDeploy
    displayName: Deploy to VM
    environment:
      name: 20_212_53_4
      resourceType: VirtualMachine
      tags: UMenuBackendQA-DSR
    strategy:
      runOnce:
        deploy:
          steps:
            - task: NodeTool@0
              inputs:
                versionSpec: "20.x"
              displayName: "Install node version 20.x"
            - script: |
                echo "Deploy API"
                cd /datadrive/www/DSRBackendUATPackage
                ls -la
                git reset --hard
                git pull https://$(API_GIT_TOKEN)@dev.azure.com/heineken/DSR%20App/_git/DSR%20-%20Backend malaysia/production
                npm install --production
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                cp -a .env.package .env
                npm run build
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                rm -rf src azure* Docker* docker* package-lock.json yarn.lock .env.example .gitignore .prettierrc .eslintrc.js tsconfig* README.md test docs migrations package.json

              displayName: Run Script in VM

            - script: |
                echo "Copy to Package"
                rm -rf /datadrive/www/Packages/DSR/BackendAPI/dsr.backendapi/*
                cp -r /datadrive/www/DSRBackendUATPackage/* /datadrive/www/Packages/DSR/BackendAPI/dsr.backendapi/
                cp -r /datadrive/www/DSRBackendUATPackage/.env /datadrive/www/Packages/DSR/BackendAPI/dsr.backendapi/
                rm -rf /datadrive/www/Packages/DSR/BackendAPI/dsr.backendapi/.git
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                cd /datadrive/www/DSRBackendUATPackage
                ls -la
                git reset --hard
              displayName: Copy to Package
