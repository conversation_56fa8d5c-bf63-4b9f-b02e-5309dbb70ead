trigger: none

pool:
  vmImage: ubuntu-latest

variables:
  - group: GIT

jobs:
  - deployment: VMDeploy
    displayName: Deploy to VM
    environment:
      name: IndonesiaProduction
      resourceType: VirtualMachine
      tags: DSR_ID_Production
    strategy:
      runOnce:
        deploy:
          steps:
            - task: NodeTool@0
              inputs:
                versionSpec: '20.x'
              displayName: 'Install node version 20.x'
            - script: |
                echo "Deploy indonesia/production"
                cd /datadrive/www/DSR_BE_Indonesia
                ls -la
                git reset --hard
                git fetch --tags https://$(API_GIT_TOKEN)@dev.azure.com/heineken/DSR%20App/_git/DSR%20-%20Backend
                git tag
                tag_name=$(Build.SourceBranchName)
                git checkout $tag_name
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                npm install --production
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                npm run build
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                pm2 restart ecosystem.config.js --only DSR_BE_Indonesia
              displayName: Run Script in VM
