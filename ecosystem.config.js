module.exports = {
  apps: [
    {
      name: 'DSR_BE_Malaysia_Development',
      exec_mode: 'cluster',
      instances: 1,
      max_memory_restart: '600M',
      script: 'dist/main.js',
      env: {
        PORT: 8000,
        NODE_ENV: 'development',
      },
    },
    {
      name: 'DSR_BE_Malaysia_Staging',
      exec_mode: 'cluster',
      instances: 1,
      max_memory_restart: '600M',
      script: 'dist/main.js',
      env: {
        PORT: 8001,
        NODE_ENV: 'staging',
      },
    },
    {
      name: 'DSR_BE_Indonesia_Development',
      exec_mode: 'cluster',
      instances: 1,
      max_memory_restart: '600M',
      script: 'dist/main.js',
      env: {
        PORT: 8002,
        NODE_ENV: 'development',
      },
    },
    {
      name: 'DSR_BE_Indonesia_Staging',
      exec_mode: 'cluster',
      instances: 1,
      max_memory_restart: '1G',
      script: 'dist/main.js',
      env: {
        PORT: 8003,
        NODE_ENV: 'staging',
      },
    },
    {
      name: 'DSR_BE_Cambodia_Development',
      exec_mode: 'cluster',
      instances: 1,
      max_memory_restart: '600M',
      script: 'dist/main.js',
      env: {
        PORT: 8004,
        NODE_ENV: 'development',
      },
    },
    {
      name: 'DSR_BE_Cambodia_Staging',
      exec_mode: 'cluster',
      instances: 1,
      max_memory_restart: '600M',
      script: 'dist/main.js',
      env: {
        PORT: 8005,
        NODE_ENV: 'staging',
      },
    },
    {
      name: 'DSR_BE_Malaysia_Development2',
      exec_mode: 'cluster',
      instances: 1,
      max_memory_restart: '600M',
      script: 'dist/main.js',
      env: {
        PORT: 8006,
        NODE_ENV: 'development',
      },
    },
    {
      name: 'DSR_BE_Myanmar_Development',
      exec_mode: 'cluster',
      instances: 1,
      max_memory_restart: '600M',
      script: 'dist/main.js',
      env: {
        PORT: 8007,
        NODE_ENV: 'development',
      },
    },
    {
      name: 'DSR_BE_Myanmar_Staging',
      exec_mode: 'cluster',
      instances: 1,
      max_memory_restart: '600M',
      script: 'dist/main.js',
      env: {
        PORT: 8008,
        NODE_ENV: 'staging',
      },
    },
    {
      name: 'DSR_BE_Cam_Production',
      exec_mode: 'cluster',
      instances: 'max',
      script: 'dist/main.js',
      env: {
        PORT: 4000,
        NODE_ENV: 'production',
      },
    },
    {
      name: 'DSR_BE_Indonesia',
      exec_mode: 'cluster',
      instances: 'max',
      script: 'dist/main.js',
      env: {
        PORT: 3000,
        NODE_ENV: 'production',
      },
    },
  ],
};
