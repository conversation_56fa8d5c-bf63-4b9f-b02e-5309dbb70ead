version: '3'
services:
  dsr-api-dev:
    container_name: dsr_api_dev
    image: dsr_api_dev:0.0.1
    build:
      context: .
      dockerfile: Dockerfile.dev
    environment:
      - NODE_ENV=development
      - REDIS_HOST=redis
      - MONGODB_URL=mongodb://mongodb/dsr
    ports:
      - "8000:8000"
    depends_on:
      - mongodb
      - redis
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - dsr_network
    restart: unless-stopped
  dsr-api-prod:
    container_name: dsr_api_prod
    image: dsr_api_prod:0.0.1
    build: .
    environment:
      - REDIS_HOST=redis
      - MONGODB_URL=mongodb://mongodb/dsr
    ports:
      - "8080:8080"
    depends_on:
      - mongodb
      - redis
    networks:
      - dsr_network
    restart: unless-stopped
  mongodb:
    container_name: mongodb
    image: mongo:latest
    environment:
      - NODE_ENV=development
      - REDIS_HOST=redis
      - MONGODB_URL=mongodb://localhost/dsr
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    networks:
      - dsr_network

    restart: always
  redis:
    image: 'redis:latest'
    container_name: redis
    volumes:
      - redis_data:/data
    networks:
      - dsr_network
networks:
  dsr_network:
    driver: bridge
volumes:
  mongo_data:
    external: false
  redis_data:
    external: false
