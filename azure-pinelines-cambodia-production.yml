trigger: none

pool:
  vmImage: ubuntu-latest

variables:
  - group: GIT

jobs:
  - deployment: VMDeploy
    displayName: Deploy to VM
    environment:
      name: CambodiaProduction
      resourceType: VirtualMachine
      tags: DSR_KH_Production
    strategy:
      runOnce:
        deploy:
          steps:
            - script: |
                echo "Deploy cambodia/production"
                cd /datadrive/www/DSR_BE_Cam_Production
                ls -la
                git reset --hard
                git fetch --tags https://$(API_GIT_TOKEN)@dev.azure.com/heineken/DSR%20App/_git/DSR%20-%20Backend
                git tag
                tag_name=$(Build.SourceBranchName)
                git checkout $tag_name
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                npm install --production
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                npm run build
                if [ $? -ne 0 ]; then
                  exit 1
                fi
                pm2 restart ecosystem.config.js --only DSR_BE_Cam_Production
              displayName: Run Script in VM
