.imgDesktop {
    display: none !important
}

.imgMobile {
    display: block !important
}

@media (min-width: 512px) {
    .imgDesktop {
        display: block !important
    }

    .imgMobile {
        display: none !important
    }
}

.swiper-pagination {
    height: 66px;
    bottom: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center
}

.swiper-pagination-bullet {
    border: 1px solid #6f767e !important;
    background-color: transparent !important;
    opacity: 1 !important
}

.swiper-pagination-bullet-active {
    background-color: #000 !important
}

.swiper-button-disabled {
    display: none !important
}

.swiper-button-next, .swiper-button-prev {
    width: 40px !important;
    height: 40px !important;
    border: 2px solid #ececec;
    border-radius: 20px;
    background-color: #fff;
    margin: 0 5px
}

.swiper-button-prev {
    left: 0 !important
}

.swiper-button-next {
    right: 0 !important
}

.swiper-button-next:after, .swiper-button-prev:after {
    background-image: url(/arrow.svg);
    background-repeat: no-repeat;
    background-position: 50%;
    content: "" !important;
    width: 40px;
    height: 40px
}

.swiper-button-prev:after {
    rotate: 180deg
}

.swiper-slide-thumb-active button {
    border: 1px solid teal
}

.banners .swiper-wrapper .swiper-slide {
    margin-right: 0
}

.product-page-constraint .swiper-slide {
    height: auto
}

.product-page-constraint .swiper-slide > div {
    height: 100%
}

.product-page-constraint .swiper {
    padding-bottom: 66px
}

.checkbox-container {
    display: block;
    position: relative;
    padding-left: 30px;
    margin-bottom: 16px;
    cursor: pointer;
    font-size: 16px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    z-index: 3
}

.checkbox-container:last-child {
    margin-bottom: 0
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 18px;
    width: 18px;
    border: 1px solid #6f767e;
    border-radius: 4px
}

.checkbox-container:hover input ~ .checkmark, .checkmark {
    background-color: #fff
}

.checkbox-container input:checked ~ .checkmark {
    background-color: teal
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block
}

.checkbox-container .checkmark:after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid #fff;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg)
}

.step {
    width: 20%
}

.step:last-child button:after {
    display: none
}

.step button {
    position: relative
}

.step button:after {
    content: "";
    position: absolute;
    top: 14px;
    left: calc(50% + 14px);
    margin-left: 15%;
    width: 60%;
    height: 1px;
    background-color: #e5f2f2
}

.step button.visited:after {
    background-color: teal
}

.step button span.number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #e5f2f2;
    margin-bottom: 10px
}

.step button.active span.number {
    background-color: teal;
    color: #fff
}

.step button.active span.title {
    color: #111316
}

.step button.visited span.number {
    position: relative
}

.step button.visited span.number:after {
    position: absolute;
    top: 0;
    left: 0;
    content: "";
    border: 5px solid teal;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: url(/tick.svg) no-repeat 50% #fff
}

@media (min-width: 768px) {
    .step {
        width: auto
    }

    .step button:after {
        left: 100%;
        margin-left: 13px;
        width: 30px
    }

    .step button span.number {
        margin: 0
    }

    .step button span.title {
        padding-left: 8px
    }
}

.react-datepicker-wrapper {
    position: relative;
    width: 100%
}

.react-datepicker-wrapper:after {
    position: absolute;
    top: 13px;
    right: 16px;
    content: "";
    width: 20px;
    height: 20px;
    pointer-events: none
}

.datePicker .react-datepicker-wrapper:after {
    background: url(/calendar.svg) no-repeat
}

.timePicker .react-datepicker-wrapper:after {
    background: url(/clock.svg) no-repeat
}

.react-datepicker__input-container input {
    width: 100%;
    height: 46px;
    border-radius: 12px;
    padding: 0 16px
}

.datePicker .react-datepicker__triangle, .timePicker .react-datepicker__triangle {
    left: -20px !important
}

.pagination form {
    display: none
}

.pagination ul {
    margin: 0 auto
}

.pagination li {
    display: block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    margin: 0 5px
}

.pagination li:first-child a, .pagination li:last-child a {
    position: relative;
    border-radius: 50% !important;
    border: 1px solid #ececec
}

.pagination li:first-child a[aria-label="No previous page available"], .pagination li:last-child a[aria-label="No next page available"] {
    opacity: .5
}

.pagination li:first-child a:after, .pagination li:last-child a:after {
    position: absolute;
    top: 0;
    left: 0;
    background-image: url(/arrow.svg);
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 16px auto;
    content: "" !important;
    width: 40px;
    height: 40px
}

.pagination li:first-child a:after {
    rotate: 180deg;
    top: -1px
}

.pagination li:first-child svg, .pagination li:last-child svg {
    display: none
}

.pagination li a {
    outline: none;
    color: #323232;
    padding: 0;
    min-width: auto;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%
}

.pagination a[aria-current=true] {
    background-color: var(--color-primary);
    color: #fff
}

@media (min-width: 1024px) {
    .pagination ul {
        margin: initial
    }
}

.eventRegistrationPage main, .faqsPage main, .howItWorksPage main, .redemptionPage main, .storyblockBanner .bannerContent, .subscriptionPage main, .theBladePage main {
    display: flex;
    flex-direction: column
}

.storyblockBanner .bannerContent {
    justify-content: center;
    pointer-events: none
}

.RedemptionTermsAndConditionsPage .storyblockTeaser, .beerCiderPage .storyblockTeaser, .eventRegistrationPage .storyblockTeaser, .redemptionPage .storyblockTeaser, .subscriptionPage .storyblockTeaser {
    padding-top: 40px
}

.redemptionPage .storyblockTeaser {
    background-color: #f2f5f5
}

.RedemptionTermsAndConditionsPage .storyblockTeaser, .eventRegistrationPage .storyblockTeaser {
    max-width: 800px;
    margin: 0 auto
}

@media (min-width: 768px) {
    .eventRegistrationPage .storyblockTeaser, .redemptionPage .storyblockTeaser {
        padding-top: 60px
    }
}

.redemptionPage .content-inner {
    max-width: 992px;
    margin: 0 auto;
    padding-bottom: 1rem
}

.redemptionPage .redemptionForm h2 {
    text-align: center;
    font-size: 24px
}

@media (min-width: 768px) {
    .redemptionPage .redemptionForm h2 {
        font-size: 32px
    }
}

.RedemptionTermsAndConditionsPage .richtextContent, .TermsAndConditionsPage .richtextContent {
    padding-top: 40px
}

.faq-container .faq-tabs li.active {
    background-color: #000;
    color: #fff
}

.faq-container ul.faq-content {
    width: 100%;
    max-width: 842px;
    margin: auto
}

.faq-container ul.faq-content > li {
    display: none
}

.faq-container ul.faq-content > li.active {
    display: block
}

.faq-container ul.faq-content > li ul.faq-group li {
    border-bottom: 1px solid #ececec
}

.faq-container ul.faq-content > li ul.faq-group li .faq-answer, .faq-container ul.faq-content > li ul.faq-group li .faq-question .minus {
    display: none
}

.faq-container ul.faq-content > li ul.faq-group li .faq-answer * {
    margin-bottom: 16px
}

.faq-container ul.faq-content > li ul.faq-group li.active .faq-question .minus {
    display: block
}

.faq-container ul.faq-content > li ul.faq-group li.active .faq-question .plus {
    display: none
}

.faq-container ul.faq-content > li ul.faq-group li.active .faq-answer {
    display: block
}

.howItWorksPage .howItWorksContainer .bladeTabs {
    width: 100%;
    max-width: 1062px;
    margin: auto;
    overflow: hidden
}

.howItWorksPage .howItWorksContainer .bladeTabs ul {
    flex-direction: row;
    flex-wrap: wrap;
    margin-left: -8px;
    margin-right: -8px
}

.howItWorksPage .howItWorksManualLinks .richtextContent > div {
    margin: 0;
    padding: 24px 0 0
}

.howItWorksPage .howItWorksManualLinks .richtextContent > div h3 {
    margin-bottom: 16px
}

.howItWorksPage .howItWorksManualLinks .richtextContent > div a {
    text-decoration: underline
}

@media (min-width: 640px) {
    .howItWorksPage .storyblockBanner .bannerContent {
        align-items: center;
        text-align: center
    }

    .howItWorksPage .howItWorksStepsItem {
        position: relative
    }

    .howItWorksPage .howItWorksStepsItem:after {
        position: absolute;
        bottom: -90px;
        left: 22px;
        content: "";
        width: calc(50% + 20px);
        height: 180px;
        background-image: url(/line.png);
        background-size: 100% auto;
        background-position: 50%;
        background-repeat: no-repeat
    }

    .howItWorksPage .howItWorksStepsItem:nth-child(2n):after {
        background-image: url(/line-revert.png)
    }

    .howItWorksPage .howItWorksStepsItem:last-child:after {
        display: none
    }

    .howItWorksPage .howItWorksManualLinks .richtextContent {
        width: 100%
    }
}

@media (min-width: 1024px) {
    .howItWorksPage .howItWorksStepsItem:after {
        width: calc(50% + 56px)
    }

    .howItWorksPage .howItWorksManualLinks .richtextContent {
        width: 50%
    }
}

.howToRedeem .BladeProduct + .BladeProduct > div, .theBladePage .BladeProduct + .BladeProduct > div {
    flex-direction: row-reverse
}

.howToRedeem .BladeContent ul li, .theBladePage .BladeContent ul li {
    width: 100%
}

@media (min-width: 768px) {
    .howToRedeem .BladeContent ul li, .theBladePage .BladeContent ul li {
        width: calc(33.33% - 2rem)
    }

    .howToRedeem .BladeContent + .BladeContent ul li, .theBladePage .BladeContent + .BladeContent ul li {
        width: calc(25% - 2rem)
    }
}

.HowToOrderItem + .HowToOrderItem {
    padding-top: 0
}

.HowToOrderItem:first-child {
    background-color: #79ddc4
}

.HowToOrderItem:first-child * {
    color: #fff !important
}

.HowToOrderItem:nth-child(2) {
    padding-top: 2rem
}

.HowToOrderItem ul li {
    list-style: disc !important
}

.HowToOrderItem .description {
    width: auto
}

.voucher-header {
    background: url(/voucher/thumbnail_top.png) no-repeat 50%;
    background-size: 100% 100%;
    height: 21px
}

.voucher-body {
    background: url(/voucher/thumbnail_line.png) repeat-y 50%;
    background-size: 100% 100%
}

.voucher-body .duration {
    top: 190px;
    transform: rotate(-90deg);
    width: 350px;
    right: -153px;
    text-align: right;
    font-size: 12px
}

.voucher-cornor {
    background: url(/voucher/thumbnail_cornor.png) no-repeat 50%;
    background-size: 100% 100%;
    height: 54px
}

.voucher-footer-text {
    background: url(/voucher/thumbnail_line.png) repeat-y 50%;
    background-size: 100% 100%
}

.voucher-footer {
    background: url(/voucher/thumbnail_bottom.png) no-repeat 50%;
    background-size: 100% 100%;
    height: 27px
}

.voucher-swiper .swiper-slide {
    height: auto
}

.voucher-swiper .swiper-button-next, .voucher-swiper .swiper-button-prev {
    border: 0
}

.voucher-swiper .swiper-button-next:after, .voucher-swiper .swiper-button-prev:after {
    background-image: url(/arrow-short.svg);
    background-repeat: no-repeat;
    background-position: 50%;
    content: "" !important;
    width: 40px;
    height: 40px
}

@media (max-width: 600px) {
    .voucher-header {
        background: url(/voucher/thumbnail_top_small.png) no-repeat 50%;
        background-size: 100% 100%;
        height: 18px
    }

    .voucher-body {
        background: url(/voucher/thumbnail_line_small.png) repeat-y 50%;
        background-size: 100% 100%
    }

    .voucher-cornor {
        background: url(/voucher/thumbnail_cornor_small.png) no-repeat 50%;
        background-size: 100% 100%;
        height: 38px
    }

    .voucher-footer-text {
        background: url(/voucher/thumbnail_line_small.png) repeat-y 50%;
        background-size: 100% 100%
    }

    .voucher-footer {
        background: url(/voucher/thumbnail_bottom_small.png) no-repeat 50%;
        background-size: 100% 100%;
        height: 18px
    }
}

.outlet-dropdown-mobile .dropdown-heading {
    display: none !important
}

.outlet-dropdown-mobile .dropdown-content {
    position: relative !important
}

.outlet-dropdown-mobile .dropdown-content .panel-content {
    box-shadow: none !important
}

.outlet-dropdown-mobile .dropdown-content .select-item {
    border: none !important;
    padding-left: 10px !important
}

.outlet-dropdown-mobile .dropdown-content .options {
    max-height: none !important
}

.outlet-dropdown-mobile .dropdown-content .selected {
    background-color: transparent !important
}

.rmsc .dropdown-container {
    border: 0 !important;
    box-shadow: none !important
}

.rmsc .dropdown-heading {
    height: auto !important;
    outline: 0 !important
}

.rmsc .dropdown-content {
    width: 100%
}

.rmsc .dropdown-content .select-item {
    border: 1px solid;
    border-color: var(--color-secondary-light-300);
    accent-color: var(--color-primary);
    padding-left: 15px;
    font-size: 16px
}

.rmsc .dropdown-content .select-item input {
    margin-right: 15px;
    transform: scale(1.2)
}

.country-flag input {
    background-repeat: no-repeat !important;
    background-size: 22px 22px !important;
    background-position: 10px 10px !important
}

.country-flag.SG-flag input {
    background-image: url(/flags/SG-flag.svg)
}

.country-flag.MY-flag input {
    background-image: url(/flags/MY-flag.svg)
}

.mobileApp .storyblockTeaser h1 {
    font-size: 1.5rem;
    color: #79ddc4
}

.mobileApp .storyblockTeaser .content-inner {
    padding-bottom: 1rem
}

.mobileApp .richtextContent ul li {
    list-style: none
}

html[data-theme=theme-with-banner] {
    --banner-content-pos-x: calc((100% - 1110px) / 2)
}

.theme-with-banner .side-banner {
    display: none
}

@media (min-width: 768px) {
    .theme-with-banner .side-banner {
        align-items: start;
        display: block;
        height: 100vh;
        max-width: 100%;
        overflow-x: hidden;
        position: fixed;
        top: 0;
        z-index: -1
    }

    .theme-with-banner .left-banner {
        left: 0
    }

    .theme-with-banner .right-banner {
        right: 0
    }

    .theme-with-banner .main-body {
        width: calc(100% - 152px - 152px);
        margin: 0 auto
    }

    .theme-with-banner .main-body main {
        background-color: #fff;
        z-index: 1
    }

    .theme-with-banner footer {
        z-index: 1
    }
}

@media (min-width: 1680px) {
    .theme-with-banner .main-body {
        width: calc(100% - 265px - 265px)
    }
}

/*
    ! tailwindcss v3.4.6 | MIT License | https://tailwindcss.com
    */
*, :after, :before {
    box-sizing: border-box;
    border: 0 solid #e5e7eb
}

:after, :before {
    --tw-content: ""
}

:host, html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Ubuntu, sans-serif;
    font-feature-settings: normal;
    font-variation-settings: normal;
    -webkit-tap-highlight-color: transparent
}

body {
    margin: 0;
    line-height: inherit
}

hr {
    height: 0;
    color: inherit;
    border-top-width: 1px
}

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b, strong {
    font-weight: bolder
}

code, kbd, pre, samp {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
    font-feature-settings: normal;
    font-variation-settings: normal;
    font-size: 1em
}

small {
    font-size: 80%
}

sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse
}

button, input, optgroup, select, textarea {
    font-family: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    color: inherit;
    margin: 0;
    padding: 0
}

button, select {
    text-transform: none
}

button, input:where([type=button]), input:where([type=reset]), input:where([type=submit]) {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote, dd, dl, figure, h1, h2, h3, h4, h5, h6, hr, p, pre {
    margin: 0
}

fieldset {
    margin: 0
}

fieldset, legend {
    padding: 0
}

menu, ol, ul {
    list-style: none;
    margin: 0;
    padding: 0
}

dialog {
    padding: 0
}

textarea {
    resize: vertical
}

input::-moz-placeholder, textarea::-moz-placeholder {
    opacity: 1;
    color: #9ca3af
}

input::placeholder, textarea::placeholder {
    opacity: 1;
    color: #9ca3af
}

[role=button], button {
    cursor: pointer
}

:disabled {
    cursor: default
}

audio, canvas, embed, iframe, img, object, svg, video {
    display: block;
    vertical-align: middle
}

img, video {
    max-width: 100%;
    height: auto
}

[hidden] {
    display: none
}

html {
    font-family: Helvetica, Arial, sans-serif;
    --color-primary: teal;
    --color-primary-100: #dcfce7;
    --color-primary-200: #bbf7d0;
    --color-primary-500: #22c55e;
    --color-primary-light: #4be0c3;
    --color-primary-light-100: #ddfff8;
    --color-primary-light-300: #b7e5db;
    --color-secondary: #6f767e;
    --color-secondary-100: #f3f4f6;
    --color-secondary-200: #e5e7eb;
    --color-secondary-400: #9ca3af;
    --color-secondary-500: #979797;
    --color-secondary-700: #374151;
    --color-secondary-700-75: rgba(55, 65, 81, .75);
    --color-secondary-900: #111827;
    --color-secondary-light: #e5f2f2;
    --color-secondary-light-300: #efefef;
    --color-secondary-light-400: #f2f5f5;
    --color-tertiary: #111316;
    --color-tertiary-300: #323232;
    --color-danger: #e82627;
    --color-danger-100: #fee2e2;
    --color-danger-200: #fecaca;
    --color-danger-500: red;
    --color-danger-600: #dc2626;
    --color-danger-700: #eb5757;
    --color-info: #60a5fa;
    --color-warning: #ff8f00;
    --banner-content-pos-x: calc((100% - 1280px) / 2)
}

body {
    color: #111316
}

.font-hnk {
    font-family: Heineken, Helvetica, Arial
}

a:focus, button:focus {
    outline: none
}

input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

input[type=number] {
    -moz-appearance: textfield
}

input:disabled {
    background: #f9f9f9
}

*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

.\!container {
    width: 100% !important
}

.container {
    width: 100%
}

@media (min-width: 320px) {
    .\!container {
        max-width: 320px !important
    }

    .container {
        max-width: 320px
    }
}

@media (min-width: 512px) {
    .\!container {
        max-width: 512px !important
    }

    .container {
        max-width: 512px
    }
}

@media (min-width: 640px) {
    .\!container {
        max-width: 640px !important
    }

    .container {
        max-width: 640px
    }
}

@media (min-width: 768px) {
    .\!container {
        max-width: 768px !important
    }

    .container {
        max-width: 768px
    }
}

@media (min-width: 1024px) {
    .\!container {
        max-width: 1024px !important
    }

    .container {
        max-width: 1024px
    }
}

@media (min-width: 1280px) {
    .\!container {
        max-width: 1280px !important
    }

    .container {
        max-width: 1280px
    }
}

@media (min-width: 1440px) {
    .\!container {
        max-width: 1440px !important
    }

    .container {
        max-width: 1440px
    }
}

@media (min-width: 1536px) {
    .\!container {
        max-width: 1536px !important
    }

    .container {
        max-width: 1536px
    }
}

@media (min-width: 1680px) {
    .\!container {
        max-width: 1680px !important
    }

    .container {
        max-width: 1680px
    }
}

@media (min-width: 1920px) {
    .\!container {
        max-width: 1920px !important
    }

    .container {
        max-width: 1920px
    }
}

.content-container {
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    max-width: 1440px;
    padding-left: 2rem;
    padding-right: 2rem
}

.text-xsmall-regular {
    font-size: 10px;
    font-weight: 400;
    line-height: 1rem
}

.text-small-regular {
    font-size: .75rem;
    font-weight: 400;
    line-height: 1.25rem
}

.text-small-semi {
    font-size: .75rem;
    font-weight: 600;
    line-height: 1.25rem
}

.text-base-regular {
    font-weight: 400
}

.text-base-regular, .text-base-semi {
    font-size: .875rem;
    line-height: 1.5rem
}

.text-base-semi {
    font-weight: 600
}

.text-large-regular {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5rem
}

.text-large-semi {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.5rem
}

.text-xl-regular {
    font-size: 1.5rem;
    line-height: 2rem;
    font-weight: 400;
    line-height: 36px
}

.text-2xl-regular {
    font-size: 30px;
    font-weight: 400;
    line-height: 48px
}

.text-2xl-semi {
    font-size: 30px;
    font-weight: 600;
    line-height: 48px
}

.distributor-layout {
    font-family: Inter, sans-serif
}

.distributor-layout .bg-gradient-primary {
    background: var(
            --background, linear-gradient(135deg, #f9fafe 0, #faf6ff 33.67%, #f9fafe 100%)
    )
}

.oms-custom-scroll ::-webkit-scrollbar {
    width: 6px
}

.oms-custom-scroll ::-webkit-scrollbar-thumb {
    background: #c7c7c7;
    border-radius: 20px
}

.oms-custom-scroll ::-webkit-scrollbar-thumb:hover {
    background: #757575
}

.oms-custom-scroll ::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 20px
}

.oms-custom-scroll--thin ::-webkit-scrollbar {
    width: 4px
}

.btn {
    border-radius: 10px;
    background-image: linear-gradient(135deg, rgba(0, 130, 0, .7), #008200);
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    font-weight: 700;
    letter-spacing: .025em;
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.btn-secondary {
    border-width: 1px;
    border-color: rgb(0 130 0/var(--tw-border-opacity));
    color: rgb(0 130 0/var(--tw-text-opacity))
}

.btn-danger, .btn-secondary {
    --tw-border-opacity: 1;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity));
    background-image: none;
    --tw-text-opacity: 1
}

.btn-danger {
    border-width: 1px;
    border-color: rgb(236 28 36/var(--tw-border-opacity));
    color: rgb(236 28 36/var(--tw-text-opacity))
}

.btn-small {
    height: 2rem;
    min-width: 86px
}

.btn-medium {
    height: 2.125rem;
    min-width: 10.625rem
}

.btn-large {
    height: 2.5rem;
    min-width: 10.625rem
}

.is-promo {
    border-radius: .375rem;
    --tw-bg-opacity: 1;
    background-color: rgb(179 49 49/var(--tw-bg-opacity));
    padding: .25rem .5rem;
    font-size: .75rem;
    line-height: 1rem;
    font-weight: 700;
    letter-spacing: .05em;
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.oms-input {
    border-radius: 10px;
    border: 1px solid #eaeaea;
    background: var(--white, #fff);
    max-height: 38px
}

.oms-checkbox {
    display: block;
    position: relative;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.oms-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer
}

.oms-checkbox .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 24px;
    width: 24px;
    background-color: #f7f7f7;
    border-radius: 50%
}

.oms-checkbox:hover input ~ .checkmark {
    background-color: #ccc
}

.oms-checkbox input:checked ~ .checkmark {
    background-color: #008200
}

.oms-checkbox .checkmark:after {
    content: "";
    position: absolute;
    display: none;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #fff;
    transform: translate(-50%, -50%)
}

.oms-checkbox input:checked ~ .checkmark:after {
    display: block
}

.date-range-picker {
    font-family: Inter, sans-serif !important;
    height: 436px;
    width: 378px;
    border: none !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .2), 0 4px 8px 3px rgba(0, 0, 0, .15);
    color: #3a3d46 !important;
    padding: 12px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    border-radius: 20px !important;
    font-weight: 400;
    animation: picker-appear 75ms linear
}

@keyframes picker-appear {
    0% {
        opacity: 0;
        transform: scale(95%)
    }
    to {
        opacity: 1;
        transform: scale(100%)
    }
}

.date-range-picker .react-datepicker__triangle {
    display: none !important
}

.date-range-picker .react-datepicker__month-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column
}

.date-range-picker .react-datepicker__header {
    text-align: center;
    background-color: #fff !important;
    border-bottom: none !important;
    padding: 0 !important;
    position: relative
}

.date-range-picker .react-datepicker__month {
    display: flex;
    text-align: center;
    flex-direction: column;
    align-items: center;
    gap: 4px
}

.date-range-picker .react-datepicker__day--outside-month {
    visibility: hidden
}

.date-range-picker .react-datepicker__week {
    display: flex;
    width: 100%;
    justify-content: space-between
}

.date-range-picker .react-datepicker__day-names {
    width: 100%;
    display: flex;
    justify-content: center
}

.date-range-picker .react-datepicker__day-name {
    font-weight: 400;
    font-size: 16px;
    height: 48px;
    width: 3.6rem !important;
    display: flex !important;
    justify-content: space-evenly !important;
    align-items: center !important;
    margin: 0 !important;
    color: #3a3d46 !important
}

.date-range-picker .react-datepicker__day {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    font-size: 16px;
    margin: 0 !important;
    height: 40px;
    width: 40px;
    color: #3a3d46
}

.date-range-picker .react-datepicker__day:hover {
    border-radius: 50% !important;
    background-color: #f1fcf1;
    color: #000
}

.date-range-picker .react-datepicker__day--disabled {
    cursor: not-allowed;
    opacity: .5
}

.date-range-picker .react-datepicker__day--disabled:hover {
    background-color: transparent;
    --tw-text-opacity: 1;
    color: rgb(58 61 70/var(--tw-text-opacity))
}

.date-range-picker .react-datepicker__day--today span {
    -webkit-text-decoration: underline 3px #008200;
    text-decoration: underline 3px #008200;
    font-weight: 700 !important
}

.date-range-picker .react-datepicker__day--selected {
    border-radius: 50% !important;
    background-color: #008700 !important;
    color: #fff !important;
    position: relative
}

.date-range-picker .react-datepicker__day--in-range {
    border-radius: 0 !important;
    background-color: #f1fcf1 !important;
    color: #3a3d46 !important;
    position: relative;
    border: 1px
}

.date-range-picker .react-datepicker__day--in-range:before {
    position: absolute;
    content: "";
    top: 0;
    left: 6px;
    background-color: #f1fcf1;
    border: 1px;
    width: 100%;
    height: 100%
}

.date-range-picker .react-datepicker__day--in-range:after {
    position: absolute;
    content: "";
    top: 0;
    right: 6px;
    background-color: #f1fcf1;
    border: 1px;
    width: 100%;
    height: 100%
}

.date-range-picker .react-datepicker__day--in-range span {
    position: absolute;
    z-index: 5;
    color: #3a3d46 !important
}

.date-range-picker .react-datepicker__day--in-selecting-range {
    border-radius: 50%;
    background-color: unset !important;
    position: relative;
    border: 1px
}

.date-range-picker .react-datepicker__day--in-selecting-range span {
    position: absolute;
    z-index: 100;
    color: #000
}

.date-range-picker .react-datepicker__day--in-selecting-range:before {
    position: absolute;
    content: "";
    width: 26px;
    top: 0;
    left: 50%;
    height: 100%;
    background-color: #f1fcf1
}

.date-range-picker .react-datepicker__day--in-selecting-range:after {
    position: absolute;
    content: "";
    width: 26px;
    top: 0;
    right: 50%;
    height: 100%;
    background-color: #f1fcf1
}

.date-range-picker .react-datepicker__day--selecting-range-end, .date-range-picker .react-datepicker__day--selecting-range-start {
    background-color: #008700 !important;
    color: #fff !important;
    display: flex;
    align-items: center;
    border-radius: 50% !important;
    position: relative;
    font-weight: 700
}

.date-range-picker .react-datepicker__day--selecting-range-end:before, .date-range-picker .react-datepicker__day--selecting-range-start:before {
    position: absolute;
    z-index: 22;
    width: 100%;
    height: 100%;
    background-color: #008200;
    content: "";
    top: 0;
    left: 0;
    border-radius: 50%
}

.date-range-picker .react-datepicker__day--selecting-range-end span, .date-range-picker .react-datepicker__day--selecting-range-start span {
    position: absolute;
    z-index: 22;
    color: #fff;
    font-weight: 700 !important
}

.date-range-picker .react-datepicker__day--range-end, .date-range-picker .react-datepicker__day--range-start {
    background-color: #008700 !important
}

.date-range-picker .react-datepicker__day--range-end:hover, .date-range-picker .react-datepicker__day--range-start:hover {
    background-color: #008700 !important;
    color: #fff !important
}

.date-range-picker .react-datepicker__day--in-range:hover, .date-range-picker .react-datepicker__day--in-selecting-range:hover, .date-range-picker .react-datepicker__day--selected:hover {
    background-color: #008700;
    color: #fff;
    border: none !important
}

.date-range-picker .react-datepicker__day--keyboard-selected {
    border-radius: 50% !important;
    background-color: #008700 !important;
    color: #fff !important
}

.date-range-picker .react-datepicker__day--range-end, .date-range-picker .react-datepicker__day--range-start {
    border-radius: 50% !important;
    background-color: #008200 !important;
    color: #fff !important;
    position: relative
}

.date-range-picker .react-datepicker__day--range-end:before, .date-range-picker .react-datepicker__day--range-start:before {
    border-radius: 50% !important;
    width: 100%;
    content: "";
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1 !important;
    background-color: #008200 !important
}

.date-range-picker .react-datepicker__day--range-start:after {
    width: 26px;
    content: "";
    height: 100%;
    position: absolute;
    left: 50%;
    top: 0;
    z-index: 0 !important;
    background-color: #f1fcf1 !important
}

.date-range-picker .react-datepicker__day--range-end:after {
    width: 26px;
    content: "";
    height: 100%;
    position: absolute;
    right: 50%;
    top: 0;
    z-index: 0 !important;
    background-color: #f1fcf1 !important
}

.date-range-picker .react-datepicker__day--range-end span, .date-range-picker .react-datepicker__day--range-start span {
    position: absolute;
    z-index: 5;
    color: #fff !important;
    font-weight: 700 !important
}

.date-range-picker .react-datepicker__children-container {
    position: absolute;
    bottom: 12px;
    right: 12px;
    left: 12px;
    width: calc(100% - 24px) !important;
    padding: 0 !important;
    margin: 0 !important
}

.time-list::-webkit-scrollbar {
    display: none
}

.time-list {
    -ms-overflow-style: none;
    scrollbar-width: none
}

.photo-stack {
    position: relative
}

.photo-stack .image-stacked {
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid #fff;
    box-shadow: 0 1px 1px 1px rgba(0, 0, 0, .2);
    transition: all .3s ease-out;
    width: auto;
    max-width: 110px;
    height: 120px;
    background-color: #fff;
    border-radius: 8px
}

.photo-stack .image-stacked:first-child {
    z-index: 999
}

.photo-stack .image-stacked:nth-child(2) {
    transform: rotate(3deg)
}

.photo-stack .image-stacked:nth-child(3) {
    transform: rotate(-3deg)
}

.photo-stack .image-stacked:nth-child(4) {
    transform: rotate(2deg)
}

.photo-stack:hover .image-stacked:first-child {
    transform: scale(1.02)
}

.photo-stack:hover .image-stacked:nth-child(2) {
    transform: translate3d(10%, 0, 0) rotate(3deg)
}

.photo-stack:hover .image-stacked:nth-child(3) {
    transform: translate3d(-10%, 0, 0) rotate(-3deg)
}

.photo-stack:hover .image-stacked:nth-child(4) {
    transform: translate3d(2%, -5%, 0) rotate(2deg)
}

.photo-stack:hover .image-stacked:nth-child(5) {
    transform: translate3d(-5%, -2%, 0) rotate(2deg)
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0
}

.pointer-events-none {
    pointer-events: none
}

.pointer-events-auto {
    pointer-events: auto
}

.visible {
    visibility: visible
}

.invisible {
    visibility: hidden
}

.static {
    position: static
}

.fixed {
    position: fixed
}

.absolute {
    position: absolute
}

.relative {
    position: relative
}

.sticky {
    position: sticky
}

.-inset-2\.5 {
    inset: -.625rem
}

.inset-0 {
    inset: 0
}

.inset-x-0 {
    left: 0;
    right: 0
}

.inset-y-0 {
    top: 0;
    bottom: 0
}

.\!left-4 {
    left: 1rem !important
}

.-bottom-\[calc\(100\%-36px\)\] {
    bottom: calc(calc(100% - 36px) * -1)
}

.-left-1\.5 {
    left: -.375rem
}

.-top-1\.5 {
    top: -.375rem
}

.-top-2 {
    top: -.5rem
}

.bottom-0 {
    bottom: 0
}

.bottom-10 {
    bottom: 2.5rem
}

.bottom-14 {
    bottom: 3.5rem
}

.bottom-2\.5 {
    bottom: .625rem
}

.bottom-3 {
    bottom: .75rem
}

.bottom-5 {
    bottom: 1.25rem
}

.bottom-6 {
    bottom: 1.5rem
}

.bottom-8 {
    bottom: 2rem
}

.bottom-\[-10px\] {
    bottom: -10px
}

.bottom-\[-12px\] {
    bottom: -12px
}

.bottom-\[-25px\] {
    bottom: -25px
}

.bottom-\[1\.125rem\] {
    bottom: 1.125rem
}

.bottom-\[40px\] {
    bottom: 40px
}

.left-0 {
    left: 0
}

.left-1\/2 {
    left: 50%
}

.left-12 {
    left: 3rem
}

.left-2 {
    left: .5rem
}

.left-4 {
    left: 1rem
}

.left-\[-24px\] {
    left: -24px
}

.left-\[-9999px\] {
    left: -9999px
}

.left-\[16\.5px\] {
    left: 16.5px
}

.left-\[20\%\] {
    left: 20%
}

.left-\[7px\] {
    left: 7px
}

.right-0 {
    right: 0
}

.right-1 {
    right: .25rem
}

.right-10 {
    right: 2.5rem
}

.right-2 {
    right: .5rem
}

.right-4 {
    right: 1rem
}

.right-5 {
    right: 1.25rem
}

.right-6 {
    right: 1.5rem
}

.right-\[-20px\] {
    right: -20px
}

.right-\[-50px\] {
    right: -50px
}

.right-\[0px\] {
    right: 0
}

.right-\[6px\] {
    right: 6px
}

.top-0 {
    top: 0
}

.top-1 {
    top: .25rem
}

.top-1\/2 {
    top: 50%
}

.top-10 {
    top: 2.5rem
}

.top-2 {
    top: .5rem
}

.top-20 {
    top: 5rem
}

.top-4 {
    top: 1rem
}

.top-5 {
    top: 1.25rem
}

.top-\[-100px\] {
    top: -100px
}

.top-\[-26px\] {
    top: -26px
}

.top-\[-30px\] {
    top: -30px
}

.top-\[-50px\] {
    top: -50px
}

.top-\[-8px\] {
    top: -8px
}

.top-\[100\%\] {
    top: 100%
}

.top-\[22px\] {
    top: 22px
}

.top-\[24px\] {
    top: 24px
}

.top-\[30px\] {
    top: 30px
}

.top-\[36px\] {
    top: 36px
}

.top-\[56px\] {
    top: 56px
}

.top-\[7px\] {
    top: 7px
}

.top-\[87\%\] {
    top: 87%
}

.top-\[calc\(100\%\+15px\)\] {
    top: calc(100% + 15px)
}

.z-0 {
    z-index: 0
}

.z-10 {
    z-index: 10
}

.z-20 {
    z-index: 20
}

.z-30 {
    z-index: 30
}

.z-40 {
    z-index: 40
}

.z-50 {
    z-index: 50
}

.z-\[-1\] {
    z-index: -1
}

.z-\[100\] {
    z-index: 100
}

.z-\[1\] {
    z-index: 1
}

.z-\[75\] {
    z-index: 75
}

.z-\[900\] {
    z-index: 900
}

.z-\[9999999\] {
    z-index: 9999999
}

.z-\[999999\] {
    z-index: 999999
}

.order-1 {
    order: 1
}

.order-2 {
    order: 2
}

.order-\[-1\] {
    order: -1
}

.order-first {
    order: -9999
}

.\!col-span-12 {
    grid-column: span 12/span 12 !important
}

.\!col-span-3 {
    grid-column: span 3/span 3 !important
}

.col-span-1 {
    grid-column: span 1/span 1
}

.col-span-10 {
    grid-column: span 10/span 10
}

.col-span-12 {
    grid-column: span 12/span 12
}

.col-span-2 {
    grid-column: span 2/span 2
}

.col-span-3 {
    grid-column: span 3/span 3
}

.col-span-4 {
    grid-column: span 4/span 4
}

.col-span-5 {
    grid-column: span 5/span 5
}

.col-span-6 {
    grid-column: span 6/span 6
}

.col-span-7 {
    grid-column: span 7/span 7
}

.col-span-8 {
    grid-column: span 8/span 8
}

.col-span-9 {
    grid-column: span 9/span 9
}

.row-span-2 {
    grid-row: span 2/span 2
}

.float-right {
    float: right
}

.m-0\.5 {
    margin: .125rem
}

.m-2 {
    margin: .5rem
}

.m-3 {
    margin: .75rem
}

.m-4 {
    margin: 1rem
}

.m-6 {
    margin: 1.5rem
}

.m-auto {
    margin: auto
}

.-mx-4 {
    margin-left: -1rem;
    margin-right: -1rem
}

.-mx-8 {
    margin-left: -2rem;
    margin-right: -2rem
}

.-my-2 {
    margin-top: -.5rem;
    margin-bottom: -.5rem
}

.mx-0 {
    margin-left: 0;
    margin-right: 0
}

.mx-1 {
    margin-left: .25rem;
    margin-right: .25rem
}

.mx-1\.5 {
    margin-left: .375rem;
    margin-right: .375rem
}

.mx-10 {
    margin-left: 2.5rem;
    margin-right: 2.5rem
}

.mx-2 {
    margin-left: .5rem;
    margin-right: .5rem
}

.mx-2\.5 {
    margin-left: .625rem;
    margin-right: .625rem
}

.mx-32 {
    margin-left: 8rem;
    margin-right: 8rem
}

.mx-4 {
    margin-left: 1rem;
    margin-right: 1rem
}

.mx-6 {
    margin-left: 1.5rem;
    margin-right: 1.5rem
}

.mx-8 {
    margin-left: 2rem;
    margin-right: 2rem
}

.mx-\[-16px\] {
    margin-left: -16px;
    margin-right: -16px
}

.mx-\[-1rem\] {
    margin-left: -1rem;
    margin-right: -1rem
}

.mx-\[-20px\] {
    margin-left: -20px;
    margin-right: -20px
}

.mx-\[30px\] {
    margin-left: 30px;
    margin-right: 30px
}

.mx-auto {
    margin-left: auto;
    margin-right: auto
}

.my-1\.5 {
    margin-top: .375rem;
    margin-bottom: .375rem
}

.my-10 {
    margin-top: 2.5rem;
    margin-bottom: 2.5rem
}

.my-12 {
    margin-top: 3rem;
    margin-bottom: 3rem
}

.my-2 {
    margin-top: .5rem;
    margin-bottom: .5rem
}

.my-2\.5 {
    margin-top: .625rem;
    margin-bottom: .625rem
}

.my-3 {
    margin-top: .75rem;
    margin-bottom: .75rem
}

.my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem
}

.my-5 {
    margin-top: 1.25rem;
    margin-bottom: 1.25rem
}

.my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem
}

.my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem
}

.my-\[10px\] {
    margin-top: 10px;
    margin-bottom: 10px
}

.my-\[15px\] {
    margin-top: 15px;
    margin-bottom: 15px
}

.my-\[25px\] {
    margin-top: 25px;
    margin-bottom: 25px
}

.my-\[5px\] {
    margin-top: 5px;
    margin-bottom: 5px
}

.my-\[7px\] {
    margin-top: 7px;
    margin-bottom: 7px
}

.\!mb-0 {
    margin-bottom: 0 !important
}

.\!mt-0 {
    margin-top: 0 !important
}

.\!mt-3 {
    margin-top: .75rem !important
}

.-mb-0\.5 {
    margin-bottom: -.125rem
}

.-mb-8 {
    margin-bottom: -2rem
}

.-mb-px {
    margin-bottom: -1px
}

.-ml-px {
    margin-left: -1px
}

.-mr-1 {
    margin-right: -.25rem
}

.-mt-3 {
    margin-top: -.75rem
}

.-mt-\[17px\] {
    margin-top: -17px
}

.mb-0 {
    margin-bottom: 0
}

.mb-1 {
    margin-bottom: .25rem
}

.mb-10 {
    margin-bottom: 2.5rem
}

.mb-12 {
    margin-bottom: 3rem
}

.mb-16 {
    margin-bottom: 4rem
}

.mb-2 {
    margin-bottom: .5rem
}

.mb-20 {
    margin-bottom: 5rem
}

.mb-3 {
    margin-bottom: .75rem
}

.mb-4 {
    margin-bottom: 1rem
}

.mb-5 {
    margin-bottom: 1.25rem
}

.mb-6 {
    margin-bottom: 1.5rem
}

.mb-8 {
    margin-bottom: 2rem
}

.mb-\[-20px\] {
    margin-bottom: -20px
}

.mb-\[10px\] {
    margin-bottom: 10px
}

.mb-\[15px\] {
    margin-bottom: 15px
}

.mb-\[16px\] {
    margin-bottom: 16px
}

.mb-\[18px\] {
    margin-bottom: 18px
}

.mb-\[20px\] {
    margin-bottom: 20px
}

.mb-\[25px\] {
    margin-bottom: 25px
}

.ml-0 {
    margin-left: 0
}

.ml-1 {
    margin-left: .25rem
}

.ml-10 {
    margin-left: 2.5rem
}

.ml-2 {
    margin-left: .5rem
}

.ml-2\.5 {
    margin-left: .625rem
}

.ml-3 {
    margin-left: .75rem
}

.ml-4 {
    margin-left: 1rem
}

.ml-5 {
    margin-left: 1.25rem
}

.ml-6 {
    margin-left: 1.5rem
}

.ml-8 {
    margin-left: 2rem
}

.ml-\[-10px\] {
    margin-left: -10px
}

.ml-\[26px\] {
    margin-left: 26px
}

.ml-\[50px\] {
    margin-left: 50px
}

.ml-auto {
    margin-left: auto
}

.mr-1 {
    margin-right: .25rem
}

.mr-10 {
    margin-right: 2.5rem
}

.mr-16 {
    margin-right: 4rem
}

.mr-2 {
    margin-right: .5rem
}

.mr-2\.5 {
    margin-right: .625rem
}

.mr-3 {
    margin-right: .75rem
}

.mr-3\.5 {
    margin-right: .875rem
}

.mr-4 {
    margin-right: 1rem
}

.mr-5 {
    margin-right: 1.25rem
}

.mr-\[-10px\] {
    margin-right: -10px
}

.mr-\[-7px\] {
    margin-right: -7px
}

.mr-\[10px\] {
    margin-right: 10px
}

.mr-\[7px\] {
    margin-right: 7px
}

.mr-auto {
    margin-right: auto
}

.mt-0 {
    margin-top: 0
}

.mt-0\.5 {
    margin-top: .125rem
}

.mt-1 {
    margin-top: .25rem
}

.mt-1\.5 {
    margin-top: .375rem
}

.mt-10 {
    margin-top: 2.5rem
}

.mt-12 {
    margin-top: 3rem
}

.mt-2 {
    margin-top: .5rem
}

.mt-3 {
    margin-top: .75rem
}

.mt-4 {
    margin-top: 1rem
}

.mt-40 {
    margin-top: 10rem
}

.mt-5 {
    margin-top: 1.25rem
}

.mt-6 {
    margin-top: 1.5rem
}

.mt-7 {
    margin-top: 1.75rem
}

.mt-8 {
    margin-top: 2rem
}

.mt-9 {
    margin-top: 2.25rem
}

.mt-\[-18px\] {
    margin-top: -18px
}

.mt-\[-6px\] {
    margin-top: -6px
}

.mt-\[1rem\] {
    margin-top: 1rem
}

.mt-\[20px\] {
    margin-top: 20px
}

.mt-\[3px\] {
    margin-top: 3px
}

.mt-\[46px\] {
    margin-top: 46px
}

.mt-\[6px\] {
    margin-top: 6px
}

.mt-\[9px\] {
    margin-top: 9px
}

.mt-auto {
    margin-top: auto
}

.box-border {
    box-sizing: border-box
}

.line-clamp-1 {
    -webkit-line-clamp: 1
}

.line-clamp-1, .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical
}

.line-clamp-2 {
    -webkit-line-clamp: 2
}

.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3
}

.\!block {
    display: block !important
}

.block {
    display: block
}

.\!inline-block {
    display: inline-block !important
}

.inline-block {
    display: inline-block
}

.inline {
    display: inline
}

.flex {
    display: flex
}

.inline-flex {
    display: inline-flex
}

.table {
    display: table
}

.flow-root {
    display: flow-root
}

.grid {
    display: grid
}

.\!hidden {
    display: none !important
}

.hidden {
    display: none
}

.aspect-\[29\/18\] {
    aspect-ratio: 29/18
}

.aspect-\[29\/34\] {
    aspect-ratio: 29/34
}

.aspect-square {
    aspect-ratio: 1/1
}

.\!size-12 {
    width: 3rem !important;
    height: 3rem !important
}

.size-10 {
    width: 2.5rem;
    height: 2.5rem
}

.size-11 {
    width: 2.75rem;
    height: 2.75rem
}

.size-12 {
    width: 3rem;
    height: 3rem
}

.size-4 {
    width: 1rem;
    height: 1rem
}

.size-5 {
    width: 1.25rem;
    height: 1.25rem
}

.size-6 {
    width: 1.5rem;
    height: 1.5rem
}

.size-full {
    width: 100%;
    height: 100%
}

.\!h-10 {
    height: 2.5rem !important
}

.\!h-11 {
    height: 2.75rem !important
}

.\!h-\[46px\] {
    height: 46px !important
}

.\!h-\[649px\] {
    height: 649px !important
}

.\!h-auto {
    height: auto !important
}

.\!h-fit {
    height: -moz-fit-content !important;
    height: fit-content !important
}

.h-0 {
    height: 0
}

.h-0\.5 {
    height: .125rem
}

.h-1 {
    height: .25rem
}

.h-1\.5 {
    height: .375rem
}

.h-10 {
    height: 2.5rem
}

.h-11 {
    height: 2.75rem
}

.h-12 {
    height: 3rem
}

.h-14 {
    height: 3.5rem
}

.h-16 {
    height: 4rem
}

.h-2 {
    height: .5rem
}

.h-2\.5 {
    height: .625rem
}

.h-20 {
    height: 5rem
}

.h-28 {
    height: 7rem
}

.h-3 {
    height: .75rem
}

.h-3\.5 {
    height: .875rem
}

.h-4 {
    height: 1rem
}

.h-5 {
    height: 1.25rem
}

.h-6 {
    height: 1.5rem
}

.h-7 {
    height: 1.75rem
}

.h-8 {
    height: 2rem
}

.h-80 {
    height: 20rem
}

.h-9 {
    height: 2.25rem
}

.h-\[100vh\] {
    height: 100vh
}

.h-\[106px\] {
    height: 106px
}

.h-\[120px\] {
    height: 120px
}

.h-\[126px\] {
    height: 126px
}

.h-\[12px\] {
    height: 12px
}

.h-\[140px\] {
    height: 140px
}

.h-\[143px\] {
    height: 143px
}

.h-\[14px\] {
    height: 14px
}

.h-\[15px\] {
    height: 15px
}

.h-\[164px\] {
    height: 164px
}

.h-\[167px\] {
    height: 167px
}

.h-\[17px\] {
    height: 17px
}

.h-\[20px\] {
    height: 20px
}

.h-\[220px\] {
    height: 220px
}

.h-\[22px\] {
    height: 22px
}

.h-\[230px\] {
    height: 230px
}

.h-\[240px\] {
    height: 240px
}

.h-\[250px\] {
    height: 250px
}

.h-\[26px\] {
    height: 26px
}

.h-\[28px\] {
    height: 28px
}

.h-\[3\.25rem\] {
    height: 3.25rem
}

.h-\[300px\] {
    height: 300px
}

.h-\[305px\] {
    height: 305px
}

.h-\[30px\] {
    height: 30px
}

.h-\[34px\] {
    height: 34px
}

.h-\[360px\] {
    height: 360px
}

.h-\[36px\] {
    height: 36px
}

.h-\[40px\] {
    height: 40px
}

.h-\[41px\] {
    height: 41px
}

.h-\[42px\] {
    height: 42px
}

.h-\[45px\] {
    height: 45px
}

.h-\[468px\] {
    height: 468px
}

.h-\[46px\] {
    height: 46px
}

.h-\[48px\] {
    height: 48px
}

.h-\[50px\] {
    height: 50px
}

.h-\[510px\] {
    height: 510px
}

.h-\[52px\] {
    height: 52px
}

.h-\[55vh\] {
    height: 55vh
}

.h-\[57px\] {
    height: 57px
}

.h-\[58px\] {
    height: 58px
}

.h-\[60px\] {
    height: 60px
}

.h-\[64px\] {
    height: 64px
}

.h-\[6px\] {
    height: 6px
}

.h-\[70px\] {
    height: 70px
}

.h-\[70vh\] {
    height: 70vh
}

.h-\[80vh\] {
    height: 80vh
}

.h-\[84vh\] {
    height: 84vh
}

.h-\[88px\] {
    height: 88px
}

.h-\[90vh\] {
    height: 90vh
}

.h-\[calc\(100\%-113px\)\] {
    height: calc(100% - 113px)
}

.h-\[calc\(100\%-40px-24px-40px-8px\)\] {
    height: calc(100% - 40px - 24px - 40px - 8px)
}

.h-\[calc\(100\%-48px\)\] {
    height: calc(100% - 48px)
}

.h-\[calc\(100vh\)\] {
    height: calc(100vh)
}

.h-\[calc\(100vh-125px\)\] {
    height: calc(100vh - 125px)
}

.h-\[calc\(100vh-140px-40px-410px\)\] {
    height: calc(100vh - 140px - 40px - 410px)
}

.h-\[calc\(100vh-160px\)\] {
    height: calc(100vh - 160px)
}

.h-\[calc\(100vh-180px\)\] {
    height: calc(100vh - 180px)
}

.h-\[calc\(100vh-190px\)\] {
    height: calc(100vh - 190px)
}

.h-\[calc\(100vh-286px\)\] {
    height: calc(100vh - 286px)
}

.h-\[calc\(100vh-300px\)\] {
    height: calc(100vh - 300px)
}

.h-\[calc\(100vh-320px-100px\)\] {
    height: calc(100vh - 320px - 100px)
}

.h-\[calc\(100vh-360px\)\] {
    height: calc(100vh - 360px)
}

.h-\[calc\(100vh-380px-100px\)\] {
    height: calc(100vh - 380px - 100px)
}

.h-\[calc\(100vh-390px-100px\)\] {
    height: calc(100vh - 390px - 100px)
}

.h-\[calc\(100vh-420px\)\] {
    height: calc(100vh - 420px)
}

.h-\[calc\(100vh-430px\)\] {
    height: calc(100vh - 430px)
}

.h-\[calc\(100vh-460px\)\] {
    height: calc(100vh - 460px)
}

.h-\[calc\(100vh-480px\)\] {
    height: calc(100vh - 480px)
}

.h-\[calc\(100vh-80px\)\] {
    height: calc(100vh - 80px)
}

.h-\[calc\(100vh-80px-32px-169px-28px-34px-12px-32px\)\] {
    height: calc(100vh - 80px - 32px - 169px - 28px - 34px - 12px - 32px)
}

.h-\[calc\(100vh-82px\)\] {
    height: calc(100vh - 82px)
}

.h-\[calc\(100vh-90px\)\] {
    height: calc(100vh - 90px)
}

.h-auto {
    height: auto
}

.h-fit {
    height: -moz-fit-content;
    height: fit-content
}

.h-full {
    height: 100%
}

.h-px {
    height: 1px
}

.h-screen {
    height: 100vh
}

.\!max-h-\[210px\] {
    max-height: 210px !important
}

.max-h-0 {
    max-height: 0
}

.max-h-60 {
    max-height: 15rem
}

.max-h-8 {
    max-height: 2rem
}

.max-h-\[1000px\] {
    max-height: 1000px
}

.max-h-\[100vh\] {
    max-height: 100vh
}

.max-h-\[240px\] {
    max-height: 240px
}

.max-h-\[300px\] {
    max-height: 300px
}

.max-h-\[32rem\] {
    max-height: 32rem
}

.max-h-\[442px\] {
    max-height: 442px
}

.max-h-\[48vh\] {
    max-height: 48vh
}

.max-h-\[500px\] {
    max-height: 500px
}

.max-h-\[560px\] {
    max-height: 560px
}

.max-h-\[600px\] {
    max-height: 600px
}

.max-h-\[64px\] {
    max-height: 64px
}

.max-h-\[65vh\] {
    max-height: 65vh
}

.max-h-\[70vh\] {
    max-height: 70vh
}

.max-h-\[80vh\] {
    max-height: 80vh
}

.max-h-\[85vh\] {
    max-height: 85vh
}

.max-h-\[90vh\] {
    max-height: 90vh
}

.max-h-\[9999px\] {
    max-height: 9999px
}

.max-h-\[calc\(100vh-128px\)\] {
    max-height: calc(100vh - 128px)
}

.max-h-\[calc\(100vh-218px\)\] {
    max-height: calc(100vh - 218px)
}

.max-h-\[calc\(100vh-410px\)\] {
    max-height: calc(100vh - 410px)
}

.max-h-\[calc\(100vh-80px-32px-168px-28px-16px\)\] {
    max-height: calc(100vh - 80px - 32px - 168px - 28px - 16px)
}

.max-h-\[calc\(100vh-80px-32px-168px-28px-34px-16px-40px-12px-36px\)\] {
    max-height: calc(100vh - 80px - 32px - 168px - 28px - 34px - 16px - 40px - 12px - 36px)
}

.max-h-\[calc\(100vh-80px-32px-44px-20px-38px-32px-44px-32px\)\] {
    max-height: calc(100vh - 80px - 32px - 44px - 20px - 38px - 32px - 44px - 32px)
}

.\!min-h-fit {
    min-height: -moz-fit-content !important;
    min-height: fit-content !important
}

.min-h-0 {
    min-height: 0
}

.min-h-4 {
    min-height: 1rem
}

.min-h-5 {
    min-height: 1.25rem
}

.min-h-6 {
    min-height: 1.5rem
}

.min-h-\[160px\] {
    min-height: 160px
}

.min-h-\[18px\] {
    min-height: 18px
}

.min-h-\[1em\] {
    min-height: 1em
}

.min-h-\[20px\] {
    min-height: 20px
}

.min-h-\[220px\] {
    min-height: 220px
}

.min-h-\[28px\] {
    min-height: 28px
}

.min-h-\[30px\] {
    min-height: 30px
}

.min-h-\[398px\] {
    min-height: 398px
}

.min-h-\[400px\] {
    min-height: 400px
}

.min-h-\[40px\] {
    min-height: 40px
}

.min-h-\[50px\] {
    min-height: 50px
}

.min-h-\[50vh\] {
    min-height: 50vh
}

.min-h-\[510px\] {
    min-height: 510px
}

.min-h-\[520px\] {
    min-height: 520px
}

.min-h-\[52px\] {
    min-height: 52px
}

.min-h-\[5rem\] {
    min-height: 5rem
}

.min-h-\[600px\] {
    min-height: 600px
}

.min-h-\[640px\] {
    min-height: 640px
}

.min-h-\[68px\] {
    min-height: 68px
}

.min-h-\[70px\] {
    min-height: 70px
}

.min-h-\[75px\] {
    min-height: 75px
}

.min-h-\[90px\] {
    min-height: 90px
}

.min-h-\[calc\(100vh-0px\)\] {
    min-height: calc(100vh - 0px)
}

.min-h-\[calc\(100vh-260px\)\] {
    min-height: calc(100vh - 260px)
}

.min-h-\[calc\(100vh-64px\)\] {
    min-height: calc(100vh - 64px)
}

.min-h-full {
    min-height: 100%
}

.min-h-min {
    min-height: -moz-min-content;
    min-height: min-content
}

.\!w-40 {
    width: 10rem !important
}

.\!w-\[100px\] {
    width: 100px !important
}

.\!w-\[160px\] {
    width: 160px !important
}

.\!w-\[200px\] {
    width: 200px !important
}

.\!w-\[448px\] {
    width: 448px !important
}

.\!w-\[46px\] {
    width: 46px !important
}

.\!w-\[477px\] {
    width: 477px !important
}

.\!w-\[calc\(100\%-2rem\)\] {
    width: calc(100% - 2rem) !important
}

.\!w-auto {
    width: auto !important
}

.\!w-fit {
    width: -moz-fit-content !important;
    width: fit-content !important
}

.w-0 {
    width: 0
}

.w-1 {
    width: .25rem
}

.w-1\.5 {
    width: .375rem
}

.w-1\/2 {
    width: 50%
}

.w-1\/3 {
    width: 33.333333%
}

.w-1\/4 {
    width: 25%
}

.w-1\/6 {
    width: 16.666667%
}

.w-10 {
    width: 2.5rem
}

.w-11 {
    width: 2.75rem
}

.w-12 {
    width: 3rem
}

.w-14 {
    width: 3.5rem
}

.w-16 {
    width: 4rem
}

.w-2 {
    width: .5rem
}

.w-2\/12 {
    width: 16.666667%
}

.w-2\/4 {
    width: 50%
}

.w-2\/5 {
    width: 40%
}

.w-2\/6 {
    width: 33.333333%
}

.w-20 {
    width: 5rem
}

.w-24 {
    width: 6rem
}

.w-28 {
    width: 7rem
}

.w-3 {
    width: .75rem
}

.w-3\.5 {
    width: .875rem
}

.w-3\/5 {
    width: 60%
}

.w-3\/6 {
    width: 50%
}

.w-32 {
    width: 8rem
}

.w-4 {
    width: 1rem
}

.w-4\/5 {
    width: 80%
}

.w-40 {
    width: 10rem
}

.w-48 {
    width: 12rem
}

.w-5 {
    width: 1.25rem
}

.w-52 {
    width: 13rem
}

.w-56 {
    width: 14rem
}

.w-6 {
    width: 1.5rem
}

.w-6\/12 {
    width: 50%
}

.w-60 {
    width: 15rem
}

.w-64 {
    width: 16rem
}

.w-8 {
    width: 2rem
}

.w-8\/12 {
    width: 66.666667%
}

.w-80 {
    width: 20rem
}

.w-9 {
    width: 2.25rem
}

.w-\[10\.5rem\] {
    width: 10.5rem
}

.w-\[100\%\] {
    width: 100%
}

.w-\[100px\] {
    width: 100px
}

.w-\[11\.25rem\] {
    width: 11.25rem
}

.w-\[110px\] {
    width: 110px
}

.w-\[120px\] {
    width: 120px
}

.w-\[122px\] {
    width: 122px
}

.w-\[126px\] {
    width: 126px
}

.w-\[12px\] {
    width: 12px
}

.w-\[140px\] {
    width: 140px
}

.w-\[14px\] {
    width: 14px
}

.w-\[150px\] {
    width: 150px
}

.w-\[15px\] {
    width: 15px
}

.w-\[160px\] {
    width: 160px
}

.w-\[164px\] {
    width: 164px
}

.w-\[170px\] {
    width: 170px
}

.w-\[180px\] {
    width: 180px
}

.w-\[187px\] {
    width: 187px
}

.w-\[200px\] {
    width: 200px
}

.w-\[20px\] {
    width: 20px
}

.w-\[211px\] {
    width: 211px
}

.w-\[220px\] {
    width: 220px
}

.w-\[230px\] {
    width: 230px
}

.w-\[250px\] {
    width: 250px
}

.w-\[25px\] {
    width: 25px
}

.w-\[268px\] {
    width: 268px
}

.w-\[26px\] {
    width: 26px
}

.w-\[270px\] {
    width: 270px
}

.w-\[272px\] {
    width: 272px
}

.w-\[280px\] {
    width: 280px
}

.w-\[286px\] {
    width: 286px
}

.w-\[288px\] {
    width: 288px
}

.w-\[290px\] {
    width: 290px
}

.w-\[300px\] {
    width: 300px
}

.w-\[30px\] {
    width: 30px
}

.w-\[310px\] {
    width: 310px
}

.w-\[34px\] {
    width: 34px
}

.w-\[36px\] {
    width: 36px
}

.w-\[378px\] {
    width: 378px
}

.w-\[385px\] {
    width: 385px
}

.w-\[3px\] {
    width: 3px
}

.w-\[4\.5rem\] {
    width: 4.5rem
}

.w-\[40px\] {
    width: 40px
}

.w-\[440px\] {
    width: 440px
}

.w-\[48px\] {
    width: 48px
}

.w-\[50\%\] {
    width: 50%
}

.w-\[50px\] {
    width: 50px
}

.w-\[58px\] {
    width: 58px
}

.w-\[60px\] {
    width: 60px
}

.w-\[62px\] {
    width: 62px
}

.w-\[65px\] {
    width: 65px
}

.w-\[76px\] {
    width: 76px
}

.w-\[86px\] {
    width: 86px
}

.w-\[96px\] {
    width: 96px
}

.w-\[calc\(100\%-100px\)\] {
    width: calc(100% - 100px)
}

.w-\[calc\(100\%-12px\)\] {
    width: calc(100% - 12px)
}

.w-\[calc\(100\%-26px-20px\)\] {
    width: calc(100% - 26px - 20px)
}

.w-\[calc\(100\%-32px\)\] {
    width: calc(100% - 32px)
}

.w-\[calc\(30\%-12px\)\] {
    width: calc(30% - 12px)
}

.w-\[calc\(40\%-12px\)\] {
    width: calc(40% - 12px)
}

.w-\[calc\(50\%-16px\)\] {
    width: calc(50% - 16px)
}

.w-\[calc\(50\%-1rem\)\] {
    width: calc(50% - 1rem)
}

.w-\[calc\(50\%-2rem\)\] {
    width: calc(50% - 2rem)
}

.w-\[calc\(50\%-8px\)\] {
    width: calc(50% - 8px)
}

.w-auto {
    width: auto
}

.w-fit {
    width: -moz-fit-content;
    width: fit-content
}

.w-full {
    width: 100%
}

.w-px {
    width: 1px
}

.w-screen {
    width: 100vw
}

.min-w-0 {
    min-width: 0
}

.min-w-4 {
    min-width: 1rem
}

.min-w-5 {
    min-width: 1.25rem
}

.min-w-6 {
    min-width: 1.5rem
}

.min-w-\[100px\] {
    min-width: 100px
}

.min-w-\[112px\] {
    min-width: 112px
}

.min-w-\[113px\] {
    min-width: 113px
}

.min-w-\[115px\] {
    min-width: 115px
}

.min-w-\[120px\] {
    min-width: 120px
}

.min-w-\[126px\] {
    min-width: 126px
}

.min-w-\[130px\] {
    min-width: 130px
}

.min-w-\[140px\] {
    min-width: 140px
}

.min-w-\[150px\] {
    min-width: 150px
}

.min-w-\[164px\] {
    min-width: 164px
}

.min-w-\[170px\] {
    min-width: 170px
}

.min-w-\[190px\] {
    min-width: 190px
}

.min-w-\[200px\] {
    min-width: 200px
}

.min-w-\[206px\] {
    min-width: 206px
}

.min-w-\[232px\] {
    min-width: 232px
}

.min-w-\[240px\] {
    min-width: 240px
}

.min-w-\[250px\] {
    min-width: 250px
}

.min-w-\[273px\] {
    min-width: 273px
}

.min-w-\[300px\] {
    min-width: 300px
}

.min-w-\[316px\] {
    min-width: 316px
}

.min-w-\[320px\] {
    min-width: 320px
}

.min-w-\[36px\] {
    min-width: 36px
}

.min-w-\[59px\] {
    min-width: 59px
}

.min-w-\[60px\] {
    min-width: 60px
}

.min-w-\[660px\] {
    min-width: 660px
}

.min-w-\[80px\] {
    min-width: 80px
}

.min-w-\[86px\] {
    min-width: 86px
}

.min-w-\[87px\] {
    min-width: 87px
}

.min-w-\[90px\] {
    min-width: 90px
}

.min-w-\[calc\(100vw-0px\)\] {
    min-width: calc(100vw - 0px)
}

.min-w-full {
    min-width: 100%
}

.\!max-w-4xl {
    max-width: 56rem !important
}

.\!max-w-\[100\%\] {
    max-width: 100% !important
}

.max-w-2xl {
    max-width: 42rem
}

.max-w-3xl {
    max-width: 48rem
}

.max-w-4xl {
    max-width: 56rem
}

.max-w-5xl {
    max-width: 64rem
}

.max-w-7xl {
    max-width: 80rem
}

.max-w-\[1000px\] {
    max-width: 1000px
}

.max-w-\[1062px\] {
    max-width: 1062px
}

.max-w-\[1200px\] {
    max-width: 1200px
}

.max-w-\[120px\] {
    max-width: 120px
}

.max-w-\[1280px\] {
    max-width: 1280px
}

.max-w-\[152px\] {
    max-width: 152px
}

.max-w-\[160px\] {
    max-width: 160px
}

.max-w-\[200px\] {
    max-width: 200px
}

.max-w-\[250px\] {
    max-width: 250px
}

.max-w-\[32\.5rem\] {
    max-width: 32.5rem
}

.max-w-\[320px\] {
    max-width: 320px
}

.max-w-\[32rem\] {
    max-width: 32rem
}

.max-w-\[341px\] {
    max-width: 341px
}

.max-w-\[360px\] {
    max-width: 360px
}

.max-w-\[400px\] {
    max-width: 400px
}

.max-w-\[470px\] {
    max-width: 470px
}

.max-w-\[490px\] {
    max-width: 490px
}

.max-w-\[50rem\] {
    max-width: 50rem
}

.max-w-\[540px\] {
    max-width: 540px
}

.max-w-\[616px\] {
    max-width: 616px
}

.max-w-\[624px\] {
    max-width: 624px
}

.max-w-\[64px\] {
    max-width: 64px
}

.max-w-\[665px\] {
    max-width: 665px
}

.max-w-\[670px\] {
    max-width: 670px
}

.max-w-\[750px\] {
    max-width: 750px
}

.max-w-\[768px\] {
    max-width: 768px
}

.max-w-\[80\%\] {
    max-width: 80%
}

.max-w-\[800px\] {
    max-width: 800px
}

.max-w-\[840px\] {
    max-width: 840px
}

.max-w-\[860px\] {
    max-width: 860px
}

.max-w-\[874px\] {
    max-width: 874px
}

.max-w-\[876px\] {
    max-width: 876px
}

.max-w-\[88px\] {
    max-width: 88px
}

.max-w-\[96px\] {
    max-width: 96px
}

.max-w-fit {
    max-width: -moz-fit-content;
    max-width: fit-content
}

.max-w-full {
    max-width: 100%
}

.max-w-lg {
    max-width: 32rem
}

.max-w-md {
    max-width: 28rem
}

.max-w-none {
    max-width: none
}

.max-w-sm {
    max-width: 24rem
}

.max-w-xl {
    max-width: 36rem
}

.flex-1 {
    flex: 1 1 0%
}

.flex-auto {
    flex: 1 1 auto
}

.flex-none {
    flex: none
}

.flex-shrink-0, .shrink-0 {
    flex-shrink: 0
}

.grow {
    flex-grow: 1
}

.basis-0 {
    flex-basis: 0px
}

.basis-auto {
    flex-basis: auto
}

.table-fixed {
    table-layout: fixed
}

.origin-bottom-right {
    transform-origin: bottom right
}

.origin-top {
    transform-origin: top
}

.origin-top-right {
    transform-origin: top right
}

.-translate-x-1\/2 {
    --tw-translate-x: -50%
}

.-translate-x-1\/2, .-translate-y-1\.5 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-y-1\.5 {
    --tw-translate-y: -0.375rem
}

.-translate-y-1\/2 {
    --tw-translate-y: -50%
}

.-translate-y-1\/2, .translate-x-0 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-0 {
    --tw-translate-x: 0px
}

.translate-x-1 {
    --tw-translate-x: 0.25rem
}

.translate-x-1, .translate-x-5 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-5 {
    --tw-translate-x: 1.25rem
}

.translate-x-full {
    --tw-translate-x: 100%
}

.translate-x-full, .translate-y-0 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-y-0 {
    --tw-translate-y: 0px
}

.translate-y-1 {
    --tw-translate-y: 0.25rem
}

.translate-y-1, .translate-y-1\.5 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-y-1\.5 {
    --tw-translate-y: 0.375rem
}

.-rotate-90 {
    --tw-rotate: -90deg
}

.-rotate-90, .rotate-180 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-180 {
    --tw-rotate: 180deg
}

.rotate-45 {
    --tw-rotate: 45deg
}

.rotate-45, .rotate-90 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-90 {
    --tw-rotate: 90deg
}

.scale-0 {
    --tw-scale-x: 0;
    --tw-scale-y: 0
}

.scale-0, .scale-100 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1
}

.scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95
}

.scale-95, .transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.transform-none {
    transform: none
}

@keyframes pulse {
    50% {
        opacity: .5
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite
}

@keyframes spin {
    to {
        transform: rotate(1turn)
    }
}

.animate-spin {
    animation: spin 1s linear infinite
}

.\!cursor-default {
    cursor: default !important
}

.cursor-default {
    cursor: default
}

.cursor-no-drop {
    cursor: no-drop
}

.cursor-not-allowed {
    cursor: not-allowed
}

.cursor-pointer {
    cursor: pointer
}

.select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.resize-none {
    resize: none
}

.list-inside {
    list-style-position: inside
}

.list-disc {
    list-style-type: disc
}

.appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.columns-2 {
    -moz-columns: 2;
    column-count: 2
}

.grid-cols-1 {
    grid-template-columns:repeat(1, minmax(0, 1fr))
}

.grid-cols-11 {
    grid-template-columns:repeat(11, minmax(0, 1fr))
}

.grid-cols-12 {
    grid-template-columns:repeat(12, minmax(0, 1fr))
}

.grid-cols-2 {
    grid-template-columns:repeat(2, minmax(0, 1fr))
}

.grid-cols-3 {
    grid-template-columns:repeat(3, minmax(0, 1fr))
}

.grid-cols-4 {
    grid-template-columns:repeat(4, minmax(0, 1fr))
}

.grid-cols-48 {
    grid-template-columns:repeat(48, minmax(0, 1fr))
}

.grid-cols-\[1\.5fr_repeat\(4\2c minmax\(0\2c 1fr\)\)\] {
    grid-template-columns:1.5fr repeat(4, minmax(0, 1fr))
}

.grid-cols-\[122px_1fr\] {
    grid-template-columns:122px 1fr
}

.grid-cols-\[1fr_10px\] {
    grid-template-columns:1fr 10px
}

.grid-cols-\[1fr_80px\] {
    grid-template-columns:1fr 80px
}

.grid-cols-\[60px_1fr\] {
    grid-template-columns:60px 1fr
}

.grid-cols-\[76px_1fr\] {
    grid-template-columns:76px 1fr
}

.grid-rows-2 {
    grid-template-rows:repeat(2, minmax(0, 1fr))
}

.flex-row {
    flex-direction: row
}

.flex-col {
    flex-direction: column
}

.flex-col-reverse {
    flex-direction: column-reverse
}

.flex-wrap {
    flex-wrap: wrap
}

.place-items-center {
    place-items: center
}

.items-start {
    align-items: flex-start
}

.items-end {
    align-items: flex-end
}

.items-center {
    align-items: center
}

.items-baseline {
    align-items: baseline
}

.items-stretch {
    align-items: stretch
}

.justify-start {
    justify-content: flex-start
}

.justify-end {
    justify-content: flex-end
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.gap-0\.5 {
    gap: .125rem
}

.gap-1 {
    gap: .25rem
}

.gap-10 {
    gap: 2.5rem
}

.gap-12 {
    gap: 3rem
}

.gap-2 {
    gap: .5rem
}

.gap-3 {
    gap: .75rem
}

.gap-4 {
    gap: 1rem
}

.gap-5 {
    gap: 1.25rem
}

.gap-6 {
    gap: 1.5rem
}

.gap-8 {
    gap: 2rem
}

.gap-9 {
    gap: 2.25rem
}

.gap-\[10px\] {
    gap: 10px
}

.gap-\[11px\] {
    gap: 11px
}

.gap-\[14px\] {
    gap: 14px
}

.gap-\[15px\] {
    gap: 15px
}

.gap-\[1px\] {
    gap: 1px
}

.gap-\[25px\] {
    gap: 25px
}

.gap-\[30px\] {
    gap: 30px
}

.gap-\[35px\] {
    gap: 35px
}

.gap-\[50px\] {
    gap: 50px
}

.gap-\[5px\] {
    gap: 5px
}

.gap-\[7px\] {
    gap: 7px
}

.gap-x-1 {
    -moz-column-gap: .25rem;
    column-gap: .25rem
}

.gap-x-1\.5 {
    -moz-column-gap: .375rem;
    column-gap: .375rem
}

.gap-x-2 {
    -moz-column-gap: .5rem;
    column-gap: .5rem
}

.gap-x-2\.5 {
    -moz-column-gap: .625rem;
    column-gap: .625rem
}

.gap-x-3 {
    -moz-column-gap: .75rem;
    column-gap: .75rem
}

.gap-x-4 {
    -moz-column-gap: 1rem;
    column-gap: 1rem
}

.gap-x-5 {
    -moz-column-gap: 1.25rem;
    column-gap: 1.25rem
}

.gap-x-6 {
    -moz-column-gap: 1.5rem;
    column-gap: 1.5rem
}

.gap-x-8 {
    -moz-column-gap: 2rem;
    column-gap: 2rem
}

.gap-y-1 {
    row-gap: .25rem
}

.gap-y-10 {
    row-gap: 2.5rem
}

.gap-y-12 {
    row-gap: 3rem
}

.gap-y-2 {
    row-gap: .5rem
}

.gap-y-3 {
    row-gap: .75rem
}

.gap-y-4 {
    row-gap: 1rem
}

.gap-y-6 {
    row-gap: 1.5rem
}

.gap-y-8 {
    row-gap: 2rem
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.25rem * var(--tw-space-x-reverse));
    margin-left: calc(.25rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-11 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2.75rem * var(--tw-space-x-reverse));
    margin-left: calc(2.75rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.5rem * var(--tw-space-x-reverse));
    margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.75rem * var(--tw-space-x-reverse));
    margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse))
}

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.125rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.125rem * var(--tw-space-y-reverse))
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.25rem * var(--tw-space-y-reverse))
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.5rem * var(--tw-space-y-reverse))
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.75rem * var(--tw-space-y-reverse))
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse))
}

.space-y-5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.25rem * var(--tw-space-y-reverse))
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse))
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse))
}

.divide-x > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)))
}

.divide-x-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(0px * var(--tw-divide-x-reverse));
    border-left-width: calc(0px * calc(1 - var(--tw-divide-x-reverse)))
}

.divide-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(2px * var(--tw-divide-x-reverse));
    border-left-width: calc(2px * calc(1 - var(--tw-divide-x-reverse)))
}

.divide-x-\[5px\] > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(5px * var(--tw-divide-x-reverse));
    border-left-width: calc(5px * calc(1 - var(--tw-divide-x-reverse)))
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(1px * var(--tw-divide-y-reverse))
}

.divide-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(2px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(2px * var(--tw-divide-y-reverse))
}

.divide-\[\#D4D4D4\] > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(212 212 212/var(--tw-divide-opacity))
}

.divide-\[\#E5E7EB\] > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-divide-opacity))
}

.divide-gray-300 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-divide-opacity))
}

.divide-oms-gray-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(242 242 242/var(--tw-divide-opacity))
}

.divide-oms-gray-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(216 216 218/var(--tw-divide-opacity))
}

.divide-secondary-100 > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--color-secondary-100)
}

.divide-white > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-divide-opacity))
}

.self-end {
    align-self: flex-end
}

.self-center {
    align-self: center
}

.justify-self-end {
    justify-self: end
}

.overflow-auto {
    overflow: auto
}

.overflow-hidden {
    overflow: hidden
}

.\!overflow-visible {
    overflow: visible !important
}

.overflow-visible {
    overflow: visible
}

.overflow-scroll {
    overflow: scroll
}

.overflow-x-auto {
    overflow-x: auto
}

.overflow-y-auto {
    overflow-y: auto
}

.overflow-x-hidden {
    overflow-x: hidden
}

.\!overflow-x-visible {
    overflow-x: visible !important
}

.overflow-x-scroll {
    overflow-x: scroll
}

.overflow-y-scroll {
    overflow-y: scroll
}

.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.whitespace-normal {
    white-space: normal
}

.whitespace-nowrap {
    white-space: nowrap
}

.whitespace-pre-wrap {
    white-space: pre-wrap
}

.break-words {
    overflow-wrap: break-word
}

.break-all {
    word-break: break-all
}

.\!rounded-none {
    border-radius: 0 !important
}

.\!rounded-xl {
    border-radius: .75rem !important
}

.rounded {
    border-radius: .25rem
}

.rounded-2xl {
    border-radius: 1rem
}

.rounded-3xl {
    border-radius: 1.5rem
}

.rounded-\[10px\] {
    border-radius: 10px
}

.rounded-\[20px\] {
    border-radius: 20px
}

.rounded-\[4px\] {
    border-radius: 4px
}

.rounded-\[50\%\] {
    border-radius: 50%
}

.rounded-\[50px\] {
    border-radius: 50px
}

.rounded-full {
    border-radius: 9999px
}

.rounded-lg {
    border-radius: .5rem
}

.rounded-md {
    border-radius: .375rem
}

.rounded-none {
    border-radius: 0
}

.rounded-sm {
    border-radius: .125rem
}

.rounded-xl {
    border-radius: .75rem
}

.\!rounded-l-none {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important
}

.\!rounded-r-none {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important
}

.rounded-b-3xl {
    border-bottom-right-radius: 1.5rem;
    border-bottom-left-radius: 1.5rem
}

.rounded-b-\[20px\] {
    border-bottom-right-radius: 20px;
    border-bottom-left-radius: 20px
}

.rounded-b-xl {
    border-bottom-right-radius: .75rem;
    border-bottom-left-radius: .75rem
}

.rounded-l {
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.rounded-l-\[10px\] {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px
}

.rounded-l-\[12px\] {
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px
}

.rounded-l-none {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.rounded-r-\[10px\] {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px
}

.rounded-r-md {
    border-top-right-radius: .375rem;
    border-bottom-right-radius: .375rem
}

.rounded-t-3xl {
    border-top-left-radius: 1.5rem;
    border-top-right-radius: 1.5rem
}

.rounded-t-\[30px\] {
    border-top-left-radius: 30px;
    border-top-right-radius: 30px
}

.rounded-t-xl {
    border-top-left-radius: .75rem;
    border-top-right-radius: .75rem
}

.rounded-br {
    border-bottom-right-radius: .25rem
}

.rounded-tl {
    border-top-left-radius: .25rem
}

.\!border {
    border-width: 1px !important
}

.\!border-0 {
    border-width: 0 !important
}

.border {
    border-width: 1px
}

.border-0 {
    border-width: 0
}

.border-2 {
    border-width: 2px
}

.border-4 {
    border-width: 4px
}

.border-\[1\.5px\] {
    border-width: 1.5px
}

.border-x-0 {
    border-left-width: 0;
    border-right-width: 0
}

.border-y {
    border-top-width: 1px;
    border-bottom-width: 1px
}

.\!border-b {
    border-bottom-width: 1px !important
}

.border-b {
    border-bottom-width: 1px
}

.border-b-2 {
    border-bottom-width: 2px
}

.border-b-8 {
    border-bottom-width: 8px
}

.border-l {
    border-left-width: 1px
}

.border-l-0 {
    border-left-width: 0
}

.border-l-4 {
    border-left-width: 4px
}

.border-l-\[2px\] {
    border-left-width: 2px
}

.border-l-\[3px\] {
    border-left-width: 3px
}

.border-r {
    border-right-width: 1px
}

.border-t {
    border-top-width: 1px
}

.border-t-2 {
    border-top-width: 2px
}

.border-solid {
    border-style: solid
}

.border-dashed {
    border-style: dashed
}

.border-dotted {
    border-style: dotted
}

.\!border-none {
    border-style: none !important
}

.border-none {
    border-style: none
}

.\!border-\[\#E5E7EB\] {
    --tw-border-opacity: 1 !important;
    border-color: rgb(229 231 235/var(--tw-border-opacity)) !important
}

.\!border-oms-gray-2 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(205 205 205/var(--tw-border-opacity)) !important
}

.\!border-oms-green-3 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(104 215 139/var(--tw-border-opacity)) !important
}

.\!border-oms-green-6 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(0 135 0/var(--tw-border-opacity)) !important
}

.\!border-oms-orange-3 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(211 185 115/var(--tw-border-opacity)) !important
}

.\!border-primary {
    border-color: var(--color-primary) !important
}

.\!border-primary-200 {
    border-color: var(--color-primary-200) !important
}

.\!border-primary-light {
    border-color: var(--color-primary-light) !important
}

.\!border-rose-500 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(244 63 94/var(--tw-border-opacity)) !important
}

.\!border-secondary {
    border-color: var(--color-secondary) !important
}

.\!border-secondary-200 {
    border-color: var(--color-secondary-200) !important
}

.border-\[\#B5B5B5\] {
    --tw-border-opacity: 1;
    border-color: rgb(181 181 181/var(--tw-border-opacity))
}

.border-\[\#C4C5C7\] {
    --tw-border-opacity: 1;
    border-color: rgb(196 197 199/var(--tw-border-opacity))
}

.border-\[\#C5D4FA\] {
    --tw-border-opacity: 1;
    border-color: rgb(197 212 250/var(--tw-border-opacity))
}

.border-\[\#C8E6C9\] {
    --tw-border-opacity: 1;
    border-color: rgb(200 230 201/var(--tw-border-opacity))
}

.border-\[\#CC7A00\] {
    --tw-border-opacity: 1;
    border-color: rgb(204 122 0/var(--tw-border-opacity))
}

.border-\[\#D1D5DB\] {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity))
}

.border-\[\#D4D4D4\] {
    --tw-border-opacity: 1;
    border-color: rgb(212 212 212/var(--tw-border-opacity))
}

.border-\[\#D8D8DA\] {
    --tw-border-opacity: 1;
    border-color: rgb(216 216 218/var(--tw-border-opacity))
}

.border-\[\#DADADA\] {
    --tw-border-opacity: 1;
    border-color: rgb(218 218 218/var(--tw-border-opacity))
}

.border-\[\#E5BBED\] {
    --tw-border-opacity: 1;
    border-color: rgb(229 187 237/var(--tw-border-opacity))
}

.border-\[\#E5E7EB\] {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity))
}

.border-\[\#EAEAEA\] {
    --tw-border-opacity: 1;
    border-color: rgb(234 234 234/var(--tw-border-opacity))
}

.border-\[\#FF7900\]\/30 {
    border-color: rgba(255, 121, 0, .3)
}

.border-\[\#FFAE0F\] {
    --tw-border-opacity: 1;
    border-color: rgb(255 174 15/var(--tw-border-opacity))
}

.border-\[\#f2f2f2\] {
    --tw-border-opacity: 1;
    border-color: rgb(242 242 242/var(--tw-border-opacity))
}

.border-\[rgba\(0\2c 0\2c 0\2c 0\.08\)\] {
    border-color: rgba(0, 0, 0, .08)
}

.border-\[rgba\(0\2c 0\2c 0\2c 0\.12\)\] {
    border-color: rgba(0, 0, 0, .12)
}

.border-amber-500 {
    --tw-border-opacity: 1;
    border-color: rgb(245 158 11/var(--tw-border-opacity))
}

.border-black {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0/var(--tw-border-opacity))
}

.border-blue-300 {
    --tw-border-opacity: 1;
    border-color: rgb(147 197 253/var(--tw-border-opacity))
}

.border-current {
    border-color: currentColor
}

.border-danger-200 {
    border-color: var(--color-danger-200)
}

.border-danger-600 {
    border-color: var(--color-danger-600)
}

.border-danger-700 {
    border-color: var(--color-danger-700)
}

.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity))
}

.border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity))
}

.border-gray-900 {
    --tw-border-opacity: 1;
    border-color: rgb(17 24 39/var(--tw-border-opacity))
}

.border-oms-blue-2 {
    --tw-border-opacity: 1;
    border-color: rgb(14 128 143/var(--tw-border-opacity))
}

.border-oms-gray {
    --tw-border-opacity: 1;
    border-color: rgb(84 84 84/var(--tw-border-opacity))
}

.border-oms-gray-10 {
    --tw-border-opacity: 1;
    border-color: rgb(97 100 107/var(--tw-border-opacity))
}

.border-oms-gray-2 {
    --tw-border-opacity: 1;
    border-color: rgb(205 205 205/var(--tw-border-opacity))
}

.border-oms-gray-3 {
    --tw-border-opacity: 1;
    border-color: rgb(242 242 242/var(--tw-border-opacity))
}

.border-oms-gray-6 {
    --tw-border-opacity: 1;
    border-color: rgb(216 216 218/var(--tw-border-opacity))
}

.border-oms-gray-9 {
    --tw-border-opacity: 1;
    border-color: rgb(235 236 236/var(--tw-border-opacity))
}

.border-oms-green {
    --tw-border-opacity: 1;
    border-color: rgb(0 130 0/var(--tw-border-opacity))
}

.border-oms-green-5 {
    --tw-border-opacity: 1;
    border-color: rgb(1 152 88/var(--tw-border-opacity))
}

.border-oms-green-6 {
    --tw-border-opacity: 1;
    border-color: rgb(0 135 0/var(--tw-border-opacity))
}

.border-oms-orange {
    --tw-border-opacity: 1;
    border-color: rgb(255 184 0/var(--tw-border-opacity))
}

.border-oms-orange-5 {
    --tw-border-opacity: 1;
    border-color: rgb(235 155 0/var(--tw-border-opacity))
}

.border-oms-orange-6 {
    --tw-border-opacity: 1;
    border-color: rgb(255 174 15/var(--tw-border-opacity))
}

.border-oms-red {
    --tw-border-opacity: 1;
    border-color: rgb(236 28 36/var(--tw-border-opacity))
}

.border-oms-red-3 {
    --tw-border-opacity: 1;
    border-color: rgb(179 49 49/var(--tw-border-opacity))
}

.border-pink-400 {
    --tw-border-opacity: 1;
    border-color: rgb(244 114 182/var(--tw-border-opacity))
}

.border-primary {
    border-color: var(--color-primary)
}

.border-primary-light {
    border-color: var(--color-primary-light)
}

.border-primary-light-300 {
    border-color: var(--color-primary-light-300)
}

.border-rose-500 {
    --tw-border-opacity: 1;
    border-color: rgb(244 63 94/var(--tw-border-opacity))
}

.border-secondary {
    border-color: var(--color-secondary)
}

.border-secondary-100 {
    border-color: var(--color-secondary-100)
}

.border-secondary-200 {
    border-color: var(--color-secondary-200)
}

.border-secondary-900 {
    border-color: var(--color-secondary-900)
}

.border-secondary-light {
    border-color: var(--color-secondary-light)
}

.border-secondary-light-300 {
    border-color: var(--color-secondary-light-300)
}

.border-secondary-light-400 {
    border-color: var(--color-secondary-light-400)
}

.border-transparent {
    border-color: transparent
}

.border-violet-600 {
    --tw-border-opacity: 1;
    border-color: rgb(124 58 237/var(--tw-border-opacity))
}

.border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-border-opacity))
}

.border-yellow-400 {
    --tw-border-opacity: 1;
    border-color: rgb(250 204 21/var(--tw-border-opacity))
}

.border-yellow-500 {
    --tw-border-opacity: 1;
    border-color: rgb(234 179 8/var(--tw-border-opacity))
}

.border-yellow-800 {
    --tw-border-opacity: 1;
    border-color: rgb(133 77 14/var(--tw-border-opacity))
}

.\!border-b-\[\#E7E7F1\] {
    --tw-border-opacity: 1 !important;
    border-bottom-color: rgb(231 231 241/var(--tw-border-opacity)) !important
}

.\!border-b-oms-green {
    --tw-border-opacity: 1 !important;
    border-bottom-color: rgb(0 130 0/var(--tw-border-opacity)) !important
}

.\!border-b-oms-green-7 {
    --tw-border-opacity: 1 !important;
    border-bottom-color: rgb(0 104 0/var(--tw-border-opacity)) !important
}

.border-b-\[\#E5E7EB\] {
    --tw-border-opacity: 1;
    border-bottom-color: rgb(229 231 235/var(--tw-border-opacity))
}

.border-b-transparent {
    border-bottom-color: transparent
}

.border-l-\[\#D9D9D9\] {
    --tw-border-opacity: 1;
    border-left-color: rgb(217 217 217/var(--tw-border-opacity))
}

.border-l-oms-blue-3 {
    --tw-border-opacity: 1;
    border-left-color: rgb(42 110 187/var(--tw-border-opacity))
}

.border-l-oms-green {
    --tw-border-opacity: 1;
    border-left-color: rgb(0 130 0/var(--tw-border-opacity))
}

.border-l-oms-green-12 {
    --tw-border-opacity: 1;
    border-left-color: rgb(109 164 0/var(--tw-border-opacity))
}

.border-l-oms-orange-11 {
    --tw-border-opacity: 1;
    border-left-color: rgb(229 39 0/var(--tw-border-opacity))
}

.border-t-\[\#e4e4e4\] {
    --tw-border-opacity: 1;
    border-top-color: rgb(228 228 228/var(--tw-border-opacity))
}

.border-t-oms-gray-2 {
    --tw-border-opacity: 1;
    border-top-color: rgb(205 205 205/var(--tw-border-opacity))
}

.border-t-oms-gray-3 {
    --tw-border-opacity: 1;
    border-top-color: rgb(242 242 242/var(--tw-border-opacity))
}

.border-t-oms-gray-9 {
    border-top-color: rgb(235 236 236/var(--tw-border-opacity))
}

.border-opacity-100, .border-t-oms-gray-9 {
    --tw-border-opacity: 1
}

.border-opacity-60 {
    --tw-border-opacity: 0.6
}

.border-opacity-\[0\.08\] {
    --tw-border-opacity: 0.08
}

.\!bg-\[\#EBECEC\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(235 236 236/var(--tw-bg-opacity)) !important
}

.\!bg-\[\#F2F2F2\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(242 242 242/var(--tw-bg-opacity)) !important
}

.\!bg-\[\#f9f9f9\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(249 249 249/var(--tw-bg-opacity)) !important
}

.\!bg-black {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(0 0 0/var(--tw-bg-opacity)) !important
}

.\!bg-oms-gray-11 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(156 158 162/var(--tw-bg-opacity)) !important
}

.\!bg-oms-green-10 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(146 204 146/var(--tw-bg-opacity)) !important
}

.\!bg-oms-green-4 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(236 255 242/var(--tw-bg-opacity)) !important
}

.\!bg-oms-green-8 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(208 231 208/var(--tw-bg-opacity)) !important
}

.\!bg-oms-green-9 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(82 183 82/var(--tw-bg-opacity)) !important
}

.\!bg-oms-orange-4 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 234 178/var(--tw-bg-opacity)) !important
}

.\!bg-oms-orange-6 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 174 15/var(--tw-bg-opacity)) !important
}

.\!bg-oms-orange-8 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 236 198/var(--tw-bg-opacity)) !important
}

.\!bg-oms-red-6 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(199 71 71/var(--tw-bg-opacity)) !important
}

.\!bg-oms-red-8 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(253 221 221/var(--tw-bg-opacity)) !important
}

.\!bg-primary {
    background-color: var(--color-primary) !important
}

.\!bg-primary-light {
    background-color: var(--color-primary-light) !important
}

.\!bg-transparent {
    background-color: transparent !important
}

.\!bg-white {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 255 255/var(--tw-bg-opacity)) !important
}

.bg-\[\#008700\] {
    --tw-bg-opacity: 1;
    background-color: rgb(0 135 0/var(--tw-bg-opacity))
}

.bg-\[\#00A551\] {
    --tw-bg-opacity: 1;
    background-color: rgb(0 165 81/var(--tw-bg-opacity))
}

.bg-\[\#222\] {
    --tw-bg-opacity: 1;
    background-color: rgb(34 34 34/var(--tw-bg-opacity))
}

.bg-\[\#3156B3\] {
    --tw-bg-opacity: 1;
    background-color: rgb(49 86 179/var(--tw-bg-opacity))
}

.bg-\[\#31B387\] {
    --tw-bg-opacity: 1;
    background-color: rgb(49 179 135/var(--tw-bg-opacity))
}

.bg-\[\#34B53A\] {
    --tw-bg-opacity: 1;
    background-color: rgb(52 181 58/var(--tw-bg-opacity))
}

.bg-\[\#3a3d46\] {
    --tw-bg-opacity: 1;
    background-color: rgb(58 61 70/var(--tw-bg-opacity))
}

.bg-\[\#3b5998\] {
    --tw-bg-opacity: 1;
    background-color: rgb(59 89 152/var(--tw-bg-opacity))
}

.bg-\[\#4339F2\] {
    --tw-bg-opacity: 1;
    background-color: rgb(67 57 242/var(--tw-bg-opacity))
}

.bg-\[\#5C31B3\] {
    --tw-bg-opacity: 1;
    background-color: rgb(92 49 179/var(--tw-bg-opacity))
}

.bg-\[\#79ddc4\] {
    --tw-bg-opacity: 1;
    background-color: rgb(121 221 196/var(--tw-bg-opacity))
}

.bg-\[\#8F02A8\] {
    --tw-bg-opacity: 1;
    background-color: rgb(143 2 168/var(--tw-bg-opacity))
}

.bg-\[\#9CA3AF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(156 163 175/var(--tw-bg-opacity))
}

.bg-\[\#ADD8E6\] {
    --tw-bg-opacity: 1;
    background-color: rgb(173 216 230/var(--tw-bg-opacity))
}

.bg-\[\#B33131\] {
    --tw-bg-opacity: 1;
    background-color: rgb(179 49 49/var(--tw-bg-opacity))
}

.bg-\[\#B35A31\] {
    --tw-bg-opacity: 1;
    background-color: rgb(179 90 49/var(--tw-bg-opacity))
}

.bg-\[\#B5B5B5\] {
    --tw-bg-opacity: 1;
    background-color: rgb(181 181 181/var(--tw-bg-opacity))
}

.bg-\[\#B7B7B7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(183 183 183/var(--tw-bg-opacity))
}

.bg-\[\#C6C6C6\] {
    --tw-bg-opacity: 1;
    background-color: rgb(198 198 198/var(--tw-bg-opacity))
}

.bg-\[\#CC6100\] {
    --tw-bg-opacity: 1;
    background-color: rgb(204 97 0/var(--tw-bg-opacity))
}

.bg-\[\#D0E7D0\] {
    --tw-bg-opacity: 1;
    background-color: rgb(208 231 208/var(--tw-bg-opacity))
}

.bg-\[\#D2FBDF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.bg-\[\#D9D9D9\] {
    --tw-bg-opacity: 1;
    background-color: rgb(217 217 217/var(--tw-bg-opacity))
}

.bg-\[\#DDE6FD\] {
    --tw-bg-opacity: 1;
    background-color: rgb(221 230 253/var(--tw-bg-opacity))
}

.bg-\[\#E0FAE0\] {
    --tw-bg-opacity: 1;
    background-color: rgb(224 250 224/var(--tw-bg-opacity))
}

.bg-\[\#E2E2E2\] {
    --tw-bg-opacity: 1;
    background-color: rgb(226 226 226/var(--tw-bg-opacity))
}

.bg-\[\#E2FBD7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(226 251 215/var(--tw-bg-opacity))
}

.bg-\[\#E3F7ED\] {
    --tw-bg-opacity: 1;
    background-color: rgb(227 247 237/var(--tw-bg-opacity))
}

.bg-\[\#E6E6E6\] {
    --tw-bg-opacity: 1;
    background-color: rgb(230 230 230/var(--tw-bg-opacity))
}

.bg-\[\#E8E8E8\] {
    --tw-bg-opacity: 1;
    background-color: rgb(232 232 232/var(--tw-bg-opacity))
}

.bg-\[\#EBEBEB\] {
    --tw-bg-opacity: 1;
    background-color: rgb(235 235 235/var(--tw-bg-opacity))
}

.bg-\[\#EBECEC\] {
    --tw-bg-opacity: 1;
    background-color: rgb(235 236 236/var(--tw-bg-opacity))
}

.bg-\[\#EC1C24\] {
    --tw-bg-opacity: 1;
    background-color: rgb(236 28 36/var(--tw-bg-opacity))
}

.bg-\[\#ECECEC\] {
    --tw-bg-opacity: 1;
    background-color: rgb(236 236 236/var(--tw-bg-opacity))
}

.bg-\[\#ECEFF2\] {
    --tw-bg-opacity: 1;
    background-color: rgb(236 239 242/var(--tw-bg-opacity))
}

.bg-\[\#F1F1F1\] {
    --tw-bg-opacity: 1;
    background-color: rgb(241 241 241/var(--tw-bg-opacity))
}

.bg-\[\#F1F5F1\] {
    --tw-bg-opacity: 1;
    background-color: rgb(241 245 241/var(--tw-bg-opacity))
}

.bg-\[\#F1FCF1\] {
    --tw-bg-opacity: 1;
    background-color: rgb(241 252 241/var(--tw-bg-opacity))
}

.bg-\[\#F5F5F6\] {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 246/var(--tw-bg-opacity))
}

.bg-\[\#F6F6F7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(246 246 247/var(--tw-bg-opacity))
}

.bg-\[\#F7F7F7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(247 247 247/var(--tw-bg-opacity))
}

.bg-\[\#FAF6FF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(250 246 255/var(--tw-bg-opacity))
}

.bg-\[\#FF7900\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 121 0/var(--tw-bg-opacity))
}

.bg-\[\#FFEFEC\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 239 236/var(--tw-bg-opacity))
}

.bg-\[\#FFF7DD\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 247 221/var(--tw-bg-opacity))
}

.bg-\[\#FFF7DE\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 247 222/var(--tw-bg-opacity))
}

.bg-\[\#FFFCF8\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 252 248/var(--tw-bg-opacity))
}

.bg-\[\#eee\] {
    --tw-bg-opacity: 1;
    background-color: rgb(238 238 238/var(--tw-bg-opacity))
}

.bg-\[\#f4f7f4\] {
    --tw-bg-opacity: 1;
    background-color: rgb(244 247 244/var(--tw-bg-opacity))
}

.bg-\[\#f6f6f7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(246 246 247/var(--tw-bg-opacity))
}

.bg-\[\#ff8f00\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 143 0/var(--tw-bg-opacity))
}

.bg-\[rgba\(241\2c 241\2c 241\2c 0\.7\)\] {
    background-color: hsla(0, 0%, 95%, .7)
}

.bg-amber-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 243 199/var(--tw-bg-opacity))
}

.bg-amber-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(245 158 11/var(--tw-bg-opacity))
}

.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity))
}

.bg-black\/30 {
    background-color: rgba(0, 0, 0, .3)
}

.bg-black\/50 {
    background-color: rgba(0, 0, 0, .5)
}

.bg-black\/60 {
    background-color: rgba(0, 0, 0, .6)
}

.bg-black\/\[\.08\] {
    background-color: rgba(0, 0, 0, .08)
}

.bg-blue-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(147 197 253/var(--tw-bg-opacity))
}

.bg-current {
    background-color: currentColor
}

.bg-danger-100 {
    background-color: var(--color-danger-100)
}

.bg-danger-600 {
    background-color: var(--color-danger-600)
}

.bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity))
}

.bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity))
}

.bg-gray-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251/var(--tw-bg-opacity))
}

.bg-gray-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39/var(--tw-bg-opacity))
}

.bg-green-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(240 253 244/var(--tw-bg-opacity))
}

.bg-inherit {
    background-color: inherit
}

.bg-neutral-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 245/var(--tw-bg-opacity))
}

.bg-oms-blue-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(14 128 143/var(--tw-bg-opacity))
}

.bg-oms-gray-10 {
    --tw-bg-opacity: 1;
    background-color: rgb(97 100 107/var(--tw-bg-opacity))
}

.bg-oms-gray-14 {
    --tw-bg-opacity: 1;
    background-color: rgb(217 217 217/var(--tw-bg-opacity))
}

.bg-oms-gray-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(205 205 205/var(--tw-bg-opacity))
}

.bg-oms-gray-3 {
    --tw-bg-opacity: 1;
    background-color: rgb(242 242 242/var(--tw-bg-opacity))
}

.bg-oms-gray-6 {
    --tw-bg-opacity: 1;
    background-color: rgb(216 216 218/var(--tw-bg-opacity))
}

.bg-oms-gray-8 {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 246/var(--tw-bg-opacity))
}

.bg-oms-gray-9 {
    --tw-bg-opacity: 1;
    background-color: rgb(235 236 236/var(--tw-bg-opacity))
}

.bg-oms-green {
    --tw-bg-opacity: 1;
    background-color: rgb(0 130 0/var(--tw-bg-opacity))
}

.bg-oms-green-11 {
    --tw-bg-opacity: 1;
    background-color: rgb(232 244 232/var(--tw-bg-opacity))
}

.bg-oms-green-13 {
    --tw-bg-opacity: 1;
    background-color: rgb(19 163 19/var(--tw-bg-opacity))
}

.bg-oms-green-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.bg-oms-green-6 {
    --tw-bg-opacity: 1;
    background-color: rgb(0 135 0/var(--tw-bg-opacity))
}

.bg-oms-green-8 {
    --tw-bg-opacity: 1;
    background-color: rgb(208 231 208/var(--tw-bg-opacity))
}

.bg-oms-green\/5 {
    background-color: rgba(0, 130, 0, .05)
}

.bg-oms-orange {
    --tw-bg-opacity: 1;
    background-color: rgb(255 184 0/var(--tw-bg-opacity))
}

.bg-oms-orange-10 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 121 0/var(--tw-bg-opacity))
}

.bg-oms-orange-11 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 39 0/var(--tw-bg-opacity))
}

.bg-oms-orange-4 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 234 178/var(--tw-bg-opacity))
}

.bg-oms-orange-5 {
    --tw-bg-opacity: 1;
    background-color: rgb(235 155 0/var(--tw-bg-opacity))
}

.bg-oms-orange-6 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 174 15/var(--tw-bg-opacity))
}

.bg-oms-orange-9 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 184 16/var(--tw-bg-opacity))
}

.bg-oms-purple-4 {
    --tw-bg-opacity: 1;
    background-color: rgb(215 206 251/var(--tw-bg-opacity))
}

.bg-oms-red {
    --tw-bg-opacity: 1;
    background-color: rgb(236 28 36/var(--tw-bg-opacity))
}

.bg-oms-red-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(214 101 110/var(--tw-bg-opacity))
}

.bg-oms-red-3 {
    --tw-bg-opacity: 1;
    background-color: rgb(179 49 49/var(--tw-bg-opacity))
}

.bg-oms-white {
    --tw-bg-opacity: 1;
    background-color: rgb(241 252 241/var(--tw-bg-opacity))
}

.bg-pink-400 {
    --tw-bg-opacity: 1;
    background-color: rgb(244 114 182/var(--tw-bg-opacity))
}

.bg-primary {
    background-color: var(--color-primary)
}

.bg-primary-light {
    background-color: var(--color-primary-light)
}

.bg-primary-light-100 {
    background-color: var(--color-primary-light-100)
}

.bg-red-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 242 242/var(--tw-bg-opacity))
}

.bg-secondary {
    background-color: var(--color-secondary)
}

.bg-secondary-100 {
    background-color: var(--color-secondary-100)
}

.bg-secondary-200 {
    background-color: var(--color-secondary-200)
}

.bg-secondary-400 {
    background-color: var(--color-secondary-400)
}

.bg-secondary-700-75 {
    background-color: var(--color-secondary-700-75)
}

.bg-secondary-900 {
    background-color: var(--color-secondary-900)
}

.bg-secondary-light {
    background-color: var(--color-secondary-light)
}

.bg-secondary-light-300 {
    background-color: var(--color-secondary-light-300)
}

.bg-secondary-light-400 {
    background-color: var(--color-secondary-light-400)
}

.bg-tertiary-300 {
    background-color: var(--color-tertiary-300)
}

.bg-transparent {
    background-color: transparent
}

.bg-violet-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(124 58 237/var(--tw-bg-opacity))
}

.bg-warning {
    background-color: var(--color-warning)
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.bg-white\/5 {
    background-color: hsla(0, 0%, 100%, .05)
}

.bg-yellow-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 249 195/var(--tw-bg-opacity))
}

.bg-yellow-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 252 232/var(--tw-bg-opacity))
}

.bg-yellow-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(234 179 8/var(--tw-bg-opacity))
}

.bg-yellow-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(133 77 14/var(--tw-bg-opacity))
}

.bg-opacity-25 {
    --tw-bg-opacity: 0.25
}

.bg-opacity-50 {
    --tw-bg-opacity: 0.5
}

.bg-opacity-60 {
    --tw-bg-opacity: 0.6
}

.bg-opacity-75 {
    --tw-bg-opacity: 0.75
}

.bg-\[url\(\'\/distributor\/bg-outlet-detail-requested\.jpg\'\)\] {
    background-image: url(/distributor/bg-outlet-detail-requested.jpg)
}

.bg-gradient-to-b {
    background-image: linear-gradient(to bottom, var(--tw-gradient-stops))
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops))
}

.bg-gradient-to-t {
    background-image: linear-gradient(to top, var(--tw-gradient-stops))
}

.from-\[\#008200\] {
    --tw-gradient-from: #008200 var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(0, 130, 0, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#44B238\] {
    --tw-gradient-from: #44b238 var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(68, 178, 56, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#E7FFE9\] {
    --tw-gradient-from: #e7ffe9 var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(231, 255, 233, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#F9FAFE\] {
    --tw-gradient-from: #f9fafe var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(249, 250, 254, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[rgba\(0\2c 130\2c 0\2c 0\.70\)\] {
    --tw-gradient-from: rgba(0, 130, 0, .7) var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(0, 130, 0, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-black {
    --tw-gradient-from: #000 var(--tw-gradient-from-position);
    --tw-gradient-to: transparent var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-transparent {
    --tw-gradient-from: transparent var(--tw-gradient-from-position);
    --tw-gradient-to: transparent var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.to-\[\#1C4A22\] {
    --tw-gradient-to: #1c4a22 var(--tw-gradient-to-position)
}

.to-\[\#58BF58\] {
    --tw-gradient-to: #58bf58 var(--tw-gradient-to-position)
}

.to-\[\#F3FFF4\] {
    --tw-gradient-to: #f3fff4 var(--tw-gradient-to-position)
}

.to-\[\#FAF6FF\] {
    --tw-gradient-to: #faf6ff var(--tw-gradient-to-position)
}

.to-gray-50 {
    --tw-gradient-to: #f9fafb var(--tw-gradient-to-position)
}

.to-oms-green {
    --tw-gradient-to: #008200 var(--tw-gradient-to-position)
}

.bg-cover {
    background-size: cover
}

.bg-top {
    background-position: top
}

.bg-no-repeat {
    background-repeat: no-repeat
}

.\!object-contain {
    -o-object-fit: contain !important;
    object-fit: contain !important
}

.object-contain {
    -o-object-fit: contain;
    object-fit: contain
}

.\!p-0 {
    padding: 0 !important
}

.\!p-4 {
    padding: 1rem !important
}

.\!p-\[0px\] {
    padding: 0 !important
}

.p-0 {
    padding: 0
}

.p-1 {
    padding: .25rem
}

.p-10 {
    padding: 2.5rem
}

.p-2 {
    padding: .5rem
}

.p-2\.5 {
    padding: .625rem
}

.p-3 {
    padding: .75rem
}

.p-4 {
    padding: 1rem
}

.p-5 {
    padding: 1.25rem
}

.p-6 {
    padding: 1.5rem
}

.p-7 {
    padding: 1.75rem
}

.p-8 {
    padding: 2rem
}

.p-9 {
    padding: 2.25rem
}

.p-\[10px\] {
    padding: 10px
}

.p-\[1rem\] {
    padding: 1rem
}

.p-\[24px\] {
    padding: 24px
}

.p-\[30px\] {
    padding: 30px
}

.\!px-0 {
    padding-left: 0 !important;
    padding-right: 0 !important
}

.\!px-2 {
    padding-left: .5rem !important;
    padding-right: .5rem !important
}

.\!px-\[0px\] {
    padding-left: 0 !important;
    padding-right: 0 !important
}

.\!py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important
}

.\!py-1\.5 {
    padding-top: .375rem !important;
    padding-bottom: .375rem !important
}

.\!py-2 {
    padding-top: .5rem !important;
    padding-bottom: .5rem !important
}

.px-0 {
    padding-left: 0;
    padding-right: 0
}

.px-1 {
    padding-left: .25rem;
    padding-right: .25rem
}

.px-1\.5 {
    padding-left: .375rem;
    padding-right: .375rem
}

.px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem
}

.px-12 {
    padding-left: 3rem;
    padding-right: 3rem
}

.px-2 {
    padding-left: .5rem;
    padding-right: .5rem
}

.px-2\.5 {
    padding-left: .625rem;
    padding-right: .625rem
}

.px-24 {
    padding-left: 6rem;
    padding-right: 6rem
}

.px-3 {
    padding-left: .75rem;
    padding-right: .75rem
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}

.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem
}

.px-7 {
    padding-left: 1.75rem;
    padding-right: 1.75rem
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem
}

.px-9 {
    padding-left: 2.25rem;
    padding-right: 2.25rem
}

.px-\[10px\] {
    padding-left: 10px;
    padding-right: 10px
}

.px-\[12px\] {
    padding-left: 12px;
    padding-right: 12px
}

.px-\[15px\] {
    padding-left: 15px;
    padding-right: 15px
}

.px-\[1px\] {
    padding-left: 1px;
    padding-right: 1px
}

.px-\[30px\] {
    padding-left: 30px;
    padding-right: 30px
}

.px-\[50px\] {
    padding-left: 50px;
    padding-right: 50px
}

.px-\[64px\] {
    padding-left: 64px;
    padding-right: 64px
}

.px-\[6px\] {
    padding-left: 6px;
    padding-right: 6px
}

.px-\[8px\] {
    padding-left: 8px;
    padding-right: 8px
}

.py-0\.5 {
    padding-top: .125rem;
    padding-bottom: .125rem
}

.py-1 {
    padding-top: .25rem;
    padding-bottom: .25rem
}

.py-1\.5 {
    padding-top: .375rem;
    padding-bottom: .375rem
}

.py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem
}

.py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem
}

.py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem
}

.py-2 {
    padding-top: .5rem;
    padding-bottom: .5rem
}

.py-2\.5 {
    padding-top: .625rem;
    padding-bottom: .625rem
}

.py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem
}

.py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem
}

.py-3 {
    padding-top: .75rem;
    padding-bottom: .75rem
}

.py-3\.5 {
    padding-top: .875rem;
    padding-bottom: .875rem
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem
}

.py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem
}

.py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem
}

.py-7 {
    padding-top: 1.75rem;
    padding-bottom: 1.75rem
}

.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem
}

.py-\[10px\] {
    padding-top: 10px;
    padding-bottom: 10px
}

.py-\[14px\] {
    padding-top: 14px;
    padding-bottom: 14px
}

.py-\[1px\] {
    padding-top: 1px;
    padding-bottom: 1px
}

.py-\[4px\] {
    padding-top: 4px;
    padding-bottom: 4px
}

.py-\[5\.5rem\] {
    padding-top: 5.5rem;
    padding-bottom: 5.5rem
}

.py-\[5px\] {
    padding-top: 5px;
    padding-bottom: 5px
}

.\!pb-5 {
    padding-bottom: 1.25rem !important
}

.\!pl-3 {
    padding-left: .75rem !important
}

.\!pl-4 {
    padding-left: 1rem !important
}

.\!pr-4 {
    padding-right: 1rem !important
}

.pb-0 {
    padding-bottom: 0
}

.pb-0\.5 {
    padding-bottom: .125rem
}

.pb-1 {
    padding-bottom: .25rem
}

.pb-1\.5 {
    padding-bottom: .375rem
}

.pb-10 {
    padding-bottom: 2.5rem
}

.pb-12 {
    padding-bottom: 3rem
}

.pb-14 {
    padding-bottom: 3.5rem
}

.pb-2 {
    padding-bottom: .5rem
}

.pb-2\.5 {
    padding-bottom: .625rem
}

.pb-20 {
    padding-bottom: 5rem
}

.pb-24 {
    padding-bottom: 6rem
}

.pb-28 {
    padding-bottom: 7rem
}

.pb-3 {
    padding-bottom: .75rem
}

.pb-4 {
    padding-bottom: 1rem
}

.pb-5 {
    padding-bottom: 1.25rem
}

.pb-6 {
    padding-bottom: 1.5rem
}

.pb-7 {
    padding-bottom: 1.75rem
}

.pb-8 {
    padding-bottom: 2rem
}

.pb-9 {
    padding-bottom: 2.25rem
}

.pb-\[10px\] {
    padding-bottom: 10px
}

.pb-\[15px\] {
    padding-bottom: 15px
}

.pb-\[32px\] {
    padding-bottom: 32px
}

.pb-\[4\.5rem\] {
    padding-bottom: 4.5rem
}

.pl-10 {
    padding-left: 2.5rem
}

.pl-11 {
    padding-left: 2.75rem
}

.pl-12 {
    padding-left: 3rem
}

.pl-2 {
    padding-left: .5rem
}

.pl-3 {
    padding-left: .75rem
}

.pl-4 {
    padding-left: 1rem
}

.pl-5 {
    padding-left: 1.25rem
}

.pl-6 {
    padding-left: 1.5rem
}

.pl-8 {
    padding-left: 2rem
}

.pl-9 {
    padding-left: 2.25rem
}

.pl-\[10px\] {
    padding-left: 10px
}

.pl-\[140px\] {
    padding-left: 140px
}

.pl-\[29px\] {
    padding-left: 29px
}

.pl-\[54px\] {
    padding-left: 54px
}

.pl-\[64px\] {
    padding-left: 64px
}

.pl-\[6px\] {
    padding-left: 6px
}

.pl-\[80px\] {
    padding-left: 80px
}

.pr-0\.5 {
    padding-right: .125rem
}

.pr-1 {
    padding-right: .25rem
}

.pr-10 {
    padding-right: 2.5rem
}

.pr-12 {
    padding-right: 3rem
}

.pr-2 {
    padding-right: .5rem
}

.pr-3 {
    padding-right: .75rem
}

.pr-4 {
    padding-right: 1rem
}

.pr-48 {
    padding-right: 12rem
}

.pr-5 {
    padding-right: 1.25rem
}

.pr-6 {
    padding-right: 1.5rem
}

.pr-8 {
    padding-right: 2rem
}

.pt-0 {
    padding-top: 0
}

.pt-1 {
    padding-top: .25rem
}

.pt-10 {
    padding-top: 2.5rem
}

.pt-12 {
    padding-top: 3rem
}

.pt-2 {
    padding-top: .5rem
}

.pt-2\.5 {
    padding-top: .625rem
}

.pt-24 {
    padding-top: 6rem
}

.pt-3 {
    padding-top: .75rem
}

.pt-4 {
    padding-top: 1rem
}

.pt-5 {
    padding-top: 1.25rem
}

.pt-6 {
    padding-top: 1.5rem
}

.pt-7 {
    padding-top: 1.75rem
}

.pt-8 {
    padding-top: 2rem
}

.pt-\[100\%\] {
    padding-top: 100%
}

.pt-\[38px\] {
    padding-top: 38px
}

.pt-\[45\%\] {
    padding-top: 45%
}

.pt-\[55\%\] {
    padding-top: 55%
}

.pt-\[81px\] {
    padding-top: 81px
}

.pt-\[82px\] {
    padding-top: 82px
}

.pt-\[85\%\] {
    padding-top: 85%
}

.text-left {
    text-align: left
}

.text-center {
    text-align: center
}

.\!text-right {
    text-align: right !important
}

.text-right {
    text-align: right
}

.-indent-\[9999px\] {
    text-indent: -9999px
}

.align-middle {
    vertical-align: middle
}

.font-inter {
    font-family: Inter, sans-serif
}

.\!text-\[11px\] {
    font-size: 11px !important
}

.\!text-\[13px\] {
    font-size: 13px !important
}

.\!text-base {
    font-size: 1rem !important;
    line-height: 1.5rem !important
}

.\!text-xs {
    font-size: .75rem !important;
    line-height: 1rem !important
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem
}

.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem
}

.text-5xl {
    font-size: 3rem;
    line-height: 1
}

.text-\[0\.7578rem\] {
    font-size: .7578rem
}

.text-\[1\.5rem\] {
    font-size: 1.5rem
}

.text-\[10px\] {
    font-size: 10px
}

.text-\[11px\] {
    font-size: 11px
}

.text-\[11px\]\/4 {
    font-size: 11px;
    line-height: 1rem
}

.text-\[13px\] {
    font-size: 13px
}

.text-\[13px\]\/4 {
    font-size: 13px;
    line-height: 1rem
}

.text-\[13px\]\/\[13px\] {
    font-size: 13px;
    line-height: 13px
}

.text-\[13px\]\/\[17px\] {
    font-size: 13px;
    line-height: 17px
}

.text-\[14px\] {
    font-size: 14px
}

.text-\[15px\] {
    font-size: 15px
}

.text-\[16px\] {
    font-size: 16px
}

.text-\[17px\] {
    font-size: 17px
}

.text-\[18px\] {
    font-size: 18px
}

.text-\[19px\] {
    font-size: 19px
}

.text-\[2\.5rem\] {
    font-size: 2.5rem
}

.text-\[20px\] {
    font-size: 20px
}

.text-\[21px\] {
    font-size: 21px
}

.text-\[22px\] {
    font-size: 22px
}

.text-\[2rem\] {
    font-size: 2rem
}

.text-\[30px\] {
    font-size: 30px
}

.text-\[32px\] {
    font-size: 32px
}

.text-\[40px\] {
    font-size: 40px
}

.text-\[66px\] {
    font-size: 66px
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem
}

.text-sm {
    font-size: .875rem;
    line-height: 1.25rem
}

.text-sm\/6 {
    font-size: .875rem;
    line-height: 1.5rem
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem
}

.text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.\!font-medium {
    font-weight: 500 !important
}

.font-bold {
    font-weight: 700
}

.font-extrabold {
    font-weight: 800
}

.font-light {
    font-weight: 300
}

.font-medium {
    font-weight: 500
}

.font-normal {
    font-weight: 400
}

.font-semibold {
    font-weight: 600
}

.font-thin {
    font-weight: 100
}

.uppercase {
    text-transform: uppercase
}

.capitalize {
    text-transform: capitalize
}

.normal-case {
    text-transform: none
}

.italic {
    font-style: italic
}

.not-italic {
    font-style: normal
}

.leading-10 {
    line-height: 2.5rem
}

.leading-5 {
    line-height: 1.25rem
}

.leading-6 {
    line-height: 1.5rem
}

.leading-8 {
    line-height: 2rem
}

.leading-\[1\.25rem\] {
    line-height: 1.25rem
}

.leading-\[2\.125rem\] {
    line-height: 2.125rem
}

.leading-\[20px\] {
    line-height: 20px
}

.leading-\[23\.4px\] {
    line-height: 23.4px
}

.leading-\[24px\] {
    line-height: 24px
}

.leading-\[2rem\] {
    line-height: 2rem
}

.leading-\[30px\] {
    line-height: 30px
}

.leading-\[31\.5px\] {
    line-height: 31.5px
}

.leading-\[3rem\] {
    line-height: 3rem
}

.leading-\[40px\] {
    line-height: 40px
}

.leading-\[44px\] {
    line-height: 44px
}

.leading-\[50px\] {
    line-height: 50px
}

.leading-\[52px\] {
    line-height: 52px
}

.leading-\[60px\] {
    line-height: 60px
}

.leading-\[80px\] {
    line-height: 80px
}

.leading-none {
    line-height: 1
}

.leading-normal {
    line-height: 1.5
}

.leading-tight {
    line-height: 1.25
}

.tracking-normal {
    letter-spacing: 0
}

.tracking-tight {
    letter-spacing: -.025em
}

.tracking-wide {
    letter-spacing: .025em
}

.tracking-widest {
    letter-spacing: .1em
}

.\!text-\[\#3A3D46\] {
    --tw-text-opacity: 1 !important;
    color: rgb(58 61 70/var(--tw-text-opacity)) !important
}

.\!text-\[\#6B7280\] {
    --tw-text-opacity: 1 !important;
    color: rgb(107 114 128/var(--tw-text-opacity)) !important
}

.\!text-oms-gray-10 {
    --tw-text-opacity: 1 !important;
    color: rgb(97 100 107/var(--tw-text-opacity)) !important
}

.\!text-oms-green-6 {
    --tw-text-opacity: 1 !important;
    color: rgb(0 135 0/var(--tw-text-opacity)) !important
}

.\!text-oms-green-7 {
    --tw-text-opacity: 1 !important;
    color: rgb(0 104 0/var(--tw-text-opacity)) !important
}

.\!text-primary {
    color: var(--color-primary) !important
}

.\!text-secondary {
    color: var(--color-secondary) !important
}

.\!text-tertiary {
    color: var(--color-tertiary) !important
}

.\!text-white {
    --tw-text-opacity: 1 !important;
    color: rgb(255 255 255/var(--tw-text-opacity)) !important
}

.text-\[\#006800\] {
    --tw-text-opacity: 1;
    color: rgb(0 104 0/var(--tw-text-opacity))
}

.text-\[\#111827\] {
    --tw-text-opacity: 1;
    color: rgb(17 24 39/var(--tw-text-opacity))
}

.text-\[\#1D429E\] {
    --tw-text-opacity: 1;
    color: rgb(29 66 158/var(--tw-text-opacity))
}

.text-\[\#2E7D32\] {
    --tw-text-opacity: 1;
    color: rgb(46 125 50/var(--tw-text-opacity))
}

.text-\[\#3156B3\] {
    --tw-text-opacity: 1;
    color: rgb(49 86 179/var(--tw-text-opacity))
}

.text-\[\#34B53A\] {
    --tw-text-opacity: 1;
    color: rgb(52 181 58/var(--tw-text-opacity))
}

.text-\[\#363636\] {
    --tw-text-opacity: 1;
    color: rgb(54 54 54/var(--tw-text-opacity))
}

.text-\[\#3A3D46\], .text-\[\#3a3d46\] {
    --tw-text-opacity: 1;
    color: rgb(58 61 70/var(--tw-text-opacity))
}

.text-\[\#4B5563\] {
    --tw-text-opacity: 1;
    color: rgb(75 85 99/var(--tw-text-opacity))
}

.text-\[\#4E5059\] {
    --tw-text-opacity: 1;
    color: rgb(78 80 89/var(--tw-text-opacity))
}

.text-\[\#5D5D5D\] {
    --tw-text-opacity: 1;
    color: rgb(93 93 93/var(--tw-text-opacity))
}

.text-\[\#61646B\] {
    --tw-text-opacity: 1;
    color: rgb(97 100 107/var(--tw-text-opacity))
}

.text-\[\#666666\] {
    --tw-text-opacity: 1;
    color: rgb(102 102 102/var(--tw-text-opacity))
}

.text-\[\#6B7280\] {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity))
}

.text-\[\#6E6E6E\] {
    --tw-text-opacity: 1;
    color: rgb(110 110 110/var(--tw-text-opacity))
}

.text-\[\#75777E\] {
    --tw-text-opacity: 1;
    color: rgb(117 119 126/var(--tw-text-opacity))
}

.text-\[\#767676\] {
    --tw-text-opacity: 1;
    color: rgb(118 118 118/var(--tw-text-opacity))
}

.text-\[\#79DDC4\] {
    --tw-text-opacity: 1;
    color: rgb(121 221 196/var(--tw-text-opacity))
}

.text-\[\#848283\] {
    --tw-text-opacity: 1;
    color: rgb(132 130 131/var(--tw-text-opacity))
}

.text-\[\#888888\] {
    --tw-text-opacity: 1;
    color: rgb(136 136 136/var(--tw-text-opacity))
}

.text-\[\#898888\] {
    --tw-text-opacity: 1;
    color: rgb(137 136 136/var(--tw-text-opacity))
}

.text-\[\#898B90\] {
    --tw-text-opacity: 1;
    color: rgb(137 139 144/var(--tw-text-opacity))
}

.text-\[\#8F02A8\] {
    --tw-text-opacity: 1;
    color: rgb(143 2 168/var(--tw-text-opacity))
}

.text-\[\#989898\] {
    --tw-text-opacity: 1;
    color: rgb(152 152 152/var(--tw-text-opacity))
}

.text-\[\#9AA2A9\] {
    --tw-text-opacity: 1;
    color: rgb(154 162 169/var(--tw-text-opacity))
}

.text-\[\#9CA3AF\] {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity))
}

.text-\[\#9FA3AC\] {
    --tw-text-opacity: 1;
    color: rgb(159 163 172/var(--tw-text-opacity))
}

.text-\[\#B0B1B5\] {
    --tw-text-opacity: 1;
    color: rgb(176 177 181/var(--tw-text-opacity))
}

.text-\[\#B5B5B5\] {
    --tw-text-opacity: 1;
    color: rgb(181 181 181/var(--tw-text-opacity))
}

.text-\[\#CC6100\] {
    --tw-text-opacity: 1;
    color: rgb(204 97 0/var(--tw-text-opacity))
}

.text-\[\#CC7A00\] {
    --tw-text-opacity: 1;
    color: rgb(204 122 0/var(--tw-text-opacity))
}

.text-\[\#E52700\] {
    --tw-text-opacity: 1;
    color: rgb(229 39 0/var(--tw-text-opacity))
}

.text-\[\#E82627\] {
    --tw-text-opacity: 1;
    color: rgb(232 38 39/var(--tw-text-opacity))
}

.text-\[\#EC1C24\] {
    --tw-text-opacity: 1;
    color: rgb(236 28 36/var(--tw-text-opacity))
}

.text-\[\#FDB92C\] {
    --tw-text-opacity: 1;
    color: rgb(253 185 44/var(--tw-text-opacity))
}

.text-\[rgba\(0\2c 0\2c 0\2c 0\.38\)\] {
    color: rgba(0, 0, 0, .38)
}

.text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity))
}

.text-black\/40 {
    color: rgba(0, 0, 0, .4)
}

.text-black\/60 {
    color: rgba(0, 0, 0, .6)
}

.text-blue-300 {
    --tw-text-opacity: 1;
    color: rgb(147 197 253/var(--tw-text-opacity))
}

.text-danger {
    color: var(--color-danger)
}

.text-danger-500 {
    color: var(--color-danger-500)
}

.text-danger-600 {
    color: var(--color-danger-600)
}

.text-danger-700 {
    color: var(--color-danger-700)
}

.text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity))
}

.text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity))
}

.text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99/var(--tw-text-opacity))
}

.text-gray-700 {
    --tw-text-opacity: 1;
    color: rgb(55 65 81/var(--tw-text-opacity))
}

.text-gray-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39/var(--tw-text-opacity))
}

.text-green-400 {
    --tw-text-opacity: 1;
    color: rgb(74 222 128/var(--tw-text-opacity))
}

.text-green-700 {
    --tw-text-opacity: 1;
    color: rgb(21 128 61/var(--tw-text-opacity))
}

.text-info {
    color: var(--color-info)
}

.text-oms-black-1 {
    --tw-text-opacity: 1;
    color: rgb(54 54 54/var(--tw-text-opacity))
}

.text-oms-blue-2 {
    --tw-text-opacity: 1;
    color: rgb(14 128 143/var(--tw-text-opacity))
}

.text-oms-blue-3 {
    --tw-text-opacity: 1;
    color: rgb(42 110 187/var(--tw-text-opacity))
}

.text-oms-gray {
    --tw-text-opacity: 1;
    color: rgb(84 84 84/var(--tw-text-opacity))
}

.text-oms-gray-10 {
    --tw-text-opacity: 1;
    color: rgb(97 100 107/var(--tw-text-opacity))
}

.text-oms-gray-12 {
    --tw-text-opacity: 1;
    color: rgb(78 80 89/var(--tw-text-opacity))
}

.text-oms-gray-13 {
    --tw-text-opacity: 1;
    color: rgb(158 158 158/var(--tw-text-opacity))
}

.text-oms-gray-4 {
    --tw-text-opacity: 1;
    color: rgb(58 61 70/var(--tw-text-opacity))
}

.text-oms-gray-5 {
    --tw-text-opacity: 1;
    color: rgb(117 119 126/var(--tw-text-opacity))
}

.text-oms-green {
    --tw-text-opacity: 1;
    color: rgb(0 130 0/var(--tw-text-opacity))
}

.text-oms-green-12 {
    --tw-text-opacity: 1;
    color: rgb(109 164 0/var(--tw-text-opacity))
}

.text-oms-green-5 {
    --tw-text-opacity: 1;
    color: rgb(1 152 88/var(--tw-text-opacity))
}

.text-oms-green-6 {
    --tw-text-opacity: 1;
    color: rgb(0 135 0/var(--tw-text-opacity))
}

.text-oms-green-7 {
    --tw-text-opacity: 1;
    color: rgb(0 104 0/var(--tw-text-opacity))
}

.text-oms-orange {
    --tw-text-opacity: 1;
    color: rgb(255 184 0/var(--tw-text-opacity))
}

.text-oms-orange-11 {
    --tw-text-opacity: 1;
    color: rgb(229 39 0/var(--tw-text-opacity))
}

.text-oms-orange-4 {
    --tw-text-opacity: 1;
    color: rgb(255 234 178/var(--tw-text-opacity))
}

.text-oms-orange-5 {
    --tw-text-opacity: 1;
    color: rgb(235 155 0/var(--tw-text-opacity))
}

.text-oms-purple {
    --tw-text-opacity: 1;
    color: rgb(132 105 242/var(--tw-text-opacity))
}

.text-oms-red {
    --tw-text-opacity: 1;
    color: rgb(236 28 36/var(--tw-text-opacity))
}

.text-oms-red-2 {
    --tw-text-opacity: 1;
    color: rgb(214 101 110/var(--tw-text-opacity))
}

.text-oms-red-3 {
    --tw-text-opacity: 1;
    color: rgb(179 49 49/var(--tw-text-opacity))
}

.text-pink-400 {
    --tw-text-opacity: 1;
    color: rgb(244 114 182/var(--tw-text-opacity))
}

.text-primary {
    color: var(--color-primary)
}

.text-primary-light {
    color: var(--color-primary-light)
}

.text-red-400 {
    --tw-text-opacity: 1;
    color: rgb(248 113 113/var(--tw-text-opacity))
}

.text-red-500 {
    --tw-text-opacity: 1;
    color: rgb(239 68 68/var(--tw-text-opacity))
}

.text-red-700 {
    --tw-text-opacity: 1;
    color: rgb(185 28 28/var(--tw-text-opacity))
}

.text-rose-500 {
    --tw-text-opacity: 1;
    color: rgb(244 63 94/var(--tw-text-opacity))
}

.text-rose-600 {
    --tw-text-opacity: 1;
    color: rgb(225 29 72/var(--tw-text-opacity))
}

.text-secondary {
    color: var(--color-secondary)
}

.text-secondary-400 {
    color: var(--color-secondary-400)
}

.text-secondary-500 {
    color: var(--color-secondary-500)
}

.text-secondary-700 {
    color: var(--color-secondary-700)
}

.text-secondary-900 {
    color: var(--color-secondary-900)
}

.text-tertiary {
    color: var(--color-tertiary)
}

.text-tertiary-300 {
    color: var(--color-tertiary-300)
}

.text-violet-600 {
    --tw-text-opacity: 1;
    color: rgb(124 58 237/var(--tw-text-opacity))
}

.text-warning {
    color: var(--color-warning)
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.text-yellow-400 {
    --tw-text-opacity: 1;
    color: rgb(250 204 21/var(--tw-text-opacity))
}

.text-yellow-500 {
    --tw-text-opacity: 1;
    color: rgb(234 179 8/var(--tw-text-opacity))
}

.text-yellow-700 {
    --tw-text-opacity: 1;
    color: rgb(161 98 7/var(--tw-text-opacity))
}

.text-yellow-800 {
    --tw-text-opacity: 1;
    color: rgb(133 77 14/var(--tw-text-opacity))
}

.text-opacity-60 {
    --tw-text-opacity: 0.6
}

.text-opacity-\[\.87\] {
    --tw-text-opacity: .87
}

.underline {
    text-decoration-line: underline
}

.line-through {
    text-decoration-line: line-through
}

.decoration-oms-green {
    text-decoration-color: #008200
}

.decoration-\[3px\] {
    text-decoration-thickness: 3px
}

.placeholder-\[\#848283\]::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(132 130 131/var(--tw-placeholder-opacity))
}

.placeholder-\[\#848283\]::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(132 130 131/var(--tw-placeholder-opacity))
}

.placeholder-\[\#898B90\]::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(137 139 144/var(--tw-placeholder-opacity))
}

.placeholder-\[\#898B90\]::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(137 139 144/var(--tw-placeholder-opacity))
}

.placeholder-oms-gray-7::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(137 139 144/var(--tw-placeholder-opacity))
}

.placeholder-oms-gray-7::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(137 139 144/var(--tw-placeholder-opacity))
}

.accent-amber-200 {
    accent-color: #fde68a
}

.\!opacity-0 {
    opacity: 0 !important
}

.\!opacity-100 {
    opacity: 1 !important
}

.opacity-0 {
    opacity: 0
}

.opacity-100 {
    opacity: 1
}

.opacity-20 {
    opacity: .2
}

.opacity-25 {
    opacity: .25
}

.opacity-30 {
    opacity: .3
}

.opacity-40 {
    opacity: .4
}

.opacity-50 {
    opacity: .5
}

.opacity-60 {
    opacity: .6
}

.opacity-75 {
    opacity: .75
}

.opacity-80 {
    opacity: .8
}

.\!shadow-\[inset_0px_-2px_0px_\#E51A7F\] {
    --tw-shadow: inset 0px -2px 0px #e51a7f !important;
    --tw-shadow-colored: inset 0px -2px 0px var(--tw-shadow-color) !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important
}

.shadow {
    --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color)
}

.shadow, .shadow-2xl {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-2xl {
    --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color)
}

.shadow-\[0_-35px_40px_-50px_rgba\(0\2c 0\2c 0\2c 0\.3\)\] {
    --tw-shadow: 0 -35px 40px -50px rgba(0, 0, 0, .3);
    --tw-shadow-colored: 0 -35px 40px -50px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0_0px_20px_0px_rgba\(0\2c 0\2c 0\2c 0\.15\)\] {
    --tw-shadow: 0 0px 20px 0px rgba(0, 0, 0, .15);
    --tw-shadow-colored: 0 0px 20px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0_10px_0_0_\#008943\] {
    --tw-shadow: 0 10px 0 0 #008943;
    --tw-shadow-colored: 0 10px 0 0 var(--tw-shadow-color)
}

.shadow-\[0_10px_0_0_\#008943\], .shadow-\[0_10px_0_0_\#E2E2E2\] {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0_10px_0_0_\#E2E2E2\] {
    --tw-shadow: 0 10px 0 0 #e2e2e2;
    --tw-shadow-colored: 0 10px 0 0 var(--tw-shadow-color)
}

.shadow-\[0_16px_40px_0_\#0E308712\] {
    --tw-shadow: 0 16px 40px 0 #0e308712;
    --tw-shadow-colored: 0 16px 40px 0 var(--tw-shadow-color)
}

.shadow-\[0_16px_40px_0_\#0E308712\], .shadow-\[0_16px_40px_0px_rgba\(14\2c 48\2c 135\2c 0\.07\)\] {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0_16px_40px_0px_rgba\(14\2c 48\2c 135\2c 0\.07\)\] {
    --tw-shadow: 0 16px 40px 0px rgba(14, 48, 135, .07);
    --tw-shadow-colored: 0 16px 40px 0px var(--tw-shadow-color)
}

.shadow-\[0_1px_14px_0px_\#00000015\] {
    --tw-shadow: 0 1px 14px 0px #00000015;
    --tw-shadow-colored: 0 1px 14px 0px var(--tw-shadow-color)
}

.shadow-\[0_1px_14px_0px_\#00000015\], .shadow-\[0_6px_24px_0_rgba\(14\2c 48\2c 135\2c 0\.07\)\] {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0_6px_24px_0_rgba\(14\2c 48\2c 135\2c 0\.07\)\] {
    --tw-shadow: 0 6px 24px 0 rgba(14, 48, 135, .07);
    --tw-shadow-colored: 0 6px 24px 0 var(--tw-shadow-color)
}

.shadow-\[0px_2px_16px_0px_\#32323224\] {
    --tw-shadow: 0px 2px 16px 0px #32323224;
    --tw-shadow-colored: 0px 2px 16px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0px_6px_24px_0px_rgba\(14\2c 48\2c 135\2c 0\.07\)\] {
    --tw-shadow: 0px 6px 24px 0px rgba(14, 48, 135, .07);
    --tw-shadow-colored: 0px 6px 24px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[2px_0px_20px_-12px_rgba\(0\2c 0\2c 0\2c 0\.4\)\] {
    --tw-shadow: 2px 0px 20px -12px rgba(0, 0, 0, .4);
    --tw-shadow-colored: 2px 0px 20px -12px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[inset_0px_-1px_0px_rgba\(255\2c 255\2c 255\2c 0\.25\)\] {
    --tw-shadow: inset 0px -1px 0px hsla(0, 0%, 100%, .25);
    --tw-shadow-colored: inset 0px -1px 0px var(--tw-shadow-color)
}

.shadow-\[inset_0px_-1px_0px_rgba\(255\2c 255\2c 255\2c 0\.25\)\], .shadow-bold {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-bold {
    --tw-shadow: 0px 4px 4px 0px rgba(0, 0, 0, .25);
    --tw-shadow-colored: 0px 4px 4px 0px var(--tw-shadow-color)
}

.shadow-card {
    --tw-shadow: 0px 4px 10px 0px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0px 4px 10px 0px var(--tw-shadow-color)
}

.shadow-card, .shadow-inner {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, .05);
    --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color)
}

.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color)
}

.shadow-lg, .shadow-md {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-md {
    --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color)
}

.shadow-navbar {
    --tw-shadow: 0px 0px 17px 0px rgba(0, 0, 0, .15);
    --tw-shadow-colored: 0px 0px 17px 0px var(--tw-shadow-color)
}

.shadow-navbar, .shadow-none {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-none {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000
}

.shadow-promotion {
    --tw-shadow: 0px 0px 20px 0px rgba(0, 0, 0, .15);
    --tw-shadow-colored: 0px 0px 20px 0px var(--tw-shadow-color)
}

.shadow-promotion, .shadow-section {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-section {
    --tw-shadow: 0px 6px 20px 0px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0px 6px 20px 0px var(--tw-shadow-color)
}

.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color)
}

.shadow-sm, .shadow-tab {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-tab {
    --tw-shadow: 0px 0px 16px 0px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0px 0px 16px 0px var(--tw-shadow-color)
}

.shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-black {
    --tw-shadow-color: #000;
    --tw-shadow: var(--tw-shadow-colored)
}

.shadow-gray-300\/50 {
    --tw-shadow-color: rgba(209, 213, 219, .5);
    --tw-shadow: var(--tw-shadow-colored)
}

.\!outline-none {
    outline: 2px solid transparent !important;
    outline-offset: 2px !important
}

.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.outline {
    outline-style: solid
}

.ring-1 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.ring-1, .ring-2 {
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.ring-2 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.ring-inset {
    --tw-ring-inset: inset
}

.ring-\[\#E6E6E6\] {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(230 230 230/var(--tw-ring-opacity))
}

.ring-black {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 0 0/var(--tw-ring-opacity))
}

.ring-black\/5 {
    --tw-ring-color: rgba(0, 0, 0, .05)
}

.ring-gray-300 {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(209 213 219/var(--tw-ring-opacity))
}

.ring-oms-gray-2 {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(205 205 205/var(--tw-ring-opacity))
}

.ring-oms-green {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 130 0/var(--tw-ring-opacity))
}

.ring-oms-red {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(236 28 36/var(--tw-ring-opacity))
}

.ring-opacity-5 {
    --tw-ring-opacity: 0.05
}

.blur {
    --tw-blur: blur(8px)
}

.blur, .brightness-50 {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.brightness-50 {
    --tw-brightness: brightness(.5)
}

.contrast-75 {
    --tw-contrast: contrast(.75)
}

.contrast-75, .drop-shadow-md {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.drop-shadow-md {
    --tw-drop-shadow: drop-shadow(0 4px 3px rgba(0, 0, 0, .07)) drop-shadow(0 2px 2px rgba(0, 0, 0, .06))
}

.drop-shadow-xl {
    --tw-drop-shadow: drop-shadow(0 20px 13px rgba(0, 0, 0, .03)) drop-shadow(0 8px 5px rgba(0, 0, 0, .08))
}

.drop-shadow-xl, .grayscale {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.grayscale {
    --tw-grayscale: grayscale(100%)
}

.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.backdrop-blur-2xl {
    --tw-backdrop-blur: blur(40px)
}

.backdrop-blur-2xl, .backdrop-blur-sm {
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)
}

.backdrop-blur-sm {
    --tw-backdrop-blur: blur(4px)
}

.transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-\[max-height\2c opacity\] {
    transition-property: max-height, opacity;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.duration-100 {
    transition-duration: .1s
}

.duration-150 {
    transition-duration: .15s
}

.duration-200 {
    transition-duration: .2s
}

.duration-300 {
    transition-duration: .3s
}

.duration-500 {
    transition-duration: .5s
}

.duration-700 {
    transition-duration: .7s
}

.duration-75 {
    transition-duration: 75ms
}

.ease-in {
    transition-timing-function: cubic-bezier(.4, 0, 1, 1)
}

.ease-in-out {
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.ease-out {
    transition-timing-function: cubic-bezier(0, 0, .2, 1)
}

.no-scrollbar::-webkit-scrollbar {
    display: none
}

.no-scrollbar::-webkit-scrollbar-track {
    background-color: transparent
}

.no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none
}

input:not(:-moz-placeholder-shown) ~ label {
    font-size: 10px;
    font-weight: 400;
    line-height: 1rem;
    --tw-translate-y: -0.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

input:focus ~ label, input:not(:placeholder-shown) ~ label {
    font-size: 10px;
    font-weight: 400;
    line-height: 1rem;
    --tw-translate-y: -0.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

input:focus ~ label {
    left: 0
}

input:-webkit-autofill, input:-webkit-autofill:focus, input:-webkit-autofill:hover, select:-webkit-autofill, select:-webkit-autofill:focus, select:-webkit-autofill:hover, textarea:-webkit-autofill, textarea:-webkit-autofill:focus, textarea:-webkit-autofill:hover {
    border: 1px solid #212121;
    -webkit-text-fill-color: #212121;
    -webkit-box-shadow: inset 0 0 0 1000px #fff;
    -webkit-transition: background-color 5000s ease-in-out 0s;
    transition: background-color 5000s ease-in-out 0s
}

input[type=search]::-webkit-search-cancel-button, input[type=search]::-webkit-search-decoration, input[type=search]::-webkit-search-results-button, input[type=search]::-webkit-search-results-decoration {
    -webkit-appearance: none
}

.\[--anchor-gap\:var\(--spacing-5\)\] {
    --anchor-gap: var(--spacing-5)
}

@font-face {
    font-family: Heineken;
    src: url(/_next/static/media/heineken-webfont.01474fca.woff2) format("woff2"), url(/_next/static/media/heineken-webfont.d98c6ca2.woff) format("woff");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-Light.0b4ff3e0.ttf) format("truetype");
    font-weight: 300;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-Regular.1282ebc6.ttf) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-Medium.8d213a54.ttf) format("truetype");
    font-weight: 500;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-SemiBold.524c2f2b.ttf) format("truetype");
    font-weight: 600;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-Bold.c146dcab.ttf) format("truetype");
    font-weight: 700;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-ExtraBold.6e29c005.ttf) format("truetype");
    font-weight: 800;
    font-style: normal
}

button.active-filter {
    background-color: #111316 !important;
    color: #fff !important
}

.plp-button button {
    width: 100%;
    padding-left: 0;
    padding-right: 0
}

.plp-button > div {
    display: flex;
    justify-content: center
}

@media (min-width: 768px) {
    .plp-button button {
        width: 151px
    }

    .pdp-button, .pdp-button button {
        width: 100% !important
    }
}

.eventRegistrationPage main, .oms-redemptionPage main, .subscriptionPage main {
    display: flex;
    flex-direction: column
}

.beerCiderPage .storyblockTeaser, .eventRegistrationPage .storyblockTeaser, .oms-redemptionPage .storyblockTeaser, .oms-redemptionTermsAndConditionsPage .storyblockTeaser, .subscriptionPage .storyblockTeaser {
    padding-top: 40px
}

.homepage .storyblockTeaser {
    max-width: 1280px;
    margin: 0 auto
}

.oms-redemptionPage .storyblockTeaser {
    max-width: 992px;
    margin: 0 auto
}

.eventRegistrationPage .storyblockTeaser, .oms-redemptionTermsAndConditionsPage .storyblockTeaser {
    max-width: 800px;
    margin: 0 auto
}

.storyblockBanner {
    order: -1
}

.banner-img, .storyblockBanner span {
    height: auto !important;
    position: static !important
}

.banner-img {
    width: 100% !important
}

.eventRegistrationPage form > div:first-child {
    width: calc(50% - 8px);
    float: left
}

.eventRegistrationPage form > div:first-child + div {
    width: calc(50% - 8px);
    margin-left: 16px;
    float: left
}

.eventRegistrationPage form > div:nth-child(3n) {
    clear: both
}

@media (min-width: 768px) {
    .eventRegistrationPage .storyblockTeaser, .oms-redemptionPage .storyblockTeaser {
        padding-top: 60px
    }
}

.account-dropdown {
    filter: drop-shadow(0 2px 16px rgba(50, 50, 50, .12));
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    display: block !important
}

.account-dropdown:before {
    content: "";
    position: absolute;
    top: -15px;
    right: 62px;
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 16px solid #fff
}

.account-dropdown ul {
    padding: 8px 0
}

.account-dropdown ul li {
    padding: 12px 0
}

.account-dropdown ul li a {
    font-size: 16px;
    display: flex;
    justify-content: space-between
}

.account-dropdown ul li a .dropdown-icon, .account-dropdown ul li button .dropdown-icon {
    width: calc(100% - 186px)
}

.account-dropdown ul li a .dropdown-label, .account-dropdown ul li button .dropdown-label {
    width: 186px;
    text-align: left
}

@media (min-width: 1024px) {
    .account-dropdown:before {
        right: 11px
    }
}

.without-icon-close + .absolute.right-4.top-4 {
    display: none
}

.tooltip {
    z-index: 1
}

.tooltip:after {
    content: "";
    position: absolute;
    bottom: -9px;
    left: calc(50% - 9px);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 10px solid #fff
}

@media (max-width: 600px) {
    .tooltip.arrow-left-mobile:after {
        left: 7px
    }
}

.react-datepicker-popper[data-placement^=bottom] {
    padding-top: 6px !important
}

.placeholder\:font-medium::-moz-placeholder {
    font-weight: 500
}

.placeholder\:font-medium::placeholder {
    font-weight: 500
}

.placeholder\:font-normal::-moz-placeholder {
    font-weight: 400
}

.placeholder\:font-normal::placeholder {
    font-weight: 400
}

.placeholder\:text-secondary::-moz-placeholder {
    color: var(--color-secondary)
}

.placeholder\:text-secondary::placeholder {
    color: var(--color-secondary)
}

.before\:absolute:before {
    content: var(--tw-content);
    position: absolute
}

.before\:right-0:before {
    content: var(--tw-content);
    right: 0
}

.before\:top-\[-20px\]:before {
    content: var(--tw-content);
    top: -20px
}

.before\:h-10:before {
    content: var(--tw-content);
    height: 2.5rem
}

.before\:w-\[90px\]:before {
    content: var(--tw-content);
    width: 90px
}

.before\:bg-white:before {
    content: var(--tw-content);
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.after\:absolute:after {
    content: var(--tw-content);
    position: absolute
}

.after\:left-1\/2:after, .after\:left-2\/4:after {
    content: var(--tw-content);
    left: 50%
}

.after\:top-\[70\%\]:after {
    content: var(--tw-content);
    top: 70%
}

.after\:top-full:after {
    content: var(--tw-content);
    top: 100%
}

.after\:mx-1:after {
    content: var(--tw-content);
    margin-left: .25rem;
    margin-right: .25rem
}

.after\:ml-\[-22px\]:after {
    content: var(--tw-content);
    margin-left: -22px
}

.after\:ml-\[-45px\]:after {
    content: var(--tw-content);
    margin-left: -45px
}

.after\:block:after {
    content: var(--tw-content);
    display: block
}

.after\:h-0\.5:after {
    content: var(--tw-content);
    height: .125rem
}

.after\:h-\[33px\]:after {
    content: var(--tw-content);
    height: 33px
}

.after\:w-11:after {
    content: var(--tw-content);
    width: 2.75rem
}

.after\:w-\[105px\]:after {
    content: var(--tw-content);
    width: 105px
}

.after\:\!bg-oms-green-7:after {
    content: var(--tw-content);
    --tw-bg-opacity: 1 !important;
    background-color: rgb(0 104 0/var(--tw-bg-opacity)) !important
}

.after\:bg-transparent:after {
    content: var(--tw-content);
    background-color: transparent
}

.after\:bg-\[url\(\'\/price\.svg\'\)\]:after {
    content: var(--tw-content);
    background-image: url(/price.svg)
}

.after\:bg-no-repeat:after {
    content: var(--tw-content);
    background-repeat: no-repeat
}

.after\:content-\[\'\'\]:after {
    --tw-content: "";
    content: var(--tw-content)
}

.after\:content-\[\'\/\'\]:after {
    --tw-content: "/";
    content: var(--tw-content)
}

.first\:border-l-2:first-child {
    border-left-width: 2px
}

.first\:border-l-oms-gray-10:first-child {
    --tw-border-opacity: 1;
    border-left-color: rgb(97 100 107/var(--tw-border-opacity))
}

.first\:border-l-oms-green-6:first-child {
    --tw-border-opacity: 1;
    border-left-color: rgb(0 135 0/var(--tw-border-opacity))
}

.first\:border-l-oms-orange-10:first-child {
    --tw-border-opacity: 1;
    border-left-color: rgb(255 121 0/var(--tw-border-opacity))
}

.first\:border-l-oms-orange-9:first-child {
    --tw-border-opacity: 1;
    border-left-color: rgb(255 184 16/var(--tw-border-opacity))
}

.first\:pl-2:first-child {
    padding-left: .5rem
}

.first\:pt-0:first-child {
    padding-top: 0
}

.last\:mb-4:last-child {
    margin-bottom: 1rem
}

.last\:border-b-0:last-child {
    border-bottom-width: 0
}

.last\:border-none:last-child {
    border-style: none
}

.last\:pb-0:last-child {
    padding-bottom: 0
}

.valid\:mt-3:valid {
    margin-top: .75rem
}

.valid\:mt-\[17px\]:valid {
    margin-top: 17px
}

.valid\:h-6:valid {
    height: 1.5rem
}

.focus-within\:mt-3:focus-within {
    margin-top: .75rem
}

.focus-within\:mt-\[17px\]:focus-within {
    margin-top: 17px
}

.focus-within\:h-6:focus-within {
    height: 1.5rem
}

.focus-within\:text-secondary-400:focus-within {
    color: var(--color-secondary-400)
}

.focus-within\:shadow-lg:focus-within {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.focus-within\:\!ring-0:focus-within {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important
}

.focus-within\:ring-2:focus-within {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-within\:ring-oms-green:focus-within {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 130 0/var(--tw-ring-opacity))
}

.hover\:text-clip:hover {
    text-overflow: clip
}

.hover\:rounded-full:hover {
    border-radius: 9999px
}

.hover\:border-none:hover {
    border-style: none
}

.hover\:border-oms-gray-2:hover {
    --tw-border-opacity: 1;
    border-color: rgb(205 205 205/var(--tw-border-opacity))
}

.hover\:border-oms-green-3:hover {
    --tw-border-opacity: 1;
    border-color: rgb(104 215 139/var(--tw-border-opacity))
}

.hover\:border-oms-green-7:hover {
    --tw-border-opacity: 1;
    border-color: rgb(0 104 0/var(--tw-border-opacity))
}

.hover\:border-oms-orange-3:hover {
    --tw-border-opacity: 1;
    border-color: rgb(211 185 115/var(--tw-border-opacity))
}

.hover\:border-oms-purple-3:hover {
    --tw-border-opacity: 1;
    border-color: rgb(145 128 216/var(--tw-border-opacity))
}

.hover\:\!bg-primary-light:hover {
    background-color: var(--color-primary-light) !important
}

.hover\:\!bg-secondary-100:hover {
    background-color: var(--color-secondary-100) !important
}

.hover\:\!bg-transparent:hover {
    background-color: transparent !important
}

.hover\:bg-\[\#3b5998\]\/90:hover {
    background-color: rgba(59, 89, 152, .9)
}

.hover\:bg-\[\#F1FCF1\]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(241 252 241/var(--tw-bg-opacity))
}

.hover\:bg-\[\#f9f9f9\]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(249 249 249/var(--tw-bg-opacity))
}

.hover\:bg-black:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity))
}

.hover\:bg-gray-200:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity))
}

.hover\:bg-gray-50:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251/var(--tw-bg-opacity))
}

.hover\:bg-neutral-100:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 245/var(--tw-bg-opacity))
}

.hover\:bg-oms-gray-5:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(117 119 126/var(--tw-bg-opacity))
}

.hover\:bg-oms-green:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(0 130 0/var(--tw-bg-opacity))
}

.hover\:bg-oms-green-11:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(232 244 232/var(--tw-bg-opacity))
}

.hover\:bg-oms-green-4:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(236 255 242/var(--tw-bg-opacity))
}

.hover\:bg-oms-orange-4:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 234 178/var(--tw-bg-opacity))
}

.hover\:bg-oms-purple-4:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(215 206 251/var(--tw-bg-opacity))
}

.hover\:bg-oms-white:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(241 252 241/var(--tw-bg-opacity))
}

.hover\:bg-primary:hover {
    background-color: var(--color-primary)
}

.hover\:bg-secondary-100:hover {
    background-color: var(--color-secondary-100)
}

.hover\:bg-secondary-200:hover {
    background-color: var(--color-secondary-200)
}

.hover\:bg-secondary-400:hover {
    background-color: var(--color-secondary-400)
}

.hover\:bg-white:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.hover\:bg-opacity-10:hover {
    --tw-bg-opacity: 0.1
}

.hover\:from-\[\#E7FFE9\]:hover {
    --tw-gradient-from: #e7ffe9 var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(231, 255, 233, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.hover\:to-\[\#F3FFF4\]:hover {
    --tw-gradient-to: #f3fff4 var(--tw-gradient-to-position)
}

.hover\:pl-4:hover {
    padding-left: 1rem
}

.hover\:pr-1:hover {
    padding-right: .25rem
}

.hover\:font-bold:hover {
    font-weight: 700
}

.hover\:text-black:hover {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity))
}

.hover\:text-gray-500:hover {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity))
}

.hover\:text-gray-600:hover {
    --tw-text-opacity: 1;
    color: rgb(75 85 99/var(--tw-text-opacity))
}

.hover\:text-oms-green:hover {
    --tw-text-opacity: 1;
    color: rgb(0 130 0/var(--tw-text-opacity))
}

.hover\:text-oms-green-6:hover {
    --tw-text-opacity: 1;
    color: rgb(0 135 0/var(--tw-text-opacity))
}

.hover\:text-oms-green-7:hover {
    --tw-text-opacity: 1;
    color: rgb(0 104 0/var(--tw-text-opacity))
}

.hover\:text-primary:hover {
    color: var(--color-primary)
}

.hover\:text-secondary-900:hover {
    color: var(--color-secondary-900)
}

.hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.hover\:underline:hover {
    text-decoration-line: underline
}

.hover\:opacity-100:hover {
    opacity: 1
}

.hover\:opacity-70:hover {
    opacity: .7
}

.hover\:opacity-80:hover {
    opacity: .8
}

.hover\:after\:bg-oms-green-7:hover:after {
    content: var(--tw-content);
    --tw-bg-opacity: 1;
    background-color: rgb(0 104 0/var(--tw-bg-opacity))
}

.focus\:border-blue-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246/var(--tw-border-opacity))
}

.focus\:border-gray-400:focus {
    --tw-border-opacity: 1;
    border-color: rgb(156 163 175/var(--tw-border-opacity))
}

.focus\:border-rose-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(244 63 94/var(--tw-border-opacity))
}

.focus\:border-secondary-400:focus {
    border-color: var(--color-secondary-400)
}

.focus\:border-secondary-700:focus {
    border-color: var(--color-secondary-700)
}

.focus\:text-gray-700:focus {
    --tw-text-opacity: 1;
    color: rgb(55 65 81/var(--tw-text-opacity))
}

.focus\:text-secondary-700:focus {
    color: var(--color-secondary-700)
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus\:outline:focus {
    outline-style: solid
}

.focus\:outline-1:focus {
    outline-width: 1px
}

.focus\:outline-oms-green:focus {
    outline-color: #008200
}

.focus\:ring-0:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.focus\:ring-0:focus, .focus\:ring-2:focus {
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.focus\:ring-4:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-\[\#3b5998\]\/50:focus {
    --tw-ring-color: rgba(59, 89, 152, .5)
}

.focus\:ring-blue-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(59 130 246/var(--tw-ring-opacity))
}

.focus\:ring-indigo-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(99 102 241/var(--tw-ring-opacity))
}

.focus-visible\:border-gray-300:focus-visible {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity))
}

.focus-visible\:ring-2:focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-visible\:ring-oms-green:focus-visible {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 130 0/var(--tw-ring-opacity))
}

.focus-visible\:ring-white:focus-visible {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(255 255 255/var(--tw-ring-opacity))
}

.focus-visible\:ring-opacity-75:focus-visible {
    --tw-ring-opacity: 0.75
}

.focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px
}

.focus-visible\:ring-offset-gray-300:focus-visible {
    --tw-ring-offset-color: #d1d5db
}

.active\:translate-y-1:active {
    --tw-translate-y: 0.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.active\:bg-\[\#004800\]:active {
    --tw-bg-opacity: 1;
    background-color: rgb(0 72 0/var(--tw-bg-opacity))
}

.active\:bg-\[\#d8f5d8\]:active {
    --tw-bg-opacity: 1;
    background-color: rgb(216 245 216/var(--tw-bg-opacity))
}

.active\:bg-oms-green:active {
    --tw-bg-opacity: 1;
    background-color: rgb(0 130 0/var(--tw-bg-opacity))
}

.active\:text-white:active {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.active\:opacity-50:active {
    opacity: .5
}

.active\:shadow-\[0_6px_0_0_\#006643\]:active {
    --tw-shadow: 0 6px 0 0 #006643;
    --tw-shadow-colored: 0 6px 0 0 var(--tw-shadow-color)
}

.active\:shadow-\[0_6px_0_0_\#006643\]:active, .active\:shadow-\[0_6px_0_0_\#E2E2E2\]:active {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.active\:shadow-\[0_6px_0_0_\#E2E2E2\]:active {
    --tw-shadow: 0 6px 0 0 #e2e2e2;
    --tw-shadow-colored: 0 6px 0 0 var(--tw-shadow-color)
}

.disabled\:pointer-events-none:disabled {
    pointer-events: none
}

.disabled\:cursor-default:disabled {
    cursor: default
}

.disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed
}

.disabled\:bg-inherit:disabled {
    background-color: inherit
}

.disabled\:bg-oms-gray-2:disabled {
    --tw-bg-opacity: 1;
    background-color: rgb(205 205 205/var(--tw-bg-opacity))
}

.disabled\:bg-oms-gray-6:disabled {
    --tw-bg-opacity: 1;
    background-color: rgb(216 216 218/var(--tw-bg-opacity))
}

.disabled\:font-normal:disabled {
    font-weight: 400
}

.disabled\:\!text-\[\#4B5563\]:disabled {
    --tw-text-opacity: 1 !important;
    color: rgb(75 85 99/var(--tw-text-opacity)) !important
}

.disabled\:text-oms-gray-4:disabled {
    --tw-text-opacity: 1;
    color: rgb(58 61 70/var(--tw-text-opacity))
}

.disabled\:text-oms-gray-5:disabled {
    --tw-text-opacity: 1;
    color: rgb(117 119 126/var(--tw-text-opacity))
}

.disabled\:text-opacity-40:disabled {
    --tw-text-opacity: 0.4
}

.disabled\:\!opacity-100:disabled {
    opacity: 1 !important
}

.disabled\:opacity-30:disabled {
    opacity: .3
}

.disabled\:opacity-40:disabled {
    opacity: .4
}

.disabled\:opacity-50:disabled {
    opacity: .5
}

.disabled\:opacity-60:disabled {
    opacity: .6
}

.disabled\:after\:bg-transparent:disabled:after {
    content: var(--tw-content);
    background-color: transparent
}

.disabled\:hover\:bg-gray-900:hover:disabled {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39/var(--tw-bg-opacity))
}

.disabled\:hover\:bg-secondary-900:hover:disabled {
    background-color: var(--color-secondary-900)
}

.disabled\:hover\:bg-transparent:hover:disabled {
    background-color: transparent
}

.disabled\:hover\:text-\[rgba\(0\2c 0\2c 0\2c 0\.38\)\]:hover:disabled {
    color: rgba(0, 0, 0, .38)
}

.disabled\:hover\:text-white:hover:disabled {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.group:focus-within .group-focus-within\:left-4 {
    left: 1rem
}

.group:focus-within .group-focus-within\:\!text-xs {
    font-size: .75rem !important;
    line-height: 1rem !important
}

.group:focus-within .group-focus-within\:opacity-100 {
    opacity: 1
}

.group:hover .group-hover\:ml-2 {
    margin-left: .5rem
}

.group:hover .group-hover\:block {
    display: block
}

.group:hover .group-hover\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:bg-oms-green-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.group:hover .group-hover\:bg-oms-orange {
    --tw-bg-opacity: 1;
    background-color: rgb(255 184 0/var(--tw-bg-opacity))
}

.group:hover .group-hover\:bg-oms-purple {
    --tw-bg-opacity: 1;
    background-color: rgb(132 105 242/var(--tw-bg-opacity))
}

.group:hover .group-hover\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.group:hover .group-hover\:text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity))
}

.group:hover .group-hover\:text-oms-green-5 {
    --tw-text-opacity: 1;
    color: rgb(1 152 88/var(--tw-text-opacity))
}

.group:hover .group-hover\:text-oms-orange-4 {
    --tw-text-opacity: 1;
    color: rgb(255 234 178/var(--tw-text-opacity))
}

.group:hover .group-hover\:text-oms-purple-4 {
    --tw-text-opacity: 1;
    color: rgb(215 206 251/var(--tw-text-opacity))
}

.group.is-active .group-\[\.is-active\]\:bg-oms-green-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.group.is-active .group-\[\.is-active\]\:bg-oms-orange {
    --tw-bg-opacity: 1;
    background-color: rgb(255 184 0/var(--tw-bg-opacity))
}

.group.is-active .group-\[\.is-active\]\:text-oms-green-5 {
    --tw-text-opacity: 1;
    color: rgb(1 152 88/var(--tw-text-opacity))
}

.group.is-active .group-\[\.is-active\]\:text-oms-orange-4 {
    --tw-text-opacity: 1;
    color: rgb(255 234 178/var(--tw-text-opacity))
}

.peer:checked ~ .peer-checked\:border-primary {
    border-color: var(--color-primary)
}

.peer:valid ~ .peer-valid\:left-4 {
    left: 1rem
}

.peer:valid ~ .peer-valid\:\!text-xs {
    font-size: .75rem !important;
    line-height: 1rem !important
}

.peer:valid ~ .peer-valid\:text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.peer:valid ~ .peer-valid\:opacity-100 {
    opacity: 1
}

.peer:focus-within ~ .peer-focus-within\:text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.data-\[closed\]\:-translate-y-1[data-closed] {
    --tw-translate-y: -0.25rem
}

.data-\[closed\]\:-translate-y-1[data-closed], .data-\[closed\]\:-translate-y-6[data-closed] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[closed\]\:-translate-y-6[data-closed] {
    --tw-translate-y: -1.5rem
}

.data-\[closed\]\:translate-x-full[data-closed] {
    --tw-translate-x: 100%
}

.data-\[closed\]\:translate-x-full[data-closed], .data-\[closed\]\:translate-y-full[data-closed] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[closed\]\:translate-y-full[data-closed] {
    --tw-translate-y: 100%
}

.data-\[closed\]\:scale-95[data-closed] {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[disabled\]\:cursor-not-allowed[data-disabled] {
    cursor: not-allowed
}

.data-\[checked\]\:border-none[data-checked] {
    border-style: none
}

.data-\[active\]\:bg-\[\#F3F4F6\][data-active] {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity))
}

.data-\[checked\]\:bg-oms-green-6[data-checked] {
    --tw-bg-opacity: 1;
    background-color: rgb(0 135 0/var(--tw-bg-opacity))
}

.data-\[checked\]\:data-\[disabled\]\:bg-gray-500[data-disabled][data-checked] {
    --tw-bg-opacity: 1;
    background-color: rgb(107 114 128/var(--tw-bg-opacity))
}

.data-\[focus\]\:bg-oms-green-2[data-focus] {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.data-\[hover\]\:bg-\[\#F3F4F6\][data-hover] {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity))
}

.data-\[selected\]\:bg-oms-green-2[data-selected] {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.data-\[closed\]\:data-\[leave\]\:opacity-0[data-leave][data-closed], .data-\[closed\]\:opacity-0[data-closed] {
    opacity: 0
}

.data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5
}

.data-\[focus\]\:outline-1[data-focus] {
    outline-width: 1px
}

.data-\[focus\]\:outline-white[data-focus] {
    outline-color: #fff
}

.data-\[leave\]\:transition[data-leave] {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.data-\[leave\]\:duration-100[data-leave] {
    transition-duration: .1s
}

.data-\[leave\]\:ease-in[data-leave] {
    transition-timing-function: cubic-bezier(.4, 0, 1, 1)
}

.group[data-open] .group-data-\[open\]\:rotate-180 {
    --tw-rotate: 180deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group[data-selected] .group-data-\[selected\]\:font-bold {
    font-weight: 700
}

.group[data-selected] .group-data-\[selected\]\:font-medium {
    font-weight: 500
}

.group[data-hover] .group-data-\[hover\]\:text-white\/80 {
    color: hsla(0, 0%, 100%, .8)
}

.peer:checked ~ .group .group-peer-checked\:block {
    display: block
}

.peer:checked ~ .group .group-peer-checked\:hidden {
    display: none
}

@media (min-width: 640px) {
    .sm\:visible {
        visibility: visible
    }

    .sm\:absolute {
        position: absolute
    }

    .sm\:relative {
        position: relative
    }

    .sm\:left-\[calc\(\(100\%-1280px\)\/2\)\] {
        left: calc((100% - 1280px) / 2)
    }

    .sm\:top-0 {
        top: 0
    }

    .sm\:top-4 {
        top: 1rem
    }

    .sm\:-mx-6 {
        margin-left: -1.5rem;
        margin-right: -1.5rem
    }

    .sm\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .sm\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .sm\:mb-10 {
        margin-bottom: 2.5rem
    }

    .sm\:mb-4 {
        margin-bottom: 1rem
    }

    .sm\:ml-2 {
        margin-left: .5rem
    }

    .sm\:block {
        display: block
    }

    .sm\:flex {
        display: flex
    }

    .sm\:hidden {
        display: none
    }

    .sm\:h-\[560px\] {
        height: 560px
    }

    .sm\:h-full {
        height: 100%
    }

    .sm\:min-h-\[32px\] {
        min-height: 32px
    }

    .sm\:w-1\/4 {
        width: 25%
    }

    .sm\:w-2\/4 {
        width: 50%
    }

    .sm\:w-5\/12 {
        width: 41.666667%
    }

    .sm\:w-\[220px\] {
        width: 220px
    }

    .sm\:w-\[233px\] {
        width: 233px
    }

    .sm\:w-\[240px\] {
        width: 240px
    }

    .sm\:w-\[284\.5px\] {
        width: 284.5px
    }

    .sm\:w-\[calc\(25\%-16px\)\] {
        width: calc(25% - 16px)
    }

    .sm\:w-\[calc\(50\%-20px\)\] {
        width: calc(50% - 20px)
    }

    .sm\:w-full {
        width: 100%
    }

    .sm\:max-w-\[460px\] {
        max-width: 460px
    }

    .sm\:max-w-\[480px\] {
        max-width: 480px
    }

    .sm\:max-w-\[540px\] {
        max-width: 540px
    }

    .sm\:max-w-md {
        max-width: 28rem
    }

    .sm\:flex-1 {
        flex: 1 1 0%
    }

    .sm\:flex-row {
        flex-direction: row
    }

    .sm\:items-start {
        align-items: flex-start
    }

    .sm\:items-center {
        align-items: center
    }

    .sm\:justify-end {
        justify-content: flex-end
    }

    .sm\:justify-center {
        justify-content: center
    }

    .sm\:justify-between {
        justify-content: space-between
    }

    .sm\:rounded-lg {
        border-radius: .5rem
    }

    .sm\:bg-transparent {
        background-color: transparent
    }

    .sm\:p-\[40px\] {
        padding: 40px
    }

    .sm\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }

    .sm\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }

    .sm\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .sm\:px-\[20px\] {
        padding-left: 20px;
        padding-right: 20px
    }

    .sm\:px-\[24px\] {
        padding-left: 24px;
        padding-right: 24px
    }

    .sm\:px-\[2rem\] {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .sm\:py-14 {
        padding-top: 3.5rem;
        padding-bottom: 3.5rem
    }

    .sm\:py-32 {
        padding-top: 8rem;
        padding-bottom: 8rem
    }

    .sm\:py-6 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
    }

    .sm\:py-\[26px\] {
        padding-top: 26px;
        padding-bottom: 26px
    }

    .sm\:py-\[90px\] {
        padding-top: 90px;
        padding-bottom: 90px
    }

    .sm\:pl-16 {
        padding-left: 4rem
    }

    .sm\:pt-12 {
        padding-top: 3rem
    }

    .sm\:text-left {
        text-align: left
    }

    .sm\:text-center {
        text-align: center
    }

    .sm\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .sm\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem
    }

    .sm\:text-\[13px\] {
        font-size: 13px
    }

    .sm\:text-\[2rem\] {
        font-size: 2rem
    }

    .sm\:text-\[32px\] {
        font-size: 32px
    }

    .sm\:text-\[50px\] {
        font-size: 50px
    }

    .sm\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }

    .sm\:text-sm {
        font-size: .875rem;
        line-height: 1.25rem
    }

    .sm\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem
    }

    .sm\:leading-6 {
        line-height: 1.5rem
    }

    .sm\:leading-\[28px\] {
        line-height: 28px
    }

    .sm\:leading-\[3rem\] {
        line-height: 3rem
    }

    .sm\:text-white {
        --tw-text-opacity: 1;
        color: rgb(255 255 255/var(--tw-text-opacity))
    }

    .sm\:even\:flex-row-reverse:nth-child(2n) {
        flex-direction: row-reverse
    }
}

@media (min-width: 768px) {
    .md\:m-10 {
        margin: 2.5rem
    }

    .md\:mx-10 {
        margin-left: 2.5rem;
        margin-right: 2.5rem
    }

    .md\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .md\:mb-8 {
        margin-bottom: 2rem
    }

    .md\:block {
        display: block
    }

    .md\:hidden {
        display: none
    }

    .md\:w-1\/4 {
        width: 25%
    }

    .md\:w-\[100\%\] {
        width: 100%
    }

    .md\:w-\[200px\] {
        width: 200px
    }

    .md\:w-\[calc\(100\%-5rem\)\] {
        width: calc(100% - 5rem)
    }

    .md\:grid-cols-1 {
        grid-template-columns:repeat(1, minmax(0, 1fr))
    }

    .md\:grid-cols-2 {
        grid-template-columns:repeat(2, minmax(0, 1fr))
    }

    .md\:flex-wrap {
        flex-wrap: wrap
    }

    .md\:gap-20 {
        gap: 5rem
    }

    .md\:gap-\[30px\] {
        gap: 30px
    }

    .md\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem
    }

    .md\:py-10 {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem
    }

    .md\:py-8 {
        padding-top: 2rem;
        padding-bottom: 2rem
    }

    .md\:pt-2 {
        padding-top: .5rem
    }

    .md\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }
}

@media (min-width: 1024px) {
    .lg\:-mx-8 {
        margin-left: -2rem;
        margin-right: -2rem
    }

    .lg\:mx-\[30px\] {
        margin-left: 30px;
        margin-right: 30px
    }

    .lg\:my-0 {
        margin-top: 0;
        margin-bottom: 0
    }

    .lg\:mb-5 {
        margin-bottom: 1.25rem
    }

    .lg\:mb-\[3\.75rem\] {
        margin-bottom: 3.75rem
    }

    .lg\:mr-20 {
        margin-right: 5rem
    }

    .lg\:mr-\[110px\] {
        margin-right: 110px
    }

    .lg\:mr-\[30px\] {
        margin-right: 30px
    }

    .lg\:mt-0 {
        margin-top: 0
    }

    .lg\:mt-12 {
        margin-top: 3rem
    }

    .lg\:mt-2 {
        margin-top: .5rem
    }

    .lg\:block {
        display: block
    }

    .lg\:flex {
        display: flex
    }

    .lg\:hidden {
        display: none
    }

    .lg\:w-2\/4 {
        width: 50%
    }

    .lg\:w-\[250px\] {
        width: 250px
    }

    .lg\:w-\[330px\] {
        width: 330px
    }

    .lg\:w-\[calc\(40\%-4rem\)\] {
        width: calc(40% - 4rem)
    }

    .lg\:w-\[calc\(50\%-56px\)\] {
        width: calc(50% - 56px)
    }

    .lg\:w-\[calc\(50\%-60px\)\] {
        width: calc(50% - 60px)
    }

    .lg\:max-w-\[490px\] {
        max-width: 490px
    }

    .lg\:max-w-\[500px\] {
        max-width: 500px
    }

    .lg\:max-w-\[622px\] {
        max-width: 622px
    }

    .lg\:max-w-\[632px\] {
        max-width: 632px
    }

    .lg\:grid-cols-2 {
        grid-template-columns:repeat(2, minmax(0, 1fr))
    }

    .lg\:grid-cols-6 {
        grid-template-columns:repeat(6, minmax(0, 1fr))
    }

    .lg\:flex-col {
        flex-direction: column
    }

    .lg\:gap-y-2 {
        row-gap: .5rem
    }

    .lg\:rounded-xl {
        border-radius: .75rem
    }

    .lg\:bg-secondary-light-400 {
        background-color: var(--color-secondary-light-400)
    }

    .lg\:p-10 {
        padding: 2.5rem
    }

    .lg\:p-6 {
        padding: 1.5rem
    }

    .lg\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .lg\:pb-12 {
        padding-bottom: 3rem
    }

    .lg\:pt-0 {
        padding-top: 0
    }

    .lg\:pt-6 {
        padding-top: 1.5rem
    }

    .lg\:pt-8 {
        padding-top: 2rem
    }

    .lg\:text-center {
        text-align: center
    }

    .lg\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .lg\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem
    }

    .lg\:text-\[2\.75rem\] {
        font-size: 2.75rem
    }

    .lg\:text-\[56px\] {
        font-size: 56px
    }

    .lg\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }

    .lg\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem
    }
}

@media (min-width: 1280px) {
    .xl\:col-span-5 {
        grid-column: span 5/span 5
    }

    .xl\:col-span-7 {
        grid-column: span 7/span 7
    }

    .xl\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .xl\:mr-5 {
        margin-right: 1.25rem
    }

    .xl\:mt-0 {
        margin-top: 0
    }

    .xl\:mt-10 {
        margin-top: 2.5rem
    }

    .xl\:mt-2 {
        margin-top: .5rem
    }

    .xl\:mt-5 {
        margin-top: 1.25rem
    }

    .xl\:block {
        display: block
    }

    .xl\:flex {
        display: flex
    }

    .xl\:grid {
        display: grid
    }

    .xl\:w-\[295px\] {
        width: 295px
    }

    .xl\:w-\[45\%\] {
        width: 45%
    }

    .xl\:w-\[55\%\] {
        width: 55%
    }

    .xl\:w-auto {
        width: auto
    }

    .xl\:table-fixed {
        table-layout: fixed
    }

    .xl\:grid-cols-12 {
        grid-template-columns:repeat(12, minmax(0, 1fr))
    }

    .xl\:grid-cols-2 {
        grid-template-columns:repeat(2, minmax(0, 1fr))
    }

    .xl\:grid-cols-4 {
        grid-template-columns:repeat(4, minmax(0, 1fr))
    }

    .xl\:flex-col {
        flex-direction: column
    }

    .xl\:flex-wrap {
        flex-wrap: wrap
    }

    .xl\:gap-0 {
        gap: 0
    }

    .xl\:gap-\[48px\] {
        gap: 48px
    }

    .xl\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
        --tw-space-x-reverse: 0;
        margin-right: calc(1.5rem * var(--tw-space-x-reverse));
        margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))
    }

    .xl\:divide-x > :not([hidden]) ~ :not([hidden]) {
        --tw-divide-x-reverse: 0;
        border-right-width: calc(1px * var(--tw-divide-x-reverse));
        border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)))
    }

    .xl\:p-6 {
        padding: 1.5rem
    }

    .xl\:pl-6 {
        padding-left: 1.5rem
    }

    .xl\:text-\[24px\] {
        font-size: 24px
    }
}

@media (min-width: 1536px) {
    .\32xl\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }
}

@media (min-width: 512px) {
    .xsmall\:left-auto {
        left: auto
    }

    .xsmall\:right-0 {
        right: 0
    }

    .xsmall\:mx-2 {
        margin-left: .5rem;
        margin-right: .5rem
    }

    .xsmall\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .xsmall\:mx-\[-1rem\] {
        margin-left: -1rem;
        margin-right: -1rem
    }

    .xsmall\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .xsmall\:mb-0 {
        margin-bottom: 0
    }

    .xsmall\:mb-10 {
        margin-bottom: 2.5rem
    }

    .xsmall\:mb-6 {
        margin-bottom: 1.5rem
    }

    .xsmall\:mt-8 {
        margin-top: 2rem
    }

    .xsmall\:flex {
        display: flex
    }

    .xsmall\:w-32 {
        width: 8rem
    }

    .xsmall\:w-\[168px\] {
        width: 168px
    }

    .xsmall\:w-\[calc\(50\%-1rem\)\] {
        width: calc(50% - 1rem)
    }

    .xsmall\:w-\[calc\(50\%-2rem\)\] {
        width: calc(50% - 2rem)
    }

    .xsmall\:min-w-\[200px\] {
        min-width: 200px
    }

    .xsmall\:flex-wrap {
        flex-wrap: wrap
    }

    .xsmall\:justify-end {
        justify-content: flex-end
    }

    .xsmall\:justify-center {
        justify-content: center
    }

    .xsmall\:rounded-xl {
        border-radius: .75rem
    }

    .xsmall\:bg-secondary-light-400 {
        background-color: var(--color-secondary-light-400)
    }

    .xsmall\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }

    .xsmall\:px-2 {
        padding-left: .5rem;
        padding-right: .5rem
    }

    .xsmall\:px-\[1\.75rem\] {
        padding-left: 1.75rem;
        padding-right: 1.75rem
    }

    .xsmall\:pb-0 {
        padding-bottom: 0
    }

    .xsmall\:pb-6 {
        padding-bottom: 1.5rem
    }

    .xsmall\:pt-10 {
        padding-top: 2.5rem
    }

    .xsmall\:pt-8 {
        padding-top: 2rem
    }

    .xsmall\:text-center {
        text-align: center
    }

    .xsmall\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }
}

@media (min-width: 768px) {
    .regular\:static {
        position: static
    }

    .regular\:m-0 {
        margin: 0
    }

    .regular\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .regular\:mx-\[-1rem\] {
        margin-left: -1rem;
        margin-right: -1rem
    }

    .regular\:ml-10 {
        margin-left: 2.5rem
    }

    .regular\:mt-10 {
        margin-top: 2.5rem
    }

    .regular\:mt-5 {
        margin-top: 1.25rem
    }

    .regular\:mt-6 {
        margin-top: 1.5rem
    }

    .regular\:block {
        display: block
    }

    .regular\:flex {
        display: flex
    }

    .regular\:\!w-full {
        width: 100% !important
    }

    .regular\:w-2\/6 {
        width: 33.333333%
    }

    .regular\:w-\[40\%\] {
        width: 40%
    }

    .regular\:w-\[calc\(50\%-20px\)\] {
        width: calc(50% - 20px)
    }

    .regular\:w-auto {
        width: auto
    }

    .regular\:w-full {
        width: 100%
    }

    .regular\:max-w-\[27\.5rem\] {
        max-width: 27.5rem
    }

    .regular\:max-w-\[320px\] {
        max-width: 320px
    }

    .regular\:flex-1 {
        flex: 1 1 0%
    }

    .regular\:flex-row {
        flex-direction: row
    }

    .regular\:flex-col {
        flex-direction: column
    }

    .regular\:flex-wrap {
        flex-wrap: wrap
    }

    .regular\:items-start {
        align-items: flex-start
    }

    .regular\:justify-start {
        justify-content: flex-start
    }

    .regular\:justify-between {
        justify-content: space-between
    }

    .regular\:rounded-xl {
        border-radius: .75rem
    }

    .regular\:border {
        border-width: 1px
    }

    .regular\:border-0 {
        border-width: 0
    }

    .regular\:border-b-0 {
        border-bottom-width: 0
    }

    .regular\:border-solid {
        border-style: solid
    }

    .regular\:border-secondary-light {
        border-color: var(--color-secondary-light)
    }

    .regular\:\!bg-secondary-light-400 {
        background-color: var(--color-secondary-light-400) !important
    }

    .regular\:bg-\[\#FCFCFD\] {
        --tw-bg-opacity: 1;
        background-color: rgb(252 252 253/var(--tw-bg-opacity))
    }

    .regular\:p-6 {
        padding: 1.5rem
    }

    .regular\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .regular\:px-3 {
        padding-left: .75rem;
        padding-right: .75rem
    }

    .regular\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem
    }

    .regular\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .regular\:py-0 {
        padding-top: 0;
        padding-bottom: 0
    }

    .regular\:py-2 {
        padding-top: .5rem;
        padding-bottom: .5rem
    }

    .regular\:py-6 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
    }

    .regular\:py-8 {
        padding-top: 2rem;
        padding-bottom: 2rem
    }

    .regular\:pb-0 {
        padding-bottom: 0
    }

    .regular\:pb-6 {
        padding-bottom: 1.5rem
    }

    .regular\:pb-8 {
        padding-bottom: 2rem
    }

    .regular\:pl-3 {
        padding-left: .75rem
    }

    .regular\:pt-0 {
        padding-top: 0
    }

    .regular\:text-\[2\.5rem\] {
        font-size: 2.5rem
    }

    .regular\:text-\[2rem\] {
        font-size: 2rem
    }

    .regular\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }

    .regular\:shadow-md {
        --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
        --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color)
    }

    .regular\:shadow-md, .regular\:shadow-none {
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
    }

    .regular\:shadow-none {
        --tw-shadow: 0 0 #0000;
        --tw-shadow-colored: 0 0 #0000
    }
}

@media (min-width: 1024px) {
    .small\:static {
        position: static
    }

    .small\:absolute {
        position: absolute
    }

    .small\:relative {
        position: relative
    }

    .small\:sticky {
        position: sticky
    }

    .small\:right-0 {
        right: 0
    }

    .small\:top-0 {
        top: 0
    }

    .small\:top-20 {
        top: 5rem
    }

    .small\:mx-0 {
        margin-left: 0;
        margin-right: 0
    }

    .small\:mx-16 {
        margin-left: 4rem;
        margin-right: 4rem
    }

    .small\:mx-2 {
        margin-left: .5rem;
        margin-right: .5rem
    }

    .small\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .small\:mx-8 {
        margin-left: 2rem;
        margin-right: 2rem
    }

    .small\:mx-\[-1rem\] {
        margin-left: -1rem;
        margin-right: -1rem
    }

    .small\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .small\:my-0 {
        margin-top: 0;
        margin-bottom: 0
    }

    .small\:my-32 {
        margin-top: 8rem;
        margin-bottom: 8rem
    }

    .small\:mb-0 {
        margin-bottom: 0
    }

    .small\:mb-10 {
        margin-bottom: 2.5rem
    }

    .small\:mb-12 {
        margin-bottom: 3rem
    }

    .small\:mb-4 {
        margin-bottom: 1rem
    }

    .small\:ml-0 {
        margin-left: 0
    }

    .small\:ml-3 {
        margin-left: .75rem
    }

    .small\:ml-\[109px\] {
        margin-left: 109px
    }

    .small\:ml-\[80px\] {
        margin-left: 80px
    }

    .small\:ml-auto {
        margin-left: auto
    }

    .small\:mr-4 {
        margin-right: 1rem
    }

    .small\:mr-6 {
        margin-right: 1.5rem
    }

    .small\:mt-0 {
        margin-top: 0
    }

    .small\:mt-10 {
        margin-top: 2.5rem
    }

    .small\:mt-12 {
        margin-top: 3rem
    }

    .small\:mt-16 {
        margin-top: 4rem
    }

    .small\:mt-2 {
        margin-top: .5rem
    }

    .small\:mt-2\.5 {
        margin-top: .625rem
    }

    .small\:mt-6 {
        margin-top: 1.5rem
    }

    .small\:mt-\[3\.75rem\] {
        margin-top: 3.75rem
    }

    .small\:block {
        display: block
    }

    .small\:flex {
        display: flex
    }

    .small\:grid {
        display: grid
    }

    .small\:hidden {
        display: none
    }

    .small\:aspect-\[28\/36\] {
        aspect-ratio: 28/36
    }

    .small\:min-h-screen {
        min-height: 100vh
    }

    .small\:w-24 {
        width: 6rem
    }

    .small\:w-96 {
        width: 24rem
    }

    .small\:w-\[200px\] {
        width: 200px
    }

    .small\:w-\[250px\] {
        width: 250px
    }

    .small\:w-\[35\%\] {
        width: 35%
    }

    .small\:w-\[408px\] {
        width: 408px
    }

    .small\:w-\[calc\(100\%-160px\)\] {
        width: calc(100% - 160px)
    }

    .small\:w-\[calc\(25\%-1rem\)\] {
        width: calc(25% - 1rem)
    }

    .small\:w-\[calc\(25\%-2rem\)\] {
        width: calc(25% - 2rem)
    }

    .small\:w-\[calc\(33\.33\%-2rem\)\] {
        width: calc(33.33% - 2rem)
    }

    .small\:w-\[calc\(50\%-40px\)\] {
        width: calc(50% - 40px)
    }

    .small\:min-w-\[250px\] {
        min-width: 250px
    }

    .small\:min-w-\[300px\] {
        min-width: 300px
    }

    .small\:max-w-\[140px\] {
        max-width: 140px
    }

    .small\:max-w-\[344px\] {
        max-width: 344px
    }

    .small\:max-w-\[400px\] {
        max-width: 400px
    }

    .small\:max-w-\[calc\(780px\)\] {
        max-width: calc(780px)
    }

    .small\:flex-1 {
        flex: 1 1 0%
    }

    .small\:grid-cols-1 {
        grid-template-columns:repeat(1, minmax(0, 1fr))
    }

    .small\:grid-cols-2 {
        grid-template-columns:repeat(2, minmax(0, 1fr))
    }

    .small\:grid-cols-3 {
        grid-template-columns:repeat(3, minmax(0, 1fr))
    }

    .small\:grid-cols-4 {
        grid-template-columns:repeat(4, minmax(0, 1fr))
    }

    .small\:grid-cols-\[1fr_360px\] {
        grid-template-columns:1fr 360px
    }

    .small\:flex-row {
        flex-direction: row
    }

    .small\:flex-col {
        flex-direction: column
    }

    .small\:flex-wrap {
        flex-wrap: wrap
    }

    .small\:items-start {
        align-items: flex-start
    }

    .small\:items-center {
        align-items: center
    }

    .small\:justify-start {
        justify-content: flex-start
    }

    .small\:justify-end {
        justify-content: flex-end
    }

    .small\:justify-between {
        justify-content: space-between
    }

    .small\:gap-10 {
        gap: 2.5rem
    }

    .small\:gap-y-2 {
        row-gap: .5rem
    }

    .small\:gap-y-3 {
        row-gap: .75rem
    }

    .small\:overflow-hidden {
        overflow: hidden
    }

    .small\:rounded-xl {
        border-radius: .75rem
    }

    .small\:border-0 {
        border-width: 0
    }

    .small\:p-32 {
        padding: 8rem
    }

    .small\:p-4 {
        padding: 1rem
    }

    .small\:p-6 {
        padding: 1.5rem
    }

    .small\:p-7 {
        padding: 1.75rem
    }

    .small\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .small\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }

    .small\:px-16 {
        padding-left: 4rem;
        padding-right: 4rem
    }

    .small\:px-20 {
        padding-left: 5rem;
        padding-right: 5rem
    }

    .small\:px-3 {
        padding-left: .75rem;
        padding-right: .75rem
    }

    .small\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem
    }

    .small\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }

    .small\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .small\:py-0 {
        padding-top: 0;
        padding-bottom: 0
    }

    .small\:py-12 {
        padding-top: 3rem;
        padding-bottom: 3rem
    }

    .small\:py-20 {
        padding-top: 5rem;
        padding-bottom: 5rem
    }

    .small\:py-24 {
        padding-top: 6rem;
        padding-bottom: 6rem
    }

    .small\:py-3 {
        padding-top: .75rem;
        padding-bottom: .75rem
    }

    .small\:py-4 {
        padding-top: 1rem;
        padding-bottom: 1rem
    }

    .small\:py-5 {
        padding-top: 1.25rem;
        padding-bottom: 1.25rem
    }

    .small\:py-6 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
    }

    .small\:py-8 {
        padding-top: 2rem;
        padding-bottom: 2rem
    }

    .small\:py-\[6\.75rem\] {
        padding-top: 6.75rem;
        padding-bottom: 6.75rem
    }

    .small\:pb-11 {
        padding-bottom: 2.75rem
    }

    .small\:pb-12 {
        padding-bottom: 3rem
    }

    .small\:pb-14 {
        padding-bottom: 3.5rem
    }

    .small\:pb-16 {
        padding-bottom: 4rem
    }

    .small\:pb-2 {
        padding-bottom: .5rem
    }

    .small\:pb-24 {
        padding-bottom: 6rem
    }

    .small\:pb-4 {
        padding-bottom: 1rem
    }

    .small\:pb-6 {
        padding-bottom: 1.5rem
    }

    .small\:pl-8 {
        padding-left: 2rem
    }

    .small\:pr-0 {
        padding-right: 0
    }

    .small\:pr-28 {
        padding-right: 7rem
    }

    .small\:pt-0 {
        padding-top: 0
    }

    .small\:pt-1 {
        padding-top: .25rem
    }

    .small\:pt-10 {
        padding-top: 2.5rem
    }

    .small\:pt-12 {
        padding-top: 3rem
    }

    .small\:pt-16 {
        padding-top: 4rem
    }

    .small\:pt-24 {
        padding-top: 6rem
    }

    .small\:pt-3 {
        padding-top: .75rem
    }

    .small\:pt-4 {
        padding-top: 1rem
    }

    .small\:pt-6 {
        padding-top: 1.5rem
    }

    .small\:pt-8 {
        padding-top: 2rem
    }

    .small\:pt-\[52px\] {
        padding-top: 52px
    }

    .small\:text-left {
        text-align: left
    }

    .small\:text-center {
        text-align: center
    }

    .small\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .small\:text-5xl {
        font-size: 3rem;
        line-height: 1
    }

    .small\:text-\[2\.5rem\] {
        font-size: 2.5rem
    }

    .small\:text-\[2rem\] {
        font-size: 2rem
    }

    .small\:text-\[32px\] {
        font-size: 32px
    }

    .small\:text-\[40px\] {
        font-size: 40px
    }

    .small\:text-\[76px\] {
        font-size: 76px
    }

    .small\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }

    .small\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }

    .small\:text-sm {
        font-size: .875rem;
        line-height: 1.25rem
    }

    .small\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem
    }

    .small\:leading-\[3\.5rem\] {
        line-height: 3.5rem
    }
}

@media (min-width: 1280px) {
    .medium\:left-banner-content-pos-x {
        left: var(--banner-content-pos-x)
    }

    .medium\:max-w-\[400px\] {
        max-width: 400px
    }

    .medium\:grid-cols-4 {
        grid-template-columns:repeat(4, minmax(0, 1fr))
    }
}

@media (min-width: 1440px) {
    .large\:pl-\[100px\] {
        padding-left: 100px
    }

    .large\:pl-\[30px\] {
        padding-left: 30px
    }

    .large\:pl-\[90px\] {
        padding-left: 90px
    }

    .large\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }
}

@media (min-width: 1680px) {
    .xlarge\:col-span-5 {
        grid-column: span 5/span 5
    }

    .xlarge\:col-span-6 {
        grid-column: span 6/span 6
    }

    .xlarge\:w-10 {
        width: 2.5rem
    }

    .xlarge\:w-\[300px\] {
        width: 300px
    }

    .xlarge\:min-w-\[190px\] {
        min-width: 190px
    }

    .xlarge\:max-w-\[265px\] {
        max-width: 265px
    }

    .xlarge\:pl-\[120px\] {
        padding-left: 120px
    }

    .xlarge\:pl-\[124px\] {
        padding-left: 124px
    }

    .xlarge\:pl-\[40px\] {
        padding-left: 40px
    }
}

@media (max-width: 767px) {
    .mdDown\:\!hidden {
        display: none !important
    }
}

@media (prefers-color-scheme: dark) {
    .dark\:border-gray-600 {
        --tw-border-opacity: 1;
        border-color: rgb(75 85 99/var(--tw-border-opacity))
    }

    .dark\:border-neutral-500 {
        --tw-border-opacity: 1;
        border-color: rgb(115 115 115/var(--tw-border-opacity))
    }

    .dark\:border-secondary-900 {
        border-color: var(--color-secondary-900)
    }

    .dark\:bg-gray-700 {
        --tw-bg-opacity: 1;
        background-color: rgb(55 65 81/var(--tw-bg-opacity))
    }

    .dark\:bg-secondary-700 {
        background-color: var(--color-secondary-700)
    }

    .dark\:bg-secondary-light {
        background-color: var(--color-secondary-light)
    }

    .dark\:text-danger-500 {
        color: var(--color-danger-500)
    }

    .dark\:text-white {
        --tw-text-opacity: 1;
        color: rgb(255 255 255/var(--tw-text-opacity))
    }

    .dark\:placeholder-gray-400::-moz-placeholder {
        --tw-placeholder-opacity: 1;
        color: rgb(156 163 175/var(--tw-placeholder-opacity))
    }

    .dark\:placeholder-gray-400::placeholder {
        --tw-placeholder-opacity: 1;
        color: rgb(156 163 175/var(--tw-placeholder-opacity))
    }

    .dark\:focus\:border-blue-500:focus {
        --tw-border-opacity: 1;
        border-color: rgb(59 130 246/var(--tw-border-opacity))
    }

    .dark\:focus\:ring-\[\#3b5998\]\/55:focus {
        --tw-ring-color: rgba(59, 89, 152, .55)
    }

    .dark\:focus\:ring-blue-500:focus {
        --tw-ring-opacity: 1;
        --tw-ring-color: rgb(59 130 246/var(--tw-ring-opacity))
    }
}

@media (min-width: 768px) {
    .\[\&\>div\:last-child\]\:regular\:pb-6 > div:last-child {
        padding-bottom: 1.5rem
    }
}

.group:not([data-selected]) .\[\.group\:not\(\[data-selected\]\)_\&\]\:hidden {
    display: none
}

@media (min-width: 1441px) {
    .\[\@media\(min-width\:1441px\)\]\:col-span-3 {
        grid-column: span 3/span 3
    }

    .\[\@media\(min-width\:1441px\)\]\:col-span-4 {
        grid-column: span 4/span 4
    }

    .\[\@media\(min-width\:1441px\)\]\:col-span-6 {
        grid-column: span 6/span 6
    }

    .\[\@media\(min-width\:1441px\)\]\:col-span-7 {
        grid-column: span 7/span 7
    }

    .\[\@media\(min-width\:1441px\)\]\:flex {
        display: flex
    }
}

@media (min-width: 2098px) {
    .\[\@media\(min-width\:2098px\)\]\:min-h-\[540px\] {
        min-height: 540px
    }
}

.instructions_step__X8Pyz li {
    position: relative;
    display: flex;
    padding-bottom: 48px
}

.instructions_step__X8Pyz li:last-child {
    padding-bottom: 0
}

.instructions_step__X8Pyz li:last-child:after {
    display: none
}

.instructions_step__X8Pyz li:after {
    position: absolute;
    top: 0;
    left: 23px;
    content: "";
    width: 1px;
    height: 100%;
    border-left: 1px dashed teal;
    opacity: .2
}

.instructions_step__X8Pyz li > span {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 46px;
    height: 46px;
    border-radius: 50%;
    margin-right: 20px;
    background-color: teal;
    color: #fff;
    font-size: 16px;
    filter: drop-shadow(0 4px 20px rgba(0, 128, 128, .32));
    z-index: 10
}

.instructions_step__X8Pyz li > div {
    width: calc(100% - 46px - 20px)
}

.instructions_step__X8Pyz li > div p {
    font-size: 18px;
    font-weight: 700
}

.instructions_step__X8Pyz li > div span {
    font-size: 16px;
    color: #6f767e
}

@media (min-width: 1024px) {
    .instructions_step__X8Pyz {
        display: flex
    }

    .instructions_step__X8Pyz li {
        width: 33.33%;
        flex-direction: column;
        align-items: center
    }

    .instructions_step__X8Pyz li:first-child:before {
        display: none
    }

    .instructions_step__X8Pyz li:first-child:after {
        display: initial
    }

    .instructions_step__X8Pyz li:last-child:after {
        display: none
    }

    .instructions_step__X8Pyz li:after, .instructions_step__X8Pyz li:before {
        content: "";
        position: absolute;
        top: 28px;
        width: 50%;
        height: 1px;
        border-top: 1px dashed teal;
        border-left: 1px dashed teal;
        opacity: .2
    }

    .instructions_step__X8Pyz li:before {
        left: 0
    }

    .instructions_step__X8Pyz li:after {
        left: auto;
        right: 0
    }

    .instructions_step__X8Pyz li > span {
        width: 56px;
        height: 56px;
        margin-right: 0;
        margin-bottom: 24px;
        font-size: 18px
    }

    .instructions_step__X8Pyz li > div {
        width: calc(100% - 56px - 20px);
        text-align: center;
        padding: 0 40px;
        margin: 8px 0 0
    }
}

@font-face {
    font-family: swiper-icons;
    src: url("data:application/font-woff;charset=utf-8;base64, 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");
    font-weight: 400;
    font-style: normal
}

:root {
    --swiper-theme-color: #007aff
}

:host {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    z-index: 1
}

.swiper {
    margin-left: auto;
    margin-right: auto;
    position: relative;
    overflow: hidden;
    list-style: none;
    padding: 0;
    z-index: 1;
    display: block
}

.swiper-vertical > .swiper-wrapper {
    flex-direction: column
}

.swiper-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
    display: flex;
    transition-property: transform;
    transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);
    box-sizing: content-box
}

.swiper-android .swiper-slide, .swiper-ios .swiper-slide, .swiper-wrapper {
    transform: translateZ(0)
}

.swiper-horizontal {
    touch-action: pan-y
}

.swiper-vertical {
    touch-action: pan-x
}

.swiper-slide {
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    position: relative;
    transition-property: transform;
    display: block
}

.swiper-slide-invisible-blank {
    visibility: hidden
}

.swiper-autoheight, .swiper-autoheight .swiper-slide {
    height: auto
}

.swiper-autoheight .swiper-wrapper {
    align-items: flex-start;
    transition-property: transform, height
}

.swiper-backface-hidden .swiper-slide {
    transform: translateZ(0);
    backface-visibility: hidden
}

.swiper-3d.swiper-css-mode .swiper-wrapper {
    perspective: 1200px
}

.swiper-3d .swiper-wrapper {
    transform-style: preserve-3d
}

.swiper-3d {
    perspective: 1200px
}

.swiper-3d .swiper-cube-shadow, .swiper-3d .swiper-slide {
    transform-style: preserve-3d
}

.swiper-css-mode > .swiper-wrapper {
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
    display: none
}

.swiper-css-mode > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: start start
}

.swiper-css-mode.swiper-horizontal > .swiper-wrapper {
    scroll-snap-type: x mandatory
}

.swiper-css-mode.swiper-vertical > .swiper-wrapper {
    scroll-snap-type: y mandatory
}

.swiper-css-mode.swiper-free-mode > .swiper-wrapper {
    scroll-snap-type: none
}

.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: none
}

.swiper-css-mode.swiper-centered > .swiper-wrapper:before {
    content: "";
    flex-shrink: 0;
    order: 9999
}

.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: center center;
    scroll-snap-stop: always
}

.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {
    margin-inline-start: var(--swiper-centered-offset-before)
}

.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper:before {
    height: 100%;
    min-height: 1px;
    width: var(--swiper-centered-offset-after)
}

.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {
    margin-block-start: var(--swiper-centered-offset-before)
}

.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper:before {
    width: 100%;
    min-width: 1px;
    height: var(--swiper-centered-offset-after)
}

.swiper-3d .swiper-slide-shadow, .swiper-3d .swiper-slide-shadow-bottom, .swiper-3d .swiper-slide-shadow-left, .swiper-3d .swiper-slide-shadow-right, .swiper-3d .swiper-slide-shadow-top {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10
}

.swiper-3d .swiper-slide-shadow {
    background: rgba(0, 0, 0, .15)
}

.swiper-3d .swiper-slide-shadow-left {
    background-image: linear-gradient(270deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-3d .swiper-slide-shadow-right {
    background-image: linear-gradient(90deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-3d .swiper-slide-shadow-top {
    background-image: linear-gradient(0deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-3d .swiper-slide-shadow-bottom {
    background-image: linear-gradient(180deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-lazy-preloader {
    width: 42px;
    height: 42px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -21px;
    margin-top: -21px;
    z-index: 10;
    transform-origin: 50%;
    box-sizing: border-box;
    border-radius: 50%;
    border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
    border-top: 4px solid transparent
}

.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader, .swiper:not(.swiper-watch-progress) .swiper-lazy-preloader {
    animation: swiper-preloader-spin 1s linear infinite
}

.swiper-lazy-preloader-white {
    --swiper-preloader-color: #fff
}

.swiper-lazy-preloader-black {
    --swiper-preloader-color: #000
}

@keyframes swiper-preloader-spin {
    0% {
        transform: rotate(0deg)
    }
    to {
        transform: rotate(1turn)
    }
}

.swiper-pagination {
    position: absolute;
    text-align: center;
    transition: opacity .3s;
    transform: translateZ(0);
    z-index: 10
}

.swiper-pagination.swiper-pagination-hidden {
    opacity: 0
}

.swiper-pagination-disabled > .swiper-pagination, .swiper-pagination.swiper-pagination-disabled {
    display: none !important
}

.swiper-horizontal > .swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
    bottom: var(--swiper-pagination-bottom, 8px);
    top: var(--swiper-pagination-top, auto);
    left: 0;
    width: 100%
}

.swiper-pagination-bullets-dynamic {
    overflow: hidden;
    font-size: 0
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transform: scale(.33);
    position: relative
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active, .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
    transform: scale(1)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
    transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
    transform: scale(.33)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
    transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
    transform: scale(.33)
}

.swiper-pagination-bullet {
    width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));
    height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));
    display: inline-block;
    border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
    background: var(--swiper-pagination-bullet-inactive-color, #000);
    opacity: var(--swiper-pagination-bullet-inactive-opacity, .2)
}

button.swiper-pagination-bullet {
    border: none;
    margin: 0;
    padding: 0;
    box-shadow: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.swiper-pagination-clickable .swiper-pagination-bullet {
    cursor: pointer
}

.swiper-pagination-bullet:only-child {
    display: none !important
}

.swiper-pagination-bullet-active {
    opacity: var(--swiper-pagination-bullet-opacity, 1);
    background: var(--swiper-pagination-color, var(--swiper-theme-color))
}

.swiper-pagination-vertical.swiper-pagination-bullets, .swiper-vertical > .swiper-pagination-bullets {
    right: var(--swiper-pagination-right, 8px);
    left: var(--swiper-pagination-left, auto);
    top: 50%;
    transform: translate3d(0, -50%, 0)
}

.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet, .swiper-vertical > .swiper-pagination-bullets .swiper-pagination-bullet {
    margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
    display: block
}

.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic, .swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    top: 50%;
    transform: translateY(-50%);
    width: 8px
}

.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet, .swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    display: inline-block;
    transition: transform .2s, top .2s
}

.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet, .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px)
}

.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic, .swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap
}

.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet, .swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transition: transform .2s, left .2s
}

.swiper-horizontal.swiper-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transition: transform .2s, right .2s
}

.swiper-pagination-fraction {
    color: var(--swiper-pagination-fraction-color, inherit)
}

.swiper-pagination-progressbar {
    background: var(--swiper-pagination-progressbar-bg-color, rgba(0, 0, 0, .25));
    position: absolute
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    background: var(--swiper-pagination-color, var(--swiper-theme-color));
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    transform: scale(0);
    transform-origin: left top
}

.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    transform-origin: right top
}

.swiper-horizontal > .swiper-pagination-progressbar, .swiper-pagination-progressbar.swiper-pagination-horizontal, .swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite, .swiper-vertical > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
    width: 100%;
    height: var(--swiper-pagination-progressbar-size, 4px);
    left: 0;
    top: 0
}

.swiper-horizontal > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite, .swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite, .swiper-pagination-progressbar.swiper-pagination-vertical, .swiper-vertical > .swiper-pagination-progressbar {
    width: var(--swiper-pagination-progressbar-size, 4px);
    height: 100%;
    left: 0;
    top: 0
}

.swiper-pagination-lock {
    display: none
}

.banners_swiper__0MTBv {
    padding-bottom: 66px
}

.banners_swiperItem__mH1QM span {
    position: static !important
}

.banners_swiperItem__mH1QM img {
    position: static !important;
    width: 100% !important;
    height: auto !important
}

:root {
    --swiper-navigation-size: 44px
}

.swiper-button-next, .swiper-button-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    width: calc(var(--swiper-navigation-size) / 44 * 27);
    height: var(--swiper-navigation-size);
    margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--swiper-navigation-color, var(--swiper-theme-color))
}

.swiper-button-next.swiper-button-disabled, .swiper-button-prev.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none
}

.swiper-button-next.swiper-button-hidden, .swiper-button-prev.swiper-button-hidden {
    opacity: 0;
    cursor: auto;
    pointer-events: none
}

.swiper-navigation-disabled .swiper-button-next, .swiper-navigation-disabled .swiper-button-prev {
    display: none !important
}

.swiper-button-next svg, .swiper-button-prev svg {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    transform-origin: center
}

.swiper-rtl .swiper-button-next svg, .swiper-rtl .swiper-button-prev svg {
    transform: rotate(180deg)
}

.swiper-button-prev, .swiper-rtl .swiper-button-next {
    left: var(--swiper-navigation-sides-offset, 10px);
    right: auto
}

.swiper-button-lock {
    display: none
}

.swiper-button-next:after, .swiper-button-prev:after {
    font-family: swiper-icons;
    font-size: var(--swiper-navigation-size);
    text-transform: none !important;
    letter-spacing: 0;
    font-variant: normal;
    line-height: 1
}

.swiper-button-prev:after, .swiper-rtl .swiper-button-next:after {
    content: "prev"
}

.swiper-button-next, .swiper-rtl .swiper-button-prev {
    right: var(--swiper-navigation-sides-offset, 10px);
    left: auto
}

.swiper-button-next:after, .swiper-rtl .swiper-button-prev:after {
    content: "next"
}

.richtext_richtextContent__c6X8A {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 15px
}

.richtext_richtextContent__c6X8A ol {
    padding-left: 30px
}

.richtext_richtextContent__c6X8A ol * {
    margin-bottom: 25px
}

.richtext_richtextContent__c6X8A ol dt {
    margin-left: -30px;
    font-weight: 700
}

.richtext_richtextContent__c6X8A ol li {
    list-style: decimal
}

.richtext_richtextContent__c6X8A ol li > h3 {
    margin-left: -25px
}

.richtext_richtextContent__c6X8A ol li a {
    color: teal;
    word-break: break-word
}

.richtext_richtextContent__c6X8A ol ul {
    margin: 20px 0 0 50px
}

.richtext_richtextContent__c6X8A ul li {
    list-style: lower-alpha
}

.richtext_richtextContent__c6X8A h1, .richtext_richtextContent__c6X8A h2, .richtext_richtextContent__c6X8A h3, .richtext_richtextContent__c6X8A h4, .richtext_richtextContent__c6X8A h5 {
    font-weight: 700;
    margin-bottom: 25px
}

.richtext_richtextContent__c6X8A h1 {
    font-size: 24px
}

.richtext_richtextContent__c6X8A h2 {
    font-size: 20px
}

.richtext_richtextContent__c6X8A h3 {
    font-size: 16px
}

.richtext_richtextContent__c6X8A h4 {
    text-align: center
}

.bladeContent_listing__HFboo {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    overflow: hidden
}

.bladeContent_listing__HFboo ul li {
    display: flex;
    flex-direction: column
}

.bladeContent_listing__HFboo ul li span {
    position: static !important;
    width: 100% !important
}

.bladeContent_listing__HFboo ul li img {
    position: static !important;
    width: 48px !important;
    height: auto !important;
    max-width: none !important;
    min-width: auto !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    margin: initial !important
}

.bladeContent_listing__HFboo ul li > div:last-child * {
    margin-bottom: 6px
}

.bladeproduct_infoImage__dpJeY {
    width: 100%
}

.bladeproduct_img__9_vCO, .bladeproduct_infoImage__dpJeY span {
    position: static !important
}

.bladeproduct_img__9_vCO {
    width: 100% !important;
    height: auto !important;
    min-width: auto !important;
    min-height: auto !important;
    max-width: none !important;
    max-height: none !important
}

.bladeproduct_BladeProductInfo__TJwtN li {
    background-image: url(/Blade.png);
    background-repeat: no-repeat;
    padding: 7px 0 12px 40px
}

@media (min-width: 768px) {
    .bladeproduct_infoImage__dpJeY {
        width: 50%
    }

    .bladeproduct_img__9_vCO {
        width: 90% !important
    }
}

.carousel_swiper__Z0Mu2 {
    padding-bottom: 66px
}

.carousel_swiperItem__c9npg span {
    position: static !important
}

.carousel_swiperItem__c9npg img {
    position: static !important;
    width: 100% !important;
    height: auto !important
}

.category_listing__JiFl0 ul li {
    display: flex;
    flex-direction: column
}

.category_listing__JiFl0 ul li img, .category_listing__JiFl0 ul li span {
    position: static !important;
    width: 100% !important
}

.category_listing__JiFl0 ul li img {
    height: auto !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px
}

.category_listing__JiFl0 ul li a img {
    max-width: none !important;
    min-width: auto !important
}

.category_listing__JiFl0 ul li a:first-child img {
    width: 72px !important;
    height: 30px !important
}

.category_listing__JiFl0 ul li a:first-child + a img {
    width: 88px !important;
    height: 28px !important
}

.grid_listing__mB46N {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    overflow: hidden
}

.grid_listing__mB46N ul li {
    display: flex;
    flex-direction: column
}

.grid_listing__mB46N ul li img, .grid_listing__mB46N ul li span {
    position: static !important;
    width: 100% !important
}

.grid_listing__mB46N ul li img {
    height: auto !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px
}

.grid_listing__mB46N ul li a img {
    max-width: none !important;
    min-width: auto !important
}

.grid_listing__mB46N ul li a:first-child img {
    width: 72px !important;
    height: 30px !important
}

.grid_listing__mB46N ul li a:first-child + a img {
    width: 88px !important;
    height: 28px !important
}

.howItWorksContainer_img__uKc7r img, .howItWorksContainer_img__uKc7r span {
    position: static !important;
    width: 100% !important
}

.howItWorksContainer_img__uKc7r img {
    height: auto !important;
    max-width: none !important;
    min-width: auto !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    margin: initial !important
}

.howItWorksStepsItem_stepImage__5BsA4 {
    width: 100%
}

.howItWorksStepsItem_stepImage__5BsA4 img, .howItWorksStepsItem_stepImage__5BsA4 span {
    position: static !important;
    width: 100% !important
}

.howItWorksStepsItem_stepImage__5BsA4 img {
    height: auto !important;
    max-width: none !important;
    min-width: auto !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    margin: initial !important
}

@media (min-width: 640px) {
    .howItWorksStepsItem_stepImage__5BsA4 {
        width: calc(50% - 20px)
    }
}

@media (min-width: 1024px) {
    .howItWorksStepsItem_stepImage__5BsA4 {
        width: calc(50% - 56px)
    }
}

.react-datepicker__month-read-view--down-arrow, .react-datepicker__month-year-read-view--down-arrow, .react-datepicker__navigation-icon:before, .react-datepicker__year-read-view--down-arrow {
    border-color: #ccc;
    border-style: solid;
    border-width: 3px 3px 0 0;
    content: "";
    display: block;
    height: 9px;
    position: absolute;
    top: 6px;
    width: 9px
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle, .react-datepicker-popper[data-placement^=top] .react-datepicker__triangle {
    margin-left: -4px;
    position: absolute;
    width: 0
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after, .react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before, .react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after, .react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before {
    box-sizing: content-box;
    position: absolute;
    height: 0;
    width: 1px;
    content: "";
    z-index: -1;
    border: 8px solid transparent;
    left: -8px
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before, .react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before {
    border-bottom-color: #aeaeae
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle {
    top: 0;
    margin-top: -8px
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after, .react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before {
    border-top: none;
    border-bottom-color: #f0f0f0
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after {
    top: 0
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before {
    top: -1px;
    border-bottom-color: #aeaeae
}

.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle {
    bottom: 0;
    margin-bottom: -8px
}

.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after, .react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before {
    border-bottom: none;
    border-top-color: #fff
}

.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after {
    bottom: 0
}

.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before {
    bottom: -1px;
    border-top-color: #aeaeae
}

.react-datepicker-wrapper {
    display: inline-block;
    padding: 0;
    border: 0
}

.react-datepicker {
    font-family: Helvetica Neue, helvetica, arial, sans-serif;
    font-size: .8rem;
    background-color: #fff;
    color: #000;
    border: 1px solid #aeaeae;
    border-radius: .3rem;
    display: inline-block;
    position: relative
}

.react-datepicker--time-only .react-datepicker__triangle {
    left: 35px
}

.react-datepicker--time-only .react-datepicker__time-container {
    border-left: 0
}

.react-datepicker--time-only .react-datepicker__time, .react-datepicker--time-only .react-datepicker__time-box {
    border-bottom-left-radius: .3rem;
    border-bottom-right-radius: .3rem
}

.react-datepicker__triangle {
    position: absolute;
    left: 50px
}

.react-datepicker-popper {
    z-index: 1
}

.react-datepicker-popper[data-placement^=bottom] {
    padding-top: 10px
}

.react-datepicker-popper[data-placement=bottom-end] .react-datepicker__triangle, .react-datepicker-popper[data-placement=top-end] .react-datepicker__triangle {
    left: auto;
    right: 50px
}

.react-datepicker-popper[data-placement^=top] {
    padding-bottom: 10px
}

.react-datepicker-popper[data-placement^=right] {
    padding-left: 8px
}

.react-datepicker-popper[data-placement^=right] .react-datepicker__triangle {
    left: auto;
    right: 42px
}

.react-datepicker-popper[data-placement^=left] {
    padding-right: 8px
}

.react-datepicker-popper[data-placement^=left] .react-datepicker__triangle {
    left: 42px;
    right: auto
}

.react-datepicker__header {
    text-align: center;
    background-color: #f0f0f0;
    border-bottom: 1px solid #aeaeae;
    border-top-left-radius: .3rem;
    padding: 8px 0;
    position: relative
}

.react-datepicker__header--time {
    padding-bottom: 8px;
    padding-left: 5px;
    padding-right: 5px
}

.react-datepicker__header--time:not(.react-datepicker__header--time--only) {
    border-top-left-radius: 0
}

.react-datepicker__header:not(.react-datepicker__header--has-time-select) {
    border-top-right-radius: .3rem
}

.react-datepicker__month-dropdown-container--scroll, .react-datepicker__month-dropdown-container--select, .react-datepicker__month-year-dropdown-container--scroll, .react-datepicker__month-year-dropdown-container--select, .react-datepicker__year-dropdown-container--scroll, .react-datepicker__year-dropdown-container--select {
    display: inline-block;
    margin: 0 15px
}

.react-datepicker-time__header, .react-datepicker-year-header, .react-datepicker__current-month {
    margin-top: 0;
    color: #000;
    font-weight: 700;
    font-size: .944rem
}

.react-datepicker-time__header {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
}

.react-datepicker__navigation {
    align-items: center;
    background: none;
    display: flex;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    position: absolute;
    top: 2px;
    padding: 0;
    border: none;
    z-index: 1;
    height: 32px;
    width: 32px;
    text-indent: -999em;
    overflow: hidden
}

.react-datepicker__navigation--previous {
    left: 2px
}

.react-datepicker__navigation--next {
    right: 2px
}

.react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button) {
    right: 85px
}

.react-datepicker__navigation--years {
    position: relative;
    top: 0;
    display: block;
    margin-left: auto;
    margin-right: auto
}

.react-datepicker__navigation--years-previous {
    top: 4px
}

.react-datepicker__navigation--years-upcoming {
    top: -4px
}

.react-datepicker__navigation:hover :before {
    border-color: #a6a6a6
}

.react-datepicker__navigation-icon {
    position: relative;
    top: -1px;
    font-size: 20px;
    width: 0
}

.react-datepicker__navigation-icon--next {
    left: -2px
}

.react-datepicker__navigation-icon--next:before {
    transform: rotate(45deg);
    left: -7px
}

.react-datepicker__navigation-icon--previous {
    right: -2px
}

.react-datepicker__navigation-icon--previous:before {
    transform: rotate(225deg);
    right: -7px
}

.react-datepicker__month-container {
    float: left
}

.react-datepicker__year {
    margin: .4rem;
    text-align: center
}

.react-datepicker__year-wrapper {
    display: flex;
    flex-wrap: wrap;
    max-width: 180px
}

.react-datepicker__year .react-datepicker__year-text {
    display: inline-block;
    width: 4rem;
    margin: 2px
}

.react-datepicker__month {
    margin: .4rem;
    text-align: center
}

.react-datepicker__month .react-datepicker__month-text, .react-datepicker__month .react-datepicker__quarter-text {
    display: inline-block;
    width: 4rem;
    margin: 2px
}

.react-datepicker__input-time-container {
    clear: both;
    width: 100%;
    float: left;
    margin: 5px 0 10px 15px;
    text-align: left
}

.react-datepicker__input-time-container .react-datepicker-time__caption, .react-datepicker__input-time-container .react-datepicker-time__input-container {
    display: inline-block
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input {
    display: inline-block;
    margin-left: 10px
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input {
    width: auto
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-inner-spin-button, .react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time] {
    -moz-appearance: textfield
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__delimiter {
    margin-left: 5px;
    display: inline-block
}

.react-datepicker__time-container {
    float: right;
    border-left: 1px solid #aeaeae;
    width: 85px
}

.react-datepicker__time-container--with-today-button {
    display: inline;
    border: 1px solid #aeaeae;
    border-radius: .3rem;
    position: absolute;
    right: -87px;
    top: 0
}

.react-datepicker__time-container .react-datepicker__time {
    position: relative;
    background: #fff;
    border-bottom-right-radius: .3rem
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box {
    width: 85px;
    overflow-x: hidden;
    margin: 0 auto;
    text-align: center;
    border-bottom-right-radius: .3rem
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list {
    list-style: none;
    margin: 0;
    height: calc(195px + (1.7rem / 2));
    overflow-y: scroll;
    padding-right: 0;
    padding-left: 0;
    width: 100%;
    box-sizing: content-box
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
    height: 30px;
    padding: 5px 10px;
    white-space: nowrap
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
    cursor: pointer;
    background-color: #f0f0f0
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
    background-color: #216ba5;
    color: #fff;
    font-weight: 700
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover {
    background-color: #216ba5
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled {
    color: #ccc
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled:hover {
    cursor: default;
    background-color: transparent
}

.react-datepicker__week-number {
    color: #ccc;
    display: inline-block;
    width: 1.7rem;
    line-height: 1.7rem;
    text-align: center;
    margin: .166rem
}

.react-datepicker__week-number.react-datepicker__week-number--clickable {
    cursor: pointer
}

.react-datepicker__week-number.react-datepicker__week-number--clickable:hover {
    border-radius: .3rem;
    background-color: #f0f0f0
}

.react-datepicker__day-names, .react-datepicker__week {
    white-space: nowrap
}

.react-datepicker__day-names {
    margin-bottom: -8px
}

.react-datepicker__day, .react-datepicker__day-name, .react-datepicker__time-name {
    color: #000;
    display: inline-block;
    width: 1.7rem;
    line-height: 1.7rem;
    text-align: center;
    margin: .166rem
}

.react-datepicker__day, .react-datepicker__month-text, .react-datepicker__quarter-text, .react-datepicker__year-text {
    cursor: pointer
}

.react-datepicker__day:hover, .react-datepicker__month-text:hover, .react-datepicker__quarter-text:hover, .react-datepicker__year-text:hover {
    border-radius: .3rem;
    background-color: #f0f0f0
}

.react-datepicker__day--today, .react-datepicker__month-text--today, .react-datepicker__quarter-text--today, .react-datepicker__year-text--today {
    font-weight: 700
}

.react-datepicker__day--highlighted, .react-datepicker__month-text--highlighted, .react-datepicker__quarter-text--highlighted, .react-datepicker__year-text--highlighted {
    border-radius: .3rem;
    background-color: #3dcc4a;
    color: #fff
}

.react-datepicker__day--highlighted:hover, .react-datepicker__month-text--highlighted:hover, .react-datepicker__quarter-text--highlighted:hover, .react-datepicker__year-text--highlighted:hover {
    background-color: #32be3f
}

.react-datepicker__day--highlighted-custom-1, .react-datepicker__month-text--highlighted-custom-1, .react-datepicker__quarter-text--highlighted-custom-1, .react-datepicker__year-text--highlighted-custom-1 {
    color: #f0f
}

.react-datepicker__day--highlighted-custom-2, .react-datepicker__month-text--highlighted-custom-2, .react-datepicker__quarter-text--highlighted-custom-2, .react-datepicker__year-text--highlighted-custom-2 {
    color: green
}

.react-datepicker__day--in-range, .react-datepicker__day--in-selecting-range, .react-datepicker__day--selected, .react-datepicker__month-text--in-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__month-text--selected, .react-datepicker__quarter-text--in-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__quarter-text--selected, .react-datepicker__year-text--in-range, .react-datepicker__year-text--in-selecting-range, .react-datepicker__year-text--selected {
    border-radius: .3rem;
    background-color: #216ba5;
    color: #fff
}

.react-datepicker__day--in-range:hover, .react-datepicker__day--in-selecting-range:hover, .react-datepicker__day--selected:hover, .react-datepicker__month-text--in-range:hover, .react-datepicker__month-text--in-selecting-range:hover, .react-datepicker__month-text--selected:hover, .react-datepicker__quarter-text--in-range:hover, .react-datepicker__quarter-text--in-selecting-range:hover, .react-datepicker__quarter-text--selected:hover, .react-datepicker__year-text--in-range:hover, .react-datepicker__year-text--in-selecting-range:hover, .react-datepicker__year-text--selected:hover {
    background-color: #1d5d90
}

.react-datepicker__day--keyboard-selected, .react-datepicker__month-text--keyboard-selected, .react-datepicker__quarter-text--keyboard-selected, .react-datepicker__year-text--keyboard-selected {
    border-radius: .3rem;
    background-color: #bad9f1;
    color: #000
}

.react-datepicker__day--keyboard-selected:hover, .react-datepicker__month-text--keyboard-selected:hover, .react-datepicker__quarter-text--keyboard-selected:hover, .react-datepicker__year-text--keyboard-selected:hover {
    background-color: #1d5d90
}

.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range), .react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range), .react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range), .react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range) {
    background-color: rgba(33, 107, 165, .5)
}

.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__year--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__year--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__year--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__year--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range) {
    background-color: #f0f0f0;
    color: #000
}

.react-datepicker__day--disabled, .react-datepicker__month-text--disabled, .react-datepicker__quarter-text--disabled, .react-datepicker__year-text--disabled {
    cursor: default;
    color: #ccc
}

.react-datepicker__day--disabled:hover, .react-datepicker__month-text--disabled:hover, .react-datepicker__quarter-text--disabled:hover, .react-datepicker__year-text--disabled:hover {
    background-color: transparent
}

.react-datepicker__input-container {
    position: relative;
    display: inline-block;
    width: 100%
}

.react-datepicker__input-container .react-datepicker__calendar-icon {
    position: absolute;
    padding: .5rem
}

.react-datepicker__view-calendar-icon input {
    padding: 6px 10px 5px 25px
}

.react-datepicker__month-read-view, .react-datepicker__month-year-read-view, .react-datepicker__year-read-view {
    border: 1px solid transparent;
    border-radius: .3rem;
    position: relative
}

.react-datepicker__month-read-view:hover, .react-datepicker__month-year-read-view:hover, .react-datepicker__year-read-view:hover {
    cursor: pointer
}

.react-datepicker__month-read-view:hover .react-datepicker__month-read-view--down-arrow, .react-datepicker__month-read-view:hover .react-datepicker__year-read-view--down-arrow, .react-datepicker__month-year-read-view:hover .react-datepicker__month-read-view--down-arrow, .react-datepicker__month-year-read-view:hover .react-datepicker__year-read-view--down-arrow, .react-datepicker__year-read-view:hover .react-datepicker__month-read-view--down-arrow, .react-datepicker__year-read-view:hover .react-datepicker__year-read-view--down-arrow {
    border-top-color: #b3b3b3
}

.react-datepicker__month-read-view--down-arrow, .react-datepicker__month-year-read-view--down-arrow, .react-datepicker__year-read-view--down-arrow {
    transform: rotate(135deg);
    right: -16px;
    top: 0
}

.react-datepicker__month-dropdown, .react-datepicker__month-year-dropdown, .react-datepicker__year-dropdown {
    background-color: #f0f0f0;
    position: absolute;
    width: 50%;
    left: 25%;
    top: 30px;
    z-index: 1;
    text-align: center;
    border-radius: .3rem;
    border: 1px solid #aeaeae
}

.react-datepicker__month-dropdown:hover, .react-datepicker__month-year-dropdown:hover, .react-datepicker__year-dropdown:hover {
    cursor: pointer
}

.react-datepicker__month-dropdown--scrollable, .react-datepicker__month-year-dropdown--scrollable, .react-datepicker__year-dropdown--scrollable {
    height: 150px;
    overflow-y: scroll
}

.react-datepicker__month-option, .react-datepicker__month-year-option, .react-datepicker__year-option {
    line-height: 20px;
    width: 100%;
    display: block;
    margin-left: auto;
    margin-right: auto
}

.react-datepicker__month-option:first-of-type, .react-datepicker__month-year-option:first-of-type, .react-datepicker__year-option:first-of-type {
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem
}

.react-datepicker__month-option:last-of-type, .react-datepicker__month-year-option:last-of-type, .react-datepicker__year-option:last-of-type {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    border-bottom-left-radius: .3rem;
    border-bottom-right-radius: .3rem
}

.react-datepicker__month-option:hover, .react-datepicker__month-year-option:hover, .react-datepicker__year-option:hover {
    background-color: #ccc
}

.react-datepicker__month-option:hover .react-datepicker__navigation--years-upcoming, .react-datepicker__month-year-option:hover .react-datepicker__navigation--years-upcoming, .react-datepicker__year-option:hover .react-datepicker__navigation--years-upcoming {
    border-bottom-color: #b3b3b3
}

.react-datepicker__month-option:hover .react-datepicker__navigation--years-previous, .react-datepicker__month-year-option:hover .react-datepicker__navigation--years-previous, .react-datepicker__year-option:hover .react-datepicker__navigation--years-previous {
    border-top-color: #b3b3b3
}

.react-datepicker__month-option--selected, .react-datepicker__month-year-option--selected, .react-datepicker__year-option--selected {
    position: absolute;
    left: 15px
}

.react-datepicker__close-icon {
    cursor: pointer;
    background-color: transparent;
    border: 0;
    outline: 0;
    padding: 0 6px 0 0;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    display: table-cell;
    vertical-align: middle
}

.react-datepicker__close-icon:after {
    cursor: pointer;
    background-color: #216ba5;
    color: #fff;
    border-radius: 50%;
    height: 16px;
    width: 16px;
    padding: 2px;
    font-size: 12px;
    line-height: 1;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
    content: "×"
}

.react-datepicker__today-button {
    background: #f0f0f0;
    border-top: 1px solid #aeaeae;
    cursor: pointer;
    text-align: center;
    font-weight: 700;
    padding: 5px 0;
    clear: left
}

.react-datepicker__portal {
    position: fixed;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .8);
    left: 0;
    top: 0;
    justify-content: center;
    align-items: center;
    display: flex;
    z-index: 2147483647
}

.react-datepicker__portal .react-datepicker__day, .react-datepicker__portal .react-datepicker__day-name, .react-datepicker__portal .react-datepicker__time-name {
    width: 3rem;
    line-height: 3rem
}

@media (max-height: 550px),(max-width: 400px) {
    .react-datepicker__portal .react-datepicker__day, .react-datepicker__portal .react-datepicker__day-name, .react-datepicker__portal .react-datepicker__time-name {
        width: 2rem;
        line-height: 2rem
    }
}

.react-datepicker__portal .react-datepicker-time__header, .react-datepicker__portal .react-datepicker__current-month {
    font-size: 1.44rem
}

.react-datepicker__children-container {
    width: 13.8rem;
    margin: .4rem;
    padding-right: .2rem;
    padding-left: .2rem;
    height: auto
}

.react-datepicker__aria-live {
    position: absolute;
    -webkit-clip-path: circle(0);
    clip-path: circle(0);
    border: 0;
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    width: 1px;
    white-space: nowrap
}

.react-datepicker__calendar-icon {
    width: 1em;
    height: 1em;
    vertical-align: -.125em
}

.input_phoneRow__FsI4F {
    display: flex;
    justify-content: space-between
}

.input_phoneRow__FsI4F > div:first-child {
    position: relative;
    width: 94px;
    height: -moz-fit-content;
    height: fit-content;
    margin-right: 16px
}

.input_phoneRow__FsI4F > div:first-child input {
    padding-left: 46px
}

.subscriptionInfo_infoImage___vE8N {
    width: 50%
}

.subscriptionInfo_infoImage___vE8N span {
    position: static !important
}

.subscriptionInfo_img__Z5WWc {
    position: static !important;
    width: 100% !important;
    height: auto !important;
    min-width: auto !important;
    min-height: auto !important;
    max-width: none !important;
    max-height: none !important
}

.subscriptionInfo_infoDesc__rZsHK {
    padding: 48px 0 0
}

.subscriptionInfo_infoList__pMe_D {
    list-style: none;
    margin: 0;
    padding: 0
}

@media (min-width: 1024px) {
    .subscriptionInfo_infoDesc__rZsHK {
        position: relative;
        padding: 0 0 0 80px;
        width: 50%
    }

    .subscriptionInfo_infoDesc__rZsHK:before {
        content: "";
        position: absolute;
        top: 25%;
        left: 0;
        width: 1px;
        height: 50%;
        border-left: 1px solid #ececec
    }
}

.subscriptionList_infoItem__IwUZc {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0 0 20px;
    color: #111316
}

.subscriptionList_infoItem__IwUZc:last-child {
    padding-bottom: 0
}

.subscriptionList_infoItem__IwUZc div:first-child {
    width: 46px !important;
    position: static !important;
    margin-right: 1rem !important
}

.subscriptionList_infoItem__IwUZc div:last-child {
    width: calc(100% - 46px - 16px)
}

.table_tableContent__Owdt8 {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-direction: column
}

@charset "UTF-8";
.imgDesktop {
    display: none !important
}

.imgMobile {
    display: block !important
}

@media (min-width: 512px) {
    .imgDesktop {
        display: block !important
    }

    .imgMobile {
        display: none !important
    }
}

.swiper-pagination {
    height: 66px;
    bottom: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center
}

.swiper-pagination-bullet {
    border: 1px solid #6f767e !important;
    background-color: transparent !important;
    opacity: 1 !important
}

.swiper-pagination-bullet-active {
    background-color: #000 !important
}

.swiper-button-disabled {
    display: none !important
}

.swiper-button-next, .swiper-button-prev {
    width: 40px !important;
    height: 40px !important;
    border: 2px solid #ececec;
    border-radius: 20px;
    background-color: #fff;
    margin: 0 5px
}

.swiper-button-prev {
    left: 0 !important
}

.swiper-button-next {
    right: 0 !important
}

.swiper-button-next:after, .swiper-button-prev:after {
    background-image: url(/arrow.svg);
    background-repeat: no-repeat;
    background-position: 50%;
    content: "" !important;
    width: 40px;
    height: 40px
}

.swiper-button-prev:after {
    rotate: 180deg
}

.swiper-slide-thumb-active button {
    border: 1px solid teal
}

.banners .swiper-wrapper .swiper-slide {
    margin-right: 0
}

.product-page-constraint .swiper-slide {
    height: auto
}

.product-page-constraint .swiper-slide > div {
    height: 100%
}

.product-page-constraint .swiper {
    padding-bottom: 66px
}

.checkbox-container {
    display: block;
    position: relative;
    padding-left: 30px;
    margin-bottom: 16px;
    cursor: pointer;
    font-size: 16px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    z-index: 3
}

.checkbox-container:last-child {
    margin-bottom: 0
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 18px;
    width: 18px;
    border: 1px solid #6f767e;
    border-radius: 4px
}

.checkbox-container:hover input ~ .checkmark, .checkmark {
    background-color: #fff
}

.checkbox-container input:checked ~ .checkmark {
    background-color: teal
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block
}

.checkbox-container .checkmark:after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid #fff;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg)
}

.step {
    width: 20%
}

.step:last-child button:after {
    display: none
}

.step button {
    position: relative
}

.step button:after {
    content: "";
    position: absolute;
    top: 14px;
    left: calc(50% + 14px);
    margin-left: 15%;
    width: 60%;
    height: 1px;
    background-color: #e5f2f2
}

.step button.visited:after {
    background-color: teal
}

.step button span.number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #e5f2f2;
    margin-bottom: 10px
}

.step button.active span.number {
    background-color: teal;
    color: #fff
}

.step button.active span.title {
    color: #111316
}

.step button.visited span.number {
    position: relative
}

.step button.visited span.number:after {
    position: absolute;
    top: 0;
    left: 0;
    content: "";
    border: 5px solid teal;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: url(/tick.svg) no-repeat 50% #fff
}

@media (min-width: 768px) {
    .step {
        width: auto
    }

    .step button:after {
        left: 100%;
        margin-left: 13px;
        width: 30px
    }

    .step button span.number {
        margin: 0
    }

    .step button span.title {
        padding-left: 8px
    }
}

.react-datepicker-wrapper {
    position: relative;
    width: 100%
}

.react-datepicker-wrapper:after {
    position: absolute;
    top: 13px;
    right: 16px;
    content: "";
    width: 20px;
    height: 20px;
    pointer-events: none
}

.datePicker .react-datepicker-wrapper:after {
    background: url(/calendar.svg) no-repeat
}

.timePicker .react-datepicker-wrapper:after {
    background: url(/clock.svg) no-repeat
}

.react-datepicker__input-container input {
    width: 100%;
    height: 46px;
    border-radius: 12px;
    padding: 0 16px
}

.datePicker .react-datepicker__triangle, .timePicker .react-datepicker__triangle {
    left: -20px !important
}

.pagination form {
    display: none
}

.pagination ul {
    margin: 0 auto
}

.pagination li {
    display: block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    margin: 0 5px
}

.pagination li:first-child a, .pagination li:last-child a {
    position: relative;
    border-radius: 50% !important;
    border: 1px solid #ececec
}

.pagination li:first-child a[aria-label="No previous page available"], .pagination li:last-child a[aria-label="No next page available"] {
    opacity: .5
}

.pagination li:first-child a:after, .pagination li:last-child a:after {
    position: absolute;
    top: 0;
    left: 0;
    background-image: url(/arrow.svg);
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 16px auto;
    content: "" !important;
    width: 40px;
    height: 40px
}

.pagination li:first-child a:after {
    rotate: 180deg;
    top: -1px
}

.pagination li:first-child svg, .pagination li:last-child svg {
    display: none
}

.pagination li a {
    outline: none;
    color: #323232;
    padding: 0;
    min-width: auto;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%
}

.pagination a[aria-current=true] {
    background-color: var(--color-primary);
    color: #fff
}

@media (min-width: 1024px) {
    .pagination ul {
        margin: initial
    }
}

.eventRegistrationPage main, .faqsPage main, .howItWorksPage main, .redemptionPage main, .storyblockBanner .bannerContent, .subscriptionPage main, .theBladePage main {
    display: flex;
    flex-direction: column
}

.storyblockBanner .bannerContent {
    justify-content: center;
    pointer-events: none
}

.RedemptionTermsAndConditionsPage .storyblockTeaser, .beerCiderPage .storyblockTeaser, .eventRegistrationPage .storyblockTeaser, .redemptionPage .storyblockTeaser, .subscriptionPage .storyblockTeaser {
    padding-top: 40px
}

.redemptionPage .storyblockTeaser {
    background-color: #f2f5f5
}

.RedemptionTermsAndConditionsPage .storyblockTeaser, .eventRegistrationPage .storyblockTeaser {
    max-width: 800px;
    margin: 0 auto
}

@media (min-width: 768px) {
    .eventRegistrationPage .storyblockTeaser, .redemptionPage .storyblockTeaser {
        padding-top: 60px
    }
}

.redemptionPage .content-inner {
    max-width: 992px;
    margin: 0 auto;
    padding-bottom: 1rem
}

.redemptionPage .redemptionForm h2 {
    text-align: center;
    font-size: 24px
}

@media (min-width: 768px) {
    .redemptionPage .redemptionForm h2 {
        font-size: 32px
    }
}

.RedemptionTermsAndConditionsPage .richtextContent, .TermsAndConditionsPage .richtextContent {
    padding-top: 40px
}

.faq-container .faq-tabs li.active {
    background-color: #000;
    color: #fff
}

.faq-container ul.faq-content {
    width: 100%;
    max-width: 842px;
    margin: auto
}

.faq-container ul.faq-content > li {
    display: none
}

.faq-container ul.faq-content > li.active {
    display: block
}

.faq-container ul.faq-content > li ul.faq-group li {
    border-bottom: 1px solid #ececec
}

.faq-container ul.faq-content > li ul.faq-group li .faq-answer, .faq-container ul.faq-content > li ul.faq-group li .faq-question .minus {
    display: none
}

.faq-container ul.faq-content > li ul.faq-group li .faq-answer * {
    margin-bottom: 16px
}

.faq-container ul.faq-content > li ul.faq-group li.active .faq-question .minus {
    display: block
}

.faq-container ul.faq-content > li ul.faq-group li.active .faq-question .plus {
    display: none
}

.faq-container ul.faq-content > li ul.faq-group li.active .faq-answer {
    display: block
}

.howItWorksPage .howItWorksContainer .bladeTabs {
    width: 100%;
    max-width: 1062px;
    margin: auto;
    overflow: hidden
}

.howItWorksPage .howItWorksContainer .bladeTabs ul {
    flex-direction: row;
    flex-wrap: wrap;
    margin-left: -8px;
    margin-right: -8px
}

.howItWorksPage .howItWorksManualLinks .richtextContent > div {
    margin: 0;
    padding: 24px 0 0
}

.howItWorksPage .howItWorksManualLinks .richtextContent > div h3 {
    margin-bottom: 16px
}

.howItWorksPage .howItWorksManualLinks .richtextContent > div a {
    text-decoration: underline
}

@media (min-width: 640px) {
    .howItWorksPage .storyblockBanner .bannerContent {
        align-items: center;
        text-align: center
    }

    .howItWorksPage .howItWorksStepsItem {
        position: relative
    }

    .howItWorksPage .howItWorksStepsItem:after {
        position: absolute;
        bottom: -90px;
        left: 22px;
        content: "";
        width: calc(50% + 20px);
        height: 180px;
        background-image: url(/line.png);
        background-size: 100% auto;
        background-position: 50%;
        background-repeat: no-repeat
    }

    .howItWorksPage .howItWorksStepsItem:nth-child(2n):after {
        background-image: url(/line-revert.png)
    }

    .howItWorksPage .howItWorksStepsItem:last-child:after {
        display: none
    }

    .howItWorksPage .howItWorksManualLinks .richtextContent {
        width: 100%
    }
}

@media (min-width: 1024px) {
    .howItWorksPage .howItWorksStepsItem:after {
        width: calc(50% + 56px)
    }

    .howItWorksPage .howItWorksManualLinks .richtextContent {
        width: 50%
    }
}

.howToRedeem .BladeProduct + .BladeProduct > div, .theBladePage .BladeProduct + .BladeProduct > div {
    flex-direction: row-reverse
}

.howToRedeem .BladeContent ul li, .theBladePage .BladeContent ul li {
    width: 100%
}

@media (min-width: 768px) {
    .howToRedeem .BladeContent ul li, .theBladePage .BladeContent ul li {
        width: calc(33.33% - 2rem)
    }

    .howToRedeem .BladeContent + .BladeContent ul li, .theBladePage .BladeContent + .BladeContent ul li {
        width: calc(25% - 2rem)
    }
}

.HowToOrderItem + .HowToOrderItem {
    padding-top: 0
}

.HowToOrderItem:first-child {
    background-color: #79ddc4
}

.HowToOrderItem:first-child * {
    color: #fff !important
}

.HowToOrderItem:nth-child(2) {
    padding-top: 2rem
}

.HowToOrderItem ul li {
    list-style: disc !important
}

.HowToOrderItem .description {
    width: auto
}

.voucher-header {
    background: url(/voucher/thumbnail_top.png) no-repeat 50%;
    background-size: 100% 100%;
    height: 21px
}

.voucher-body {
    background: url(/voucher/thumbnail_line.png) repeat-y 50%;
    background-size: 100% 100%
}

.voucher-body .duration {
    top: 190px;
    transform: rotate(-90deg);
    width: 350px;
    right: -153px;
    text-align: right;
    font-size: 12px
}

.voucher-cornor {
    background: url(/voucher/thumbnail_cornor.png) no-repeat 50%;
    background-size: 100% 100%;
    height: 54px
}

.voucher-footer-text {
    background: url(/voucher/thumbnail_line.png) repeat-y 50%;
    background-size: 100% 100%
}

.voucher-footer {
    background: url(/voucher/thumbnail_bottom.png) no-repeat 50%;
    background-size: 100% 100%;
    height: 27px
}

.voucher-swiper .swiper-slide {
    height: auto
}

.voucher-swiper .swiper-button-next, .voucher-swiper .swiper-button-prev {
    border: 0
}

.voucher-swiper .swiper-button-next:after, .voucher-swiper .swiper-button-prev:after {
    background-image: url(/arrow-short.svg);
    background-repeat: no-repeat;
    background-position: 50%;
    content: "" !important;
    width: 40px;
    height: 40px
}

@media (max-width: 600px) {
    .voucher-header {
        background: url(/voucher/thumbnail_top_small.png) no-repeat 50%;
        background-size: 100% 100%;
        height: 18px
    }

    .voucher-body {
        background: url(/voucher/thumbnail_line_small.png) repeat-y 50%;
        background-size: 100% 100%
    }

    .voucher-cornor {
        background: url(/voucher/thumbnail_cornor_small.png) no-repeat 50%;
        background-size: 100% 100%;
        height: 38px
    }

    .voucher-footer-text {
        background: url(/voucher/thumbnail_line_small.png) repeat-y 50%;
        background-size: 100% 100%
    }

    .voucher-footer {
        background: url(/voucher/thumbnail_bottom_small.png) no-repeat 50%;
        background-size: 100% 100%;
        height: 18px
    }
}

.outlet-dropdown-mobile .dropdown-heading {
    display: none !important
}

.outlet-dropdown-mobile .dropdown-content {
    position: relative !important
}

.outlet-dropdown-mobile .dropdown-content .panel-content {
    box-shadow: none !important
}

.outlet-dropdown-mobile .dropdown-content .select-item {
    border: none !important;
    padding-left: 10px !important
}

.outlet-dropdown-mobile .dropdown-content .options {
    max-height: none !important
}

.outlet-dropdown-mobile .dropdown-content .selected {
    background-color: transparent !important
}

.rmsc .dropdown-container {
    border: 0 !important;
    box-shadow: none !important
}

.rmsc .dropdown-heading {
    height: auto !important;
    outline: 0 !important
}

.rmsc .dropdown-content {
    width: 100%
}

.rmsc .dropdown-content .select-item {
    border: 1px solid;
    border-color: var(--color-secondary-light-300);
    accent-color: var(--color-primary);
    padding-left: 15px;
    font-size: 16px
}

.rmsc .dropdown-content .select-item input {
    margin-right: 15px;
    transform: scale(1.2)
}

.country-flag input {
    background-repeat: no-repeat !important;
    background-size: 22px 22px !important;
    background-position: 10px 10px !important
}

.country-flag.SG-flag input {
    background-image: url(/flags/SG-flag.svg)
}

.country-flag.MY-flag input {
    background-image: url(/flags/MY-flag.svg)
}

.mobileApp .storyblockTeaser h1 {
    font-size: 1.5rem;
    color: #79ddc4
}

.mobileApp .storyblockTeaser .content-inner {
    padding-bottom: 1rem
}

.mobileApp .richtextContent ul li {
    list-style: none
}

html[data-theme=theme-with-banner] {
    --banner-content-pos-x: calc((100% - 1110px) / 2)
}

.theme-with-banner .side-banner {
    display: none
}

@media (min-width: 768px) {
    .theme-with-banner .side-banner {
        align-items: start;
        display: block;
        height: 100vh;
        max-width: 100%;
        overflow-x: hidden;
        position: fixed;
        top: 0;
        z-index: -1
    }

    .theme-with-banner .left-banner {
        left: 0
    }

    .theme-with-banner .right-banner {
        right: 0
    }

    .theme-with-banner .main-body {
        width: calc(100% - 152px - 152px);
        margin: 0 auto
    }

    .theme-with-banner .main-body main {
        background-color: #fff;
        z-index: 1
    }

    .theme-with-banner footer {
        z-index: 1
    }
}

@media (min-width: 1680px) {
    .theme-with-banner .main-body {
        width: calc(100% - 265px - 265px)
    }
}

/*
    ! tailwindcss v3.4.6 | MIT License | https://tailwindcss.com
    */
*, :after, :before {
    box-sizing: border-box;
    border: 0 solid #e5e7eb
}

:after, :before {
    --tw-content: ""
}

:host, html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Ubuntu, sans-serif;
    font-feature-settings: normal;
    font-variation-settings: normal;
    -webkit-tap-highlight-color: transparent
}

body {
    margin: 0;
    line-height: inherit
}

hr {
    height: 0;
    color: inherit;
    border-top-width: 1px
}

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b, strong {
    font-weight: bolder
}

code, kbd, pre, samp {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
    font-feature-settings: normal;
    font-variation-settings: normal;
    font-size: 1em
}

small {
    font-size: 80%
}

sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse
}

button, input, optgroup, select, textarea {
    font-family: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    color: inherit;
    margin: 0;
    padding: 0
}

button, select {
    text-transform: none
}

button, input:where([type=button]), input:where([type=reset]), input:where([type=submit]) {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote, dd, dl, figure, h1, h2, h3, h4, h5, h6, hr, p, pre {
    margin: 0
}

fieldset {
    margin: 0
}

fieldset, legend {
    padding: 0
}

menu, ol, ul {
    list-style: none;
    margin: 0;
    padding: 0
}

dialog {
    padding: 0
}

textarea {
    resize: vertical
}

input::-moz-placeholder, textarea::-moz-placeholder {
    opacity: 1;
    color: #9ca3af
}

input::placeholder, textarea::placeholder {
    opacity: 1;
    color: #9ca3af
}

[role=button], button {
    cursor: pointer
}

:disabled {
    cursor: default
}

audio, canvas, embed, iframe, img, object, svg, video {
    display: block;
    vertical-align: middle
}

img, video {
    max-width: 100%;
    height: auto
}

[hidden] {
    display: none
}

html {
    font-family: Helvetica, Arial, sans-serif;
    --color-primary: teal;
    --color-primary-100: #dcfce7;
    --color-primary-200: #bbf7d0;
    --color-primary-500: #22c55e;
    --color-primary-light: #4be0c3;
    --color-primary-light-100: #ddfff8;
    --color-primary-light-300: #b7e5db;
    --color-secondary: #6f767e;
    --color-secondary-100: #f3f4f6;
    --color-secondary-200: #e5e7eb;
    --color-secondary-400: #9ca3af;
    --color-secondary-500: #979797;
    --color-secondary-700: #374151;
    --color-secondary-700-75: rgba(55, 65, 81, .75);
    --color-secondary-900: #111827;
    --color-secondary-light: #e5f2f2;
    --color-secondary-light-300: #efefef;
    --color-secondary-light-400: #f2f5f5;
    --color-tertiary: #111316;
    --color-tertiary-300: #323232;
    --color-danger: #e82627;
    --color-danger-100: #fee2e2;
    --color-danger-200: #fecaca;
    --color-danger-500: red;
    --color-danger-600: #dc2626;
    --color-danger-700: #eb5757;
    --color-info: #60a5fa;
    --color-warning: #ff8f00;
    --banner-content-pos-x: calc((100% - 1280px) / 2)
}

body {
    color: #111316
}

.font-hnk {
    font-family: Heineken, Helvetica, Arial
}

a:focus, button:focus {
    outline: none
}

input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

input[type=number] {
    -moz-appearance: textfield
}

input:disabled {
    background: #f9f9f9
}

*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

.\!container {
    width: 100% !important
}

.container {
    width: 100%
}

@media (min-width: 320px) {
    .\!container {
        max-width: 320px !important
    }

    .container {
        max-width: 320px
    }
}

@media (min-width: 512px) {
    .\!container {
        max-width: 512px !important
    }

    .container {
        max-width: 512px
    }
}

@media (min-width: 640px) {
    .\!container {
        max-width: 640px !important
    }

    .container {
        max-width: 640px
    }
}

@media (min-width: 768px) {
    .\!container {
        max-width: 768px !important
    }

    .container {
        max-width: 768px
    }
}

@media (min-width: 1024px) {
    .\!container {
        max-width: 1024px !important
    }

    .container {
        max-width: 1024px
    }
}

@media (min-width: 1280px) {
    .\!container {
        max-width: 1280px !important
    }

    .container {
        max-width: 1280px
    }
}

@media (min-width: 1440px) {
    .\!container {
        max-width: 1440px !important
    }

    .container {
        max-width: 1440px
    }
}

@media (min-width: 1536px) {
    .\!container {
        max-width: 1536px !important
    }

    .container {
        max-width: 1536px
    }
}

@media (min-width: 1680px) {
    .\!container {
        max-width: 1680px !important
    }

    .container {
        max-width: 1680px
    }
}

@media (min-width: 1920px) {
    .\!container {
        max-width: 1920px !important
    }

    .container {
        max-width: 1920px
    }
}

.content-container {
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    max-width: 1440px;
    padding-left: 2rem;
    padding-right: 2rem
}

.text-xsmall-regular {
    font-size: 10px;
    font-weight: 400;
    line-height: 1rem
}

.text-small-regular {
    font-size: .75rem;
    font-weight: 400;
    line-height: 1.25rem
}

.text-small-semi {
    font-size: .75rem;
    font-weight: 600;
    line-height: 1.25rem
}

.text-base-regular {
    font-weight: 400
}

.text-base-regular, .text-base-semi {
    font-size: .875rem;
    line-height: 1.5rem
}

.text-base-semi {
    font-weight: 600
}

.text-large-regular {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5rem
}

.text-large-semi {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.5rem
}

.text-xl-regular {
    font-size: 1.5rem;
    line-height: 2rem;
    font-weight: 400;
    line-height: 36px
}

.text-2xl-regular {
    font-size: 30px;
    font-weight: 400;
    line-height: 48px
}

.text-2xl-semi {
    font-size: 30px;
    font-weight: 600;
    line-height: 48px
}

.distributor-layout {
    font-family: Inter, sans-serif
}

.distributor-layout .bg-gradient-primary {
    background: var(
            --background, linear-gradient(135deg, #f9fafe 0, #faf6ff 33.67%, #f9fafe 100%)
    )
}

.oms-custom-scroll ::-webkit-scrollbar {
    width: 6px
}

.oms-custom-scroll ::-webkit-scrollbar-thumb {
    background: #c7c7c7;
    border-radius: 20px
}

.oms-custom-scroll ::-webkit-scrollbar-thumb:hover {
    background: #757575
}

.oms-custom-scroll ::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 20px
}

.oms-custom-scroll--thin ::-webkit-scrollbar {
    width: 4px
}

.btn {
    border-radius: 10px;
    background-image: linear-gradient(135deg, rgba(0, 130, 0, .7), #008200);
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    font-weight: 700;
    letter-spacing: .025em;
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.btn-secondary {
    border-width: 1px;
    border-color: rgb(0 130 0/var(--tw-border-opacity));
    color: rgb(0 130 0/var(--tw-text-opacity))
}

.btn-danger, .btn-secondary {
    --tw-border-opacity: 1;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity));
    background-image: none;
    --tw-text-opacity: 1
}

.btn-danger {
    border-width: 1px;
    border-color: rgb(236 28 36/var(--tw-border-opacity));
    color: rgb(236 28 36/var(--tw-text-opacity))
}

.btn-small {
    height: 2rem;
    min-width: 86px
}

.btn-medium {
    height: 2.125rem;
    min-width: 10.625rem
}

.btn-large {
    height: 2.5rem;
    min-width: 10.625rem
}

.is-promo {
    border-radius: .375rem;
    --tw-bg-opacity: 1;
    background-color: rgb(179 49 49/var(--tw-bg-opacity));
    padding: .25rem .5rem;
    font-size: .75rem;
    line-height: 1rem;
    font-weight: 700;
    letter-spacing: .05em;
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.oms-input {
    border-radius: 10px;
    border: 1px solid #eaeaea;
    background: var(--white, #fff);
    max-height: 38px
}

.oms-checkbox {
    display: block;
    position: relative;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.oms-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer
}

.oms-checkbox .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 24px;
    width: 24px;
    background-color: #f7f7f7;
    border-radius: 50%
}

.oms-checkbox:hover input ~ .checkmark {
    background-color: #ccc
}

.oms-checkbox input:checked ~ .checkmark {
    background-color: #008200
}

.oms-checkbox .checkmark:after {
    content: "";
    position: absolute;
    display: none;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #fff;
    transform: translate(-50%, -50%)
}

.oms-checkbox input:checked ~ .checkmark:after {
    display: block
}

.date-range-picker {
    font-family: Inter, sans-serif !important;
    height: 436px;
    width: 378px;
    border: none !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .2), 0 4px 8px 3px rgba(0, 0, 0, .15);
    color: #3a3d46 !important;
    padding: 12px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    border-radius: 20px !important;
    font-weight: 400;
    animation: picker-appear 75ms linear
}

@keyframes picker-appear {
    0% {
        opacity: 0;
        transform: scale(95%)
    }
    to {
        opacity: 1;
        transform: scale(100%)
    }
}

.date-range-picker .react-datepicker__triangle {
    display: none !important
}

.date-range-picker .react-datepicker__month-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column
}

.date-range-picker .react-datepicker__header {
    text-align: center;
    background-color: #fff !important;
    border-bottom: none !important;
    padding: 0 !important;
    position: relative
}

.date-range-picker .react-datepicker__month {
    display: flex;
    text-align: center;
    flex-direction: column;
    align-items: center;
    gap: 4px
}

.date-range-picker .react-datepicker__day--outside-month {
    visibility: hidden
}

.date-range-picker .react-datepicker__week {
    display: flex;
    width: 100%;
    justify-content: space-between
}

.date-range-picker .react-datepicker__day-names {
    width: 100%;
    display: flex;
    justify-content: center
}

.date-range-picker .react-datepicker__day-name {
    font-weight: 400;
    font-size: 16px;
    height: 48px;
    width: 3.6rem !important;
    display: flex !important;
    justify-content: space-evenly !important;
    align-items: center !important;
    margin: 0 !important;
    color: #3a3d46 !important
}

.date-range-picker .react-datepicker__day {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    font-size: 16px;
    margin: 0 !important;
    height: 40px;
    width: 40px;
    color: #3a3d46
}

.date-range-picker .react-datepicker__day:hover {
    border-radius: 50% !important;
    background-color: #f1fcf1;
    color: #000
}

.date-range-picker .react-datepicker__day--disabled {
    cursor: not-allowed;
    opacity: .5
}

.date-range-picker .react-datepicker__day--disabled:hover {
    background-color: transparent;
    --tw-text-opacity: 1;
    color: rgb(58 61 70/var(--tw-text-opacity))
}

.date-range-picker .react-datepicker__day--today span {
    -webkit-text-decoration: underline 3px #008200;
    text-decoration: underline 3px #008200;
    font-weight: 700 !important
}

.date-range-picker .react-datepicker__day--selected {
    border-radius: 50% !important;
    background-color: #008700 !important;
    color: #fff !important;
    position: relative
}

.date-range-picker .react-datepicker__day--in-range {
    border-radius: 0 !important;
    background-color: #f1fcf1 !important;
    color: #3a3d46 !important;
    position: relative;
    border: 1px
}

.date-range-picker .react-datepicker__day--in-range:before {
    position: absolute;
    content: "";
    top: 0;
    left: 6px;
    background-color: #f1fcf1;
    border: 1px;
    width: 100%;
    height: 100%
}

.date-range-picker .react-datepicker__day--in-range:after {
    position: absolute;
    content: "";
    top: 0;
    right: 6px;
    background-color: #f1fcf1;
    border: 1px;
    width: 100%;
    height: 100%
}

.date-range-picker .react-datepicker__day--in-range span {
    position: absolute;
    z-index: 5;
    color: #3a3d46 !important
}

.date-range-picker .react-datepicker__day--in-selecting-range {
    border-radius: 50%;
    background-color: unset !important;
    position: relative;
    border: 1px
}

.date-range-picker .react-datepicker__day--in-selecting-range span {
    position: absolute;
    z-index: 100;
    color: #000
}

.date-range-picker .react-datepicker__day--in-selecting-range:before {
    position: absolute;
    content: "";
    width: 26px;
    top: 0;
    left: 50%;
    height: 100%;
    background-color: #f1fcf1
}

.date-range-picker .react-datepicker__day--in-selecting-range:after {
    position: absolute;
    content: "";
    width: 26px;
    top: 0;
    right: 50%;
    height: 100%;
    background-color: #f1fcf1
}

.date-range-picker .react-datepicker__day--selecting-range-end, .date-range-picker .react-datepicker__day--selecting-range-start {
    background-color: #008700 !important;
    color: #fff !important;
    display: flex;
    align-items: center;
    border-radius: 50% !important;
    position: relative;
    font-weight: 700
}

.date-range-picker .react-datepicker__day--selecting-range-end:before, .date-range-picker .react-datepicker__day--selecting-range-start:before {
    position: absolute;
    z-index: 22;
    width: 100%;
    height: 100%;
    background-color: #008200;
    content: "";
    top: 0;
    left: 0;
    border-radius: 50%
}

.date-range-picker .react-datepicker__day--selecting-range-end span, .date-range-picker .react-datepicker__day--selecting-range-start span {
    position: absolute;
    z-index: 22;
    color: #fff;
    font-weight: 700 !important
}

.date-range-picker .react-datepicker__day--range-end, .date-range-picker .react-datepicker__day--range-start {
    background-color: #008700 !important
}

.date-range-picker .react-datepicker__day--range-end:hover, .date-range-picker .react-datepicker__day--range-start:hover {
    background-color: #008700 !important;
    color: #fff !important
}

.date-range-picker .react-datepicker__day--in-range:hover, .date-range-picker .react-datepicker__day--in-selecting-range:hover, .date-range-picker .react-datepicker__day--selected:hover {
    background-color: #008700;
    color: #fff;
    border: none !important
}

.date-range-picker .react-datepicker__day--keyboard-selected {
    border-radius: 50% !important;
    background-color: #008700 !important;
    color: #fff !important
}

.date-range-picker .react-datepicker__day--range-end, .date-range-picker .react-datepicker__day--range-start {
    border-radius: 50% !important;
    background-color: #008200 !important;
    color: #fff !important;
    position: relative
}

.date-range-picker .react-datepicker__day--range-end:before, .date-range-picker .react-datepicker__day--range-start:before {
    border-radius: 50% !important;
    width: 100%;
    content: "";
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1 !important;
    background-color: #008200 !important
}

.date-range-picker .react-datepicker__day--range-start:after {
    width: 26px;
    content: "";
    height: 100%;
    position: absolute;
    left: 50%;
    top: 0;
    z-index: 0 !important;
    background-color: #f1fcf1 !important
}

.date-range-picker .react-datepicker__day--range-end:after {
    width: 26px;
    content: "";
    height: 100%;
    position: absolute;
    right: 50%;
    top: 0;
    z-index: 0 !important;
    background-color: #f1fcf1 !important
}

.date-range-picker .react-datepicker__day--range-end span, .date-range-picker .react-datepicker__day--range-start span {
    position: absolute;
    z-index: 5;
    color: #fff !important;
    font-weight: 700 !important
}

.date-range-picker .react-datepicker__children-container {
    position: absolute;
    bottom: 12px;
    right: 12px;
    left: 12px;
    width: calc(100% - 24px) !important;
    padding: 0 !important;
    margin: 0 !important
}

.time-list::-webkit-scrollbar {
    display: none
}

.time-list {
    -ms-overflow-style: none;
    scrollbar-width: none
}

.photo-stack {
    position: relative
}

.photo-stack .image-stacked {
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid #fff;
    box-shadow: 0 1px 1px 1px rgba(0, 0, 0, .2);
    transition: all .3s ease-out;
    width: auto;
    max-width: 110px;
    height: 120px;
    background-color: #fff;
    border-radius: 8px
}

.photo-stack .image-stacked:first-child {
    z-index: 999
}

.photo-stack .image-stacked:nth-child(2) {
    transform: rotate(3deg)
}

.photo-stack .image-stacked:nth-child(3) {
    transform: rotate(-3deg)
}

.photo-stack .image-stacked:nth-child(4) {
    transform: rotate(2deg)
}

.photo-stack:hover .image-stacked:first-child {
    transform: scale(1.02)
}

.photo-stack:hover .image-stacked:nth-child(2) {
    transform: translate3d(10%, 0, 0) rotate(3deg)
}

.photo-stack:hover .image-stacked:nth-child(3) {
    transform: translate3d(-10%, 0, 0) rotate(-3deg)
}

.photo-stack:hover .image-stacked:nth-child(4) {
    transform: translate3d(2%, -5%, 0) rotate(2deg)
}

.photo-stack:hover .image-stacked:nth-child(5) {
    transform: translate3d(-5%, -2%, 0) rotate(2deg)
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0
}

.pointer-events-none {
    pointer-events: none
}

.pointer-events-auto {
    pointer-events: auto
}

.visible {
    visibility: visible
}

.invisible {
    visibility: hidden
}

.static {
    position: static
}

.fixed {
    position: fixed
}

.absolute {
    position: absolute
}

.relative {
    position: relative
}

.sticky {
    position: sticky
}

.-inset-2\.5 {
    inset: -.625rem
}

.inset-0 {
    inset: 0
}

.inset-x-0 {
    left: 0;
    right: 0
}

.inset-y-0 {
    top: 0;
    bottom: 0
}

.\!left-4 {
    left: 1rem !important
}

.-bottom-\[calc\(100\%-36px\)\] {
    bottom: calc(calc(100% - 36px) * -1)
}

.-left-1\.5 {
    left: -.375rem
}

.-top-1\.5 {
    top: -.375rem
}

.-top-2 {
    top: -.5rem
}

.bottom-0 {
    bottom: 0
}

.bottom-10 {
    bottom: 2.5rem
}

.bottom-14 {
    bottom: 3.5rem
}

.bottom-2\.5 {
    bottom: .625rem
}

.bottom-3 {
    bottom: .75rem
}

.bottom-5 {
    bottom: 1.25rem
}

.bottom-6 {
    bottom: 1.5rem
}

.bottom-8 {
    bottom: 2rem
}

.bottom-\[-10px\] {
    bottom: -10px
}

.bottom-\[-12px\] {
    bottom: -12px
}

.bottom-\[-25px\] {
    bottom: -25px
}

.bottom-\[1\.125rem\] {
    bottom: 1.125rem
}

.bottom-\[40px\] {
    bottom: 40px
}

.left-0 {
    left: 0
}

.left-1\/2 {
    left: 50%
}

.left-12 {
    left: 3rem
}

.left-2 {
    left: .5rem
}

.left-4 {
    left: 1rem
}

.left-\[-24px\] {
    left: -24px
}

.left-\[-9999px\] {
    left: -9999px
}

.left-\[16\.5px\] {
    left: 16.5px
}

.left-\[20\%\] {
    left: 20%
}

.left-\[7px\] {
    left: 7px
}

.right-0 {
    right: 0
}

.right-1 {
    right: .25rem
}

.right-10 {
    right: 2.5rem
}

.right-2 {
    right: .5rem
}

.right-4 {
    right: 1rem
}

.right-5 {
    right: 1.25rem
}

.right-6 {
    right: 1.5rem
}

.right-\[-20px\] {
    right: -20px
}

.right-\[-50px\] {
    right: -50px
}

.right-\[0px\] {
    right: 0
}

.right-\[6px\] {
    right: 6px
}

.top-0 {
    top: 0
}

.top-1 {
    top: .25rem
}

.top-1\/2 {
    top: 50%
}

.top-10 {
    top: 2.5rem
}

.top-2 {
    top: .5rem
}

.top-20 {
    top: 5rem
}

.top-4 {
    top: 1rem
}

.top-5 {
    top: 1.25rem
}

.top-\[-100px\] {
    top: -100px
}

.top-\[-26px\] {
    top: -26px
}

.top-\[-30px\] {
    top: -30px
}

.top-\[-50px\] {
    top: -50px
}

.top-\[-8px\] {
    top: -8px
}

.top-\[100\%\] {
    top: 100%
}

.top-\[22px\] {
    top: 22px
}

.top-\[24px\] {
    top: 24px
}

.top-\[30px\] {
    top: 30px
}

.top-\[36px\] {
    top: 36px
}

.top-\[56px\] {
    top: 56px
}

.top-\[7px\] {
    top: 7px
}

.top-\[87\%\] {
    top: 87%
}

.top-\[calc\(100\%\+15px\)\] {
    top: calc(100% + 15px)
}

.z-0 {
    z-index: 0
}

.z-10 {
    z-index: 10
}

.z-20 {
    z-index: 20
}

.z-30 {
    z-index: 30
}

.z-40 {
    z-index: 40
}

.z-50 {
    z-index: 50
}

.z-\[-1\] {
    z-index: -1
}

.z-\[100\] {
    z-index: 100
}

.z-\[1\] {
    z-index: 1
}

.z-\[75\] {
    z-index: 75
}

.z-\[900\] {
    z-index: 900
}

.z-\[9999999\] {
    z-index: 9999999
}

.z-\[999999\] {
    z-index: 999999
}

.order-1 {
    order: 1
}

.order-2 {
    order: 2
}

.order-\[-1\] {
    order: -1
}

.order-first {
    order: -9999
}

.\!col-span-12 {
    grid-column: span 12/span 12 !important
}

.\!col-span-3 {
    grid-column: span 3/span 3 !important
}

.col-span-1 {
    grid-column: span 1/span 1
}

.col-span-10 {
    grid-column: span 10/span 10
}

.col-span-12 {
    grid-column: span 12/span 12
}

.col-span-2 {
    grid-column: span 2/span 2
}

.col-span-3 {
    grid-column: span 3/span 3
}

.col-span-4 {
    grid-column: span 4/span 4
}

.col-span-5 {
    grid-column: span 5/span 5
}

.col-span-6 {
    grid-column: span 6/span 6
}

.col-span-7 {
    grid-column: span 7/span 7
}

.col-span-8 {
    grid-column: span 8/span 8
}

.col-span-9 {
    grid-column: span 9/span 9
}

.row-span-2 {
    grid-row: span 2/span 2
}

.float-right {
    float: right
}

.m-0\.5 {
    margin: .125rem
}

.m-2 {
    margin: .5rem
}

.m-3 {
    margin: .75rem
}

.m-4 {
    margin: 1rem
}

.m-6 {
    margin: 1.5rem
}

.m-auto {
    margin: auto
}

.-mx-4 {
    margin-left: -1rem;
    margin-right: -1rem
}

.-mx-8 {
    margin-left: -2rem;
    margin-right: -2rem
}

.-my-2 {
    margin-top: -.5rem;
    margin-bottom: -.5rem
}

.mx-0 {
    margin-left: 0;
    margin-right: 0
}

.mx-1 {
    margin-left: .25rem;
    margin-right: .25rem
}

.mx-1\.5 {
    margin-left: .375rem;
    margin-right: .375rem
}

.mx-10 {
    margin-left: 2.5rem;
    margin-right: 2.5rem
}

.mx-2 {
    margin-left: .5rem;
    margin-right: .5rem
}

.mx-2\.5 {
    margin-left: .625rem;
    margin-right: .625rem
}

.mx-32 {
    margin-left: 8rem;
    margin-right: 8rem
}

.mx-4 {
    margin-left: 1rem;
    margin-right: 1rem
}

.mx-6 {
    margin-left: 1.5rem;
    margin-right: 1.5rem
}

.mx-8 {
    margin-left: 2rem;
    margin-right: 2rem
}

.mx-\[-16px\] {
    margin-left: -16px;
    margin-right: -16px
}

.mx-\[-1rem\] {
    margin-left: -1rem;
    margin-right: -1rem
}

.mx-\[-20px\] {
    margin-left: -20px;
    margin-right: -20px
}

.mx-\[30px\] {
    margin-left: 30px;
    margin-right: 30px
}

.mx-auto {
    margin-left: auto;
    margin-right: auto
}

.my-1\.5 {
    margin-top: .375rem;
    margin-bottom: .375rem
}

.my-10 {
    margin-top: 2.5rem;
    margin-bottom: 2.5rem
}

.my-12 {
    margin-top: 3rem;
    margin-bottom: 3rem
}

.my-2 {
    margin-top: .5rem;
    margin-bottom: .5rem
}

.my-2\.5 {
    margin-top: .625rem;
    margin-bottom: .625rem
}

.my-3 {
    margin-top: .75rem;
    margin-bottom: .75rem
}

.my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem
}

.my-5 {
    margin-top: 1.25rem;
    margin-bottom: 1.25rem
}

.my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem
}

.my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem
}

.my-\[10px\] {
    margin-top: 10px;
    margin-bottom: 10px
}

.my-\[15px\] {
    margin-top: 15px;
    margin-bottom: 15px
}

.my-\[25px\] {
    margin-top: 25px;
    margin-bottom: 25px
}

.my-\[5px\] {
    margin-top: 5px;
    margin-bottom: 5px
}

.my-\[7px\] {
    margin-top: 7px;
    margin-bottom: 7px
}

.\!mb-0 {
    margin-bottom: 0 !important
}

.\!mt-0 {
    margin-top: 0 !important
}

.\!mt-3 {
    margin-top: .75rem !important
}

.-mb-0\.5 {
    margin-bottom: -.125rem
}

.-mb-8 {
    margin-bottom: -2rem
}

.-mb-px {
    margin-bottom: -1px
}

.-ml-px {
    margin-left: -1px
}

.-mr-1 {
    margin-right: -.25rem
}

.-mt-3 {
    margin-top: -.75rem
}

.-mt-\[17px\] {
    margin-top: -17px
}

.mb-0 {
    margin-bottom: 0
}

.mb-1 {
    margin-bottom: .25rem
}

.mb-10 {
    margin-bottom: 2.5rem
}

.mb-12 {
    margin-bottom: 3rem
}

.mb-16 {
    margin-bottom: 4rem
}

.mb-2 {
    margin-bottom: .5rem
}

.mb-20 {
    margin-bottom: 5rem
}

.mb-3 {
    margin-bottom: .75rem
}

.mb-4 {
    margin-bottom: 1rem
}

.mb-5 {
    margin-bottom: 1.25rem
}

.mb-6 {
    margin-bottom: 1.5rem
}

.mb-8 {
    margin-bottom: 2rem
}

.mb-\[-20px\] {
    margin-bottom: -20px
}

.mb-\[10px\] {
    margin-bottom: 10px
}

.mb-\[15px\] {
    margin-bottom: 15px
}

.mb-\[16px\] {
    margin-bottom: 16px
}

.mb-\[18px\] {
    margin-bottom: 18px
}

.mb-\[20px\] {
    margin-bottom: 20px
}

.mb-\[25px\] {
    margin-bottom: 25px
}

.ml-0 {
    margin-left: 0
}

.ml-1 {
    margin-left: .25rem
}

.ml-10 {
    margin-left: 2.5rem
}

.ml-2 {
    margin-left: .5rem
}

.ml-2\.5 {
    margin-left: .625rem
}

.ml-3 {
    margin-left: .75rem
}

.ml-4 {
    margin-left: 1rem
}

.ml-5 {
    margin-left: 1.25rem
}

.ml-6 {
    margin-left: 1.5rem
}

.ml-8 {
    margin-left: 2rem
}

.ml-\[-10px\] {
    margin-left: -10px
}

.ml-\[26px\] {
    margin-left: 26px
}

.ml-\[50px\] {
    margin-left: 50px
}

.ml-auto {
    margin-left: auto
}

.mr-1 {
    margin-right: .25rem
}

.mr-10 {
    margin-right: 2.5rem
}

.mr-16 {
    margin-right: 4rem
}

.mr-2 {
    margin-right: .5rem
}

.mr-2\.5 {
    margin-right: .625rem
}

.mr-3 {
    margin-right: .75rem
}

.mr-3\.5 {
    margin-right: .875rem
}

.mr-4 {
    margin-right: 1rem
}

.mr-5 {
    margin-right: 1.25rem
}

.mr-\[-10px\] {
    margin-right: -10px
}

.mr-\[-7px\] {
    margin-right: -7px
}

.mr-\[10px\] {
    margin-right: 10px
}

.mr-\[7px\] {
    margin-right: 7px
}

.mr-auto {
    margin-right: auto
}

.mt-0 {
    margin-top: 0
}

.mt-0\.5 {
    margin-top: .125rem
}

.mt-1 {
    margin-top: .25rem
}

.mt-1\.5 {
    margin-top: .375rem
}

.mt-10 {
    margin-top: 2.5rem
}

.mt-12 {
    margin-top: 3rem
}

.mt-2 {
    margin-top: .5rem
}

.mt-3 {
    margin-top: .75rem
}

.mt-4 {
    margin-top: 1rem
}

.mt-40 {
    margin-top: 10rem
}

.mt-5 {
    margin-top: 1.25rem
}

.mt-6 {
    margin-top: 1.5rem
}

.mt-7 {
    margin-top: 1.75rem
}

.mt-8 {
    margin-top: 2rem
}

.mt-9 {
    margin-top: 2.25rem
}

.mt-\[-18px\] {
    margin-top: -18px
}

.mt-\[-6px\] {
    margin-top: -6px
}

.mt-\[1rem\] {
    margin-top: 1rem
}

.mt-\[20px\] {
    margin-top: 20px
}

.mt-\[3px\] {
    margin-top: 3px
}

.mt-\[46px\] {
    margin-top: 46px
}

.mt-\[6px\] {
    margin-top: 6px
}

.mt-\[9px\] {
    margin-top: 9px
}

.mt-auto {
    margin-top: auto
}

.box-border {
    box-sizing: border-box
}

.line-clamp-1 {
    -webkit-line-clamp: 1
}

.line-clamp-1, .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical
}

.line-clamp-2 {
    -webkit-line-clamp: 2
}

.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3
}

.\!block {
    display: block !important
}

.block {
    display: block
}

.\!inline-block {
    display: inline-block !important
}

.inline-block {
    display: inline-block
}

.inline {
    display: inline
}

.flex {
    display: flex
}

.inline-flex {
    display: inline-flex
}

.table {
    display: table
}

.flow-root {
    display: flow-root
}

.grid {
    display: grid
}

.\!hidden {
    display: none !important
}

.hidden {
    display: none
}

.aspect-\[29\/18\] {
    aspect-ratio: 29/18
}

.aspect-\[29\/34\] {
    aspect-ratio: 29/34
}

.aspect-square {
    aspect-ratio: 1/1
}

.\!size-12 {
    width: 3rem !important;
    height: 3rem !important
}

.size-10 {
    width: 2.5rem;
    height: 2.5rem
}

.size-11 {
    width: 2.75rem;
    height: 2.75rem
}

.size-12 {
    width: 3rem;
    height: 3rem
}

.size-4 {
    width: 1rem;
    height: 1rem
}

.size-5 {
    width: 1.25rem;
    height: 1.25rem
}

.size-6 {
    width: 1.5rem;
    height: 1.5rem
}

.size-full {
    width: 100%;
    height: 100%
}

.\!h-10 {
    height: 2.5rem !important
}

.\!h-11 {
    height: 2.75rem !important
}

.\!h-\[46px\] {
    height: 46px !important
}

.\!h-\[649px\] {
    height: 649px !important
}

.\!h-auto {
    height: auto !important
}

.\!h-fit {
    height: -moz-fit-content !important;
    height: fit-content !important
}

.h-0 {
    height: 0
}

.h-0\.5 {
    height: .125rem
}

.h-1 {
    height: .25rem
}

.h-1\.5 {
    height: .375rem
}

.h-10 {
    height: 2.5rem
}

.h-11 {
    height: 2.75rem
}

.h-12 {
    height: 3rem
}

.h-14 {
    height: 3.5rem
}

.h-16 {
    height: 4rem
}

.h-2 {
    height: .5rem
}

.h-2\.5 {
    height: .625rem
}

.h-20 {
    height: 5rem
}

.h-28 {
    height: 7rem
}

.h-3 {
    height: .75rem
}

.h-3\.5 {
    height: .875rem
}

.h-4 {
    height: 1rem
}

.h-5 {
    height: 1.25rem
}

.h-6 {
    height: 1.5rem
}

.h-7 {
    height: 1.75rem
}

.h-8 {
    height: 2rem
}

.h-80 {
    height: 20rem
}

.h-9 {
    height: 2.25rem
}

.h-\[100vh\] {
    height: 100vh
}

.h-\[106px\] {
    height: 106px
}

.h-\[120px\] {
    height: 120px
}

.h-\[126px\] {
    height: 126px
}

.h-\[12px\] {
    height: 12px
}

.h-\[140px\] {
    height: 140px
}

.h-\[143px\] {
    height: 143px
}

.h-\[14px\] {
    height: 14px
}

.h-\[15px\] {
    height: 15px
}

.h-\[164px\] {
    height: 164px
}

.h-\[167px\] {
    height: 167px
}

.h-\[17px\] {
    height: 17px
}

.h-\[20px\] {
    height: 20px
}

.h-\[220px\] {
    height: 220px
}

.h-\[22px\] {
    height: 22px
}

.h-\[230px\] {
    height: 230px
}

.h-\[240px\] {
    height: 240px
}

.h-\[250px\] {
    height: 250px
}

.h-\[26px\] {
    height: 26px
}

.h-\[28px\] {
    height: 28px
}

.h-\[3\.25rem\] {
    height: 3.25rem
}

.h-\[300px\] {
    height: 300px
}

.h-\[305px\] {
    height: 305px
}

.h-\[30px\] {
    height: 30px
}

.h-\[34px\] {
    height: 34px
}

.h-\[360px\] {
    height: 360px
}

.h-\[36px\] {
    height: 36px
}

.h-\[40px\] {
    height: 40px
}

.h-\[41px\] {
    height: 41px
}

.h-\[42px\] {
    height: 42px
}

.h-\[45px\] {
    height: 45px
}

.h-\[468px\] {
    height: 468px
}

.h-\[46px\] {
    height: 46px
}

.h-\[48px\] {
    height: 48px
}

.h-\[50px\] {
    height: 50px
}

.h-\[510px\] {
    height: 510px
}

.h-\[52px\] {
    height: 52px
}

.h-\[55vh\] {
    height: 55vh
}

.h-\[57px\] {
    height: 57px
}

.h-\[58px\] {
    height: 58px
}

.h-\[60px\] {
    height: 60px
}

.h-\[64px\] {
    height: 64px
}

.h-\[6px\] {
    height: 6px
}

.h-\[70px\] {
    height: 70px
}

.h-\[70vh\] {
    height: 70vh
}

.h-\[80vh\] {
    height: 80vh
}

.h-\[84vh\] {
    height: 84vh
}

.h-\[88px\] {
    height: 88px
}

.h-\[90vh\] {
    height: 90vh
}

.h-\[calc\(100\%-113px\)\] {
    height: calc(100% - 113px)
}

.h-\[calc\(100\%-40px-24px-40px-8px\)\] {
    height: calc(100% - 40px - 24px - 40px - 8px)
}

.h-\[calc\(100\%-48px\)\] {
    height: calc(100% - 48px)
}

.h-\[calc\(100vh\)\] {
    height: calc(100vh)
}

.h-\[calc\(100vh-125px\)\] {
    height: calc(100vh - 125px)
}

.h-\[calc\(100vh-140px-40px-410px\)\] {
    height: calc(100vh - 140px - 40px - 410px)
}

.h-\[calc\(100vh-160px\)\] {
    height: calc(100vh - 160px)
}

.h-\[calc\(100vh-180px\)\] {
    height: calc(100vh - 180px)
}

.h-\[calc\(100vh-190px\)\] {
    height: calc(100vh - 190px)
}

.h-\[calc\(100vh-286px\)\] {
    height: calc(100vh - 286px)
}

.h-\[calc\(100vh-300px\)\] {
    height: calc(100vh - 300px)
}

.h-\[calc\(100vh-320px-100px\)\] {
    height: calc(100vh - 320px - 100px)
}

.h-\[calc\(100vh-360px\)\] {
    height: calc(100vh - 360px)
}

.h-\[calc\(100vh-380px-100px\)\] {
    height: calc(100vh - 380px - 100px)
}

.h-\[calc\(100vh-390px-100px\)\] {
    height: calc(100vh - 390px - 100px)
}

.h-\[calc\(100vh-420px\)\] {
    height: calc(100vh - 420px)
}

.h-\[calc\(100vh-430px\)\] {
    height: calc(100vh - 430px)
}

.h-\[calc\(100vh-460px\)\] {
    height: calc(100vh - 460px)
}

.h-\[calc\(100vh-480px\)\] {
    height: calc(100vh - 480px)
}

.h-\[calc\(100vh-80px\)\] {
    height: calc(100vh - 80px)
}

.h-\[calc\(100vh-80px-32px-169px-28px-34px-12px-32px\)\] {
    height: calc(100vh - 80px - 32px - 169px - 28px - 34px - 12px - 32px)
}

.h-\[calc\(100vh-82px\)\] {
    height: calc(100vh - 82px)
}

.h-\[calc\(100vh-90px\)\] {
    height: calc(100vh - 90px)
}

.h-auto {
    height: auto
}

.h-fit {
    height: -moz-fit-content;
    height: fit-content
}

.h-full {
    height: 100%
}

.h-px {
    height: 1px
}

.h-screen {
    height: 100vh
}

.\!max-h-\[210px\] {
    max-height: 210px !important
}

.max-h-0 {
    max-height: 0
}

.max-h-60 {
    max-height: 15rem
}

.max-h-8 {
    max-height: 2rem
}

.max-h-\[1000px\] {
    max-height: 1000px
}

.max-h-\[100vh\] {
    max-height: 100vh
}

.max-h-\[240px\] {
    max-height: 240px
}

.max-h-\[300px\] {
    max-height: 300px
}

.max-h-\[32rem\] {
    max-height: 32rem
}

.max-h-\[442px\] {
    max-height: 442px
}

.max-h-\[48vh\] {
    max-height: 48vh
}

.max-h-\[500px\] {
    max-height: 500px
}

.max-h-\[560px\] {
    max-height: 560px
}

.max-h-\[600px\] {
    max-height: 600px
}

.max-h-\[64px\] {
    max-height: 64px
}

.max-h-\[65vh\] {
    max-height: 65vh
}

.max-h-\[70vh\] {
    max-height: 70vh
}

.max-h-\[80vh\] {
    max-height: 80vh
}

.max-h-\[85vh\] {
    max-height: 85vh
}

.max-h-\[90vh\] {
    max-height: 90vh
}

.max-h-\[9999px\] {
    max-height: 9999px
}

.max-h-\[calc\(100vh-128px\)\] {
    max-height: calc(100vh - 128px)
}

.max-h-\[calc\(100vh-218px\)\] {
    max-height: calc(100vh - 218px)
}

.max-h-\[calc\(100vh-410px\)\] {
    max-height: calc(100vh - 410px)
}

.max-h-\[calc\(100vh-80px-32px-168px-28px-16px\)\] {
    max-height: calc(100vh - 80px - 32px - 168px - 28px - 16px)
}

.max-h-\[calc\(100vh-80px-32px-168px-28px-34px-16px-40px-12px-36px\)\] {
    max-height: calc(100vh - 80px - 32px - 168px - 28px - 34px - 16px - 40px - 12px - 36px)
}

.max-h-\[calc\(100vh-80px-32px-44px-20px-38px-32px-44px-32px\)\] {
    max-height: calc(100vh - 80px - 32px - 44px - 20px - 38px - 32px - 44px - 32px)
}

.\!min-h-fit {
    min-height: -moz-fit-content !important;
    min-height: fit-content !important
}

.min-h-0 {
    min-height: 0
}

.min-h-4 {
    min-height: 1rem
}

.min-h-5 {
    min-height: 1.25rem
}

.min-h-6 {
    min-height: 1.5rem
}

.min-h-\[160px\] {
    min-height: 160px
}

.min-h-\[18px\] {
    min-height: 18px
}

.min-h-\[1em\] {
    min-height: 1em
}

.min-h-\[20px\] {
    min-height: 20px
}

.min-h-\[220px\] {
    min-height: 220px
}

.min-h-\[28px\] {
    min-height: 28px
}

.min-h-\[30px\] {
    min-height: 30px
}

.min-h-\[398px\] {
    min-height: 398px
}

.min-h-\[400px\] {
    min-height: 400px
}

.min-h-\[40px\] {
    min-height: 40px
}

.min-h-\[50px\] {
    min-height: 50px
}

.min-h-\[50vh\] {
    min-height: 50vh
}

.min-h-\[510px\] {
    min-height: 510px
}

.min-h-\[520px\] {
    min-height: 520px
}

.min-h-\[52px\] {
    min-height: 52px
}

.min-h-\[5rem\] {
    min-height: 5rem
}

.min-h-\[600px\] {
    min-height: 600px
}

.min-h-\[640px\] {
    min-height: 640px
}

.min-h-\[68px\] {
    min-height: 68px
}

.min-h-\[70px\] {
    min-height: 70px
}

.min-h-\[75px\] {
    min-height: 75px
}

.min-h-\[90px\] {
    min-height: 90px
}

.min-h-\[calc\(100vh-0px\)\] {
    min-height: calc(100vh - 0px)
}

.min-h-\[calc\(100vh-260px\)\] {
    min-height: calc(100vh - 260px)
}

.min-h-\[calc\(100vh-64px\)\] {
    min-height: calc(100vh - 64px)
}

.min-h-full {
    min-height: 100%
}

.min-h-min {
    min-height: -moz-min-content;
    min-height: min-content
}

.\!w-40 {
    width: 10rem !important
}

.\!w-\[100px\] {
    width: 100px !important
}

.\!w-\[160px\] {
    width: 160px !important
}

.\!w-\[200px\] {
    width: 200px !important
}

.\!w-\[448px\] {
    width: 448px !important
}

.\!w-\[46px\] {
    width: 46px !important
}

.\!w-\[477px\] {
    width: 477px !important
}

.\!w-\[calc\(100\%-2rem\)\] {
    width: calc(100% - 2rem) !important
}

.\!w-auto {
    width: auto !important
}

.\!w-fit {
    width: -moz-fit-content !important;
    width: fit-content !important
}

.w-0 {
    width: 0
}

.w-1 {
    width: .25rem
}

.w-1\.5 {
    width: .375rem
}

.w-1\/2 {
    width: 50%
}

.w-1\/3 {
    width: 33.333333%
}

.w-1\/4 {
    width: 25%
}

.w-1\/6 {
    width: 16.666667%
}

.w-10 {
    width: 2.5rem
}

.w-11 {
    width: 2.75rem
}

.w-12 {
    width: 3rem
}

.w-14 {
    width: 3.5rem
}

.w-16 {
    width: 4rem
}

.w-2 {
    width: .5rem
}

.w-2\/12 {
    width: 16.666667%
}

.w-2\/4 {
    width: 50%
}

.w-2\/5 {
    width: 40%
}

.w-2\/6 {
    width: 33.333333%
}

.w-20 {
    width: 5rem
}

.w-24 {
    width: 6rem
}

.w-28 {
    width: 7rem
}

.w-3 {
    width: .75rem
}

.w-3\.5 {
    width: .875rem
}

.w-3\/5 {
    width: 60%
}

.w-3\/6 {
    width: 50%
}

.w-32 {
    width: 8rem
}

.w-4 {
    width: 1rem
}

.w-4\/5 {
    width: 80%
}

.w-40 {
    width: 10rem
}

.w-48 {
    width: 12rem
}

.w-5 {
    width: 1.25rem
}

.w-52 {
    width: 13rem
}

.w-56 {
    width: 14rem
}

.w-6 {
    width: 1.5rem
}

.w-6\/12 {
    width: 50%
}

.w-60 {
    width: 15rem
}

.w-64 {
    width: 16rem
}

.w-8 {
    width: 2rem
}

.w-8\/12 {
    width: 66.666667%
}

.w-80 {
    width: 20rem
}

.w-9 {
    width: 2.25rem
}

.w-\[10\.5rem\] {
    width: 10.5rem
}

.w-\[100\%\] {
    width: 100%
}

.w-\[100px\] {
    width: 100px
}

.w-\[11\.25rem\] {
    width: 11.25rem
}

.w-\[110px\] {
    width: 110px
}

.w-\[120px\] {
    width: 120px
}

.w-\[122px\] {
    width: 122px
}

.w-\[126px\] {
    width: 126px
}

.w-\[12px\] {
    width: 12px
}

.w-\[140px\] {
    width: 140px
}

.w-\[14px\] {
    width: 14px
}

.w-\[150px\] {
    width: 150px
}

.w-\[15px\] {
    width: 15px
}

.w-\[160px\] {
    width: 160px
}

.w-\[164px\] {
    width: 164px
}

.w-\[170px\] {
    width: 170px
}

.w-\[180px\] {
    width: 180px
}

.w-\[187px\] {
    width: 187px
}

.w-\[200px\] {
    width: 200px
}

.w-\[20px\] {
    width: 20px
}

.w-\[211px\] {
    width: 211px
}

.w-\[220px\] {
    width: 220px
}

.w-\[230px\] {
    width: 230px
}

.w-\[250px\] {
    width: 250px
}

.w-\[25px\] {
    width: 25px
}

.w-\[268px\] {
    width: 268px
}

.w-\[26px\] {
    width: 26px
}

.w-\[270px\] {
    width: 270px
}

.w-\[272px\] {
    width: 272px
}

.w-\[280px\] {
    width: 280px
}

.w-\[286px\] {
    width: 286px
}

.w-\[288px\] {
    width: 288px
}

.w-\[290px\] {
    width: 290px
}

.w-\[300px\] {
    width: 300px
}

.w-\[30px\] {
    width: 30px
}

.w-\[310px\] {
    width: 310px
}

.w-\[34px\] {
    width: 34px
}

.w-\[36px\] {
    width: 36px
}

.w-\[378px\] {
    width: 378px
}

.w-\[385px\] {
    width: 385px
}

.w-\[3px\] {
    width: 3px
}

.w-\[4\.5rem\] {
    width: 4.5rem
}

.w-\[40px\] {
    width: 40px
}

.w-\[440px\] {
    width: 440px
}

.w-\[48px\] {
    width: 48px
}

.w-\[50\%\] {
    width: 50%
}

.w-\[50px\] {
    width: 50px
}

.w-\[58px\] {
    width: 58px
}

.w-\[60px\] {
    width: 60px
}

.w-\[62px\] {
    width: 62px
}

.w-\[65px\] {
    width: 65px
}

.w-\[76px\] {
    width: 76px
}

.w-\[86px\] {
    width: 86px
}

.w-\[96px\] {
    width: 96px
}

.w-\[calc\(100\%-100px\)\] {
    width: calc(100% - 100px)
}

.w-\[calc\(100\%-12px\)\] {
    width: calc(100% - 12px)
}

.w-\[calc\(100\%-26px-20px\)\] {
    width: calc(100% - 26px - 20px)
}

.w-\[calc\(100\%-32px\)\] {
    width: calc(100% - 32px)
}

.w-\[calc\(30\%-12px\)\] {
    width: calc(30% - 12px)
}

.w-\[calc\(40\%-12px\)\] {
    width: calc(40% - 12px)
}

.w-\[calc\(50\%-16px\)\] {
    width: calc(50% - 16px)
}

.w-\[calc\(50\%-1rem\)\] {
    width: calc(50% - 1rem)
}

.w-\[calc\(50\%-2rem\)\] {
    width: calc(50% - 2rem)
}

.w-\[calc\(50\%-8px\)\] {
    width: calc(50% - 8px)
}

.w-auto {
    width: auto
}

.w-fit {
    width: -moz-fit-content;
    width: fit-content
}

.w-full {
    width: 100%
}

.w-px {
    width: 1px
}

.w-screen {
    width: 100vw
}

.min-w-0 {
    min-width: 0
}

.min-w-4 {
    min-width: 1rem
}

.min-w-5 {
    min-width: 1.25rem
}

.min-w-6 {
    min-width: 1.5rem
}

.min-w-\[100px\] {
    min-width: 100px
}

.min-w-\[112px\] {
    min-width: 112px
}

.min-w-\[113px\] {
    min-width: 113px
}

.min-w-\[115px\] {
    min-width: 115px
}

.min-w-\[120px\] {
    min-width: 120px
}

.min-w-\[126px\] {
    min-width: 126px
}

.min-w-\[130px\] {
    min-width: 130px
}

.min-w-\[140px\] {
    min-width: 140px
}

.min-w-\[150px\] {
    min-width: 150px
}

.min-w-\[164px\] {
    min-width: 164px
}

.min-w-\[170px\] {
    min-width: 170px
}

.min-w-\[190px\] {
    min-width: 190px
}

.min-w-\[200px\] {
    min-width: 200px
}

.min-w-\[206px\] {
    min-width: 206px
}

.min-w-\[232px\] {
    min-width: 232px
}

.min-w-\[240px\] {
    min-width: 240px
}

.min-w-\[250px\] {
    min-width: 250px
}

.min-w-\[273px\] {
    min-width: 273px
}

.min-w-\[300px\] {
    min-width: 300px
}

.min-w-\[316px\] {
    min-width: 316px
}

.min-w-\[320px\] {
    min-width: 320px
}

.min-w-\[36px\] {
    min-width: 36px
}

.min-w-\[59px\] {
    min-width: 59px
}

.min-w-\[60px\] {
    min-width: 60px
}

.min-w-\[660px\] {
    min-width: 660px
}

.min-w-\[80px\] {
    min-width: 80px
}

.min-w-\[86px\] {
    min-width: 86px
}

.min-w-\[87px\] {
    min-width: 87px
}

.min-w-\[90px\] {
    min-width: 90px
}

.min-w-\[calc\(100vw-0px\)\] {
    min-width: calc(100vw - 0px)
}

.min-w-full {
    min-width: 100%
}

.\!max-w-4xl {
    max-width: 56rem !important
}

.\!max-w-\[100\%\] {
    max-width: 100% !important
}

.max-w-2xl {
    max-width: 42rem
}

.max-w-3xl {
    max-width: 48rem
}

.max-w-4xl {
    max-width: 56rem
}

.max-w-5xl {
    max-width: 64rem
}

.max-w-7xl {
    max-width: 80rem
}

.max-w-\[1000px\] {
    max-width: 1000px
}

.max-w-\[1062px\] {
    max-width: 1062px
}

.max-w-\[1200px\] {
    max-width: 1200px
}

.max-w-\[120px\] {
    max-width: 120px
}

.max-w-\[1280px\] {
    max-width: 1280px
}

.max-w-\[152px\] {
    max-width: 152px
}

.max-w-\[160px\] {
    max-width: 160px
}

.max-w-\[200px\] {
    max-width: 200px
}

.max-w-\[250px\] {
    max-width: 250px
}

.max-w-\[32\.5rem\] {
    max-width: 32.5rem
}

.max-w-\[320px\] {
    max-width: 320px
}

.max-w-\[32rem\] {
    max-width: 32rem
}

.max-w-\[341px\] {
    max-width: 341px
}

.max-w-\[360px\] {
    max-width: 360px
}

.max-w-\[400px\] {
    max-width: 400px
}

.max-w-\[470px\] {
    max-width: 470px
}

.max-w-\[490px\] {
    max-width: 490px
}

.max-w-\[50rem\] {
    max-width: 50rem
}

.max-w-\[540px\] {
    max-width: 540px
}

.max-w-\[616px\] {
    max-width: 616px
}

.max-w-\[624px\] {
    max-width: 624px
}

.max-w-\[64px\] {
    max-width: 64px
}

.max-w-\[665px\] {
    max-width: 665px
}

.max-w-\[670px\] {
    max-width: 670px
}

.max-w-\[750px\] {
    max-width: 750px
}

.max-w-\[768px\] {
    max-width: 768px
}

.max-w-\[80\%\] {
    max-width: 80%
}

.max-w-\[800px\] {
    max-width: 800px
}

.max-w-\[840px\] {
    max-width: 840px
}

.max-w-\[860px\] {
    max-width: 860px
}

.max-w-\[874px\] {
    max-width: 874px
}

.max-w-\[876px\] {
    max-width: 876px
}

.max-w-\[88px\] {
    max-width: 88px
}

.max-w-\[96px\] {
    max-width: 96px
}

.max-w-fit {
    max-width: -moz-fit-content;
    max-width: fit-content
}

.max-w-full {
    max-width: 100%
}

.max-w-lg {
    max-width: 32rem
}

.max-w-md {
    max-width: 28rem
}

.max-w-none {
    max-width: none
}

.max-w-sm {
    max-width: 24rem
}

.max-w-xl {
    max-width: 36rem
}

.flex-1 {
    flex: 1 1 0%
}

.flex-auto {
    flex: 1 1 auto
}

.flex-none {
    flex: none
}

.flex-shrink-0, .shrink-0 {
    flex-shrink: 0
}

.grow {
    flex-grow: 1
}

.basis-0 {
    flex-basis: 0px
}

.basis-auto {
    flex-basis: auto
}

.table-fixed {
    table-layout: fixed
}

.origin-bottom-right {
    transform-origin: bottom right
}

.origin-top {
    transform-origin: top
}

.origin-top-right {
    transform-origin: top right
}

.-translate-x-1\/2 {
    --tw-translate-x: -50%
}

.-translate-x-1\/2, .-translate-y-1\.5 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-y-1\.5 {
    --tw-translate-y: -0.375rem
}

.-translate-y-1\/2 {
    --tw-translate-y: -50%
}

.-translate-y-1\/2, .translate-x-0 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-0 {
    --tw-translate-x: 0px
}

.translate-x-1 {
    --tw-translate-x: 0.25rem
}

.translate-x-1, .translate-x-5 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-5 {
    --tw-translate-x: 1.25rem
}

.translate-x-full {
    --tw-translate-x: 100%
}

.translate-x-full, .translate-y-0 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-y-0 {
    --tw-translate-y: 0px
}

.translate-y-1 {
    --tw-translate-y: 0.25rem
}

.translate-y-1, .translate-y-1\.5 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-y-1\.5 {
    --tw-translate-y: 0.375rem
}

.-rotate-90 {
    --tw-rotate: -90deg
}

.-rotate-90, .rotate-180 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-180 {
    --tw-rotate: 180deg
}

.rotate-45 {
    --tw-rotate: 45deg
}

.rotate-45, .rotate-90 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-90 {
    --tw-rotate: 90deg
}

.scale-0 {
    --tw-scale-x: 0;
    --tw-scale-y: 0
}

.scale-0, .scale-100 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1
}

.scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95
}

.scale-95, .transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.transform-none {
    transform: none
}

@keyframes pulse {
    50% {
        opacity: .5
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite
}

@keyframes spin {
    to {
        transform: rotate(1turn)
    }
}

.animate-spin {
    animation: spin 1s linear infinite
}

.\!cursor-default {
    cursor: default !important
}

.cursor-default {
    cursor: default
}

.cursor-no-drop {
    cursor: no-drop
}

.cursor-not-allowed {
    cursor: not-allowed
}

.cursor-pointer {
    cursor: pointer
}

.select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.resize-none {
    resize: none
}

.list-inside {
    list-style-position: inside
}

.list-disc {
    list-style-type: disc
}

.appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.columns-2 {
    -moz-columns: 2;
    column-count: 2
}

.grid-cols-1 {
    grid-template-columns:repeat(1, minmax(0, 1fr))
}

.grid-cols-11 {
    grid-template-columns:repeat(11, minmax(0, 1fr))
}

.grid-cols-12 {
    grid-template-columns:repeat(12, minmax(0, 1fr))
}

.grid-cols-2 {
    grid-template-columns:repeat(2, minmax(0, 1fr))
}

.grid-cols-3 {
    grid-template-columns:repeat(3, minmax(0, 1fr))
}

.grid-cols-4 {
    grid-template-columns:repeat(4, minmax(0, 1fr))
}

.grid-cols-48 {
    grid-template-columns:repeat(48, minmax(0, 1fr))
}

.grid-cols-\[1\.5fr_repeat\(4\2c minmax\(0\2c 1fr\)\)\] {
    grid-template-columns:1.5fr repeat(4, minmax(0, 1fr))
}

.grid-cols-\[122px_1fr\] {
    grid-template-columns:122px 1fr
}

.grid-cols-\[1fr_10px\] {
    grid-template-columns:1fr 10px
}

.grid-cols-\[1fr_80px\] {
    grid-template-columns:1fr 80px
}

.grid-cols-\[60px_1fr\] {
    grid-template-columns:60px 1fr
}

.grid-cols-\[76px_1fr\] {
    grid-template-columns:76px 1fr
}

.grid-rows-2 {
    grid-template-rows:repeat(2, minmax(0, 1fr))
}

.flex-row {
    flex-direction: row
}

.flex-col {
    flex-direction: column
}

.flex-col-reverse {
    flex-direction: column-reverse
}

.flex-wrap {
    flex-wrap: wrap
}

.place-items-center {
    place-items: center
}

.items-start {
    align-items: flex-start
}

.items-end {
    align-items: flex-end
}

.items-center {
    align-items: center
}

.items-baseline {
    align-items: baseline
}

.items-stretch {
    align-items: stretch
}

.justify-start {
    justify-content: flex-start
}

.justify-end {
    justify-content: flex-end
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.gap-0\.5 {
    gap: .125rem
}

.gap-1 {
    gap: .25rem
}

.gap-10 {
    gap: 2.5rem
}

.gap-12 {
    gap: 3rem
}

.gap-2 {
    gap: .5rem
}

.gap-3 {
    gap: .75rem
}

.gap-4 {
    gap: 1rem
}

.gap-5 {
    gap: 1.25rem
}

.gap-6 {
    gap: 1.5rem
}

.gap-8 {
    gap: 2rem
}

.gap-9 {
    gap: 2.25rem
}

.gap-\[10px\] {
    gap: 10px
}

.gap-\[11px\] {
    gap: 11px
}

.gap-\[14px\] {
    gap: 14px
}

.gap-\[15px\] {
    gap: 15px
}

.gap-\[1px\] {
    gap: 1px
}

.gap-\[25px\] {
    gap: 25px
}

.gap-\[30px\] {
    gap: 30px
}

.gap-\[35px\] {
    gap: 35px
}

.gap-\[50px\] {
    gap: 50px
}

.gap-\[5px\] {
    gap: 5px
}

.gap-\[7px\] {
    gap: 7px
}

.gap-x-1 {
    -moz-column-gap: .25rem;
    column-gap: .25rem
}

.gap-x-1\.5 {
    -moz-column-gap: .375rem;
    column-gap: .375rem
}

.gap-x-2 {
    -moz-column-gap: .5rem;
    column-gap: .5rem
}

.gap-x-2\.5 {
    -moz-column-gap: .625rem;
    column-gap: .625rem
}

.gap-x-3 {
    -moz-column-gap: .75rem;
    column-gap: .75rem
}

.gap-x-4 {
    -moz-column-gap: 1rem;
    column-gap: 1rem
}

.gap-x-5 {
    -moz-column-gap: 1.25rem;
    column-gap: 1.25rem
}

.gap-x-6 {
    -moz-column-gap: 1.5rem;
    column-gap: 1.5rem
}

.gap-x-8 {
    -moz-column-gap: 2rem;
    column-gap: 2rem
}

.gap-y-1 {
    row-gap: .25rem
}

.gap-y-10 {
    row-gap: 2.5rem
}

.gap-y-12 {
    row-gap: 3rem
}

.gap-y-2 {
    row-gap: .5rem
}

.gap-y-3 {
    row-gap: .75rem
}

.gap-y-4 {
    row-gap: 1rem
}

.gap-y-6 {
    row-gap: 1.5rem
}

.gap-y-8 {
    row-gap: 2rem
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.25rem * var(--tw-space-x-reverse));
    margin-left: calc(.25rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-11 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2.75rem * var(--tw-space-x-reverse));
    margin-left: calc(2.75rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.5rem * var(--tw-space-x-reverse));
    margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.75rem * var(--tw-space-x-reverse));
    margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse))
}

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.125rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.125rem * var(--tw-space-y-reverse))
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.25rem * var(--tw-space-y-reverse))
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.5rem * var(--tw-space-y-reverse))
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(.75rem * var(--tw-space-y-reverse))
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse))
}

.space-y-5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.25rem * var(--tw-space-y-reverse))
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse))
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse))
}

.divide-x > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)))
}

.divide-x-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(0px * var(--tw-divide-x-reverse));
    border-left-width: calc(0px * calc(1 - var(--tw-divide-x-reverse)))
}

.divide-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(2px * var(--tw-divide-x-reverse));
    border-left-width: calc(2px * calc(1 - var(--tw-divide-x-reverse)))
}

.divide-x-\[5px\] > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(5px * var(--tw-divide-x-reverse));
    border-left-width: calc(5px * calc(1 - var(--tw-divide-x-reverse)))
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(1px * var(--tw-divide-y-reverse))
}

.divide-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(2px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(2px * var(--tw-divide-y-reverse))
}

.divide-\[\#D4D4D4\] > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(212 212 212/var(--tw-divide-opacity))
}

.divide-\[\#E5E7EB\] > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-divide-opacity))
}

.divide-gray-300 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-divide-opacity))
}

.divide-oms-gray-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(242 242 242/var(--tw-divide-opacity))
}

.divide-oms-gray-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(216 216 218/var(--tw-divide-opacity))
}

.divide-secondary-100 > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--color-secondary-100)
}

.divide-white > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-divide-opacity))
}

.self-end {
    align-self: flex-end
}

.self-center {
    align-self: center
}

.justify-self-end {
    justify-self: end
}

.overflow-auto {
    overflow: auto
}

.overflow-hidden {
    overflow: hidden
}

.\!overflow-visible {
    overflow: visible !important
}

.overflow-visible {
    overflow: visible
}

.overflow-scroll {
    overflow: scroll
}

.overflow-x-auto {
    overflow-x: auto
}

.overflow-y-auto {
    overflow-y: auto
}

.overflow-x-hidden {
    overflow-x: hidden
}

.\!overflow-x-visible {
    overflow-x: visible !important
}

.overflow-x-scroll {
    overflow-x: scroll
}

.overflow-y-scroll {
    overflow-y: scroll
}

.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.whitespace-normal {
    white-space: normal
}

.whitespace-nowrap {
    white-space: nowrap
}

.whitespace-pre-wrap {
    white-space: pre-wrap
}

.break-words {
    overflow-wrap: break-word
}

.break-all {
    word-break: break-all
}

.\!rounded-none {
    border-radius: 0 !important
}

.\!rounded-xl {
    border-radius: .75rem !important
}

.rounded {
    border-radius: .25rem
}

.rounded-2xl {
    border-radius: 1rem
}

.rounded-3xl {
    border-radius: 1.5rem
}

.rounded-\[10px\] {
    border-radius: 10px
}

.rounded-\[20px\] {
    border-radius: 20px
}

.rounded-\[4px\] {
    border-radius: 4px
}

.rounded-\[50\%\] {
    border-radius: 50%
}

.rounded-\[50px\] {
    border-radius: 50px
}

.rounded-full {
    border-radius: 9999px
}

.rounded-lg {
    border-radius: .5rem
}

.rounded-md {
    border-radius: .375rem
}

.rounded-none {
    border-radius: 0
}

.rounded-sm {
    border-radius: .125rem
}

.rounded-xl {
    border-radius: .75rem
}

.\!rounded-l-none {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important
}

.\!rounded-r-none {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important
}

.rounded-b-3xl {
    border-bottom-right-radius: 1.5rem;
    border-bottom-left-radius: 1.5rem
}

.rounded-b-\[20px\] {
    border-bottom-right-radius: 20px;
    border-bottom-left-radius: 20px
}

.rounded-b-xl {
    border-bottom-right-radius: .75rem;
    border-bottom-left-radius: .75rem
}

.rounded-l {
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.rounded-l-\[10px\] {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px
}

.rounded-l-\[12px\] {
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px
}

.rounded-l-none {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.rounded-r-\[10px\] {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px
}

.rounded-r-md {
    border-top-right-radius: .375rem;
    border-bottom-right-radius: .375rem
}

.rounded-t-3xl {
    border-top-left-radius: 1.5rem;
    border-top-right-radius: 1.5rem
}

.rounded-t-\[30px\] {
    border-top-left-radius: 30px;
    border-top-right-radius: 30px
}

.rounded-t-xl {
    border-top-left-radius: .75rem;
    border-top-right-radius: .75rem
}

.rounded-br {
    border-bottom-right-radius: .25rem
}

.rounded-tl {
    border-top-left-radius: .25rem
}

.\!border {
    border-width: 1px !important
}

.\!border-0 {
    border-width: 0 !important
}

.border {
    border-width: 1px
}

.border-0 {
    border-width: 0
}

.border-2 {
    border-width: 2px
}

.border-4 {
    border-width: 4px
}

.border-\[1\.5px\] {
    border-width: 1.5px
}

.border-x-0 {
    border-left-width: 0;
    border-right-width: 0
}

.border-y {
    border-top-width: 1px;
    border-bottom-width: 1px
}

.\!border-b {
    border-bottom-width: 1px !important
}

.border-b {
    border-bottom-width: 1px
}

.border-b-2 {
    border-bottom-width: 2px
}

.border-b-8 {
    border-bottom-width: 8px
}

.border-l {
    border-left-width: 1px
}

.border-l-0 {
    border-left-width: 0
}

.border-l-4 {
    border-left-width: 4px
}

.border-l-\[2px\] {
    border-left-width: 2px
}

.border-l-\[3px\] {
    border-left-width: 3px
}

.border-r {
    border-right-width: 1px
}

.border-t {
    border-top-width: 1px
}

.border-t-2 {
    border-top-width: 2px
}

.border-solid {
    border-style: solid
}

.border-dashed {
    border-style: dashed
}

.border-dotted {
    border-style: dotted
}

.\!border-none {
    border-style: none !important
}

.border-none {
    border-style: none
}

.\!border-\[\#E5E7EB\] {
    --tw-border-opacity: 1 !important;
    border-color: rgb(229 231 235/var(--tw-border-opacity)) !important
}

.\!border-oms-gray-2 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(205 205 205/var(--tw-border-opacity)) !important
}

.\!border-oms-green-3 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(104 215 139/var(--tw-border-opacity)) !important
}

.\!border-oms-green-6 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(0 135 0/var(--tw-border-opacity)) !important
}

.\!border-oms-orange-3 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(211 185 115/var(--tw-border-opacity)) !important
}

.\!border-primary {
    border-color: var(--color-primary) !important
}

.\!border-primary-200 {
    border-color: var(--color-primary-200) !important
}

.\!border-primary-light {
    border-color: var(--color-primary-light) !important
}

.\!border-rose-500 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(244 63 94/var(--tw-border-opacity)) !important
}

.\!border-secondary {
    border-color: var(--color-secondary) !important
}

.\!border-secondary-200 {
    border-color: var(--color-secondary-200) !important
}

.border-\[\#B5B5B5\] {
    --tw-border-opacity: 1;
    border-color: rgb(181 181 181/var(--tw-border-opacity))
}

.border-\[\#C4C5C7\] {
    --tw-border-opacity: 1;
    border-color: rgb(196 197 199/var(--tw-border-opacity))
}

.border-\[\#C5D4FA\] {
    --tw-border-opacity: 1;
    border-color: rgb(197 212 250/var(--tw-border-opacity))
}

.border-\[\#C8E6C9\] {
    --tw-border-opacity: 1;
    border-color: rgb(200 230 201/var(--tw-border-opacity))
}

.border-\[\#CC7A00\] {
    --tw-border-opacity: 1;
    border-color: rgb(204 122 0/var(--tw-border-opacity))
}

.border-\[\#D1D5DB\] {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity))
}

.border-\[\#D4D4D4\] {
    --tw-border-opacity: 1;
    border-color: rgb(212 212 212/var(--tw-border-opacity))
}

.border-\[\#D8D8DA\] {
    --tw-border-opacity: 1;
    border-color: rgb(216 216 218/var(--tw-border-opacity))
}

.border-\[\#DADADA\] {
    --tw-border-opacity: 1;
    border-color: rgb(218 218 218/var(--tw-border-opacity))
}

.border-\[\#E5BBED\] {
    --tw-border-opacity: 1;
    border-color: rgb(229 187 237/var(--tw-border-opacity))
}

.border-\[\#E5E7EB\] {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity))
}

.border-\[\#EAEAEA\] {
    --tw-border-opacity: 1;
    border-color: rgb(234 234 234/var(--tw-border-opacity))
}

.border-\[\#FF7900\]\/30 {
    border-color: rgba(255, 121, 0, .3)
}

.border-\[\#FFAE0F\] {
    --tw-border-opacity: 1;
    border-color: rgb(255 174 15/var(--tw-border-opacity))
}

.border-\[\#f2f2f2\] {
    --tw-border-opacity: 1;
    border-color: rgb(242 242 242/var(--tw-border-opacity))
}

.border-\[rgba\(0\2c 0\2c 0\2c 0\.08\)\] {
    border-color: rgba(0, 0, 0, .08)
}

.border-\[rgba\(0\2c 0\2c 0\2c 0\.12\)\] {
    border-color: rgba(0, 0, 0, .12)
}

.border-amber-500 {
    --tw-border-opacity: 1;
    border-color: rgb(245 158 11/var(--tw-border-opacity))
}

.border-black {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0/var(--tw-border-opacity))
}

.border-blue-300 {
    --tw-border-opacity: 1;
    border-color: rgb(147 197 253/var(--tw-border-opacity))
}

.border-current {
    border-color: currentColor
}

.border-danger-200 {
    border-color: var(--color-danger-200)
}

.border-danger-600 {
    border-color: var(--color-danger-600)
}

.border-danger-700 {
    border-color: var(--color-danger-700)
}

.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity))
}

.border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity))
}

.border-gray-900 {
    --tw-border-opacity: 1;
    border-color: rgb(17 24 39/var(--tw-border-opacity))
}

.border-oms-blue-2 {
    --tw-border-opacity: 1;
    border-color: rgb(14 128 143/var(--tw-border-opacity))
}

.border-oms-gray {
    --tw-border-opacity: 1;
    border-color: rgb(84 84 84/var(--tw-border-opacity))
}

.border-oms-gray-10 {
    --tw-border-opacity: 1;
    border-color: rgb(97 100 107/var(--tw-border-opacity))
}

.border-oms-gray-2 {
    --tw-border-opacity: 1;
    border-color: rgb(205 205 205/var(--tw-border-opacity))
}

.border-oms-gray-3 {
    --tw-border-opacity: 1;
    border-color: rgb(242 242 242/var(--tw-border-opacity))
}

.border-oms-gray-6 {
    --tw-border-opacity: 1;
    border-color: rgb(216 216 218/var(--tw-border-opacity))
}

.border-oms-gray-9 {
    --tw-border-opacity: 1;
    border-color: rgb(235 236 236/var(--tw-border-opacity))
}

.border-oms-green {
    --tw-border-opacity: 1;
    border-color: rgb(0 130 0/var(--tw-border-opacity))
}

.border-oms-green-5 {
    --tw-border-opacity: 1;
    border-color: rgb(1 152 88/var(--tw-border-opacity))
}

.border-oms-green-6 {
    --tw-border-opacity: 1;
    border-color: rgb(0 135 0/var(--tw-border-opacity))
}

.border-oms-orange {
    --tw-border-opacity: 1;
    border-color: rgb(255 184 0/var(--tw-border-opacity))
}

.border-oms-orange-5 {
    --tw-border-opacity: 1;
    border-color: rgb(235 155 0/var(--tw-border-opacity))
}

.border-oms-orange-6 {
    --tw-border-opacity: 1;
    border-color: rgb(255 174 15/var(--tw-border-opacity))
}

.border-oms-red {
    --tw-border-opacity: 1;
    border-color: rgb(236 28 36/var(--tw-border-opacity))
}

.border-oms-red-3 {
    --tw-border-opacity: 1;
    border-color: rgb(179 49 49/var(--tw-border-opacity))
}

.border-pink-400 {
    --tw-border-opacity: 1;
    border-color: rgb(244 114 182/var(--tw-border-opacity))
}

.border-primary {
    border-color: var(--color-primary)
}

.border-primary-light {
    border-color: var(--color-primary-light)
}

.border-primary-light-300 {
    border-color: var(--color-primary-light-300)
}

.border-rose-500 {
    --tw-border-opacity: 1;
    border-color: rgb(244 63 94/var(--tw-border-opacity))
}

.border-secondary {
    border-color: var(--color-secondary)
}

.border-secondary-100 {
    border-color: var(--color-secondary-100)
}

.border-secondary-200 {
    border-color: var(--color-secondary-200)
}

.border-secondary-900 {
    border-color: var(--color-secondary-900)
}

.border-secondary-light {
    border-color: var(--color-secondary-light)
}

.border-secondary-light-300 {
    border-color: var(--color-secondary-light-300)
}

.border-secondary-light-400 {
    border-color: var(--color-secondary-light-400)
}

.border-transparent {
    border-color: transparent
}

.border-violet-600 {
    --tw-border-opacity: 1;
    border-color: rgb(124 58 237/var(--tw-border-opacity))
}

.border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-border-opacity))
}

.border-yellow-400 {
    --tw-border-opacity: 1;
    border-color: rgb(250 204 21/var(--tw-border-opacity))
}

.border-yellow-500 {
    --tw-border-opacity: 1;
    border-color: rgb(234 179 8/var(--tw-border-opacity))
}

.border-yellow-800 {
    --tw-border-opacity: 1;
    border-color: rgb(133 77 14/var(--tw-border-opacity))
}

.\!border-b-\[\#E7E7F1\] {
    --tw-border-opacity: 1 !important;
    border-bottom-color: rgb(231 231 241/var(--tw-border-opacity)) !important
}

.\!border-b-oms-green {
    --tw-border-opacity: 1 !important;
    border-bottom-color: rgb(0 130 0/var(--tw-border-opacity)) !important
}

.\!border-b-oms-green-7 {
    --tw-border-opacity: 1 !important;
    border-bottom-color: rgb(0 104 0/var(--tw-border-opacity)) !important
}

.border-b-\[\#E5E7EB\] {
    --tw-border-opacity: 1;
    border-bottom-color: rgb(229 231 235/var(--tw-border-opacity))
}

.border-b-transparent {
    border-bottom-color: transparent
}

.border-l-\[\#D9D9D9\] {
    --tw-border-opacity: 1;
    border-left-color: rgb(217 217 217/var(--tw-border-opacity))
}

.border-l-oms-blue-3 {
    --tw-border-opacity: 1;
    border-left-color: rgb(42 110 187/var(--tw-border-opacity))
}

.border-l-oms-green {
    --tw-border-opacity: 1;
    border-left-color: rgb(0 130 0/var(--tw-border-opacity))
}

.border-l-oms-green-12 {
    --tw-border-opacity: 1;
    border-left-color: rgb(109 164 0/var(--tw-border-opacity))
}

.border-l-oms-orange-11 {
    --tw-border-opacity: 1;
    border-left-color: rgb(229 39 0/var(--tw-border-opacity))
}

.border-t-\[\#e4e4e4\] {
    --tw-border-opacity: 1;
    border-top-color: rgb(228 228 228/var(--tw-border-opacity))
}

.border-t-oms-gray-2 {
    --tw-border-opacity: 1;
    border-top-color: rgb(205 205 205/var(--tw-border-opacity))
}

.border-t-oms-gray-3 {
    --tw-border-opacity: 1;
    border-top-color: rgb(242 242 242/var(--tw-border-opacity))
}

.border-t-oms-gray-9 {
    border-top-color: rgb(235 236 236/var(--tw-border-opacity))
}

.border-opacity-100, .border-t-oms-gray-9 {
    --tw-border-opacity: 1
}

.border-opacity-60 {
    --tw-border-opacity: 0.6
}

.border-opacity-\[0\.08\] {
    --tw-border-opacity: 0.08
}

.\!bg-\[\#EBECEC\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(235 236 236/var(--tw-bg-opacity)) !important
}

.\!bg-\[\#F2F2F2\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(242 242 242/var(--tw-bg-opacity)) !important
}

.\!bg-\[\#f9f9f9\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(249 249 249/var(--tw-bg-opacity)) !important
}

.\!bg-black {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(0 0 0/var(--tw-bg-opacity)) !important
}

.\!bg-oms-gray-11 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(156 158 162/var(--tw-bg-opacity)) !important
}

.\!bg-oms-green-10 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(146 204 146/var(--tw-bg-opacity)) !important
}

.\!bg-oms-green-4 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(236 255 242/var(--tw-bg-opacity)) !important
}

.\!bg-oms-green-8 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(208 231 208/var(--tw-bg-opacity)) !important
}

.\!bg-oms-green-9 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(82 183 82/var(--tw-bg-opacity)) !important
}

.\!bg-oms-orange-4 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 234 178/var(--tw-bg-opacity)) !important
}

.\!bg-oms-orange-6 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 174 15/var(--tw-bg-opacity)) !important
}

.\!bg-oms-orange-8 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 236 198/var(--tw-bg-opacity)) !important
}

.\!bg-oms-red-6 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(199 71 71/var(--tw-bg-opacity)) !important
}

.\!bg-oms-red-8 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(253 221 221/var(--tw-bg-opacity)) !important
}

.\!bg-primary {
    background-color: var(--color-primary) !important
}

.\!bg-primary-light {
    background-color: var(--color-primary-light) !important
}

.\!bg-transparent {
    background-color: transparent !important
}

.\!bg-white {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 255 255/var(--tw-bg-opacity)) !important
}

.bg-\[\#008700\] {
    --tw-bg-opacity: 1;
    background-color: rgb(0 135 0/var(--tw-bg-opacity))
}

.bg-\[\#00A551\] {
    --tw-bg-opacity: 1;
    background-color: rgb(0 165 81/var(--tw-bg-opacity))
}

.bg-\[\#222\] {
    --tw-bg-opacity: 1;
    background-color: rgb(34 34 34/var(--tw-bg-opacity))
}

.bg-\[\#3156B3\] {
    --tw-bg-opacity: 1;
    background-color: rgb(49 86 179/var(--tw-bg-opacity))
}

.bg-\[\#31B387\] {
    --tw-bg-opacity: 1;
    background-color: rgb(49 179 135/var(--tw-bg-opacity))
}

.bg-\[\#34B53A\] {
    --tw-bg-opacity: 1;
    background-color: rgb(52 181 58/var(--tw-bg-opacity))
}

.bg-\[\#3a3d46\] {
    --tw-bg-opacity: 1;
    background-color: rgb(58 61 70/var(--tw-bg-opacity))
}

.bg-\[\#3b5998\] {
    --tw-bg-opacity: 1;
    background-color: rgb(59 89 152/var(--tw-bg-opacity))
}

.bg-\[\#4339F2\] {
    --tw-bg-opacity: 1;
    background-color: rgb(67 57 242/var(--tw-bg-opacity))
}

.bg-\[\#5C31B3\] {
    --tw-bg-opacity: 1;
    background-color: rgb(92 49 179/var(--tw-bg-opacity))
}

.bg-\[\#79ddc4\] {
    --tw-bg-opacity: 1;
    background-color: rgb(121 221 196/var(--tw-bg-opacity))
}

.bg-\[\#8F02A8\] {
    --tw-bg-opacity: 1;
    background-color: rgb(143 2 168/var(--tw-bg-opacity))
}

.bg-\[\#9CA3AF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(156 163 175/var(--tw-bg-opacity))
}

.bg-\[\#ADD8E6\] {
    --tw-bg-opacity: 1;
    background-color: rgb(173 216 230/var(--tw-bg-opacity))
}

.bg-\[\#B33131\] {
    --tw-bg-opacity: 1;
    background-color: rgb(179 49 49/var(--tw-bg-opacity))
}

.bg-\[\#B35A31\] {
    --tw-bg-opacity: 1;
    background-color: rgb(179 90 49/var(--tw-bg-opacity))
}

.bg-\[\#B5B5B5\] {
    --tw-bg-opacity: 1;
    background-color: rgb(181 181 181/var(--tw-bg-opacity))
}

.bg-\[\#B7B7B7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(183 183 183/var(--tw-bg-opacity))
}

.bg-\[\#C6C6C6\] {
    --tw-bg-opacity: 1;
    background-color: rgb(198 198 198/var(--tw-bg-opacity))
}

.bg-\[\#CC6100\] {
    --tw-bg-opacity: 1;
    background-color: rgb(204 97 0/var(--tw-bg-opacity))
}

.bg-\[\#D0E7D0\] {
    --tw-bg-opacity: 1;
    background-color: rgb(208 231 208/var(--tw-bg-opacity))
}

.bg-\[\#D2FBDF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.bg-\[\#D9D9D9\] {
    --tw-bg-opacity: 1;
    background-color: rgb(217 217 217/var(--tw-bg-opacity))
}

.bg-\[\#DDE6FD\] {
    --tw-bg-opacity: 1;
    background-color: rgb(221 230 253/var(--tw-bg-opacity))
}

.bg-\[\#E0FAE0\] {
    --tw-bg-opacity: 1;
    background-color: rgb(224 250 224/var(--tw-bg-opacity))
}

.bg-\[\#E2E2E2\] {
    --tw-bg-opacity: 1;
    background-color: rgb(226 226 226/var(--tw-bg-opacity))
}

.bg-\[\#E2FBD7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(226 251 215/var(--tw-bg-opacity))
}

.bg-\[\#E3F7ED\] {
    --tw-bg-opacity: 1;
    background-color: rgb(227 247 237/var(--tw-bg-opacity))
}

.bg-\[\#E6E6E6\] {
    --tw-bg-opacity: 1;
    background-color: rgb(230 230 230/var(--tw-bg-opacity))
}

.bg-\[\#E8E8E8\] {
    --tw-bg-opacity: 1;
    background-color: rgb(232 232 232/var(--tw-bg-opacity))
}

.bg-\[\#EBEBEB\] {
    --tw-bg-opacity: 1;
    background-color: rgb(235 235 235/var(--tw-bg-opacity))
}

.bg-\[\#EBECEC\] {
    --tw-bg-opacity: 1;
    background-color: rgb(235 236 236/var(--tw-bg-opacity))
}

.bg-\[\#EC1C24\] {
    --tw-bg-opacity: 1;
    background-color: rgb(236 28 36/var(--tw-bg-opacity))
}

.bg-\[\#ECECEC\] {
    --tw-bg-opacity: 1;
    background-color: rgb(236 236 236/var(--tw-bg-opacity))
}

.bg-\[\#ECEFF2\] {
    --tw-bg-opacity: 1;
    background-color: rgb(236 239 242/var(--tw-bg-opacity))
}

.bg-\[\#F1F1F1\] {
    --tw-bg-opacity: 1;
    background-color: rgb(241 241 241/var(--tw-bg-opacity))
}

.bg-\[\#F1F5F1\] {
    --tw-bg-opacity: 1;
    background-color: rgb(241 245 241/var(--tw-bg-opacity))
}

.bg-\[\#F1FCF1\] {
    --tw-bg-opacity: 1;
    background-color: rgb(241 252 241/var(--tw-bg-opacity))
}

.bg-\[\#F5F5F6\] {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 246/var(--tw-bg-opacity))
}

.bg-\[\#F6F6F7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(246 246 247/var(--tw-bg-opacity))
}

.bg-\[\#F7F7F7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(247 247 247/var(--tw-bg-opacity))
}

.bg-\[\#FAF6FF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(250 246 255/var(--tw-bg-opacity))
}

.bg-\[\#FF7900\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 121 0/var(--tw-bg-opacity))
}

.bg-\[\#FFEFEC\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 239 236/var(--tw-bg-opacity))
}

.bg-\[\#FFF7DD\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 247 221/var(--tw-bg-opacity))
}

.bg-\[\#FFF7DE\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 247 222/var(--tw-bg-opacity))
}

.bg-\[\#FFFCF8\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 252 248/var(--tw-bg-opacity))
}

.bg-\[\#eee\] {
    --tw-bg-opacity: 1;
    background-color: rgb(238 238 238/var(--tw-bg-opacity))
}

.bg-\[\#f4f7f4\] {
    --tw-bg-opacity: 1;
    background-color: rgb(244 247 244/var(--tw-bg-opacity))
}

.bg-\[\#f6f6f7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(246 246 247/var(--tw-bg-opacity))
}

.bg-\[\#ff8f00\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 143 0/var(--tw-bg-opacity))
}

.bg-\[rgba\(241\2c 241\2c 241\2c 0\.7\)\] {
    background-color: hsla(0, 0%, 95%, .7)
}

.bg-amber-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 243 199/var(--tw-bg-opacity))
}

.bg-amber-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(245 158 11/var(--tw-bg-opacity))
}

.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity))
}

.bg-black\/30 {
    background-color: rgba(0, 0, 0, .3)
}

.bg-black\/50 {
    background-color: rgba(0, 0, 0, .5)
}

.bg-black\/60 {
    background-color: rgba(0, 0, 0, .6)
}

.bg-black\/\[\.08\] {
    background-color: rgba(0, 0, 0, .08)
}

.bg-blue-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(147 197 253/var(--tw-bg-opacity))
}

.bg-current {
    background-color: currentColor
}

.bg-danger-100 {
    background-color: var(--color-danger-100)
}

.bg-danger-600 {
    background-color: var(--color-danger-600)
}

.bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity))
}

.bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity))
}

.bg-gray-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251/var(--tw-bg-opacity))
}

.bg-gray-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39/var(--tw-bg-opacity))
}

.bg-green-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(240 253 244/var(--tw-bg-opacity))
}

.bg-inherit {
    background-color: inherit
}

.bg-neutral-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 245/var(--tw-bg-opacity))
}

.bg-oms-blue-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(14 128 143/var(--tw-bg-opacity))
}

.bg-oms-gray-10 {
    --tw-bg-opacity: 1;
    background-color: rgb(97 100 107/var(--tw-bg-opacity))
}

.bg-oms-gray-14 {
    --tw-bg-opacity: 1;
    background-color: rgb(217 217 217/var(--tw-bg-opacity))
}

.bg-oms-gray-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(205 205 205/var(--tw-bg-opacity))
}

.bg-oms-gray-3 {
    --tw-bg-opacity: 1;
    background-color: rgb(242 242 242/var(--tw-bg-opacity))
}

.bg-oms-gray-6 {
    --tw-bg-opacity: 1;
    background-color: rgb(216 216 218/var(--tw-bg-opacity))
}

.bg-oms-gray-8 {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 246/var(--tw-bg-opacity))
}

.bg-oms-gray-9 {
    --tw-bg-opacity: 1;
    background-color: rgb(235 236 236/var(--tw-bg-opacity))
}

.bg-oms-green {
    --tw-bg-opacity: 1;
    background-color: rgb(0 130 0/var(--tw-bg-opacity))
}

.bg-oms-green-11 {
    --tw-bg-opacity: 1;
    background-color: rgb(232 244 232/var(--tw-bg-opacity))
}

.bg-oms-green-13 {
    --tw-bg-opacity: 1;
    background-color: rgb(19 163 19/var(--tw-bg-opacity))
}

.bg-oms-green-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.bg-oms-green-6 {
    --tw-bg-opacity: 1;
    background-color: rgb(0 135 0/var(--tw-bg-opacity))
}

.bg-oms-green-8 {
    --tw-bg-opacity: 1;
    background-color: rgb(208 231 208/var(--tw-bg-opacity))
}

.bg-oms-green\/5 {
    background-color: rgba(0, 130, 0, .05)
}

.bg-oms-orange {
    --tw-bg-opacity: 1;
    background-color: rgb(255 184 0/var(--tw-bg-opacity))
}

.bg-oms-orange-10 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 121 0/var(--tw-bg-opacity))
}

.bg-oms-orange-11 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 39 0/var(--tw-bg-opacity))
}

.bg-oms-orange-4 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 234 178/var(--tw-bg-opacity))
}

.bg-oms-orange-5 {
    --tw-bg-opacity: 1;
    background-color: rgb(235 155 0/var(--tw-bg-opacity))
}

.bg-oms-orange-6 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 174 15/var(--tw-bg-opacity))
}

.bg-oms-orange-9 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 184 16/var(--tw-bg-opacity))
}

.bg-oms-purple-4 {
    --tw-bg-opacity: 1;
    background-color: rgb(215 206 251/var(--tw-bg-opacity))
}

.bg-oms-red {
    --tw-bg-opacity: 1;
    background-color: rgb(236 28 36/var(--tw-bg-opacity))
}

.bg-oms-red-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(214 101 110/var(--tw-bg-opacity))
}

.bg-oms-red-3 {
    --tw-bg-opacity: 1;
    background-color: rgb(179 49 49/var(--tw-bg-opacity))
}

.bg-oms-white {
    --tw-bg-opacity: 1;
    background-color: rgb(241 252 241/var(--tw-bg-opacity))
}

.bg-pink-400 {
    --tw-bg-opacity: 1;
    background-color: rgb(244 114 182/var(--tw-bg-opacity))
}

.bg-primary {
    background-color: var(--color-primary)
}

.bg-primary-light {
    background-color: var(--color-primary-light)
}

.bg-primary-light-100 {
    background-color: var(--color-primary-light-100)
}

.bg-red-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 242 242/var(--tw-bg-opacity))
}

.bg-secondary {
    background-color: var(--color-secondary)
}

.bg-secondary-100 {
    background-color: var(--color-secondary-100)
}

.bg-secondary-200 {
    background-color: var(--color-secondary-200)
}

.bg-secondary-400 {
    background-color: var(--color-secondary-400)
}

.bg-secondary-700-75 {
    background-color: var(--color-secondary-700-75)
}

.bg-secondary-900 {
    background-color: var(--color-secondary-900)
}

.bg-secondary-light {
    background-color: var(--color-secondary-light)
}

.bg-secondary-light-300 {
    background-color: var(--color-secondary-light-300)
}

.bg-secondary-light-400 {
    background-color: var(--color-secondary-light-400)
}

.bg-tertiary-300 {
    background-color: var(--color-tertiary-300)
}

.bg-transparent {
    background-color: transparent
}

.bg-violet-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(124 58 237/var(--tw-bg-opacity))
}

.bg-warning {
    background-color: var(--color-warning)
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.bg-white\/5 {
    background-color: hsla(0, 0%, 100%, .05)
}

.bg-yellow-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 249 195/var(--tw-bg-opacity))
}

.bg-yellow-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 252 232/var(--tw-bg-opacity))
}

.bg-yellow-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(234 179 8/var(--tw-bg-opacity))
}

.bg-yellow-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(133 77 14/var(--tw-bg-opacity))
}

.bg-opacity-25 {
    --tw-bg-opacity: 0.25
}

.bg-opacity-50 {
    --tw-bg-opacity: 0.5
}

.bg-opacity-60 {
    --tw-bg-opacity: 0.6
}

.bg-opacity-75 {
    --tw-bg-opacity: 0.75
}

.bg-\[url\(\'\/distributor\/bg-outlet-detail-requested\.jpg\'\)\] {
    background-image: url(/distributor/bg-outlet-detail-requested.jpg)
}

.bg-gradient-to-b {
    background-image: linear-gradient(to bottom, var(--tw-gradient-stops))
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops))
}

.bg-gradient-to-t {
    background-image: linear-gradient(to top, var(--tw-gradient-stops))
}

.from-\[\#008200\] {
    --tw-gradient-from: #008200 var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(0, 130, 0, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#44B238\] {
    --tw-gradient-from: #44b238 var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(68, 178, 56, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#E7FFE9\] {
    --tw-gradient-from: #e7ffe9 var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(231, 255, 233, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[\#F9FAFE\] {
    --tw-gradient-from: #f9fafe var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(249, 250, 254, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-\[rgba\(0\2c 130\2c 0\2c 0\.70\)\] {
    --tw-gradient-from: rgba(0, 130, 0, .7) var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(0, 130, 0, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-black {
    --tw-gradient-from: #000 var(--tw-gradient-from-position);
    --tw-gradient-to: transparent var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-transparent {
    --tw-gradient-from: transparent var(--tw-gradient-from-position);
    --tw-gradient-to: transparent var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.to-\[\#1C4A22\] {
    --tw-gradient-to: #1c4a22 var(--tw-gradient-to-position)
}

.to-\[\#58BF58\] {
    --tw-gradient-to: #58bf58 var(--tw-gradient-to-position)
}

.to-\[\#F3FFF4\] {
    --tw-gradient-to: #f3fff4 var(--tw-gradient-to-position)
}

.to-\[\#FAF6FF\] {
    --tw-gradient-to: #faf6ff var(--tw-gradient-to-position)
}

.to-gray-50 {
    --tw-gradient-to: #f9fafb var(--tw-gradient-to-position)
}

.to-oms-green {
    --tw-gradient-to: #008200 var(--tw-gradient-to-position)
}

.bg-cover {
    background-size: cover
}

.bg-top {
    background-position: top
}

.bg-no-repeat {
    background-repeat: no-repeat
}

.\!object-contain {
    -o-object-fit: contain !important;
    object-fit: contain !important
}

.object-contain {
    -o-object-fit: contain;
    object-fit: contain
}

.\!p-0 {
    padding: 0 !important
}

.\!p-4 {
    padding: 1rem !important
}

.\!p-\[0px\] {
    padding: 0 !important
}

.p-0 {
    padding: 0
}

.p-1 {
    padding: .25rem
}

.p-10 {
    padding: 2.5rem
}

.p-2 {
    padding: .5rem
}

.p-2\.5 {
    padding: .625rem
}

.p-3 {
    padding: .75rem
}

.p-4 {
    padding: 1rem
}

.p-5 {
    padding: 1.25rem
}

.p-6 {
    padding: 1.5rem
}

.p-7 {
    padding: 1.75rem
}

.p-8 {
    padding: 2rem
}

.p-9 {
    padding: 2.25rem
}

.p-\[10px\] {
    padding: 10px
}

.p-\[1rem\] {
    padding: 1rem
}

.p-\[24px\] {
    padding: 24px
}

.p-\[30px\] {
    padding: 30px
}

.\!px-0 {
    padding-left: 0 !important;
    padding-right: 0 !important
}

.\!px-2 {
    padding-left: .5rem !important;
    padding-right: .5rem !important
}

.\!px-\[0px\] {
    padding-left: 0 !important;
    padding-right: 0 !important
}

.\!py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important
}

.\!py-1\.5 {
    padding-top: .375rem !important;
    padding-bottom: .375rem !important
}

.\!py-2 {
    padding-top: .5rem !important;
    padding-bottom: .5rem !important
}

.px-0 {
    padding-left: 0;
    padding-right: 0
}

.px-1 {
    padding-left: .25rem;
    padding-right: .25rem
}

.px-1\.5 {
    padding-left: .375rem;
    padding-right: .375rem
}

.px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem
}

.px-12 {
    padding-left: 3rem;
    padding-right: 3rem
}

.px-2 {
    padding-left: .5rem;
    padding-right: .5rem
}

.px-2\.5 {
    padding-left: .625rem;
    padding-right: .625rem
}

.px-24 {
    padding-left: 6rem;
    padding-right: 6rem
}

.px-3 {
    padding-left: .75rem;
    padding-right: .75rem
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}

.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem
}

.px-7 {
    padding-left: 1.75rem;
    padding-right: 1.75rem
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem
}

.px-9 {
    padding-left: 2.25rem;
    padding-right: 2.25rem
}

.px-\[10px\] {
    padding-left: 10px;
    padding-right: 10px
}

.px-\[12px\] {
    padding-left: 12px;
    padding-right: 12px
}

.px-\[15px\] {
    padding-left: 15px;
    padding-right: 15px
}

.px-\[1px\] {
    padding-left: 1px;
    padding-right: 1px
}

.px-\[30px\] {
    padding-left: 30px;
    padding-right: 30px
}

.px-\[50px\] {
    padding-left: 50px;
    padding-right: 50px
}

.px-\[64px\] {
    padding-left: 64px;
    padding-right: 64px
}

.px-\[6px\] {
    padding-left: 6px;
    padding-right: 6px
}

.px-\[8px\] {
    padding-left: 8px;
    padding-right: 8px
}

.py-0\.5 {
    padding-top: .125rem;
    padding-bottom: .125rem
}

.py-1 {
    padding-top: .25rem;
    padding-bottom: .25rem
}

.py-1\.5 {
    padding-top: .375rem;
    padding-bottom: .375rem
}

.py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem
}

.py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem
}

.py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem
}

.py-2 {
    padding-top: .5rem;
    padding-bottom: .5rem
}

.py-2\.5 {
    padding-top: .625rem;
    padding-bottom: .625rem
}

.py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem
}

.py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem
}

.py-3 {
    padding-top: .75rem;
    padding-bottom: .75rem
}

.py-3\.5 {
    padding-top: .875rem;
    padding-bottom: .875rem
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem
}

.py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem
}

.py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem
}

.py-7 {
    padding-top: 1.75rem;
    padding-bottom: 1.75rem
}

.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem
}

.py-\[10px\] {
    padding-top: 10px;
    padding-bottom: 10px
}

.py-\[14px\] {
    padding-top: 14px;
    padding-bottom: 14px
}

.py-\[1px\] {
    padding-top: 1px;
    padding-bottom: 1px
}

.py-\[4px\] {
    padding-top: 4px;
    padding-bottom: 4px
}

.py-\[5\.5rem\] {
    padding-top: 5.5rem;
    padding-bottom: 5.5rem
}

.py-\[5px\] {
    padding-top: 5px;
    padding-bottom: 5px
}

.\!pb-5 {
    padding-bottom: 1.25rem !important
}

.\!pl-3 {
    padding-left: .75rem !important
}

.\!pl-4 {
    padding-left: 1rem !important
}

.\!pr-4 {
    padding-right: 1rem !important
}

.pb-0 {
    padding-bottom: 0
}

.pb-0\.5 {
    padding-bottom: .125rem
}

.pb-1 {
    padding-bottom: .25rem
}

.pb-1\.5 {
    padding-bottom: .375rem
}

.pb-10 {
    padding-bottom: 2.5rem
}

.pb-12 {
    padding-bottom: 3rem
}

.pb-14 {
    padding-bottom: 3.5rem
}

.pb-2 {
    padding-bottom: .5rem
}

.pb-2\.5 {
    padding-bottom: .625rem
}

.pb-20 {
    padding-bottom: 5rem
}

.pb-24 {
    padding-bottom: 6rem
}

.pb-28 {
    padding-bottom: 7rem
}

.pb-3 {
    padding-bottom: .75rem
}

.pb-4 {
    padding-bottom: 1rem
}

.pb-5 {
    padding-bottom: 1.25rem
}

.pb-6 {
    padding-bottom: 1.5rem
}

.pb-7 {
    padding-bottom: 1.75rem
}

.pb-8 {
    padding-bottom: 2rem
}

.pb-9 {
    padding-bottom: 2.25rem
}

.pb-\[10px\] {
    padding-bottom: 10px
}

.pb-\[15px\] {
    padding-bottom: 15px
}

.pb-\[32px\] {
    padding-bottom: 32px
}

.pb-\[4\.5rem\] {
    padding-bottom: 4.5rem
}

.pl-10 {
    padding-left: 2.5rem
}

.pl-11 {
    padding-left: 2.75rem
}

.pl-12 {
    padding-left: 3rem
}

.pl-2 {
    padding-left: .5rem
}

.pl-3 {
    padding-left: .75rem
}

.pl-4 {
    padding-left: 1rem
}

.pl-5 {
    padding-left: 1.25rem
}

.pl-6 {
    padding-left: 1.5rem
}

.pl-8 {
    padding-left: 2rem
}

.pl-9 {
    padding-left: 2.25rem
}

.pl-\[10px\] {
    padding-left: 10px
}

.pl-\[140px\] {
    padding-left: 140px
}

.pl-\[29px\] {
    padding-left: 29px
}

.pl-\[54px\] {
    padding-left: 54px
}

.pl-\[64px\] {
    padding-left: 64px
}

.pl-\[6px\] {
    padding-left: 6px
}

.pl-\[80px\] {
    padding-left: 80px
}

.pr-0\.5 {
    padding-right: .125rem
}

.pr-1 {
    padding-right: .25rem
}

.pr-10 {
    padding-right: 2.5rem
}

.pr-12 {
    padding-right: 3rem
}

.pr-2 {
    padding-right: .5rem
}

.pr-3 {
    padding-right: .75rem
}

.pr-4 {
    padding-right: 1rem
}

.pr-48 {
    padding-right: 12rem
}

.pr-5 {
    padding-right: 1.25rem
}

.pr-6 {
    padding-right: 1.5rem
}

.pr-8 {
    padding-right: 2rem
}

.pt-0 {
    padding-top: 0
}

.pt-1 {
    padding-top: .25rem
}

.pt-10 {
    padding-top: 2.5rem
}

.pt-12 {
    padding-top: 3rem
}

.pt-2 {
    padding-top: .5rem
}

.pt-2\.5 {
    padding-top: .625rem
}

.pt-24 {
    padding-top: 6rem
}

.pt-3 {
    padding-top: .75rem
}

.pt-4 {
    padding-top: 1rem
}

.pt-5 {
    padding-top: 1.25rem
}

.pt-6 {
    padding-top: 1.5rem
}

.pt-7 {
    padding-top: 1.75rem
}

.pt-8 {
    padding-top: 2rem
}

.pt-\[100\%\] {
    padding-top: 100%
}

.pt-\[38px\] {
    padding-top: 38px
}

.pt-\[45\%\] {
    padding-top: 45%
}

.pt-\[55\%\] {
    padding-top: 55%
}

.pt-\[81px\] {
    padding-top: 81px
}

.pt-\[82px\] {
    padding-top: 82px
}

.pt-\[85\%\] {
    padding-top: 85%
}

.text-left {
    text-align: left
}

.text-center {
    text-align: center
}

.\!text-right {
    text-align: right !important
}

.text-right {
    text-align: right
}

.-indent-\[9999px\] {
    text-indent: -9999px
}

.align-middle {
    vertical-align: middle
}

.font-inter {
    font-family: Inter, sans-serif
}

.\!text-\[11px\] {
    font-size: 11px !important
}

.\!text-\[13px\] {
    font-size: 13px !important
}

.\!text-base {
    font-size: 1rem !important;
    line-height: 1.5rem !important
}

.\!text-xs {
    font-size: .75rem !important;
    line-height: 1rem !important
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem
}

.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem
}

.text-5xl {
    font-size: 3rem;
    line-height: 1
}

.text-\[0\.7578rem\] {
    font-size: .7578rem
}

.text-\[1\.5rem\] {
    font-size: 1.5rem
}

.text-\[10px\] {
    font-size: 10px
}

.text-\[11px\] {
    font-size: 11px
}

.text-\[11px\]\/4 {
    font-size: 11px;
    line-height: 1rem
}

.text-\[13px\] {
    font-size: 13px
}

.text-\[13px\]\/4 {
    font-size: 13px;
    line-height: 1rem
}

.text-\[13px\]\/\[13px\] {
    font-size: 13px;
    line-height: 13px
}

.text-\[13px\]\/\[17px\] {
    font-size: 13px;
    line-height: 17px
}

.text-\[14px\] {
    font-size: 14px
}

.text-\[15px\] {
    font-size: 15px
}

.text-\[16px\] {
    font-size: 16px
}

.text-\[17px\] {
    font-size: 17px
}

.text-\[18px\] {
    font-size: 18px
}

.text-\[19px\] {
    font-size: 19px
}

.text-\[2\.5rem\] {
    font-size: 2.5rem
}

.text-\[20px\] {
    font-size: 20px
}

.text-\[21px\] {
    font-size: 21px
}

.text-\[22px\] {
    font-size: 22px
}

.text-\[2rem\] {
    font-size: 2rem
}

.text-\[30px\] {
    font-size: 30px
}

.text-\[32px\] {
    font-size: 32px
}

.text-\[40px\] {
    font-size: 40px
}

.text-\[66px\] {
    font-size: 66px
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem
}

.text-sm {
    font-size: .875rem;
    line-height: 1.25rem
}

.text-sm\/6 {
    font-size: .875rem;
    line-height: 1.5rem
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem
}

.text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.\!font-medium {
    font-weight: 500 !important
}

.font-bold {
    font-weight: 700
}

.font-extrabold {
    font-weight: 800
}

.font-light {
    font-weight: 300
}

.font-medium {
    font-weight: 500
}

.font-normal {
    font-weight: 400
}

.font-semibold {
    font-weight: 600
}

.font-thin {
    font-weight: 100
}

.uppercase {
    text-transform: uppercase
}

.capitalize {
    text-transform: capitalize
}

.normal-case {
    text-transform: none
}

.italic {
    font-style: italic
}

.not-italic {
    font-style: normal
}

.leading-10 {
    line-height: 2.5rem
}

.leading-5 {
    line-height: 1.25rem
}

.leading-6 {
    line-height: 1.5rem
}

.leading-8 {
    line-height: 2rem
}

.leading-\[1\.25rem\] {
    line-height: 1.25rem
}

.leading-\[2\.125rem\] {
    line-height: 2.125rem
}

.leading-\[20px\] {
    line-height: 20px
}

.leading-\[23\.4px\] {
    line-height: 23.4px
}

.leading-\[24px\] {
    line-height: 24px
}

.leading-\[2rem\] {
    line-height: 2rem
}

.leading-\[30px\] {
    line-height: 30px
}

.leading-\[31\.5px\] {
    line-height: 31.5px
}

.leading-\[3rem\] {
    line-height: 3rem
}

.leading-\[40px\] {
    line-height: 40px
}

.leading-\[44px\] {
    line-height: 44px
}

.leading-\[50px\] {
    line-height: 50px
}

.leading-\[52px\] {
    line-height: 52px
}

.leading-\[60px\] {
    line-height: 60px
}

.leading-\[80px\] {
    line-height: 80px
}

.leading-none {
    line-height: 1
}

.leading-normal {
    line-height: 1.5
}

.leading-tight {
    line-height: 1.25
}

.tracking-normal {
    letter-spacing: 0
}

.tracking-tight {
    letter-spacing: -.025em
}

.tracking-wide {
    letter-spacing: .025em
}

.tracking-widest {
    letter-spacing: .1em
}

.\!text-\[\#3A3D46\] {
    --tw-text-opacity: 1 !important;
    color: rgb(58 61 70/var(--tw-text-opacity)) !important
}

.\!text-\[\#6B7280\] {
    --tw-text-opacity: 1 !important;
    color: rgb(107 114 128/var(--tw-text-opacity)) !important
}

.\!text-oms-gray-10 {
    --tw-text-opacity: 1 !important;
    color: rgb(97 100 107/var(--tw-text-opacity)) !important
}

.\!text-oms-green-6 {
    --tw-text-opacity: 1 !important;
    color: rgb(0 135 0/var(--tw-text-opacity)) !important
}

.\!text-oms-green-7 {
    --tw-text-opacity: 1 !important;
    color: rgb(0 104 0/var(--tw-text-opacity)) !important
}

.\!text-primary {
    color: var(--color-primary) !important
}

.\!text-secondary {
    color: var(--color-secondary) !important
}

.\!text-tertiary {
    color: var(--color-tertiary) !important
}

.\!text-white {
    --tw-text-opacity: 1 !important;
    color: rgb(255 255 255/var(--tw-text-opacity)) !important
}

.text-\[\#006800\] {
    --tw-text-opacity: 1;
    color: rgb(0 104 0/var(--tw-text-opacity))
}

.text-\[\#111827\] {
    --tw-text-opacity: 1;
    color: rgb(17 24 39/var(--tw-text-opacity))
}

.text-\[\#1D429E\] {
    --tw-text-opacity: 1;
    color: rgb(29 66 158/var(--tw-text-opacity))
}

.text-\[\#2E7D32\] {
    --tw-text-opacity: 1;
    color: rgb(46 125 50/var(--tw-text-opacity))
}

.text-\[\#3156B3\] {
    --tw-text-opacity: 1;
    color: rgb(49 86 179/var(--tw-text-opacity))
}

.text-\[\#34B53A\] {
    --tw-text-opacity: 1;
    color: rgb(52 181 58/var(--tw-text-opacity))
}

.text-\[\#363636\] {
    --tw-text-opacity: 1;
    color: rgb(54 54 54/var(--tw-text-opacity))
}

.text-\[\#3A3D46\], .text-\[\#3a3d46\] {
    --tw-text-opacity: 1;
    color: rgb(58 61 70/var(--tw-text-opacity))
}

.text-\[\#4B5563\] {
    --tw-text-opacity: 1;
    color: rgb(75 85 99/var(--tw-text-opacity))
}

.text-\[\#4E5059\] {
    --tw-text-opacity: 1;
    color: rgb(78 80 89/var(--tw-text-opacity))
}

.text-\[\#5D5D5D\] {
    --tw-text-opacity: 1;
    color: rgb(93 93 93/var(--tw-text-opacity))
}

.text-\[\#61646B\] {
    --tw-text-opacity: 1;
    color: rgb(97 100 107/var(--tw-text-opacity))
}

.text-\[\#666666\] {
    --tw-text-opacity: 1;
    color: rgb(102 102 102/var(--tw-text-opacity))
}

.text-\[\#6B7280\] {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity))
}

.text-\[\#6E6E6E\] {
    --tw-text-opacity: 1;
    color: rgb(110 110 110/var(--tw-text-opacity))
}

.text-\[\#75777E\] {
    --tw-text-opacity: 1;
    color: rgb(117 119 126/var(--tw-text-opacity))
}

.text-\[\#767676\] {
    --tw-text-opacity: 1;
    color: rgb(118 118 118/var(--tw-text-opacity))
}

.text-\[\#79DDC4\] {
    --tw-text-opacity: 1;
    color: rgb(121 221 196/var(--tw-text-opacity))
}

.text-\[\#848283\] {
    --tw-text-opacity: 1;
    color: rgb(132 130 131/var(--tw-text-opacity))
}

.text-\[\#888888\] {
    --tw-text-opacity: 1;
    color: rgb(136 136 136/var(--tw-text-opacity))
}

.text-\[\#898888\] {
    --tw-text-opacity: 1;
    color: rgb(137 136 136/var(--tw-text-opacity))
}

.text-\[\#898B90\] {
    --tw-text-opacity: 1;
    color: rgb(137 139 144/var(--tw-text-opacity))
}

.text-\[\#8F02A8\] {
    --tw-text-opacity: 1;
    color: rgb(143 2 168/var(--tw-text-opacity))
}

.text-\[\#989898\] {
    --tw-text-opacity: 1;
    color: rgb(152 152 152/var(--tw-text-opacity))
}

.text-\[\#9AA2A9\] {
    --tw-text-opacity: 1;
    color: rgb(154 162 169/var(--tw-text-opacity))
}

.text-\[\#9CA3AF\] {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity))
}

.text-\[\#9FA3AC\] {
    --tw-text-opacity: 1;
    color: rgb(159 163 172/var(--tw-text-opacity))
}

.text-\[\#B0B1B5\] {
    --tw-text-opacity: 1;
    color: rgb(176 177 181/var(--tw-text-opacity))
}

.text-\[\#B5B5B5\] {
    --tw-text-opacity: 1;
    color: rgb(181 181 181/var(--tw-text-opacity))
}

.text-\[\#CC6100\] {
    --tw-text-opacity: 1;
    color: rgb(204 97 0/var(--tw-text-opacity))
}

.text-\[\#CC7A00\] {
    --tw-text-opacity: 1;
    color: rgb(204 122 0/var(--tw-text-opacity))
}

.text-\[\#E52700\] {
    --tw-text-opacity: 1;
    color: rgb(229 39 0/var(--tw-text-opacity))
}

.text-\[\#E82627\] {
    --tw-text-opacity: 1;
    color: rgb(232 38 39/var(--tw-text-opacity))
}

.text-\[\#EC1C24\] {
    --tw-text-opacity: 1;
    color: rgb(236 28 36/var(--tw-text-opacity))
}

.text-\[\#FDB92C\] {
    --tw-text-opacity: 1;
    color: rgb(253 185 44/var(--tw-text-opacity))
}

.text-\[rgba\(0\2c 0\2c 0\2c 0\.38\)\] {
    color: rgba(0, 0, 0, .38)
}

.text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity))
}

.text-black\/40 {
    color: rgba(0, 0, 0, .4)
}

.text-black\/60 {
    color: rgba(0, 0, 0, .6)
}

.text-blue-300 {
    --tw-text-opacity: 1;
    color: rgb(147 197 253/var(--tw-text-opacity))
}

.text-danger {
    color: var(--color-danger)
}

.text-danger-500 {
    color: var(--color-danger-500)
}

.text-danger-600 {
    color: var(--color-danger-600)
}

.text-danger-700 {
    color: var(--color-danger-700)
}

.text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity))
}

.text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity))
}

.text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99/var(--tw-text-opacity))
}

.text-gray-700 {
    --tw-text-opacity: 1;
    color: rgb(55 65 81/var(--tw-text-opacity))
}

.text-gray-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39/var(--tw-text-opacity))
}

.text-green-400 {
    --tw-text-opacity: 1;
    color: rgb(74 222 128/var(--tw-text-opacity))
}

.text-green-700 {
    --tw-text-opacity: 1;
    color: rgb(21 128 61/var(--tw-text-opacity))
}

.text-info {
    color: var(--color-info)
}

.text-oms-black-1 {
    --tw-text-opacity: 1;
    color: rgb(54 54 54/var(--tw-text-opacity))
}

.text-oms-blue-2 {
    --tw-text-opacity: 1;
    color: rgb(14 128 143/var(--tw-text-opacity))
}

.text-oms-blue-3 {
    --tw-text-opacity: 1;
    color: rgb(42 110 187/var(--tw-text-opacity))
}

.text-oms-gray {
    --tw-text-opacity: 1;
    color: rgb(84 84 84/var(--tw-text-opacity))
}

.text-oms-gray-10 {
    --tw-text-opacity: 1;
    color: rgb(97 100 107/var(--tw-text-opacity))
}

.text-oms-gray-12 {
    --tw-text-opacity: 1;
    color: rgb(78 80 89/var(--tw-text-opacity))
}

.text-oms-gray-13 {
    --tw-text-opacity: 1;
    color: rgb(158 158 158/var(--tw-text-opacity))
}

.text-oms-gray-4 {
    --tw-text-opacity: 1;
    color: rgb(58 61 70/var(--tw-text-opacity))
}

.text-oms-gray-5 {
    --tw-text-opacity: 1;
    color: rgb(117 119 126/var(--tw-text-opacity))
}

.text-oms-green {
    --tw-text-opacity: 1;
    color: rgb(0 130 0/var(--tw-text-opacity))
}

.text-oms-green-12 {
    --tw-text-opacity: 1;
    color: rgb(109 164 0/var(--tw-text-opacity))
}

.text-oms-green-5 {
    --tw-text-opacity: 1;
    color: rgb(1 152 88/var(--tw-text-opacity))
}

.text-oms-green-6 {
    --tw-text-opacity: 1;
    color: rgb(0 135 0/var(--tw-text-opacity))
}

.text-oms-green-7 {
    --tw-text-opacity: 1;
    color: rgb(0 104 0/var(--tw-text-opacity))
}

.text-oms-orange {
    --tw-text-opacity: 1;
    color: rgb(255 184 0/var(--tw-text-opacity))
}

.text-oms-orange-11 {
    --tw-text-opacity: 1;
    color: rgb(229 39 0/var(--tw-text-opacity))
}

.text-oms-orange-4 {
    --tw-text-opacity: 1;
    color: rgb(255 234 178/var(--tw-text-opacity))
}

.text-oms-orange-5 {
    --tw-text-opacity: 1;
    color: rgb(235 155 0/var(--tw-text-opacity))
}

.text-oms-purple {
    --tw-text-opacity: 1;
    color: rgb(132 105 242/var(--tw-text-opacity))
}

.text-oms-red {
    --tw-text-opacity: 1;
    color: rgb(236 28 36/var(--tw-text-opacity))
}

.text-oms-red-2 {
    --tw-text-opacity: 1;
    color: rgb(214 101 110/var(--tw-text-opacity))
}

.text-oms-red-3 {
    --tw-text-opacity: 1;
    color: rgb(179 49 49/var(--tw-text-opacity))
}

.text-pink-400 {
    --tw-text-opacity: 1;
    color: rgb(244 114 182/var(--tw-text-opacity))
}

.text-primary {
    color: var(--color-primary)
}

.text-primary-light {
    color: var(--color-primary-light)
}

.text-red-400 {
    --tw-text-opacity: 1;
    color: rgb(248 113 113/var(--tw-text-opacity))
}

.text-red-500 {
    --tw-text-opacity: 1;
    color: rgb(239 68 68/var(--tw-text-opacity))
}

.text-red-700 {
    --tw-text-opacity: 1;
    color: rgb(185 28 28/var(--tw-text-opacity))
}

.text-rose-500 {
    --tw-text-opacity: 1;
    color: rgb(244 63 94/var(--tw-text-opacity))
}

.text-rose-600 {
    --tw-text-opacity: 1;
    color: rgb(225 29 72/var(--tw-text-opacity))
}

.text-secondary {
    color: var(--color-secondary)
}

.text-secondary-400 {
    color: var(--color-secondary-400)
}

.text-secondary-500 {
    color: var(--color-secondary-500)
}

.text-secondary-700 {
    color: var(--color-secondary-700)
}

.text-secondary-900 {
    color: var(--color-secondary-900)
}

.text-tertiary {
    color: var(--color-tertiary)
}

.text-tertiary-300 {
    color: var(--color-tertiary-300)
}

.text-violet-600 {
    --tw-text-opacity: 1;
    color: rgb(124 58 237/var(--tw-text-opacity))
}

.text-warning {
    color: var(--color-warning)
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.text-yellow-400 {
    --tw-text-opacity: 1;
    color: rgb(250 204 21/var(--tw-text-opacity))
}

.text-yellow-500 {
    --tw-text-opacity: 1;
    color: rgb(234 179 8/var(--tw-text-opacity))
}

.text-yellow-700 {
    --tw-text-opacity: 1;
    color: rgb(161 98 7/var(--tw-text-opacity))
}

.text-yellow-800 {
    --tw-text-opacity: 1;
    color: rgb(133 77 14/var(--tw-text-opacity))
}

.text-opacity-60 {
    --tw-text-opacity: 0.6
}

.text-opacity-\[\.87\] {
    --tw-text-opacity: .87
}

.underline {
    text-decoration-line: underline
}

.line-through {
    text-decoration-line: line-through
}

.decoration-oms-green {
    text-decoration-color: #008200
}

.decoration-\[3px\] {
    text-decoration-thickness: 3px
}

.placeholder-\[\#848283\]::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(132 130 131/var(--tw-placeholder-opacity))
}

.placeholder-\[\#848283\]::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(132 130 131/var(--tw-placeholder-opacity))
}

.placeholder-\[\#898B90\]::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(137 139 144/var(--tw-placeholder-opacity))
}

.placeholder-\[\#898B90\]::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(137 139 144/var(--tw-placeholder-opacity))
}

.placeholder-oms-gray-7::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(137 139 144/var(--tw-placeholder-opacity))
}

.placeholder-oms-gray-7::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(137 139 144/var(--tw-placeholder-opacity))
}

.accent-amber-200 {
    accent-color: #fde68a
}

.\!opacity-0 {
    opacity: 0 !important
}

.\!opacity-100 {
    opacity: 1 !important
}

.opacity-0 {
    opacity: 0
}

.opacity-100 {
    opacity: 1
}

.opacity-20 {
    opacity: .2
}

.opacity-25 {
    opacity: .25
}

.opacity-30 {
    opacity: .3
}

.opacity-40 {
    opacity: .4
}

.opacity-50 {
    opacity: .5
}

.opacity-60 {
    opacity: .6
}

.opacity-75 {
    opacity: .75
}

.opacity-80 {
    opacity: .8
}

.\!shadow-\[inset_0px_-2px_0px_\#E51A7F\] {
    --tw-shadow: inset 0px -2px 0px #e51a7f !important;
    --tw-shadow-colored: inset 0px -2px 0px var(--tw-shadow-color) !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important
}

.shadow {
    --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color)
}

.shadow, .shadow-2xl {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-2xl {
    --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color)
}

.shadow-\[0_-35px_40px_-50px_rgba\(0\2c 0\2c 0\2c 0\.3\)\] {
    --tw-shadow: 0 -35px 40px -50px rgba(0, 0, 0, .3);
    --tw-shadow-colored: 0 -35px 40px -50px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0_0px_20px_0px_rgba\(0\2c 0\2c 0\2c 0\.15\)\] {
    --tw-shadow: 0 0px 20px 0px rgba(0, 0, 0, .15);
    --tw-shadow-colored: 0 0px 20px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0_10px_0_0_\#008943\] {
    --tw-shadow: 0 10px 0 0 #008943;
    --tw-shadow-colored: 0 10px 0 0 var(--tw-shadow-color)
}

.shadow-\[0_10px_0_0_\#008943\], .shadow-\[0_10px_0_0_\#E2E2E2\] {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0_10px_0_0_\#E2E2E2\] {
    --tw-shadow: 0 10px 0 0 #e2e2e2;
    --tw-shadow-colored: 0 10px 0 0 var(--tw-shadow-color)
}

.shadow-\[0_16px_40px_0_\#0E308712\] {
    --tw-shadow: 0 16px 40px 0 #0e308712;
    --tw-shadow-colored: 0 16px 40px 0 var(--tw-shadow-color)
}

.shadow-\[0_16px_40px_0_\#0E308712\], .shadow-\[0_16px_40px_0px_rgba\(14\2c 48\2c 135\2c 0\.07\)\] {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0_16px_40px_0px_rgba\(14\2c 48\2c 135\2c 0\.07\)\] {
    --tw-shadow: 0 16px 40px 0px rgba(14, 48, 135, .07);
    --tw-shadow-colored: 0 16px 40px 0px var(--tw-shadow-color)
}

.shadow-\[0_1px_14px_0px_\#00000015\] {
    --tw-shadow: 0 1px 14px 0px #00000015;
    --tw-shadow-colored: 0 1px 14px 0px var(--tw-shadow-color)
}

.shadow-\[0_1px_14px_0px_\#00000015\], .shadow-\[0_6px_24px_0_rgba\(14\2c 48\2c 135\2c 0\.07\)\] {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0_6px_24px_0_rgba\(14\2c 48\2c 135\2c 0\.07\)\] {
    --tw-shadow: 0 6px 24px 0 rgba(14, 48, 135, .07);
    --tw-shadow-colored: 0 6px 24px 0 var(--tw-shadow-color)
}

.shadow-\[0px_2px_16px_0px_\#32323224\] {
    --tw-shadow: 0px 2px 16px 0px #32323224;
    --tw-shadow-colored: 0px 2px 16px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0px_6px_24px_0px_rgba\(14\2c 48\2c 135\2c 0\.07\)\] {
    --tw-shadow: 0px 6px 24px 0px rgba(14, 48, 135, .07);
    --tw-shadow-colored: 0px 6px 24px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[2px_0px_20px_-12px_rgba\(0\2c 0\2c 0\2c 0\.4\)\] {
    --tw-shadow: 2px 0px 20px -12px rgba(0, 0, 0, .4);
    --tw-shadow-colored: 2px 0px 20px -12px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[inset_0px_-1px_0px_rgba\(255\2c 255\2c 255\2c 0\.25\)\] {
    --tw-shadow: inset 0px -1px 0px hsla(0, 0%, 100%, .25);
    --tw-shadow-colored: inset 0px -1px 0px var(--tw-shadow-color)
}

.shadow-\[inset_0px_-1px_0px_rgba\(255\2c 255\2c 255\2c 0\.25\)\], .shadow-bold {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-bold {
    --tw-shadow: 0px 4px 4px 0px rgba(0, 0, 0, .25);
    --tw-shadow-colored: 0px 4px 4px 0px var(--tw-shadow-color)
}

.shadow-card {
    --tw-shadow: 0px 4px 10px 0px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0px 4px 10px 0px var(--tw-shadow-color)
}

.shadow-card, .shadow-inner {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, .05);
    --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color)
}

.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color)
}

.shadow-lg, .shadow-md {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-md {
    --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color)
}

.shadow-navbar {
    --tw-shadow: 0px 0px 17px 0px rgba(0, 0, 0, .15);
    --tw-shadow-colored: 0px 0px 17px 0px var(--tw-shadow-color)
}

.shadow-navbar, .shadow-none {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-none {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000
}

.shadow-promotion {
    --tw-shadow: 0px 0px 20px 0px rgba(0, 0, 0, .15);
    --tw-shadow-colored: 0px 0px 20px 0px var(--tw-shadow-color)
}

.shadow-promotion, .shadow-section {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-section {
    --tw-shadow: 0px 6px 20px 0px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0px 6px 20px 0px var(--tw-shadow-color)
}

.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color)
}

.shadow-sm, .shadow-tab {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-tab {
    --tw-shadow: 0px 0px 16px 0px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0px 0px 16px 0px var(--tw-shadow-color)
}

.shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-black {
    --tw-shadow-color: #000;
    --tw-shadow: var(--tw-shadow-colored)
}

.shadow-gray-300\/50 {
    --tw-shadow-color: rgba(209, 213, 219, .5);
    --tw-shadow: var(--tw-shadow-colored)
}

.\!outline-none {
    outline: 2px solid transparent !important;
    outline-offset: 2px !important
}

.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.outline {
    outline-style: solid
}

.ring-1 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.ring-1, .ring-2 {
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.ring-2 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.ring-inset {
    --tw-ring-inset: inset
}

.ring-\[\#E6E6E6\] {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(230 230 230/var(--tw-ring-opacity))
}

.ring-black {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 0 0/var(--tw-ring-opacity))
}

.ring-black\/5 {
    --tw-ring-color: rgba(0, 0, 0, .05)
}

.ring-gray-300 {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(209 213 219/var(--tw-ring-opacity))
}

.ring-oms-gray-2 {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(205 205 205/var(--tw-ring-opacity))
}

.ring-oms-green {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 130 0/var(--tw-ring-opacity))
}

.ring-oms-red {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(236 28 36/var(--tw-ring-opacity))
}

.ring-opacity-5 {
    --tw-ring-opacity: 0.05
}

.blur {
    --tw-blur: blur(8px)
}

.blur, .brightness-50 {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.brightness-50 {
    --tw-brightness: brightness(.5)
}

.contrast-75 {
    --tw-contrast: contrast(.75)
}

.contrast-75, .drop-shadow-md {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.drop-shadow-md {
    --tw-drop-shadow: drop-shadow(0 4px 3px rgba(0, 0, 0, .07)) drop-shadow(0 2px 2px rgba(0, 0, 0, .06))
}

.drop-shadow-xl {
    --tw-drop-shadow: drop-shadow(0 20px 13px rgba(0, 0, 0, .03)) drop-shadow(0 8px 5px rgba(0, 0, 0, .08))
}

.drop-shadow-xl, .grayscale {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.grayscale {
    --tw-grayscale: grayscale(100%)
}

.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.backdrop-blur-2xl {
    --tw-backdrop-blur: blur(40px)
}

.backdrop-blur-2xl, .backdrop-blur-sm {
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)
}

.backdrop-blur-sm {
    --tw-backdrop-blur: blur(4px)
}

.transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-\[max-height\2c opacity\] {
    transition-property: max-height, opacity;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.duration-100 {
    transition-duration: .1s
}

.duration-150 {
    transition-duration: .15s
}

.duration-200 {
    transition-duration: .2s
}

.duration-300 {
    transition-duration: .3s
}

.duration-500 {
    transition-duration: .5s
}

.duration-700 {
    transition-duration: .7s
}

.duration-75 {
    transition-duration: 75ms
}

.ease-in {
    transition-timing-function: cubic-bezier(.4, 0, 1, 1)
}

.ease-in-out {
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.ease-out {
    transition-timing-function: cubic-bezier(0, 0, .2, 1)
}

.no-scrollbar::-webkit-scrollbar {
    display: none
}

.no-scrollbar::-webkit-scrollbar-track {
    background-color: transparent
}

.no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none
}

input:not(:-moz-placeholder-shown) ~ label {
    font-size: 10px;
    font-weight: 400;
    line-height: 1rem;
    --tw-translate-y: -0.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

input:focus ~ label, input:not(:placeholder-shown) ~ label {
    font-size: 10px;
    font-weight: 400;
    line-height: 1rem;
    --tw-translate-y: -0.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

input:focus ~ label {
    left: 0
}

input:-webkit-autofill, input:-webkit-autofill:focus, input:-webkit-autofill:hover, select:-webkit-autofill, select:-webkit-autofill:focus, select:-webkit-autofill:hover, textarea:-webkit-autofill, textarea:-webkit-autofill:focus, textarea:-webkit-autofill:hover {
    border: 1px solid #212121;
    -webkit-text-fill-color: #212121;
    -webkit-box-shadow: inset 0 0 0 1000px #fff;
    -webkit-transition: background-color 5000s ease-in-out 0s;
    transition: background-color 5000s ease-in-out 0s
}

input[type=search]::-webkit-search-cancel-button, input[type=search]::-webkit-search-decoration, input[type=search]::-webkit-search-results-button, input[type=search]::-webkit-search-results-decoration {
    -webkit-appearance: none
}

.\[--anchor-gap\:var\(--spacing-5\)\] {
    --anchor-gap: var(--spacing-5)
}

@font-face {
    font-family: Heineken;
    src: url(/_next/static/media/heineken-webfont.01474fca.woff2) format("woff2"), url(/_next/static/media/heineken-webfont.d98c6ca2.woff) format("woff");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-Light.0b4ff3e0.ttf) format("truetype");
    font-weight: 300;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-Regular.1282ebc6.ttf) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-Medium.8d213a54.ttf) format("truetype");
    font-weight: 500;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-SemiBold.524c2f2b.ttf) format("truetype");
    font-weight: 600;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-Bold.c146dcab.ttf) format("truetype");
    font-weight: 700;
    font-style: normal
}

@font-face {
    font-family: Inter;
    src: url(/_next/static/media/Inter-ExtraBold.6e29c005.ttf) format("truetype");
    font-weight: 800;
    font-style: normal
}

button.active-filter {
    background-color: #111316 !important;
    color: #fff !important
}

.plp-button button {
    width: 100%;
    padding-left: 0;
    padding-right: 0
}

.plp-button > div {
    display: flex;
    justify-content: center
}

@media (min-width: 768px) {
    .plp-button button {
        width: 151px
    }

    .pdp-button, .pdp-button button {
        width: 100% !important
    }
}

.eventRegistrationPage main, .oms-redemptionPage main, .subscriptionPage main {
    display: flex;
    flex-direction: column
}

.beerCiderPage .storyblockTeaser, .eventRegistrationPage .storyblockTeaser, .oms-redemptionPage .storyblockTeaser, .oms-redemptionTermsAndConditionsPage .storyblockTeaser, .subscriptionPage .storyblockTeaser {
    padding-top: 40px
}

.homepage .storyblockTeaser {
    max-width: 1280px;
    margin: 0 auto
}

.oms-redemptionPage .storyblockTeaser {
    max-width: 992px;
    margin: 0 auto
}

.eventRegistrationPage .storyblockTeaser, .oms-redemptionTermsAndConditionsPage .storyblockTeaser {
    max-width: 800px;
    margin: 0 auto
}

.storyblockBanner {
    order: -1
}

.banner-img, .storyblockBanner span {
    height: auto !important;
    position: static !important
}

.banner-img {
    width: 100% !important
}

.eventRegistrationPage form > div:first-child {
    width: calc(50% - 8px);
    float: left
}

.eventRegistrationPage form > div:first-child + div {
    width: calc(50% - 8px);
    margin-left: 16px;
    float: left
}

.eventRegistrationPage form > div:nth-child(3n) {
    clear: both
}

@media (min-width: 768px) {
    .eventRegistrationPage .storyblockTeaser, .oms-redemptionPage .storyblockTeaser {
        padding-top: 60px
    }
}

.account-dropdown {
    filter: drop-shadow(0 2px 16px rgba(50, 50, 50, .12));
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    display: block !important
}

.account-dropdown:before {
    content: "";
    position: absolute;
    top: -15px;
    right: 62px;
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 16px solid #fff
}

.account-dropdown ul {
    padding: 8px 0
}

.account-dropdown ul li {
    padding: 12px 0
}

.account-dropdown ul li a {
    font-size: 16px;
    display: flex;
    justify-content: space-between
}

.account-dropdown ul li a .dropdown-icon, .account-dropdown ul li button .dropdown-icon {
    width: calc(100% - 186px)
}

.account-dropdown ul li a .dropdown-label, .account-dropdown ul li button .dropdown-label {
    width: 186px;
    text-align: left
}

@media (min-width: 1024px) {
    .account-dropdown:before {
        right: 11px
    }
}

.without-icon-close + .absolute.right-4.top-4 {
    display: none
}

.tooltip {
    z-index: 1
}

.tooltip:after {
    content: "";
    position: absolute;
    bottom: -9px;
    left: calc(50% - 9px);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 10px solid #fff
}

@media (max-width: 600px) {
    .tooltip.arrow-left-mobile:after {
        left: 7px
    }
}

.react-datepicker-popper[data-placement^=bottom] {
    padding-top: 6px !important
}

.placeholder\:font-medium::-moz-placeholder {
    font-weight: 500
}

.placeholder\:font-medium::placeholder {
    font-weight: 500
}

.placeholder\:font-normal::-moz-placeholder {
    font-weight: 400
}

.placeholder\:font-normal::placeholder {
    font-weight: 400
}

.placeholder\:text-secondary::-moz-placeholder {
    color: var(--color-secondary)
}

.placeholder\:text-secondary::placeholder {
    color: var(--color-secondary)
}

.before\:absolute:before {
    content: var(--tw-content);
    position: absolute
}

.before\:right-0:before {
    content: var(--tw-content);
    right: 0
}

.before\:top-\[-20px\]:before {
    content: var(--tw-content);
    top: -20px
}

.before\:h-10:before {
    content: var(--tw-content);
    height: 2.5rem
}

.before\:w-\[90px\]:before {
    content: var(--tw-content);
    width: 90px
}

.before\:bg-white:before {
    content: var(--tw-content);
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.after\:absolute:after {
    content: var(--tw-content);
    position: absolute
}

.after\:left-1\/2:after, .after\:left-2\/4:after {
    content: var(--tw-content);
    left: 50%
}

.after\:top-\[70\%\]:after {
    content: var(--tw-content);
    top: 70%
}

.after\:top-full:after {
    content: var(--tw-content);
    top: 100%
}

.after\:mx-1:after {
    content: var(--tw-content);
    margin-left: .25rem;
    margin-right: .25rem
}

.after\:ml-\[-22px\]:after {
    content: var(--tw-content);
    margin-left: -22px
}

.after\:ml-\[-45px\]:after {
    content: var(--tw-content);
    margin-left: -45px
}

.after\:block:after {
    content: var(--tw-content);
    display: block
}

.after\:h-0\.5:after {
    content: var(--tw-content);
    height: .125rem
}

.after\:h-\[33px\]:after {
    content: var(--tw-content);
    height: 33px
}

.after\:w-11:after {
    content: var(--tw-content);
    width: 2.75rem
}

.after\:w-\[105px\]:after {
    content: var(--tw-content);
    width: 105px
}

.after\:\!bg-oms-green-7:after {
    content: var(--tw-content);
    --tw-bg-opacity: 1 !important;
    background-color: rgb(0 104 0/var(--tw-bg-opacity)) !important
}

.after\:bg-transparent:after {
    content: var(--tw-content);
    background-color: transparent
}

.after\:bg-\[url\(\'\/price\.svg\'\)\]:after {
    content: var(--tw-content);
    background-image: url(/price.svg)
}

.after\:bg-no-repeat:after {
    content: var(--tw-content);
    background-repeat: no-repeat
}

.after\:content-\[\'\'\]:after {
    --tw-content: "";
    content: var(--tw-content)
}

.after\:content-\[\'\/\'\]:after {
    --tw-content: "/";
    content: var(--tw-content)
}

.first\:border-l-2:first-child {
    border-left-width: 2px
}

.first\:border-l-oms-gray-10:first-child {
    --tw-border-opacity: 1;
    border-left-color: rgb(97 100 107/var(--tw-border-opacity))
}

.first\:border-l-oms-green-6:first-child {
    --tw-border-opacity: 1;
    border-left-color: rgb(0 135 0/var(--tw-border-opacity))
}

.first\:border-l-oms-orange-10:first-child {
    --tw-border-opacity: 1;
    border-left-color: rgb(255 121 0/var(--tw-border-opacity))
}

.first\:border-l-oms-orange-9:first-child {
    --tw-border-opacity: 1;
    border-left-color: rgb(255 184 16/var(--tw-border-opacity))
}

.first\:pl-2:first-child {
    padding-left: .5rem
}

.first\:pt-0:first-child {
    padding-top: 0
}

.last\:mb-4:last-child {
    margin-bottom: 1rem
}

.last\:border-b-0:last-child {
    border-bottom-width: 0
}

.last\:border-none:last-child {
    border-style: none
}

.last\:pb-0:last-child {
    padding-bottom: 0
}

.valid\:mt-3:valid {
    margin-top: .75rem
}

.valid\:mt-\[17px\]:valid {
    margin-top: 17px
}

.valid\:h-6:valid {
    height: 1.5rem
}

.focus-within\:mt-3:focus-within {
    margin-top: .75rem
}

.focus-within\:mt-\[17px\]:focus-within {
    margin-top: 17px
}

.focus-within\:h-6:focus-within {
    height: 1.5rem
}

.focus-within\:text-secondary-400:focus-within {
    color: var(--color-secondary-400)
}

.focus-within\:shadow-lg:focus-within {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.focus-within\:\!ring-0:focus-within {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important
}

.focus-within\:ring-2:focus-within {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-within\:ring-oms-green:focus-within {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 130 0/var(--tw-ring-opacity))
}

.hover\:text-clip:hover {
    text-overflow: clip
}

.hover\:rounded-full:hover {
    border-radius: 9999px
}

.hover\:border-none:hover {
    border-style: none
}

.hover\:border-oms-gray-2:hover {
    --tw-border-opacity: 1;
    border-color: rgb(205 205 205/var(--tw-border-opacity))
}

.hover\:border-oms-green-3:hover {
    --tw-border-opacity: 1;
    border-color: rgb(104 215 139/var(--tw-border-opacity))
}

.hover\:border-oms-green-7:hover {
    --tw-border-opacity: 1;
    border-color: rgb(0 104 0/var(--tw-border-opacity))
}

.hover\:border-oms-orange-3:hover {
    --tw-border-opacity: 1;
    border-color: rgb(211 185 115/var(--tw-border-opacity))
}

.hover\:border-oms-purple-3:hover {
    --tw-border-opacity: 1;
    border-color: rgb(145 128 216/var(--tw-border-opacity))
}

.hover\:\!bg-primary-light:hover {
    background-color: var(--color-primary-light) !important
}

.hover\:\!bg-secondary-100:hover {
    background-color: var(--color-secondary-100) !important
}

.hover\:\!bg-transparent:hover {
    background-color: transparent !important
}

.hover\:bg-\[\#3b5998\]\/90:hover {
    background-color: rgba(59, 89, 152, .9)
}

.hover\:bg-\[\#F1FCF1\]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(241 252 241/var(--tw-bg-opacity))
}

.hover\:bg-\[\#f9f9f9\]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(249 249 249/var(--tw-bg-opacity))
}

.hover\:bg-black:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity))
}

.hover\:bg-gray-200:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity))
}

.hover\:bg-gray-50:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251/var(--tw-bg-opacity))
}

.hover\:bg-neutral-100:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 245/var(--tw-bg-opacity))
}

.hover\:bg-oms-gray-5:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(117 119 126/var(--tw-bg-opacity))
}

.hover\:bg-oms-green:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(0 130 0/var(--tw-bg-opacity))
}

.hover\:bg-oms-green-11:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(232 244 232/var(--tw-bg-opacity))
}

.hover\:bg-oms-green-4:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(236 255 242/var(--tw-bg-opacity))
}

.hover\:bg-oms-orange-4:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 234 178/var(--tw-bg-opacity))
}

.hover\:bg-oms-purple-4:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(215 206 251/var(--tw-bg-opacity))
}

.hover\:bg-oms-white:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(241 252 241/var(--tw-bg-opacity))
}

.hover\:bg-primary:hover {
    background-color: var(--color-primary)
}

.hover\:bg-secondary-100:hover {
    background-color: var(--color-secondary-100)
}

.hover\:bg-secondary-200:hover {
    background-color: var(--color-secondary-200)
}

.hover\:bg-secondary-400:hover {
    background-color: var(--color-secondary-400)
}

.hover\:bg-white:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.hover\:bg-opacity-10:hover {
    --tw-bg-opacity: 0.1
}

.hover\:from-\[\#E7FFE9\]:hover {
    --tw-gradient-from: #e7ffe9 var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(231, 255, 233, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.hover\:to-\[\#F3FFF4\]:hover {
    --tw-gradient-to: #f3fff4 var(--tw-gradient-to-position)
}

.hover\:pl-4:hover {
    padding-left: 1rem
}

.hover\:pr-1:hover {
    padding-right: .25rem
}

.hover\:font-bold:hover {
    font-weight: 700
}

.hover\:text-black:hover {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity))
}

.hover\:text-gray-500:hover {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity))
}

.hover\:text-gray-600:hover {
    --tw-text-opacity: 1;
    color: rgb(75 85 99/var(--tw-text-opacity))
}

.hover\:text-oms-green:hover {
    --tw-text-opacity: 1;
    color: rgb(0 130 0/var(--tw-text-opacity))
}

.hover\:text-oms-green-6:hover {
    --tw-text-opacity: 1;
    color: rgb(0 135 0/var(--tw-text-opacity))
}

.hover\:text-oms-green-7:hover {
    --tw-text-opacity: 1;
    color: rgb(0 104 0/var(--tw-text-opacity))
}

.hover\:text-primary:hover {
    color: var(--color-primary)
}

.hover\:text-secondary-900:hover {
    color: var(--color-secondary-900)
}

.hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.hover\:underline:hover {
    text-decoration-line: underline
}

.hover\:opacity-100:hover {
    opacity: 1
}

.hover\:opacity-70:hover {
    opacity: .7
}

.hover\:opacity-80:hover {
    opacity: .8
}

.hover\:after\:bg-oms-green-7:hover:after {
    content: var(--tw-content);
    --tw-bg-opacity: 1;
    background-color: rgb(0 104 0/var(--tw-bg-opacity))
}

.focus\:border-blue-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246/var(--tw-border-opacity))
}

.focus\:border-gray-400:focus {
    --tw-border-opacity: 1;
    border-color: rgb(156 163 175/var(--tw-border-opacity))
}

.focus\:border-rose-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(244 63 94/var(--tw-border-opacity))
}

.focus\:border-secondary-400:focus {
    border-color: var(--color-secondary-400)
}

.focus\:border-secondary-700:focus {
    border-color: var(--color-secondary-700)
}

.focus\:text-gray-700:focus {
    --tw-text-opacity: 1;
    color: rgb(55 65 81/var(--tw-text-opacity))
}

.focus\:text-secondary-700:focus {
    color: var(--color-secondary-700)
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus\:outline:focus {
    outline-style: solid
}

.focus\:outline-1:focus {
    outline-width: 1px
}

.focus\:outline-oms-green:focus {
    outline-color: #008200
}

.focus\:ring-0:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.focus\:ring-0:focus, .focus\:ring-2:focus {
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.focus\:ring-4:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-\[\#3b5998\]\/50:focus {
    --tw-ring-color: rgba(59, 89, 152, .5)
}

.focus\:ring-blue-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(59 130 246/var(--tw-ring-opacity))
}

.focus\:ring-indigo-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(99 102 241/var(--tw-ring-opacity))
}

.focus-visible\:border-gray-300:focus-visible {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity))
}

.focus-visible\:ring-2:focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-visible\:ring-oms-green:focus-visible {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 130 0/var(--tw-ring-opacity))
}

.focus-visible\:ring-white:focus-visible {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(255 255 255/var(--tw-ring-opacity))
}

.focus-visible\:ring-opacity-75:focus-visible {
    --tw-ring-opacity: 0.75
}

.focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px
}

.focus-visible\:ring-offset-gray-300:focus-visible {
    --tw-ring-offset-color: #d1d5db
}

.active\:translate-y-1:active {
    --tw-translate-y: 0.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.active\:bg-\[\#004800\]:active {
    --tw-bg-opacity: 1;
    background-color: rgb(0 72 0/var(--tw-bg-opacity))
}

.active\:bg-\[\#d8f5d8\]:active {
    --tw-bg-opacity: 1;
    background-color: rgb(216 245 216/var(--tw-bg-opacity))
}

.active\:bg-oms-green:active {
    --tw-bg-opacity: 1;
    background-color: rgb(0 130 0/var(--tw-bg-opacity))
}

.active\:text-white:active {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.active\:opacity-50:active {
    opacity: .5
}

.active\:shadow-\[0_6px_0_0_\#006643\]:active {
    --tw-shadow: 0 6px 0 0 #006643;
    --tw-shadow-colored: 0 6px 0 0 var(--tw-shadow-color)
}

.active\:shadow-\[0_6px_0_0_\#006643\]:active, .active\:shadow-\[0_6px_0_0_\#E2E2E2\]:active {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.active\:shadow-\[0_6px_0_0_\#E2E2E2\]:active {
    --tw-shadow: 0 6px 0 0 #e2e2e2;
    --tw-shadow-colored: 0 6px 0 0 var(--tw-shadow-color)
}

.disabled\:pointer-events-none:disabled {
    pointer-events: none
}

.disabled\:cursor-default:disabled {
    cursor: default
}

.disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed
}

.disabled\:bg-inherit:disabled {
    background-color: inherit
}

.disabled\:bg-oms-gray-2:disabled {
    --tw-bg-opacity: 1;
    background-color: rgb(205 205 205/var(--tw-bg-opacity))
}

.disabled\:bg-oms-gray-6:disabled {
    --tw-bg-opacity: 1;
    background-color: rgb(216 216 218/var(--tw-bg-opacity))
}

.disabled\:font-normal:disabled {
    font-weight: 400
}

.disabled\:\!text-\[\#4B5563\]:disabled {
    --tw-text-opacity: 1 !important;
    color: rgb(75 85 99/var(--tw-text-opacity)) !important
}

.disabled\:text-oms-gray-4:disabled {
    --tw-text-opacity: 1;
    color: rgb(58 61 70/var(--tw-text-opacity))
}

.disabled\:text-oms-gray-5:disabled {
    --tw-text-opacity: 1;
    color: rgb(117 119 126/var(--tw-text-opacity))
}

.disabled\:text-opacity-40:disabled {
    --tw-text-opacity: 0.4
}

.disabled\:\!opacity-100:disabled {
    opacity: 1 !important
}

.disabled\:opacity-30:disabled {
    opacity: .3
}

.disabled\:opacity-40:disabled {
    opacity: .4
}

.disabled\:opacity-50:disabled {
    opacity: .5
}

.disabled\:opacity-60:disabled {
    opacity: .6
}

.disabled\:after\:bg-transparent:disabled:after {
    content: var(--tw-content);
    background-color: transparent
}

.disabled\:hover\:bg-gray-900:hover:disabled {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39/var(--tw-bg-opacity))
}

.disabled\:hover\:bg-secondary-900:hover:disabled {
    background-color: var(--color-secondary-900)
}

.disabled\:hover\:bg-transparent:hover:disabled {
    background-color: transparent
}

.disabled\:hover\:text-\[rgba\(0\2c 0\2c 0\2c 0\.38\)\]:hover:disabled {
    color: rgba(0, 0, 0, .38)
}

.disabled\:hover\:text-white:hover:disabled {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.group:focus-within .group-focus-within\:left-4 {
    left: 1rem
}

.group:focus-within .group-focus-within\:\!text-xs {
    font-size: .75rem !important;
    line-height: 1rem !important
}

.group:focus-within .group-focus-within\:opacity-100 {
    opacity: 1
}

.group:hover .group-hover\:ml-2 {
    margin-left: .5rem
}

.group:hover .group-hover\:block {
    display: block
}

.group:hover .group-hover\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:bg-oms-green-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.group:hover .group-hover\:bg-oms-orange {
    --tw-bg-opacity: 1;
    background-color: rgb(255 184 0/var(--tw-bg-opacity))
}

.group:hover .group-hover\:bg-oms-purple {
    --tw-bg-opacity: 1;
    background-color: rgb(132 105 242/var(--tw-bg-opacity))
}

.group:hover .group-hover\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.group:hover .group-hover\:text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity))
}

.group:hover .group-hover\:text-oms-green-5 {
    --tw-text-opacity: 1;
    color: rgb(1 152 88/var(--tw-text-opacity))
}

.group:hover .group-hover\:text-oms-orange-4 {
    --tw-text-opacity: 1;
    color: rgb(255 234 178/var(--tw-text-opacity))
}

.group:hover .group-hover\:text-oms-purple-4 {
    --tw-text-opacity: 1;
    color: rgb(215 206 251/var(--tw-text-opacity))
}

.group.is-active .group-\[\.is-active\]\:bg-oms-green-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.group.is-active .group-\[\.is-active\]\:bg-oms-orange {
    --tw-bg-opacity: 1;
    background-color: rgb(255 184 0/var(--tw-bg-opacity))
}

.group.is-active .group-\[\.is-active\]\:text-oms-green-5 {
    --tw-text-opacity: 1;
    color: rgb(1 152 88/var(--tw-text-opacity))
}

.group.is-active .group-\[\.is-active\]\:text-oms-orange-4 {
    --tw-text-opacity: 1;
    color: rgb(255 234 178/var(--tw-text-opacity))
}

.peer:checked ~ .peer-checked\:border-primary {
    border-color: var(--color-primary)
}

.peer:valid ~ .peer-valid\:left-4 {
    left: 1rem
}

.peer:valid ~ .peer-valid\:\!text-xs {
    font-size: .75rem !important;
    line-height: 1rem !important
}

.peer:valid ~ .peer-valid\:text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.peer:valid ~ .peer-valid\:opacity-100 {
    opacity: 1
}

.peer:focus-within ~ .peer-focus-within\:text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.data-\[closed\]\:-translate-y-1[data-closed] {
    --tw-translate-y: -0.25rem
}

.data-\[closed\]\:-translate-y-1[data-closed], .data-\[closed\]\:-translate-y-6[data-closed] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[closed\]\:-translate-y-6[data-closed] {
    --tw-translate-y: -1.5rem
}

.data-\[closed\]\:translate-x-full[data-closed] {
    --tw-translate-x: 100%
}

.data-\[closed\]\:translate-x-full[data-closed], .data-\[closed\]\:translate-y-full[data-closed] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[closed\]\:translate-y-full[data-closed] {
    --tw-translate-y: 100%
}

.data-\[closed\]\:scale-95[data-closed] {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.data-\[disabled\]\:cursor-not-allowed[data-disabled] {
    cursor: not-allowed
}

.data-\[checked\]\:border-none[data-checked] {
    border-style: none
}

.data-\[active\]\:bg-\[\#F3F4F6\][data-active] {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity))
}

.data-\[checked\]\:bg-oms-green-6[data-checked] {
    --tw-bg-opacity: 1;
    background-color: rgb(0 135 0/var(--tw-bg-opacity))
}

.data-\[checked\]\:data-\[disabled\]\:bg-gray-500[data-disabled][data-checked] {
    --tw-bg-opacity: 1;
    background-color: rgb(107 114 128/var(--tw-bg-opacity))
}

.data-\[focus\]\:bg-oms-green-2[data-focus] {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.data-\[hover\]\:bg-\[\#F3F4F6\][data-hover] {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity))
}

.data-\[selected\]\:bg-oms-green-2[data-selected] {
    --tw-bg-opacity: 1;
    background-color: rgb(210 251 223/var(--tw-bg-opacity))
}

.data-\[closed\]\:data-\[leave\]\:opacity-0[data-leave][data-closed], .data-\[closed\]\:opacity-0[data-closed] {
    opacity: 0
}

.data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5
}

.data-\[focus\]\:outline-1[data-focus] {
    outline-width: 1px
}

.data-\[focus\]\:outline-white[data-focus] {
    outline-color: #fff
}

.data-\[leave\]\:transition[data-leave] {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.data-\[leave\]\:duration-100[data-leave] {
    transition-duration: .1s
}

.data-\[leave\]\:ease-in[data-leave] {
    transition-timing-function: cubic-bezier(.4, 0, 1, 1)
}

.group[data-open] .group-data-\[open\]\:rotate-180 {
    --tw-rotate: 180deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group[data-selected] .group-data-\[selected\]\:font-bold {
    font-weight: 700
}

.group[data-selected] .group-data-\[selected\]\:font-medium {
    font-weight: 500
}

.group[data-hover] .group-data-\[hover\]\:text-white\/80 {
    color: hsla(0, 0%, 100%, .8)
}

.peer:checked ~ .group .group-peer-checked\:block {
    display: block
}

.peer:checked ~ .group .group-peer-checked\:hidden {
    display: none
}

@media (min-width: 640px) {
    .sm\:visible {
        visibility: visible
    }

    .sm\:absolute {
        position: absolute
    }

    .sm\:relative {
        position: relative
    }

    .sm\:left-\[calc\(\(100\%-1280px\)\/2\)\] {
        left: calc((100% - 1280px) / 2)
    }

    .sm\:top-0 {
        top: 0
    }

    .sm\:top-4 {
        top: 1rem
    }

    .sm\:-mx-6 {
        margin-left: -1.5rem;
        margin-right: -1.5rem
    }

    .sm\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .sm\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .sm\:mb-10 {
        margin-bottom: 2.5rem
    }

    .sm\:mb-4 {
        margin-bottom: 1rem
    }

    .sm\:ml-2 {
        margin-left: .5rem
    }

    .sm\:block {
        display: block
    }

    .sm\:flex {
        display: flex
    }

    .sm\:hidden {
        display: none
    }

    .sm\:h-\[560px\] {
        height: 560px
    }

    .sm\:h-full {
        height: 100%
    }

    .sm\:min-h-\[32px\] {
        min-height: 32px
    }

    .sm\:w-1\/4 {
        width: 25%
    }

    .sm\:w-2\/4 {
        width: 50%
    }

    .sm\:w-5\/12 {
        width: 41.666667%
    }

    .sm\:w-\[220px\] {
        width: 220px
    }

    .sm\:w-\[233px\] {
        width: 233px
    }

    .sm\:w-\[240px\] {
        width: 240px
    }

    .sm\:w-\[284\.5px\] {
        width: 284.5px
    }

    .sm\:w-\[calc\(25\%-16px\)\] {
        width: calc(25% - 16px)
    }

    .sm\:w-\[calc\(50\%-20px\)\] {
        width: calc(50% - 20px)
    }

    .sm\:w-full {
        width: 100%
    }

    .sm\:max-w-\[460px\] {
        max-width: 460px
    }

    .sm\:max-w-\[480px\] {
        max-width: 480px
    }

    .sm\:max-w-\[540px\] {
        max-width: 540px
    }

    .sm\:max-w-md {
        max-width: 28rem
    }

    .sm\:flex-1 {
        flex: 1 1 0%
    }

    .sm\:flex-row {
        flex-direction: row
    }

    .sm\:items-start {
        align-items: flex-start
    }

    .sm\:items-center {
        align-items: center
    }

    .sm\:justify-end {
        justify-content: flex-end
    }

    .sm\:justify-center {
        justify-content: center
    }

    .sm\:justify-between {
        justify-content: space-between
    }

    .sm\:rounded-lg {
        border-radius: .5rem
    }

    .sm\:bg-transparent {
        background-color: transparent
    }

    .sm\:p-\[40px\] {
        padding: 40px
    }

    .sm\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }

    .sm\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }

    .sm\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .sm\:px-\[20px\] {
        padding-left: 20px;
        padding-right: 20px
    }

    .sm\:px-\[24px\] {
        padding-left: 24px;
        padding-right: 24px
    }

    .sm\:px-\[2rem\] {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .sm\:py-14 {
        padding-top: 3.5rem;
        padding-bottom: 3.5rem
    }

    .sm\:py-32 {
        padding-top: 8rem;
        padding-bottom: 8rem
    }

    .sm\:py-6 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
    }

    .sm\:py-\[26px\] {
        padding-top: 26px;
        padding-bottom: 26px
    }

    .sm\:py-\[90px\] {
        padding-top: 90px;
        padding-bottom: 90px
    }

    .sm\:pl-16 {
        padding-left: 4rem
    }

    .sm\:pt-12 {
        padding-top: 3rem
    }

    .sm\:text-left {
        text-align: left
    }

    .sm\:text-center {
        text-align: center
    }

    .sm\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .sm\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem
    }

    .sm\:text-\[13px\] {
        font-size: 13px
    }

    .sm\:text-\[2rem\] {
        font-size: 2rem
    }

    .sm\:text-\[32px\] {
        font-size: 32px
    }

    .sm\:text-\[50px\] {
        font-size: 50px
    }

    .sm\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }

    .sm\:text-sm {
        font-size: .875rem;
        line-height: 1.25rem
    }

    .sm\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem
    }

    .sm\:leading-6 {
        line-height: 1.5rem
    }

    .sm\:leading-\[28px\] {
        line-height: 28px
    }

    .sm\:leading-\[3rem\] {
        line-height: 3rem
    }

    .sm\:text-white {
        --tw-text-opacity: 1;
        color: rgb(255 255 255/var(--tw-text-opacity))
    }

    .sm\:even\:flex-row-reverse:nth-child(2n) {
        flex-direction: row-reverse
    }
}

@media (min-width: 768px) {
    .md\:m-10 {
        margin: 2.5rem
    }

    .md\:mx-10 {
        margin-left: 2.5rem;
        margin-right: 2.5rem
    }

    .md\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .md\:mb-8 {
        margin-bottom: 2rem
    }

    .md\:block {
        display: block
    }

    .md\:hidden {
        display: none
    }

    .md\:w-1\/4 {
        width: 25%
    }

    .md\:w-\[100\%\] {
        width: 100%
    }

    .md\:w-\[200px\] {
        width: 200px
    }

    .md\:w-\[calc\(100\%-5rem\)\] {
        width: calc(100% - 5rem)
    }

    .md\:grid-cols-1 {
        grid-template-columns:repeat(1, minmax(0, 1fr))
    }

    .md\:grid-cols-2 {
        grid-template-columns:repeat(2, minmax(0, 1fr))
    }

    .md\:flex-wrap {
        flex-wrap: wrap
    }

    .md\:gap-20 {
        gap: 5rem
    }

    .md\:gap-\[30px\] {
        gap: 30px
    }

    .md\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem
    }

    .md\:py-10 {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem
    }

    .md\:py-8 {
        padding-top: 2rem;
        padding-bottom: 2rem
    }

    .md\:pt-2 {
        padding-top: .5rem
    }

    .md\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }
}

@media (min-width: 1024px) {
    .lg\:-mx-8 {
        margin-left: -2rem;
        margin-right: -2rem
    }

    .lg\:mx-\[30px\] {
        margin-left: 30px;
        margin-right: 30px
    }

    .lg\:my-0 {
        margin-top: 0;
        margin-bottom: 0
    }

    .lg\:mb-5 {
        margin-bottom: 1.25rem
    }

    .lg\:mb-\[3\.75rem\] {
        margin-bottom: 3.75rem
    }

    .lg\:mr-20 {
        margin-right: 5rem
    }

    .lg\:mr-\[110px\] {
        margin-right: 110px
    }

    .lg\:mr-\[30px\] {
        margin-right: 30px
    }

    .lg\:mt-0 {
        margin-top: 0
    }

    .lg\:mt-12 {
        margin-top: 3rem
    }

    .lg\:mt-2 {
        margin-top: .5rem
    }

    .lg\:block {
        display: block
    }

    .lg\:flex {
        display: flex
    }

    .lg\:hidden {
        display: none
    }

    .lg\:w-2\/4 {
        width: 50%
    }

    .lg\:w-\[250px\] {
        width: 250px
    }

    .lg\:w-\[330px\] {
        width: 330px
    }

    .lg\:w-\[calc\(40\%-4rem\)\] {
        width: calc(40% - 4rem)
    }

    .lg\:w-\[calc\(50\%-56px\)\] {
        width: calc(50% - 56px)
    }

    .lg\:w-\[calc\(50\%-60px\)\] {
        width: calc(50% - 60px)
    }

    .lg\:max-w-\[490px\] {
        max-width: 490px
    }

    .lg\:max-w-\[500px\] {
        max-width: 500px
    }

    .lg\:max-w-\[622px\] {
        max-width: 622px
    }

    .lg\:max-w-\[632px\] {
        max-width: 632px
    }

    .lg\:grid-cols-2 {
        grid-template-columns:repeat(2, minmax(0, 1fr))
    }

    .lg\:grid-cols-6 {
        grid-template-columns:repeat(6, minmax(0, 1fr))
    }

    .lg\:flex-col {
        flex-direction: column
    }

    .lg\:gap-y-2 {
        row-gap: .5rem
    }

    .lg\:rounded-xl {
        border-radius: .75rem
    }

    .lg\:bg-secondary-light-400 {
        background-color: var(--color-secondary-light-400)
    }

    .lg\:p-10 {
        padding: 2.5rem
    }

    .lg\:p-6 {
        padding: 1.5rem
    }

    .lg\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .lg\:pb-12 {
        padding-bottom: 3rem
    }

    .lg\:pt-0 {
        padding-top: 0
    }

    .lg\:pt-6 {
        padding-top: 1.5rem
    }

    .lg\:pt-8 {
        padding-top: 2rem
    }

    .lg\:text-center {
        text-align: center
    }

    .lg\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .lg\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem
    }

    .lg\:text-\[2\.75rem\] {
        font-size: 2.75rem
    }

    .lg\:text-\[56px\] {
        font-size: 56px
    }

    .lg\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }

    .lg\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem
    }
}

@media (min-width: 1280px) {
    .xl\:col-span-5 {
        grid-column: span 5/span 5
    }

    .xl\:col-span-7 {
        grid-column: span 7/span 7
    }

    .xl\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .xl\:mr-5 {
        margin-right: 1.25rem
    }

    .xl\:mt-0 {
        margin-top: 0
    }

    .xl\:mt-10 {
        margin-top: 2.5rem
    }

    .xl\:mt-2 {
        margin-top: .5rem
    }

    .xl\:mt-5 {
        margin-top: 1.25rem
    }

    .xl\:block {
        display: block
    }

    .xl\:flex {
        display: flex
    }

    .xl\:grid {
        display: grid
    }

    .xl\:w-\[295px\] {
        width: 295px
    }

    .xl\:w-\[45\%\] {
        width: 45%
    }

    .xl\:w-\[55\%\] {
        width: 55%
    }

    .xl\:w-auto {
        width: auto
    }

    .xl\:table-fixed {
        table-layout: fixed
    }

    .xl\:grid-cols-12 {
        grid-template-columns:repeat(12, minmax(0, 1fr))
    }

    .xl\:grid-cols-2 {
        grid-template-columns:repeat(2, minmax(0, 1fr))
    }

    .xl\:grid-cols-4 {
        grid-template-columns:repeat(4, minmax(0, 1fr))
    }

    .xl\:flex-col {
        flex-direction: column
    }

    .xl\:flex-wrap {
        flex-wrap: wrap
    }

    .xl\:gap-0 {
        gap: 0
    }

    .xl\:gap-\[48px\] {
        gap: 48px
    }

    .xl\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
        --tw-space-x-reverse: 0;
        margin-right: calc(1.5rem * var(--tw-space-x-reverse));
        margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))
    }

    .xl\:divide-x > :not([hidden]) ~ :not([hidden]) {
        --tw-divide-x-reverse: 0;
        border-right-width: calc(1px * var(--tw-divide-x-reverse));
        border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)))
    }

    .xl\:p-6 {
        padding: 1.5rem
    }

    .xl\:pl-6 {
        padding-left: 1.5rem
    }

    .xl\:text-\[24px\] {
        font-size: 24px
    }
}

@media (min-width: 1536px) {
    .\32xl\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }
}

@media (min-width: 512px) {
    .xsmall\:left-auto {
        left: auto
    }

    .xsmall\:right-0 {
        right: 0
    }

    .xsmall\:mx-2 {
        margin-left: .5rem;
        margin-right: .5rem
    }

    .xsmall\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .xsmall\:mx-\[-1rem\] {
        margin-left: -1rem;
        margin-right: -1rem
    }

    .xsmall\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .xsmall\:mb-0 {
        margin-bottom: 0
    }

    .xsmall\:mb-10 {
        margin-bottom: 2.5rem
    }

    .xsmall\:mb-6 {
        margin-bottom: 1.5rem
    }

    .xsmall\:mt-8 {
        margin-top: 2rem
    }

    .xsmall\:flex {
        display: flex
    }

    .xsmall\:w-32 {
        width: 8rem
    }

    .xsmall\:w-\[168px\] {
        width: 168px
    }

    .xsmall\:w-\[calc\(50\%-1rem\)\] {
        width: calc(50% - 1rem)
    }

    .xsmall\:w-\[calc\(50\%-2rem\)\] {
        width: calc(50% - 2rem)
    }

    .xsmall\:min-w-\[200px\] {
        min-width: 200px
    }

    .xsmall\:flex-wrap {
        flex-wrap: wrap
    }

    .xsmall\:justify-end {
        justify-content: flex-end
    }

    .xsmall\:justify-center {
        justify-content: center
    }

    .xsmall\:rounded-xl {
        border-radius: .75rem
    }

    .xsmall\:bg-secondary-light-400 {
        background-color: var(--color-secondary-light-400)
    }

    .xsmall\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }

    .xsmall\:px-2 {
        padding-left: .5rem;
        padding-right: .5rem
    }

    .xsmall\:px-\[1\.75rem\] {
        padding-left: 1.75rem;
        padding-right: 1.75rem
    }

    .xsmall\:pb-0 {
        padding-bottom: 0
    }

    .xsmall\:pb-6 {
        padding-bottom: 1.5rem
    }

    .xsmall\:pt-10 {
        padding-top: 2.5rem
    }

    .xsmall\:pt-8 {
        padding-top: 2rem
    }

    .xsmall\:text-center {
        text-align: center
    }

    .xsmall\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }
}

@media (min-width: 768px) {
    .regular\:static {
        position: static
    }

    .regular\:m-0 {
        margin: 0
    }

    .regular\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .regular\:mx-\[-1rem\] {
        margin-left: -1rem;
        margin-right: -1rem
    }

    .regular\:ml-10 {
        margin-left: 2.5rem
    }

    .regular\:mt-10 {
        margin-top: 2.5rem
    }

    .regular\:mt-5 {
        margin-top: 1.25rem
    }

    .regular\:mt-6 {
        margin-top: 1.5rem
    }

    .regular\:block {
        display: block
    }

    .regular\:flex {
        display: flex
    }

    .regular\:\!w-full {
        width: 100% !important
    }

    .regular\:w-2\/6 {
        width: 33.333333%
    }

    .regular\:w-\[40\%\] {
        width: 40%
    }

    .regular\:w-\[calc\(50\%-20px\)\] {
        width: calc(50% - 20px)
    }

    .regular\:w-auto {
        width: auto
    }

    .regular\:w-full {
        width: 100%
    }

    .regular\:max-w-\[27\.5rem\] {
        max-width: 27.5rem
    }

    .regular\:max-w-\[320px\] {
        max-width: 320px
    }

    .regular\:flex-1 {
        flex: 1 1 0%
    }

    .regular\:flex-row {
        flex-direction: row
    }

    .regular\:flex-col {
        flex-direction: column
    }

    .regular\:flex-wrap {
        flex-wrap: wrap
    }

    .regular\:items-start {
        align-items: flex-start
    }

    .regular\:justify-start {
        justify-content: flex-start
    }

    .regular\:justify-between {
        justify-content: space-between
    }

    .regular\:rounded-xl {
        border-radius: .75rem
    }

    .regular\:border {
        border-width: 1px
    }

    .regular\:border-0 {
        border-width: 0
    }

    .regular\:border-b-0 {
        border-bottom-width: 0
    }

    .regular\:border-solid {
        border-style: solid
    }

    .regular\:border-secondary-light {
        border-color: var(--color-secondary-light)
    }

    .regular\:\!bg-secondary-light-400 {
        background-color: var(--color-secondary-light-400) !important
    }

    .regular\:bg-\[\#FCFCFD\] {
        --tw-bg-opacity: 1;
        background-color: rgb(252 252 253/var(--tw-bg-opacity))
    }

    .regular\:p-6 {
        padding: 1.5rem
    }

    .regular\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .regular\:px-3 {
        padding-left: .75rem;
        padding-right: .75rem
    }

    .regular\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem
    }

    .regular\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .regular\:py-0 {
        padding-top: 0;
        padding-bottom: 0
    }

    .regular\:py-2 {
        padding-top: .5rem;
        padding-bottom: .5rem
    }

    .regular\:py-6 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
    }

    .regular\:py-8 {
        padding-top: 2rem;
        padding-bottom: 2rem
    }

    .regular\:pb-0 {
        padding-bottom: 0
    }

    .regular\:pb-6 {
        padding-bottom: 1.5rem
    }

    .regular\:pb-8 {
        padding-bottom: 2rem
    }

    .regular\:pl-3 {
        padding-left: .75rem
    }

    .regular\:pt-0 {
        padding-top: 0
    }

    .regular\:text-\[2\.5rem\] {
        font-size: 2.5rem
    }

    .regular\:text-\[2rem\] {
        font-size: 2rem
    }

    .regular\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }

    .regular\:shadow-md {
        --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
        --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color)
    }

    .regular\:shadow-md, .regular\:shadow-none {
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
    }

    .regular\:shadow-none {
        --tw-shadow: 0 0 #0000;
        --tw-shadow-colored: 0 0 #0000
    }
}

@media (min-width: 1024px) {
    .small\:static {
        position: static
    }

    .small\:absolute {
        position: absolute
    }

    .small\:relative {
        position: relative
    }

    .small\:sticky {
        position: sticky
    }

    .small\:right-0 {
        right: 0
    }

    .small\:top-0 {
        top: 0
    }

    .small\:top-20 {
        top: 5rem
    }

    .small\:mx-0 {
        margin-left: 0;
        margin-right: 0
    }

    .small\:mx-16 {
        margin-left: 4rem;
        margin-right: 4rem
    }

    .small\:mx-2 {
        margin-left: .5rem;
        margin-right: .5rem
    }

    .small\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .small\:mx-8 {
        margin-left: 2rem;
        margin-right: 2rem
    }

    .small\:mx-\[-1rem\] {
        margin-left: -1rem;
        margin-right: -1rem
    }

    .small\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .small\:my-0 {
        margin-top: 0;
        margin-bottom: 0
    }

    .small\:my-32 {
        margin-top: 8rem;
        margin-bottom: 8rem
    }

    .small\:mb-0 {
        margin-bottom: 0
    }

    .small\:mb-10 {
        margin-bottom: 2.5rem
    }

    .small\:mb-12 {
        margin-bottom: 3rem
    }

    .small\:mb-4 {
        margin-bottom: 1rem
    }

    .small\:ml-0 {
        margin-left: 0
    }

    .small\:ml-3 {
        margin-left: .75rem
    }

    .small\:ml-\[109px\] {
        margin-left: 109px
    }

    .small\:ml-\[80px\] {
        margin-left: 80px
    }

    .small\:ml-auto {
        margin-left: auto
    }

    .small\:mr-4 {
        margin-right: 1rem
    }

    .small\:mr-6 {
        margin-right: 1.5rem
    }

    .small\:mt-0 {
        margin-top: 0
    }

    .small\:mt-10 {
        margin-top: 2.5rem
    }

    .small\:mt-12 {
        margin-top: 3rem
    }

    .small\:mt-16 {
        margin-top: 4rem
    }

    .small\:mt-2 {
        margin-top: .5rem
    }

    .small\:mt-2\.5 {
        margin-top: .625rem
    }

    .small\:mt-6 {
        margin-top: 1.5rem
    }

    .small\:mt-\[3\.75rem\] {
        margin-top: 3.75rem
    }

    .small\:block {
        display: block
    }

    .small\:flex {
        display: flex
    }

    .small\:grid {
        display: grid
    }

    .small\:hidden {
        display: none
    }

    .small\:aspect-\[28\/36\] {
        aspect-ratio: 28/36
    }

    .small\:min-h-screen {
        min-height: 100vh
    }

    .small\:w-24 {
        width: 6rem
    }

    .small\:w-96 {
        width: 24rem
    }

    .small\:w-\[200px\] {
        width: 200px
    }

    .small\:w-\[250px\] {
        width: 250px
    }

    .small\:w-\[35\%\] {
        width: 35%
    }

    .small\:w-\[408px\] {
        width: 408px
    }

    .small\:w-\[calc\(100\%-160px\)\] {
        width: calc(100% - 160px)
    }

    .small\:w-\[calc\(25\%-1rem\)\] {
        width: calc(25% - 1rem)
    }

    .small\:w-\[calc\(25\%-2rem\)\] {
        width: calc(25% - 2rem)
    }

    .small\:w-\[calc\(33\.33\%-2rem\)\] {
        width: calc(33.33% - 2rem)
    }

    .small\:w-\[calc\(50\%-40px\)\] {
        width: calc(50% - 40px)
    }

    .small\:min-w-\[250px\] {
        min-width: 250px
    }

    .small\:min-w-\[300px\] {
        min-width: 300px
    }

    .small\:max-w-\[140px\] {
        max-width: 140px
    }

    .small\:max-w-\[344px\] {
        max-width: 344px
    }

    .small\:max-w-\[400px\] {
        max-width: 400px
    }

    .small\:max-w-\[calc\(780px\)\] {
        max-width: calc(780px)
    }

    .small\:flex-1 {
        flex: 1 1 0%
    }

    .small\:grid-cols-1 {
        grid-template-columns:repeat(1, minmax(0, 1fr))
    }

    .small\:grid-cols-2 {
        grid-template-columns:repeat(2, minmax(0, 1fr))
    }

    .small\:grid-cols-3 {
        grid-template-columns:repeat(3, minmax(0, 1fr))
    }

    .small\:grid-cols-4 {
        grid-template-columns:repeat(4, minmax(0, 1fr))
    }

    .small\:grid-cols-\[1fr_360px\] {
        grid-template-columns:1fr 360px
    }

    .small\:flex-row {
        flex-direction: row
    }

    .small\:flex-col {
        flex-direction: column
    }

    .small\:flex-wrap {
        flex-wrap: wrap
    }

    .small\:items-start {
        align-items: flex-start
    }

    .small\:items-center {
        align-items: center
    }

    .small\:justify-start {
        justify-content: flex-start
    }

    .small\:justify-end {
        justify-content: flex-end
    }

    .small\:justify-between {
        justify-content: space-between
    }

    .small\:gap-10 {
        gap: 2.5rem
    }

    .small\:gap-y-2 {
        row-gap: .5rem
    }

    .small\:gap-y-3 {
        row-gap: .75rem
    }

    .small\:overflow-hidden {
        overflow: hidden
    }

    .small\:rounded-xl {
        border-radius: .75rem
    }

    .small\:border-0 {
        border-width: 0
    }

    .small\:p-32 {
        padding: 8rem
    }

    .small\:p-4 {
        padding: 1rem
    }

    .small\:p-6 {
        padding: 1.5rem
    }

    .small\:p-7 {
        padding: 1.75rem
    }

    .small\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .small\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }

    .small\:px-16 {
        padding-left: 4rem;
        padding-right: 4rem
    }

    .small\:px-20 {
        padding-left: 5rem;
        padding-right: 5rem
    }

    .small\:px-3 {
        padding-left: .75rem;
        padding-right: .75rem
    }

    .small\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem
    }

    .small\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }

    .small\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .small\:py-0 {
        padding-top: 0;
        padding-bottom: 0
    }

    .small\:py-12 {
        padding-top: 3rem;
        padding-bottom: 3rem
    }

    .small\:py-20 {
        padding-top: 5rem;
        padding-bottom: 5rem
    }

    .small\:py-24 {
        padding-top: 6rem;
        padding-bottom: 6rem
    }

    .small\:py-3 {
        padding-top: .75rem;
        padding-bottom: .75rem
    }

    .small\:py-4 {
        padding-top: 1rem;
        padding-bottom: 1rem
    }

    .small\:py-5 {
        padding-top: 1.25rem;
        padding-bottom: 1.25rem
    }

    .small\:py-6 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
    }

    .small\:py-8 {
        padding-top: 2rem;
        padding-bottom: 2rem
    }

    .small\:py-\[6\.75rem\] {
        padding-top: 6.75rem;
        padding-bottom: 6.75rem
    }

    .small\:pb-11 {
        padding-bottom: 2.75rem
    }

    .small\:pb-12 {
        padding-bottom: 3rem
    }

    .small\:pb-14 {
        padding-bottom: 3.5rem
    }

    .small\:pb-16 {
        padding-bottom: 4rem
    }

    .small\:pb-2 {
        padding-bottom: .5rem
    }

    .small\:pb-24 {
        padding-bottom: 6rem
    }

    .small\:pb-4 {
        padding-bottom: 1rem
    }

    .small\:pb-6 {
        padding-bottom: 1.5rem
    }

    .small\:pl-8 {
        padding-left: 2rem
    }

    .small\:pr-0 {
        padding-right: 0
    }

    .small\:pr-28 {
        padding-right: 7rem
    }

    .small\:pt-0 {
        padding-top: 0
    }

    .small\:pt-1 {
        padding-top: .25rem
    }

    .small\:pt-10 {
        padding-top: 2.5rem
    }

    .small\:pt-12 {
        padding-top: 3rem
    }

    .small\:pt-16 {
        padding-top: 4rem
    }

    .small\:pt-24 {
        padding-top: 6rem
    }

    .small\:pt-3 {
        padding-top: .75rem
    }

    .small\:pt-4 {
        padding-top: 1rem
    }

    .small\:pt-6 {
        padding-top: 1.5rem
    }

    .small\:pt-8 {
        padding-top: 2rem
    }

    .small\:pt-\[52px\] {
        padding-top: 52px
    }

    .small\:text-left {
        text-align: left
    }

    .small\:text-center {
        text-align: center
    }

    .small\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .small\:text-5xl {
        font-size: 3rem;
        line-height: 1
    }

    .small\:text-\[2\.5rem\] {
        font-size: 2.5rem
    }

    .small\:text-\[2rem\] {
        font-size: 2rem
    }

    .small\:text-\[32px\] {
        font-size: 32px
    }

    .small\:text-\[40px\] {
        font-size: 40px
    }

    .small\:text-\[76px\] {
        font-size: 76px
    }

    .small\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }

    .small\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }

    .small\:text-sm {
        font-size: .875rem;
        line-height: 1.25rem
    }

    .small\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem
    }

    .small\:leading-\[3\.5rem\] {
        line-height: 3.5rem
    }
}

@media (min-width: 1280px) {
    .medium\:left-banner-content-pos-x {
        left: var(--banner-content-pos-x)
    }

    .medium\:max-w-\[400px\] {
        max-width: 400px
    }

    .medium\:grid-cols-4 {
        grid-template-columns:repeat(4, minmax(0, 1fr))
    }
}

@media (min-width: 1440px) {
    .large\:pl-\[100px\] {
        padding-left: 100px
    }

    .large\:pl-\[30px\] {
        padding-left: 30px
    }

    .large\:pl-\[90px\] {
        padding-left: 90px
    }

    .large\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }
}

@media (min-width: 1680px) {
    .xlarge\:col-span-5 {
        grid-column: span 5/span 5
    }

    .xlarge\:col-span-6 {
        grid-column: span 6/span 6
    }

    .xlarge\:w-10 {
        width: 2.5rem
    }

    .xlarge\:w-\[300px\] {
        width: 300px
    }

    .xlarge\:min-w-\[190px\] {
        min-width: 190px
    }

    .xlarge\:max-w-\[265px\] {
        max-width: 265px
    }

    .xlarge\:pl-\[120px\] {
        padding-left: 120px
    }

    .xlarge\:pl-\[124px\] {
        padding-left: 124px
    }

    .xlarge\:pl-\[40px\] {
        padding-left: 40px
    }
}

@media (max-width: 767px) {
    .mdDown\:\!hidden {
        display: none !important
    }
}

@media (prefers-color-scheme: dark) {
    .dark\:border-gray-600 {
        --tw-border-opacity: 1;
        border-color: rgb(75 85 99/var(--tw-border-opacity))
    }

    .dark\:border-neutral-500 {
        --tw-border-opacity: 1;
        border-color: rgb(115 115 115/var(--tw-border-opacity))
    }

    .dark\:border-secondary-900 {
        border-color: var(--color-secondary-900)
    }

    .dark\:bg-gray-700 {
        --tw-bg-opacity: 1;
        background-color: rgb(55 65 81/var(--tw-bg-opacity))
    }

    .dark\:bg-secondary-700 {
        background-color: var(--color-secondary-700)
    }

    .dark\:bg-secondary-light {
        background-color: var(--color-secondary-light)
    }

    .dark\:text-danger-500 {
        color: var(--color-danger-500)
    }

    .dark\:text-white {
        --tw-text-opacity: 1;
        color: rgb(255 255 255/var(--tw-text-opacity))
    }

    .dark\:placeholder-gray-400::-moz-placeholder {
        --tw-placeholder-opacity: 1;
        color: rgb(156 163 175/var(--tw-placeholder-opacity))
    }

    .dark\:placeholder-gray-400::placeholder {
        --tw-placeholder-opacity: 1;
        color: rgb(156 163 175/var(--tw-placeholder-opacity))
    }

    .dark\:focus\:border-blue-500:focus {
        --tw-border-opacity: 1;
        border-color: rgb(59 130 246/var(--tw-border-opacity))
    }

    .dark\:focus\:ring-\[\#3b5998\]\/55:focus {
        --tw-ring-color: rgba(59, 89, 152, .55)
    }

    .dark\:focus\:ring-blue-500:focus {
        --tw-ring-opacity: 1;
        --tw-ring-color: rgb(59 130 246/var(--tw-ring-opacity))
    }
}

@media (min-width: 768px) {
    .\[\&\>div\:last-child\]\:regular\:pb-6 > div:last-child {
        padding-bottom: 1.5rem
    }
}

.group:not([data-selected]) .\[\.group\:not\(\[data-selected\]\)_\&\]\:hidden {
    display: none
}

@media (min-width: 1441px) {
    .\[\@media\(min-width\:1441px\)\]\:col-span-3 {
        grid-column: span 3/span 3
    }

    .\[\@media\(min-width\:1441px\)\]\:col-span-4 {
        grid-column: span 4/span 4
    }

    .\[\@media\(min-width\:1441px\)\]\:col-span-6 {
        grid-column: span 6/span 6
    }

    .\[\@media\(min-width\:1441px\)\]\:col-span-7 {
        grid-column: span 7/span 7
    }

    .\[\@media\(min-width\:1441px\)\]\:flex {
        display: flex
    }
}

@media (min-width: 2098px) {
    .\[\@media\(min-width\:2098px\)\]\:min-h-\[540px\] {
        min-height: 540px
    }
}

.instructions_step__X8Pyz li {
    position: relative;
    display: flex;
    padding-bottom: 48px
}

.instructions_step__X8Pyz li:last-child {
    padding-bottom: 0
}

.instructions_step__X8Pyz li:last-child:after {
    display: none
}

.instructions_step__X8Pyz li:after {
    position: absolute;
    top: 0;
    left: 23px;
    content: "";
    width: 1px;
    height: 100%;
    border-left: 1px dashed teal;
    opacity: .2
}

.instructions_step__X8Pyz li > span {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 46px;
    height: 46px;
    border-radius: 50%;
    margin-right: 20px;
    background-color: teal;
    color: #fff;
    font-size: 16px;
    filter: drop-shadow(0 4px 20px rgba(0, 128, 128, .32));
    z-index: 10
}

.instructions_step__X8Pyz li > div {
    width: calc(100% - 46px - 20px)
}

.instructions_step__X8Pyz li > div p {
    font-size: 18px;
    font-weight: 700
}

.instructions_step__X8Pyz li > div span {
    font-size: 16px;
    color: #6f767e
}

@media (min-width: 1024px) {
    .instructions_step__X8Pyz {
        display: flex
    }

    .instructions_step__X8Pyz li {
        width: 33.33%;
        flex-direction: column;
        align-items: center
    }

    .instructions_step__X8Pyz li:first-child:before {
        display: none
    }

    .instructions_step__X8Pyz li:first-child:after {
        display: initial
    }

    .instructions_step__X8Pyz li:last-child:after {
        display: none
    }

    .instructions_step__X8Pyz li:after, .instructions_step__X8Pyz li:before {
        content: "";
        position: absolute;
        top: 28px;
        width: 50%;
        height: 1px;
        border-top: 1px dashed teal;
        border-left: 1px dashed teal;
        opacity: .2
    }

    .instructions_step__X8Pyz li:before {
        left: 0
    }

    .instructions_step__X8Pyz li:after {
        left: auto;
        right: 0
    }

    .instructions_step__X8Pyz li > span {
        width: 56px;
        height: 56px;
        margin-right: 0;
        margin-bottom: 24px;
        font-size: 18px
    }

    .instructions_step__X8Pyz li > div {
        width: calc(100% - 56px - 20px);
        text-align: center;
        padding: 0 40px;
        margin: 8px 0 0
    }
}

@font-face {
    font-family: swiper-icons;
    src: url("data:application/font-woff;charset=utf-8;base64, 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");
    font-weight: 400;
    font-style: normal
}

:root {
    --swiper-theme-color: #007aff
}

:host {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    z-index: 1
}

.swiper {
    margin-left: auto;
    margin-right: auto;
    position: relative;
    overflow: hidden;
    list-style: none;
    padding: 0;
    z-index: 1;
    display: block
}

.swiper-vertical > .swiper-wrapper {
    flex-direction: column
}

.swiper-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
    display: flex;
    transition-property: transform;
    transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);
    box-sizing: content-box
}

.swiper-android .swiper-slide, .swiper-ios .swiper-slide, .swiper-wrapper {
    transform: translateZ(0)
}

.swiper-horizontal {
    touch-action: pan-y
}

.swiper-vertical {
    touch-action: pan-x
}

.swiper-slide {
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    position: relative;
    transition-property: transform;
    display: block
}

.swiper-slide-invisible-blank {
    visibility: hidden
}

.swiper-autoheight, .swiper-autoheight .swiper-slide {
    height: auto
}

.swiper-autoheight .swiper-wrapper {
    align-items: flex-start;
    transition-property: transform, height
}

.swiper-backface-hidden .swiper-slide {
    transform: translateZ(0);
    backface-visibility: hidden
}

.swiper-3d.swiper-css-mode .swiper-wrapper {
    perspective: 1200px
}

.swiper-3d .swiper-wrapper {
    transform-style: preserve-3d
}

.swiper-3d {
    perspective: 1200px
}

.swiper-3d .swiper-cube-shadow, .swiper-3d .swiper-slide {
    transform-style: preserve-3d
}

.swiper-css-mode > .swiper-wrapper {
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
    display: none
}

.swiper-css-mode > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: start start
}

.swiper-css-mode.swiper-horizontal > .swiper-wrapper {
    scroll-snap-type: x mandatory
}

.swiper-css-mode.swiper-vertical > .swiper-wrapper {
    scroll-snap-type: y mandatory
}

.swiper-css-mode.swiper-free-mode > .swiper-wrapper {
    scroll-snap-type: none
}

.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: none
}

.swiper-css-mode.swiper-centered > .swiper-wrapper:before {
    content: "";
    flex-shrink: 0;
    order: 9999
}

.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: center center;
    scroll-snap-stop: always
}

.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {
    margin-inline-start: var(--swiper-centered-offset-before)
}

.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper:before {
    height: 100%;
    min-height: 1px;
    width: var(--swiper-centered-offset-after)
}

.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {
    margin-block-start: var(--swiper-centered-offset-before)
}

.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper:before {
    width: 100%;
    min-width: 1px;
    height: var(--swiper-centered-offset-after)
}

.swiper-3d .swiper-slide-shadow, .swiper-3d .swiper-slide-shadow-bottom, .swiper-3d .swiper-slide-shadow-left, .swiper-3d .swiper-slide-shadow-right, .swiper-3d .swiper-slide-shadow-top {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10
}

.swiper-3d .swiper-slide-shadow {
    background: rgba(0, 0, 0, .15)
}

.swiper-3d .swiper-slide-shadow-left {
    background-image: linear-gradient(270deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-3d .swiper-slide-shadow-right {
    background-image: linear-gradient(90deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-3d .swiper-slide-shadow-top {
    background-image: linear-gradient(0deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-3d .swiper-slide-shadow-bottom {
    background-image: linear-gradient(180deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-lazy-preloader {
    width: 42px;
    height: 42px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -21px;
    margin-top: -21px;
    z-index: 10;
    transform-origin: 50%;
    box-sizing: border-box;
    border-radius: 50%;
    border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
    border-top: 4px solid transparent
}

.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader, .swiper:not(.swiper-watch-progress) .swiper-lazy-preloader {
    animation: swiper-preloader-spin 1s linear infinite
}

.swiper-lazy-preloader-white {
    --swiper-preloader-color: #fff
}

.swiper-lazy-preloader-black {
    --swiper-preloader-color: #000
}

@keyframes swiper-preloader-spin {
    0% {
        transform: rotate(0deg)
    }
    to {
        transform: rotate(1turn)
    }
}

.swiper-pagination {
    position: absolute;
    text-align: center;
    transition: opacity .3s;
    transform: translateZ(0);
    z-index: 10
}

.swiper-pagination.swiper-pagination-hidden {
    opacity: 0
}

.swiper-pagination-disabled > .swiper-pagination, .swiper-pagination.swiper-pagination-disabled {
    display: none !important
}

.swiper-horizontal > .swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
    bottom: var(--swiper-pagination-bottom, 8px);
    top: var(--swiper-pagination-top, auto);
    left: 0;
    width: 100%
}

.swiper-pagination-bullets-dynamic {
    overflow: hidden;
    font-size: 0
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transform: scale(.33);
    position: relative
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active, .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
    transform: scale(1)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
    transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
    transform: scale(.33)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
    transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
    transform: scale(.33)
}

.swiper-pagination-bullet {
    width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));
    height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));
    display: inline-block;
    border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
    background: var(--swiper-pagination-bullet-inactive-color, #000);
    opacity: var(--swiper-pagination-bullet-inactive-opacity, .2)
}

button.swiper-pagination-bullet {
    border: none;
    margin: 0;
    padding: 0;
    box-shadow: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.swiper-pagination-clickable .swiper-pagination-bullet {
    cursor: pointer
}

.swiper-pagination-bullet:only-child {
    display: none !important
}

.swiper-pagination-bullet-active {
    opacity: var(--swiper-pagination-bullet-opacity, 1);
    background: var(--swiper-pagination-color, var(--swiper-theme-color))
}

.swiper-pagination-vertical.swiper-pagination-bullets, .swiper-vertical > .swiper-pagination-bullets {
    right: var(--swiper-pagination-right, 8px);
    left: var(--swiper-pagination-left, auto);
    top: 50%;
    transform: translate3d(0, -50%, 0)
}

.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet, .swiper-vertical > .swiper-pagination-bullets .swiper-pagination-bullet {
    margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
    display: block
}

.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic, .swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    top: 50%;
    transform: translateY(-50%);
    width: 8px
}

.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet, .swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    display: inline-block;
    transition: transform .2s, top .2s
}

.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet, .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px)
}

.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic, .swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap
}

.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet, .swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transition: transform .2s, left .2s
}

.swiper-horizontal.swiper-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transition: transform .2s, right .2s
}

.swiper-pagination-fraction {
    color: var(--swiper-pagination-fraction-color, inherit)
}

.swiper-pagination-progressbar {
    background: var(--swiper-pagination-progressbar-bg-color, rgba(0, 0, 0, .25));
    position: absolute
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    background: var(--swiper-pagination-color, var(--swiper-theme-color));
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    transform: scale(0);
    transform-origin: left top
}

.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    transform-origin: right top
}

.swiper-horizontal > .swiper-pagination-progressbar, .swiper-pagination-progressbar.swiper-pagination-horizontal, .swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite, .swiper-vertical > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
    width: 100%;
    height: var(--swiper-pagination-progressbar-size, 4px);
    left: 0;
    top: 0
}

.swiper-horizontal > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite, .swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite, .swiper-pagination-progressbar.swiper-pagination-vertical, .swiper-vertical > .swiper-pagination-progressbar {
    width: var(--swiper-pagination-progressbar-size, 4px);
    height: 100%;
    left: 0;
    top: 0
}

.swiper-pagination-lock {
    display: none
}

.banners_swiper__0MTBv {
    padding-bottom: 66px
}

.banners_swiperItem__mH1QM span {
    position: static !important
}

.banners_swiperItem__mH1QM img {
    position: static !important;
    width: 100% !important;
    height: auto !important
}

:root {
    --swiper-navigation-size: 44px
}

.swiper-button-next, .swiper-button-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    width: calc(var(--swiper-navigation-size) / 44 * 27);
    height: var(--swiper-navigation-size);
    margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--swiper-navigation-color, var(--swiper-theme-color))
}

.swiper-button-next.swiper-button-disabled, .swiper-button-prev.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none
}

.swiper-button-next.swiper-button-hidden, .swiper-button-prev.swiper-button-hidden {
    opacity: 0;
    cursor: auto;
    pointer-events: none
}

.swiper-navigation-disabled .swiper-button-next, .swiper-navigation-disabled .swiper-button-prev {
    display: none !important
}

.swiper-button-next svg, .swiper-button-prev svg {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    transform-origin: center
}

.swiper-rtl .swiper-button-next svg, .swiper-rtl .swiper-button-prev svg {
    transform: rotate(180deg)
}

.swiper-button-prev, .swiper-rtl .swiper-button-next {
    left: var(--swiper-navigation-sides-offset, 10px);
    right: auto
}

.swiper-button-lock {
    display: none
}

.swiper-button-next:after, .swiper-button-prev:after {
    font-family: swiper-icons;
    font-size: var(--swiper-navigation-size);
    text-transform: none !important;
    letter-spacing: 0;
    font-variant: normal;
    line-height: 1
}

.swiper-button-prev:after, .swiper-rtl .swiper-button-next:after {
    content: "prev"
}

.swiper-button-next, .swiper-rtl .swiper-button-prev {
    right: var(--swiper-navigation-sides-offset, 10px);
    left: auto
}

.swiper-button-next:after, .swiper-rtl .swiper-button-prev:after {
    content: "next"
}

.richtext_richtextContent__c6X8A {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 15px
}

.richtext_richtextContent__c6X8A ol {
    padding-left: 30px
}

.richtext_richtextContent__c6X8A ol * {
    margin-bottom: 25px
}

.richtext_richtextContent__c6X8A ol dt {
    margin-left: -30px;
    font-weight: 700
}

.richtext_richtextContent__c6X8A ol li {
    list-style: decimal
}

.richtext_richtextContent__c6X8A ol li > h3 {
    margin-left: -25px
}

.richtext_richtextContent__c6X8A ol li a {
    color: teal;
    word-break: break-word
}

.richtext_richtextContent__c6X8A ol ul {
    margin: 20px 0 0 50px
}

.richtext_richtextContent__c6X8A ul li {
    list-style: lower-alpha
}

.richtext_richtextContent__c6X8A h1, .richtext_richtextContent__c6X8A h2, .richtext_richtextContent__c6X8A h3, .richtext_richtextContent__c6X8A h4, .richtext_richtextContent__c6X8A h5 {
    font-weight: 700;
    margin-bottom: 25px
}

.richtext_richtextContent__c6X8A h1 {
    font-size: 24px
}

.richtext_richtextContent__c6X8A h2 {
    font-size: 20px
}

.richtext_richtextContent__c6X8A h3 {
    font-size: 16px
}

.richtext_richtextContent__c6X8A h4 {
    text-align: center
}

.bladeContent_listing__HFboo {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    overflow: hidden
}

.bladeContent_listing__HFboo ul li {
    display: flex;
    flex-direction: column
}

.bladeContent_listing__HFboo ul li span {
    position: static !important;
    width: 100% !important
}

.bladeContent_listing__HFboo ul li img {
    position: static !important;
    width: 48px !important;
    height: auto !important;
    max-width: none !important;
    min-width: auto !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    margin: initial !important
}

.bladeContent_listing__HFboo ul li > div:last-child * {
    margin-bottom: 6px
}

.bladeproduct_infoImage__dpJeY {
    width: 100%
}

.bladeproduct_img__9_vCO, .bladeproduct_infoImage__dpJeY span {
    position: static !important
}

.bladeproduct_img__9_vCO {
    width: 100% !important;
    height: auto !important;
    min-width: auto !important;
    min-height: auto !important;
    max-width: none !important;
    max-height: none !important
}

.bladeproduct_BladeProductInfo__TJwtN li {
    background-image: url(/Blade.png);
    background-repeat: no-repeat;
    padding: 7px 0 12px 40px
}

@media (min-width: 768px) {
    .bladeproduct_infoImage__dpJeY {
        width: 50%
    }

    .bladeproduct_img__9_vCO {
        width: 90% !important
    }
}

.carousel_swiper__Z0Mu2 {
    padding-bottom: 66px
}

.carousel_swiperItem__c9npg span {
    position: static !important
}

.carousel_swiperItem__c9npg img {
    position: static !important;
    width: 100% !important;
    height: auto !important
}

.category_listing__JiFl0 ul li {
    display: flex;
    flex-direction: column
}

.category_listing__JiFl0 ul li img, .category_listing__JiFl0 ul li span {
    position: static !important;
    width: 100% !important
}

.category_listing__JiFl0 ul li img {
    height: auto !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px
}

.category_listing__JiFl0 ul li a img {
    max-width: none !important;
    min-width: auto !important
}

.category_listing__JiFl0 ul li a:first-child img {
    width: 72px !important;
    height: 30px !important
}

.category_listing__JiFl0 ul li a:first-child + a img {
    width: 88px !important;
    height: 28px !important
}

.grid_listing__mB46N {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    overflow: hidden
}

.grid_listing__mB46N ul li {
    display: flex;
    flex-direction: column
}

.grid_listing__mB46N ul li img, .grid_listing__mB46N ul li span {
    position: static !important;
    width: 100% !important
}

.grid_listing__mB46N ul li img {
    height: auto !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px
}

.grid_listing__mB46N ul li a img {
    max-width: none !important;
    min-width: auto !important
}

.grid_listing__mB46N ul li a:first-child img {
    width: 72px !important;
    height: 30px !important
}

.grid_listing__mB46N ul li a:first-child + a img {
    width: 88px !important;
    height: 28px !important
}

.howItWorksContainer_img__uKc7r img, .howItWorksContainer_img__uKc7r span {
    position: static !important;
    width: 100% !important
}

.howItWorksContainer_img__uKc7r img {
    height: auto !important;
    max-width: none !important;
    min-width: auto !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    margin: initial !important
}

.howItWorksStepsItem_stepImage__5BsA4 {
    width: 100%
}

.howItWorksStepsItem_stepImage__5BsA4 img, .howItWorksStepsItem_stepImage__5BsA4 span {
    position: static !important;
    width: 100% !important
}

.howItWorksStepsItem_stepImage__5BsA4 img {
    height: auto !important;
    max-width: none !important;
    min-width: auto !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    margin: initial !important
}

@media (min-width: 640px) {
    .howItWorksStepsItem_stepImage__5BsA4 {
        width: calc(50% - 20px)
    }
}

@media (min-width: 1024px) {
    .howItWorksStepsItem_stepImage__5BsA4 {
        width: calc(50% - 56px)
    }
}

.react-datepicker__month-read-view--down-arrow, .react-datepicker__month-year-read-view--down-arrow, .react-datepicker__navigation-icon:before, .react-datepicker__year-read-view--down-arrow {
    border-color: #ccc;
    border-style: solid;
    border-width: 3px 3px 0 0;
    content: "";
    display: block;
    height: 9px;
    position: absolute;
    top: 6px;
    width: 9px
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle, .react-datepicker-popper[data-placement^=top] .react-datepicker__triangle {
    margin-left: -4px;
    position: absolute;
    width: 0
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after, .react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before, .react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after, .react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before {
    box-sizing: content-box;
    position: absolute;
    height: 0;
    width: 1px;
    content: "";
    z-index: -1;
    border: 8px solid transparent;
    left: -8px
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before, .react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before {
    border-bottom-color: #aeaeae
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle {
    top: 0;
    margin-top: -8px
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after, .react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before {
    border-top: none;
    border-bottom-color: #f0f0f0
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after {
    top: 0
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before {
    top: -1px;
    border-bottom-color: #aeaeae
}

.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle {
    bottom: 0;
    margin-bottom: -8px
}

.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after, .react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before {
    border-bottom: none;
    border-top-color: #fff
}

.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after {
    bottom: 0
}

.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before {
    bottom: -1px;
    border-top-color: #aeaeae
}

.react-datepicker-wrapper {
    display: inline-block;
    padding: 0;
    border: 0
}

.react-datepicker {
    font-family: Helvetica Neue, helvetica, arial, sans-serif;
    font-size: .8rem;
    background-color: #fff;
    color: #000;
    border: 1px solid #aeaeae;
    border-radius: .3rem;
    display: inline-block;
    position: relative
}

.react-datepicker--time-only .react-datepicker__triangle {
    left: 35px
}

.react-datepicker--time-only .react-datepicker__time-container {
    border-left: 0
}

.react-datepicker--time-only .react-datepicker__time, .react-datepicker--time-only .react-datepicker__time-box {
    border-bottom-left-radius: .3rem;
    border-bottom-right-radius: .3rem
}

.react-datepicker__triangle {
    position: absolute;
    left: 50px
}

.react-datepicker-popper {
    z-index: 1
}

.react-datepicker-popper[data-placement^=bottom] {
    padding-top: 10px
}

.react-datepicker-popper[data-placement=bottom-end] .react-datepicker__triangle, .react-datepicker-popper[data-placement=top-end] .react-datepicker__triangle {
    left: auto;
    right: 50px
}

.react-datepicker-popper[data-placement^=top] {
    padding-bottom: 10px
}

.react-datepicker-popper[data-placement^=right] {
    padding-left: 8px
}

.react-datepicker-popper[data-placement^=right] .react-datepicker__triangle {
    left: auto;
    right: 42px
}

.react-datepicker-popper[data-placement^=left] {
    padding-right: 8px
}

.react-datepicker-popper[data-placement^=left] .react-datepicker__triangle {
    left: 42px;
    right: auto
}

.react-datepicker__header {
    text-align: center;
    background-color: #f0f0f0;
    border-bottom: 1px solid #aeaeae;
    border-top-left-radius: .3rem;
    padding: 8px 0;
    position: relative
}

.react-datepicker__header--time {
    padding-bottom: 8px;
    padding-left: 5px;
    padding-right: 5px
}

.react-datepicker__header--time:not(.react-datepicker__header--time--only) {
    border-top-left-radius: 0
}

.react-datepicker__header:not(.react-datepicker__header--has-time-select) {
    border-top-right-radius: .3rem
}

.react-datepicker__month-dropdown-container--scroll, .react-datepicker__month-dropdown-container--select, .react-datepicker__month-year-dropdown-container--scroll, .react-datepicker__month-year-dropdown-container--select, .react-datepicker__year-dropdown-container--scroll, .react-datepicker__year-dropdown-container--select {
    display: inline-block;
    margin: 0 15px
}

.react-datepicker-time__header, .react-datepicker-year-header, .react-datepicker__current-month {
    margin-top: 0;
    color: #000;
    font-weight: 700;
    font-size: .944rem
}

.react-datepicker-time__header {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
}

.react-datepicker__navigation {
    align-items: center;
    background: none;
    display: flex;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    position: absolute;
    top: 2px;
    padding: 0;
    border: none;
    z-index: 1;
    height: 32px;
    width: 32px;
    text-indent: -999em;
    overflow: hidden
}

.react-datepicker__navigation--previous {
    left: 2px
}

.react-datepicker__navigation--next {
    right: 2px
}

.react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button) {
    right: 85px
}

.react-datepicker__navigation--years {
    position: relative;
    top: 0;
    display: block;
    margin-left: auto;
    margin-right: auto
}

.react-datepicker__navigation--years-previous {
    top: 4px
}

.react-datepicker__navigation--years-upcoming {
    top: -4px
}

.react-datepicker__navigation:hover :before {
    border-color: #a6a6a6
}

.react-datepicker__navigation-icon {
    position: relative;
    top: -1px;
    font-size: 20px;
    width: 0
}

.react-datepicker__navigation-icon--next {
    left: -2px
}

.react-datepicker__navigation-icon--next:before {
    transform: rotate(45deg);
    left: -7px
}

.react-datepicker__navigation-icon--previous {
    right: -2px
}

.react-datepicker__navigation-icon--previous:before {
    transform: rotate(225deg);
    right: -7px
}

.react-datepicker__month-container {
    float: left
}

.react-datepicker__year {
    margin: .4rem;
    text-align: center
}

.react-datepicker__year-wrapper {
    display: flex;
    flex-wrap: wrap;
    max-width: 180px
}

.react-datepicker__year .react-datepicker__year-text {
    display: inline-block;
    width: 4rem;
    margin: 2px
}

.react-datepicker__month {
    margin: .4rem;
    text-align: center
}

.react-datepicker__month .react-datepicker__month-text, .react-datepicker__month .react-datepicker__quarter-text {
    display: inline-block;
    width: 4rem;
    margin: 2px
}

.react-datepicker__input-time-container {
    clear: both;
    width: 100%;
    float: left;
    margin: 5px 0 10px 15px;
    text-align: left
}

.react-datepicker__input-time-container .react-datepicker-time__caption, .react-datepicker__input-time-container .react-datepicker-time__input-container {
    display: inline-block
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input {
    display: inline-block;
    margin-left: 10px
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input {
    width: auto
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-inner-spin-button, .react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time] {
    -moz-appearance: textfield
}

.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__delimiter {
    margin-left: 5px;
    display: inline-block
}

.react-datepicker__time-container {
    float: right;
    border-left: 1px solid #aeaeae;
    width: 85px
}

.react-datepicker__time-container--with-today-button {
    display: inline;
    border: 1px solid #aeaeae;
    border-radius: .3rem;
    position: absolute;
    right: -87px;
    top: 0
}

.react-datepicker__time-container .react-datepicker__time {
    position: relative;
    background: #fff;
    border-bottom-right-radius: .3rem
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box {
    width: 85px;
    overflow-x: hidden;
    margin: 0 auto;
    text-align: center;
    border-bottom-right-radius: .3rem
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list {
    list-style: none;
    margin: 0;
    height: calc(195px + (1.7rem / 2));
    overflow-y: scroll;
    padding-right: 0;
    padding-left: 0;
    width: 100%;
    box-sizing: content-box
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
    height: 30px;
    padding: 5px 10px;
    white-space: nowrap
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
    cursor: pointer;
    background-color: #f0f0f0
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
    background-color: #216ba5;
    color: #fff;
    font-weight: 700
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover {
    background-color: #216ba5
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled {
    color: #ccc
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled:hover {
    cursor: default;
    background-color: transparent
}

.react-datepicker__week-number {
    color: #ccc;
    display: inline-block;
    width: 1.7rem;
    line-height: 1.7rem;
    text-align: center;
    margin: .166rem
}

.react-datepicker__week-number.react-datepicker__week-number--clickable {
    cursor: pointer
}

.react-datepicker__week-number.react-datepicker__week-number--clickable:hover {
    border-radius: .3rem;
    background-color: #f0f0f0
}

.react-datepicker__day-names, .react-datepicker__week {
    white-space: nowrap
}

.react-datepicker__day-names {
    margin-bottom: -8px
}

.react-datepicker__day, .react-datepicker__day-name, .react-datepicker__time-name {
    color: #000;
    display: inline-block;
    width: 1.7rem;
    line-height: 1.7rem;
    text-align: center;
    margin: .166rem
}

.react-datepicker__day, .react-datepicker__month-text, .react-datepicker__quarter-text, .react-datepicker__year-text {
    cursor: pointer
}

.react-datepicker__day:hover, .react-datepicker__month-text:hover, .react-datepicker__quarter-text:hover, .react-datepicker__year-text:hover {
    border-radius: .3rem;
    background-color: #f0f0f0
}

.react-datepicker__day--today, .react-datepicker__month-text--today, .react-datepicker__quarter-text--today, .react-datepicker__year-text--today {
    font-weight: 700
}

.react-datepicker__day--highlighted, .react-datepicker__month-text--highlighted, .react-datepicker__quarter-text--highlighted, .react-datepicker__year-text--highlighted {
    border-radius: .3rem;
    background-color: #3dcc4a;
    color: #fff
}

.react-datepicker__day--highlighted:hover, .react-datepicker__month-text--highlighted:hover, .react-datepicker__quarter-text--highlighted:hover, .react-datepicker__year-text--highlighted:hover {
    background-color: #32be3f
}

.react-datepicker__day--highlighted-custom-1, .react-datepicker__month-text--highlighted-custom-1, .react-datepicker__quarter-text--highlighted-custom-1, .react-datepicker__year-text--highlighted-custom-1 {
    color: #f0f
}

.react-datepicker__day--highlighted-custom-2, .react-datepicker__month-text--highlighted-custom-2, .react-datepicker__quarter-text--highlighted-custom-2, .react-datepicker__year-text--highlighted-custom-2 {
    color: green
}

.react-datepicker__day--in-range, .react-datepicker__day--in-selecting-range, .react-datepicker__day--selected, .react-datepicker__month-text--in-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__month-text--selected, .react-datepicker__quarter-text--in-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__quarter-text--selected, .react-datepicker__year-text--in-range, .react-datepicker__year-text--in-selecting-range, .react-datepicker__year-text--selected {
    border-radius: .3rem;
    background-color: #216ba5;
    color: #fff
}

.react-datepicker__day--in-range:hover, .react-datepicker__day--in-selecting-range:hover, .react-datepicker__day--selected:hover, .react-datepicker__month-text--in-range:hover, .react-datepicker__month-text--in-selecting-range:hover, .react-datepicker__month-text--selected:hover, .react-datepicker__quarter-text--in-range:hover, .react-datepicker__quarter-text--in-selecting-range:hover, .react-datepicker__quarter-text--selected:hover, .react-datepicker__year-text--in-range:hover, .react-datepicker__year-text--in-selecting-range:hover, .react-datepicker__year-text--selected:hover {
    background-color: #1d5d90
}

.react-datepicker__day--keyboard-selected, .react-datepicker__month-text--keyboard-selected, .react-datepicker__quarter-text--keyboard-selected, .react-datepicker__year-text--keyboard-selected {
    border-radius: .3rem;
    background-color: #bad9f1;
    color: #000
}

.react-datepicker__day--keyboard-selected:hover, .react-datepicker__month-text--keyboard-selected:hover, .react-datepicker__quarter-text--keyboard-selected:hover, .react-datepicker__year-text--keyboard-selected:hover {
    background-color: #1d5d90
}

.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range), .react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range), .react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range), .react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range) {
    background-color: rgba(33, 107, 165, .5)
}

.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__year--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__year--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__year--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range), .react-datepicker__year--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range) {
    background-color: #f0f0f0;
    color: #000
}

.react-datepicker__day--disabled, .react-datepicker__month-text--disabled, .react-datepicker__quarter-text--disabled, .react-datepicker__year-text--disabled {
    cursor: default;
    color: #ccc
}

.react-datepicker__day--disabled:hover, .react-datepicker__month-text--disabled:hover, .react-datepicker__quarter-text--disabled:hover, .react-datepicker__year-text--disabled:hover {
    background-color: transparent
}

.react-datepicker__input-container {
    position: relative;
    display: inline-block;
    width: 100%
}

.react-datepicker__input-container .react-datepicker__calendar-icon {
    position: absolute;
    padding: .5rem
}

.react-datepicker__view-calendar-icon input {
    padding: 6px 10px 5px 25px
}

.react-datepicker__month-read-view, .react-datepicker__month-year-read-view, .react-datepicker__year-read-view {
    border: 1px solid transparent;
    border-radius: .3rem;
    position: relative
}

.react-datepicker__month-read-view:hover, .react-datepicker__month-year-read-view:hover, .react-datepicker__year-read-view:hover {
    cursor: pointer
}

.react-datepicker__month-read-view:hover .react-datepicker__month-read-view--down-arrow, .react-datepicker__month-read-view:hover .react-datepicker__year-read-view--down-arrow, .react-datepicker__month-year-read-view:hover .react-datepicker__month-read-view--down-arrow, .react-datepicker__month-year-read-view:hover .react-datepicker__year-read-view--down-arrow, .react-datepicker__year-read-view:hover .react-datepicker__month-read-view--down-arrow, .react-datepicker__year-read-view:hover .react-datepicker__year-read-view--down-arrow {
    border-top-color: #b3b3b3
}

.react-datepicker__month-read-view--down-arrow, .react-datepicker__month-year-read-view--down-arrow, .react-datepicker__year-read-view--down-arrow {
    transform: rotate(135deg);
    right: -16px;
    top: 0
}

.react-datepicker__month-dropdown, .react-datepicker__month-year-dropdown, .react-datepicker__year-dropdown {
    background-color: #f0f0f0;
    position: absolute;
    width: 50%;
    left: 25%;
    top: 30px;
    z-index: 1;
    text-align: center;
    border-radius: .3rem;
    border: 1px solid #aeaeae
}

.react-datepicker__month-dropdown:hover, .react-datepicker__month-year-dropdown:hover, .react-datepicker__year-dropdown:hover {
    cursor: pointer
}

.react-datepicker__month-dropdown--scrollable, .react-datepicker__month-year-dropdown--scrollable, .react-datepicker__year-dropdown--scrollable {
    height: 150px;
    overflow-y: scroll
}

.react-datepicker__month-option, .react-datepicker__month-year-option, .react-datepicker__year-option {
    line-height: 20px;
    width: 100%;
    display: block;
    margin-left: auto;
    margin-right: auto
}

.react-datepicker__month-option:first-of-type, .react-datepicker__month-year-option:first-of-type, .react-datepicker__year-option:first-of-type {
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem
}

.react-datepicker__month-option:last-of-type, .react-datepicker__month-year-option:last-of-type, .react-datepicker__year-option:last-of-type {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    border-bottom-left-radius: .3rem;
    border-bottom-right-radius: .3rem
}

.react-datepicker__month-option:hover, .react-datepicker__month-year-option:hover, .react-datepicker__year-option:hover {
    background-color: #ccc
}

.react-datepicker__month-option:hover .react-datepicker__navigation--years-upcoming, .react-datepicker__month-year-option:hover .react-datepicker__navigation--years-upcoming, .react-datepicker__year-option:hover .react-datepicker__navigation--years-upcoming {
    border-bottom-color: #b3b3b3
}

.react-datepicker__month-option:hover .react-datepicker__navigation--years-previous, .react-datepicker__month-year-option:hover .react-datepicker__navigation--years-previous, .react-datepicker__year-option:hover .react-datepicker__navigation--years-previous {
    border-top-color: #b3b3b3
}

.react-datepicker__month-option--selected, .react-datepicker__month-year-option--selected, .react-datepicker__year-option--selected {
    position: absolute;
    left: 15px
}

.react-datepicker__close-icon {
    cursor: pointer;
    background-color: transparent;
    border: 0;
    outline: 0;
    padding: 0 6px 0 0;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    display: table-cell;
    vertical-align: middle
}

.react-datepicker__close-icon:after {
    cursor: pointer;
    background-color: #216ba5;
    color: #fff;
    border-radius: 50%;
    height: 16px;
    width: 16px;
    padding: 2px;
    font-size: 12px;
    line-height: 1;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
    content: "×"
}

.react-datepicker__today-button {
    background: #f0f0f0;
    border-top: 1px solid #aeaeae;
    cursor: pointer;
    text-align: center;
    font-weight: 700;
    padding: 5px 0;
    clear: left
}

.react-datepicker__portal {
    position: fixed;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .8);
    left: 0;
    top: 0;
    justify-content: center;
    align-items: center;
    display: flex;
    z-index: 2147483647
}

.react-datepicker__portal .react-datepicker__day, .react-datepicker__portal .react-datepicker__day-name, .react-datepicker__portal .react-datepicker__time-name {
    width: 3rem;
    line-height: 3rem
}

@media (max-height: 550px),(max-width: 400px) {
    .react-datepicker__portal .react-datepicker__day, .react-datepicker__portal .react-datepicker__day-name, .react-datepicker__portal .react-datepicker__time-name {
        width: 2rem;
        line-height: 2rem
    }
}

.react-datepicker__portal .react-datepicker-time__header, .react-datepicker__portal .react-datepicker__current-month {
    font-size: 1.44rem
}

.react-datepicker__children-container {
    width: 13.8rem;
    margin: .4rem;
    padding-right: .2rem;
    padding-left: .2rem;
    height: auto
}

.react-datepicker__aria-live {
    position: absolute;
    -webkit-clip-path: circle(0);
    clip-path: circle(0);
    border: 0;
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    width: 1px;
    white-space: nowrap
}

.react-datepicker__calendar-icon {
    width: 1em;
    height: 1em;
    vertical-align: -.125em
}

.input_phoneRow__FsI4F {
    display: flex;
    justify-content: space-between
}

.input_phoneRow__FsI4F > div:first-child {
    position: relative;
    width: 94px;
    height: -moz-fit-content;
    height: fit-content;
    margin-right: 16px
}

.input_phoneRow__FsI4F > div:first-child input {
    padding-left: 46px
}

.subscriptionInfo_infoImage___vE8N {
    width: 50%
}

.subscriptionInfo_infoImage___vE8N span {
    position: static !important
}

.subscriptionInfo_img__Z5WWc {
    position: static !important;
    width: 100% !important;
    height: auto !important;
    min-width: auto !important;
    min-height: auto !important;
    max-width: none !important;
    max-height: none !important
}

.subscriptionInfo_infoDesc__rZsHK {
    padding: 48px 0 0
}

.subscriptionInfo_infoList__pMe_D {
    list-style: none;
    margin: 0;
    padding: 0
}

@media (min-width: 1024px) {
    .subscriptionInfo_infoDesc__rZsHK {
        position: relative;
        padding: 0 0 0 80px;
        width: 50%
    }

    .subscriptionInfo_infoDesc__rZsHK:before {
        content: "";
        position: absolute;
        top: 25%;
        left: 0;
        width: 1px;
        height: 50%;
        border-left: 1px solid #ececec
    }
}

.subscriptionList_infoItem__IwUZc {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0 0 20px;
    color: #111316
}

.subscriptionList_infoItem__IwUZc:last-child {
    padding-bottom: 0
}

.subscriptionList_infoItem__IwUZc div:first-child {
    width: 46px !important;
    position: static !important;
    margin-right: 1rem !important
}

.subscriptionList_infoItem__IwUZc div:last-child {
    width: calc(100% - 46px - 16px)
}

.table_tableContent__Owdt8 {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-direction: column
}

.yarl__fullsize {
    height: 100%;
    width: 100%
}

.yarl__relative {
    position: relative
}

.yarl__portal {
    bottom: 0;
    left: 0;
    opacity: 0;
    overflow: hidden;
    position: fixed;
    right: 0;
    top: 0;
    transition: opacity var(--yarl__fade_animation_duration, .25s) var(--yarl__fade_animation_timing_function, ease);
    z-index: var(--yarl__portal_zindex, 9999)
}

.yarl__portal_open {
    opacity: 1
}

.yarl__container {
    background-color: var(--yarl__container_background_color, var(--yarl__color_backdrop, #000));
    bottom: 0;
    left: 0;
    outline: none;
    overflow: hidden;
    overscroll-behavior: var(--yarl__controller_overscroll_behavior, contain);
    position: absolute;
    right: 0;
    top: 0;
    touch-action: var(--yarl__controller_touch_action, none);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.yarl__carousel {
    align-content: center;
    align-items: stretch;
    display: flex;
    flex: 0 0 auto;
    height: 100%;
    justify-content: center;
    opacity: var(--yarl__pull_opacity, 1);
    transform: translate(var(--yarl__swipe_offset, 0), var(--yarl__pull_offset, 0));
    width: calc(100% + (var(--yarl__carousel_slides_count) - 1) * (100% + var(--yarl__carousel_spacing_px, 0) * 1px + var(--yarl__carousel_spacing_percent, 0) * 1%))
}

.yarl__carousel_with_slides {
    -moz-column-gap: calc(var(--yarl__carousel_spacing_px, 0) * 1px + 100 / (var(--yarl__carousel_slides_count) * 100 + (var(--yarl__carousel_slides_count) - 1) * var(--yarl__carousel_spacing_percent, 0)) * var(--yarl__carousel_spacing_percent, 0) * 1%);
    column-gap: calc(var(--yarl__carousel_spacing_px, 0) * 1px + 100 / (var(--yarl__carousel_slides_count) * 100 + (var(--yarl__carousel_slides_count) - 1) * var(--yarl__carousel_spacing_percent, 0)) * var(--yarl__carousel_spacing_percent, 0) * 1%)
}

.yarl__flex_center {
    align-content: center;
    align-items: center;
    display: flex;
    justify-content: center
}

.yarl__slide {
    flex: 1;
    overflow: hidden;
    padding: calc(var(--yarl__carousel_padding_px, 0) * 1px + 100 / (var(--yarl__carousel_slides_count) * 100 + (var(--yarl__carousel_slides_count) - 1) * var(--yarl__carousel_spacing_percent, 0)) * var(--yarl__carousel_padding_percent, 0) * 1%);
    position: relative
}

[dir=rtl] .yarl__slide {
    --yarl__direction: -1
}

.yarl__slide_image {
    max-height: 100%;
    max-width: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    touch-action: var(--yarl__controller_touch_action, none);
    -moz-user-select: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none
}

.yarl__slide_image_cover {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.yarl__slide_image_loading {
    opacity: 0
}

@media screen and (min-width: 800px) {
    .yarl__slide_wrapper:not(.yarl__slide_wrapper_interactive) .yarl__slide_image {
        -webkit-backface-visibility: hidden;
        -webkit-transform: translateZ(0);
        -webkit-transform-style: preserve-3d
    }
}

.yarl__slide_placeholder {
    left: 50%;
    line-height: 0;
    position: absolute;
    top: 50%;
    transform: translateX(-50%) translateY(-50%)
}

.yarl__slide_loading {
    animation: yarl__delayed_fadein 1s linear;
    color: var(--yarl__slide_icon_loading_color, var(--yarl__color_button, hsla(0, 0%, 100%, .8)))
}

.yarl__slide_loading line {
    animation: yarl__stroke_opacity 1s linear infinite
}

.yarl__slide_loading line:first-of-type {
    animation-delay: -1.875s
}

.yarl__slide_loading line:nth-of-type(2) {
    animation-delay: -1.75s
}

.yarl__slide_loading line:nth-of-type(3) {
    animation-delay: -1.625s
}

.yarl__slide_loading line:nth-of-type(4) {
    animation-delay: -1.5s
}

.yarl__slide_loading line:nth-of-type(5) {
    animation-delay: -1.375s
}

.yarl__slide_loading line:nth-of-type(6) {
    animation-delay: -1.25s
}

.yarl__slide_loading line:nth-of-type(7) {
    animation-delay: -1.125s
}

.yarl__slide_loading line:nth-of-type(8) {
    animation-delay: -1s
}

.yarl__slide_error {
    color: var(--yarl__slide_icon_error_color, red);
    height: var(--yarl__slide_icon_error_size, 48px);
    width: var(--yarl__slide_icon_error_size, 48px)
}

@media (prefers-reduced-motion) {
    .yarl__portal, .yarl__slide {
        transition: unset
    }

    .yarl__slide_loading, .yarl__slide_loading line {
        animation: unset
    }
}

.yarl__toolbar {
    bottom: auto;
    display: flex;
    justify-content: flex-end;
    left: auto;
    padding: var(--yarl__toolbar_padding, 8px);
    position: absolute;
    right: 0;
    top: 0
}

[dir=rtl] .yarl__toolbar {
    bottom: auto;
    left: 0;
    right: auto;
    top: 0
}

.yarl__icon {
    height: var(--yarl__icon_size, 32px);
    width: var(--yarl__icon_size, 32px)
}

.yarl__button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--yarl__button_background_color, transparent);
    border: var(--yarl__button_border, 0);
    color: var(--yarl__color_button, hsla(0, 0%, 100%, .8));
    cursor: pointer;
    filter: var(--yarl__button_filter, drop-shadow(2px 2px 2px rgba(0, 0, 0, .8)));
    line-height: 0;
    margin: var(--yarl__button_margin, 0);
    outline: none;
    padding: var(--yarl__button_padding, 8px);
    -webkit-tap-highlight-color: transparent
}

.yarl__button:focus {
    color: var(--yarl__color_button_active, #fff)
}

.yarl__button:focus:not(:focus-visible) {
    color: var(--yarl__color_button, hsla(0, 0%, 100%, .8))
}

.yarl__button:focus-visible {
    color: var(--yarl__color_button_active, #fff)
}

@media (hover: hover) {
    .yarl__button:focus-visible:hover, .yarl__button:focus:hover, .yarl__button:hover {
        color: var(--yarl__color_button_active, #fff)
    }
}

.yarl__button:disabled {
    color: var(--yarl__color_button_disabled, hsla(0, 0%, 100%, .4));
    cursor: default
}

.yarl__navigation_next, .yarl__navigation_prev {
    padding: var(--yarl__navigation_button_padding, 24px 16px);
    position: absolute;
    top: 50%;
    transform: translateY(-50%)
}

.yarl__navigation_prev {
    left: 0
}

[dir=rtl] .yarl__navigation_prev {
    left: unset;
    right: 0;
    transform: translateY(-50%) rotate(180deg)
}

.yarl__navigation_next {
    right: 0
}

[dir=rtl] .yarl__navigation_next {
    left: 0;
    right: unset;
    transform: translateY(-50%) rotate(180deg)
}

.yarl__no_scroll {
    height: 100%;
    overflow: hidden;
    overscroll-behavior: none
}

@keyframes yarl__delayed_fadein {
    0% {
        opacity: 0
    }
    80% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@keyframes yarl__stroke_opacity {
    0% {
        stroke-opacity: 1
    }
    to {
        stroke-opacity: .125
    }
}

.yarl__counter {
    bottom: var(--yarl__counter_bottom, unset);
    color: var(--yarl__counter_color, var(--yarl__color_button, hsla(0, 0%, 100%, .8)));
    filter: var(--yarl__counter_filter, drop-shadow(2px 2px 2px rgba(0, 0, 0, .8)));
    left: var(--yarl__counter_left, 0);
    line-height: var(--yarl__counter_line_height, var(--yarl__icon_size, 32px));
    margin: var(--yarl__counter_margin, var(--yarl__toolbar_padding, 8px));
    padding: var(--yarl__counter_padding, var(--yarl__button_padding, 8px));
    position: var(--yarl__counter_position, absolute);
    right: var(--yarl__counter_right, unset);
    top: var(--yarl__counter_top, 0);
    -webkit-user-select: var(--yarl__counter_user_select, none);
    -moz-user-select: var(--yarl__counter_user_select, none);
    user-select: var(--yarl__counter_user_select, none)
}

.yarl__fullsize {
    height: 100%;
    width: 100%
}

.yarl__relative {
    position: relative
}

.yarl__portal {
    bottom: 0;
    left: 0;
    opacity: 0;
    overflow: hidden;
    position: fixed;
    right: 0;
    top: 0;
    transition: opacity var(--yarl__fade_animation_duration, .25s) var(--yarl__fade_animation_timing_function, ease);
    z-index: var(--yarl__portal_zindex, 9999)
}

.yarl__portal_open {
    opacity: 1
}

.yarl__container {
    background-color: var(--yarl__container_background_color, var(--yarl__color_backdrop, #000));
    bottom: 0;
    left: 0;
    outline: none;
    overflow: hidden;
    overscroll-behavior: var(--yarl__controller_overscroll_behavior, contain);
    position: absolute;
    right: 0;
    top: 0;
    touch-action: var(--yarl__controller_touch_action, none);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.yarl__carousel {
    align-content: center;
    align-items: stretch;
    display: flex;
    flex: 0 0 auto;
    height: 100%;
    justify-content: center;
    opacity: var(--yarl__pull_opacity, 1);
    transform: translate(var(--yarl__swipe_offset, 0), var(--yarl__pull_offset, 0));
    width: calc(100% + (var(--yarl__carousel_slides_count) - 1) * (100% + var(--yarl__carousel_spacing_px, 0) * 1px + var(--yarl__carousel_spacing_percent, 0) * 1%))
}

.yarl__carousel_with_slides {
    -moz-column-gap: calc(var(--yarl__carousel_spacing_px, 0) * 1px + 100 / (var(--yarl__carousel_slides_count) * 100 + (var(--yarl__carousel_slides_count) - 1) * var(--yarl__carousel_spacing_percent, 0)) * var(--yarl__carousel_spacing_percent, 0) * 1%);
    column-gap: calc(var(--yarl__carousel_spacing_px, 0) * 1px + 100 / (var(--yarl__carousel_slides_count) * 100 + (var(--yarl__carousel_slides_count) - 1) * var(--yarl__carousel_spacing_percent, 0)) * var(--yarl__carousel_spacing_percent, 0) * 1%)
}

.yarl__flex_center {
    align-content: center;
    align-items: center;
    display: flex;
    justify-content: center
}

.yarl__slide {
    flex: 1;
    overflow: hidden;
    padding: calc(var(--yarl__carousel_padding_px, 0) * 1px + 100 / (var(--yarl__carousel_slides_count) * 100 + (var(--yarl__carousel_slides_count) - 1) * var(--yarl__carousel_spacing_percent, 0)) * var(--yarl__carousel_padding_percent, 0) * 1%);
    position: relative
}

[dir=rtl] .yarl__slide {
    --yarl__direction: -1
}

.yarl__slide_image {
    max-height: 100%;
    max-width: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    touch-action: var(--yarl__controller_touch_action, none);
    -moz-user-select: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none
}

.yarl__slide_image_cover {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.yarl__slide_image_loading {
    opacity: 0
}

@media screen and (min-width: 800px) {
    .yarl__slide_wrapper:not(.yarl__slide_wrapper_interactive) .yarl__slide_image {
        -webkit-backface-visibility: hidden;
        -webkit-transform: translateZ(0);
        -webkit-transform-style: preserve-3d
    }
}

.yarl__slide_placeholder {
    left: 50%;
    line-height: 0;
    position: absolute;
    top: 50%;
    transform: translateX(-50%) translateY(-50%)
}

.yarl__slide_loading {
    animation: yarl__delayed_fadein 1s linear;
    color: var(--yarl__slide_icon_loading_color, var(--yarl__color_button, hsla(0, 0%, 100%, .8)))
}

.yarl__slide_loading line {
    animation: yarl__stroke_opacity 1s linear infinite
}

.yarl__slide_loading line:first-of-type {
    animation-delay: -1.875s
}

.yarl__slide_loading line:nth-of-type(2) {
    animation-delay: -1.75s
}

.yarl__slide_loading line:nth-of-type(3) {
    animation-delay: -1.625s
}

.yarl__slide_loading line:nth-of-type(4) {
    animation-delay: -1.5s
}

.yarl__slide_loading line:nth-of-type(5) {
    animation-delay: -1.375s
}

.yarl__slide_loading line:nth-of-type(6) {
    animation-delay: -1.25s
}

.yarl__slide_loading line:nth-of-type(7) {
    animation-delay: -1.125s
}

.yarl__slide_loading line:nth-of-type(8) {
    animation-delay: -1s
}

.yarl__slide_error {
    color: var(--yarl__slide_icon_error_color, red);
    height: var(--yarl__slide_icon_error_size, 48px);
    width: var(--yarl__slide_icon_error_size, 48px)
}

@media (prefers-reduced-motion) {
    .yarl__portal, .yarl__slide {
        transition: unset
    }

    .yarl__slide_loading, .yarl__slide_loading line {
        animation: unset
    }
}

.yarl__toolbar {
    bottom: auto;
    display: flex;
    justify-content: flex-end;
    left: auto;
    padding: var(--yarl__toolbar_padding, 8px);
    position: absolute;
    right: 0;
    top: 0
}

[dir=rtl] .yarl__toolbar {
    bottom: auto;
    left: 0;
    right: auto;
    top: 0
}

.yarl__icon {
    height: var(--yarl__icon_size, 32px);
    width: var(--yarl__icon_size, 32px)
}

.yarl__button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--yarl__button_background_color, transparent);
    border: var(--yarl__button_border, 0);
    color: var(--yarl__color_button, hsla(0, 0%, 100%, .8));
    cursor: pointer;
    filter: var(--yarl__button_filter, drop-shadow(2px 2px 2px rgba(0, 0, 0, .8)));
    line-height: 0;
    margin: var(--yarl__button_margin, 0);
    outline: none;
    padding: var(--yarl__button_padding, 8px);
    -webkit-tap-highlight-color: transparent
}

.yarl__button:focus {
    color: var(--yarl__color_button_active, #fff)
}

.yarl__button:focus:not(:focus-visible) {
    color: var(--yarl__color_button, hsla(0, 0%, 100%, .8))
}

.yarl__button:focus-visible {
    color: var(--yarl__color_button_active, #fff)
}

@media (hover: hover) {
    .yarl__button:focus-visible:hover, .yarl__button:focus:hover, .yarl__button:hover {
        color: var(--yarl__color_button_active, #fff)
    }
}

.yarl__button:disabled {
    color: var(--yarl__color_button_disabled, hsla(0, 0%, 100%, .4));
    cursor: default
}

.yarl__navigation_next, .yarl__navigation_prev {
    padding: var(--yarl__navigation_button_padding, 24px 16px);
    position: absolute;
    top: 50%;
    transform: translateY(-50%)
}

.yarl__navigation_prev {
    left: 0
}

[dir=rtl] .yarl__navigation_prev {
    left: unset;
    right: 0;
    transform: translateY(-50%) rotate(180deg)
}

.yarl__navigation_next {
    right: 0
}

[dir=rtl] .yarl__navigation_next {
    left: 0;
    right: unset;
    transform: translateY(-50%) rotate(180deg)
}

.yarl__no_scroll {
    height: 100%;
    overflow: hidden;
    overscroll-behavior: none
}

@keyframes yarl__delayed_fadein {
    0% {
        opacity: 0
    }
    80% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@keyframes yarl__stroke_opacity {
    0% {
        stroke-opacity: 1
    }
    to {
        stroke-opacity: .125
    }
}

.yarl__counter {
    bottom: var(--yarl__counter_bottom, unset);
    color: var(--yarl__counter_color, var(--yarl__color_button, hsla(0, 0%, 100%, .8)));
    filter: var(--yarl__counter_filter, drop-shadow(2px 2px 2px rgba(0, 0, 0, .8)));
    left: var(--yarl__counter_left, 0);
    line-height: var(--yarl__counter_line_height, var(--yarl__icon_size, 32px));
    margin: var(--yarl__counter_margin, var(--yarl__toolbar_padding, 8px));
    padding: var(--yarl__counter_padding, var(--yarl__button_padding, 8px));
    position: var(--yarl__counter_position, absolute);
    right: var(--yarl__counter_right, unset);
    top: var(--yarl__counter_top, 0);
    -webkit-user-select: var(--yarl__counter_user_select, none);
    -moz-user-select: var(--yarl__counter_user_select, none);
    user-select: var(--yarl__counter_user_select, none)
}
