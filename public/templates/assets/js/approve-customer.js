document.addEventListener('DOMContentLoaded', () => {
  // Getting token and status from URL
  const urlParams = new URLSearchParams(window.location.search);

  const pathSegments = window.location.pathname.split('/');
  const id = pathSegments.pop(); // Get the last segment from the path
  console.log('ID:', id); // Logging the ID for verification
  const token = urlParams.get('token');
  const status = urlParams.get('status');

  console.log('Token:', token);
  console.log('Status:', status);

  const isApproved = status === 'APPROVED';

  const formSubmit = document.getElementById('form_submit');
  const formSuccess = document.getElementById('form_success');

  const approveCheckbox = document.getElementById('approve_checkbox');
  const rejectCheckbox = document.getElementById('reject_checkbox');
  const rejectTextarea = document.getElementById('reject_reason_text');
  const submitButton = document.getElementById('submit_button');
  const rejectedForm = document.getElementById('rejected_form');

  // Initial state of the textarea and checkbox
  approveCheckbox.checked = isApproved;
  rejectCheckbox.checked = !isApproved;
  rejectTextarea.disabled = isApproved; // Disabled initially
  rejectedForm.style.display = isApproved ? 'none' : ''; // Disabled initially

  submitButton.textContent = isApproved ? 'Confirm Approve Request' : 'Confirm Reject Request';

  // Event listener for approve checkbox btn_radio
  approveCheckbox.addEventListener('click', () => {
    rejectTextarea.disabled = true; // Disable textarea when approved
    rejectTextarea.value = ''; // Clear any existing text
    rejectedForm.style.display = 'none';
    submitButton.textContent = 'Confirm Approve Request';
  });

  // Event listener for reject checkbox
  rejectCheckbox.addEventListener('click', () => {
    rejectTextarea.disabled = false; // Enable textarea when rejected
    rejectedForm.style.display = '';
    submitButton.textContent = 'Confirm Reject Request';
  });

  // Event listener for submit button
  submitButton.addEventListener('click', async (event) => {
    event.preventDefault();
    event.stopPropagation();
    // Check if reject is selected and textarea is empty
    if (rejectCheckbox.checked && rejectTextarea.value.trim() === '') {
      alert('Please provide a reason for rejection.'); // Alert if no reason is provided
      return; // Stop the submission
    }

    // Prepare the data for the POST request
    const requestData = {
      id: id,
      status: approveCheckbox.checked ? 'APPROVED' : 'DECLINED',
      note: rejectTextarea.value.trim(),
      token: token,
    };

    try {
      await fetch(`${BASE_URL}/business-partners/request/review-submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json', // Specify the type of content you're sending
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestData), // Convert data to JSON
        redirect: 'manual',
      })
        .then((response) => {
          formSubmit.style.display = 'none';
          formSuccess.style.display = '';
        })
        .catch((error) => console.error('Error:', error));

      // if (response.ok) {
      //     // Handle success
      //     const result = await response.json();
      //     console.log('Success:', result);
      //     alert('Submission successful!');
      // } else {
      //     // Handle error
      //     const error = await response.json();
      //     console.error('Error:', error);
      //     alert('Error in submission: ' + error.message);
      // }
    } catch (err) {
      console.error('Network Error:', err);
      alert('There was a network error. Please try again.');
    }
  });
});
