<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width">
    <title>Customer Setup | Outlets |&nbsp;OMS</title>
    <meta itemprop="name" content="Customer Setup | Outlets |&nbsp;OMS">
    <meta name="robots" content="noindex, nofollow">
    <meta itemprop="description" content="Customer Setup - Outlets">
    <link rel="icon" href="https://id.omni.heiway.com/distributor/favicon.ico">
    <meta property="og:title" content="Customer Setup | Outlets |&nbsp;OMS">
    <meta property="og:type" content="website">
    <meta name="next-head-count" content="20">
    <link rel="icon" href="https://id.omni.heiway.com/distributor/favicon.ico">
    <link data-next-font="" rel="preconnect" href="/" crossorigin="anonymous">

    <link rel="stylesheet" href="{{BASE_URL}}/templates/assets/css/approve-customer.css">
    <script>
        const BASE_URL = "{{BASE_URL}}";
    </script>
</head>
<body class="" data-new-gr-c-s-check-loaded="14.1224.0" data-gr-ext-installed="" data-new-gr-c-s-loaded="14.1224.0">
<div id="form_submit">
    <div class="bg-[#F5F5F6] font-inter text-[13px]">
        <div class="px-7 py-4 bg-white border-b"><img alt="OMNI Logo" loading="lazy" width="85" height="64" decoding="async" data-nimg="1" style="color:transparent" src="{{BASE_URL}}/templates/assets/img/oms-logo.svg"></div>
        <div class="mx-10 xl:mx-auto max-w-7xl p-4 xl:p-6">
            <div><h1 class="text-[18px] xl:text-[24px] text-[#111827] font-semibold"><span>Change request for customer </span><b>{{businessPartnerKey}}</b> <!-- -->is being requested for<b> {{businessPartnerContact}}</b> in<!-- --> <b>{{businessPartnerDepotKey}}</b></h1>
                <p class="text-[#6B7280] text-sm mt-4">Submitted by</p>
                <h3 class="text-xl text-[#111827] font-semibold mb-8">dat tran <!-- -->(ID: <!-- -->{{businessPartnerContactAdmin}})</h3></div>
            <div class="xl:grid xl:grid-cols-12 gap-5">
                <div class="space-y-5 xl:col-span-7 mb-5">
                    <div class="w-[50px] h-[50px] bg-oms-green text-white leading-[50px] text-center rounded-full font-medium text-2xl">1</div>
                    <p class="font-semibold text-xl">Verify submitted customer details.</p>
                    <section class="p-6 rounded-lg border bg-white">
                        <div>
                        <div>
                            <div class="flex items-start justify-between">
                                <div class="relative flex items-center gap-2"><img alt="outlet photo" loading="lazy" width="96" height="96" decoding="async" data-nimg="1" class="rounded-xl max-w-[96px] border shadow-sm text-center cursor-pointer hover:opacity-70 transition" style="color:transparent"
                                                                                   srcset="{{businessPartnerPhoto}}" src="{{businessPartnerPhoto}}">
                                    <div class="absolute bottom-0 w-[96px] h-5 bg-black/50 text-white font-semibold text-center rounded-b-xl">0 <!-- -->photos</div>
                                    <div>
                                        <h4 class="text-xs text-[#6B7280]">
                                            <div>{{businessPartnerName1}} {{businessPartnerName2}}</div>
                                        </h4>
                                        <h3 class="font-semibold text-xl text-[#111827]">
                                            <div>{{businessPartnerKey}}</div>
                                        </h3>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="text-[11px] font-bold uppercase flex items-center border rounded-full px-2 py-0.5 max-w-fit text-[#2E7D32] border-[#C8E6C9] ">
                                        <div class="h-1.5 w-1.5 self-center rounded-full bg-oms-green"></div>
                                        <span class="ml-2">{{status}}</span></div>
                                </div>
                            </div>
                            <div class="mt-6 xl:flex xl:space-x-6 xl:divide-x font-medium">
                                <div class="flex justify-between xl:flex-col xl:gap-0 xl:pl-6"><span class="text-[#6B7280] mb-1">Customer Type</span><span>{{businessPartnerType}}</span></div>
                                <div class="flex justify-between xl:flex-col xl:gap-0 xl:pl-6"><span class="text-[#6B7280] mb-1">Currency Key</span>
                                    <div class="uppercase">
                                        <div>{{businessPartnerCurrencyKey}}</div>
                                    </div>
                                </div>
                                <div class="flex justify-between xl:flex-col xl:gap-0 xl:pl-6"><span class="text-[#6B7280] mb-1">Default location</span>
                                    <div class="flex items-center gap-2"><span></span>
                                        <div class="flex items-center gap-1 bg-[#EBEBEB] text-[#888888] rounded-full px-2 py-0.5 font-bold">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="w-4 h-4">
                                                <path fill-rule="evenodd" d="M17 10a.75.75 0 01-.75.75H5.612l4.158 3.96a.75.75 0 11-1.04 1.08l-5.5-5.25a.75.75 0 010-1.08l5.5-5.25a.75.75 0 111.04 1.08L5.612 9.25H16.25A.75.75 0 0117 10z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{businessPartnerStockLocationKey}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section class="p-6 rounded-lg border bg-white">
                        <div><h2 class="font-bold">General Information</h2>
                            <div class="divide-y mt-2 font-medium">
                                <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Email address</span>
                                    <div>{{businessPartnerEmail}}</div>
                                </div>
                                <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Phone number</span><span><span>{{businessPartnerPhone}}</span></span></div>
                                <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Valid region</span><span>{{businessPartnerValidRegion}}</span></div>
                                <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Payment term</span>
                                    <div class="flex items-center gap-2"><span>{{businessPartnerPaymentTerm}}</span>
                                        <div class="flex items-center gap-1 bg-[#EBEBEB] text-[#888888] rounded-full px-2 py-0.5 font-bold">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="w-4 h-4">
                                                <path fill-rule="evenodd" d="M17 10a.75.75 0 01-.75.75H5.612l4.158 3.96a.75.75 0 11-1.04 1.08l-5.5-5.25a.75.75 0 010-1.08l5.5-5.25a.75.75 0 111.04 1.08L5.612 9.25H16.25A.75.75 0 0117 10z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{businessPartnerPaymentTermOld}}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Customer group</span>
                                    <div>{{businessPartnerCustomerGroup}}</div>
                                </div>
                                <div class="py-1.5 text-[#6B7280] font-normal space-y-1"><span class="font-semibold text-black text-sm">Description</span>
                                    <div class="rounded-lg"><span>{{businessPartnerDescription}}</span></div>
                                </div>
                                <div class="mt-3 pt-3"><h3 class="font-bold">Operating hours</h3>
                                    <table class="xl:table-fixed w-full text-[#6B7280]">
                                        <tbody>
                                        <tr class="border-b">
                                            <td class="py-3">Mon</td>
                                            <td class="py-3">
                                                <div class="flex items-center gap-2 opacity-30">
                                                    <button class="relative items-center inline-flex h-[22px] w-10 flex-shrink-0 rounded-full border-[1.5px] transition-colors duration-200 ease-in-out focus:outline-none" style="background-color:#cdcdcd;border-color:#cdcdcd"
                                                            id="headlessui-switch-:Roptdtam:" role="switch" type="button" tabindex="0" aria-checked="false" data-headlessui-state=""><span class="sr-only">Use setting</span><span aria-hidden="true" style="background-color:#ffffff"
                                                                                                                                                                                                                                   class="translate-x-1 pointer-events-none inline-block h-3.5 w-3.5 transform rounded-full transition duration-200 ease-in-out"></span>
                                                    </button>
                                                    <span class="hidden xl:block">Closed</span></div>
                                            </td>
                                            <td class="py-3 text-right text-[13px]">
                                                <div class="flex items-center gap-1"><span class="hidden xl:flex items-center justify-center shrink-0 w-4 h-4 bg-oms-green rounded-full  opacity-30"><span class="w-1.5 h-1.5 rounded-full bg-white"></span></span><input type="time" readonly=""
                                                                                                                                                                                                                                                                          class="min-w-[80px]"
                                                                                                                                                                                                                                                                          value="08:00"><span
                                                        class="px-2">-</span><input type="time" readonly="" class="min-w-[80px]" value="17:00"></div>
                                            </td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="py-3">Tue</td>
                                            <td class="py-3">
                                                <div class="flex items-center gap-2 opacity-30">
                                                    <button class="relative items-center inline-flex h-[22px] w-10 flex-shrink-0 rounded-full border-[1.5px] transition-colors duration-200 ease-in-out focus:outline-none" style="background-color:#cdcdcd;border-color:#cdcdcd"
                                                            id="headlessui-switch-:Rp9tdtam:" role="switch" type="button" tabindex="0" aria-checked="false" data-headlessui-state=""><span class="sr-only">Use setting</span><span aria-hidden="true" style="background-color:#ffffff"
                                                                                                                                                                                                                                   class="translate-x-1 pointer-events-none inline-block h-3.5 w-3.5 transform rounded-full transition duration-200 ease-in-out"></span>
                                                    </button>
                                                    <span class="hidden xl:block">Closed</span></div>
                                            </td>
                                            <td class="py-3 text-right text-[13px]">
                                                <div class="flex items-center gap-1"><span class="hidden xl:flex items-center justify-center shrink-0 w-4 h-4 bg-oms-green rounded-full  opacity-30"><span class="w-1.5 h-1.5 rounded-full bg-white"></span></span><input type="time" readonly=""
                                                                                                                                                                                                                                                                          class="min-w-[80px]"
                                                                                                                                                                                                                                                                          value="08:00"><span
                                                        class="px-2">-</span><input type="time" readonly="" class="min-w-[80px]" value="17:00"></div>
                                            </td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="py-3">Wed</td>
                                            <td class="py-3">
                                                <div class="flex items-center gap-2 opacity-30">
                                                    <button class="relative items-center inline-flex h-[22px] w-10 flex-shrink-0 rounded-full border-[1.5px] transition-colors duration-200 ease-in-out focus:outline-none" style="background-color:#cdcdcd;border-color:#cdcdcd"
                                                            id="headlessui-switch-:Rpptdtam:" role="switch" type="button" tabindex="0" aria-checked="false" data-headlessui-state=""><span class="sr-only">Use setting</span><span aria-hidden="true" style="background-color:#ffffff"
                                                                                                                                                                                                                                   class="translate-x-1 pointer-events-none inline-block h-3.5 w-3.5 transform rounded-full transition duration-200 ease-in-out"></span>
                                                    </button>
                                                    <span class="hidden xl:block">Closed</span></div>
                                            </td>
                                            <td class="py-3 text-right text-[13px]">
                                                <div class="flex items-center gap-1"><span class="hidden xl:flex items-center justify-center shrink-0 w-4 h-4 bg-oms-green rounded-full  opacity-30"><span class="w-1.5 h-1.5 rounded-full bg-white"></span></span><input type="time" readonly=""
                                                                                                                                                                                                                                                                          class="min-w-[80px]"
                                                                                                                                                                                                                                                                          value="08:00"><span
                                                        class="px-2">-</span><input type="time" readonly="" class="min-w-[80px]" value="17:00"></div>
                                            </td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="py-3">Thu</td>
                                            <td class="py-3">
                                                <div class="flex items-center gap-2 opacity-30">
                                                    <button class="relative items-center inline-flex h-[22px] w-10 flex-shrink-0 rounded-full border-[1.5px] transition-colors duration-200 ease-in-out focus:outline-none" style="background-color:#cdcdcd;border-color:#cdcdcd"
                                                            id="headlessui-switch-:Rq9tdtam:" role="switch" type="button" tabindex="0" aria-checked="false" data-headlessui-state=""><span class="sr-only">Use setting</span><span aria-hidden="true" style="background-color:#ffffff"
                                                                                                                                                                                                                                   class="translate-x-1 pointer-events-none inline-block h-3.5 w-3.5 transform rounded-full transition duration-200 ease-in-out"></span>
                                                    </button>
                                                    <span class="hidden xl:block">Closed</span></div>
                                            </td>
                                            <td class="py-3 text-right text-[13px]">
                                                <div class="flex items-center gap-1"><span class="hidden xl:flex items-center justify-center shrink-0 w-4 h-4 bg-oms-green rounded-full  opacity-30"><span class="w-1.5 h-1.5 rounded-full bg-white"></span></span><input type="time" readonly=""
                                                                                                                                                                                                                                                                          class="min-w-[80px]"
                                                                                                                                                                                                                                                                          value="08:00"><span
                                                        class="px-2">-</span><input type="time" readonly="" class="min-w-[80px]" value="17:00"></div>
                                            </td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="py-3">Fri</td>
                                            <td class="py-3">
                                                <div class="flex items-center gap-2 opacity-30">
                                                    <button class="relative items-center inline-flex h-[22px] w-10 flex-shrink-0 rounded-full border-[1.5px] transition-colors duration-200 ease-in-out focus:outline-none" style="background-color:#cdcdcd;border-color:#cdcdcd"
                                                            id="headlessui-switch-:Rqptdtam:" role="switch" type="button" tabindex="0" aria-checked="false" data-headlessui-state=""><span class="sr-only">Use setting</span><span aria-hidden="true" style="background-color:#ffffff"
                                                                                                                                                                                                                                   class="translate-x-1 pointer-events-none inline-block h-3.5 w-3.5 transform rounded-full transition duration-200 ease-in-out"></span>
                                                    </button>
                                                    <span class="hidden xl:block">Closed</span></div>
                                            </td>
                                            <td class="py-3 text-right text-[13px]">
                                                <div class="flex items-center gap-1"><span class="hidden xl:flex items-center justify-center shrink-0 w-4 h-4 bg-oms-green rounded-full  opacity-30"><span class="w-1.5 h-1.5 rounded-full bg-white"></span></span><input type="time" readonly=""
                                                                                                                                                                                                                                                                          class="min-w-[80px]"
                                                                                                                                                                                                                                                                          value="08:00"><span
                                                        class="px-2">-</span><input type="time" readonly="" class="min-w-[80px]" value="17:00"></div>
                                            </td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="py-3">Sat</td>
                                            <td class="py-3">
                                                <div class="flex items-center gap-2 opacity-30">
                                                    <button class="relative items-center inline-flex h-[22px] w-10 flex-shrink-0 rounded-full border-[1.5px] transition-colors duration-200 ease-in-out focus:outline-none" style="background-color:#cdcdcd;border-color:#cdcdcd"
                                                            id="headlessui-switch-:Rr9tdtam:" role="switch" type="button" tabindex="0" aria-checked="false" data-headlessui-state=""><span class="sr-only">Use setting</span><span aria-hidden="true" style="background-color:#ffffff"
                                                                                                                                                                                                                                   class="translate-x-1 pointer-events-none inline-block h-3.5 w-3.5 transform rounded-full transition duration-200 ease-in-out"></span>
                                                    </button>
                                                    <span class="hidden xl:block">Closed</span></div>
                                            </td>
                                            <td class="py-3 text-right text-[13px]">
                                                <div class="flex items-center gap-1"><span class="hidden xl:flex items-center justify-center shrink-0 w-4 h-4 bg-oms-green rounded-full  opacity-30"><span class="w-1.5 h-1.5 rounded-full bg-white"></span></span><input type="time" readonly=""
                                                                                                                                                                                                                                                                          class="min-w-[80px]"
                                                                                                                                                                                                                                                                          value="10:00"><span
                                                        class="px-2">-</span><input type="time" readonly="" class="min-w-[80px]" value="16:00"></div>
                                            </td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="py-3">Sun</td>
                                            <td class="py-3">
                                                <div class="flex items-center gap-2 opacity-30">
                                                    <button class="relative items-center inline-flex h-[22px] w-10 flex-shrink-0 rounded-full border-[1.5px] transition-colors duration-200 ease-in-out focus:outline-none" style="background-color:#cdcdcd;border-color:#cdcdcd"
                                                            id="headlessui-switch-:Rrptdtam:" role="switch" type="button" tabindex="0" aria-checked="false" data-headlessui-state=""><span class="sr-only">Use setting</span><span aria-hidden="true" style="background-color:#ffffff"
                                                                                                                                                                                                                                   class="translate-x-1 pointer-events-none inline-block h-3.5 w-3.5 transform rounded-full transition duration-200 ease-in-out"></span>
                                                    </button>
                                                    <span class="hidden xl:block">Closed</span></div>
                                            </td>
                                            <td class="py-3 text-right text-[13px]">
                                                <div class="flex items-center gap-1"><span class="hidden xl:flex items-center justify-center shrink-0 w-4 h-4 bg-oms-green rounded-full  opacity-30"><span class="w-1.5 h-1.5 rounded-full bg-white"></span></span><input type="time" readonly=""
                                                                                                                                                                                                                                                                          class="min-w-[80px]"
                                                                                                                                                                                                                                                                          value="10:00"><span
                                                        class="px-2">-</span><input type="time" readonly="" class="min-w-[80px]" value="16:00"></div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mb-3 border-transparent"><h2 class="mt-4 font-bold">Geographical Locations</h2>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">address line</span><span>-</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">region iso code</span><span>-</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">location type</span>
                                        <div>{{LocationType}}</div>
                                    </div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">street</span><span>{{Street}}</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">house number</span><span>{{HouseNumber}}</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">postal code</span><span>{{PostalCode}}</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">city</span><span>{{City}}</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">country iso code</span>
                                        <div>{{CountryCode}}</div>
                                    </div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">region</span><span>{{Region}}</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">latitude</span><span>{{Latitude}}</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">longitude</span><span>{{Longitude}}</span></div>
                                </div>
                                <div class="mb-3"><h2 class="mt-4 font-bold">Segmentation</h2>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Business segment</span><span>-</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Channel</span>
                                        <div>{{Channel}}</div>
                                    </div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Sub channel</span>
                                        <div>{{SubChannel}}</div>
                                    </div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Classification</span><span>{{Classification}}</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Business organizational segment</span><span>{{BusinessOrganizationalSegment}}</span></div>
                                </div>
                                <div class="mb-3"><h2 class="mt-4 font-bold">Attributes</h2>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Sale Area</span><span>{{SaleArea}}</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Sale Section</span><span>{{SaleSection}}</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Sale Sector</span><span>{{SaleSector}}</span></div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Trading End Date</span>
                                        <div>{{TradingEndDate}}</div>
                                    </div>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">Credit Block</span>
                                        <div>{{CreditBlock}}</div>
                                    </div>
                                </div>
                                <div><h2 class="mt-3 font-bold">Depot</h2>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">depot external id</span>
                                        <div class="flex items-center gap-2"><span>{{businessPartnerDepotKey}}</span>
                                            <div class="flex items-center gap-1 bg-[#EBEBEB] text-[#888888] rounded-full px-2 py-0.5 font-bold">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="w-4 h-4">
                                                    <path fill-rule="evenodd" d="M17 10a.75.75 0 01-.75.75H5.612l4.158 3.96a.75.75 0 11-1.04 1.08l-5.5-5.25a.75.75 0 010-1.08l5.5-5.25a.75.75 0 111.04 1.08L5.612 9.25H16.25A.75.75 0 0117 10z" clip-rule="evenodd"></path>
                                                </svg>
                                                {{businessPartnerDepotKey}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div><h2 class="mt-3 font-bold">Contact</h2>
                                    <div class="flex justify-between items-center py-1.5"><span class="text-[#6B7280] font-normal">External ID</span>
                                        <div>{{businessPartnerContactExternalId}}</div>
                                    </div>
                                    <div></div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
                <div class="xl:col-span-5 mb-5">
                    <div class="w-[50px] h-[50px] bg-oms-green text-white leading-[50px] text-center rounded-full font-medium text-2xl">2</div>
                    <p class="font-semibold text-xl my-5">Evaluate and process the customer request</p>
                    <section class="p-6 rounded-lg border bg-white">
                        <div class="flex items-center justify-between">
                            <div><h2 class="font-bold text-2xl text-[#111827]">Review Decision</h2>
                                <h3 class="text-xs text-[#6B7280] mt-1">Select your decision for this customer request.</h3></div>
                            <div class="flex items-center gap-x-2"></div>
                        </div>
                        <div>
                            <div class="mt-5 space-y-3">
                                <div class="bg-[#F7F7F7] rounded-lg p-3"><label class="oms-checkbox pl-[29px] text-base flex items-center gap-1 text-oms-green font-medium">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="w-6 h-6">
                                        <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"></path>
                                    </svg>
                                    Approve<input type="radio" name="btn_radio" id="approve_checkbox" checked="" value="approve"><span class="checkmark"></span></label></div>
                                <div class="bg-[#F7F7F7] rounded-lg p-3"><label class="oms-checkbox flex items-center gap-1 pl-[29px] text-base text-[#CC6100] font-medium">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 16 16" fill="none">
                                        <path d="M12.854 12.1463C12.9005 12.1927 12.9373 12.2479 12.9625 12.3086C12.9876 12.3693 13.0006 12.4343 13.0006 12.5C13.0006 12.5657 12.9876 12.6308 12.9625 12.6915C12.9373 12.7522 12.9005 12.8073 12.854 12.8538C12.8076 12.9002 12.7524 12.9371 12.6917 12.9622C12.631 12.9874 12.566 13.0003 12.5003 13.0003C12.4346 13.0003 12.3695 12.9874 12.3088 12.9622C12.2481 12.9371 12.193 12.9002 12.1465 12.8538L8.00028 8.70691L3.85403 12.8538C3.76021 12.9476 3.63296 13.0003 3.50028 13.0003C3.3676 13.0003 3.24035 12.9476 3.14653 12.8538C3.05271 12.76 3 12.6327 3 12.5C3 12.3674 3.05271 12.2401 3.14653 12.1463L7.2934 8.00003L3.14653 3.85378C3.05271 3.75996 3 3.63272 3 3.50003C3 3.36735 3.05271 3.2401 3.14653 3.14628C3.24035 3.05246 3.3676 2.99976 3.50028 2.99976C3.63296 2.99976 3.76021 3.05246 3.85403 3.14628L8.00028 7.29316L12.1465 3.14628C12.2403 3.05246 12.3676 2.99976 12.5003 2.99976C12.633 2.99976 12.7602 3.05246 12.854 3.14628C12.9478 3.2401 13.0006 3.36735 13.0006 3.50003C13.0006 3.63272 12.9478 3.75996 12.854 3.85378L8.70715 8.00003L12.854 12.1463Z"
                                              fill="currentColor"></path>
                                    </svg>
                                    Reject<input type="radio" name="btn_radio" id="reject_checkbox" value="reject"><span class="checkmark"></span></label></div>
                            </div>
                            <form class="mt-5">
                                <div id="rejected_form">
                                    <label for="reject_reason_text" class="font-medium text-sm">Please provide reason for your rejection <span class="text-oms-red">*</span></label>
                                    <textarea id="reject_reason_text" name="reason" aria-invalid="false" class="border rounded-lg outline-none px-4 py-2 mt-2 w-full resize-none" rows="4" placeholder="Reject Reason" spellcheck="false"></textarea>
                                </div>
                                <button id="submit_button" type="submit" class="flex items-center justify-center min-w-[206px] mx-auto mt-8 text-sm font-semibold bg-oms-green-6 text-white h-12 rounded-lg hover:opacity-70 transition px-4 disabled:opacity-60">
                                    Confirm Reject Request
                                </button>
                            </form>
                        </div>
                    </section>
                </div>
            </div>
        </div>
        <div style="position:fixed;z-index:99999999;top:16px;left:16px;right:16px;bottom:16px;pointer-events:none"></div>
    </div>
</div>
<div id="form_success" style="display: none">
    <div class="relative flex items-center justify-between w-screen h-screen bg-cover bg-no-repeat bg-top font-inter text-[13px]" style="background-image: url('{{BASE_URL}}/templates/assets/img/bg-outlet-detail-requested.jpg'); background-size: cover; background-repeat: no-repeat; background-position: top;">
        <div class="absolute top-10 w-full flex justify-center"><a href="/distributors/customer-setup/outlets"><img alt="OMNI Logo" loading="lazy" width="105" height="84" decoding="async" data-nimg="1" src="{{BASE_URL}}/templates/assets/img/oms-logo.svg" style="color: transparent;"></a></div>
        <div class="mx-auto flex flex-col items-center gap-2">
            <svg width="48" height="48" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="Check-Badge">
                    <circle id="Ellipse_81" cx="13" cy="13" r="12" fill="#D2FBDF" stroke="#D2FBDF" stroke-width="2"></circle>
                    <path id="icon_confirmed" d="M18.2364 9.75L12.5082 16.2987L8.66675 12.4044" stroke="#019858" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </g>
            </svg>
            <h1 class="text-2xl font-semibold">Request completed</h1>
            <p class="text-sm font-medium">Your decision has been submitted successfully</p></div>
    </div>
    <script src="{{BASE_URL}}/templates/assets/js/approve-customer.js"></script>
</div>
</body>
</html>
