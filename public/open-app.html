<html>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1" />

<head>
  <style>
    body {
      background-color: #205527;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: sans-serif;
    }

    .logo-block {
      text-align: center;
      padding: 50px;
    }

    .logo {
      margin: 0 auto;
      z-index: 2;
      width: 60px;
    }


    .button-open-app {
      display: none;
      background-image: linear-gradient(-180deg, #37AEE2 0%, #1E96C8 100%);
      border-radius: .5rem;
      box-sizing: border-box;
      color: #FFFFFF;
      font-size: 16px;
      justify-content: center;
      padding: 10px 15px;
      text-decoration: none;
      width: 100%;
      border: 0;
      cursor: pointer;
      user-select: none;
      -webkit-user-select: none;
      touch-action: manipulation;
      margin-top: 20px;
    }

    .button-open-app:hover {
      background-image: linear-gradient(-180deg, #1D95C9 0%, #17759C 100%);
    }
  </style>

  <script type="text/javascript">
    const WINDOWS_PHONE = 'WINDOWS_PHONE';
    const ANDROID = 'ANDROID';
    const APPLE = 'APPLE';
    const baseUrl = window.location.origin;
    const baseWebUrl = window.location.origin.replace('api-', '');

    var getParameterByName = function (name, url = window.location.href) {
      name = name.replace(/[\[\]]/g, '\\$&');
      var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
        results = regex.exec(url);
      if (!results) return null;
      if (!results[2]) return '';
      return decodeURIComponent(results[2].replace(/\+/g, ' '));
    };
    var getUserAgent = function () {
      var userAgent = navigator.userAgent || navigator.vendor || window.opera;
      if (/windows phone/i.test(userAgent)) {
        return WINDOWS_PHONE;
      }
      if (/android/i.test(userAgent)) {
        return ANDROID;
      }
      // iOS detection from: http://stackoverflow.com/a/9039885/177710
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return APPLE;
      }
    };

    var getAppStoreLink = function () {
      var userAgent = navigator.userAgent || navigator.vendor || window.opera;
      // Windows Phone must come first because its UA also contains "Android"
      if (/windows phone/i.test(userAgent)) {
        return 'https://www.microsoft.com/en-us/store/apps/windows-phone';
      }
      if (/android/i.test(userAgent)) {
        return 'https://play.google.com/store/apps/details?id=com.heinekenmy.dsr';
      }
      // iOS detection from: http://stackoverflow.com/a/9039885/177710
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'https://apps.apple.com/vn/app/heineken-dsr/id1634537826';
      }
      return 'https://apps.apple.com/vn/app/heineken-dsr/id1634537826';
    };
    var appIsInstalled = false;
    var countInterVal = 0;
    var countAppInstall = 0;
    var registerEvents = function () {
      window.addEventListener(
        'focus',
        function (event) {
          appIsInstalled = false;
        },
        false,
      );

      window.addEventListener(
        'blur',
        function (event) {
          appIsInstalled = true;
        },
        false,
      );
    };
    var fallbackToStore = function () {
      var intervalOpenApp = setInterval(function () {
        console.log(appIsInstalled);
        if (!appIsInstalled) {
          clearInterval(intervalOpenApp);
          window.location.replace(getAppStoreLink());
        }
      }, 5000);
    };
    var openApp = function () {
      var link = document.getElementById('open-app-link');
      var isPlatform = getUserAgent();
      if (isPlatform !== ANDROID) {
        setTimeout(() => {
          window.location.replace('dsr:/' + getParameterByName('at'));
        }, 2000);
        return true;
      } else {
        link.style.display = 'flex';
        link.setAttribute('href', 'dsr:/' + getParameterByName('at'));
        link.addEventListener('click', function () {
          var intervalOpenApp = setInterval(function () {
            console.log(appIsInstalled);
            if (!appIsInstalled) {
              clearInterval(intervalOpenApp);
              window.location.replace(getAppStoreLink());
            }
          }, 4000);
          return true;
        });

        try {
          setTimeout(() => {
            link.click();
            window.location.replace('dsr:/' + getParameterByName('at'));
          }, 3000);
        } catch (e) { }
      }
    };

    registerEvents();
    var triggerAppOpen = function () {
      openApp();
      fallbackToStore();
    };
  </script>
</head>

<body onload="triggerAppOpen()">
  <div class="logo-block">
    <img class="logo" src="/images/icon_white.png" />
    <br>
    <a id="open-app-link" href="#" class="button-open-app">Open App</a>
  </div>

</body>

</html>