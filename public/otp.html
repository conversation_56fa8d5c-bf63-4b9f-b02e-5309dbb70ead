<html>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1" />

<head>
  <style>
    body {
      background-color: #205527;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: sans-serif;
    }

    .logo-block {
      text-align: center;
      padding: 50px;
    }

    .logo {
      margin: 0 auto;
      z-index: 2;
      width: 60px;
    }

    input {
      margin-top: 10px;
      padding: 10px 15px;
      margin-bottom: 10px;
    }

    .button-open-app {
      background-image: linear-gradient(-180deg, #37AEE2 0%, #1E96C8 100%);
      border-radius: .5rem;
      box-sizing: border-box;
      color: #FFFFFF;
      font-size: 16px;
      justify-content: center;
      padding: 10px 15px;
      text-decoration: none;
      width: 100%;
      border: 0;
      cursor: pointer;
      user-select: none;
      -webkit-user-select: none;
      touch-action: manipulation;
      margin-top: 10px;
      display: block;
    }

    .button-open-app:hover {
      background-image: linear-gradient(-180deg, #1D95C9 0%, #17759C 100%);
    }

    #otp {
      font-size: 20px;
      color: red;
    }
  </style>

  <script>
    const submitForm = () => {
      const phone = document.getElementById("phone").value?.trim()
      const password = document.getElementById("password").value?.trim()
      if (phone && password) {
        var myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");

        var raw = JSON.stringify({
          "phone": phone,
          "password": password
        });

        var requestOptions = {
          method: 'POST',
          headers: myHeaders,
          body: raw,
          redirect: 'follow'
        };
        const otp = document.getElementById("otp")

        fetch("/api/users/get-otp", requestOptions)
          .then(response => response.json())
          .then(result => {
            if (result.error) {
              otp.style.color = "red"
              otp.innerHTML = result.error.message
            }
            else if (result.data) {
              otp.style.color = "white"
              otp.innerHTML = result.data.smsCode
            }
          })
          .catch(error => { otp.innerHTML = error.message });
      }
    }
  </script>
</head>

<body>
  <div class="logo-block">
    <!-- <img class="logo" src="/images/icon_white.png" /> -->
    <img class="logo" src="./images/icon_white.png" />
    <br>
    <div>
      <input type="text" class="form-control" placeholder="Phone, ex: +601128392182" value="" required id="phone">
      <br>
      <input type="password" class="form-control" placeholder="Heineken Password" required id="password">
      <br>
      <button type="button" class="button-open-app" onclick="submitForm()">Get OTP</button>
      <p id="otp"></p>
    </div>

  </div>

</body>

</html>
