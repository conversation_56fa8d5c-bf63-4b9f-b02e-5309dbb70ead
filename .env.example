NODE_ENV=develop
TZ=Asia/Kuala_Lumpur
DEFAULT_LANGUAGE=en
DEBUG_MODE=true
BASE_URL=https://hei-dsr-uat-api-d-ea.niteco.dev
BASE_WEB_URL=https://hei-dsr-uat-d-ea.niteco.dev
PORT=8000
EXPIRED_TIME_REMEMBER_LOGGED_IN=3d
APP_TIME_OUT=30000000
JWT_PRIVATE_KEY=dsr_20220718
JWT_EXPIRED_TIME=1m
OPEN_APP_LINK=https://hei-dsr-api-d-ea.niteco.dev/open-app.html
#MONGODB_URL=*********************************************
MONGODB_URL=mongodb+srv://DsrDB:<EMAIL>
MONGO_DB_NAME=dsr
MONGO_DB_USER=DsrDB
MONGO_DB_PASS=DsrDB2022

POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=123456
POSTGRES_DATABASE=dsr
POSTGRES_CERT_PATH=

# PUSH NOTIFICATION
FIREBASE_PROJECT_ID=dsr-indonesia
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>

#REDIS
CACHE_MODE=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis
REDIS_TTL=36000
REDIS_TLS=false

#DOT
#DOT_API_BASE_URL=https://hei-my-public-qa-v3.azurewebsites.net/storefrontapi
DOT_API_BASE_URL=https://acc-www.heinekendot.my/storefrontapi
DOT_API_SEARCH_ORDER=https://acc-admin.heinekendot.my
DOT_API_SEARCH_ORDER_KEY=fbc03d25-420e-4529-9ede-84d1c394ef5f

TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=1ec6f72a06ab54d82eef77ed23892c68
TWILIO_PHONE_NUMBER=+***********

PHONE_COUNTRY_CODE_DEFAULT=+60
PHONE_COUNTRY_CODES=MY|+60,VN|+84,SG|+65

# JOB EXPRESSION
MISSED_VISIT_OUTLET_EXPRESSION=59 59 23 * * *
CREATE_STATISTIC_SALES_EXPRESSION=50 59 */1 * * *
DELETE_EXPIRED_FILES_EXPRESSION=0 0 * * *
CLONE_JOURNEY_PLAN_EXPRESSION=0 0 0 * * *
CREATE_CYCLE_TIME=0 0 0 1 11 *
CRON_DOT_ERROR=0 0 * * * *
CRON_SYSTEM_REPORT=0 0 8 * * *
CRON_CACHE_OMS=0 0 */2 * * *

# APP INSIGHTS
APPINSIGHTS_CONNECTIONSTRING=

#Mail Service
SENDGRID_API_KEY=*********************************************************************
SENDGRID_SENDER_EMAIL=<EMAIL>

# PLASGATE
PLASGATE_PRIVATE_KEY=
PLASGATE_X_SECRET=
PLASGATE_ENDPOINT_URL=

# OMS
OMS_API_BASE_URL=https://hei-oms-apac-qa-kh-backend.azurewebsites.net
OMS_EMAIL=
OMS_PASSWORD=
MUST_HAVE_SKU_EXCEL_URL=

# S3
AZURE_S3_CONNECTION=
