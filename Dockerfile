FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS builder
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json .
RUN  npm install --production
COPY . .
RUN npm run build

FROM base AS final
WORKDIR /app
EXPOSE 3000

RUN npm install -g pm2

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nestjs

COPY --from=builder --chown=nestjs:nodejs /app/public ./public
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/entrypoint.sh ./entrypoint.sh
COPY --from=builder --chown=nestjs:nodejs /app/MicrosoftRSARootCertificateAuthority.crt.pem ./MicrosoftRSARootCertificateAuthority.crt.pem
COPY --from=builder --chown=nestjs:nodejs /app/DigiCertGlobalRootG2.crt.pem ./DigiCertGlobalRootG2.crt.pem
COPY --from=builder --chown=nestjs:nodejs /app/src/typeorm.config.ts ./src/typeorm.config.ts
COPY --from=builder /app/ecosystem-prod.config.js ./ecosystem-prod.config.js
COPY --from=builder /app/.env.package ./.env

RUN chown -R nestjs:nodejs /app

USER nestjs

ENV PORT 3000

RUN ["chmod", "+x", "/app/entrypoint.sh"]
ENTRYPOINT ["/app/entrypoint.sh"]

#CMD ["node", "dist/main.js"]
#CMD ["pm2-runtime", "dist/main.js"]
