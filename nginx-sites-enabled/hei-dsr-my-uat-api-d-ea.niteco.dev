server {

    server_name hei-dsr-my-uat-api-d-ea.niteco.dev;

    location / {
        gzip_static on;
                rewrite ^/route/?(.*)$ /$1 break;
                proxy_pass http://localhost:8001;
                proxy_http_version 1.1;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Forwarded-Server $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header Host $http_host;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "Upgrade";
                proxy_pass_request_headers on;
        }


        location /logs {
              root /var/www/DSR_BE_Malaysia_Staging/public;
              location ~ \.(.log|.mp4) {
                 allow all;
                 autoindex on;
              }

              try_files $uri $uri/ =404;
              auth_basic            "Basic Auth";
              auth_basic_user_file  "/etc/nginx/.htpasswd";
        }

        location /elk {
              rewrite ^/route/?(.*)$ /$1 break;
                proxy_pass http://localhost:5601;
                proxy_http_version 1.1;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Forwarded-Server $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header Host $http_host;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "Upgrade";
                proxy_pass_request_headers on;

        }


    listen 443 ssl;
    ssl_password_file /var/www/certs/global.pass;
    ssl_certificate /var/www/certs/niteco.se.pem;
    ssl_certificate_key /var/www/certs/niteco.se.key;
}
