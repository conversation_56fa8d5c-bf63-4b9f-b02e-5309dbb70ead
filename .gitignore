# compiled output
/dist
/blue_dist
/green_dist
/node_modules
.env
/.env
# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
public/tmp/
public/images/*
!public/images/flags
!public/images/icon_white.png
!public/images/competitors

package-lock.json
yarn.lock

# SpecStory explanation file
.specstory/.what-is-this.md
