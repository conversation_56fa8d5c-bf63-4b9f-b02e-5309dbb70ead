{"name": "DSR", "version": "2.1.0", "description": "", "author": "Heineken", "private": true, "license": "UNLICENSED", "engines": {"node": ">=20.0.0"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "deploy:dev": "nest build && pm2 startOrRestart ecosystem.config.js --env dev", "deploy:acc": "nest build && pm2 startOrRestart ecosystem.config.js --env acc", "deploy:uat": "nest build && pm2 startOrRestart ecosystem.config.js --env uat", "deploy:prod": "nest build && pm2 startOrRestart ecosystem.config.js --env prod", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js -d ./src/typeorm.config.ts", "migration:generate": "npm run build && npm run typeorm migration:generate -- -n updated -o", "migration:run": "npm run build && npm run typeorm migration:run", "migration:revert": "npm run build && npm run typeorm migration:revert"}, "dependencies": {"@azure/storage-blob": "^12.23.0", "@googlemaps/google-maps-services-js": "^3.3.42", "@nestjs/axios": "^3.0.1", "@nestjs/bull": "^10.0.1", "@nestjs/cache-manager": "^2.1.0", "@nestjs/cli": "^10.3.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.3", "@nestjs/jwt": "^10.2.0", "@nestjs/mongoose": "^10.0.1", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.1", "@nestjs/platform-fastify": "^10.3.1", "@nestjs/schedule": "^4.0.0", "@nestjs/schematics": "^10.1.0", "@nestjs/serve-static": "^4.0.0", "@nestjs/swagger": "^7.2.0", "@nestjs/typeorm": "^11.0.0", "@sendgrid/mail": "^7.7.0", "@types/bcryptjs": "^2.4.2", "@types/cache-manager": "^5.0.0", "@types/cache-manager-redis-store": "^3.0.0", "@types/multer": "^1.4.11", "@types/sharp": "^0.32.0", "@types/ua-parser-js": "^0.7.36", "applicationinsights": "^2.6.0", "bcryptjs": "^2.4.3", "cache-manager": "^5.2.4", "cache-manager-ioredis": "^2.1.0", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "core-js": "^3.36.1", "crypto-js": "^4.1.1", "express-http-context": "^1.2.4", "firebase-admin": "^11.11.1", "fs-extra": "^11.2.0", "ioredis": "^5.5.0", "libphonenumber-js": "^1.10.12", "lodash": "^4.17.21", "moment-timezone": "^0.5.37", "mongoose": "^7.6.3", "nanoid": "^3.3.6", "nest-winston": "^1.10.2", "nestjs-i18n": "^10.5.0", "nj-request-scope": "^1.0.8", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pg": "^8.11.3", "qs": "^6.12.3", "rand-token": "^1.0.1", "reflect-metadata": "^0.1.13", "request-ip": "^3.3.0", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "sharp": "^0.33.3", "solclientjs": "^10.15.0", "twilio": "^3.80.0", "typeorm": "^0.3.17", "ua-parser-js": "^1.0.2", "winston": "^3.8.1", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.3.1", "@types/cron": "^2.4.3", "@types/express": "^4.17.13", "@types/jest": "28.1.4", "@types/lodash": "^4.17.16", "@types/node": "^16.0.0", "@types/passport-jwt": "^3.0.6", "@types/passport-local": "^1.0.34", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.1.2", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^5.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}