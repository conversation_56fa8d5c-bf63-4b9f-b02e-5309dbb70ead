import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';

export class OutletProductCartDto {
  @ApiModelProperty({ default: null })
  productId: string;

  @ApiModelProperty({ default: null })
  sku?: string;

  @ApiModelProperty({ default: null })
  returnableEmptySku?: string;

  @ApiModelProperty({ default: 0 })
  quantity: number;

  @ApiModelProperty({ default: 'BOT' })
  packageTypeHop?: string;

  @ApiModelProperty({ default: null })
  storeId?: string;

  @ApiModelProperty({ default: true })
  isSelected: boolean;

  @ApiModelProperty({ default: true })
  isInStock?: boolean;

  @ApiModelProperty({ default: false })
  isGift?: boolean;

  @ApiModelProperty({ default: false })
  isReturnableEmpty?: boolean;
}

export class OutletUpdateCartDto {
  @ApiModelProperty()
  products: OutletProductCartDto[];
}
