import { ApiProperty } from '@nestjs/swagger';

export class OutletProductCartDto {
  @ApiProperty({ default: null })
  productId: string;

  @ApiProperty({ default: null })
  sku?: string;

  @ApiProperty({ default: null })
  returnableEmptySku?: string;

  @ApiProperty({ default: 0 })
  quantity: number;

  @ApiProperty({ default: 'BOT' })
  packageTypeHop?: string;

  @ApiProperty({ default: null })
  storeId?: string;

  @ApiProperty({ default: true })
  isSelected: boolean;

  @ApiProperty({ default: true })
  isInStock?: boolean;

  @ApiProperty({ default: false })
  isGift?: boolean;

  @ApiProperty({ default: false })
  isReturnableEmpty?: boolean;
}

export class OutletUpdateCartDto {
  @ApiProperty()
  products: OutletProductCartDto[];
}
