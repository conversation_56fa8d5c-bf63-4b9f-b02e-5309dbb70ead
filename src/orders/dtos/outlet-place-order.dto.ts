import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { OutletProductCartDto } from './outlet-update-cart.dto';
import { ArrayNotEmpty, IsArray, IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class OutletPlaceOrderDto {
  @ApiModelProperty({ default: null })
  cartValidationMessages: string[];

  @ApiModelProperty({ default: 0 })
  comment?: string;

  @ApiModelProperty({ default: null })
  preferredDeliveryDate?: string;

  @ApiModelProperty({ default: null })
  purchaseOrderNumber?: string;

  @ApiModelProperty({ default: true })
  receiverPhoneNumber: string;

  @ApiModelProperty({ default: false })
  splitAlcoholicNonAlcoholicProducts: boolean;

  @ApiModelProperty({ required: true })
  products: OutletProductCartDto[];
}

export class ItemOrderOms {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  sku: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsNumber()
  quantity: number;

  @ApiModelProperty()
  @IsOptional()
  @IsString()
  promotion: string;

  @ApiModelProperty()
  @IsOptional()
  @IsBoolean()
  isFreeItem: boolean;
}

export class OmsPlaceOrderDto {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  outletId: string;

  @ApiModelProperty()
  @IsOptional()
  @IsString()
  couponCode: string;

  @ApiModelProperty()
  @IsOptional()
  @IsString()
  deliveryInstruction?: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ItemOrderOms)
  items: ItemOrderOms[];
}
