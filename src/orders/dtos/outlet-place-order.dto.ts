import { ApiProperty } from '@nestjs/swagger';
import { OutletProductCartDto } from './outlet-update-cart.dto';
import { ArrayNotEmpty, IsArray, IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class OutletPlaceOrderDto {
  @ApiProperty({ default: null })
  cartValidationMessages: string[];

  @ApiProperty({ default: 0 })
  comment?: string;

  @ApiProperty({ default: null })
  preferredDeliveryDate?: string;

  @ApiProperty({ default: null })
  purchaseOrderNumber?: string;

  @ApiProperty({ default: true })
  receiverPhoneNumber: string;

  @ApiProperty({ default: false })
  splitAlcoholicNonAlcoholicProducts: boolean;

  @ApiProperty({ required: true })
  products: OutletProductCartDto[];
}

export class ItemOrderOms {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  sku: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  promotion: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isFreeItem: boolean;
}

export class OmsPlaceOrderDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  outletId: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  sourceSystem: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  couponCode: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  deliveryInstruction?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ItemOrderOms)
  items: ItemOrderOms[];
}

export class PlaceOrderDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  outletUCC: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  sourceSystem: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  planId: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  couponCode: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  deliveryInstruction?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ItemOrderOms)
  items: ItemOrderOms[];
}
