import { ConstantCommons, ENUM_HELPER_DATE_FORMAT } from './../../utils/constants/index';
import { IsNotEmpty, IsNumberString, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import * as moment from 'moment';

export class OrderFilterDto {
  @ApiProperty({ required: true })
  outletId: string;

  @ApiProperty({ required: false })
  outletUCC?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  orderId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  orderStatus: string;

  @ApiProperty({ required: false, default: moment().subtract(3, 'months').format(ENUM_HELPER_DATE_FORMAT.DATE) })
  @IsOptional()
  startDate: Date;

  @ApiProperty({ required: false, default: moment().format(ENUM_HELPER_DATE_FORMAT.DATE) })
  @IsOptional()
  endDate: Date;
}

export class OrderParams {
  @ApiProperty({ required: false, default: ConstantCommons.ORDER_BY_DEFAULT })
  @IsOptional()
  @Type(() => String)
  orderBy?: string;

  @ApiProperty({ required: false, default: ConstantCommons.ORDER_DESC_DEFAULT.toLocaleLowerCase() })
  @IsOptional()
  @Type(() => String)
  orderDesc?: string;
}

export class ProductQuery {
  @ApiProperty()
  @IsNotEmpty()
  @Type(() => String)
  outletId?: string;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  offset?: number;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  limit?: number;
}

export class OrderOMSFilterDto {
  @ApiProperty({ required: true })
  outletId: string;

  @ApiProperty({ required: true, default: moment().subtract(3, 'months').format(ENUM_HELPER_DATE_FORMAT.DATE) })
  start_date?: Date;

  @ApiProperty({ required: true, default: moment().format(ENUM_HELPER_DATE_FORMAT.DATE) })
  end_date?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  search?: string;

  @ApiProperty({ required: true, default: 0 })
  @IsNumberString()
  offset: number;

  @ApiProperty({ required: true, default: 10 })
  @IsNumberString()
  limit: number;

  @ApiProperty({ required: false })
  @IsOptional()
  status?: string;
}

export class OrderListFilterDto {
  @ApiProperty({ required: true })
  outletUCC: string;

  @ApiProperty({ required: true, default: moment().subtract(3, 'months').format(ENUM_HELPER_DATE_FORMAT.DATE) })
  start_date?: Date;

  @ApiProperty({ required: true, default: moment().format(ENUM_HELPER_DATE_FORMAT.DATE) })
  end_date?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  search?: string;

  @ApiProperty({ required: true, default: 0 })
  @IsNumberString()
  offset: number;

  @ApiProperty({ required: true, default: 10 })
  @IsNumberString()
  limit: number;

  @ApiProperty({ required: false })
  @IsOptional()
  status?: string;
}

export class checkStockSku {
  @ApiProperty({ required: false, default: null })
  @IsOptional()
  @Type(() => String)
  sku: string;

  @ApiProperty({ required: false, default: 0 })
  @IsOptional()
  @Type(() => Number)
  check_stock_quantity: number;
}

export class OmsOrderRecommendationDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  outletId: string;

  @ApiProperty()
  @IsOptional()
  checkStockSkus?: checkStockSku[];
}
