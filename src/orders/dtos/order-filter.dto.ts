import { ConstantCommons, ENUM_HELPER_DATE_FORMAT } from './../../utils/constants/index';
import { IsNotEmpty, IsNumberString, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import * as moment from 'moment';

export class OrderFilterDto {
  @ApiModelProperty({ required: true })
  outletId: string;

  @ApiModelProperty({ required: false })
  outletUCC?: string;

  @ApiModelProperty({ required: false })
  @IsOptional()
  orderId: string;

  @ApiModelProperty({ required: false })
  @IsOptional()
  orderStatus: string;

  @ApiModelProperty({ required: false, default: moment().subtract(3, 'months').format(ENUM_HELPER_DATE_FORMAT.DATE) })
  @IsOptional()
  startDate: Date;

  @ApiModelProperty({ required: false, default: moment().format(ENUM_HELPER_DATE_FORMAT.DATE) })
  @IsOptional()
  endDate: Date;
}

export class OrderParams {
  @ApiModelProperty({ required: false, default: ConstantCommons.ORDER_BY_DEFAULT })
  @IsOptional()
  @Type(() => String)
  orderBy?: string;

  @ApiModelProperty({ required: false, default: ConstantCommons.ORDER_DESC_DEFAULT.toLocaleLowerCase() })
  @IsOptional()
  @Type(() => String)
  orderDesc?: string;
}

export class ProductQuery {
  @ApiModelProperty()
  @IsNotEmpty()
  @Type(() => String)
  outletId?: string;

  @ApiModelProperty()
  @IsOptional()
  @Type(() => Number)
  offset?: number;

  @ApiModelProperty()
  @IsOptional()
  @Type(() => Number)
  limit?: number;
}

export class OrderOMSFilterDto {
  @ApiModelProperty({ required: true })
  outletId: string;

  @ApiModelProperty({ required: false, default: moment().subtract(3, 'months').format(ENUM_HELPER_DATE_FORMAT.DATE) })
  start_date?: Date;

  @ApiModelProperty({ required: false, default: moment().format(ENUM_HELPER_DATE_FORMAT.DATE) })
  end_date?: Date;

  @ApiModelProperty({ required: false })
  @IsOptional()
  search?: string;

  @ApiModelProperty({ required: true, default: 0 })
  @IsNumberString()
  offset: number;

  @ApiModelProperty({ required: true, default: 10 })
  @IsNumberString()
  limit: number;

  @ApiModelProperty({ required: false })
  @IsOptional()
  status?: string;
}

export class checkStockSku {
  @ApiModelProperty({ required: false, default: null })
  @IsOptional()
  @Type(() => String)
  sku: string;

  @ApiModelProperty({ required: false, default: 0 })
  @IsOptional()
  @Type(() => Number)
  check_stock_quantity: number;
}

export class OmsOrderRecommendationDto {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  outletId: string;

  @ApiModelProperty()
  @IsOptional()
  checkStockSkus?: checkStockSku[];
}
