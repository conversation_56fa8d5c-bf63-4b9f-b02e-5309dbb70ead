import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';

export type SaleOrderStatisticsDocument = SaleOrderStatistics & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
})
export class SaleOrderStatistics extends BaseSchema {
  @Prop({ required: true, index: true })
  saleRepId: string;

  @Prop({ required: true, index: true })
  outletId: string;

  @Prop({ required: true, index: true })
  outletDotId: string;

  @Prop({ required: true, index: true })
  month: number;

  @Prop({ required: true, index: true })
  year: number;

  @Prop({ default: 0 })
  totalConfirmed: number; // total amount of orders

  @Prop({ default: 0 })
  totalCancelled: number; // total amount of orders

  @Prop({ default: 0 })
  totalConfirmedVolume: number;

  @Prop({ default: 0 })
  totalCancelledVolume: number;

  @Prop({ default: 0 })
  totalOutletConfirmed: number;

  @Prop({ default: 0 })
  totalOutletCancelled: number;
}

export const SaleOrderStatisticsSchema = SchemaFactory.createForClass(SaleOrderStatistics);
