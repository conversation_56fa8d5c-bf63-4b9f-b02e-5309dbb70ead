import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MSchema, Types } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { Outlet } from '../../outlets/schemas/outlet.schema';
import { User } from 'src/users/schemas/user.schema';
import { OutletJourneyPlanning } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';

export type OrderOMSDocument = OrderOMS & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
})
export class OrderOMS extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  salesRep: User;

  @Prop({ type: String })
  orderId: string;

  @Prop({ type: String, default: null })
  depotId: string;

  @Prop({ type: String, default: null })
  displayId: string;

  @Prop({ type: Date })
  orderDate: Date;

  @Prop({ type: Date })
  orderDateUTC: Date;

  @Prop({ type: String })
  orderStatus: string;

  @Prop({ type: String, default: 'REP' })
  orderSource: string;

  @Prop({ nullable: true, default: 0 })
  orderVolume: number;

  @Prop({ nullable: true, default: 0 })
  orderTotal: number;

  @Prop({ type: [MSchema.Types.Mixed], index: true })
  invoices: any;

  @Prop({ type: Types.ObjectId, ref: OutletJourneyPlanning.name })
  jp: OutletJourneyPlanning;
}

export const OrderOMSSchema = SchemaFactory.createForClass(OrderOMS);
