import { Body, Controller, Get, HttpException, HttpStatus, Post, Query, Request, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { I18n, I18nContext } from 'nestjs-i18n';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { MockService } from '../external/services/mock.service';
import { OutletsService } from '../outlets/services/outlets.service';
import { ApiException } from '../shared/api-exception.model';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { Roles } from '../shared/decorator/roles.decorator';
import { RolesGuard } from '../shared/guards/roles.guard';
import { ApiResponse } from '../shared/response/api-response';
import { ConstantRoles } from '../utils/constants/role';
import { OmsOrderRecommendationDto, OrderOMSFilterDto } from './dtos/order-filter.dto';
import { OmsPlaceOrderDto } from './dtos/outlet-place-order.dto';
import { OrdersService } from './services/orders.service';
import { OmsService } from 'src/external/services/oms.service';
import { Types } from 'mongoose';
import { OmsCacheService } from 'src/external/services/oms-cache.service';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { ConstantCommons } from '../utils/constants';
import { isEmptyObjectOrArray } from '../utils';

@ApiTags('Orders')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/orders')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class OrdersController {
  constructor(
    private readonly _outletService: OutletsService,
    private readonly orderService: OrdersService,
    private readonly omsService: OmsService,
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    private mockService: MockService,
    private readonly omsCacheService: OmsCacheService,
  ) {}

  @Post('oms-place-order')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Place Order OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async placeOrderOMS(@Body() body: OmsPlaceOrderDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      if (currentUser.isTestAccount) {
        const order = await this.mockService.placeOrderOMS(body, `${currentUser.firstname} ${currentUser.lastname}`);
        return new ApiResponse(order);
      }
      const order = await this.orderService.createOrderOms(body, i18n, currentUser);
      return new ApiResponse(order);
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Post('oms-place-temporary-order')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Place Temporary Order OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async placeTemporaryOrderOMS(@Body() body: OmsPlaceOrderDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      if (currentUser.isTestAccount) {
        const order = await this.mockService.placeOrderOMS(body, `${currentUser.firstname} ${currentUser.lastname}`);
        return new ApiResponse(order);
      }
      const order = await this.orderService.createTemporaryOrderOms(body, i18n, currentUser);
      return new ApiResponse(order);
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Get('latest-order-oms')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Get Latest Order OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getLatestOrderOms(@Query('outletId') outletId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      if (currentUser.isTestAccount) {
        const latestOrder = await this.mockService.getLatestOrderOms({
          outlet: outletId,
        });
        return new ApiResponse({ lastUpdate: latestOrder?.created_at || null, latestOrder: latestOrder || null });
      }
      const outlet = await this._outletService.findById(outletId);
      if (!outlet) {
        throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
      }
      const orders =
        (await this.omsService.getListOrdersOMS({
          outletExternalID: outlet.ucc,
          depotExternalID: outlet.depotId,
          start_date: null,
          end_date: null,
          limit: 1,
          offset: 0,
          search: null,
          status: null,
        })) || [];
      let latestOrder = null;
      if (orders.length) {
        latestOrder = orders[0];
        this.omsCacheService.model
          .updateOne(
            { outlet: new Types.ObjectId(outletId) },
            {
              latestOrder: latestOrder,
              orders: orders.slice(0, 5),
            },
            {
              upsert: true,
            },
          )
          .then()
          .catch();
      }

      return new ApiResponse({ lastUpdate: latestOrder?.created_at || null, latestOrder: latestOrder || null });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Get('list-products-oms')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Get list products OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getListProductsOms(@Query('outletId') outletId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      if (currentUser.isTestAccount) {
        const mockProducts = this.mockService.getListProductsOms();
        return new ApiResponse({ mustHaveSKUs: mockProducts || [], listProducts: mockProducts || [] });
      }
      const outlet = await this._outletService.findById(outletId);
      if (!outlet) {
        throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
      }
      const dataCached = await this.omsService.getCachedDataByOutlet({
        outletId: new Types.ObjectId(outletId),
        useDb: true,
        project: {
          products: 1,
        },
        outletExternalId: outlet.ucc,
      });
      if (!dataCached) {
        throw new HttpException(await i18n.translate(`common.oms.not_connected`), HttpStatus.BAD_REQUEST);
      }
      const lastCheckStock = await this.outletJourneyPlanningService.findOne(
        {
          outlet: outlet._id,
          checkStock: { $ne: null },
        },
        {},
        { sort: { day: -1, rescheduledDay: -1 } },
      );
      const products =
        dataCached?.products && dataCached?.products?.some((p) => p.average_quantity === undefined)
          ? await this.omsService.getOrderAverageQuantity(outlet.depotId, outlet, dataCached?.products)
          : dataCached?.products;
      let mustHaveSKUs = products?.filter((p) => p.tagging == ConstantCommons.MUST_HAVE_SKU_LABEL);
      if (lastCheckStock?.checkStock?.listProductsChecked?.length) {
        for (const element of mustHaveSKUs) {
          const found = lastCheckStock?.checkStock?.listProductsChecked.find((p) => p.sku == element.sku);
          if (found) {
            element.check_stock_quantity = found.check_stock_quantity;
          }
        }
        mustHaveSKUs = mustHaveSKUs.sort((a, b) => a.check_stock_quantity - b.check_stock_quantity);
      }

      const listProducts = products?.filter((p) => p.tagging != ConstantCommons.MUST_HAVE_SKU_LABEL);
      return new ApiResponse({ mustHaveSKUs: mustHaveSKUs || [], listProducts: listProducts || [] });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Get('list-promotions-oms')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Get list promotions OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getListPromotionsOms(@Query('outletId') outletId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      if (currentUser.isTestAccount) {
        const promotions = this.mockService.getListPromotionsOms();
        return new ApiResponse({ listPromotions: promotions || [] });
      }
      const outlet = await this._outletService.findById(outletId);
      if (!outlet) {
        throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
      }
      const dataCached = await this.omsService.getCachedDataByOutlet({
        outletId: new Types.ObjectId(outletId),
        useDb: true,
        project: {
          promotions: 1,
        },
      });
      if (!dataCached) {
        throw new HttpException(await i18n.translate(`common.oms.not_connected`), HttpStatus.BAD_REQUEST);
      }
      const promotions =
        dataCached?.promotions && dataCached?.promotions?.some((p) => p.average_quantity === undefined)
          ? await this.omsService.getOrderAverageQuantity(outlet.depotId, outlet, dataCached?.promotions, true)
          : dataCached?.promotions;
      return new ApiResponse({ listPromotions: promotions || [] });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Get('list-orders-oms')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Get list orders OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getListOrdersOms(@Query() query: OrderOMSFilterDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      if (currentUser.isTestAccount) {
        const orders = await this.mockService.getListOrdersOms(query);
        return new ApiResponse({ listOrders: orders || [] });
      }
      const outlet = await this._outletService.findById(query.outletId);
      if (!outlet) {
        throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
      }
      if (!outlet.ucc || !outlet.depotId) {
        throw new HttpException(await i18n.translate(`common.oms.not_connected`), HttpStatus.BAD_REQUEST);
      }
      const orders =
        (await this.omsService.getListOrdersOMS({
          outletExternalID: outlet.ucc,
          depotExternalID: outlet.depotId,
          start_date: query.start_date,
          end_date: query.end_date,
          limit: Number(query.limit),
          offset: Number(query.offset),
          search: query.search || null,
          status: query.status || null,
        })) || [];
      this.orderService.syncOrderOmsToRep(currentUser, outlet, orders).catch().then();
      return new ApiResponse({ listOrders: orders || [] });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Post('recommendations')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Get list orders OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getListOrdersRecommendationOms(@Body() body: OmsOrderRecommendationDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      let { checkStockSkus } = body;
      if (currentUser.isTestAccount) {
        const products = this.mockService.getListOrdersOms({ outletId: body.outletId, limit: 10, offset: 0 });
        return new ApiResponse({ mustHaveSKUs: products || [], listProducts: products || [] });
      }
      const outlet = await this._outletService.findById(body.outletId);
      if (!outlet || !outlet?.depotId) {
        throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
      }

      if (isEmptyObjectOrArray(checkStockSkus)) {
        const jp = await this.outletJourneyPlanningService.getTodayPlanByOutlet(outlet._id);
        if (!isEmptyObjectOrArray(jp?.checkStock?.listProductsChecked)) {
          checkStockSkus = jp.checkStock?.listProductsChecked;
        }
      }
      const listProducts = await this.omsService.getOrderRecommendation(outlet.depotId, outlet, checkStockSkus);
      return new ApiResponse({ listProducts: listProducts || [] });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }
}
