import { SaleOrderStatisticService } from './services/sale-order-statistic.service';
import { MongooseModule } from '@nestjs/mongoose';
import { forwardRef, Module } from '@nestjs/common';
import { OrdersController } from './orders.controller';
import { OrdersService } from './services/orders.service';
import { HttpModule } from '@nestjs/axios';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';
import { ExternalModule } from '../external/external.module';
import { OutletsModule } from '../outlets/outlets.module';
import { SaleRepModule } from '../sale-rep/sale-rep.module';
import { SettingsModule } from '../settings/settings.module';
import { SaleOrderStatistics, SaleOrderStatisticsSchema } from './schemas/sale-order-statistics.schema';
import { DistributorModule } from '../distributor/distributor.module';
import { OrderOMS, OrderOMSSchema } from './schemas/order-oms.schema';
import { JourneyPlanningsModule } from 'src/journey-plannings/journey-plannings.module';
import { OmsModule } from "src/oms/oms.module";

@Module({
  imports: [
    HttpModule.register({
      timeout: 20000,
      maxRedirects: 5,
    }),
    MongooseModule.forFeature([
      { name: OrderOMS.name, schema: OrderOMSSchema },
      { name: SaleOrderStatistics.name, schema: SaleOrderStatisticsSchema },
    ]),
    forwardRef(() => AuthModule),
    forwardRef(() => UsersModule),
    forwardRef(() => JourneyPlanningsModule),
    OmsModule,
    ExternalModule,
    OutletsModule,
    SaleRepModule,
    SettingsModule,
    DistributorModule,
  ],
  controllers: [OrdersController],
  providers: [OrdersService, SaleOrderStatisticService],
  exports: [OrdersService, SaleOrderStatisticService],
})
export class OrdersModule {}
