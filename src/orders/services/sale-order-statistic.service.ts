import { SaleOrderStatistics, SaleOrderStatisticsDocument } from '../schemas/sale-order-statistics.schema';
import { isEmptyObjectOrArray, printLog } from '../../utils';
import { Model } from 'mongoose';
import { BaseService } from 'src/shared/services/base-service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { SaleRepOutletRelationTrackingService } from '../../outlets/services/sale-rep-outlet-relation-tracking.service';
import { SaleRepOutletRelationStatusTracking } from '../../outlets/enums/salerep-outlet-relation-status-tracking.enum';
import { OutletStatus } from '../../outlets/enums/outlet-status.enum';
import { SaleRepOutletRelationService } from '../../outlets/services/sale-rep-outlet-relation.service';
import { OpCos } from 'src/config';

@Injectable()
export class SaleOrderStatisticService extends BaseService<SaleOrderStatistics> {
  constructor(
    @InjectModel(SaleOrderStatistics.name)
    private readonly _saleOrderStatistics: Model<SaleOrderStatisticsDocument>,
    private readonly _saleRepOutletRelationTrackingService: SaleRepOutletRelationTrackingService,
    private readonly _saleRepOutletRelationService: SaleRepOutletRelationService,
  ) {
    super();
    this.model = _saleOrderStatistics;
    if (!process.env.NODE_APP_INSTANCE || (process.env.NODE_APP_INSTANCE && parseInt(process.env.NODE_APP_INSTANCE) === 0)) {
      this.removeOldData().then().catch();
    }
  }

  async removeOldData() {
    const removed = await this.model.deleteMany({
      beVersion: { $exists: false },
    });
  }

  async createOrUpdateSaleOrderStatistic(data: SaleOrderStatistics) {
    try {
      if (data?.saleRepId && data?.outletId) {
        data.totalOutletCancelled = 0;
        data.totalOutletConfirmed = 0;
        return this.findOneOrCreate(
          {
            saleRepId: data?.saleRepId,
            outletId: data?.outletId,
            month: data?.month,
            year: data?.year,
          },
          data,
        );
      }
    } catch (e) {
      return null;
    }
  }

  async calculateSaleStatisticID(saleRepId: string, statisticDay: string) {
    try {
      const momentStatisticDay = moment(statisticDay).tz(process.env.TZ);
      const statisticMonth = momentStatisticDay.get('month') + 1;
      const statisticYear = momentStatisticDay.year();

      const getTotalOrderByStatus = await this.model
        .aggregate()
        .match({
          saleRepId: saleRepId,
          month: statisticMonth,
          year: statisticYear,
        })
        .group({
          _id: '$month',
          totalCancelled: { $sum: '$totalCancelled' },
          totalConfirmed: { $sum: '$totalConfirmed' },
          totalConfirmedVolume: {
            $sum: '$totalConfirmedVolume',
          },
        });
      return getTotalOrderByStatus?.[0]?.totalConfirmedVolume ?? 0;
    } catch (error) {
      printLog('error', error);
      return 0;
    }
  }

  async calculateSaleStatistic(saleRepId: string, statisticDay: string) {
    try {
      if (process.env.OPCO == OpCos.Indonesia) {
        return await this.calculateSaleStatisticID(saleRepId, statisticDay);
      }
      const momentStatisticDay = moment(statisticDay).tz(process.env.TZ);
      const statisticMonth = momentStatisticDay.get('month') + 1;
      const statisticYear = momentStatisticDay.year();

      const getTotalOrderByStatus = await this.model
        .aggregate()
        .match({
          saleRepId: saleRepId,
          month: statisticMonth,
          year: statisticYear,
        })
        .group({
          _id: '$month',
          totalCancelled: { $sum: '$totalCancelled' },
          totalConfirmed: { $sum: '$totalConfirmed' },
          totalOutletConfirmed: {
            $sum: {
              $cond: {
                if: {
                  $gte: ['$totalConfirmed', 0],
                },
                then: 1,
                else: 0,
              },
            },
          },
        });
      return getTotalOrderByStatus?.[0]?.totalConfirmed ?? 0;
    } catch (error) {
      printLog('error', error);
      return 0;
    }
  }

  async calculateMaboStatisticBySaleRepId(saleRepId: string, statisticDay: string) {
    const getAllOutletAssignedConfirmed = await this._saleRepOutletRelationService.findAllActiveOutletBySaleRepId(saleRepId);
    const uniqueOutletAssignedConfirmed = [
      ...new Set(
        getAllOutletAssignedConfirmed.filter((el) => el.outlet && el.outlet?.outletDotId && el.outlet?.status == OutletStatus.ACTIVE).map((el) => el?.outlet?._id?.toString()),
      ),
    ];
    const target = uniqueOutletAssignedConfirmed?.length || 0;
    const result = await this.calculateMaboStatistic(saleRepId, uniqueOutletAssignedConfirmed, statisticDay);
    if (!isEmptyObjectOrArray(result)) {
      const { totalConfirmed, totalOutletConfirmed } = result;
      return {
        current: totalConfirmed > 0 ? totalOutletConfirmed : 0,
        target,
      };
    }
    return {
      current: 0,
      target,
    };
  }

  async calculateMaboStatistic(saleRepId: string, allOutletAssignedConfirmed: any = [], statisticDay: string) {
    try {
      const momentStatisticDay = moment(statisticDay).tz(process.env.TZ);
      const statisticMonth = momentStatisticDay.get('month') + 1;
      const statisticYear = momentStatisticDay.year();

      const startDate = momentStatisticDay.clone().startOf('month').endOf('d').toDate();
      const endDate = momentStatisticDay.clone().endOf('d').toDate();

      let outletConnectedFullMoth = allOutletAssignedConfirmed;
      const outletConnectionBlocked = await this._saleRepOutletRelationTrackingService.findAll({
        outlet: { $in: allOutletAssignedConfirmed },
        saleRep: saleRepId,
        status: SaleRepOutletRelationStatusTracking.SHUTDOWN,
        createdAt: { $lte: startDate, $gte: endDate },
      });

      if (!isEmptyObjectOrArray(outletConnectionBlocked)) {
        const outletConnectedFullMothIds = outletConnectionBlocked.map((o) => o.outlet._id);
        outletConnectedFullMoth = allOutletAssignedConfirmed?.filter((outletId) => outletConnectedFullMothIds.indexOf(outletId) < 0);
      }

      if (outletConnectedFullMoth?.length <= 0) {
        return null;
      }

      const getTotalOrderByStatus = await this.model
        .aggregate()
        .match({
          saleRepId: saleRepId,
          outletId: { $in: outletConnectedFullMoth },
          month: statisticMonth,
          year: statisticYear,
        })
        .group({
          _id: '$month',
          totalCancelled: { $sum: '$totalCancelled' },
          totalConfirmed: { $sum: '$totalConfirmed' },
          // totalCancelledVolume: { $sum: '$totalCancelledVolume' },
          // totalConfirmedVolume: { $sum: '$totalConfirmedVolume' },
        });

      const getTotalOutletByStatus = await this.model
        .aggregate()
        .match({
          saleRepId: saleRepId,
          outletId: { $in: outletConnectedFullMoth },
          month: statisticMonth,
          totalConfirmed: { $gt: 0 },
          year: statisticYear,
        })
        .group({
          _id: '$outletId',
        });

      if (isEmptyObjectOrArray(getTotalOrderByStatus[0])) return {};

      return { ...getTotalOrderByStatus[0], totalOutletConfirmed: getTotalOutletByStatus?.length };
    } catch (error) {
      printLog('error', error);
      return 0;
    }
  }
}
