import { Model, Types } from 'mongoose';
import { BaseService } from 'src/shared/services/base-service';
import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';

import { InjectModel } from '@nestjs/mongoose';
import { User } from 'src/users/schemas/user.schema';
import { I18nContext } from 'nestjs-i18n';
import { OmsPlaceOrderDto } from '../dtos/outlet-place-order.dto';
import { OutletsService } from 'src/outlets/services/outlets.service';
import { OmsService } from 'src/external/services/oms.service';
import { OrderOMS, OrderOMSDocument } from '../schemas/order-oms.schema';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { OmsRepReportsCalculatorsService } from 'src/oms/services/rep-reports-calculator.service';
import { OmsPlaceOrder } from 'src/offline-mode-v2/dtos/sync-offline-data.dto';
import * as moment from 'moment-timezone';

@Injectable()
export class OrdersService extends BaseService<OrderOMS> {
  constructor(
    @InjectModel(OrderOMS.name)
    private readonly orderOMSDocument: Model<OrderOMSDocument>,
    private readonly outletService: OutletsService,
    @Inject(forwardRef(() => OutletJourneyPlanningService))
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    @Inject(forwardRef(() => OmsService)) private readonly omsService: OmsService,
    private readonly omsRepReportsCalculatorsService: OmsRepReportsCalculatorsService,
  ) {
    super();
    this.model = orderOMSDocument;
  }

  async createOrderOms(body: OmsPlaceOrderDto, i18n: I18nContext, currentUser: User) {
    const outlet = await this.outletService.findById(body.outletId);
    if (!outlet) {
      throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
    }
    const items = [];
    body.items.forEach((item) => {
      items.push({
        sku: item.sku,
        quantity: item.quantity,
        metadata: {},
      });
    });
    const order = await this.omsService.createOrderOMS({
      outletId: outlet._id,
      depotExternalID: outlet.depotId,
      outletExternalID: outlet.ucc,
      email: currentUser.email || process.env.OMS_EMAIL,
      items,
      couponCodes: (body?.couponCode || '').split(',').map((e) => e.trim()),
      deliveryInstruction: body.deliveryInstruction,
      sourceSystem: body?.sourceSystem || 'REP',
    });
    if (order) {
      await this.create({
        salesRep: currentUser._id,
        outlet: new Types.ObjectId(body.outletId),
        orderDate: moment(order?.created_at),
        orderDateUTC: moment(order?.created_at).utc(),
        orderId: order?.id,
        displayId: order.display_id,
        depotId: outlet.depotId,
        orderStatus: order?.status,
        orderTotal: order?.total || 0,
        orderVolume: (order?.items || []).reduce((sum, item) => sum + item.volume * item.quantity, 0),
        sourceSystem: order?.source_system || 'REP',
      });
      this.outletJourneyPlanningService
        .checkHasOrder(String(currentUser._id), String(body.outletId))
        .then(() => {
          this.omsRepReportsCalculatorsService.executeDSRStatistic({ salesRepId: currentUser.saleRepId }).then().catch();
        })
        .catch();
    }

    return order;
  }

  /**
   *
   * @param salesRep
   * @param outlet
   * @param orders
   */
  async syncOrderOmsToRep(salesRep, outlet, orders) {
    if (orders?.length) {
      for (const order of orders) {
        await this.create({
          salesRep: salesRep._id,
          outlet: outlet._id,
          orderId: order?.id,
          orderDate: moment(order?.created_at),
          orderDateUTC: moment(order?.created_at).utc(),
          orderStatus: order?.status,
          displayId: order.display_id,
          depotId: outlet.depotId,
          orderTotal: order?.total || 0,
          orderVolume: (order?.items || []).reduce((sum, item) => sum + item.volume * item.quantity, 0),
          sourceSystem: order?.source_system || 'REP',
          invoices:
            order?.invoices?.map((invoice: any) => ({
              invoiceId: invoice.id,
              amount: invoice.total,
              date: invoice.created_at,
            })) || [],
        });
      }
    }
  }

  async createTemporaryOrderOms(body: OmsPlaceOrderDto, i18n: I18nContext, currentUser: User) {
    const outlet = await this.outletService.findById(body.outletId);
    if (!outlet) {
      throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
    }
    const items = [];
    body.items.forEach((item) => {
      items.push({
        sku: item.sku,
        quantity: item.quantity,
        metadata: {},
      });
    });

    return await this.omsService.createTemporaryOrderOMS({
      depotExternalID: outlet.depotId,
      outletExternalID: outlet.ucc,
      email: currentUser.email || process.env.OMS_EMAIL,
      items,
      couponCodes: (body?.couponCode || '').split(',').map((e) => e.trim()),
      deliveryInstruction: body.deliveryInstruction,
      sourceSystem: body?.sourceSystem,
    });
  }

  /**
   * Create offline orders for an outlet
   * @param outletId - Outlet ID
   * @param list - Array of orders to create
   * @param i18n - Internationalization context
   * @param currentUser - Current user
   * @returns Result of order creation
   */
  async createOrderOmsOffline(outletId: string, list: OmsPlaceOrder[], i18n: I18nContext, currentUser: User) {
    try {
      // Validate outlet exists
      const outlet = await this.outletService.findById(outletId);
      if (!outlet) {
        throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
      }

      // Process orders in parallel for better performance
      const orderPromises = list.map(async (orderItem) => {
        try {
          // Transform items
          const items = orderItem.items.map((item) => ({
            sku: item.sku,
            quantity: item.quantity,
            metadata: {},
          }));

          // Create order in OMS
          const orderInfo = await this.omsService.createOrderOMS({
            outletId: outlet._id,
            depotExternalID: outlet.depotId,
            outletExternalID: outlet.ucc,
            email: currentUser.email || process.env.OMS_EMAIL,
            items,
            couponCodes: (orderItem?.couponCode || '').split(',').map((e) => e.trim()),
            deliveryInstruction: orderItem.deliveryInstruction,
          });

          if (!orderInfo) {
            throw new Error('Failed to create order in OMS');
          }

          // Create local order record
          await this.create({
            salesRep: currentUser._id,
            outlet: outlet._id,
            orderDate: new Date(),
            orderId: orderInfo.id,
            depotId: outlet.depotId,
            displayId: orderInfo.display_id,
          });

          // Update journey planning (fire and forget)
          this.outletJourneyPlanningService.checkHasOrder(String(currentUser._id), String(outlet._id)).catch((error) => {
            console.error('Error updating journey planning:', error);
          });

          return {
            offlineOrderId: orderItem.offlineOrderId,
            success: true,
            message: '',
            orderInfo,
          };
        } catch (error) {
          return {
            offlineOrderId: orderItem.offlineOrderId,
            success: false,
            message: error.message,
          };
        }
      });

      // Wait for all orders to be processed
      const results = await Promise.all(orderPromises);

      // Check if any orders failed
      const failedOrders = results.filter((result) => !result.success);
      const success = failedOrders.length === 0;
      const message = failedOrders.length > 0 ? `Failed to create ${failedOrders.length} orders` : '';

      return {
        key: 'orders',
        success,
        message,
        items: results,
      };
    } catch (error) {
      console.error('Error in createOrderOmsOffline:', error);
      return {
        key: 'orders',
        success: false,
        message: error.message,
        items: [],
      };
    }
  }

  /**
   * Get outlets that have not placed orders in the specified date range
   * @param salesRepId - Sales rep ID
   * @param outlets - Array of outlets to check
   * @param startDate - Start date for checking orders
   * @param endDate - End date for checking orders
   * @returns Array of outlets without orders
   */
  async getOutletHasNotOrder(salesRepId: any, outlets: any, startDate: Date, endDate: Date) {
    try {
      if (!outlets?.length) {
        return [];
      }

      const outletIds = outlets.map((o) => o.outletId);

      // Get outlets that have orders in the date range
      const outletsWithOrders = await this.orderOMSDocument.distinct('outlet', {
        salesRep: salesRepId,
        outlet: { $in: outletIds },
        orderDate: { $gte: startDate, $lte: endDate },
      });

      // Filter out outlets that have orders
      const outletsWithoutOrders = outlets.filter((outlet) => !outletsWithOrders.some((orderOutlet) => orderOutlet.toString() === outlet.outletId.toString()));

      return outletsWithoutOrders;
    } catch (error) {
      console.error('Error in getOutletHasNotOrder:', error);
      return outlets || [];
    }
  }

  /**
   * Get inactive outlets based on order history
   * @param salesRepId - Sales rep ID
   * @param outlets - Array of outlets to check
   * @param startDate - Start date for active period
   * @param endDate - End date for active period
   * @returns Array of inactive outlets
   */
  async getInactiveOutlets(salesRepId: any, outlets: any, startDate: Date, endDate: Date) {
    const result = await this.getActiveAndInactiveOutlets([salesRepId], outlets, startDate, endDate);
    return result.inactive;
  }

  /**
   * Get active and inactive outlets based on order history
   * @param salesRepIds - Array of sales rep IDs
   * @param outlets - Array of outlets to check
   * @param startDate - Start date for active period
   * @param endDate - End date for active period
   * @returns Object with active and inactive outlets
   */
  async getActiveAndInactiveOutlets(salesRepIds: any[], outlets: any, startDate: Date, endDate: Date) {
    try {
      if (!outlets?.length) {
        return { active: [], inactive: [] };
      }

      const outletIds = outlets.map((o) => o._id);

      // Get latest order dates for all outlets in one query
      const latestOrders = await this.orderOMSDocument.aggregate([
        {
          $match: {
            salesRep: { $in: salesRepIds },
            outlet: { $in: outletIds },
          },
        },
        {
          $sort: { orderDate: -1 },
        },
        {
          $group: {
            _id: '$outlet',
            latestOrderDate: { $first: '$orderDate' },
          },
        },
      ]);

      // Create lookup map for latest order dates
      const latestOrderMap = new Map(latestOrders.map((o) => [o._id.toString(), o.latestOrderDate]));

      // Process outlets and categorize them
      const processedOutlets = outlets.map((outlet) => {
        const latestOrderDate = latestOrderMap.get(outlet.outletId.toString()) || null;
        const isActive = latestOrderDate && latestOrderDate >= startDate && latestOrderDate <= endDate;

        return {
          ...outlet,
          latestOrderDate,
          plan: null, // Will be populated later
          isActive,
        };
      });

      // Separate active and inactive outlets
      const activeOutlets = processedOutlets.filter((outlet) => outlet.isActive).sort((a, b) => (b.latestOrderDate || 0) - (a.latestOrderDate || 0));
      const inactiveOutlets = processedOutlets.filter((outlet) => !outlet.isActive).sort((a, b) => (b.latestOrderDate || 0) - (a.latestOrderDate || 0));

      // Get plans data in parallel
      /*const plansPromise = this.outletJourneyPlanningService.getPlansBySaleRep({
        saleRep: { $in: salesRepIds },
        outletIds,
      });*/
      // Get plans and populate outlet data
      /*const plans = await plansPromise;
      if (plans?.length) {
        const plansMap = new Map(plans.map((plan) => [plan.outlet.toString(), plan]));

        // Helper function to populate plans
        const populatePlans = (outletList) => {
          return outletList.map((outlet) => ({
            ...outlet,
            plan: plansMap.get(outlet.outletId.toString()) || null,
          }));
        };

        return {
          active: populatePlans(activeOutlets),
          inactive: populatePlans(inactiveOutlets),
        };
      }*/

      return {
        active: activeOutlets,
        inactive: inactiveOutlets,
      };
    } catch (error) {
      console.error('Error in getActiveInactiveOutlets:', error);
      return { active: [], inactive: [] };
    }
  }

  /**
   *
   * @param orderIds
   * @param distinctField
   */
  async getOrdersByConditions(orderIds: string[], distinctField = 'orderId') {
    return await this.orderOMSDocument
      .find({
        orderId: { $in: orderIds },
      })
      .distinct(distinctField);
  }
  
  /**
   * Get order data filtered by sales rep and date range
   * @param salesRepId - Sales representative ID
   * @param startDate - Start date for filtering orders (inclusive)
   * @param endDate - End date for filtering orders (inclusive)
   * @returns Promise<OrderOMSDocument[]> - Array of filtered orders
   */
  async getOrderData(salesRepId: string, startDate: Date, endDate: Date) {
    try {
      // Build the filter query
      const filter: any = {};

      // Filter by sales rep if provided
      if (salesRepId) {
        filter.salesRep = new Types.ObjectId(salesRepId);
      }

      // Filter by date range if both dates are provided
      if (startDate && endDate) {
        filter.orderDate = {
          $gte: startDate,
          $lte: endDate
        };
      } else if (startDate) {
        // Only start date provided
        filter.orderDate = { $gte: startDate };
      } else if (endDate) {
        // Only end date provided
        filter.orderDate = { $lte: endDate };
      }

      // Execute the query with population of related data
      return await this.orderOMSDocument
        .find(filter)
        .populate('outlet', 'name ucc depotId')
        .populate('salesRep', 'username saleRepId email')
        .sort({ orderDate: -1 }) // Sort by most recent orders first
        .exec();

    } catch (error) {
      console.error('Error in getOrderData:', error);
      throw error;
    }
  }

  /**
   * Insert multiple orders in batch
   * @param orders - Array of orders to insert
   * @returns Result of bulk insert operation
   */
  async insertMany(orders: any[]) {
    try {
      if (!orders?.length) {
        return [];
      }

      return await this.orderOMSDocument.insertMany(orders, { ordered: false });
    } catch (error) {
      console.error('Error in insertMany:', error);
      throw error;
    }
  }
}
