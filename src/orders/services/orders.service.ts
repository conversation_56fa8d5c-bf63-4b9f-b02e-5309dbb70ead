import { Model, Types } from 'mongoose';
import { BaseService } from 'src/shared/services/base-service';
import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';

import { InjectModel } from '@nestjs/mongoose';
import { User } from 'src/users/schemas/user.schema';
import { I18nContext } from 'nestjs-i18n';
import { OmsPlaceOrderDto } from '../dtos/outlet-place-order.dto';
import { OutletsService } from 'src/outlets/services/outlets.service';
import { OmsService } from 'src/external/services/oms.service';
import { OrderOMS, OrderOMSDocument } from '../schemas/order-oms.schema';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { OmsRepReportsCalculatorsService } from 'src/oms/services/rep-reports-calculator.service';
import { OmsPlaceOrder } from 'src/offline-mode-v2/dtos/sync-offline-data.dto';
import { isEmptyObjectOrArray } from '../../utils';

@Injectable()
export class OrdersService extends BaseService<OrderOMS> {
  constructor(
    @InjectModel(OrderOMS.name)
    private readonly orderOMSDocument: Model<OrderOMSDocument>,
    private readonly outletService: OutletsService,
    @Inject(forwardRef(() => OutletJourneyPlanningService)) private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    @Inject(forwardRef(() => OmsService)) private readonly omsService: OmsService,
    private readonly omsRepReportsCalculatorsService: OmsRepReportsCalculatorsService,
  ) {
    super();
    this.model = orderOMSDocument;
  }

  async createOrderOms(body: OmsPlaceOrderDto, i18n: I18nContext, currentUser: User) {
    const outlet = await this.outletService.findById(body.outletId);
    if (!outlet) {
      throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
    }
    const items = [];
    body.items.forEach((item) => {
      items.push({
        sku: item.sku,
        quantity: item.quantity,
        metadata: {},
      });
    });
    const order = await this.omsService.createOrderOMS({
      outletId: outlet._id,
      depotExternalID: outlet.depotId,
      outletExternalID: outlet.ucc,
      email: currentUser.email || process.env.OMS_EMAIL,
      items,
      couponCodes: body.couponCode && body.couponCode.split(',').map((e) => e.trim()),
      deliveryInstruction: body.deliveryInstruction,
    });
    if (order) {
      await this.create({
        salesRep: currentUser._id,
        outlet: new Types.ObjectId(body.outletId),
        orderDate: new Date(),
        orderId: order?.id,
        displayId: order.display_id,
        depotId: outlet.depotId,
      });
      this.outletJourneyPlanningService
        .checkHasOrder(String(currentUser._id), String(body.outletId))
        .then(() => {
          this.omsRepReportsCalculatorsService.executeDSRStatistic({ salesRepId: currentUser.saleRepId }).then().catch();
        })
        .catch();
    }

    return order;
  }

  async createTemporaryOrderOms(body: OmsPlaceOrderDto, i18n: I18nContext, currentUser: User) {
    const outlet = await this.outletService.findById(body.outletId);
    if (!outlet) {
      throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
    }
    const items = [];
    body.items.forEach((item) => {
      items.push({
        sku: item.sku,
        quantity: item.quantity,
        metadata: {},
      });
    });

    return await this.omsService.createTemporaryOrderOMS({
      depotExternalID: outlet.depotId,
      outletExternalID: outlet.ucc,
      email: currentUser.email || process.env.OMS_EMAIL,
      items,
      couponCodes: body.couponCode && body.couponCode.split(',').map((e) => e.trim()),
      deliveryInstruction: body.deliveryInstruction,
    });
  }

  async createOrderOmsOffline(outletId: string, list: OmsPlaceOrder[], i18n: I18nContext, currentUser: User) {
    let success = true;
    let message = '';
    const itemsTemp = [];
    try {
      const outlet = await this.outletService.findById(outletId);
      if (!outlet) {
        throw new HttpException(await i18n.translate(`outlet.not_found`), HttpStatus.BAD_REQUEST);
      }
      for (const iterator of list) {
        try {
          const items = [];
          iterator.items.forEach((item) => {
            const metadata = {};
            items.push({
              sku: item.sku,
              quantity: item.quantity,
              metadata,
            });
          });
          const orderInfo = await this.omsService.createOrderOMS({
            outletId: outlet._id,
            depotExternalID: outlet.depotId,
            outletExternalID: outlet.ucc,
            email: currentUser.email || process.env.OMS_EMAIL,
            items,
            couponCodes: iterator.couponCode && iterator.couponCode.split(',').map((e) => e.trim()),
            deliveryInstruction: iterator.deliveryInstruction,
          });
          if (orderInfo) {
            await this.create({
              salesRep: currentUser._id,
              outlet: outlet._id,
              orderDate: new Date(),
              orderId: orderInfo?.id,
              depotId: outlet.depotId,
              displayId: orderInfo.display_id,
            });
            this.outletJourneyPlanningService.checkHasOrder(String(currentUser._id), String(outlet._id)).then().catch();
          }
          itemsTemp.push({
            offlineOrderId: iterator.offlineOrderId,
            success: true,
            message: '',
            orderInfo,
          });
        } catch (error) {
          itemsTemp.push({
            offlineOrderId: iterator.offlineOrderId,
            success: false,
            message: error.message,
          });
          success = false;
          message = error.message;
        }
      }
    } catch (error) {
      success = false;
      message = error.message;
    }
    return { key: 'orders', success, message, items: itemsTemp };
  }

  async getOutletHasNotOrder(salesRepId: any, outlets: any, startDate: Date, endDate: Date) {
    const outletHasOrder = await this.orderOMSDocument.find({
      salesRep: salesRepId,
      outlet: { $ne: outlets?.map((o) => o.outletId) },
      orderDate: { $gte: startDate, $lte: endDate },
    });
    if (!isEmptyObjectOrArray(outletHasOrder)) {
      return outlets.filter((o) => !outletHasOrder.some((order) => order.outlet._id.toString() === o.outletId.toString()));
    }
    return outlets;
  }

  async getInactiveOutlets(salesRepId: any, outlets: any, startDate: Date, endDate: Date) {
    const latestOrders = await this.orderOMSDocument.aggregate([
      {
        $match: {
          salesRep: salesRepId,
          outlet: { $in: outlets.map((o) => o.outletId) },
        },
      },
      {
        $sort: { orderDate: -1 },
      },
      {
        $group: {
          _id: '$outlet', // Group by outlet
          latestOrderDate: { $first: '$orderDate' },
        },
      },
    ]);

    const latestOrderMap = new Map(latestOrders.map((o) => [o._id.toString(), o.latestOrderDate]));

    const inactiveOutlet = outlets
      .map((o) => {
        const latestOrderDate = latestOrderMap.get(o.outletId.toString()) || null;
        return {
          ...o,
          latestOrderDate,
          plan: null,
        };
      })
      .filter((o) => !o.latestOrderDate || o.latestOrderDate < startDate || o.latestOrderDate > endDate)
      .sort((a, b) => (b.latestOrderDate || 0) - (a.latestOrderDate || 0));

    //Get plans data
    const plans = await this.outletJourneyPlanningService.getPlansBySaleRep({ saleRep: new Types.ObjectId(salesRepId), outletIds: outlets.map((o) => o.outletId) });
    if (!isEmptyObjectOrArray(plans)) {
      return inactiveOutlet?.map((o) => {
        const plan = plans.find((p) => p.outlet.toString() === o.outletId.toString()) || null;
        return {
          ...o,
          plan,
        };
      });
    }
    return inactiveOutlet;
  }

  /**
   *
   * @param orderIds
   * @param distinctField
   */
  async getOrdersByConditions(orderIds: string[], distinctField = 'orderId') {
    return await this.orderOMSDocument
      .find({
        orderId: { $in: orderIds },
      })
      .distinct(distinctField);
  }

  async insertMany(orders: any[]) {
    return await this.orderOMSDocument.insertMany(orders);
  }
}
