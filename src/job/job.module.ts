import { Module } from '@nestjs/common';

import { OrdersModule } from '../orders/orders.module';
import { ExternalModule } from '../external/external.module';
import { JobService } from './job.service';
import { MessageModule } from '../message/message.module';
import { OutletsModule } from '../outlets/outlets.module';
import { JourneyPlanningsModule } from '../journey-plannings/journey-plannings.module';
import { JobController } from './job.controller';
import { AuthModule } from '../auth/auth.module';
import { SaleRepModule } from '../sale-rep/sale-rep.module';
import { UsersModule } from '../users/users.module';
import { FilesModule } from '../files/files.module';
import { DistributorModule } from '../distributor/distributor.module';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from '../users/schemas/user.schema';
import { DsrTargetsModule } from '../dsr-targets/dsr-targets.module';
import { SettingsModule } from '../settings/settings.module';
import { SendGridService } from 'src/shared/mail/sendgrid';
import { OmsModule } from 'src/oms/oms.module';
import { DistributedLockService } from './distributed-lock.service';
import { ThirdPartiesModule } from '../third-parties/third-parties.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: User.name,
        schema: UserSchema,
      },
    ]),
    MessageModule,
    OutletsModule,
    JourneyPlanningsModule,
    AuthModule,
    SaleRepModule,
    UsersModule,
    ExternalModule,
    OrdersModule,
    FilesModule,
    DistributorModule,
    DsrTargetsModule,
    SettingsModule,
    OmsModule,
    ThirdPartiesModule,
  ],
  providers: [JobService, SendGridService, DistributedLockService],
  controllers: [JobController],
})
export class JobModule {}
