import { Injectable } from '@nestjs/common';
import { CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { CycleNameType } from 'src/journey-plannings/enums/cycle-type.enum';
import { JourneyPlanCycleService } from 'src/journey-plannings/services/journey-plan-cycle.service';
import { UsersService } from 'src/users/services/users.service';
import { BaseJourneyPlanService, DistributorService } from '../distributor/services';
import { FilesService } from '../files/services';
import { JourneyPlanWeekService } from '../journey-plannings/services/journey-plan-week.service';
import { UserAdminsService } from '../users/services/user-admins.service';
import { LogsService } from 'src/settings/logs.service';
import { OmsService } from 'src/external/services/oms.service';
import { UserActionsService } from 'src/users/services/user-actions.service';
import { OmsRepReportsCalculatorsService } from 'src/oms/services/rep-reports-calculator.service';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { DsrTargetService } from '../dsr-targets/services';
import { User, UserDocument } from '../users/schemas/user.schema';
import { InjectModel } from '@nestjs/mongoose';
import { printLog, sleep } from '../utils';
import { DistributedLockService } from './distributed-lock.service';
import { SolaceService } from '../third-parties/services/solace.service';

@Injectable()
export class JobService {
  constructor(
    private readonly _journeyPlanWeekService: JourneyPlanWeekService,
    private readonly _JourneyPlanCycleService: JourneyPlanCycleService,
    private readonly _scheduleRegistry: SchedulerRegistry,
    private readonly _usersService: UsersService,
    private readonly _userAdminsService: UserAdminsService,
    private readonly omsService: OmsService,
    private readonly _filesService: FilesService,
    private readonly _baseJourneyPlanService: BaseJourneyPlanService,
    private readonly _logsService: LogsService,
    private readonly userActionsService: UserActionsService,
    private readonly _solaceService: SolaceService,
    private readonly omsRepReportsCalculatorsService: OmsRepReportsCalculatorsService,
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly distributorService: DistributorService,
    private readonly _dsrTargetService: DsrTargetService,
    @InjectModel(User.name) private readonly userModel: Model<UserDocument>,
    private readonly distributedLockService: DistributedLockService,
  ) {}

  onModuleInit() {
    if (process.env.NODE_ENV === 'local') return;
    printLog('[onModuleInit JOB]', process.env.NODE_ENV);
    const deleteExpiredFilesJob = this.deleteExpiredFilesJob();
    const cloneJourneyPlans = this.cloneJourneyPlansJob();
    const createDefaultUsersJob = this.createDefaultUsersJob();
    const createCycleJob = this.createCycleJob();
    const cacheDataOMS = this.cacheDataOMS();
    const clearOldDataJob = this.clearOldDataJob();
    const executeDSRStatistic = this.executeDSRStatistic();
    const executeDSRSalesRepTarget = this.executeDSRSalesRepTarget();

    this._scheduleRegistry.addCronJob('everydayAtMidnight_deleteExpiredFiles', deleteExpiredFilesJob);
    this._scheduleRegistry.addCronJob('atLastDayOfCycle_cloneJourneyPlans', cloneJourneyPlans);
    this._scheduleRegistry.addCronJob('At_0:00_1/12_createCycleJob', createCycleJob);
    this._scheduleRegistry.addCronJob('cacheDataOMS', cacheDataOMS);
    this._scheduleRegistry.addCronJob('clearOldDataJob', clearOldDataJob);
    this._scheduleRegistry.addCronJob('executeDSRStatistic', executeDSRStatistic);
    this._scheduleRegistry.addCronJob('executeDSRSalesRepTarget', executeDSRSalesRepTarget);

    createDefaultUsersJob.start();
    deleteExpiredFilesJob.start();
    cloneJourneyPlans.start();
    createCycleJob.start();
    cacheDataOMS.start();
    clearOldDataJob.start();
    executeDSRStatistic.start();
    executeDSRSalesRepTarget.start();
  }

  private deleteExpiredFilesJob() {
    return new CronJob({
      cronTime: process.env.DELETE_EXPIRED_FILES_EXPRESSION || '0 0 * * *',
      timeZone: process?.env?.TZ ?? 'Asia/Kuala_Lumpur',
      onTick: async () => {
        const keyLock = `locks:${this.deleteExpiredFilesJob.name}`;
        await this.distributedLockService.runJobWithLock(keyLock, async () => {
          await this._filesService.deleteExpiredFiles();
        });
      },
    });
  }

  private cloneJourneyPlansJob() {
    return new CronJob({
      cronTime: process.env.CLONE_JOURNEY_PLAN_EXPRESSION || '0 0 0 * * *',
      timeZone: process?.env?.TZ ?? 'Asia/Kuala_Lumpur',
      onTick: async () => {
        const keyLock = `locks:${this.cloneJourneyPlansJob.name}`;
        await this.distributedLockService.runJobWithLock(keyLock, async () => {
          if (process.env.NODE_ENV !== 'local') {
            const previousDay = moment().subtract(1, 'd').endOf('d').toISOString();
            this._journeyPlanWeekService.isLastDayOfCurrentCycle(previousDay).then((isLastDayOfCycle) => {
              if (isLastDayOfCycle) {
                this._journeyPlanWeekService.getCycleByGivenDay().then((currentCycle) => {
                  this._baseJourneyPlanService.cloneJourneyPlan(currentCycle._id.toString(), false).then();
                });
              }
            });
          }
        });
      },
    });
  }

  private createDefaultUsersJob() {
    return new CronJob({
      cronTime: CronExpression.EVERY_MINUTE,
      timeZone: process?.env?.TZ ?? 'Asia/Kuala_Lumpur',
      onTick: async () => {
        const keyLock = `locks:${this.createDefaultUsersJob.name}`;
        await this.distributedLockService.runJobWithLock(keyLock, async () => {
          await this._usersService.createDefaultUsers();
          await this._userAdminsService.createDefaultUsers();
          await this._userAdminsService.createDefaultExternalUsers();
        });
      },
    });
  }

  private createCycleJob() {
    return new CronJob({
      cronTime: process.env.CREATE_CYCLE_TIME || '0 0 1 11 *',
      timeZone: process?.env?.TZ ?? 'Asia/Kuala_Lumpur',
      onTick: async () => {
        const keyLock = `locks:${this.createCycleJob.name}`;
        await this.distributedLockService.runJobWithLock(keyLock, async () => {
          const lastWeekInDb = await this._journeyPlanWeekService.getLastWeek();
          let date = null;
          let cycleCounter = 1;
          if (lastWeekInDb) {
            date = moment(lastWeekInDb.startTime).tz(process.env.TZ).add(7, 'days').startOf('day');
            cycleCounter = parseInt(lastWeekInDb?.cycle?.cycleName?.split(' ')[1]);
            cycleCounter++;
          } else {
            date = moment().tz(process.env.TZ).startOf('year').isoWeekday(8);
          }
          const year = moment().year() + 1;
          while (date.year() <= year) {
            if (cycleCounter > 13) {
              cycleCounter = 1;
            }
            const cycle = await this._JourneyPlanCycleService.findOrCreate(CycleNameType[`CYCLE_${cycleCounter}`], date.year());
            for (let index = 0; index < 4; index++) {
              const week = await this._journeyPlanWeekService.findOne({
                cycle: new Types.ObjectId(cycle._id),
                startTime: date.startOf('day').toDate(),
              });
              if (!week) {
                await this._journeyPlanWeekService.create({
                  cycle: new Types.ObjectId(cycle._id),
                  startTime: date.startOf('day').toDate(),
                  weekName: `Week ${index + 1}`,
                });
              }
              date.add(7, 'days').startOf('day');
            }
            cycleCounter++;
          }
        });
      },
    });
  }

  private cacheDataOMS() {
    return new CronJob({
      cronTime: process.env.CRON_CACHE_OMS || '0 15 5 * * *',
      timeZone: process?.env?.TZ ?? 'Asia/Kuala_Lumpur',
      onTick: async () => {
        const keyLock = `locks:${this.cacheDataOMS.name}`;
        await this.distributedLockService.runJobWithLock(keyLock, async () => {
          await this.cacheDataOMSFunc();
        });
      },
    });
  }
  private async cacheDataOMSFunc() {
    try {
      await this.omsService.loginOMS();
      const listTodayJp = await this.outletJourneyPlanningService.getAllTodayPlans();
      const listOutlet = listTodayJp.map((jp) => jp.outlet);
      while (listOutlet.length > 0) {
        const temp = listOutlet.splice(0, 3);
        await Promise.all(
          temp.map(async (outlet) => {
            let region = outlet.region;
            if (!outlet.region) {
              const distributor = await this.distributorService.findOne({
                distributorId: outlet.distributorId,
              });
              region = distributor.region;
            }
            const outletExternalID = outlet.ucc;
            const depotExternalID = outlet.depotId;
            const distributorExternalID = outlet.distributorId;
            const outletId = outlet._id;
            const isOmsConnected = await this.omsService.isOmsOutletConnected(outletId, outletExternalID, depotExternalID);
            if (isOmsConnected) {
              const outletInfo = {
                outletId,
                distributorExternalID,
                depotExternalID,
                outletExternalID,
                region: region,
                channel: outlet.channel,
                subChannel: outlet.subChannel,
              };
              await this.omsService.cacheDataByOutlet(outletInfo, false);
            }
          }),
        );
        await sleep(1500);
      }
    } catch (error) {}
  }

  private executeDSRStatistic() {
    return new CronJob({
      cronTime: process.env.CRON_STATISTIC || '0 */10 * * * *',
      timeZone: process?.env?.TZ ?? 'Asia/Kuala_Lumpur',
      onTick: async () => {
        const keyLock = `locks:${this.executeDSRStatistic.name}`;
        await this.distributedLockService.runJobWithLock(keyLock, async () => {
          this.omsRepReportsCalculatorsService.executeDSRStatistic({}).then().catch();
        });
      },
    });
  }

  private clearOldDataJob() {
    return new CronJob({
      cronTime: '0 0 2 * * *',
      timeZone: process?.env?.TZ ?? 'Asia/Kuala_Lumpur',
      onTick: async () => {
        const keyLock = `locks:${this.clearOldDataJob.name}`;
        await this.distributedLockService.runJobWithLock(keyLock, async () => {
          await Promise.all([this._logsService.clearOldData(), this.userActionsService.clearOldData()]);
          await this._solaceService.clearOldData();
        });
      },
    });
  }

  private executeDSRSalesRepTarget() {
    return new CronJob({
      cronTime: '0 1 1 * *',
      timeZone: process?.env?.TZ ?? 'Asia/Jakarta',
      onTick: async () => {
        const keyLock = `locks:${this.executeDSRSalesRepTarget.name}`;
        await this.distributedLockService.runJobWithLock(keyLock, async () => {
          const saleReps = await this.userModel
            .aggregate()
            .match({
              saleRepId: { $ne: null },
            })
            .project({
              _id: 1,
            })
            .exec();
          await this._dsrTargetService.createTargetForSaleRep(saleReps?.map((s) => s._id));
        });
      },
    });
  }
}
