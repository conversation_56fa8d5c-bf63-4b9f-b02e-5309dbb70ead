import { Injectable } from '@nestjs/common';
import { printLog, sleep } from '../utils';
import Redis from 'ioredis';

@Injectable()
export class DistributedLockService {
  private redis: Redis;

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: Number(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
      tls: process.env.REDIS_TLS === 'true' ? { host: process.env.REDIS_HOST } : undefined,
    });
  }

  /**
   * Try to acquire a lock atomically using Redis SET NX PX.
   * @param resource Redis key for the lock
   * @param ttl Time to live in ms
   * @returns true if lock acquired, false otherwise
   */
  async acquireLock(resource: string, ttl = 30000): Promise<boolean> {
    await sleep(Math.floor(Math.random() * 500));
    const result = await this.redis.set(this.getResourceGeneralKey(resource), 'locked', 'PX', ttl, 'NX');
    printLog('=== [LOCK ATTEMPT] ', this.getResourceGeneralKey(resource), result);
    return result === 'OK';
  }

  /**
   * Release a previously acquired lock.
   * @param resource Redis key for the lock
   */
  async releaseLock(resource: string) {
    await sleep(2000);
    await this.redis.del(this.getResourceGeneralKey(resource));
  }

  getResourceGeneralKey(resource: string) {
    return `JOB-LOCK-${resource}-${process.env.BASE_URL?.replace(/https?:\/\//, '').replace(/\//g, '')}`;
  }

  /**
   * Run a job with distributed lock
   * @param keyLock
   * @param jobFn
   */
  async runJobWithLock(keyLock: string, jobFn: () => Promise<void>) {
    const locked = await this.acquireLock(keyLock);
    if (!locked) return;
    try {
      printLog('=== [START] ', keyLock);
      await jobFn();
    } catch (error) {
      await this.releaseLock(keyLock);
      printLog('=== [ERROR] ', { keyLock, error });
    } finally {
      await this.releaseLock(keyLock);
      printLog('=== [END] ', keyLock);
    }
  }
}
