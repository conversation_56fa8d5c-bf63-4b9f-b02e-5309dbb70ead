import { Module } from '@nestjs/common';

import { ReportController } from './report.controller';
import { AuthModule } from '../auth/auth.module';
import { SaleRepModule } from '../sale-rep/sale-rep.module';
import { UsersModule } from '../users/users.module';
import { JourneyPlanningsModule } from '../journey-plannings/journey-plannings.module';
import { DistributorModule } from '../distributor/distributor.module';
import { OutletsModule } from '../outlets/outlets.module';
import { DsrTargetsModule } from '../dsr-targets/dsr-targets.module';
import { FilesModule } from '../files/files.module';
import { ReportService } from './services/report.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DistributorUserRelation, DistributorUserRelationSchema } from 'src/distributor/schemas';
import { OutletJourneyPlanning, OutletJourneyPlanningSchema } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { User, UserSchema } from 'src/users/schemas/user.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: User.name,
        schema: UserSchema,
      },
      {
        name: DistributorUserRelation.name,
        schema: DistributorUserRelationSchema,
      },
      {
        name: OutletJourneyPlanning.name,
        schema: OutletJourneyPlanningSchema,
      },
    ]),

    JourneyPlanningsModule,
    SaleRepModule,
    AuthModule,
    UsersModule,
    DistributorModule,
    OutletsModule,
    DsrTargetsModule,
    FilesModule,
    MongooseModule.forFeature([{ name: OutletJourneyPlanning.name, schema: OutletJourneyPlanningSchema }]),
  ],
  controllers: [ReportController],
  providers: [ReportService],
  exports: [ReportService],
})
export class ReportModule {}
