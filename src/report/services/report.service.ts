import { GetMissedOutletReportDto, GetSuccessOutletReportDto } from '../dtos';
import { DistributorService, DistributorUserRelationService } from 'src/distributor/services';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { SaleRepExecutionVisibilityService } from 'src/sale-rep/services';
import { FilesService } from 'src/files/services';
import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model, Types } from 'mongoose';
import { SaleRepOutletRelationService } from 'src/outlets/services/sale-rep-outlet-relation.service';
import { User, UserDocument } from 'src/users/schemas/user.schema';
import { FetchBreakdownDto } from '../dtos/fetch-breakdown.dto';
import { ConstantRoles } from 'src/utils/constants/role';
import { Distributor, DistributorUserRelation, DistributorUserRelationDocument } from 'src/distributor/schemas';
import { I18nContext } from 'nestjs-i18n';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import * as moment from 'moment-timezone';
import { FetchCPSRDetailDto } from '../dtos/fetch-cpsr-detail.dto';
import { FetchNNDDto } from '../dtos/fetch-nnd.dto';
import { FetchForwardStockDto } from '../dtos/fetch-forward-stock.dto';

@Injectable()
export class ReportService {
  constructor(
    @InjectModel(User.name)
    private readonly userModel: Model<UserDocument>,
    @InjectModel(DistributorUserRelation.name)
    private readonly distributorUserRelationModel: Model<DistributorUserRelationDocument>,
    @InjectModel(OutletJourneyPlanning.name)
    private readonly outletJourneyPlanningModel: Model<OutletJourneyPlanningDocument>,
    private readonly saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly _distributorService: DistributorService,
    private readonly _saleRepExecutionVisibilityService: SaleRepExecutionVisibilityService,
    private readonly _outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _filesService: FilesService,
  ) {}

  async getSuccessVisitsOutletMY(getOutletReportDto: GetSuccessOutletReportDto, currentUser: any) {
    const { skip, limit, sort, search, dayInMonth, distributorIds } = getOutletReportDto;
    let result = [{ totalRecords: [{ total: 0 }], data: null }];
    const existedDistributorObjectIds = [];
    if (currentUser.roles.some((r) => r === ConstantRoles.SUPER_USER || r === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)) {
      await Promise.all(
        distributorIds.map(async (distributorId) => {
          const distributor = await this._distributorService.findOne({ distributorId: distributorId });
          if (distributor) {
            existedDistributorObjectIds.push(distributor._id);
          }
        }),
      );
    }

    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      for (const distributorId of distributorIds) {
        const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
        if (distributor) {
          existedDistributorObjectIds.push(distributor.distributor._id);
        }
      }
    }
    if (!existedDistributorObjectIds.length) {
      throw new BadRequestException('distributor.not_found');
    }
    result = await this._outletJourneyPlanningService.findVisitedByOutletAndDistributorAndMonthMY({
      distributorIds: existedDistributorObjectIds,
      skip,
      limit,
      sort,
      search,
      dayInMonth,
    });
    result[0].data = await Promise.all(
      result[0]?.data?.map(async (item) => {
        const numOfBrands = item.availability.length;
        const availableOfBrands = item.availability.filter((avai) => avai.hasQt || avai.hasPt || avai.hasCan || avai.hasKeg).length;
        item.availability = `${availableOfBrands}/${numOfBrands}`;
        item.visibility = item.visibility.length;
        item.availableOfBrands = availableOfBrands;
        return item;
      }),
    );

    const availableOfBrandsDesc =
      sort?.availability === -1 || sort?.availability?.toString().toLowerCase() === 'desc' || sort?.availability?.toString().toLowerCase() === 'descending';
    const availableOfBrandsAsc = sort?.availability === 1 || sort?.availability?.toString().toLowerCase() === 'asc' || sort?.availability?.toString().toLowerCase() === 'ascending';
    if (availableOfBrandsDesc || availableOfBrandsAsc) {
      result[0]?.data.sort((item1, item2) => (availableOfBrandsAsc ? item1.availableOfBrands - item2.availableOfBrands : item2.availableOfBrands - item1.availableOfBrands));
    }

    const visibilityDesc = sort?.visibility === -1 || sort?.visibility?.toString().toLowerCase() === 'desc' || sort?.visibility?.toString().toLowerCase() === 'descending';
    const visibilityAsc = sort?.visibility === 1 || sort?.visibility?.toString().toLowerCase() === 'asc' || sort?.visibility?.toString().toLowerCase() === 'ascending';
    if (visibilityDesc || visibilityAsc) {
      result[0]?.data.sort((item1, item2) => (visibilityAsc ? item1.visibility - item2.visibility : item2.visibility - item1.visibility));
    }
    const [{ totalRecords, data }] = result;
    return [data, totalRecords?.[0]?.total ?? 0];
  }

  async getSuccessVisitsOutlet(getOutletReportDto: GetSuccessOutletReportDto, currentUser: any) {
    const { skip, limit, sort, search, dayInMonth, distributorId } = getOutletReportDto;
    let result = [{ totalRecords: [{ total: 0 }], data: null }];
    let existedDistributorObjectId = distributorId;
    if (currentUser.roles.some((r) => r === ConstantRoles.SUPER_USER || r === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)) {
      const existedDistributor = await this._distributorService.findOne({ distributorId: distributorId });
      if (!existedDistributor) {
        throw new BadRequestException('distributor.not_found');
      }
      existedDistributorObjectId = existedDistributor._id;
    }

    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (!distributor) {
        throw new BadRequestException('report.dsr.invalid_request');
      }
      existedDistributorObjectId = distributor.distributor._id;
    }
    result = await this._outletJourneyPlanningService.findVisitedByOutletAndDistributorAndMonth({
      distributorId: existedDistributorObjectId,
      skip,
      limit,
      sort,
      search,
      dayInMonth,
    });
    result[0].data = await Promise.all(
      result[0]?.data?.map(async (item) => {
        const numOfBrands = item.availability.length;
        const availableOfBrands = item.availability.filter((avai) => avai.hasQt || avai.hasPt || avai.hasCan || avai.hasKeg).length;
        item.availability = `${availableOfBrands}/${numOfBrands}`;
        item.visibility = item.visibility.length;
        item.availableOfBrands = availableOfBrands;
        return item;
      }),
    );

    const availableOfBrandsDesc =
      sort?.availability === -1 || sort?.availability?.toString().toLowerCase() === 'desc' || sort?.availability?.toString().toLowerCase() === 'descending';
    const availableOfBrandsAsc = sort?.availability === 1 || sort?.availability?.toString().toLowerCase() === 'asc' || sort?.availability?.toString().toLowerCase() === 'ascending';
    if (availableOfBrandsDesc || availableOfBrandsAsc) {
      result[0]?.data.sort((item1, item2) => (availableOfBrandsAsc ? item1.availableOfBrands - item2.availableOfBrands : item2.availableOfBrands - item1.availableOfBrands));
    }

    const visibilityDesc = sort?.visibility === -1 || sort?.visibility?.toString().toLowerCase() === 'desc' || sort?.visibility?.toString().toLowerCase() === 'descending';
    const visibilityAsc = sort?.visibility === 1 || sort?.visibility?.toString().toLowerCase() === 'asc' || sort?.visibility?.toString().toLowerCase() === 'ascending';
    if (visibilityDesc || visibilityAsc) {
      result[0]?.data.sort((item1, item2) => (visibilityAsc ? item1.visibility - item2.visibility : item2.visibility - item1.visibility));
    }
    const [{ totalRecords, data }] = result;
    return [data, totalRecords?.[0]?.total ?? 0];
  }

  async exportSuccessVisitsOutletMY(getOutletReportDto: GetSuccessOutletReportDto, currentUser: any, i18n: I18nContext) {
    const { skip, limit, sort, search, dayInMonth, distributorIds } = getOutletReportDto;
    let result = [{ totalRecords: [{ total: 0 }], data: null }];
    const existedDistributorObjectIds = [];

    if (currentUser.roles.some((r) => r === ConstantRoles.SUPER_USER || r === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)) {
      await Promise.all(
        distributorIds.map(async (distributorId) => {
          const distributor = await this._distributorService.findOne({ distributorId: distributorId });
          if (distributor) {
            existedDistributorObjectIds.push(distributor._id);
          }
        }),
      );
    }

    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      for (const distributorId of distributorIds) {
        const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
        if (distributor) {
          existedDistributorObjectIds.push(distributor.distributor._id);
        }
      }
    }
    if (!existedDistributorObjectIds.length) {
      throw new BadRequestException('distributor.not_found');
    }
    result = await this._outletJourneyPlanningService.findVisitedByOutletAndDistributorAndMonthMY({
      distributorIds: existedDistributorObjectIds,
      skip,
      limit,
      sort,
      search,
      dayInMonth,
    });
    result[0].data = await Promise.all(
      result[0]?.data?.map(async (item) => {
        item.salesRepName = item.saleRep.username;
        const numOfBrands = item.availability.length;
        const availableOfBrands = item.availability.filter((avai) => avai.hasQt || avai.hasPt || avai.hasCan || avai.hasKeg).length;
        item.availability = `${availableOfBrands}/${numOfBrands}`;
        item.visibility = item.visibility.length;
        item.availableOfBrands = availableOfBrands;
        return item;
      }),
    );

    const [{ data }] = result;
    const xlsxData = data?.map((item) => {
      return {
        [i18n.translate(`importExport.plannedDate`)]: moment(item.plannedDate).format('DD/MM/YYYY - hh:mm A'),
        [i18n.translate(`importExport.visitedDate`)]: moment(item.visitedDate).format('MMM DD YYYY'),
        [i18n.translate(`importExport.startTime`)]: moment(item.visitedDate).format('hh:mm A'),
        [i18n.translate(`importExport.endTime`)]: moment(item.endDate).format('hh:mm A'),
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.outletID`)]: item.outletUcc,
        [i18n.translate(`importExport.outletName`)]: item.outletName,
        [i18n.translate(`importExport.availability`)]: item.availability,
        [i18n.translate(`importExport.visibility`)]: item.visibility,
        [i18n.translate(`importExport.salesRepId`)]: item.saleRepId,
      };
    });
    const fileName = `SuccessVisits_Data_${moment(dayInMonth).format('MMM YYYY')}`;
    const file = await this._filesService.exportXLSXFile(fileName, xlsxData, 'SuccessVisits_Data', currentUser, { wch: 20 });
    return file;
  }

  async exportSuccessVisitsOutletID(getOutletReportDto: GetSuccessOutletReportDto, currentUser: any, i18n: I18nContext) {
    const { skip, limit, sort, search, dayInMonth, distributorId } = getOutletReportDto;
    let result = [{ totalRecords: [{ total: 0 }], data: null }];
    let existedDistributorObjectId = distributorId;
    let existedDistributor: Distributor;

    if (currentUser.roles.some((r) => r === ConstantRoles.SUPER_USER || r === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)) {
      existedDistributor = await this._distributorService.findOne({ distributorId: distributorId });
      if (!existedDistributor) {
        throw new BadRequestException('distributor.not_found');
      }
      existedDistributorObjectId = existedDistributor._id;
    }

    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (!distributor) {
        throw new BadRequestException('report.dsr.invalid_request');
      }
      existedDistributorObjectId = distributor.distributor._id;
      existedDistributor = distributor.distributor;
    }
    result = await this._outletJourneyPlanningService.findVisitedByOutletAndDistributorAndMonth({
      distributorId: existedDistributorObjectId,
      skip,
      limit,
      sort,
      search,
      dayInMonth,
    });
    result[0].data = await Promise.all(
      result[0]?.data?.map(async (item) => {
        item.salesRepName = item.saleRep.username;
        item.availability = item.availability
          .filter((e) => e.hasQt || e.hasPt || e.hasCan || e.hasKeg)
          .map((e) => {
            let text = '';
            const temp = [];
            if (e.hasQt) temp.push(`${e.name} Qt`);
            if (e.hasPt) temp.push(`${e.name} Pt`);
            if (e.hasCan) temp.push(`${e.name} Can`);
            if (e.hasKeg) temp.push(`${e.name} Keg`);
            if (temp.length) {
              text += `${temp.join(', ')}`;
            }
            return text;
          })
          .join(', ');

        const visibilityById = await this._saleRepExecutionVisibilityService.getVisibilityById(item.visibilitiesId);
        item.visibility = visibilityById.images
          .map((e) => {
            return `${process.env.BASE_URL}/${e.path}`;
          })
          .join('\n');
        return item;
      }),
    );

    const [{ data }] = result;
    const xlsxData = data?.map((item) => {
      return {
        [i18n.translate(`importExport.plannedDate`)]: moment(item.plannedDate).format('DD/MM/YYYY - hh:mm A'),
        [i18n.translate(`importExport.visitedDate`)]: moment(item.visitedDate).format('MMM DD YYYY'),
        [i18n.translate(`importExport.startTime`)]: moment(item.visitedDate).format('hh:mm A'),
        [i18n.translate(`importExport.endTime`)]: moment(item.endDate).format('hh:mm A'),
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.outletID`)]: item.outletUcc,
        [i18n.translate(`importExport.outletName`)]: item.outletName,
        [i18n.translate(`importExport.availability`)]: item.availability,
        [i18n.translate(`importExport.visibility`)]: item.visibility,
        [i18n.translate(`importExport.salesRepId`)]: item.saleRepId,
      };
    });
    const fileName = `SuccessVisits_Data`;
    const file = await this._filesService.exportXLSXFile(fileName, xlsxData, 'SuccessVisits_Data', currentUser, { wch: 20 });
    return file;
  }

  async exportSuccessVisitsOutlet(getOutletReportDto: GetSuccessOutletReportDto, currentUser: any, i18n: I18nContext) {
    const { skip, limit, sort, search, dayInMonth, distributorId } = getOutletReportDto;
    let result = [{ totalRecords: [{ total: 0 }], data: null }];
    let existedDistributorObjectId = distributorId;
    let existedDistributor: Distributor;

    if (currentUser.roles.some((r) => r === ConstantRoles.SUPER_USER || r === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)) {
      existedDistributor = await this._distributorService.findOne({ distributorId: distributorId });
      if (!existedDistributor) {
        throw new BadRequestException('distributor.not_found');
      }
      existedDistributorObjectId = existedDistributor._id;
    }

    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (!distributor) {
        throw new BadRequestException('report.dsr.invalid_request');
      }
      existedDistributorObjectId = distributor.distributor._id;
      existedDistributor = distributor.distributor;
    }
    result = await this._outletJourneyPlanningService.findVisitedByOutletAndDistributorAndMonth({
      distributorId: existedDistributorObjectId,
      skip,
      limit,
      sort,
      search,
      dayInMonth,
    });
    result[0].data = await Promise.all(
      result[0]?.data?.map(async (item) => {
        item.location = item.ojp.location;
        item.salesRepName = item.saleRep.username;
        item.availability = item.availability
          .filter((e) => e.hasQt || e.hasPt || e.hasCan || e.hasKeg)
          .map((e) => {
            let text = '';
            const temp = [];
            if (e.hasQt) temp.push(`${e.name} Qt`);
            if (e.hasPt) temp.push(`${e.name} Pt`);
            if (e.hasCan) temp.push(`${e.name} Can`);
            if (e.hasKeg) temp.push(`${e.name} Keg`);
            if (temp.length) {
              text += `${temp.join(', ')}`;
            }
            return text;
          })
          .join(', ');

        const visibilityById = await this._saleRepExecutionVisibilityService.getVisibilityById(item.visibilitiesId);
        item.visibility = visibilityById.images
          .map((e) => {
            return `${process.env.BASE_URL}/${e.path}`;
          })
          .join('\n');
        return item;
      }),
    );

    const [{ data }] = result;
    const xlsxData = data?.map((item) => {
      return {
        [i18n.translate(`importExport.plannedDate`)]: moment(item.plannedDate).format('DD/MM/YYYY - hh:mm A'),
        [i18n.translate(`importExport.visitedDate`)]: moment(item.visitedDate).format('MMM DD YYYY'),
        [i18n.translate(`importExport.startTime`)]: moment(item.visitedDate).format('hh:mm A'),
        [i18n.translate(`importExport.endTime`)]: moment(item.endDate).format('hh:mm A'),
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.outletID`)]: item.outletUcc,
        [i18n.translate(`importExport.outletName`)]: item.outletName,
        [i18n.translate(`importExport.availability`)]: item.availability,
        [i18n.translate(`importExport.visibility`)]: item.visibility,
        [i18n.translate(`importExport.location`)]: item.location ? `${item.location.lat}, ${item.location.long}` : '',
        [i18n.translate(`importExport.salesRepId`)]: item.saleRepId,
        [i18n.translate(`importExport.salesRepName`)]: item.salesRepName,
      };
    });
    const fileName = `SuccessVisits_Data`;
    const file = await this._filesService.exportXLSXFile(fileName, xlsxData, 'SuccessVisits_Data', currentUser, { wch: 20 });
    return file;
  }

  async getMissedVisitsOutletMY(getOutletReportDto: GetMissedOutletReportDto, currentUser: any, i18n: I18nContext) {
    const { skip, limit, sort, search, dayInMonth, distributorIds } = getOutletReportDto;
    const existedDistributorObjectIds = [];
    if (currentUser.roles.some((r) => r === ConstantRoles.SUPER_USER || r === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)) {
      await Promise.all(
        distributorIds.map(async (distributorId) => {
          const distributor = await this._distributorService.findOne({ distributorId: distributorId });
          if (distributor) {
            existedDistributorObjectIds.push(distributor._id);
          }
        }),
      );
    }
    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      for (const distributorId of distributorIds) {
        const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
        if (distributor) {
          existedDistributorObjectIds.push(distributor.distributor._id);
        }
      }
    }
    if (!existedDistributorObjectIds.length) {
      throw new BadRequestException('distributor.not_found');
    }
    const { data, totalRecords } = await this._outletJourneyPlanningService.findMissedVisitByOutletAndDistributorAndMonthMY(
      {
        distributorIds: existedDistributorObjectIds,
        skip,
        limit,
        sort,
        search,
        dayInMonth,
      },
      i18n,
    );
    return [data.map((item) => ({ ...item, missedReason: item.missedReason?._id?.toString() })), totalRecords];
  }

  async getMissedVisitsOutlet(getOutletReportDto: GetMissedOutletReportDto, currentUser: any, i18n: I18nContext) {
    const { skip, limit, sort, search, dayInMonth, distributorId } = getOutletReportDto;
    let existedDistributorObjectId = distributorId;
    if (currentUser.roles.some((r) => r === ConstantRoles.SUPER_USER || r === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)) {
      const existedDistributor = await this._distributorService.findOne({ distributorId: distributorId });
      if (!existedDistributor) {
        throw new BadRequestException('distributor.not_found');
      }
      existedDistributorObjectId = existedDistributor._id;
    }
    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (!distributor) {
        throw new BadRequestException('report.dsr.invalid_request');
      }
      existedDistributorObjectId = distributor.distributor._id;
    }
    const { data, totalRecords } = await this._outletJourneyPlanningService.findMissedVisitByOutletAndDistributorAndMonth(
      {
        distributorId: existedDistributorObjectId,
        skip,
        limit,
        sort,
        search,
        dayInMonth,
      },
      i18n,
    );
    return [data.map((item) => ({ ...item, missedReason: item.missedReason?._id?.toString() })), totalRecords];
  }

  async exportMissedVisitsOutletMY(getOutletReportDto: GetMissedOutletReportDto, currentUser: any, i18n: I18nContext) {
    const { skip, limit, sort, search, dayInMonth, distributorIds } = getOutletReportDto;
    const existedDistributorObjectIds = [];
    if (currentUser.roles.some((r) => r === ConstantRoles.SUPER_USER || r === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)) {
      await Promise.all(
        distributorIds.map(async (distributorId) => {
          const distributor = await this._distributorService.findOne({ distributorId: distributorId });
          if (distributor) {
            existedDistributorObjectIds.push(distributor._id);
          }
        }),
      );
    }
    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      for (const distributorId of distributorIds) {
        const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
        if (distributor) {
          existedDistributorObjectIds.push(distributor.distributor._id);
        }
      }
    }
    if (!existedDistributorObjectIds.length) {
      throw new BadRequestException('distributor.not_found');
    }
    const { data } = await this._outletJourneyPlanningService.findMissedVisitByOutletAndDistributorAndMonthMY(
      {
        distributorIds: existedDistributorObjectIds,
        skip,
        limit,
        sort,
        search,
        dayInMonth,
      },
      i18n,
    );
    const xlsxData = data?.map((item) => {
      return {
        [i18n.translate(`importExport.plannedDate`)]: moment(item.plannedDate).format('DD/MM/YYYY - hh:mm A'),
        [i18n.translate(`importExport.visitedDate`)]: moment(item.visitedDate).format('DD/MM/YYYY - hh:mm A'),
        [i18n.translate(`importExport.missedReason`)]: item.cancellationReason,
        [i18n.translate(`importExport.uncontrollable`)]: item.missedReason && !item.controllable ? i18n.t('common.yes') : i18n.t('common.no'),
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.outletID`)]: item.outletUcc,
        [i18n.translate(`importExport.outletName`)]: item.outletName,
        [i18n.translate(`importExport.salesRepId`)]: item.saleRepId,
      };
    });
    const fileName = `MisedVisits_Data`;
    const file = await this._filesService.exportXLSXFile(fileName, xlsxData, 'MissedVisits_Data', currentUser, { wch: 20 });
    return file;
  }

  async exportMissedVisitsOutlet(getOutletReportDto: GetMissedOutletReportDto, currentUser: any, i18n: I18nContext) {
    const { skip, limit, sort, search, dayInMonth, distributorId } = getOutletReportDto;
    let existedDistributorObjectId = distributorId;
    let existedDistributor: Distributor;
    if (currentUser.roles.some((r) => r === ConstantRoles.SUPER_USER || r === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)) {
      existedDistributor = await this._distributorService.findOne({ distributorId: distributorId });
      if (!existedDistributor) {
        throw new BadRequestException('distributor.not_found');
      }
      existedDistributorObjectId = existedDistributor._id;
    }
    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (!distributor) {
        throw new BadRequestException('report.dsr.invalid_request');
      }
      existedDistributorObjectId = distributor.distributor._id;
      existedDistributor = distributor.distributor;
    }
    const { data } = await this._outletJourneyPlanningService.findMissedVisitByOutletAndDistributorAndMonth(
      {
        distributorId: existedDistributorObjectId,
        skip,
        limit,
        sort,
        search,
        dayInMonth,
      },
      i18n,
    );
    const xlsxData = data?.map((item) => {
      return {
        [i18n.translate(`importExport.plannedDate`)]: moment(item.plannedDate).format('DD/MM/YYYY - hh:mm A'),
        [i18n.translate(`importExport.visitedDate`)]: moment(item.visitedDate).format('DD/MM/YYYY - hh:mm A'),
        [i18n.translate(`importExport.missedReason`)]: item.cancellationReason,
        [i18n.translate(`importExport.uncontrollable`)]: item.missedReason && !item.controllable ? i18n.t('common.yes') : i18n.t('common.no'),
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.outletID`)]: item.outletUcc,
        [i18n.translate(`importExport.outletName`)]: item.outletName,
        [i18n.translate(`importExport.salesRepId`)]: item.saleRepId,
      };
    });
    const fileName = `MisedVisits_Data`;
    const file = await this._filesService.exportXLSXFile(fileName, xlsxData, 'MissedVisits_Data', currentUser, { wch: 20 });
    return file;
  }

  async getDsrNote({ salesRepObjectIds, search, skip, limit }) {
    const data = await this.saleRepOutletRelationService.getAllOutletNoteBySalesrepObjectIds(
      salesRepObjectIds.map((e) => new Types.ObjectId(e)),
      search,
      skip,
      limit,
    );
    return data;
  }

  async exportDsrNote({ salesRepObjectIds, search }) {
    const data = await this.saleRepOutletRelationService.getAllOutletNoteBySalesrepObjectIds(
      salesRepObjectIds.map((e) => new Types.ObjectId(e)),
      search,
    );
    return data;
  }

  async getBreakdown({ currentUser, query, isExport, i18n }: { currentUser: User & { roles: string[] }; query: FetchBreakdownDto; isExport?: boolean; i18n: I18nContext }) {
    const startTime = query.startTime ? moment(query.startTime).tz(process.env.TZ).startOf('date') : moment().tz(process.env.TZ).startOf('month').startOf('date');
    const endTime = query.endTime ? moment(query.endTime).tz(process.env.TZ).endOf('date') : moment().tz(process.env.TZ).endOf('date');

    if (startTime.isAfter(endTime)) {
      throw new BadRequestException(await i18n.t('report.dsr.invalid_date_range'));
    }

    const distributorAdminRelationConditions: mongoose.FilterQuery<DistributorUserRelationDocument> = {
      userAdmin: { $eq: null },
    };

    if (query.distributorId) {
      distributorAdminRelationConditions.distributor = new mongoose.Types.ObjectId(query.distributorId);
    }
    if (query.salesRepId) {
      distributorAdminRelationConditions.user = new mongoose.Types.ObjectId(query.salesRepId);
    }

    let salesRepIds = [];
    const isDistributorAdmin = currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN);
    if (isDistributorAdmin) {
      const distributorAdminRelations = await this.distributorUserRelationModel.find({
        userAdmin: currentUser._id,
      });

      const distributorIds = distributorAdminRelations.map((relation) => String(relation.distributor));
      if (query.distributorId && !distributorIds.includes(query.distributorId)) {
        throw new ForbiddenException(await i18n.t('report.dsr.not_found_distributor'));
      }

      if (!query.distributorId) {
        distributorAdminRelationConditions.distributor = {
          $in: distributorIds.map((distributorId) => new mongoose.Types.ObjectId(distributorId)),
        };
      }

      const salesRepRelations = await this.distributorUserRelationModel.find({
        user: { $ne: null },
        userAdmin: { $eq: null },
        ...distributorAdminRelationConditions,
      });

      salesRepIds = salesRepRelations.map((relation) => String(relation.user));
      if (query.salesRepId && !salesRepIds.includes(query.salesRepId)) {
        throw new ForbiddenException(await i18n.t('report.dsr.not_found_sales_rep'));
      }

      if (!query.salesRepId) {
        distributorAdminRelationConditions.user = {
          $in: salesRepIds.map((salesRepId) => new mongoose.Types.ObjectId(salesRepId)),
        };
      }
    } else {
      const distributorUserRelations = await this.distributorUserRelationModel.find(distributorAdminRelationConditions);
      salesRepIds = distributorUserRelations.map((relation) => String(relation.user));
    }

    if (!salesRepIds.length) {
      return {
        total: 0,
        items: [],
      };
    }

    const facetData = isExport
      ? [
          {
            $skip: 0,
          },
        ]
      : [
          {
            $skip: query.skip ? +query.skip : 0,
          },
          {
            $limit: query.limit ? +query.limit : 10,
          },
        ];

    const queryResult = await this.userModel
      .aggregate()
      .match({
        _id: salesRepIds.length ? { $in: salesRepIds.map((salesRepId) => new mongoose.Types.ObjectId(salesRepId)) } : { $exists: true },
      })
      .lookup({
        localField: '_id',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'distributorUserRelation',
      })
      .unwind({ path: '$distributorUserRelation', preserveNullAndEmptyArrays: true })
      .lookup({
        localField: 'distributorUserRelation.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({ path: '$distributor', preserveNullAndEmptyArrays: true })
      .lookup({
        localField: '_id',
        from: 'outletjourneyplannings',
        foreignField: 'saleRep',
        as: 'journeyPlan',
      })
      .unwind({ path: '$journeyPlan', preserveNullAndEmptyArrays: true })
      .lookup({
        localField: 'journeyPlan.missedReason',
        from: 'journeyplanmissedreasons',
        foreignField: '_id',
        as: 'missedReason',
      })
      .unwind({ path: '$missedReason', preserveNullAndEmptyArrays: true })
      .group({
        _id: {
          salesRep: '$_id',
          distributor: '$distributor._id',
        },
        planned: {
          $sum: {
            $cond: {
              if: {
                $or: [
                  {
                    $and: [
                      {
                        $eq: ['$journeyPlan.rescheduled', true],
                      },
                      {
                        $gte: ['$journeyPlan.rescheduledDay', startTime.toDate()],
                      },
                      {
                        $lte: ['$journeyPlan.rescheduledDay', endTime.toDate()],
                      },
                    ],
                  },
                  {
                    $and: [
                      {
                        $eq: ['$journeyPlan.rescheduled', false],
                      },
                      {
                        $gte: ['$journeyPlan.day', startTime.toDate()],
                      },
                      {
                        $lte: ['$journeyPlan.day', endTime.toDate()],
                      },
                    ],
                  },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
        success: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  { $eq: ['$journeyPlan.visitStatus', VisitStatus.COMPLETED] },
                  {
                    $or: [
                      {
                        $and: [
                          {
                            $eq: ['$journeyPlan.rescheduled', true],
                          },
                          {
                            $gte: ['$journeyPlan.rescheduledDay', startTime.toDate()],
                          },
                          {
                            $lte: ['$journeyPlan.rescheduledDay', endTime.toDate()],
                          },
                        ],
                      },
                      {
                        $and: [
                          {
                            $eq: ['$journeyPlan.rescheduled', false],
                          },
                          {
                            $gte: ['$journeyPlan.day', startTime.toDate()],
                          },
                          {
                            $lte: ['$journeyPlan.day', endTime.toDate()],
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
        controllable: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  {
                    $ne: ['$missedReason.controllable', false],
                  },
                  { $ne: ['$journeyPlan.visitStatus', VisitStatus.COMPLETED] },
                  {
                    $or: [
                      {
                        $and: [
                          { $eq: ['$journeyPlan.rescheduled', true] },
                          {
                            $gte: ['$journeyPlan.rescheduledDay', startTime.toDate()],
                          },
                          {
                            $lte: ['$journeyPlan.rescheduledDay', endTime.toDate()],
                          },
                          {
                            $lt: ['$journeyPlan.rescheduledDay', moment().tz(process.env.TZ).startOf('date').toDate()],
                          },
                        ],
                      },
                      {
                        $and: [
                          { $eq: ['$journeyPlan.rescheduled', false] },
                          {
                            $gte: ['$journeyPlan.day', startTime.toDate()],
                          },
                          {
                            $lte: ['$journeyPlan.day', endTime.toDate()],
                          },
                          {
                            $lt: ['$journeyPlan.day', moment().tz(process.env.TZ).startOf('date').toDate()],
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
        uncontrollable: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  {
                    $eq: ['$missedReason.controllable', false],
                  },
                  { $ne: ['$journeyPlan.visitStatus', VisitStatus.COMPLETED] },
                  {
                    $or: [
                      {
                        $and: [
                          { $eq: ['$journeyPlan.rescheduled', true] },
                          {
                            $gte: ['$journeyPlan.rescheduledDay', startTime.toDate()],
                          },
                          {
                            $lte: ['$journeyPlan.rescheduledDay', endTime.toDate()],
                          },
                          {
                            $lt: ['$journeyPlan.rescheduledDay', moment().tz(process.env.TZ).startOf('date').toDate()],
                          },
                        ],
                      },
                      {
                        $and: [
                          { $eq: ['$journeyPlan.rescheduled', false] },
                          {
                            $gte: ['$journeyPlan.day', startTime.toDate()],
                          },
                          {
                            $lte: ['$journeyPlan.day', endTime.toDate()],
                          },
                          {
                            $lt: ['$journeyPlan.day', moment().tz(process.env.TZ).startOf('date').toDate()],
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
      })
      .lookup({
        localField: '_id.salesRep',
        from: 'users',
        foreignField: '_id',
        as: 'salesRep',
      })
      .unwind({ path: '$salesRep', preserveNullAndEmptyArrays: true })
      .lookup({
        localField: '_id.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({ path: '$distributor', preserveNullAndEmptyArrays: true })
      .project({
        _id: 0,
        id: '$_id.salesRep',
        salesRepId: '$salesRep.saleRepId',
        salesRepName: '$salesRep.username',
        distributorId: '$distributor.distributorId',
        distributorName: '$distributor.distributorName',
        plannedJourneyPlans: '$planned',
        successJourneyPlans: '$success',
        controllableJourneyPlans: '$controllable',
        uncontrollableJourneyPlans: '$uncontrollable',
        cpsr: {
          $switch: {
            branches: [
              {
                case: { $lte: [{ $subtract: ['$planned', '$uncontrollable'] }, 0] },
                then: 0,
              },
            ],
            default: {
              $divide: ['$success', { $subtract: ['$planned', '$uncontrollable'] }],
            },
          },
          // $cond: {
          //   if: {
          //     $lte: [{ $subtract: ['$planned', '$uncontrollable'] }, 0],
          //   },
          //   then: 0,
          //   else: {
          //     $divide: ['$success', { $subtract: ['$planned', '$uncontrollable'] }],
          //   },
          // },
        },
      })
      .sort(
        query.sort || {
          distributorId: -1,
          salesRepId: -1,
          plannedJourneyPlans: -1,
        },
      )
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: facetData,
      })
      .exec();

    const [{ totalRecords, data }] = queryResult;

    return {
      total: totalRecords?.[0]?.total,
      items: data,
    };
  }

  async getCPSRDetail({ currentUser, query, isExport, i18n }: { currentUser: User & { roles: string[] }; query: FetchCPSRDetailDto; isExport?: boolean; i18n: I18nContext }) {
    const today = moment().tz(process.env.TZ);
    const startTime = query.startTime ? moment(query.startTime).tz(process.env.TZ).startOf('date') : moment().tz(process.env.TZ).startOf('month').startOf('date');
    const endTime = query.endTime ? moment(query.endTime).tz(process.env.TZ).endOf('date') : moment().tz(process.env.TZ).endOf('date');

    if (startTime.isAfter(endTime)) {
      throw new BadRequestException(await i18n.t('report.dsr.invalid_date_range'));
    }

    const distributorAdminRelationConditions: mongoose.FilterQuery<DistributorUserRelationDocument> = {
      userAdmin: { $eq: null },
    };

    if (query.distributorId) {
      distributorAdminRelationConditions.distributor = new mongoose.Types.ObjectId(query.distributorId);
    }
    if (query.salesRepId) {
      distributorAdminRelationConditions.user = new mongoose.Types.ObjectId(query.salesRepId);
    }

    let salesRepIds = [];
    const isDistributorAdmin = currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN);
    if (isDistributorAdmin) {
      const distributorAdminRelations = await this.distributorUserRelationModel.find({
        userAdmin: currentUser._id,
      });
      const distributorIds = distributorAdminRelations.map((relation) => String(relation.distributor));
      if (query.distributorId && !distributorIds.includes(query.distributorId)) {
        throw new ForbiddenException(await i18n.t('report.dsr.not_found_distributor'));
      }

      if (!query.distributorId) {
        distributorAdminRelationConditions.distributor = {
          $in: distributorIds.map((distributorId) => new mongoose.Types.ObjectId(distributorId)),
        };
      }

      const salesRepRelations = await this.distributorUserRelationModel.find({
        user: { $ne: null },
        userAdmin: { $eq: null },
        ...distributorAdminRelationConditions,
      });

      salesRepIds = salesRepRelations.map((relation) => String(relation.user));
      if (query.salesRepId && !salesRepIds.includes(query.salesRepId)) {
        throw new ForbiddenException(await i18n.t('report.dsr.not_found_sales_rep'));
      }

      if (!query.salesRepId) {
        distributorAdminRelationConditions.user = {
          $in: salesRepIds.map((salesRepId) => new mongoose.Types.ObjectId(salesRepId)),
        };
      }
    } else {
      const distributorUserRelations = await this.distributorUserRelationModel.find(distributorAdminRelationConditions);
      salesRepIds = distributorUserRelations.map((relation) => String(relation.user));
    }

    if (!salesRepIds.length) {
      return {
        total: 0,
        items: [],
      };
    }

    const facetData = isExport
      ? [
          {
            $skip: 0,
          },
        ]
      : [
          {
            $skip: query.skip ? +query.skip : 0,
          },
          {
            $limit: query.limit ? query.limit : 10,
          },
        ];

    const queryResult = await this.outletJourneyPlanningModel
      .aggregate()
      .match({
        saleRep: { $in: salesRepIds.map((salesRepId) => new mongoose.Types.ObjectId(salesRepId)) },
        $or: [
          {
            rescheduled: true,
            rescheduledDay: {
              $gte: startTime.toDate(),
              $lte: endTime.toDate(),
            },
          },
          {
            rescheduled: false,
            day: {
              $gte: startTime.toDate(),
              $lte: endTime.toDate(),
            },
          },
        ],
      })
      .match({
        $or: [
          {
            visitStatus: VisitStatus.COMPLETED,
          },
          {
            visitStatus: { $ne: VisitStatus.COMPLETED },
            rescheduled: true,
            rescheduledDay: {
              $lte: today.startOf('date').toDate(),
            },
          },
          {
            visitStatus: { $ne: VisitStatus.COMPLETED },
            rescheduled: false,
            day: {
              $lte: today.startOf('date').toDate(),
            },
          },
        ],
      })
      .lookup({
        localField: 'saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'salesRep',
      })
      .unwind({ path: '$salesRep', preserveNullAndEmptyArrays: true })
      .lookup({
        localField: 'saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'distributoruserrelations',
      })
      .unwind({ path: '$distributoruserrelations', preserveNullAndEmptyArrays: false })
      .lookup({
        localField: 'distributoruserrelations.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({ path: '$distributor', preserveNullAndEmptyArrays: false })
      .lookup({
        localField: 'outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({ path: '$outlet', preserveNullAndEmptyArrays: true })
      .lookup({
        localField: 'missedReason',
        from: 'journeyplanmissedreasons',
        foreignField: '_id',
        as: 'missedReason',
      })
      .unwind({ path: '$missedReason', preserveNullAndEmptyArrays: true })
      .project({
        _id: 0,
        id: '$_id',
        visitedDate: { $ifNull: ['$visitedDay', { $ifNull: ['$rescheduledDay', '$day'] }] },
        plannedDate: '$day',
        ucc: '$outlet.ucc',
        outletName: '$outlet.name',
        salesRepId: '$salesRep.saleRepId',
        salesRepName: '$salesRep.username',
        distributorId: '$distributor.distributorId',
        distributorName: '$distributor.distributorName',
        status: {
          $cond: {
            if: {
              $eq: ['$visitStatus', VisitStatus.COMPLETED],
            },
            then: 'success',
            else: {
              $cond: {
                if: {
                  $eq: ['$missedReason.controllable', false],
                },
                then: 'uc-miss',
                else: 'c-miss',
              },
            },
          },
        },
        reason: {
          $cond: {
            if: {
              $eq: ['$visitStatus', VisitStatus.COMPLETED],
            },
            then: null,
            else: '$missedReason.reasonKey',
          },
        },
      })
      .match({
        status: query.status || { $exists: true },
      })
      .sort(
        query.sort || {
          visitDate: -1,
        },
      )
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: facetData,
      });

    const [{ totalRecords, data }] = queryResult;

    return {
      items: data.map((item) => ({ ...item, reason: item.reason ? i18n.t(item.reason) : '' })),
      total: totalRecords?.[0]?.total,
    };
  }

  nndAggregate(body: FetchNNDDto) {
    const { brands, salesRepObjectIds, sort, from, to } = body;
    const fromDate = moment(from).startOf('day').toDate();
    const toDate = moment(to).endOf('day').toDate();

    const aggregate = this.outletJourneyPlanningModel.aggregate().match({
      saleRep: { $in: salesRepObjectIds.map((e) => new Types.ObjectId(e)) },
      visitStatus: VisitStatus.COMPLETED,
      $or: [
        { day: { $gte: fromDate, $lte: toDate }, rescheduled: false },
        { rescheduledDay: { $gte: fromDate, $lte: toDate }, rescheduled: true },
        { visitedDay: { $gte: fromDate, $lte: toDate } },
      ],
    });
    aggregate
      .lookup({
        localField: 'saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'saleRep',
      })
      .unwind({
        path: '$saleRep',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({
        path: '$outlet',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'saleRep._id',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'distributoruserrelations',
      })
      .unwind({ path: '$distributoruserrelations', preserveNullAndEmptyArrays: false })
      .lookup({
        localField: 'distributoruserrelations.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({ path: '$distributor', preserveNullAndEmptyArrays: false })
      .lookup({
        localField: '_id',
        from: 'salerepexecutionavailabilities',
        foreignField: 'journeyPlan',
        as: 'salerepexecutionavailabilities',
      })
      .addFields({
        availability: {
          $arrayElemAt: ['$salerepexecutionavailabilities', -1],
        },
      });
    aggregate.project({
      salerepexecutionavailabilities: '$availability.brands',
      _id: 1,
      visitedDay: '$visitedDay',
      rescheduledDay: 1,
      day: 1,
      rescheduled: 1,
      plannedDate: '$day',
      ucc: '$outlet.ucc',
      outletName: '$outlet.name',
      distributorName: '$distributor.distributorName',
      distributorId: '$distributor.distributorId',
      salesRepName: '$saleRep.username',
      salesRepId: '$saleRep.saleRepId',
    });
    if (brands.length) {
      const temp = {
        salerepexecutionavailabilities: {
          $elemMatch: {
            name: { $in: brands },
            $or: [
              {
                hasCan: true,
              },
              {
                hasBcan: true,
              },
              {
                hasQt: true,
              },
              {
                hasPt: true,
              },
              {
                hasKeg: true,
              },
            ],
          },
        },
      };
      aggregate.match(temp);
    }
    aggregate.sort(sort);
    return aggregate;
  }

  async getNNDReport(body: FetchNNDDto) {
    const { skip, limit } = body;
    const aggregate = this.nndAggregate(body);
    aggregate.facet({
      totalRecords: [
        {
          $count: 'total',
        },
      ],
      data: [
        {
          $skip: skip ? skip : 0,
        },
        {
          $limit: limit ? limit : 10,
        },
      ],
    });
    const res = await aggregate.exec();
    const totalItem = res[0]?.totalRecords[0]?.total || 0;
    const data = res[0].data || [];
    return {
      totalItem,
      data,
    };
  }

  async exportNNDReport(body: FetchNNDDto) {
    const res = await this.nndAggregate(body).exec();
    return res;
  }

  forwardStockLookupSalesRep(aggregate, sort, isPaging, skip, limit) {
    aggregate
      .lookup({
        localField: 'saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'saleRepInfo',
      })
      .unwind({
        path: '$saleRepInfo',
        preserveNullAndEmptyArrays: false,
      });
    if (sort?.salesRepName || sort?.salesRepId) {
      aggregate.addFields({
        salesRepName: '$saleRepInfo.username',
        salesRepId: '$saleRepInfo.saleRepId',
      });
      aggregate.sort(sort);
      if (isPaging) {
        aggregate.skip(skip).limit(limit);
      }
    }
  }
  forwardStockLookupOutlet(aggregate, sort, isPaging, skip, limit) {
    aggregate
      .lookup({
        localField: 'outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({
        path: '$outlet',
        preserveNullAndEmptyArrays: false,
      });

    if (sort?.ucc || sort?.outletName) {
      aggregate.addFields({
        ucc: '$outlet.ucc',
        outletName: '$outlet.name',
      });
      aggregate.sort(sort);
      if (isPaging) {
        aggregate.skip(skip).limit(limit);
      }
    }
  }
  forwardStockLookupDistributor(aggregate, sort, isPaging, skip, limit) {
    aggregate
      .lookup({
        localField: 'saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'distributoruserrelations',
      })
      .unwind({ path: '$distributoruserrelations', preserveNullAndEmptyArrays: false })
      .lookup({
        localField: 'distributoruserrelations.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({ path: '$distributor', preserveNullAndEmptyArrays: false });
    if (sort?.distributorId || sort?.distributorName) {
      aggregate.addFields({
        distributorName: '$distributor.distributorName',
        distributorId: '$distributor.distributorId',
      });
      aggregate.sort(sort);
      if (isPaging) {
        aggregate.skip(skip).limit(limit);
      }
    }
  }
  forwardStockLookupVisibility(aggregate, sort, isPaging, skip, limit) {
    aggregate
      .lookup({
        localField: '_id',
        from: 'salerepexecutionvisibilities',
        foreignField: 'journeyPlan',
        as: 'salerepexecutionvisibilities',
      })
      .addFields({
        visibility: {
          $arrayElemAt: ['$salerepexecutionvisibilities', -1],
        },
      })
      .lookup({
        localField: 'visibility.images',
        from: 'files',
        foreignField: '_id',
        as: 'visibility.images',
      });
    if (sort?.forwardStock) {
      aggregate.addFields({
        forwardStock: '$visibility.forwardStock',
      });
      aggregate.sort(sort);
      if (isPaging) {
        aggregate.skip(skip).limit(limit);
      }
    }
  }
  forwardStockAggregate(body: FetchForwardStockDto, isPaging) {
    const { salesRepObjectIds, from, to, skip, limit } = body;
    let { sort } = body;
    const fromDate = moment(from).startOf('day').toDate();
    const toDate = moment(to).endOf('day').toDate();
    if (!sort || !Object.keys(sort).length) {
      sort = {
        visitedDay: 'asc',
      };
    }
    const aggregate = this.outletJourneyPlanningModel.aggregate().match({
      saleRep: { $in: salesRepObjectIds.map((e) => new Types.ObjectId(e)) },
      visitStatus: VisitStatus.COMPLETED,
      $or: [
        { day: { $gte: fromDate, $lte: toDate }, rescheduled: false },
        { rescheduledDay: { $gte: fromDate, $lte: toDate }, rescheduled: true },
      ],
    });

    if (sort?.visitedDay || sort?.plannedDate) {
      aggregate.sort(sort);
      if (isPaging) {
        aggregate.skip(skip).limit(limit);
      }
      this.forwardStockLookupSalesRep(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupOutlet(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupDistributor(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupVisibility(aggregate, sort, isPaging, skip, limit);
    }
    if (sort?.salesRepName || sort?.salesRepId) {
      this.forwardStockLookupSalesRep(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupOutlet(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupDistributor(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupVisibility(aggregate, sort, isPaging, skip, limit);
    }
    if (sort?.ucc || sort?.outletName) {
      this.forwardStockLookupOutlet(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupSalesRep(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupDistributor(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupVisibility(aggregate, sort, isPaging, skip, limit);
    }
    if (sort?.distributorId || sort?.distributorName) {
      this.forwardStockLookupDistributor(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupOutlet(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupSalesRep(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupVisibility(aggregate, sort, isPaging, skip, limit);
    }
    if (sort?.forwardStock) {
      this.forwardStockLookupVisibility(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupDistributor(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupOutlet(aggregate, sort, isPaging, skip, limit);
      this.forwardStockLookupSalesRep(aggregate, sort, isPaging, skip, limit);
    }

    aggregate.project({
      _id: 1,
      visitedDay: 1,
      rescheduledDay: 1,
      day: 1,
      rescheduled: 1,
      plannedDate: '$day',
      ucc: '$outlet.ucc',
      outletName: '$outlet.name',
      distributorName: '$distributor.distributorName',
      distributorId: '$distributor.distributorId',
      salesRepName: '$saleRepInfo.username',
      salesRepId: '$saleRepInfo.saleRepId',
      forwardStock: '$visibility.forwardStock',
      images: '$visibility.images.path',
    });
    return aggregate;
  }

  async getForwardStockReport(body: FetchForwardStockDto) {
    const aggregate = this.forwardStockAggregate(body, true);
    const res = await aggregate.exec();
    return res;
  }

  async exportForwardStockReport(body: FetchForwardStockDto) {
    const res = await this.forwardStockAggregate(body, false).exec();
    return res;
  }
}
