import { Is<PERSON>rray, IsDateString, IsNumber, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class FetchForwardStockDto {
  @ApiProperty({ default: [] })
  @IsArray()
  salesRepObjectIds: string[];

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  skip: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  limit: number;

  @ApiProperty()
  @IsDateString()
  from: Date;

  @ApiProperty()
  @IsDateString()
  to: Date;

  @ApiProperty()
  sort: any;
}
