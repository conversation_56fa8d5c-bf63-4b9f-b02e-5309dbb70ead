import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsOptional, IsString, Matches } from 'class-validator';
import { SortOrder } from 'mongoose';
import { PaginationDto } from 'src/shared/dtos/pagination.dto';
import { SortOrderDto } from 'src/shared/dtos/sort-order.dto';
import { ConstantCommons } from 'src/utils/constants';

export class CPSRDetailOrder extends SortOrderDto {
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  visitDate?: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  plannedDate?: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  ucc?: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletName?: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorId?: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorName?: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId?: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepName?: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  status?: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  reason?: SortOrder;
}

export class FetchCPSRDetailDto extends PaginationDto {
  @ApiModelPropertyOptional()
  @IsOptional()
  @IsString()
  distributorId?: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsString()
  salesRepId?: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsString()
  status?: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  startTime?: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  endTime?: string;

  @ApiModelPropertyOptional({ type: () => CPSRDetailOrder })
  sort?: CPSRDetailOrder;
}
