import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ArrayMinSize, IsArray, IsDateString, IsString, Matches } from 'class-validator';

import { ConstantCommons } from '../../utils/constants';

export class FetchCPSRDto {
  @ApiModelProperty()
  @IsArray()
  @ArrayMinSize(1)
  saleRepIds: Array<string>;

  @ApiModelProperty({ default: new Date().toISOString() })
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  startTime: string;

  @ApiModelProperty({ default: new Date().toISOString() })
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  endTime: string;
}
