import { ArrayMinSize, <PERSON><PERSON><PERSON>y, IsString, Matches } from 'class-validator';

import { ConstantCommons } from '../../utils/constants';
import { ApiProperty } from '@nestjs/swagger';

export class FetchCPSRDto {
  @ApiProperty()
  @IsArray()
  @ArrayMinSize(1)
  saleRepIds: Array<string>;

  @ApiProperty({ default: new Date().toISOString() })
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  startTime: string;

  @ApiProperty({ default: new Date().toISOString() })
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  endTime: string;
}
