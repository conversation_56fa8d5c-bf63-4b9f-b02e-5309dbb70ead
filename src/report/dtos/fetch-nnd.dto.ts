import { ApiModelProperty, ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ArrayMinSize, IsArray, IsDateString, IsNumber, IsOptional, IsString, Matches } from 'class-validator';

export class FetchNNDDto {
  @ApiModelProperty({ default: [] })
  @IsArray()
  salesRepObjectIds: string[];

  @ApiModelProperty({ default: [] })
  @IsArray()
  brands: string[];

  @ApiModelProperty()
  @IsNumber()
  @IsOptional()
  skip: number;

  @ApiModelProperty()
  @IsNumber()
  @IsOptional()
  limit: number;

  @ApiModelProperty()
  @IsDateString()
  from: Date;

  @ApiModelProperty()
  @IsDateString()
  to: Date;

  @ApiModelProperty()
  sort: any;
}
