import { Is<PERSON>rray, IsDateString, IsNumber, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class FetchNNDDto {
  @ApiProperty({ default: [] })
  @IsArray()
  salesRepObjectIds: string[];

  @ApiProperty({ default: [] })
  @IsArray()
  brands: string[];

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  skip: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  limit: number;

  @ApiProperty()
  @IsDateString()
  from: Date;

  @ApiProperty()
  @IsDateString()
  to: Date;

  @ApiProperty()
  sort: any;
}
