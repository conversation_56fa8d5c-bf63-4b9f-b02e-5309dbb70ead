import { SortOrder } from 'mongoose';

import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsDateString, IsOptional } from 'class-validator';

export class MissedOutletReportSortOrder extends SortOrderDto {
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorId: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorName: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  plannedDate: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  visitedDate: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletId: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  missedReason: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletName: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;
}

export class GetMissedOutletReportDto extends PaginationDto {
  @ApiModelPropertyOptional()
  sort: Partial<MissedOutletReportSortOrder>;

  @ApiModelPropertyOptional()
  search?: string;

  @IsDateString()
  @ApiModelPropertyOptional()
  @IsOptional()
  dayInMonth: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  distributorId?: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  distributorIds?: string[];
}
