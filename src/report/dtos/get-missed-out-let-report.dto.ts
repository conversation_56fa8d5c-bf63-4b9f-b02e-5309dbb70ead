import { SortOrder } from 'mongoose';

import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { IsDateString, IsOptional } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class MissedOutletReportSortOrder extends SortOrderDto {
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorId: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorName: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  plannedDate: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  visitedDate: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletId: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  missedReason: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletName: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;
}

export class GetMissedOutletReportDto extends PaginationDto {
  @ApiPropertyOptional()
  sort: Partial<MissedOutletReportSortOrder>;

  @ApiPropertyOptional()
  search?: string;

  @IsDateString()
  @ApiPropertyOptional()
  @IsOptional()
  dayInMonth: string;

  @ApiPropertyOptional()
  @IsOptional()
  distributorId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  distributorIds?: string[];
}
