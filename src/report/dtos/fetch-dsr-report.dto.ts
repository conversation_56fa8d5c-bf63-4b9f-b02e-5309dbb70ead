import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { <PERSON><PERSON><PERSON><PERSON>, IsN<PERSON>ber, IsOptional, <PERSON>, <PERSON> } from 'class-validator';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { SortOrder } from 'mongoose';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DSRReportSortOrder extends SortOrderDto {
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorName: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorId: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  name: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  cpsr: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  mabo: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  sales: SortOrder;
}

export class FetchDsrReportDto {
  @ApiProperty()
  @IsNumber()
  @Min(1, { message: 'report.dsr.month.min' })
  @Max(12, { message: 'report.dsr.month.max' })
  month: number;

  @ApiProperty()
  @IsNumber()
  year: number;

  @ApiPropertyOptional({ default: [] })
  @IsArray()
  @IsOptional()
  distributorIds: string[];

  @ApiProperty({ type: () => DSRReportSortOrder })
  sort: DSRReportSortOrder;
}

export class FetchDsrReportWithPagination extends PaginationDto {
  @ApiProperty()
  @IsNumber()
  @Min(1, { message: 'report.dsr.month.min' })
  @Max(12, { message: 'report.dsr.month.max' })
  month: number;

  @ApiProperty()
  @IsNumber()
  year: number;

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  distributorIds: string[];

  @ApiPropertyOptional({ type: () => DSRReportSortOrder })
  sort: DSRReportSortOrder;
}
