import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { ApiModelProperty, ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, Max, Min } from 'class-validator';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { SortOrder } from 'mongoose';

export class DSRReportSortOrder extends SortOrderDto {
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorName: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorId: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  name: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  cpsr: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  mabo: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  sales: SortOrder;
}

export class FetchDsrReportDto {
  @ApiModelProperty()
  @IsNumber()
  @Min(1, { message: 'report.dsr.month.min' })
  @Max(12, { message: 'report.dsr.month.max' })
  month: number;

  @ApiModelProperty()
  @IsNumber()
  year: number;

  @ApiModelPropertyOptional({ default: [] })
  @IsArray()
  @IsOptional()
  distributorIds: string[];

  @ApiModelProperty({ type: () => DSRReportSortOrder })
  sort: DSRReportSortOrder;
}

export class FetchDsrReportWithPagination extends PaginationDto {
  @ApiModelProperty()
  @IsNumber()
  @Min(1, { message: 'report.dsr.month.min' })
  @Max(12, { message: 'report.dsr.month.max' })
  month: number;

  @ApiModelProperty()
  @IsNumber()
  year: number;

  @ApiModelPropertyOptional()
  @IsArray()
  @IsOptional()
  distributorIds: string[];

  @ApiModelPropertyOptional({ type: () => DSRReportSortOrder })
  sort: DSRReportSortOrder;
}
