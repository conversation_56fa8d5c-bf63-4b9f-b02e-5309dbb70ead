import { Expose, Transform } from 'class-transformer';
import { Date } from 'mongoose';

export class SaleRepOutletReport {
  @Transform(({ obj }) => obj._id.toString())
  _id: string;
  saleRepId: string;
  username: string;
}

export class GetSuccessOutletReportResponseDto {
  @Expose()
  plannedDate: Date;
  @Expose()
  visitedDate: Date;
  @Expose()
  endDate: Date;
  @Expose()
  outletUcc: string;
  @Expose()
  outletName: string;
  @Expose()
  availability: string;
  @Expose()
  distributorId: string;
  @Expose()
  distributorName: string;
  @Expose()
  visibility: string;
  @Expose()
  @Transform(({ obj }) => obj.availabilitiesId?.toString())
  availabilitiesId: string;
  @Expose()
  @Transform(({ obj }) => obj.visibilitiesId?.toString())
  visibilitiesId: string;
  @Expose()
  @Transform(({ obj }) => obj.journeyPlanId?.toString())
  journeyPlanId: string;
  @Expose()
  saleRep: SaleRepOutletReport;
}
