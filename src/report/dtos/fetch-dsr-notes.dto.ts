import { <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON>ptional, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class FetchDSRNotesDto {
  @ApiProperty({ default: '' })
  @IsString()
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  search: string;

  @ApiProperty({ default: [] })
  @IsArray()
  salesRepObjectIds: string[];

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  skip: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  limit: number;
}
