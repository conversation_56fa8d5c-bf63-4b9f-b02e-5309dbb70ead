import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ArrayMinSize, IsArray, IsDateString, IsNumber, IsOptional, IsString, Matches } from 'class-validator';

import { ConstantCommons } from '../../utils/constants';
import { Transform, TransformFnParams } from 'class-transformer';

export class FetchDSRNotesDto {
  @ApiModelProperty({ default: '' })
  @IsString()
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  search: string;

  @ApiModelProperty({ default: [] })
  @IsArray()
  salesRepObjectIds: string[];

  @ApiModelProperty()
  @IsNumber()
  @IsOptional()
  skip: number;

  @ApiModelProperty()
  @IsNumber()
  @IsOptional()
  limit: number;
}
