import { SortOrder } from 'mongoose';

import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsDateString, IsOptional } from 'class-validator';

export class SuccessOutletReportSortOrder extends SortOrderDto {
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  plannedDate: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  visitedDate: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletId: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletName: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  availability: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  visibility: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;
}

export class GetSuccessOutletReportDto extends PaginationDto {
  @ApiModelPropertyOptional()
  sort: Partial<SuccessOutletReportSortOrder>;

  @ApiModelPropertyOptional()
  search?: string;

  @IsDateString()
  @ApiModelPropertyOptional()
  @IsOptional()
  dayInMonth: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  distributorId?: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  distributorIds?: string[];
}
