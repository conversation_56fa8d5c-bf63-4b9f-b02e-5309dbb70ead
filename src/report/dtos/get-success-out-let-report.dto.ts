import { SortOrder } from 'mongoose';

import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { IsDateString, IsOptional } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class SuccessOutletReportSortOrder extends SortOrderDto {
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  plannedDate: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  visitedDate: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletId: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletName: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  availability: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  visibility: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;
}

export class GetSuccessOutletReportDto extends PaginationDto {
  @ApiPropertyOptional()
  sort: Partial<SuccessOutletReportSortOrder>;

  @ApiPropertyOptional()
  search?: string;

  @IsDateString()
  @ApiPropertyOptional()
  @IsOptional()
  dayInMonth: string;

  @ApiPropertyOptional()
  @IsOptional()
  distributorId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  distributorIds?: string[];
}
