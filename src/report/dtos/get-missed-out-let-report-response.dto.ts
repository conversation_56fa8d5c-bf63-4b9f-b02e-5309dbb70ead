import { Date } from 'mongoose';
import { Expose, Transform } from 'class-transformer';
import { SaleRepOutletReport } from './get-success-out-let-report-response.dto';

export class GetMissedOutletReportResponseDto {
  @Expose()
  plannedDate: Date;
  @Expose()
  visitedDate: Date;
  @Expose()
  missedReason: string;
  @Expose()
  controllable: boolean;
  @Expose()
  outletUcc: string;
  @Expose()
  outletName: string;
  @Expose()
  distributorId: string;
  @Expose()
  distributorName: string;
  @Expose()
  @Transform(({ obj }) => obj.journeyPlanId.toString())
  journeyPlanId: string;
  @Expose()
  saleRep: SaleRepOutletReport;
  @Expose()
  jpReasonHistory: any;
}
