import { BadRequestException, Body, Controller, Get, HttpCode, HttpStatus, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';

import * as moment from 'moment';
import { I18n, I18nContext } from 'nestjs-i18n';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { DistributorService, DistributorUserRelationService } from '../distributor/services';
import { DsrTargetService } from '../dsr-targets/services';
import { SaleRepExecutionAvailabilityService, SaleRepExecutionVisibilityService, SaleRepStatisticService } from '../sale-rep/services';
import { ApiException } from '../shared/api-exception.model';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { Roles } from '../shared/decorator/roles.decorator';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Serialize } from '../shared/interceptor/serialize.interceptor';
import { ApiResponse } from '../shared/response/api-response';
import { toListResponse } from '../utils';
import { ConstantRoles } from '../utils/constants/role';
import { FetchDsrReportDto, FetchDsrReportWithPagination, GetMissedOutletReportDto, GetSuccessOutletReportDto } from './dtos';
import { GetMissedOutletReportResponseDto } from './dtos/get-missed-out-let-report-response.dto';
import { GetSuccessOutletReportResponseDto } from './dtos/get-success-out-let-report-response.dto';
import { JourneyPlanMissedReasonHistoriesService } from 'src/journey-plannings/services/journey-plan-missed-reason-history.service';
import { FetchDSRNotesDto } from './dtos/fetch-dsr-notes.dto';
import { ReportService } from './services/report.service';
import { User } from 'src/users/schemas/user.schema';
import { FetchBreakdownDto } from './dtos/fetch-breakdown.dto';
import { FetchCPSRDetailDto } from './dtos/fetch-cpsr-detail.dto';
import { FetchNNDDto } from './dtos/fetch-nnd.dto';
import { FetchForwardStockDto } from './dtos/fetch-forward-stock.dto';
import { FilesService } from 'src/files/services';
import { OpCos } from 'src/config';

@ApiTags('Report')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/report')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ReportController {
  constructor(
    private readonly _saleRepStatisticService: SaleRepStatisticService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _saleRepExecutionVisibilityService: SaleRepExecutionVisibilityService,
    private readonly _saleRepExecutionAvailabilityService: SaleRepExecutionAvailabilityService,
    private readonly _journeyPlanMissedReasonHistoriesService: JourneyPlanMissedReasonHistoriesService,
    private readonly _distributorService: DistributorService,
    private readonly _dsrTargetService: DsrTargetService,
    private readonly _filesService: FilesService,
    private readonly reportService: ReportService,
  ) {}

  @Post('success-visit/list')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get Outlet by Id, name, distributor and month',
  })
  @Serialize(GetSuccessOutletReportResponseDto)
  async getSuccessVisitsOutlet(@Body() getOutletReportDto: GetSuccessOutletReportDto, @CurrentUser() currentUser: any) {
    let data = null;
    if (process.env.OPCO == OpCos.Malaysia) {
      data = await this.reportService.getSuccessVisitsOutletMY(getOutletReportDto, currentUser);
    } else {
      data = await this.reportService.getSuccessVisitsOutlet(getOutletReportDto, currentUser);
    }
    return new ApiResponse(toListResponse(data));
  }

  @Post('success-visit/export')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async exportSuccessVisitsOutlet(@Body() getOutletReportDto: GetSuccessOutletReportDto, @CurrentUser() currentUser: any, @I18n() i18n: I18nContext) {
    let file = null;
    if (process.env.OPCO == OpCos.Malaysia) {
      file = await this.reportService.exportSuccessVisitsOutletMY(getOutletReportDto, currentUser, i18n);
    } else if (process.env.OPCO == OpCos.Indonesia) {
      file = await this.reportService.exportSuccessVisitsOutletID(getOutletReportDto, currentUser, i18n);
    } else {
      file = await this.reportService.exportSuccessVisitsOutlet(getOutletReportDto, currentUser, i18n);
    }
    return new ApiResponse(file);
  }

  @Get('missed-visit/detail/:planId')
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async getMissedReportDetail(@Param('planId') planId: string, @CurrentUser() currentUser: any) {
    const data = await this._journeyPlanMissedReasonHistoriesService.getLatestOutletReportDetail(planId);
    return new ApiResponse(data);
  }

  @Post('missed-visit/list')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @Serialize(GetMissedOutletReportResponseDto)
  @HttpCode(HttpStatus.OK)
  async getMissedVisitsOutlet(@Body() getOutletReportDto: GetMissedOutletReportDto, @CurrentUser() currentUser: any, @I18n() i18n: I18nContext) {
    let data = null;
    if (process.env.OPCO == OpCos.Malaysia) {
      data = await this.reportService.getMissedVisitsOutletMY(getOutletReportDto, currentUser, i18n);
    } else {
      data = await this.reportService.getMissedVisitsOutlet(getOutletReportDto, currentUser, i18n);
    }
    return new ApiResponse(toListResponse(data));
  }

  @Post('missed-visit/export')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async exportMissedVisitsOutlet(@Body() getOutletReportDto: GetMissedOutletReportDto, @CurrentUser() currentUser: any, @I18n() i18n: I18nContext) {
    let file = null;
    if (process.env.OPCO == OpCos.Malaysia) {
      file = await this.reportService.exportMissedVisitsOutletMY(getOutletReportDto, currentUser, i18n);
    } else {
      file = await this.reportService.exportMissedVisitsOutlet(getOutletReportDto, currentUser, i18n);
    }
    return new ApiResponse(file);
  }

  @Get('success-visit/:availabilitiesId/:visibilitiesId')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async getSuccessVisitsOutletDetail(@Param('availabilitiesId') availabilitiesId: string, @Param('visibilitiesId') visibilitiesId: string) {
    const availabilityById = await this._saleRepExecutionAvailabilityService.getAvailabilityById(availabilitiesId);
    const visitedDay = availabilityById.journeyPlan.visitedDay;
    const outletName = availabilityById.outlet.name;
    const outletUcc = availabilityById.outlet.ucc;
    const location = availabilityById.journeyPlan.location;
    const availableOfBrands = availabilityById.brands.filter((avai) => avai.hasQt || avai.hasPt || avai.hasCan || avai.hasKeg || avai.hasBcan).length;
    const numOfBrands = `${availableOfBrands}/${availabilityById.brands.length}`;
    const availability = {
      saleRep: { _id: availabilityById.saleRep._id, saleRepId: availabilityById.saleRep.saleRepId, username: availabilityById.saleRep.username },
      outletName,
      outletUcc,
      visitedDay,
      geoLocation: { coordinates: [location.latitude, location.longitude] },
      availability: { numOfBrands, brands: availabilityById.brands },
    };
    const visibilityById = await this._saleRepExecutionVisibilityService.getVisibilityById(visibilitiesId);
    const visibility = { images: visibilityById.images, forwardStock: visibilityById.forwardStock };
    return new ApiResponse({ availability, visibility });
  }

  @Post('dsr/list')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get DSR report',
  })
  async getDSRReport(@Body() fetchDsrReportDto: FetchDsrReportWithPagination, @CurrentUser() currentUser: any) {
    const { skip, limit, month, year, sort, distributorIds } = fetchDsrReportDto;
    const validDistributorIds = [];
    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      for (const distributorId of distributorIds) {
        const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
        if (distributor) {
          validDistributorIds.push(distributor.distributor.distributorId);
        }
      }
    } else {
      await Promise.all(
        distributorIds.map(async (distributorId) => {
          const distributor = await this._distributorService.findOne({ distributorId });
          if (distributor) {
            validDistributorIds.push(distributor.distributorId);
          }
        }),
      );
      if (!validDistributorIds.length) {
        throw new BadRequestException('report.dsr.not_found_distributor');
      }
    }

    let result = [{ totalRecords: [{ total: 0 }], data: null }];
    const diffMonths = this._dsrTargetService.getDiffMonth(month, year);
    if (diffMonths > 0) {
      throw new BadRequestException('report.dsr.month.invalid');
    }
    const statisticDay = this._saleRepStatisticService.getStatisticDayByDiffMonths(diffMonths);
    if (validDistributorIds && validDistributorIds.length) {
      if (statisticDay) {
        if (process.env.OPCO == OpCos.Indonesia) {
          result = await this._saleRepStatisticService.getDSRReportWithPaginationID(validDistributorIds, statisticDay, skip, limit, sort);
        } else {
          result = await this._saleRepStatisticService.getDSRReportWithPagination(validDistributorIds, statisticDay, skip, limit, sort);
        }
      } else {
        result = await this._saleRepStatisticService.getDefaultStatisticDataByDistributor(validDistributorIds, skip, limit, sort);
      }
    }

    const [{ totalRecords, data }] = result;
    return new ApiResponse(toListResponse([data, totalRecords?.[0]?.total ?? 0]));
  }

  @Post('cpsr-breakdown')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get DSR breakdown',
  })
  async getBreakdown(@CurrentUser() currentUser: User & { roles: string[] }, @Body() query: FetchBreakdownDto, @I18n() i18n: I18nContext) {
    const { total, items } = await this.reportService.getBreakdown({ currentUser, query, i18n });
    return new ApiResponse(toListResponse([items, total]));
  }

  @Post('export-cpsr-breakdown')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Export DSR breakdown',
  })
  async exportBreakdown(@CurrentUser() currentUser: User & { roles: string[] }, @Body() query: FetchBreakdownDto, @I18n() i18n: I18nContext) {
    const { items } = await this.reportService.getBreakdown({ currentUser, query, isExport: true, i18n });
    const xlsxData = items.map((item) => {
      if ([OpCos.Indonesia].includes(process.env.OPCO)) {
        return {
          [i18n.translate(`importExport.distributorId`)]: item.distributorId,
          [i18n.translate(`importExport.distributorName`)]: item.distributorName,
          [i18n.translate(`importExport.salesRepId`)]: item.salesRepId,
          [i18n.translate(`importExport.salesRepName`)]: item.salesRepName,
          [i18n.translate(`importExport.plannedJourneyPlans`)]: item.plannedJourneyPlans,
          [i18n.translate(`importExport.controllableJourneyPlans`)]: item.controllableJourneyPlans,
          [i18n.translate(`importExport.uncontrollableJourneyPlans`)]: item.uncontrollableJourneyPlans,
          [i18n.translate(`importExport.successJourneyPlans`)]: item.successJourneyPlans,
          [i18n.translate(`importExport.cpsrPercent_ID`)]: `${Math.round(item.cpsr * 100 * 100) / 100}`,
        };
      }
      return {
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.salesRepId`)]: item.salesRepId,
        [i18n.translate(`importExport.salesRepName`)]: item.salesRepName,
        [i18n.translate(`importExport.plannedJourneyPlans`)]: item.plannedJourneyPlans,
        [i18n.translate(`importExport.controllableJourneyPlans`)]: item.controllableJourneyPlans,
        [i18n.translate(`importExport.uncontrollableJourneyPlans`)]: item.uncontrollableJourneyPlans,
        [i18n.translate(`importExport.successJourneyPlans`)]: item.successJourneyPlans,
        [i18n.translate(`importExport.cpsrPercent`)]: `${Math.round(item.cpsr * 100 * 100) / 100}`,
      };
    });
    const fileName = `DSRReportCPSRBreakdown`;
    const result = await this._filesService.exportXLSXFile(fileName, xlsxData, 'DSR Breakdown', currentUser, { wch: 20 });
    return new ApiResponse(result);
  }

  @Post('cpsr-detail')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get CPSR Detail',
  })
  async getCPSRDetail(@CurrentUser() currentUser: User & { roles: string[] }, @Body() query: FetchCPSRDetailDto, @I18n() i18n: I18nContext) {
    const { total, items } = await this.reportService.getCPSRDetail({ currentUser, query, i18n });
    return new ApiResponse(toListResponse([items, total]));
  }

  @Post('export-cpsr-detail')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Export CPSR Detail',
  })
  async exportCPSRDetail(@CurrentUser() currentUser: User & { roles: string[] }, @Body() query: FetchCPSRDetailDto, @I18n() i18n: I18nContext) {
    const { items } = await this.reportService.getCPSRDetail({ currentUser, query, isExport: true, i18n });
    const xlsxData = items.map((item) => {
      return {
        [i18n.translate(`importExport.visitDate`)]: moment(item.visitedDate).tz(process.env.TZ).format('DD/MM/YYYY'),
        [i18n.translate(`importExport.startTime`)]: item.status === 'success' ? moment(item.visitedDate).tz(process.env.TZ).format('hh:mm A') : '',
        [i18n.translate(`importExport.plannedDate`)]: moment(item.plannedDate).tz(process.env.TZ).format('DD/MM/YYYY'),
        [i18n.translate(`importExport.ucc`)]: item.ucc,
        [i18n.translate(`importExport.outletName`)]: item.outletName,
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.salesRepId`)]: item.salesRepId,
        [i18n.translate(`importExport.salesRepName`)]: item.salesRepName,
        [i18n.translate(`importExport.status`)]: item.status,
        [i18n.translate(`importExport.reason`)]: item.reason,
      };
    });
    const fileName = `DSRReportCPSRDetail`;
    const result = await this._filesService.exportXLSXFile(fileName, xlsxData, 'DSR Detail', currentUser, { wch: 20 });
    return new ApiResponse(result);
  }

  @Post('dsr/export')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Export DSR report',
  })
  async exportDSRReportToExcelFile(@Body() fetchDsrReportDto: FetchDsrReportDto, @CurrentUser() currentUser: any, @I18n() i18n: I18nContext) {
    const { month, year, sort, distributorIds } = fetchDsrReportDto;

    const validDistributorIds = [];
    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      for (const distributorId of distributorIds) {
        const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
        if (distributor) {
          validDistributorIds.push(distributor.distributor.distributorId);
        }
      }
    } else {
      await Promise.all(
        distributorIds.map(async (distributorId) => {
          const distributor = await this._distributorService.findOne({ distributorId });
          if (distributor) {
            validDistributorIds.push(distributor.distributorId);
          }
        }),
      );
      if (!validDistributorIds.length) {
        throw new BadRequestException('report.dsr.not_found_distributor');
      }
    }

    const diffMonths = this._dsrTargetService.getDiffMonth(month, year);
    if (diffMonths > 0) {
      throw new BadRequestException('report.dsr.month.invalid');
    }
    const statisticDay = this._saleRepStatisticService.getStatisticDayByDiffMonths(diffMonths);
    let rawDataReport = [];
    if (validDistributorIds && validDistributorIds.length) {
      if (statisticDay) {
        if (process.env.OPCO == OpCos.Indonesia) {
          rawDataReport = await this._saleRepStatisticService.getDSRReportID(validDistributorIds, statisticDay, sort);
        } else {
          rawDataReport = await this._saleRepStatisticService.getDSRReport(validDistributorIds, statisticDay, sort);
        }
      } else {
        rawDataReport = await this._saleRepStatisticService.getDefaultDSRReport(validDistributorIds, sort);
      }
    }
    const xlsxData = rawDataReport.map((item) => {
      if ([OpCos.Indonesia].includes(process.env.OPCO)) {
        return {
          [i18n.translate(`importExport.distributorId`)]: item.distributorId,
          [i18n.translate(`importExport.distributorName`)]: item.distributorName,
          [i18n.translate(`importExport.salesRepId`)]: item.saleRepId,
          [i18n.translate(`importExport.salesRepName`)]: item.name,
          [i18n.translate(`importExport.cpsr_ID`)]: !item.cpsrTarget ? '-' : Math.round((item.cpsr / item.cpsrTarget) * 100),
          [i18n.translate(`importExport.cpsrValue_ID`)]: item.cpsr,
          [i18n.translate(`importExport.cpsrTarget_ID`)]: item.cpsrTarget,
          [i18n.translate(`importExport.salesAchievement_ID`)]: !item.salesTarget ? '-' : Math.round((item.sales / item.salesTarget) * 100),
          [i18n.translate(`importExport.salesActual_ID`)]: item.sales,
          [i18n.translate(`importExport.salesTarget_ID`)]: item.salesTarget,
          [i18n.translate(`importExport.mabo_ID`)]: !item.maboTarget ? '-' : Math.round((item.mabo / item.maboTarget) * 100),
          [i18n.translate(`importExport.maboValue_ID`)]: item.mabo,
          [i18n.translate(`importExport.maboTarget_ID`)]: item.maboTarget,
          [i18n.translate(`importExport.strikeRateAchievement`)]: !item.totalVisitedOutlets ? '-' : Math.round((item.totalStrikeRate / item.totalVisitedOutlets) * 100),
          [i18n.translate(`importExport.strikeRateActual`)]: item.totalStrikeRate,
          [i18n.translate(`importExport.strikeRateTarget`)]: item.totalVisitedOutlets,
        };
      }
      return {
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.salesRepId`)]: item.saleRepId,
        [i18n.translate(`importExport.salesRepName`)]: item.name,
        [i18n.translate(`importExport.cpsr`)]: !item.cpsrTarget ? '-' : Math.round((item.cpsr / item.cpsrTarget) * 100),
        [i18n.translate(`importExport.cpsrValue`)]: item.cpsr,
        [i18n.translate(`importExport.cpsrTarget`)]: item.cpsrTarget,
        [i18n.translate(`importExport.salesAchievement`)]: !item.salesTarget ? '-' : Math.round((item.sales / item.salesTarget) * 100),
        [i18n.translate(`importExport.salesActual`)]: item.sales,
        [i18n.translate(`importExport.salesTarget`)]: item.salesTarget,
        [i18n.translate(`importExport.mabo`)]: 0,
        [i18n.translate(`importExport.maboValue`)]: 0,
        [i18n.translate(`importExport.maboTarget`)]: 0,
      };
    });
    const fileName = `DSRReport`;
    const result = await this._filesService.exportXLSXFile(fileName, xlsxData, 'DSR Report', currentUser, { wch: 20 });
    return new ApiResponse(result);
  }

  @Post('dsr-notes')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async getDsrNote(@Body() body: FetchDSRNotesDto) {
    const { salesRepObjectIds, search, skip, limit } = body;
    const dsrNotes = await this.reportService.getDsrNote({ salesRepObjectIds, search, skip, limit });
    return new ApiResponse(dsrNotes);
  }

  @Post('export-dsr-notes')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async exportDsrNote(@Body() body: FetchDSRNotesDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: any) {
    const { salesRepObjectIds, search } = body;
    const dsrNotes = await this.reportService.exportDsrNote({ salesRepObjectIds, search });

    const xlsxData = dsrNotes?.map((item) => {
      return {
        [i18n.translate(`importExport.ucc`)]: item.outlet.ucc,
        [i18n.translate(`importExport.salesRepId`)]: item.salesRepInfo.saleRepId,
        [i18n.translate(`importExport.note`)]: item.note,
        [i18n.translate(`importExport.lastUpdatedTime`)]: moment(item.noteUpdatedAt).format('DD/MM/YYYY HH:mm'),
      };
    });

    const fileName = `DSR_Notes`;
    const file = await this._filesService.exportXLSXFile(fileName, xlsxData, 'DSR_Notes', currentUser, { wch: 20 });
    return new ApiResponse(file);
  }

  @Post('nnd-report')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async getNNDReport(@Body() body: FetchNNDDto) {
    const res = await this.reportService.getNNDReport(body);
    return new ApiResponse(res);
  }

  @Post('export-nnd-report')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async exportNNDReport(@Body() body: FetchNNDDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: any) {
    const res = await this.reportService.exportNNDReport(body);
    const xlsxData = res?.map((item) => {
      const common = {
        [i18n.translate(`importExport.visitedDate`)]: moment(item.visitedDay).format('DD/MM/YYYY HH:mm'),
        [i18n.translate(`importExport.plannedDate`)]: moment(item.plannedDate).format('DD/MM/YYYY HH:mm'),
        [i18n.translate(`importExport.ucc`)]: item.ucc,
        [i18n.translate(`importExport.outletName`)]: item.outletName,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.salesRepName`)]: item.salesRepName,
        [i18n.translate(`importExport.salesRepId`)]: item.salesRepId,
      };
      if (item.salerepexecutionavailabilities) {
        for (const iterator of item.salerepexecutionavailabilities) {
          const listText = [];
          for (const key in iterator) {
            if (key !== 'name' && iterator[key]) {
              listText.push(i18n.translate(`importExport.${key.replace('has', '').toLowerCase()}`));
            }
          }
          common[iterator.name] = listText.join(', ');
        }
      }
      return common;
    });
    const fileName = `DSR_NND`;
    const file = await this._filesService.exportXLSXFile(fileName, xlsxData, 'DSR_NND', currentUser, { wch: 20 });
    return new ApiResponse(file);
  }

  @Post('forward-stock-report')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async getForwardStockReport(@Body() body: FetchForwardStockDto) {
    const res = await this.reportService.getForwardStockReport(body);
    return new ApiResponse(res);
  }

  @Post('export-forward-stock-report')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async exportForwardStockReport(@Body() body: FetchForwardStockDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: any) {
    const res = await this.reportService.exportForwardStockReport(body);
    const xlsxData = res?.map((item) => {
      const common = {
        [i18n.translate(`importExport.visitedDate`)]: moment(item.visitedDay).format('DD/MM/YYYY HH:mm'),
        [i18n.translate(`importExport.plannedDate`)]: moment(item.plannedDate).format('DD/MM/YYYY HH:mm'),
        [i18n.translate(`importExport.ucc`)]: item.ucc,
        [i18n.translate(`importExport.outletName`)]: item.outletName,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.salesRepName`)]: item.salesRepName,
        [i18n.translate(`importExport.salesRepId`)]: item.salesRepId,
        [i18n.translate(`importExport.forwardStocks`)]: item?.forwardStock,
      };
      return common;
    });
    const fileName = `DSR_Forward_Stock`;
    const file = await this._filesService.exportXLSXFile(fileName, xlsxData, 'DSR_Forward_Stock', currentUser, { wch: 20 });
    return new ApiResponse(file);
  }
}
