import { CacheModule, Module, OnModuleInit, ValidationPipe } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { OutletsModule } from './outlets/outlets.module';
import { VisitsModule } from './visits/visits.module';
import { InteractionsModule } from './interactions/interactions.module';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { HeaderResolver, I18nModule } from 'nestjs-i18n';
import { ExternalModule } from './external/external.module';
import * as path from 'path';
import { join } from 'path';
import { APP_FILTER, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HttpModule } from '@nestjs/axios';

import { AppInterceptor } from './shared/interceptor/app.interceptor';
import { HttpExceptionFilter } from './shared/response/http-exception.filter';
import { AuthModule } from './auth/auth.module';
import { OrdersModule } from './orders/orders.module';
import { SharedListener } from './shared/events/listener/shared.listener';
import { JourneyPlanningsModule } from './journey-plannings/journey-plannings.module';
import { SaleRepModule } from './sale-rep/sale-rep.module';
import { JobModule } from './job/job.module';
import { MessageModule } from './message/message.module';
import { WinstonModule } from 'nest-winston';
import { SettingsModule } from './settings/settings.module';
import { ReportModule } from './report/report.module';
import { AdminModule } from './admin/admin.module';
import { FilesModule } from './files/files.module';
import * as winston from 'winston';
import * as redisStore from 'cache-manager-redis-store';
import { ServeStaticModule } from '@nestjs/serve-static';
import { BrandModule } from './brand/brand.module';
import { DistributorModule } from './distributor/distributor.module';
import { DsrTargetsModule } from './dsr-targets/dsr-targets.module';
import { AIService } from './shared/application-insights/appinsight.service';
import { SendGridService } from './shared/mail/sendgrid';
import { ThirdPartiesModule } from './third-parties/third-parties.module';
import 'multer';
import { AbsencesModule } from './absences/absences.module';
import { MissReasonsModule } from './miss-reasons/miss-reasons.module';
import { OfflineModeV2Module } from './offline-mode-v2/offline-mode-v2.module';
import { OmsModule } from './oms/oms.module';
import { ScriptsModule } from './scripts/scripts.module';
import * as moment from 'moment-timezone';
import { CustomerFlowModule } from './customer-flow/customer-flow.module';
import { CacheControlInterceptor } from './shared/interceptor/cache-control.interceptor';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    ConfigModule.forRoot({ isGlobal: true }),
    MongooseModule.forRootAsync({
      useFactory: () => ({
        uri: process.env.MONGODB_URL,
        dbName: process.env.MONGO_DB_NAME,
        user: process.env.MONGO_DB_USER,
        pass: process.env.MONGO_DB_PASS,
        maxPoolSize: Number(process.env.MONGO_POOL_SIZE || 25),
        serverSelectionTimeoutMS: 60000,
        socketTimeoutMS: 300000,
      }),
    }),
    I18nModule.forRoot({
      fallbackLanguage: process.env.DEFAULT_LANGUAGE || 'en',
      loaderOptions: {
        path: path.join(__dirname, '/i18n/'),
        watch: true,
      },
      resolvers: [new HeaderResolver(['locale'])],
    }),
    WinstonModule.forRoot({
      transports: [
        new winston.transports.File({
          filename: `public/logs/error-${new Date().getMonth() + 1}-${new Date().getFullYear()}.log`,
          level: 'error',
        }),
        new winston.transports.File({
          filename: `public/logs/debug-${new Date().getMonth() + 1}-${new Date().getFullYear()}.log`,
          level: 'debug',
        }),
        new winston.transports.File({
          filename: `public/logs/info-${new Date().getMonth() + 1}-${new Date().getFullYear()}.log`,
          level: 'info',
        }),
      ],
    }),
    CacheModule.register({
      isGlobal: true,
      store: redisStore,
      host: process.env.REDIS_HOST,
      port: process.env.REDIS_PORT,
      auth_pass: process.env.REDIS_PASSWORD,
      password: process.env.REDIS_PASSWORD,
      tls:
        process.env.REDIS_TLS === 'true'
          ? {
              host: process.env.REDIS_HOST,
            }
          : false,
      ttl: Number(process.env.REDIS_TTL),
      max: 10000,
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
    }),
    HttpModule,
    EventEmitterModule.forRoot(),
    UsersModule,
    OutletsModule,
    JourneyPlanningsModule,
    VisitsModule,
    InteractionsModule,
    ExternalModule,
    AuthModule,
    OrdersModule,
    SaleRepModule,
    JobModule,
    MessageModule,
    SettingsModule,
    ReportModule,
    AdminModule,
    FilesModule,
    BrandModule,
    DistributorModule,
    DsrTargetsModule,
    ThirdPartiesModule,
    AbsencesModule,
    MissReasonsModule,
    OfflineModeV2Module,
    OmsModule,
    ScriptsModule,
    CustomerFlowModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: AppInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: CacheControlInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_PIPE,
      useValue: new ValidationPipe({}),
    },
    AppService,
    SharedListener,
    AIService,
    SendGridService,
  ],
})
export class AppModule implements OnModuleInit {
  constructor() {}
  onModuleInit() {
    moment.tz.setDefault(process.env.TZ);
  }
}
