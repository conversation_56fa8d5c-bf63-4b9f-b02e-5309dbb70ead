import { StorePagesInformationMessagesModel } from './store-pages-information-messages.model';
import { CurrencyFormat } from './currency-format';

export interface SettingsStoreModel {
  inactiveSessionTimeout: number;
  currencyFormat: CurrencyFormat;
  loyaltyCurrencyFormat: CurrencyFormat;

  enableCategoryFiltersInCatalog: boolean;

  hideSubtotal: boolean;
  hideTax: boolean;
  hideEmptiesDeposit: boolean;
  hideTotal: boolean;
  hideCartDiscountTotal: boolean;
  hideCartExtendedPriceTotal: boolean;
  showCouponDiscount: boolean;
  showCartTaxLabel: boolean;
  hideOrderTotal: boolean;
  hideOrderSubtotal: boolean;
  hideOrderDiscountTotal: boolean;
  hideOrderTaxTotal: boolean;
  hideOrderEmptiesDepositTotal: boolean;
  hideOrderExtendedPriceTotal: boolean;
  showOrderCouponDiscount: boolean;
  showOrderTaxLabel: boolean;
  showOnlyInvoiceTotals: boolean;
  hideInvoiceTaxTotal: boolean;
  hideInvoiceEmptiesReturned: boolean;
  hideInvoiceTotalNetAmount: boolean;
  hideInvoiceTotalAmount: boolean;
  hideInvoiceTotalDiscount: boolean;
  hideInvoiceTotalPrice: boolean;
  hideInvoiceTotalSurcharge: boolean;

  hideEstimatedDeliveryDateOnCheckoutPage: boolean;
  hidePreferredDeliveryDateOnCheckoutPage: boolean;
  hideAddressOnCheckoutPage: boolean;
  hidePurchaseOrderNumberOnCheckoutPage: boolean;
  hideCommentOnCheckoutPage: boolean;

  hideCustomerOrderNumberOnThankYouPage: boolean;
  hideEstimatedDeliveryDateOnThankYouPage: boolean;
  hidePreferredDeliveryDateOnThankYouPage: boolean;
  hideDistributorNameOnThankYouPage: boolean;
  hideOutletNameOnThankYouPage: boolean;
  hideAddressOnThankYouPage: boolean;
  hidePurchaseOrderNumberOnThankYouPage: boolean;
  hideCommentOnThankYouPage: boolean;
  showCancelOrderButtonOnThankYouPage: boolean;
  showCartDisclaimer: boolean;
  showCancellationInformation: boolean;
  orderCancellationInterval: number;

  showPlusMinusButtonsForQuantityInput: boolean;
  showClearCartButtonOnCartModal: boolean;

  multipleNotificationsAutoReadInterval: number;
  singleNotificationAutoReadInterval: number;

  showEmptiesDepositInformationNote: boolean;
  showProductExtendedPrice: boolean;

  showAcceptDeliveryWarning: boolean;
  showSubtotalInOrderList: boolean;
  orderTotalColumnSourceProperty: string;

  distributorDeliveryIntervalDays: number;
  offlineOrderSmsPhoneNumber: string;
  offlineOrderMessagePrefix: string;

  purchaseOrderNumberRequired: boolean;
  purchaseOrderNumberMaximumLength: number;

  displayLogoutAsText: boolean;
  showLineItemsQuantitySum: boolean;
  orderCommentMaximumLength: number;
  paymentMethodSelectionEnabled: boolean;
  paymentMethods: string[];
  orderStatusesAvailableForDeliveryApprove: string[];
  pagesInformationMessages: StorePagesInformationMessagesModel;
  isTodayDelivery: boolean;
  isOwnedByHeineken: boolean;
  showCreditLimit: boolean;
  showNewOrdersOnDistributorPortal: boolean;

  preferredDeliveryWeekStartsAfterDaysOnCheckoutPage: number;
  preferredDeliveryDateAsWeekOnCheckoutPage: boolean;
  preferredDeliveryWeekStartsAfterWeeksOnCheckoutPage: number;
  preferredDeliveryDateIsRequired: boolean;

  taxRate: number;

  acceptTermsAndConditionsWhenPlacingAnOrder: boolean;

  distributorPortalExportOrdersToExcel?: boolean;
  distributorPortalSelectDeliveryDate?: boolean;
  distributorPortalOrderStatusesForDisplayingDeliveryDate: string[];
  showPayNowOnHomePage?: boolean;
  distributorPortalCustomerPhoneNumberSource: string;
  partialPaymentsEnabled?: boolean;
  partialPaymentsMinimumAmount?: number;
  dlocalMerchantAPI?: string;
  allowUnfinishedPaymentsProcessing?: boolean;
  showSkuInProductList?: boolean;
  calculatedPackageValueSource?: string;
  checkProductsAvailability?: boolean;

  exportSingleOrder?: boolean;
  showOfflineOrdersFilter?: boolean;

  distributorConfirmStatus?: string;

  productPromotionDiscountThreshold?: number;
  showPackageHintForProductQuantity?: boolean;
  showOutletOuterIdOnCustomerProfilePage?: boolean;

  productsPerPage: number;

  enableGridViewInCatalog?: boolean;
  emptiesDepositTaxed?: boolean;
  hideTotalSavings?: boolean;
  hideStrikethroughPrice?: boolean;
  showUnitPriceOnlyInGridView?: boolean;
  hidePayNowButton?: boolean;
  orderRatingStatus?: string;
  showProductPriceWithoutEmptiesDeposit?: boolean;
  addReasonsByCustomerWhenCancelOrder?: boolean;
  invalidateBrowserCache?: boolean;
  cacheClearanceEffectiveTime?: string;
  totalInvoiceAmountAfterOrderIsDelivered?: boolean;

  restrictOrderPaymentsByChannel?: Array<string>;
  autoAddEmpties?: boolean;
  showSalesRepresentative?: boolean;
  showTotalOrdersStatistic?: boolean;
  cancelOrderByCustomerWhenOrderStatusIsSentToDistributor?: boolean;
  showSearchProductsBar?: boolean;
  productTypeFilterType?: string;
  canUnSelectBrandFilter?: boolean;

  newOrdersAgeThresholdForDistributorPortal?: string;
  allowEstimatedDeliveryDateOptional?: boolean;
  allowFilteringOrdersByFulfillmentCentersOnDistributorPortal?: boolean;

  displayOutOfStockProductsInCustomersCatalog?: boolean;
  displayIndicatorForOutOfStockProductsInCustomersCatalog?: boolean;
  showFAQWhenChatBotNotActivated?: boolean;
}
