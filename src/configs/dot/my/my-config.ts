import { MyConfigurationModel } from './my-configuration.model';

export const MyConfigs: MyConfigurationModel = {
  orderStatuses: [
    {
      key: 'saved',
      bgColor: 'rgb(254, 204, 2)',
      icon: 'saved-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'cancelled',
      bgColor: 'rgb(236, 28, 36)',
      icon: 'cancelled-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'blocked',
      bgColor: 'rgb(236, 28, 36)',
      icon: 'cancelled-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'sent-to-distributor',
      bgColor: 'rgb(0, 0, 0)',
      icon: 'placed-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'new-order',
      bgColor: 'rgb(0, 0, 0)',
      icon: 'placed-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'delivered',
      bgColor: 'rgb(12, 71, 157)',
      icon: 'delivered-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'pending',
      bgColor: 'rgb(141, 141, 141)',
      icon: 'pending-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'received',
      bgColor: 'rgb(141, 141, 141)',
      icon: 'pending-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'on-the-way',
      bgColor: 'rgb(248, 185, 102)',
      icon: 'on-the-way-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'delayed',
      bgColor: 'rgb(0, 166, 81)',
      icon: 'confirmed-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'confirmed',
      bgColor: 'rgb(0, 165, 81)',
      icon: 'confirmed-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'confirmed-delivery',
      bgColor: 'rgb(0, 165, 81)',
      icon: 'confirmed-icon',
      color: 'rgb(255, 255, 255)',
    },
    {
      key: 'sent-to-customer',
      bgColor: 'rgb(240, 90, 34)',
      icon: 'pending-icon',
      color: 'rgb(255, 255, 255)',
    },
  ],
  configCustomPromoCondition: false,
};
