import { BadRequestException, Body, Controller, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { I18n, I18nContext } from 'nestjs-i18n';

import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { ApiException } from '../shared/api-exception.model';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { Roles } from '../shared/decorator/roles.decorator';
import { RolesGuard } from '../shared/guards/roles.guard';
import { normalizeQueryHelper } from '../shared/helpers';
import { Serialize } from '../shared/interceptor/serialize.interceptor';
import { ApiResponse } from '../shared/response/api-response';
import { User } from '../users/schemas/user.schema';
import { slugify, validateFields } from '../utils';
import { ConstantRoles } from '../utils/constants/role';
import { CreateBrandDto, FetchedBrandDto, UpdateBrandDto } from './dtos';
import { CreateHintDto } from './dtos/create-hint.dto';
import { BrandService } from './services';
import { HintService } from './services/hint.service';
import { MigrateBrandDto } from './dtos/migrate-brand.dto';

@ApiTags('Brand')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/brand')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BrandController {
  constructor(private _brandService: BrandService, private _hintService: HintService) {}

  @Get('')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Get all brand',
  })
  async getAllBrand() {
    return new ApiResponse(await this._brandService.findAll({}, null, { sort: { updatedAt: 1 } }));
  }

  @Post('migrate-to-new-brand')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Create a brand',
  })
  async migrateNewBrand(@Body() body: MigrateBrandDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    const activeBrands = body['activeBrands'];
    if (activeBrands) {
      await this._brandService.migrateNewBrand(activeBrands);
      return new ApiResponse();
    }
    throw new BadRequestException();
  }

  @Post('')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Create a brand',
  })
  async createBrand(@Body() createBrandDto: CreateBrandDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    const { name } = createBrandDto;
    const normalizedQuery = normalizeQueryHelper(name);
    const existedBrand = await this._brandService.findOne({ name: { $regex: new RegExp(`^.*${normalizedQuery}.*$`, 'i') } });
    if (existedBrand) {
      throw new BadRequestException(await i18n.t('brand.existed_brand'));
    }
    const result = await this._brandService.create({
      isActive: true,
      name: name?.trim(),
      createdBy: new Types.ObjectId(currentUser._id),
    });
    return new ApiResponse(result);
  }

  @Put(':id')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Update a brand',
  })
  async updateBrand(@Param('id') id, @Body() dto: UpdateBrandDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    const result = await this._brandService.update(id, { ...dto });
    return new ApiResponse(result);
  }

  @Post('hint')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Create or Update a hint',
  })
  async createHint(@Body() createHintDto: CreateHintDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    const { channel, subChannel, hint } = createHintDto;
    await validateFields({ channel, subChannel, hint: hint?.trim() }, `common.required_field`, i18n);
    const existedHint = await this._hintService.findOne({ channel: new RegExp(channel, 'i'), subChannel: new RegExp(subChannel, 'i') });
    let result = existedHint;
    if (existedHint) {
      result = await this._hintService.update(existedHint._id, {
        hints: [{ name: hint?.trim(), code: slugify(hint?.trim()) }],
      });

      // const hints = existedHint.hints;
      // if (hints.findIndex((h) => h.name.toUpperCase() === hint?.toUpperCase()?.trim()) <= -1) {
      //   hints.push({ name: hint?.trim(), code: slugify(hint?.trim()) });
      //   result = await this._hintService.update(existedHint._id, {
      //     hints,
      //   });
      // }
    } else {
      result = await this._hintService.create({
        hints: [{ name: hint?.trim(), code: slugify(hint?.trim()) }],
        channel: channel.toLowerCase(),
        subChannel: subChannel.toLowerCase(),
      });
    }
    return new ApiResponse(result);
  }
}
