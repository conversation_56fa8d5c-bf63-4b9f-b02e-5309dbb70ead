import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { BrandService } from './services';
import { BrandController } from './brand.controller';
import { Brand, BrandSchema } from './schemas';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';
import { Hints, HintSchema } from './schemas/hint.schema';
import { HintService } from './services/hint.service';
import { OutletsModule } from '../outlets/outlets.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Brand.name,
        schema: BrandSchema,
      },
      {
        name: Hints.name,
        schema: HintSchema,
      },
    ]),
    AuthModule,
    forwardRef(() => UsersModule),
    forwardRef(() => OutletsModule),
  ],
  controllers: [BrandController],
  providers: [BrandService, HintService],
  exports: [BrandService, HintService],
})
export class BrandModule {}
