import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNotEmpty, IsString } from 'class-validator';

export class HintNameClass {
  name: string;
}

export class CreateHintDto {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  channel: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  subChannel: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  hint: string;
}
