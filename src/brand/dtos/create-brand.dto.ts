import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

export class CreateBrandDto {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string;
}

export class UpdateBrandDto {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string;

  @ApiModelProperty({ default: false })
  @IsNotEmpty()
  @IsBoolean()
  hasQt: boolean;

  @ApiModelProperty({ default: false })
  @IsNotEmpty()
  @IsBoolean()
  hasPt: boolean;

  @ApiModelProperty({ default: false })
  @IsNotEmpty()
  @IsBoolean()
  hasCan: boolean;

  @ApiModelProperty({ default: false })
  @IsNotEmpty()
  @IsBoolean()
  hasBcan: boolean;

  @ApiModelProperty({ default: false })
  @IsNotEmpty()
  @IsBoolean()
  hasKeg: boolean;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsBoolean()
  isActive: boolean;
}
