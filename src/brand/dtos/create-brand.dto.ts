import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

export class CreateBrandDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string;
}

export class UpdateBrandDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string;

  @ApiProperty({ default: false })
  @IsNotEmpty()
  @IsBoolean()
  hasQt: boolean;

  @ApiProperty({ default: false })
  @IsNotEmpty()
  @IsBoolean()
  hasPt: boolean;

  @ApiProperty({ default: false })
  @IsNotEmpty()
  @IsBoolean()
  hasCan: boolean;

  @ApiProperty({ default: false })
  @IsNotEmpty()
  @IsBoolean()
  hasBcan: boolean;

  @ApiProperty({ default: false })
  @IsNotEmpty()
  @IsBoolean()
  hasKeg: boolean;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isActive: boolean;
}
