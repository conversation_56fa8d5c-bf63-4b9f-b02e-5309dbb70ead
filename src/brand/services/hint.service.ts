import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Outlet } from 'src/outlets/schemas/outlet.schema';

import { OutletsService } from '../../outlets/services/outlets.service';
import { BaseService } from '../../shared/services/base-service';
import { HintDocument, Hints } from '../schemas/hint.schema';

@Injectable()
export class HintService extends BaseService<Hints> {
  constructor(
    @InjectModel(Hints.name)
    private readonly _model: Model<HintDocument>,
    private readonly _outletService: OutletsService,
  ) {
    super();
    this.model = _model;
  }

  async findAllHintsByChannelAndSubChannel(channel: string, subChannel: string): Promise<Hints> {
    return await this.findOne({ channel: channel?.toLowerCase(), subChannel: subChannel?.toLowerCase() });
  }

  async findAllHintsByOutlets(outlets: Outlet[]): Promise<{
    [key: string]: string;
  }> {
    const channelsAndSubChannels = outlets
      .map(({ channel, subChannel }) => ({ channel, subChannel }))
      .filter((item, index, self) => index === self.findIndex((el) => el.channel === item.channel && el.subChannel === item.subChannel));

    if (!channelsAndSubChannels.length) {
      return {};
    }

    const hints = await this._model.find({
      $or: channelsAndSubChannels.map(({ channel, subChannel }) => ({ channel: channel?.toLowerCase(), subChannel: subChannel?.toLowerCase() })),
    });

    return outlets.reduce((pre, { _id, channel, subChannel }) => {
      const hintObj = hints.find((item) => item.channel === channel?.toLowerCase() && item.subChannel === subChannel?.toLowerCase());
      if (!hintObj) {
        return pre;
      }
      return {
        ...pre,
        [_id]: hintObj?.hints?.map((h) => h.name)?.join('\n'),
      };
    }, {});
  }
}
