import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { BaseService } from '../../shared/services/base-service';
import { Brand, BrandDocument } from '../schemas';

@Injectable()
export class BrandService extends BaseService<Brand> {
  constructor(
    @InjectModel(Brand.name)
    private readonly _model: Model<BrandDocument>,
  ) {
    super();
    this.model = _model;
  }

  async migrateNewBrand(activeBrands: string[] = []) {
    await this.model.updateMany({ name: { $nin: activeBrands } }, { isActive: false });
    for (const iterator of activeBrands) {
      await this.model.updateOne(
        { name: iterator },
        {
          name: iterator,
          isActive: true,
        },
        { upsert: true },
      );
    }
  }
}
