import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';

export type BrandDocument = Brand & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class Brand extends BaseSchema {
  @Prop({ index: true })
  name: string;

  @Prop({ default: false })
  hasQt: boolean;

  @Prop({ default: false })
  hasPt: boolean;

  @Prop({ default: false })
  hasCan: boolean;

  @Prop({ default: false })
  hasBcan: boolean;

  @Prop({ default: false })
  hasKeg: boolean;

  @Prop({ type: Types.ObjectId, ref: User.name })
  createdBy: User;

  @Prop({ default: true })
  isActive: boolean;
}

export const BrandSchema = SchemaFactory.createForClass(Brand);
