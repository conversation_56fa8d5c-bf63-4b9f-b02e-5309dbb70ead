import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';

export type HintDocument = Hints & Document;

export class HintClass {
  name: string;
  code: string;
}
@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class Hints extends BaseSchema {
  @Prop({ index: true })
  channel: string;

  @Prop({ index: true })
  subChannel: string;

  @Prop([HintClass])
  hints: HintClass[];

  @Prop({ default: true, index: true })
  isActive: boolean;
}

export const HintSchema = SchemaFactory.createForClass(Hints);
HintSchema.index({ channel: 1, subChannel: 1, hint: 1 }, { unique: true });
