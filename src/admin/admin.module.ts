import { JourneyPlanningsModule } from './../journey-plannings/journey-plannings.module';
import { DistributorModule } from './../distributor/distributor.module';
import { OutletsModule } from './../outlets/outlets.module';
import { AuthModule } from './../auth/auth.module';
import { UsersModule } from './../users/users.module';
import { JwtModule } from '@nestjs/jwt';
import { Module, forwardRef } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminController } from './admin.controller';
import { FilesModule } from '../files/files.module';

@Module({
  imports: [
    JwtModule.registerAsync({
      useFactory: async () => ({
        secret: process.env.JWT_PRIVATE_KEY,
        signOptions: { expiresIn: process.env.JWT_EXPIRED_TIME },
      }),
    }),
    forwardRef(() => AuthModule),
    forwardRef(() => UsersModule),
    forwardRef(() => DistributorModule),
    forwardRef(() => OutletsModule),
    forwardRef(() => JourneyPlanningsModule),
    FilesModule,
  ],
  providers: [AdminService],
  controllers: [AdminController],
})
export class AdminModule {}
