import { DistributorUserRelationService } from './../distributor/services/distributor-user-relation.service';
import { ConstantPermissions, ConstantRoles } from './../utils/constants/role';
import { I18nContext } from 'nestjs-i18n';

import { User } from './../users/schemas/user-reset-passwords.schema';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { isEmptyObjectOrArray } from 'src/utils';
import { UserDetailService } from '../users/services/user-detail.service';

@Injectable()
export class AdminService {
  constructor(
    private readonly _userDetailService: UserDetailService,
    @Inject(forwardRef(() => DistributorUserRelationService))
    private readonly _distributorUserRelationService: DistributorUserRelationService,
  ) {}
  async validatePermissionUser(currentUser: User, roleParma: string[], rule: string, distributorIds: string[], i18n: I18nContext) {
    try {
      const findUserRole = await this._userDetailService.findUserRoles({ userId: currentUser._id.toString(), isUserAdmin: true });
      const roleOfUser = findUserRole?.map((r) => r.roleKey);

      if (!isEmptyObjectOrArray(roleOfUser)) {
        switch (true) {
          //with role is supper admin => access view all list, create, edit all role
          case roleOfUser.includes(ConstantRoles.SUPER_USER):
            return true;
          /*
          - with case MDS => access view own record, edit own record but not re-assign role
              + create, edit distributor admin but not re-assign role
           */
          case roleOfUser.includes(ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR): {
            if (rule === ConstantPermissions.CHANGE_MY_SELF) {
              return !!roleParma.every((el) => el === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR);
            } else {
              return !!roleParma.every((el) => el === ConstantRoles.DISTRIBUTOR_ADMIN);
            }
          }
          /* with case role is distributor admin:
            + Don't access view list user
            + Don't access create/edit another user
            + Access view/edit own user but not re-assign role, distributorId, distributorName
          */
          case roleOfUser.includes(ConstantRoles.DISTRIBUTOR_ADMIN): {
            if (rule === ConstantPermissions.CHANGE_MY_SELF) {
              const [existedDistributorUserRelation] = await this._distributorUserRelationService.getDistributorUserRelation({ 'ua._id': currentUser?._id });
              return !!(roleParma.every((el) => el === ConstantRoles.DISTRIBUTOR_ADMIN) && distributorIds.includes(existedDistributorUserRelation.dis?.distributorId));
            }
          }
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }
}
