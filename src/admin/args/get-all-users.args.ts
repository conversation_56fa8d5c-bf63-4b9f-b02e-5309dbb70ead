import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsOptional, IsString } from 'class-validator';

export class GetAllUsersArgs {
  // search by email or mobilePhone or username
  @ApiModelPropertyOptional()
  @IsString()
  @IsOptional()
  search?: string;

  @ApiModelPropertyOptional()
  @IsString()
  @IsOptional()
  distributorId?: string;
}
