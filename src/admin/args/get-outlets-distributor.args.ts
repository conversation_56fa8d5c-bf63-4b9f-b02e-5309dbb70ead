import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsOptional, IsString } from 'class-validator';

export class GetOutletsDistributorArgs {
  // search by ucc or name
  @ApiModelPropertyOptional()
  @IsString()
  @IsOptional()
  search?: string;

  @ApiModelPropertyOptional()
  @IsString()
  @IsOptional()
  status?: string;

  @ApiModelPropertyOptional()
  @IsString()
  @IsOptional()
  distributorId?: string;
}
