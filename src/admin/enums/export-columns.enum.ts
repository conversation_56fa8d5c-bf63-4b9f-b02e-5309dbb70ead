export const ImportExportCustomerColumnsID = ['ucc', 'outletArea', 'contactName', 'outletClass'];
export const ImportExportCustomerColumns = [
  'ucc',
  'outletName',
  'outletClass',
  'outletArea',
  'outletAddress',
  'contactName',
  'mobilePhoneCode',
  'phoneNumber',
  'salesRepId',
  'status',
  'channel',
  'subChannel',
  'distributorId',
  'distributorName',
];

export const ImportExportSalesRepColumnsID = ['dotUsername', 'salesRepId'];
export const ImportExportSalesRepColumnsKH = ['name', 'mobilePhoneCode', 'phoneNumber', 'dotUsername', 'salesRepId', 'status', 'distributorId', 'distributorName', 'geoAddress'];
export const ImportExportSalesRepColumns = ['name', 'mobilePhoneCode', 'phoneNumber', 'dotUsername', 'salesRepId', 'status', 'distributorId', 'distributorName'];

export const requiredFieldsImportCustomer = ['ucc', 'outletName', 'outletClass', 'outletArea', 'outletAddress', 'salesRepId'];
export const requiredFieldsImportCustomerID = ['ucc', 'outletArea'];

export const ImportExportVisibilityExecutionColumns = ['taskName', 'taskSubHeading', 'status', 'startDate', 'endDate', 'ucc'];

export const ImportOmsTargetSettingsColumns = [
  'targetSetting.salesRepId',
  'targetSetting.month',
  'targetSetting.year',
  'targetSetting.callComplianceRate',
  'targetSetting.callEffectiveness',
  'targetSetting.salesVolume',
  'targetSetting.salesValue',
  'targetSetting.activeSellingOutlet',
  'targetSetting.availability',
  'targetSetting.visibility',
];

export const ImportOmsPlansColumns = ['plansOutletUcc', 'plansSalesRepId', 'plansWeek', 'plansDay', 'plansDistributorId'];
