import { Body, CACHE_MANAGER, Controller, Get, HttpCode, HttpException, HttpStatus, Inject, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiTags } from '@nestjs/swagger';
import { parsePhoneNumber } from 'libphonenumber-js';
import { Types } from 'mongoose';
import { I18n, I18nContext } from 'nestjs-i18n';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { AddUserRoleDto } from '../auth/dto/add-user-role.dto';
import { AddUserDto } from '../auth/dto/add-user.dto';
import { FilesService } from '../files/services';
import { ApiException } from '../shared/api-exception.model';
import { Permissions } from '../shared/decorator/permissions.decorator';
import { PermissionsGuard } from '../shared/guards/permissions.guard';
import { RolesGuard } from '../shared/guards/roles.guard';
import { ApiResponse } from '../shared/response/api-response';
import { UserAdminsService } from '../users/services/user-admins.service';
import { clearCurrentUserTokenCache, isEmptyObjectOrArray, isPhoneNumberValidation, printLog, standardPhoneNumber, toListResponse, validateFields } from '../utils';
import { ConstantPermissions, ConstantRoles } from '../utils/constants/role';
import { AddOrUpdateCustomerMasterDto } from './../auth/dto/update-customer-master.dto';
import { DistributorUserRelationService } from './../distributor/services/distributor-user-relation.service';
import { DistributorService } from './../distributor/services/distributor.service';
import { OrderParams } from './../orders/dtos/order-filter.dto';
import { OutletsService } from './../outlets/services/outlets.service';
import { SaleRepOutletRelationService } from './../outlets/services/sale-rep-outlet-relation.service';
import { PaginationParams } from './../shared/common-params/pagination.params';
import { CurrentUser } from './../shared/decorator/current-user.decorator';
import { User } from './../users/schemas/user-reset-passwords.schema';
import { ConstantUser } from './../utils/constants/user';
import { AdminService } from './admin.service';
import { GetAllUsersArgs } from './args/get-all-users.args';
import { GetOutletsDistributorArgs } from './args/get-outlets-distributor.args';
import { UserDetailService } from '../users/services/user-detail.service';
import { RoleData } from '../users/enums';
import { UsersService } from '../users/services/users.service';
import { Cache } from 'cache-manager';

@ApiTags('Admin')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/admin')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class AdminController {
  constructor(
    private readonly _adminService: AdminService,
    private readonly _userService: UsersService,
    private readonly _userAdminService: UserAdminsService,
    private readonly _distributorService: DistributorService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly _outletService: OutletsService,
    private readonly _filesService: FilesService,
    private readonly _userDetailService: UserDetailService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  @Post('add-user')
  @ApiBearerAuth()
  @Roles(ConstantRoles.SUPER_USER)
  @Permissions(ConstantPermissions.CHANGE_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async addUser(@Body() userDto: AddUserDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    try {
      const { username, email, firstName, lastName, isActive, roleKey, distributorIds } = userDto;
      let { mobilePhone, mobilePhoneCode } = userDto;
      await validateFields({ mobilePhone, mobilePhoneCode, email, username, roleKey }, `common.required_field`, i18n);

      if (!isPhoneNumberValidation(mobilePhone, mobilePhoneCode)) {
        throw new HttpException(await i18n.translate(`user.phone_invalid_field`), HttpStatus.BAD_REQUEST);
      }
      const mobilePhoneParse = parsePhoneNumber(standardPhoneNumber(mobilePhone, mobilePhoneCode));
      mobilePhone = mobilePhoneParse.number;
      mobilePhoneCode = `+${mobilePhoneParse.countryCallingCode}`;

      //Check Email
      const userExistedEmail = await this._userAdminService.findOne({ email });
      if (userExistedEmail && userExistedEmail?._id) {
        throw new HttpException(await i18n.translate('message.existed_email'), HttpStatus.CONFLICT);
      }

      const getAllRole = RoleData;
      const allRoleKeyExist = getAllRole.map((el) => el?.roleKey?.toString().toLocaleUpperCase());
      const isRoleKeyInAllRole = roleKey.every((el) => allRoleKeyExist.includes(el?.toString().toLocaleUpperCase()));
      await validateFields({ roleKey: isRoleKeyInAllRole }, `common.required_field`, i18n);

      const userExistedPhone = await this._userAdminService.findOne({ mobilePhone });
      //Check user roles by phone
      if (userExistedPhone) {
        let roles: any = [];
        const userRoles = await this._userDetailService.findUserRoles({ userId: userExistedPhone._id.toString(), isUserAdmin: true });
        if (!isEmptyObjectOrArray(userRoles)) {
          roles = userRoles.map((r) => r.roleKey);
        } else {
          roles.push(ConstantRoles.SALE_REP);
        }
        if (roles.includes(ConstantRoles.SALE_REP)) {
          throw new HttpException(await i18n.translate('message.existed_phone_number'), HttpStatus.CONFLICT);
        }
      }

      if (roleKey.includes(ConstantRoles.DISTRIBUTOR_ADMIN)) {
        await validateFields({ distributorIds }, `common.required_field`, i18n);
      }

      //check permission of user
      const isValidatePermission = await this._adminService.validatePermissionUser(currentUser, roleKey, ConstantPermissions.CHANGE_USER, distributorIds, i18n);

      if (!isValidatePermission) {
        throw new HttpException(await i18n.t('user.permission_denied'), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
      }

      const userInstance: any = {
        mobilePhone,
        mobilePhoneCode,
        username,
        email,
        isActive,
        firstname: firstName,
        lastname: lastName,
        roleId: roleKey,
        status: ConstantUser.IS_ACTIVE,
        // password: await passwordGenerate(ConstantUser.DEFAULT_PASSWORD.toString()),
      };

      const user = await this._userAdminService.create(userInstance);
      if (user?.isActive === true) {
        await this._userAdminService.sendCreatePasswordAdmin(
          {
            mobilePhone,
            userAdmin: user,
          },
          i18n,
        );
      }
      const userRoleServiceInstance = {
        userId: user._id,
        roleKey: roleKey,
        user: user._id,
      };
      await this._userDetailService.addOrUpdate({
        userId: user._id,
        mobilePhone: user.mobilePhone,
        userRole: roleKey?.map((r) => ({ roleKey: r })) || [],
        isUserAdmin: true,
      });

      if (roleKey.includes(ConstantRoles.DISTRIBUTOR_ADMIN)) {
        await Promise.all(
          distributorIds.map(async (distributorId) => {
            const findDistributor = await this._distributorService.findOne({ distributorId });
            if (findDistributor) {
              await this._distributorUserRelationService.create({
                userAdmin: user._id,
                distributor: findDistributor?._id,
              });
            }
          }),
        );
      }

      return new ApiResponse(user);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('add-user-role')
  @ApiBearerAuth()
  @Roles(ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async addUserRole(@Body() param: AddUserRoleDto, @I18n() i18n: I18nContext) {
    try {
      const { roleKey, userId } = param;
      await validateFields({ roleKey, userId }, `common.required_field`, i18n);

      const findUser = await this._userAdminService.findById(userId);
      if (isEmptyObjectOrArray(findUser)) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'userId' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const findKeyRole = roleKey.some((r) => RoleData.some((role) => role.roleKey === r));

      if (isEmptyObjectOrArray(findKeyRole)) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'key role' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const findUserInUserRole = await this._userDetailService.findUserRoles({ userId: findUser._id.toString(), isUserAdmin: true });
      const result = await this._userDetailService.addOrUpdate({
        userId: findUser._id.toString(),
        isUserAdmin: true,
        userRole: [...(findUserInUserRole || []), ...(roleKey?.map((r) => ({ roleKey: r })) || [])],
      });
      return new ApiResponse(result);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('remove-user-role')
  @ApiBearerAuth()
  @Roles(ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async removeRole(@Body() param: AddUserRoleDto, @I18n() i18n: I18nContext) {
    try {
      const { roleKey, userId } = param;
      await validateFields({ roleKey, userId }, `common.required_field`, i18n);
      const findUser = await this._userAdminService.findById(userId);
      if (isEmptyObjectOrArray(findUser)) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'userId' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const findUserInUserRole = await this._userDetailService.findUserRoles({ userId: findUser._id.toString(), isUserAdmin: true });
      if (isEmptyObjectOrArray(findUserInUserRole)) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'key role' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }

      const roles = findUserInUserRole.filter((o1) => !roleKey.some((o2) => o1.roleKey === o2));
      const result = await this._userDetailService.addOrUpdate({
        userId: findUser._id,
        mobilePhone: findUser.mobilePhone,
        isUserAdmin: true,
        userRole: roles || [],
      });
      return new ApiResponse(result);
    } catch (e) {
      printLog('error', e);
      throw new HttpException(e, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('update-user')
  @ApiBearerAuth()
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async updateUser(@Body() userDto: AddUserDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    try {
      let { mobilePhone, mobilePhoneCode } = userDto;
      const { userId, username, email, firstName, lastName, roleKey: roleKeyParam, isActive, roleId, distributorIds } = userDto;
      await validateFields({ userId, mobilePhone, mobilePhoneCode, username, email }, `common.required_field`, i18n);
      let roleKey = roleKeyParam;

      if (!isPhoneNumberValidation(mobilePhone, mobilePhoneCode)) {
        throw new HttpException(await i18n.translate(`user.phone_invalid_field`), HttpStatus.BAD_REQUEST);
      }
      const mobilePhoneParse = parsePhoneNumber(standardPhoneNumber(mobilePhone, mobilePhoneCode));
      mobilePhone = mobilePhoneParse.number;
      mobilePhoneCode = `+${mobilePhoneParse.countryCallingCode}`;

      if (!roleKey?.length && !roleId?.length) {
        throw new HttpException(
          await i18n.translate(`common.required_field`, {
            args: { fieldName: 'role' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      if (roleId?.length && !roleKey?.length) {
        roleKey = roleId;
      }
      await validateFields({ roleKey }, `common.required_field`, i18n);

      //check permission of user
      let rule = ConstantPermissions.CHANGE_USER;

      if (currentUser?._id?.toString() === userId) {
        rule = ConstantPermissions.CHANGE_MY_SELF;
      }

      const isValidatePermission = await this._adminService.validatePermissionUser(currentUser, roleKey, rule, distributorIds, i18n);

      if (!isValidatePermission) {
        throw new HttpException(await i18n.t('user.permission_denied'), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
      }

      //User need to update
      let user = await this._userAdminService.findOne({ _id: userId });

      //Check user roles by phone
      const userExistedPhone = await this._userAdminService.findOne({ mobilePhone });
      if (userExistedPhone && user._id.toString() != userExistedPhone?._id.toString()) {
        let roles: any = [];
        const userRoles = await this._userDetailService.findUserRoles({ userId: userExistedPhone._id });
        if (!isEmptyObjectOrArray(userRoles)) {
          roles = userRoles?.map((r) => r.roleKey);
        } else {
          roles.push(ConstantRoles.SALE_REP);
        }
        if (roles.includes(ConstantRoles.SALE_REP)) {
          throw new HttpException(await i18n.translate('message.existed_phone_number'), HttpStatus.CONFLICT);
        }
      }

      //Check Email
      const userExistedEmail = await this._userAdminService.findOne({ email });
      if (userExistedEmail && user?._id?.toString() != userExistedEmail?._id?.toString()) {
        throw new HttpException(await i18n.translate('message.existed_email'), HttpStatus.CONFLICT);
      }

      const getAllRole = RoleData;
      const allRoleKeyExist = getAllRole.map((el) => el?.roleKey?.toString().toLocaleUpperCase());
      const isRoleKeyInAllRole = roleKey.every((el) => allRoleKeyExist.includes(el?.toString().toLocaleUpperCase()));
      if (!isRoleKeyInAllRole) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'roleKey' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }

      await validateFields({ user }, `common.not_found`, i18n);

      if (typeof isActive !== undefined) {
        user.isActive = isActive;
      }
      const isSentPasswordLink = await this._userService.isSentPasswordLink(user._id.toString(), 'admin');
      if (!isSentPasswordLink && isActive === true) {
        await this._userAdminService.sendCreatePasswordAdmin(
          {
            mobilePhone,
            userAdmin: user,
          },
          i18n,
        );
      }
      user.username = username?.toString()?.trim();
      user.mobilePhone = mobilePhone;
      user.mobilePhoneCode = mobilePhoneCode;
      user.firstname = firstName?.toString()?.trim();
      user.lastname = lastName?.toString()?.trim();
      user.email = email?.toString()?.trim();
      user.roleId = roleKey;
      user.status = isActive === false ? ConstantUser.BLOCKED : ConstantUser.IS_ACTIVE;

      user = await this._userAdminService.update(user._id, user);

      await this._userDetailService.addOrUpdate({
        userId: user._id,
        isUserAdmin: true,
        userRole: roleKey?.map((r) => ({ roleKey: r })),
      });
      await this._distributorUserRelationService.deleteByCondition({ userAdmin: user._id });

      if (distributorIds) {
        await Promise.all(
          distributorIds.map(async (distributorId) => {
            const findDistributor = await this._distributorService.findOne({ distributorId });
            if (findDistributor) {
              await this._distributorUserRelationService.create({
                userAdmin: user._id,
                distributor: findDistributor?._id,
              });
            }
          }),
        );
      }

      //Delete token cache
      if (isActive === false) {
        const tokensData = await this._userDetailService.findOne({ userId: user._id, isUserAdmin: true });
        if (!isEmptyObjectOrArray(tokensData)) {
          const { userToken } = tokensData;
          for (const token of userToken) {
            clearCurrentUserTokenCache(token?.accessToken, this.cacheManager).then();
          }
          await this._userDetailService.addOrUpdate({
            userId: user._id,
            isUserAdmin: true,
            userToken: [],
          });
        }
      }

      const data = {
        userId: user._id,
        username: user.username,
        firstname: user.firstname,
        lastname: user.lastname,
        status: user.status,
        roleId: roleKey,
        mobilePhone: user.mobilePhone,
        mobilePhoneCode: user.mobilePhoneCode,
        isActive: user.isActive,
        email: user.email,
        distributorIds,
      };

      return new ApiResponse(data);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('get-all-users')
  @ApiBearerAuth()
  @Roles(ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async getAllUsers(
    @Query() { search, distributorId }: GetAllUsersArgs,
    @Query() { offset, limit }: PaginationParams,
    @Query() { orderBy, orderDesc }: OrderParams,
    @CurrentUser() currentUser: User,
  ) {
    const result = [[], 0];
    let roleKeys = [];

    const userRole = await this._userDetailService.findUserRoles({ userId: currentUser._id.toString(), isUserAdmin: true });
    const getAllRoleName = RoleData;
    if (isEmptyObjectOrArray(userRole)) {
      return new ApiResponse(toListResponse(result));
    }

    if (userRole.some((r) => r.roleKey === ConstantRoles.SUPER_USER)) {
      roleKeys = [ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER];
    }

    if (userRole.some((r) => r.roleKey === ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)) {
      roleKeys = [ConstantRoles.DISTRIBUTOR_ADMIN];
    }

    const data = await this._userAdminService.getUsers(
      distributorId,
      search,
      roleKeys,
      +offset,
      +limit,
      // { [`${orderBy}`]: orderDesc }
      orderBy,
      orderDesc,
    );
    result[0] = data[0].map((el) => ({
      ...el,
      roleName: [getAllRoleName.find((role) => role.roleKey.includes(el?.roleId[0]))?.roleName] || [],
    }));
    result[1] = data[1];

    return new ApiResponse(toListResponse(result));
  }

  @Get('users/:userId')
  @ApiBearerAuth()
  @Roles(ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async detailUser(@Param('userId') userId: string, @I18n() i18n: I18nContext) {
    try {
      await validateFields({ userId }, `common.required_field`, i18n);

      const findUser = await this._userAdminService.findOne({ _id: userId });
      await validateFields({ user: findUser }, `common.not_found`, i18n);

      let distributorIds = [];
      const userRoles = await this._userDetailService.findUserRoles({ userId: findUser._id.toString(), isUserAdmin: true });
      if (!isEmptyObjectOrArray(userRoles) && userRoles.findIndex((r) => r.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN) > -1) {
        const result = await this._distributorUserRelationService.getDistributorUserRelation({ 'ua._id': findUser._id });
        if (result?.length) {
          distributorIds = result.map((e) => e.dis.distributorId);
        }
      }
      const data = {
        userId: findUser._id,
        username: findUser.username,
        firstname: findUser.firstname,
        lastname: findUser.lastname,
        status: findUser.status,
        roleId: userRoles?.map((r) => r.roleKey),
        mobilePhone: findUser.mobilePhone,
        mobilePhoneCode: findUser.mobilePhoneCode,
        isActive: findUser.isActive,
        email: findUser.email,
        distributorIds,
      };
      return new ApiResponse(data);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('list-outlets-distributor')
  @ApiBearerAuth()
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async getAllOutletDistributor(
    @Query() { search, distributorId }: GetOutletsDistributorArgs,
    @Query() { offset, limit }: PaginationParams,
    @Query() { orderBy, orderDesc }: OrderParams,
    @CurrentUser() currentUser: User,
  ) {
    const result = await this._outletService.getListOutlets(currentUser._id.toString(), {
      search,
      distributorId,
      offset,
      limit,
      orderBy,
      orderDesc,
    });
    result.data = result?.data?.map((item) => {
      return {
        ...item,
        saleRepId: item.disconnected ? [] : item.saleRepId,
        saleRepUUID: item.disconnected ? [] : item.saleRepUUID,
      };
    });
    return new ApiResponse(result);
  }

  @Get('list-outlets-distributor/export')
  @ApiBearerAuth()
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async exportAllOutletDistributor(
    @Query() { search, distributorId }: GetOutletsDistributorArgs,
    @Query() { offset, limit }: PaginationParams,
    @Query() { orderBy, orderDesc }: OrderParams,
    @CurrentUser() currentUser: any,
    @I18n() i18n: I18nContext,
  ) {
    const result: any = await this._outletService.getListOutlets(currentUser._id.toString(), {
      search,
      distributorId,
      offset,
      limit,
      orderBy,
      orderDesc,
    });
    let distributorName = '';
    if (distributorId) {
      const distributor = await this._distributorService.findOne({ distributorId });
      distributorName = `${distributor?.distributorName}`;
    }

    const xlsxData = result?.data?.map((item) => {
      return {
        [i18n.translate(`importExport.ucc`)]: item.ucc,
        [i18n.translate(`importExport.outletName`)]: item.outletName,
        [i18n.translate(`importExport.outletClass`)]: item.outletClass,
        [i18n.translate(`importExport.outletArea`)]: item.outletArea,
        [i18n.translate(`importExport.outletAddress`)]: item.outletAddress,
        [i18n.translate(`importExport.contactName`)]: item.contactName,
        [i18n.translate(`importExport.mobilePhoneCode`)]: item.contactPhoneCode,
        [i18n.translate(`importExport.phoneNumber`)]: item.contactNumber && item.contactNumber.replace(item.contactPhoneCode, '0'),
        [i18n.translate(`importExport.status`)]: i18n.translate(`importExport.${item.status}`),
        [i18n.translate(`importExport.distributorId`)]: distributorId,
        [i18n.translate(`importExport.distributorName`)]: distributorName,
        [i18n.translate(`importExport.salesRepId`)]: !item.disconnected ? (!isEmptyObjectOrArray(item.saleRepId[0]) ? item.saleRepId[0] : item.saleRepId) : null,
        [i18n.translate(`importExport.channel`)]: item.channel,
        [i18n.translate(`importExport.subChannel`)]: item.subChannel,
      };
    });

    const fileName = `Customer_Data_${distributorName}`;
    const file = await this._filesService.exportXLSXFile(fileName, xlsxData, 'Customer_Data', currentUser, { wch: 20 });
    return new ApiResponse(file);
  }

  @Get('outlets-master/:outletId')
  @ApiBearerAuth()
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async detailOutletMaster(@Param('outletId') outletId: string, @I18n() i18n: I18nContext) {
    try {
      await validateFields({ outletId }, `common.required_field`, i18n);

      const findOutlet = await this._outletService.findOne({ _id: new Types.ObjectId(outletId) });
      await validateFields({ outlet: findOutlet }, `common.not_found`, i18n);

      const saleRepInfo: any = await this._saleRepOutletRelationService.findOneByOutletId(findOutlet._id);

      return new ApiResponse({
        _id: findOutlet?._id,
        ucc: findOutlet?.ucc,
        outletName: findOutlet?.name,
        outletClass: findOutlet?.outletClass,
        outletArea: findOutlet?.area || null,
        outletAddress: findOutlet?.address,
        contactName: findOutlet?.contactName,
        contactNumber: findOutlet?.contactNumber,
        contactPhoneCode: findOutlet?.contactPhoneCode,
        status: findOutlet?.status,
        saleRepId: saleRepInfo?.saleRep?.saleRepId,
        channel: findOutlet?.channel,
        subChannel: findOutlet?.subChannel,
        depotId: findOutlet?.depotId,
      });
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('outlets-master/update')
  @ApiBearerAuth()
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  async updateOutletMaster(@Body() customerDto: AddOrUpdateCustomerMasterDto, @I18n() i18n: I18nContext) {
    try {
      const { outletId, outletAddress, outletArea, outletClass, outletName, contactName, ucc, saleRepId, status, depotId } = customerDto;
      const { contactNumber, contactPhoneCode } = customerDto;

      await validateFields(
        {
          outletId,
          outletAddress,
          outletArea,
          outletClass,
          outletName,
          // contactName,
          // contactNumber,
          // contactPhoneCode,
          ucc,
          saleRepId,
          status,
        },
        `common.required_field`,
        i18n,
      );

      const updateOutlet = await this._outletService.updateOutletFunc(
        {
          outletId,
          outletAddress,
          outletArea,
          outletClass,
          outletName,
          contactName,
          contactNumber,
          contactPhoneCode,
          ucc,
          saleRepId,
          status,
          depotId,
        },
        i18n,
      );

      const data = {
        _id: updateOutlet?._id,
        ucc: updateOutlet?.ucc,
        outletName: updateOutlet?.name,
        outletClass: updateOutlet?.outletClass,
        outletArea: updateOutlet?.area || null,
        outletAddress: updateOutlet?.address,
        contactName: updateOutlet?.contactName,
        contactNumber: updateOutlet?.contactNumber,
        contactPhoneCode: updateOutlet?.contactPhoneCode,
        status: updateOutlet?.status,
        saleRepId: saleRepId,
        depotId,
      };
      return new ApiResponse(data);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
