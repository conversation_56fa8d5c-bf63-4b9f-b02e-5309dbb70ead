import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsString, IsOptional } from 'class-validator';
import { DateRangeDto } from 'src/master-data/dtos/date-range.dto';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { ConstantCommons } from 'src/utils/constants';

export class AgentsMetricsArgs extends DateRangeDto {
  selectAll?: boolean;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.trim())
  search?: string;
}

export class AgentsMetricsOrderParams extends OrderParams {
  @ApiProperty({ required: false, default: 'name' })
  @IsOptional()
  @Type(() => String)
  orderBy?: string;

  @ApiProperty({ required: false, default: ConstantCommons.ORDER_DESC_DEFAULT.toLocaleLowerCase() })
  @IsOptional()
  @Type(() => String)
  orderDesc?: string;
}
