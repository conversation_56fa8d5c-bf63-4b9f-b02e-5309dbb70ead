import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { DateRangeDto } from 'src/master-data/dtos/date-range.dto';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';

export class CallEfficiencyMetricsArgs extends DateRangeDto {
  selectAll?: boolean;
}

export class CallEfficiencyMetricsOrderParams extends OrderParams {
  @ApiProperty({ required: false, default: 'calledDate' })
  @IsOptional()
  @Type(() => String)
  orderBy?: string;
}
