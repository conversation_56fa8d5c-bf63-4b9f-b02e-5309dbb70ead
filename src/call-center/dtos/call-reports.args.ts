import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';
import { DateRangeDto } from 'src/master-data/dtos/date-range.dto';

export class CallReportsArgs extends DateRangeDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  search?: string;

  selectAll?: boolean;
}
