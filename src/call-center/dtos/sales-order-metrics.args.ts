import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { DateRangeDto } from 'src/master-data/dtos/date-range.dto';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';

export class SalesOrderMetricsArgs extends DateRangeDto {
  selectAll?: boolean;
}

export class SalesOrderMetricsOrderParams extends OrderParams {
  @ApiProperty({ required: false, default: 'orderDate' })
  @IsOptional()
  @Type(() => String)
  orderBy?: string;
}
