import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export enum CallType {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound',
}

export class StartCallPlanDto {
  @ApiProperty()
  @IsOptional()
  location?: {
    latitude: number;
    longitude: number;
  };

  @ApiProperty()
  @IsOptional()
  @IsString()
  callCenterNote?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  outletNote?: string;
}

export class EndCallPlanDto extends StartCallPlanDto {
  @ApiProperty()
  @IsOptional()
  transcripts?: {
    language: string;
    contents: any;
  };
}

export class UpdateCheckListDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  label: string;

  @ApiProperty()
  @IsBoolean()
  checked: boolean;
}

export class DeleteCheckListItemDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  label: string;
}
