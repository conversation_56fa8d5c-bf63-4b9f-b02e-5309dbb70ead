import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { BusinessPartnerContact } from '../../master-data/entities/business-partner-contact/business-partner-contact.entity';
import { CallCenterStatus, LeaveTypes } from '../enums/call-center.enum';

@Entity('request_time_offs')
export class RequestTimeOff extends BaseSQLEntity {
  @ManyToOne(() => BusinessPartnerContact, { nullable: false })
  @JoinColumn()
  @Index()
  callCenter: BusinessPartnerContact;

  @ManyToOne(() => BusinessPartnerContact, { nullable: false })
  @JoinColumn()
  @Index()
  callCenterManagement: BusinessPartnerContact;

  @Column({ type: 'timestamp', nullable: false })
  fromTime: Date;

  @Column({ type: 'timestamp', nullable: false })
  toTime: Date;

  @Column({ type: 'enum', enum: CallCenterStatus, default: CallCenterStatus.PENDING, nullable: false })
  status: CallCenterStatus;

  @Column({ type: 'enum', enum: LeaveTypes, default: LeaveTypes.PAID_LEAVE, nullable: false })
  type: LeaveTypes;

  @Column({ type: 'varchar', nullable: false })
  callCenterNote: string;

  @Column({ type: 'varchar', nullable: true })
  callCenterManagementNote: string;
}
