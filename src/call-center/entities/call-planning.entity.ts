import { BeforeInsert, BeforeUpdate, Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { PlanWeek } from './plan-week.entity';
import { BusinessPartner } from '../../master-data/entities/business-partner/business-partner.entity';
import { BusinessPartnerContact } from '../../master-data/entities/business-partner-contact/business-partner-contact.entity';
import { CallPlanningHistories } from './call-planning-histories.entity';
import { PlanCycle } from './plan-cycle.entity';
import { printLog } from 'src/utils';
import { CallCenterStatus, CallPlanCreatorType } from '../enums/call-center.enum';
import { CallPlanningOrders } from './call-planning-order.entity';

@Entity('call_plannings')
@Unique(['week', 'callCenter', 'day', 'rescheduledDay'])
export class CallPlanning extends BaseSQLEntity {
  @ManyToOne(() => PlanWeek, { nullable: false })
  @JoinColumn()
  @Index()
  week: PlanWeek;

  @ManyToOne(() => PlanCycle, { nullable: false })
  @JoinColumn()
  @Index()
  cycle: PlanCycle;

  @ManyToOne(() => BusinessPartner, { nullable: false })
  @JoinColumn({ referencedColumnName: 'id' })
  @Index()
  outlet: BusinessPartner;

  @ManyToOne(() => BusinessPartnerContact, { nullable: false })
  @JoinColumn({ referencedColumnName: 'id' })
  @Index()
  callCenter: BusinessPartnerContact;

  @Column({ type: 'timestamp', nullable: false })
  day: Date;

  @Column({ default: false })
  rescheduled: boolean;

  @Column({ type: 'timestamp', nullable: true })
  rescheduledDay: Date;

  @Column({ nullable: true })
  rescheduleReason: string;

  @Column({ type: 'enum', enum: CallCenterStatus, default: CallCenterStatus.PENDING })
  callStatus: CallCenterStatus;

  @Column({ type: 'int', default: 0 })
  priority: number;

  @Column({ type: 'timestamp', nullable: true })
  displayDay: Date;

  @BeforeInsert()
  @BeforeUpdate()
  updateDisplayDay() {
    printLog('BeforeSave', this.rescheduled, this.rescheduledDay, this.day);
    this.displayDay = this.rescheduled && this.rescheduledDay ? this.rescheduledDay : this.day;
  }

  @Column({ type: 'jsonb', nullable: true })
  checkList: { label: string; checked: boolean; fromManager: boolean }[];

  @Column({ type: 'uuid', nullable: false })
  depotId: string;

  @Column({ nullable: true })
  reasonKey: string;

  @Column({ type: 'uuid', nullable: false })
  distributorId: string;

  @Column({ type: 'enum', enum: CallPlanCreatorType, nullable: true })
  creator: CallPlanCreatorType;

  @OneToMany(() => CallPlanningHistories, (call) => call.callPlanning)
  histories: CallPlanningHistories[];

  @Column({ type: 'boolean', default: false })
  hasOrder: boolean;

  @OneToMany(() => CallPlanningOrders, (order) => order.callPlanning)
  orders: CallPlanningOrders[];
}
