import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { PlanCycle } from './plan-cycle.entity';

@Entity('plan_weeks')
export class PlanWeek extends BaseSQLEntity {
  @ManyToOne(() => PlanCycle, { nullable: false })
  @Index()
  cycle: PlanCycle;

  @Column({ default: 'Week 1' })
  weekName: string;

  @Column({ type: 'int', default: 1 })
  weekNumber: number;

  @Column({ type: 'timestamp', nullable: false })
  startTime: Date;

  @Column({ type: 'timestamp', nullable: true })
  endTime: Date;
}
