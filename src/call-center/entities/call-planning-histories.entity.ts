import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { CallPlanning } from './call-planning.entity';
import { getRandomCode } from '../../utils';
import { CallHistoryStatus } from '../enums/call-center.enum';

@Entity('call_planning_histories')
export class CallPlanningHistories extends BaseSQLEntity {
  @ManyToOne(() => CallPlanning, (call) => call.histories, { nullable: false })
  callPlanning: CallPlanning;

  @Column({ type: 'timestamp', nullable: true })
  startCallTime: Date;

  @Column({ type: 'timestamp', nullable: true })
  endCallTime: Date;

  @Column({ type: 'jsonb', nullable: true })
  location: {
    latitude: number;
    longitude: number;
  };

  @Column({ type: 'float', nullable: true })
  locationRange: number;

  @Column({ type: 'boolean', default: false })
  hasOrder: boolean;

  @Column({ type: 'jsonb', nullable: true })
  transcripts: {
    language: string;
    contents: {
      time: Date;
      data: string;
    };
  };

  @Column({ nullable: true })
  callCenterNote: string;

  @Column({ type: 'enum', enum: CallHistoryStatus, default: CallHistoryStatus.IN_PROGRESS })
  status: CallHistoryStatus;

  @Column({ nullable: true })
  outletNote: string;

  @Column({ nullable: true })
  callPlanningRefId: string;

  @Column({ nullable: true })
  recordUrl: string;

  @Column({ nullable: true })
  callDuration: number;
}
