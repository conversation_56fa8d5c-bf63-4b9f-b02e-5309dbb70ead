import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { BusinessPartner } from '../../master-data/entities/business-partner/business-partner.entity';
import { BusinessPartnerContact } from '../../master-data/entities/business-partner-contact/business-partner-contact.entity';
import { CallPlanning } from './call-planning.entity';

@Entity('call_planning_orders')
export class CallPlanningOrders extends BaseSQLEntity {
  @Column({ type: 'uuid', nullable: true })
  callPlanningId: string;

  @ManyToOne(() => CallPlanning, (callPlanning) => callPlanning.orders, { nullable: true })
  @JoinColumn({ name: 'callPlanningId' })
  callPlanning: CallPlanning;

  @ManyToOne(() => BusinessPartner, { nullable: false })
  @JoinColumn()
  @Index()
  outlet: BusinessPartner;

  @ManyToOne(() => BusinessPartnerContact, { nullable: true })
  @JoinColumn()
  @Index()
  callCenter: BusinessPartnerContact;

  @Column({ type: 'timestamp', nullable: false })
  orderDate: Date;

  @Column({ nullable: false })
  orderId: string;

  @Column({ nullable: false })
  orderCode: string;

  @Column({ nullable: false })
  orderStatus: string;

  @Column({ nullable: false })
  orderVolume: number;

  @Column({ type: 'numeric', nullable: false, default: 0 })
  orderTotal: number;

  @Column({ nullable: false, default: 'REP' })
  orderSource: string;

  @Column({ type: 'jsonb', nullable: true })
  invoices: any;

  @Column({ type: 'timestamptz', nullable: false, default: '1900-01-01 07:00:00+00' })
  orderDateUTC: Date;
}
