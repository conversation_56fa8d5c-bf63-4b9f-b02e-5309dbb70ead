import { Column, Entity, PrimaryColumn } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';

@Entity('plan_time_frames')
export class PlanTimeFrame extends BaseSQLEntity {
  @PrimaryColumn('uuid', { nullable: false })
  distributorId: string;

  @Column({ type: 'int', default: 5 })
  timeInterval: number;

  @Column({ type: 'varchar', default: '08:00' })
  startingTimeframe: string;

  @Column({ type: 'varchar', default: '23:55' })
  endingTimeframe: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;
}
