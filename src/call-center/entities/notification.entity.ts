import { BaseSQLEntity } from 'src/shared/basesql.entity';
import { Column, Entity, PrimaryColumn } from 'typeorm';
import { NotificationStatus, NotificationType } from '../enums/call-center.enum';

@Entity('notifications')
export class Notification extends BaseSQLEntity {
  @PrimaryColumn('uuid', { nullable: false })
  fromUserId: string;

  @Column('uuid', { nullable: true })
  toUserId: string;

  @Column({ nullable: false })
  userRole: string;

  @Column({ nullable: false })
  userName: string;

  @Column({ nullable: false })
  depotId: string;

  @Column({ type: 'enum', enum: NotificationStatus, default: NotificationStatus.UNREAD })
  status: NotificationStatus;

  @Column({ type: 'text', nullable: true })
  title: string;

  @Column({ type: 'text', nullable: false })
  content: string;

  @Column({ type: 'json', nullable: true })
  data: any;

  @Column({ type: 'enum', enum: NotificationType, nullable: false })
  type: NotificationType;

  @Column({ type: 'uuid', nullable: true })
  callPlanId: string;

  @Column({ type: 'varchar', nullable: true })
  priority: string; // 'low' | 'normal' | 'high'

  @Column({ type: 'varchar', nullable: true })
  icon: string; // icon or image url

  @Column({ type: 'varchar', nullable: true })
  link: string; // link to the notification
}
