import { Column, Entity, Index, OneToMany } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { CycleNameType } from '../../journey-plannings/enums/cycle-type.enum';
import { PlanWeek } from './plan-week.entity';

@Entity('plan_cycles')
@Index(['cycleName', 'year'], { unique: true, where: '"isDeleted" = false' })
export class PlanCycle extends BaseSQLEntity {
  @Column({ type: 'enum', enum: CycleNameType, default: CycleNameType.CYCLE_1, nullable: false })
  cycleName: CycleNameType;

  @Column({ type: 'int', default: 2025, nullable: false })
  year: number;

  @OneToMany(() => PlanWeek, (week) => week.cycle)
  weeks: PlanWeek[];
}
