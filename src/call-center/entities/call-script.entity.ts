import { Column, Entity, Index, JoinColumn, ManyToOne, Unique } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { BusinessPartner } from '../../master-data/entities/business-partner/business-partner.entity';
import { BusinessPartnerContact } from '../../master-data/entities/business-partner-contact/business-partner-contact.entity';

@Entity('call_scripts')
@Unique('UQ_callscript_outlet_callcenter', ['outlet', 'callCenter'])
export class CallScript extends BaseSQLEntity {
  @ManyToOne(() => BusinessPartner, { nullable: true })
  @Index()
  outlet: BusinessPartner;

  @ManyToOne(() => BusinessPartnerContact, { nullable: true })
  @Index()
  callCenter: BusinessPartnerContact;

  @Column({ type: 'jsonb', nullable: false })
  content: object;

  @Column({ type: 'uuid', nullable: false })
  distributorId: string;
}
