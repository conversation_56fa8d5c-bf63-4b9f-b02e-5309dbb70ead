import { BaseSQLEntity } from 'src/shared/basesql.entity';
import { Column, Entity, Unique } from 'typeorm';

@Entity('target_settings')
@Unique(['agentId', 'outletId', 'month', 'year']) // This enforces uniqueness across both columns
export class TargetSettings extends BaseSQLEntity {
  @Column({ nullable: false })
  agentName: string;
  /**
   * The ID of the agent this target is assigned to
   * @type {string}
   */
  @Column({ nullable: false })
  agentId: string;
  /**
   * The strike rate target value
   * @type {number}
   */

  @Column({ nullable: true })
  outletName: string;
  /**
   * The ID of the outlet this target is assigned to
   * @type {string}
   */
  @Column({ nullable: true })
  outletId: string;
  /**
   * The strike rate target value
   * @type {number}
   */

  @Column({ type: 'decimal', default: 0 })
  volumeTarget: number;

  /**
   * The strike rate target value
   * @type {number}
   */
  @Column({ type: 'decimal', precision: 10, scale: 0, default: 0 })
  strikeRate: number;

  /**
   * The call coverage target value
   * @type {number}
   */
  @Column({ type: 'decimal', precision: 10, scale: 0, default: 0 })
  callCoverage: number;

  /**
   * The active selling outlet target value
   * @type {number}
   */
  @Column({ type: 'decimal', precision: 10, scale: 0, default: 0 })
  activeSellingOutlet: number;

  /**
   * The month this target is for (1-12)
   * @type {number}
   */
  @Column({ type: 'int' })
  month: number;

  /**
   * The year this target is for
   * @type {number}
   */
  @Column({ type: 'int' })
  year: number;
}
