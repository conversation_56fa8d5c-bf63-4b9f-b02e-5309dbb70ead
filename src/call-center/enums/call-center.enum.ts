export enum CallCenterStatus {
  PENDING = 'PENDING',
  MISSED = 'MISSED',
  RESCHEDULED = 'RESCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

export enum CallHistoryStatus {
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  ANSWERED = 'ANSWERED',
  BUSY = 'BUSY',
  NO_ANSWER = 'NO_ANSWER',
  FAILED = 'FAILED',
  CANCELED = 'CANCELED',
}

export enum TwilioDialCallStatus {
  COMPLETED = 'completed',
  ANSWERED = 'answered',
  BUSY = 'busy',
  NO_ANSWER = 'no-answer',
  FAILED = 'failed',
  CANCELED = 'canceled',
  IN_PROGRESS = 'in-progress',
}

export enum LeaveTypes {
  PAID_LEAVE = 'PAID_LEAVE',
  SICK_LEAVE = 'SICK_LEAVE',
}

export const CallRescheduleReasons = [
  { key: 'R1', content: 'I am busy' },
  { key: 'R2', content: 'Sick leave' },
  { key: 'R3', content: 'Annual leave' },
  { key: 'R4', content: 'Outlet closed' },
];

export enum AgentStatus {
  IN_A_CALL = 'IN A CALL',
  IDLE = 'IDLE',
}

export enum NotificationType {
  ASSIGNED = 'assigned',
  CALL_STARTED = 'call_started',
  CALL_ENDED = 'call_ended',
  RESCHEDULED = 'rescheduled',
  UPDATED = 'updated',
  MISSED = 'missed',
  IMPORTED = 'imported',
  REQUEST_JOIN_CALLED = 'request_join_called',
  REQUEST_JOIN_ACCEPTED = 'request_join_accepted',
  REQUEST_JOIN_REJECTED = 'request_join_rejected',
}

export enum NotificationStatus {
  READ = 'READ',
  UNREAD = 'UNREAD',
}

export enum CallPlanOrderSource {
  CALL_CENTER = 'CALL-CENTER',
  REP = 'REP',
}

export enum CallPlanCreatorType {
  MANAGER = 'MANAGER',
  AGENT = 'AGENT',
}

export const NotificationMessages = {
  [NotificationType.ASSIGNED]: 'notification.assigned',
  [NotificationType.CALL_STARTED]: 'notification.call_started',
  [NotificationType.CALL_ENDED]: 'notification.call_ended',
  [NotificationType.MISSED]: 'notification.missed',
  [NotificationType.RESCHEDULED]: 'notification.rescheduled',
};

export enum CallCenterCallStatus {
  IDLE = 'IDLE',
  IN_A_CALL = 'IN A CALL',
}
