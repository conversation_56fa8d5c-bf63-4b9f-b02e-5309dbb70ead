import { forwardRef, Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { CallCenterController } from './controllers/call-center.controller';
import { CallCenterService } from './services/call-center.service';
import { AuthModule } from '../auth/auth.module';
import { CallCenterOrderController } from './controllers/call-center-order.controller';
import { OrdersModule } from '../orders/orders.module';
import { OutletsModule } from '../outlets/outlets.module';
import { ExternalModule } from '../external/external.module';
import { JourneyPlanningsModule } from '../journey-plannings/journey-plannings.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CallPlanning } from './entities/call-planning.entity';
import { CallPlanningHistories } from './entities/call-planning-histories.entity';
import { CallPlanningOrders } from './entities/call-planning-order.entity';
import { PlanCycle } from './entities/plan-cycle.entity';
import { PlanWeek } from './entities/plan-week.entity';
import { RequestTimeOff } from './entities/request-time-off.entity';
import { CallPlanningService } from './services/call-planning.service';
import { CallPlanningOrdersService } from './services/call-planning-orders.service';
import { CallPlanningHistoriesService } from './services/call-planning-histories.service';
import { PlanCycleService } from './services/plan-cycle.service';
import { PlanWeekService } from './services/plan-week.service';
import { RequestTimeOffService } from './services/request-time-off.service';
import { MasterDataModule } from '../master-data/master-data.module';
import { PlanTimeFrameService } from './services/plan-time-frame.service';
import { PlanTimeFrame } from './entities/plan-time-frame.entity';
import { OmsModule } from '../oms/oms.module';
import { CallCenterOrderService } from './services/call-center-order.service';
import { BusinessPartner } from '../master-data/entities/business-partner/business-partner.entity';
import { TargetSettings } from './entities/target-settings.entity';
import { TargetSettingsService } from './services/target-settings.service';
import { CallCenterSettingController } from './controllers/call-center-setting.controller';
import { FilesModule } from '../files/files.module';
import { CallCenterPerformanceController } from './controllers/call-center-performance.controller';
import { CallCenterPerformanceService } from './services/call-center-performance.service';
import { CallCenterTwilioController } from './controllers/twilio.controller';
import { Notification } from './entities/notification.entity';
import { NotificationService } from './services/notification.service';
import { NotificationController } from './controllers/notification.controller';
import { CallScript } from './entities/call-script.entity';
import { CallScriptService } from './services/call-script.service';
import { UsersModule } from 'src/users/users.module';
import { CallCenterSseController } from './controllers/call-center-sse.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CallPlanning,
      CallPlanningHistories,
      CallPlanningOrders,
      PlanCycle,
      PlanWeek,
      RequestTimeOff,
      PlanTimeFrame,
      BusinessPartner,
      TargetSettings,
      Notification,
      CallScript,
    ]),
    AuthModule,
    HttpModule,
    forwardRef(() => OrdersModule),
    forwardRef(() => OutletsModule),
    forwardRef(() => ExternalModule),
    forwardRef(() => JourneyPlanningsModule),
    forwardRef(() => MasterDataModule),
    forwardRef(() => OmsModule),
    forwardRef(() => FilesModule),
    forwardRef(() => UsersModule),
  ],
  controllers: [
    CallCenterController,
    CallCenterOrderController,
    CallCenterSettingController,
    CallCenterPerformanceController,
    CallCenterTwilioController,
    NotificationController,
    CallCenterSseController,
  ],
  providers: [
    CallCenterService,
    CallCenterOrderService,
    CallPlanningService,
    CallPlanningOrdersService,
    CallPlanningHistoriesService,
    PlanCycleService,
    PlanWeekService,
    RequestTimeOffService,
    PlanTimeFrameService,
    TargetSettingsService,
    CallCenterPerformanceService,
    NotificationService,
    CallScriptService,
  ],
  exports: [
    CallCenterService,
    CallCenterOrderService,
    CallPlanningService,
    CallPlanningOrdersService,
    CallPlanningHistoriesService,
    PlanCycleService,
    PlanWeekService,
    RequestTimeOffService,
    PlanTimeFrameService,
    TargetSettingsService,
    CallCenterPerformanceService,
    NotificationService,
    CallScriptService,
  ],
})
export class CallCenterModule {}
