export type CallPlanType = {
  week: number;
  day: number;
  outlet: string;
  callCenter: string;
  depotId: string;
  distributorId: string;
};

export type CallPlanImportType = {
  week: string;
  day: string;
  outlet: string;
  callCenter: string;
  depotId: string;
  distributorId: string;
};

export type CallPlanMappingType = {
  'call-planning': CallPlanImportType;
  'call-script': CallScriptImportType;
};

export type CallPlanImportMappingType = {
  callPlanning: CallPlanImportType[];
  callScript: CallScriptImportType[];
};

export type ContactDistributorRelations = {
  originalContactId: string;
  distributorIds: string[];
  depotIds: string[];
  outletIds: string[];
  contactIds: string[];
};

export type ContactDistributorRelationsDataMap = {
  distributorMap?: Map<string, any>;
  depotMap?: Map<string, any>;
  outletMap: Map<string, any>;
  contactMap: Map<string, any>;
};

export type CallScriptImportType = {
  outlet: string;
  callCenter: string;
  distributor: string;
  content: string;
};
