import { CallTargetMappingType } from './call-plan-target.type';

export const CallTargetMapping: CallTargetMappingType = {
  'target-settings': {
    agentId: 'Call Center ID',
    outletId: 'Outlet ID',
    month: 'Month',
    year: 'Year',
    volumeTarget: 'Volume Target',
    strikeRate: 'Strike Rate',
    callCoverage: 'Call Coverage',
    activeSellingOutlet: 'Active Selling Outlet',
  },
};

export interface CallTargetImportMappingType {
  targetSettings: Array<{
    agentId: string;
    outletId: string;
    agentName: string;
    month: number;
    year: number;
    volumeTarget: number;
    strikeRate: number;
    callCoverage: number;
    activeSellingOutlet: number;
  }>;
}
