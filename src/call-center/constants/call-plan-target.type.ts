export type CallTargetType = {
  agentId: string;
  month: number;
  year: number;
  strikeRate: number;
  callCoverage: number;
  activeSellingOutlet: number;
};

export type CallTargetImportType = {
  agentId: string;
  outletId: string;
  month: string;
  year: string;
  volumeTarget: string;
  strikeRate: string;
  callCoverage: string;
  activeSellingOutlet: string;
};

export type CallTargetMappingType = {
  'target-settings': CallTargetImportType;
};

export type CallTargetImportMappingType = {
  targetSettings: CallTargetImportType[];
};
