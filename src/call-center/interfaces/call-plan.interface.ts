import { CallCenterStatus } from '../enums/call-center.enum';
import { BusinessPartnerStatus } from '../../master-data/constants/business-partner.enum';

export interface ICallPlanMetrics {
  strikeRate: {
    value: number;
    target: number;
  };
  callCoverage: {
    value: number;
    target: number;
  };
  activeSelling: {
    value: number;
    target: number;
  };
}

export interface ICallPlanStats {
  outletTarget: {
    value: number;
    label: string;
  };
  orderTotalVolume: {
    value: number;
    percentage: number;
    since: string;
  };
  avgFrequency: {
    numberOfOrder: number;
    theOrder: number;
  };
}

export interface IOutletContact {
  name: string;
  phoneNumber: string;
  email: string;
  role: string;
}

export interface IOutletVisited {
  saleRepName: string;
  saleRepId: string;
  lastVisitedTime: Date;
}

export interface ICallPlanOutlet {
  id: string;
  name: string;
  address: string;
  status: BusinessPartnerStatus;
  contact: IOutletContact;
  outletUCC: string;
  visited?: IOutletVisited;
  nps?: string;
  avgCallRate?: string;
}

export interface ICallPlanSegment {
  channel: string;
  subChannel: string;
  segment: string;
  outletClassification: string;
  organizationalSegment: string;
}

export interface ICallPlanHistory {
  startCallTime: Date;
  endCallTime: Date;
  location: {
    latitude: number;
    longitude: number;
  };
  locationRange: number;
  hasOrder: boolean;
  transcripts: {
    language: string;
    contents: {
      time: Date;
      data: string;
    };
  };
  callCenterNote: string;
  outletNote: string;
}

export interface ICallPlanItem {
  id?: string;
  week: any;
  cycle: any;
  day: Date;
  rescheduled: boolean;
  rescheduledDay: Date;
  displayDay: Date;
  priority: number;
  outlet: ICallPlanOutlet;
  segments: ICallPlanSegment;
  checkList: any;
  depotId: string;
  distributorId: string;
  histories?: ICallPlanHistory[];
  callStatus: CallCenterStatus;
  hasOrder: boolean;
  reasonKey: string;
  rescheduledReason: any;
  callCenter: any;
}

export interface ICallPlan {
  callCenter: any;
  metrics: ICallPlanMetrics;
  stats: ICallPlanStats;
  plans: ICallPlanItem[];
  date: Date;
  totalOutlets: number;
}
