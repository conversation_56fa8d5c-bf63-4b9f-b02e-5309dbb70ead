import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { PlanCycle } from '../entities/plan-cycle.entity';
import { getEndTimeOfWeek, isEmptyObjectOrArray } from 'src/utils';
import * as moment from 'moment';

@Injectable()
export class PlanCycleService extends BaseSQLService<PlanCycle> {
  constructor(
    @InjectRepository(PlanCycle)
    private readonly _planCycleRepository: Repository<PlanCycle>,
  ) {
    super();
    this._repository = this._planCycleRepository;
  }

  async findOrCreate(cycleName: string, year: number) {
    let cycle = await this.findOne({ where: { cycleName, year } });
    if (!cycle) {
      cycle = await this.save({
        cycleName,
        year,
      });
    }
    return cycle;
  }

  async getCallPlanCycleData(searchConditions: any) {
    const searchOptions: FindManyOptions<PlanCycle> = {
      relations: ['weeks'],
      order: {
        weeks: {
          startTime: 'ASC'
        },
      }
    };
    if (!isEmptyObjectOrArray(searchConditions)) {
      searchOptions.where = searchConditions;
    }
    const cycles = await this._repository.find(searchOptions);
    return cycles.map(cycle => {
      const cycleWeek = [];
      for (let i = 0; i < cycle.weeks.length; i ++) {
        const week = cycle.weeks[i];
        const weekEndTime = week?.endTime ? week.endTime : getEndTimeOfWeek(week?.startTime);
        cycleWeek.push({
          ...week,
          endTime: weekEndTime,
        })
      }

      const cycleStartDate = cycleWeek[0].startTime;
      const cycleEndDate = cycleWeek[cycleWeek.length - 1].endTime;

      const cycleDateRange = {
        cycleStartDate,
        cycleEndDate,
        displayText: `${moment(cycleStartDate).format('MM/DD/YYYY')} - ${moment(cycleEndDate).format('MM/DD/YYYY')}`
      }
      
      return {
        ...cycle,
        weeks: cycleWeek,
        cycleDateRange,
      }
    })
  }
}
