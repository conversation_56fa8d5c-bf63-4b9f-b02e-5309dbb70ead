import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { PlanTimeFrame } from '../entities/plan-time-frame.entity';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment';
import { I18nContext } from 'nestjs-i18n';

@Injectable()
export class PlanTimeFrameService extends BaseSQLService<PlanTimeFrame> {
  constructor(
    @InjectRepository(PlanTimeFrame)
    private readonly _planTimeFrameRepository: Repository<PlanTimeFrame>,
    private readonly configService: ConfigService,
  ) {
    super();
    this._repository = this._planTimeFrameRepository;
  }

  async findOrCreate(distributorId: string, timeInterval: number, startingTimeframe: string, i18n: I18nContext) {
    let timeFrame = await this.findOne({ where: { distributorId } });
    if (!timeFrame) {
      if (!this.validateTimeInput(startingTimeframe)) {
        throw new BadRequestException(await i18n.t('plan.call_plans.invalid_call_plan_time_frame_input'));
      }
      timeFrame = await this.save({ distributorId, timeInterval, startingTimeframe });
    }
    return timeFrame;
  }

  async findByDistributor(distributorId: string) {
    if (!distributorId) {
      return null;
    }
    return this._repository.findOne({
      where: {
        distributorId,
        isDeleted: false,
      },
    });
  }

  async updateTimeFrameSetting(distributorId: string, startingTimeframe: string, i18n: I18nContext) {
    if (!this.validateTimeInput(startingTimeframe)) {
      throw new BadRequestException(await i18n.t('plan.call_plans.invalid_call_plan_time_frame_input'));
    }

    const timeFrame = await this.findOne({ where: { distributorId } });
    if (timeFrame) {
      return this.save({ ...timeFrame, startingTimeframe, endingTimeframe: process.env.CALL_PLAN_END_TIME_LIMIT || '18:00', isDeleted: false, isActive: true });
    }

    const newPlanTimeFrame = new PlanTimeFrame();
    newPlanTimeFrame.distributorId = distributorId;
    newPlanTimeFrame.startingTimeframe = startingTimeframe;
    newPlanTimeFrame.endingTimeframe = process.env.CALL_PLAN_END_TIME_LIMIT || '18:00';
    return this.save(newPlanTimeFrame);
  }

  validateTimeInput(timeInput: string) {
    const timeRegex = /^([01]?\d|2[0-3]):([0-5]\d)$/;

    if (!timeRegex.test(timeInput)) {
      return false;
    }

    const callPlanStartTimeLimit = this.configService.get<string>('CALL_PLAN_START_TIME_LIMIT');
    const callPlanEndTimeLimit = this.configService.get<string>('CALL_PLAN_END_TIME_LIMIT');

    if (!callPlanStartTimeLimit || !callPlanEndTimeLimit) {
      return true;
    }

    const inputTime = moment(timeInput, 'HH:mm');
    const startTime = moment(callPlanStartTimeLimit, 'HH:mm');
    const endTime = moment(callPlanEndTimeLimit, 'HH:mm');

    if (inputTime.isBefore(startTime) || inputTime.isAfter(endTime)) {
      return false;
    }

    return true;
  }

  async getValidCallPlanTimeFrame(distributorId: string) {
    const planTimeFrame = await this._repository.findOne({
      where: { distributorId, isDeleted: false },
    });
    return this.generateValidCallPlanTimeFrameSettings(planTimeFrame);
  }

  generateValidCallPlanTimeFrameSettings(planTimeFrame: PlanTimeFrame) {
    const { startingTimeframe, endingTimeframe, timeInterval } = planTimeFrame || {};
    const callPlanStartTimeLimit = this.configService.get<string>('CALL_PLAN_START_TIME_LIMIT');
    const callPlanEndTimeLimit = this.configService.get<string>('CALL_PLAN_END_TIME_LIMIT');
    const defaultCallPlanTimeInterval = this.configService.get<string>('CALL_PLAN_TIME_INTERVAL');

    let validStartTimeFrame = this.validateTimeInput(startingTimeframe) ? startingTimeframe : callPlanStartTimeLimit;
    const validEndTimeFrame = this.validateTimeInput(endingTimeframe) ? endingTimeframe : callPlanEndTimeLimit;
    if (moment(validStartTimeFrame, 'HH:mm').isSameOrAfter(moment(validEndTimeFrame, 'HH:mm'))) {
      validStartTimeFrame = validEndTimeFrame;
    }

    const validTimeFrameSettings: DeepPartial<PlanTimeFrame> = {
      ...planTimeFrame,
      startingTimeframe: validStartTimeFrame,
      endingTimeframe: validEndTimeFrame,
      timeInterval: timeInterval || Number(defaultCallPlanTimeInterval),
    };
    return {
      ...validTimeFrameSettings,
      timeSlots: this.generateTimeSlot(validTimeFrameSettings),
    };
  }

  generateTimeSlot(planTimeFrame?: DeepPartial<PlanTimeFrame>) {
    const { timeInterval, endingTimeframe, startingTimeframe } = planTimeFrame || {};
    const callPlanStartTimeLimit = startingTimeframe || this.configService.get<string>('CALL_PLAN_START_TIME_LIMIT');
    const callPlanEndTimeLimit = endingTimeframe || this.configService.get<string>('CALL_PLAN_END_TIME_LIMIT');
    const callPlanTimeInterval = timeInterval || this.configService.get<string>('CALL_PLAN_TIME_INTERVAL');
    const timeSlots = [];
    const startTime = moment(callPlanStartTimeLimit, 'HH:mm');
    const endTime = moment(callPlanEndTimeLimit, 'HH:mm');

    while (startTime.isSameOrBefore(endTime, 'minutes')) {
      timeSlots.push(startTime.format('HH:mm'));
      startTime.add(callPlanTimeInterval, 'minutes');
    }

    return timeSlots;
  }
}
