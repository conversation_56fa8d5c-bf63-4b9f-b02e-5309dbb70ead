import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { RequestTimeOff } from '../entities/request-time-off.entity';

@Injectable()
export class RequestTimeOffService extends BaseSQLService<RequestTimeOff> {
  constructor(
    @InjectRepository(RequestTimeOff)
    private readonly _requestTimeOffRepository: Repository<RequestTimeOff>,
  ) {
    super();
    this._repository = this._requestTimeOffRepository;
  }
}
