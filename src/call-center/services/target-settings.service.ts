import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, In, IsNull, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { TargetSettings } from '../entities/target-settings.entity';
import { I18nContext } from 'nestjs-i18n';
import { FilesService } from '../../files/services';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { BusinessPartnerContactRole, BusinessPartnerRelationType } from '../../master-data/constants/business-partner.enum';
import { CallTargetImportMappingType } from '../constants/call-plan-target.mapping';
import { BusinessPartnersService } from '../../master-data/services/business-partners.service';
import { BusinessPartnerRelationService } from 'src/master-data/services/business-partners-relation.service';

@Injectable()
export class TargetSettingsService extends BaseSQLService<TargetSettings> {
  constructor(
    @InjectRepository(TargetSettings)
    private readonly _targetSettingsRepository: Repository<TargetSettings>,
    private readonly filesService: FilesService,
    private readonly businessPartnersContactService: BusinessPartnersContactService,
    private readonly businessPartnersService: BusinessPartnersService,
    private readonly businessPartnerRelationService: BusinessPartnerRelationService,
  ) {
    super();
    this._repository = this._targetSettingsRepository;
  }

  /**
   * Get target settings list with pagination
   * @param params Query parameters for filtering and pagination
   * @returns List of target settings and total count
   */
  async getTargetSettings(params: { month?: number; year?: number; agentIds?: string | string[]; offset?: number; limit?: number; orderBy?: string; orderDesc?: string }) {
    const { month, year, agentIds, offset = 0, limit = 10, orderBy = 'createdAt', orderDesc = 'DESC' } = params;
    const query = this._targetSettingsRepository.createQueryBuilder('target_settings');

    if (month) {
      query.andWhere('target_settings.month = :month', { month });
    }
    if (year) {
      query.andWhere('target_settings.year = :year', { year });
    }
    if (agentIds) {
      if (Array.isArray(agentIds)) {
        query.andWhere('target_settings.agentId IN (:...agentIds)', { agentIds: agentIds });
      } else {
        query.andWhere('target_settings.agentId = :agentId', { agentId: agentIds });
      }
    }

    query.skip(offset).take(limit);
    query.orderBy(`target_settings.${orderBy || 'createdAt'}`, orderDesc === 'DESC' ? 'DESC' : 'ASC');

    const [targets, totalRecords] = await query.getManyAndCount();
    return {
      targets,
      totalRecords,
    };
  }

  /**
   * Create or update target settings
   * @param data Target settings data
   * @param i18n I18n context for translations
   * @returns Created or updated target settings
   */
  async createOrUpdateTargetSettings(
    data: {
      agentId: string;
      agentName: string;
      outletId: string;
      outletName: string;
      volumeTarget: number;
      strikeRate: number;
      callCoverage: number;
      activeSellingOutlet: number;
      month: number;
      year: number;
    },
    i18n: I18nContext,
  ) {
    // Find existing target settings for the agent and month/year
    let targetSettings = await this._targetSettingsRepository.findOne({
      where: {
        agentId: data.agentId,
        outletId: !!data.outletId ? data.outletId : IsNull(),
        month: data.month,
        year: data.year,
      },
    });

    data.outletId = !!data.outletId ? data.outletId : null;

    if (!targetSettings) {
      // Create new target settings if none exists
      targetSettings = this._targetSettingsRepository.create(data);
    } else {
      // Update existing target settings
      Object.assign(targetSettings, data);
    }

    return await this._targetSettingsRepository.save(targetSettings);
  }

  /**
   * Get target settings for a specific agent and month/year
   * @param agentId Agent ID
   * @param month Month number (1-12)
   * @param year Year
   * @returns Target settings for the agent
   */
  async getAgentTargetSettings(agentId: string, outletId: string, month: number, year: number) {
    const searchCondition = [
      {
        agentId,
        outletId,
        month,
        year,
      },
      {
        agentId,
        outletId: IsNull(),
        month,
        year,
      },
    ];
    const agentTargetSettings = await this._targetSettingsRepository.find({
      where: searchCondition,
    });

    if (!agentTargetSettings.length) {
      return null;
    }

    const targetSetting = agentTargetSettings.find((setting) => setting.agentId === agentId && setting.outletId === outletId);

    if (targetSetting) {
      return targetSetting;
    }

    return agentTargetSettings[0];
  }

  /**
   * Export target settings to Excel
   * @param params Query parameters for filtering
   * @param i18n I18n context for translations
   * @returns Excel file buffer
   */
  async exportTargetSettings(params: { month?: number; year?: number; agentIds?: string | string[] }, i18n: I18nContext) {
    const { month, year, agentIds } = params;
    const query = this._targetSettingsRepository.createQueryBuilder('target_settings');

    if (month) {
      query.andWhere('target_settings.month = :month', { month });
    }
    if (year) {
      query.andWhere('target_settings.year = :year', { year });
    }
    if (agentIds) {
      if (Array.isArray(agentIds)) {
        query.andWhere('target_settings.agentId IN (:...agentIds)', { agentIds });
      } else {
        query.andWhere('target_settings.agentId = :agentId', { agentId: agentIds });
      }
    }

    const targetSettings = await query.getMany();

    let xlsxData: { [key: string]: string }[] = [
      {
        [i18n.translate(`importExport.targetSetting.agentId`)]: '',
        [i18n.translate(`importExport.targetSetting.agentName`)]: '',
        [i18n.translate(`importExport.targetSetting.outetId`)]: '',
        [i18n.translate(`importExport.targetSetting.outletName`)]: '',
        [i18n.translate(`importExport.targetSetting.month`)]: '',
        [i18n.translate(`importExport.targetSetting.year`)]: '',
        [i18n.translate(`importExport.targetSetting.volumeTarget`)]: '',
        [i18n.translate(`importExport.targetSetting.strikeRate`)]: '',
        [i18n.translate(`importExport.targetSetting.callCoverage`)]: '',
        [i18n.translate(`importExport.targetSetting.activeSellingOutlet`)]: '',
      },
    ];

    if (targetSettings.length) {
      xlsxData = targetSettings.map((setting) => ({
        [i18n.translate(`importExport.targetSetting.agentId`)]: setting.agentId,
        [i18n.translate(`importExport.targetSetting.agentName`)]: setting.agentName,
        [i18n.translate(`importExport.targetSetting.outletId`)]: setting.outletId,
        [i18n.translate(`importExport.targetSetting.outletName`)]: setting.outletName,
        [i18n.translate(`importExport.targetSetting.month`)]: setting.month.toString(),
        [i18n.translate(`importExport.targetSetting.year`)]: setting.year.toString(),
        [i18n.translate(`importExport.targetSetting.volumeTarget`)]: setting.volumeTarget.toString(),
        [i18n.translate(`importExport.targetSetting.strikeRate`)]: setting.strikeRate.toString(),
        [i18n.translate(`importExport.targetSetting.callCoverage`)]: setting.callCoverage.toString(),
        [i18n.translate(`importExport.targetSetting.activeSellingOutlet`)]: setting.activeSellingOutlet.toString(),
      }));
    }

    const fileName = `Target_Settings_${year || 'All'}_${month ? `Month_${month}` : 'All_Months'}`;
    const result = await this.filesService.exportXLSXFile(fileName, xlsxData, 'Target Settings', null);
    return result;
  }

  async validateImportTargetData(importData: CallTargetImportMappingType, i18n: I18nContext) {
    const outletIds = [],
      contactIds = [],
      outletContacts = [];
    const targetSettings = importData.targetSettings;
    for (let i = 0; i < targetSettings.length; i++) {
      const row = targetSettings[i];
      // Validate required fields
      if (!row.agentId || !row.month || !row.year || !row.volumeTarget || !row.strikeRate || !row.callCoverage || !row.activeSellingOutlet) {
        throw new HttpException(await i18n.translate('importExport.targetSetting.invalid_data'), HttpStatus.BAD_REQUEST);
      }

      // Validate month and year
      if (row.month < 1 || row.month > 12) {
        throw new HttpException(await i18n.translate('importExport.targetSetting.invalid_month'), HttpStatus.BAD_REQUEST);
      }

      // Validate percentages
      if (row.strikeRate < 0 || row.strikeRate > 100 || row.callCoverage < 0 || row.callCoverage > 100 || row.activeSellingOutlet < 0 || row.activeSellingOutlet > 100) {
        throw new HttpException(await i18n.translate('importExport.targetSetting.invalid_percentage'), HttpStatus.BAD_REQUEST);
      }
      const { agentId, outletId } = row;
      if (outletContacts.includes(`${agentId}-${outletId}`)) {
        throw new HttpException(await i18n.translate('importExport.targetSetting.invalid_data'), HttpStatus.BAD_REQUEST);
      }
      outletIds.push(outletId);
      contactIds.push(agentId);
    }

    const [contacts, outlets] = await Promise.all([
      this.businessPartnersContactService.find({
        where: {
          businessPartnerContactKey: In(contactIds),
          businessPartnerContactPersonRole: BusinessPartnerContactRole.CALL_CENTER,
          isDeleted: false,
        },
      }),
      this.businessPartnersService.find({
        where: {
          businessPartnerKey: In(outletIds),
          isDeleted: false,
        },
      }),
    ]);

    const contactsByIdMap = new Map(contacts.map((c) => [c.businessPartnerContactKey, c]));
    const outletsByIdMap = new Map(outlets.map((o) => [o.businessPartnerKey, o]));
    return targetSettings.map((tS) => ({
      ...tS,
      contact: contactsByIdMap.get(`${tS.agentId}`),
      outlet: outletsByIdMap.get(`${tS.outletId}`),
    }));
  }

  /**
   * Import target settings from Excel
   * @param mappingResult Mapping result containing target settings
   * @param i18n I18n context for translations
   * @returns Import results
   */
  async importTargetSettings(mappingResult: CallTargetImportMappingType, i18n: I18nContext) {
    const results = {
      success: 0,
      failed: 0,
      successMessage: await i18n.translate('importExport.targetSetting.importSuccess'),
      errors: [] as string[],
    };

    const targetSettings: any = await this.validateImportTargetData(mappingResult, i18n);

    // Process each row
    for (const row of targetSettings) {
      try {
        if (!row.contact) {
          throw new HttpException(i18n.translate('importExport.targetSetting.agent_not_found', { args: { agentId: row.agentId } }), HttpStatus.BAD_REQUEST);
        }

        if (row.outletId && !row.outlet) {
          throw new HttpException(i18n.translate('importExport.targetSetting.outlet_not_found', { args: { outletId: row.outletId } }), HttpStatus.BAD_REQUEST);
        }

        if (row.outletId) {
          const contactOutletRelation = await this.businessPartnerRelationService.findOne({
            where: {
              businessPartner1: row.contact.id,
              businessPartner2: row.outlet.id,
              businessPartnerRelationType: BusinessPartnerRelationType.CONTACT_OUTLET,
              isDeleted: false,
            },
          });
          if (!contactOutletRelation) {
            throw new HttpException(
              i18n.translate('importExport.targetSetting.incorrect_relation', { args: { agentId: row.agentId, outletId: row.outletId } }),
              HttpStatus.BAD_REQUEST,
            );
          }
        }

        const agentName = row.contact.businessPartnerContactName1 || row.contact.businessPartnerContactName2;

        // Create or update target settings
        await this.createOrUpdateTargetSettings(
          {
            agentId: row.agentId,
            agentName,
            outletId: row.outlet?.businessPartnerKey,
            outletName: row.outlet?.businessPartnerName1 || row.outlet?.businessPartnerName2,
            month: row.month,
            year: row.year,
            volumeTarget: row.volumeTarget,
            strikeRate: row.strikeRate,
            callCoverage: row.callCoverage,
            activeSellingOutlet: row.activeSellingOutlet,
          },
          i18n,
        );
        results.success++;
      } catch (error) {
        results.errors.push(`${results.failed}: ${error.message}`);
        throw error;
      }
    }

    return results;
  }
}
