import { BadRequestException, forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, DeepPartial, FindOptionsWhere, ILike, In, IsNull, LessThan<PERSON>r<PERSON>qual, Not, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { CallPlanning } from '../entities/call-planning.entity';
import { I18nContext } from 'nestjs-i18n';
import * as moment from 'moment-timezone';
import * as _ from 'lodash';
import { HttpService } from '@nestjs/axios';
// import { Request } from 'express';
import { PlanCycleService } from './plan-cycle.service';
import { PlanWeekService } from './plan-week.service';
import { PlanTimeFrameService } from './plan-time-frame.service';
import { BusinessPartnersService, RawContact } from '../../master-data/services/business-partners.service';
import { BusinessPartnerRelationService } from '../../master-data/services/business-partners-relation.service';
import {
  BusinessPartnerContactRole,
  BusinessPartnerRelationCommunication,
  BusinessPartnerRelationType,
  BusinessPartnerType,
} from '../../master-data/constants/business-partner.enum';
import { BusinessPartnersContactService } from 'src/master-data/services/business-partners-contact.service';
import { getCallPlanDayNumber, isEmptyObjectOrArray, printLog, roundNumber, roundToNearestInt } from '../../utils';
import { ICallPlan, ICallPlanItem, ICallPlanMetrics, ICallPlanStats } from '../interfaces/call-plan.interface';
import {
  CallCenterCallStatus,
  CallCenterStatus,
  CallHistoryStatus,
  CallPlanCreatorType,
  CallRescheduleReasons,
  NotificationMessages,
  NotificationStatus,
  NotificationType,
  TwilioDialCallStatus,
} from '../enums/call-center.enum';
import { CallPlanningHistories } from '../entities/call-planning-histories.entity';
import { ConstantRoles } from '../../utils/constants/role';
import { OmsService } from '../../external/services/oms.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { OmsSalesRepStatisticsService } from '../../oms/services/sales-rep-statistics.service';
import { BusinessPartnerContact } from '../../master-data/entities/business-partner-contact/business-partner-contact.entity';
import { DeleteCheckListItemDto, EndCallPlanDto, StartCallPlanDto, UpdateCheckListDto } from '../dtos/call-plan.dto';
import { OutletJourneyPlanningService } from '../../journey-plannings/services/outlet-journey-planning.service';
import { CallPlanningHistoriesService } from './call-planning-histories.service';
import { CONVERT_ML_TO_HL } from 'src/external/constants';
import { CallPlanningOrders } from '../entities/call-planning-order.entity';
import { CallPlanningOrdersService } from './call-planning-orders.service';
import { DateRangeDto } from 'src/master-data/dtos/date-range.dto';
import { buildSearchDateCriteria } from 'src/utils/helpers/database';
import { CallPlanImportMappingType, ContactDistributorRelations, ContactDistributorRelationsDataMap } from '../constants/call-plan.type';
import { FilesService } from '../../files/services';
import { DateRangeDtos } from '../dtos/date-range.dto';
import { endTwilioCall, endTwilioConferenceRoom, generateSIPURLfromPhoneNumber, getRecordingStream, getTwilioCallDetails, makePhoneCall } from 'src/shared/sms-provider/twilio';
import { NotificationService } from './notification.service';
import { TargetSettingsService } from './target-settings.service';
import { BusinessPartnerCommunicationService } from 'src/master-data/services/business-partners-communication.service';
import { convertMomentToCallPlanDate } from 'src/utils/helpers/call-planning';
import { CallCenterPerformanceService } from './call-center-performance.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

interface DayData {
  week: number;
  total: number;
  completed: number;
  pending: number;
  in_progress: number;
  rescheduled: number;
  missed: number;
}

interface DayInfo {
  day: string;
  total: number;
  completed: number;
  pending: number;
  in_progress: number;
  rescheduled: number;
  missed: number;
}

interface WeekData {
  weekStart: string;
  weekEnd: string;
  days: DayInfo[];
}

@Injectable()
export class CallPlanningService extends BaseSQLService<CallPlanning> {
  constructor(
    @InjectRepository(CallPlanning)
    private readonly _callPlanningRepository: Repository<CallPlanning>,
    private readonly _businessPartnerService: BusinessPartnersService,
    private readonly _businessPartnerContactService: BusinessPartnersContactService,
    private readonly _businessPartnerRelationService: BusinessPartnerRelationService,
    @Inject(forwardRef(() => BusinessPartnerCommunicationService))
    private readonly _businessPartnerCommunicationService: BusinessPartnerCommunicationService,
    private readonly _planTimeFrameService: PlanTimeFrameService,
    private readonly _planCycleService: PlanCycleService,
    private readonly _planWeekService: PlanWeekService,
    private readonly omsService: OmsService,
    private readonly omsSalesRepStatisticsService: OmsSalesRepStatisticsService,
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly callPlanningHistoriesService: CallPlanningHistoriesService,
    private readonly _targetSettingsService: TargetSettingsService,
    @Inject(forwardRef(() => CallCenterPerformanceService))
    private readonly _callCenterPerformanceService: CallCenterPerformanceService,
    @Inject(forwardRef(() => FilesService))
    private readonly fileService: FilesService,

    @Inject(CACHE_MANAGER) private cacheManager: Cache,

    @Inject(forwardRef(() => CallPlanningOrdersService))
    readonly callPlanningOrderService: CallPlanningOrdersService,
    @Inject(forwardRef(() => NotificationService))
    private readonly notificationService: NotificationService,

    private readonly _httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    super();
    this._repository = this._callPlanningRepository;
  }

  async getDistributorIdFromDepot(depotKey: string): Promise<any> {
    const depot = await this._businessPartnerService.findOne({
      where: { businessPartnerKey: depotKey, isDeleted: false },
    });

    if (!depot) {
      throw new BadRequestException('Depot not found');
    }

    const relation = await this._businessPartnerRelationService.findOne({
      where: {
        businessPartner1: depot.id,
        businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR,
        isDeleted: false,
      },
    });

    if (!relation || !relation.businessPartner2) {
      throw new BadRequestException('Distributor relation not found for this depot');
    }

    return { distributorId: relation.businessPartner2, depotId: depot.id };
  }

  /**
   *
   * @param mappingResult
   * @param cycleId
   * @param contactDistributorRelationDataMap
   * @param i18n
   * @param currentContact
   */
  async importCallPlan(mappingResult: any, cycleId: string, contactDistributorRelationDataMap: ContactDistributorRelationsDataMap, i18n: I18nContext, currentContact: any) {
    const { callPlanning: importedPlans } = mappingResult;
    if (!importedPlans || importedPlans.length === 0 || !cycleId) {
      throw new BadRequestException('No call plans to import');
    }

    const [cycleTimeRange, weeks] = await Promise.all([this._planWeekService.getCycleTimeRange(cycleId), this._planWeekService.getWeeksByCycleId(cycleId)]);

    if (!cycleTimeRange) {
      throw new BadRequestException('Cycle not found');
    }

    if (cycleTimeRange.endTime.isBefore(moment().tz(process.env.TZ))) {
      throw new BadRequestException('Cannot import plans for past cycles');
    }

    const distributorIds = Array.from(contactDistributorRelationDataMap.distributorMap.values()).map((value) => value.id);

    const timeFrameSettings = await this._planTimeFrameService.find({
      where: { distributorId: In(distributorIds), isDeleted: false },
    });

    const callCenterKeys = [...new Set(importedPlans.map((row: any) => row.callCenter))];
    const outletKeys = [...new Set(importedPlans.map((row: any) => String(row.outlet)))];

    // Batch fetch call centers and outlets
    const [callCenters, outlets] = await Promise.all([
      this._businessPartnerContactService.find({
        where: { businessPartnerContactKey: In(callCenterKeys), isDeleted: false },
      }),
      this._businessPartnerService.find({
        where: { businessPartnerKey: In(outletKeys), isDeleted: false },
      }),
    ]);
    const callCenterMap = new Map(callCenters.map((cc) => [cc.businessPartnerContactKey, cc]));
    const outletMap = new Map(outlets.map((o) => [o.businessPartnerKey, o]));

    const existingPlans = await this._repository.find({
      where: {
        cycle: { id: cycleId },
        distributorId: In(distributorIds),
        isDeleted: false,
      },
      relations: ['outlet', 'callCenter', 'week'],
    });

    const existingPlansByWeekAndCallCenter = new Map();
    const existingPlansByWeekAndOutlet = new Map();
    existingPlans.forEach((plan) => {
      const weekKey = plan.week.id;
      const callCenterKey = plan.callCenter.id;
      const outletKey = plan.outlet.id;

      // Group by week and call center
      if (!existingPlansByWeekAndCallCenter.has(weekKey)) {
        existingPlansByWeekAndCallCenter.set(weekKey, new Map());
      }
      if (!existingPlansByWeekAndCallCenter.get(weekKey).has(callCenterKey)) {
        existingPlansByWeekAndCallCenter.get(weekKey).set(callCenterKey, []);
      }
      existingPlansByWeekAndCallCenter.get(weekKey).get(callCenterKey).push(plan);

      // Group by week and outlet
      if (!existingPlansByWeekAndOutlet.has(weekKey)) {
        existingPlansByWeekAndOutlet.set(weekKey, new Map());
      }
      if (!existingPlansByWeekAndOutlet.get(weekKey).has(outletKey)) {
        existingPlansByWeekAndOutlet.get(weekKey).set(outletKey, []);
      }
      existingPlansByWeekAndOutlet.get(weekKey).get(outletKey).push(plan);
    });

    const pendingPlansByCallCenter = new Map();
    const callPlans = [];
    const { distributorMap, depotMap } = contactDistributorRelationDataMap;
    for (const row of importedPlans) {
      const { depotId, distributorId } = row;
      const distributorUUID = distributorMap.get(String(distributorId))?.id;
      const depotUUID = depotMap.get(String(depotId))?.id;
      const callPlan = await this.validateAndCreateCallPlan(
        row,
        timeFrameSettings,
        depotUUID,
        distributorUUID,
        cycleId,
        weeks,
        pendingPlansByCallCenter,
        callCenterMap,
        outletMap,
        existingPlansByWeekAndCallCenter,
        existingPlansByWeekAndOutlet,
        i18n,
      );

      const callCenterId = callPlan.callCenter.id;
      const planDate = moment(callPlan.day).tz(process.env.TZ);
      const dayKey = planDate.format('YYYY-MM-DD');

      if (!pendingPlansByCallCenter.has(callCenterId)) {
        pendingPlansByCallCenter.set(callCenterId, new Map());
      }
      if (!pendingPlansByCallCenter.get(callCenterId).has(dayKey)) {
        pendingPlansByCallCenter.get(callCenterId).set(dayKey, []);
      }
      pendingPlansByCallCenter.get(callCenterId).get(dayKey).push(planDate);

      callPlans.push(callPlan);
    }
    // Save with batches
    const BATCH_SIZE = 1000;
    const results = [];
    for (let i = 0; i < callPlans.length; i += BATCH_SIZE) {
      const batch = callPlans.slice(i, i + BATCH_SIZE);
      const batchResults = await this._repository.save(batch);
      results.push(...batchResults);
    }

    /**
     * @params results: all saved plans
     * Group by callCenter.id, then send notification with count = number of plans per callCenter
     */
    if (currentContact && !isEmptyObjectOrArray(results)) {
      // Group plans by callCenter.id
      const plansByCallCenter = new Map();
      for (const plan of results) {
        const callCenterId = plan.callCenter.id;
        if (!plansByCallCenter.has(callCenterId)) {
          plansByCallCenter.set(callCenterId, []);
        }
        plansByCallCenter.get(callCenterId).push(plan);
      }
      // save notification for each callCenter
      const content = await i18n.translate('notification.assigned', {
        args: { callCenterManager: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2 },
      });
      for (const [callCenterId, plans] of plansByCallCenter?.entries()) {
        const callCenter = callCenters?.find((cc) => cc.id === callCenterId);
        if (!callCenter) continue;
        const count = plans.length;
        /*const content = await i18n.translate('notification.assigned', {
          args: { callCenterManager: currentContact.businessPartnerName1 || currentContact.businessPartnerName2 },
        });*/
        this.notificationService
          .create({
            fromUserId: currentContact.id,
            toUserId: callCenter.id,
            userName: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2,
            userRole: currentContact.businessPartnerContactPersonRole,
            status: NotificationStatus.UNREAD,
            content: content as string,
            type: NotificationType.IMPORTED,
            callPlanId: null,
            depotId: plans[0]?.depotId,
            title: await i18n.translate('plan.call_plans.call_plans', {
              args: { count },
            }),
            data: { count, planIds: plans.map((p) => p.id) },
          })
          .catch((e) => printLog(e?.toString ? e.toString() : JSON.stringify(e)));
      }
    }

    return results;
  }

  private async validateAndCreateCallPlan(
    row: any,
    timeFrameSettings: any[],
    depotId: string,
    distributorId: string,
    cycleId: string,
    weeks: any[],
    pendingPlansByCallCenter: Map<string, Map<string, moment.Moment[]>>,
    callCenterMap: Map<string, any>,
    outletMap: Map<string, any>,
    existingPlansByWeekAndCallCenter: Map<string, Map<string, any[]>>,
    existingPlansByWeekAndOutlet: Map<string, Map<string, any[]>>,
    i18n: I18nContext,
  ) {
    // Validate required fields
    if (!row.day || !row.callCenter || !row.outlet || !row.week) {
      throw new HttpException(
        await i18n.translate(`plan.call_plans.missing_required_fields`, {
          args: { fieldName: 'callCenter or outlet or week' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    const callCenterContact = callCenterMap.get(row.callCenter);
    if (!callCenterContact) {
      throw new HttpException(await i18n.translate(`plan.call_plans.not_found_call_center`), HttpStatus.BAD_REQUEST);
    }

    const outlet = outletMap.get(String(row.outlet));
    if (!outlet) {
      throw new HttpException(await i18n.translate(`plan.call_plans.not_found_outlet`), HttpStatus.BAD_REQUEST);
    }

    // Validate day number is between 1-7
    const dayNumber = parseInt(row.day);
    if (isNaN(dayNumber) || dayNumber < 1 || dayNumber > 7) {
      throw new HttpException(await i18n.translate(`plan.call_plans.invalid_day_number`), HttpStatus.BAD_REQUEST);
    }

    // Validate week number is between 1-4
    const weekNumber = parseInt(row.week);
    if (isNaN(weekNumber) || weekNumber < 1 || weekNumber > 4) {
      throw new HttpException(await i18n.translate(`plan.call_plans.invalid_week_number`), HttpStatus.BAD_REQUEST);
    }

    const week = weeks.find((w) => w.weekNumber === weekNumber);

    // Calculate the actual date based on week start time and day number
    const weekStartTime = moment(week.startTime).tz(process.env.TZ);
    const planTime = weekStartTime.clone().add(dayNumber - 1, 'days');

    // Get current time
    const currentTime = moment().tz(process.env.TZ);

    // Validate that the plan is not in the past (only check date)
    if (planTime.isBefore(currentTime, 'day')) {
      throw new HttpException(await i18n.translate(`plan.call_plans.cannot_create_past_date`), HttpStatus.BAD_REQUEST);
    }

    // Get plans for this day from the maps
    const dayKey = planTime.format('YYYY-MM-DD');
    const dayPlans =
      existingPlansByWeekAndCallCenter
        .get(week.id)
        ?.get(callCenterContact.id)
        ?.filter((plan) => moment(plan.day).tz(process.env.TZ).format('YYYY-MM-DD') === dayKey) || [];

    const endHour = 23;
    const endMinute = 45; // Last slot should be at 23:45 for 15-minute intervals

    const pendingPlans = pendingPlansByCallCenter.get(callCenterContact.id)?.get(dayKey) || [];
    const availableSlotsInDay = await this.calculateAvailableSlotsInDay(null, { distributorId, callCenter: { id: callCenterContact.id } }, planTime, dayPlans, i18n);

    // additionalFilter
    const availableSlots = availableSlotsInDay.filter((slot) => {
      const slotTime = slot.format('HH:mm');
      // Check pending plans
      return !pendingPlans.some((pendingTime) => pendingTime.format('HH:mm') === slotTime);
    });

    const nextSlotTime = availableSlots[0];

    if (nextSlotTime.hour() > endHour || (nextSlotTime.hour() === endHour && nextSlotTime.minute() > endMinute)) {
      throw new HttpException(await i18n.translate(`plan.call_plans.no_more_slots`), HttpStatus.BAD_REQUEST);
    }

    // Check if this outlet is already planned for this week
    const weekPlans = existingPlansByWeekAndOutlet.get(week.id)?.get(outlet.id) || [];
    if (weekPlans.length > 0) {
      // If the first plan is still pending, update its day/displayDay and return it
      if (weekPlans[0].callStatus === CallCenterStatus.PENDING) {
        const pendingPlan = { ...weekPlans[0] };
        pendingPlan.day = convertMomentToCallPlanDate(nextSlotTime);
        pendingPlan.displayDay = convertMomentToCallPlanDate(nextSlotTime);
        return pendingPlan;
      } else {
        // Otherwise, throw an error that the outlet is already planned
        throw new HttpException(
          await i18n.translate(`plan.call_plans.outlet_already_planned_for_week`, { args: { outletKey: outlet.businessPartnerKey } }),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // Create a new call plan if not already planned
    return this._repository.create({
      day: convertMomentToCallPlanDate(nextSlotTime),
      displayDay: convertMomentToCallPlanDate(nextSlotTime),
      week: { id: week.id },
      cycle: { id: cycleId },
      callCenter: { id: callCenterContact.id },
      outlet: { id: outlet.id },
      depotId: depotId,
      distributorId: distributorId,
      callStatus: CallCenterStatus.PENDING,
      rescheduled: false,
      priority: 0,
      hasOrder: false,
      creator: CallPlanCreatorType.MANAGER,
    });
  }

  /**
   *
   * @param contact
   */
  async getTodayCallPlans({ contact, currentUser }: { contact: any; currentUser: any }): Promise<{ callCenter: any; plans: any }> {
    try {
      const startOfCurrencyDate = moment().tz(process.env.TZ).startOf('day').toDate();
      const endOfCurrencyDate = moment().tz(process.env.TZ).endOf('day').toDate();
      const plans = await this._repository.find({
        where: [
          {
            callCenter: { id: contact.id },
            rescheduled: false,
            day: Between(startOfCurrencyDate, endOfCurrencyDate),
          },
          {
            callCenter: { id: contact.id },
            rescheduled: true,
            rescheduledDay: Between(startOfCurrencyDate, endOfCurrencyDate),
          },
        ],
        relations: ['outlet', 'callCenter', 'histories', 'cycle', 'week'],
        order: {
          displayDay: 'ASC',
        },
      });
      // Sort histories by updatedAt in descending order
      plans.forEach((plan) => {
        if (plan.histories?.length) {
          plan.histories.sort((a, b) => moment(b.updatedAt).valueOf() - moment(a.updatedAt).valueOf());
        }
      });

      const outlets = await this._businessPartnerService.find({
        where: { id: In(plans.map((plan) => plan.outlet.id)) },
      });

      const outletsWithRelations = await this._businessPartnerService.attachBusinessPartnersRelationData(outlets);
      const outletMap = new Map(outletsWithRelations.map((outlet) => [outlet.id, outlet]));

      const contactsWithRelations = await this._businessPartnerContactService.attachBusinessPartnerContactRelationData(contact);

      return {
        callCenter: contactsWithRelations,
        plans: plans.map((plan) => ({
          ...plan,
          outlet: outletMap.get(plan.outlet.id),
        })),
      };
    } catch (e) {
      return null;
    }
  }

  async validateWeeklyOutletCallPlan(cycleId: string, weekId: string, outletId: string, excludePlanId: string, creator: CallPlanCreatorType, i18n: I18nContext) {
    const weeklyOutletPlanLimit = Number(process.env.CALL_PLAN_OUTLET_WEEKLY_LIMIT);
    const searchCondition: FindOptionsWhere<CallPlanning> = {
      week: { id: weekId },
      outlet: { id: outletId },
      isDeleted: false,
    };

    if (cycleId) {
      searchCondition.cycle = { id: cycleId };
    }

    if (excludePlanId) {
      searchCondition.id = Not(excludePlanId);
    }

    const isValidPlanLimit = _.isNumber(weeklyOutletPlanLimit) && weeklyOutletPlanLimit > 0;

    if (!isValidPlanLimit && creator === CallPlanCreatorType.AGENT) {
      return;
    }

    const weeklyOutletPlans = await this._repository.find({
      where: searchCondition,
    });

    if (!excludePlanId && isValidPlanLimit && (weeklyOutletPlans || [])?.length > weeklyOutletPlanLimit) {
      throw new HttpException(await i18n.translate(`plan.call_plans.outlet_week_plan_number_exceed_limit`), HttpStatus.BAD_REQUEST);
    }

    if (creator === CallPlanCreatorType.MANAGER) {
      const managerPlan = (weeklyOutletPlans || []).find((plan) => plan.creator === CallPlanCreatorType.MANAGER && plan.id !== excludePlanId);
      if (managerPlan) {
        throw new HttpException(await i18n.translate(`plan.call_plans.outlet_already_planned_for_week`), HttpStatus.BAD_REQUEST);
      }
    }
  }

  /**
   *
   * @param data
   * @param i18n
   */
  async createCallPlan(
    data: {
      cycleId: string;
      day: number;
      week: number;
      outlet: string;
      contact: string;
      hour?: string; // Optional hour in format "HH:mm",
      creator: CallPlanCreatorType;
    },
    i18n: I18nContext,
    currentContact: any,
  ) {
    if (data.day < 1 || data.day > 7) {
      throw new HttpException(await i18n.translate(`plan.call_plans.invalid_day_number`), HttpStatus.BAD_REQUEST);
    }
    if (data.week < 1 || data.week > 4) {
      throw new HttpException(await i18n.translate(`plan.call_plans.invalid_week_number`), HttpStatus.BAD_REQUEST);
    }

    // Validate hour format if provided
    if (data.hour) {
      const hourRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!hourRegex.test(data.hour)) {
        throw new HttpException(await i18n.translate(`plan.call_plans.invalid_hour_format`), HttpStatus.BAD_REQUEST);
      }
    }

    // Get week by cycle and week number
    const week = await this._planWeekService.findOne({
      where: {
        cycle: { id: data.cycleId },
        weekNumber: data.week,
      },
    });

    if (!week) {
      throw new HttpException(await i18n.translate(`plan.call_plans.week_not_found`), HttpStatus.BAD_REQUEST);
    }

    // Find outlet and contact by businessPartnerKey
    const outlet = await this._businessPartnerService.findOne({
      where: { businessPartnerKey: data.outlet },
    });

    if (!outlet) {
      throw new HttpException(await i18n.translate(`plan.call_plans.not_found_outlet`), HttpStatus.BAD_REQUEST);
    }

    const contact = await this._businessPartnerContactService.findOne({
      where: { businessPartnerContactKey: data.contact },
    });

    if (!contact) {
      throw new HttpException(await i18n.translate(`plan.call_pls.not_found_call_center`), HttpStatus.BAD_REQUEST);
    }

    const { depotIds, distributorIds } = await this._businessPartnerRelationService.findAboveRelationIdsFromOutlet(outlet.id);
    const distributorId: string = ((distributorIds && distributorIds[0]) || '') as string;
    const depotId: string = ((depotIds && depotIds[0]) || '') as string;

    await this.validateWeeklyOutletCallPlan(data.cycleId, week.id, outlet.id, null, data.creator, i18n);

    const currentTime = moment().tz(process.env.TZ);
    const planTime = moment(week.startTime)
      .tz(process.env.TZ)
      .add(data.day - 1, 'days');

    // Validate that the plan is not in the past (only check date)
    if (planTime.isBefore(currentTime, 'day')) {
      throw new HttpException(await i18n.translate(`plan.call_plans.cannot_create_past_date`), HttpStatus.BAD_REQUEST);
    }

    if (data.creator === CallPlanCreatorType.AGENT && !planTime.isSame(currentTime, 'day')) {
      throw new HttpException(await i18n.translate(`plan.call_plans.agent_created_plan_invalid_date`), HttpStatus.BAD_REQUEST);
    }

    const dayPlans = await this._repository.find({
      where: {
        callCenter: { id: contact.id },
        displayDay: Between(planTime.startOf('day').toDate(), planTime.endOf('day').toDate()),
        isDeleted: false,
      },
    });

    const availableSlots = await this.calculateAvailableSlotsInDay(data.hour, { distributorId, callCenter: { id: contact.id } }, planTime, dayPlans, i18n);

    const targetTime = availableSlots[0];

    const callPlanData: DeepPartial<CallPlanning> = {
      day: convertMomentToCallPlanDate(targetTime),
      displayDay: convertMomentToCallPlanDate(targetTime),
      week: { id: week.id },
      cycle: { id: data.cycleId },
      callCenter: { id: contact.id },
      outlet: { id: outlet.id },
      depotId,
      distributorId,
      callStatus: CallCenterStatus.PENDING,
      rescheduled: false,
      priority: 0,
      hasOrder: false,
      creator: data.creator,
    };

    const savedPlan = await this._repository.save(callPlanData);
    // Fire-and-forget notification
    if (currentContact) {
      const content = await i18n.translate('notification.assigned', {
        args: { callCenterManager: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2 },
      });
      this.notificationService
        .create({
          fromUserId: currentContact.id,
          toUserId: savedPlan.callCenter.id,
          userName: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2,
          userRole: currentContact.businessPartnerContactPersonRole,
          status: NotificationStatus.UNREAD,
          content: content as string,
          type: NotificationType.ASSIGNED,
          callPlanId: savedPlan.id,
          depotId,
          title: await i18n.translate(`plan.call_plans.new_task`),
          data: { outlet: outlet.businessPartnerKey, date: savedPlan.day },
        })
        .catch((e) => printLog(e?.toString ? e.toString() : JSON.stringify(e)));
    }
    return savedPlan;
  }

  /**
   *
   * @param data
   * @param i18n
   * @returns
   */
  async createCallPlanByDate(
    data: {
      date: string; // Format: YYYY-MM-DD
      outlet: string;
      contact: string;
      hour?: string; // Optional hour in format "HH:mm",
      creator: CallPlanCreatorType;
    },
    i18n: I18nContext,
    currentContact: any,
  ) {
    // Validate date format
    if (!moment(data.date, 'YYYY-MM-DD', true).isValid()) {
      throw new HttpException(await i18n.translate(`plan.call_plans.invalid_date_format`), HttpStatus.BAD_REQUEST);
    }

    // Validate hour format if provided
    if (data.hour) {
      const hourRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!hourRegex.test(data.hour)) {
        throw new HttpException(await i18n.translate(`plan.call_plans.invalid_hour_format`), HttpStatus.BAD_REQUEST);
      }
    }

    // Find outlet and contact by businessPartnerKey
    const outlet = await this._businessPartnerService.findOne({
      where: { businessPartnerKey: data.outlet },
    });

    if (!outlet) {
      throw new HttpException(await i18n.translate(`plan.call_plans.not_found_outlet`), HttpStatus.BAD_REQUEST);
    }

    const contact = await this._businessPartnerContactService.findOne({
      where: { businessPartnerContactKey: data.contact },
    });

    if (!contact) {
      throw new HttpException(await i18n.translate(`plan.call_pls.not_found_call_center`), HttpStatus.BAD_REQUEST);
    }

    const { depotIds, distributorIds } = await this._businessPartnerRelationService.findAboveRelationIdsFromOutlet(outlet.id);
    const distributorId: string = ((distributorIds && distributorIds[0]) || '') as string;
    const depotId: string = ((depotIds && depotIds[0]) || '') as string;

    // Get current time
    const currentTime = moment().tz(process.env.TZ);
    const planTime = moment(data.date).tz(process.env.TZ);

    // Check if plan is in the past
    if (planTime.isBefore(currentTime, 'day')) {
      throw new HttpException(await i18n.translate(`plan.call_plans.cannot_create_past_date`), HttpStatus.BAD_REQUEST);
    }

    if (data.creator === CallPlanCreatorType.AGENT && !planTime.isSame(currentTime, 'day')) {
      throw new HttpException(await i18n.translate(`plan.call_plans.agent_created_plan_invalid_date`), HttpStatus.BAD_REQUEST);
    }

    const targetPlanWeek = await this._planWeekService.findWeekWithinSevenDaysOfDate(data.date);

    if (!targetPlanWeek?.cycle?.id) {
      throw new HttpException(
        await await i18n.translate(`common.not_found`, {
          args: { fieldName: 'cycle' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    // Find any week in the cycle
    const week = await this._planWeekService.findOne({
      where: {
        cycle: { id: targetPlanWeek?.cycle?.id },
        startTime: LessThanOrEqual(planTime.toDate()),
      },
      order: {
        startTime: 'DESC',
      },
    });

    if (!week) {
      throw new HttpException(await i18n.translate(`plan.call_plans.week_not_found`), HttpStatus.BAD_REQUEST);
    }

    await this.validateWeeklyOutletCallPlan(null, week.id, outlet.id, null, data.creator, i18n);

    const dayPlans = await this._repository.find({
      where: {
        callCenter: { id: contact.id },
        displayDay: Between(planTime.startOf('day').toDate(), planTime.endOf('day').toDate()),
        isDeleted: false,
      },
    });

    const availableSlots = await this.calculateAvailableSlotsInDay(data.hour, { distributorId, callCenter: { id: contact.id } }, planTime, dayPlans, i18n);

    const targetTime = availableSlots[0];

    const callPlanData: DeepPartial<CallPlanning> = {
      day: convertMomentToCallPlanDate(targetTime),
      displayDay: convertMomentToCallPlanDate(targetTime),
      week: { id: week.id },
      cycle: { id: targetPlanWeek?.cycle.id },
      callCenter: { id: contact.id },
      outlet: { id: outlet.id },
      depotId: depotId,
      distributorId: distributorId,
      callStatus: CallCenterStatus.PENDING,
      rescheduled: false,
      priority: 0,
      hasOrder: false,
      creator: data.creator,
    };

    const savedPlan = await this._repository.save(callPlanData);
    // Fire-and-forget notification
    if (currentContact) {
      const content = await i18n.translate('notification.assigned', {
        args: { callCenterManager: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2 },
      });
      this.notificationService
        .create({
          fromUserId: currentContact.id,
          toUserId: savedPlan.callCenter.id,
          userName: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2,
          userRole: currentContact.businessPartnerContactPersonRole,
          status: NotificationStatus.UNREAD,
          content: content as string,
          type: NotificationType.ASSIGNED,
          callPlanId: savedPlan.id,
          depotId: depotId,
          title: await i18n.translate('plan.call_plans.new_task'),
          data: { outlet: outlet?.businessPartnerKey, date: savedPlan.day },
        })
        .catch((e) => printLog(e?.toString ? e.toString() : JSON.stringify(e)));
    }
    return savedPlan;
  }

  /**
   *
   * @param id
   * @param data
   * @param i18n
   * @param currentContact
   */
  async updateCallPlan(
    id: string,
    data: {
      day: number;
      week: number;
      hour?: string; // Optional hour in format "HH:mm"
      reschedule: boolean;
      requester: CallPlanCreatorType;
    },
    i18n: I18nContext,
    currentContact: any,
  ) {
    if (data.day < 1 || data.day > 7) {
      throw new HttpException(await i18n.translate(`plan.call_plans.invalid_day_number`), HttpStatus.BAD_REQUEST);
    }
    if (data.week < 1 || data.week > 4) {
      throw new HttpException(await i18n.translate(`plan.call_plans.invalid_week_number`), HttpStatus.BAD_REQUEST);
    }

    // Validate hour format if provided
    if (data.hour) {
      const hourRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!hourRegex.test(data.hour)) {
        throw new HttpException(await i18n.translate(`plan.call_plans.invalid_hour_format`), HttpStatus.BAD_REQUEST);
      }
    }

    const existingPlan = await this._repository.findOne({
      where: { id, isDeleted: false },
      relations: ['cycle', 'week', 'outlet', 'callCenter'],
    });

    if (!existingPlan) {
      throw new HttpException(await i18n.translate(`plan.call_plans.plan_not_found`), HttpStatus.BAD_REQUEST);
    }

    // Get current time
    const currentTime = moment().tz(process.env.TZ);
    const startSecondDayOfWeek = moment().tz(process.env.TZ).startOf('week').add(1, 'days');
    const planTime = moment(existingPlan.displayDay).tz(process.env.TZ);

    // Check if plan is in the past
    if (planTime.isBefore(startSecondDayOfWeek, 'day')) {
      throw new HttpException(await i18n.translate(`plan.call_plans.cannot_update_past_date`), HttpStatus.BAD_REQUEST);
    }

    // Get week by cycle and week number
    const week = await this._planWeekService.findOne({
      where: {
        cycle: { id: existingPlan.cycle.id },
        weekNumber: data.week,
      },
    });

    if (!week) {
      throw new HttpException(await i18n.translate(`plan.call_plans.week_not_found`), HttpStatus.BAD_REQUEST);
    }

    // Calculate new plan date
    const newPlanTime = moment(week.startTime)
      .tz(process.env.TZ)
      .add(data.day - 1, 'days');

    // Check if new date is in the past
    if (newPlanTime.isBefore(currentTime, 'day')) {
      throw new HttpException(await i18n.translate(`plan.call_plans.cannot_create_past_date`), HttpStatus.BAD_REQUEST);
    }

    if (existingPlan.creator === CallPlanCreatorType.AGENT && data.requester === CallPlanCreatorType.AGENT && !newPlanTime.isSame(currentTime, 'day')) {
      throw new HttpException(await i18n.translate(`plan.call_plans.agent_created_plan_invalid_date`), HttpStatus.BAD_REQUEST);
    }

    await this.validateWeeklyOutletCallPlan(existingPlan.cycle.id, week.id, existingPlan.outlet.id, id, existingPlan.creator, i18n);

    const dayPlans = await this._repository.find({
      where: {
        callCenter: { id: existingPlan.callCenter.id },
        displayDay: Between(newPlanTime.startOf('day').toDate(), newPlanTime.endOf('day').toDate()),
        isDeleted: false,
        id: Not(id), // Exclude current plan
      },
    });

    const availableSlots = await this.calculateAvailableSlotsInDay(data.hour, existingPlan, newPlanTime, dayPlans, i18n);
    const targetTime = availableSlots[0];

    // existingPlan.day = targetTime.toDate();
    existingPlan.week = week;
    existingPlan.rescheduled = true; // data.reschedule;
    existingPlan.rescheduledDay = convertMomentToCallPlanDate(targetTime);
    existingPlan.callStatus = CallCenterStatus.RESCHEDULED;
    existingPlan.displayDay = convertMomentToCallPlanDate(targetTime);

    const updatedPlan = await this._repository.save(existingPlan);
    // Fire-and-forget notification
    if (currentContact) {
      const content = await i18n.translate('notification.rescheduled', {
        args: { callCenter: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2 },
      });
      this.notificationService
        .create({
          fromUserId: currentContact.id,
          toUserId: currentContact.businessPartnerContactPersonRole === ConstantRoles.CALL_CENTER ? null : updatedPlan.callCenter.id,
          userName: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2,
          userRole: currentContact.businessPartnerContactPersonRole,
          status: NotificationStatus.UNREAD,
          content: content as string,
          type: NotificationType.RESCHEDULED,
          callPlanId: updatedPlan.id,
          depotId: existingPlan.depotId,
          title: await i18n.translate('plan.call_plans.call_plan'),
          data: { outlet: existingPlan.outlet.businessPartnerKey, date: updatedPlan.day },
        })
        .catch((e) => printLog(e?.toString ? e.toString() : JSON.stringify(e)));
    }
    return updatedPlan;
  }

  async updateCallPlanByDate(
    id: string,
    data: {
      date: string; // Format: YYYY-MM-DD
      hour?: string; // Optional hour in format "HH:mm"
      reasonKey: string;
      reschedule: boolean;
      requester: CallPlanCreatorType;
    },
    i18n: I18nContext,
    currentContact: any,
  ) {
    // Validate date format
    if (!moment(data.date, 'YYYY-MM-DD', true).isValid()) {
      throw new HttpException(await i18n.translate(`plan.call_plans.invalid_date_format`), HttpStatus.BAD_REQUEST);
    }

    // Validate hour format if provided
    if (data.hour) {
      const hourRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!hourRegex.test(data.hour)) {
        throw new HttpException(await i18n.translate(`plan.call_plans.invalid_hour_format`), HttpStatus.BAD_REQUEST);
      }
    }

    // Find existing plan
    const existingPlan = await this._repository.findOne({
      where: { id, isDeleted: false },
      relations: ['cycle', 'week', 'outlet', 'callCenter'],
    });

    if (!existingPlan) {
      throw new HttpException(await i18n.translate(`plan.call_plans.plan_not_found`), HttpStatus.BAD_REQUEST);
    }

    // Get current time
    const currentTime = moment().tz(process.env.TZ);
    const newPlanTime = moment(data.date).tz(process.env.TZ);

    // Check if new date is in the past
    if (newPlanTime.isBefore(currentTime, 'day')) {
      throw new HttpException(await i18n.translate(`plan.call_plans.cannot_create_past_date`), HttpStatus.BAD_REQUEST);
    }

    if (existingPlan.creator === CallPlanCreatorType.AGENT && data.requester === CallPlanCreatorType.AGENT && !newPlanTime.isSame(currentTime, 'day')) {
      throw new HttpException(await i18n.translate(`plan.call_plans.agent_created_plan_invalid_date`), HttpStatus.BAD_REQUEST);
    }

    const targetPlanWeek = await this._planWeekService.findWeekWithinSevenDaysOfDate(data.date);

    if (!targetPlanWeek?.cycle?.id) {
      throw new HttpException(
        await await i18n.translate(`common.not_found`, {
          args: { fieldName: 'cycle' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    // Find the week that contains the target date
    const week = await this._planWeekService.findOne({
      where: {
        cycle: { id: targetPlanWeek.cycle.id },
        startTime: LessThanOrEqual(newPlanTime.toDate()),
      },
      order: {
        startTime: 'DESC',
      },
    });

    if (!week) {
      throw new HttpException(await i18n.translate(`plan.call_plans.week_not_found`), HttpStatus.BAD_REQUEST);
    }

    await this.validateWeeklyOutletCallPlan(existingPlan.cycle.id, week.id, existingPlan.outlet.id, id, existingPlan.creator, i18n);

    const dayPlans = await this._repository.find({
      where: {
        callCenter: { id: existingPlan.callCenter.id },
        displayDay: Between(newPlanTime.startOf('day').toDate(), newPlanTime.endOf('day').toDate()),
        isDeleted: false,
        id: Not(existingPlan.id), // Exclude current plan
      },
    });

    const availableSlots = await this.calculateAvailableSlotsInDay(data.hour, existingPlan, newPlanTime, dayPlans, i18n);
    const targetTime = availableSlots[0];

    // existingPlan.day = targetTime.toDate();
    existingPlan.week = week;
    existingPlan.rescheduled = true; // data.reschedule;
    existingPlan.rescheduledDay = convertMomentToCallPlanDate(targetTime);
    existingPlan.callStatus = CallCenterStatus.RESCHEDULED;
    existingPlan.displayDay = convertMomentToCallPlanDate(targetTime);
    existingPlan.reasonKey = data.reasonKey;
    existingPlan.cycle = targetPlanWeek.cycle;

    const updatedPlan = await this._repository.save(existingPlan);
    // Fire-and-forget notification
    if (currentContact) {
      const content = await i18n.translate('notification.rescheduled', {
        args: { callCenter: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2 },
      });
      this.notificationService
        .create({
          fromUserId: currentContact.id,
          toUserId: currentContact.businessPartnerContactPersonRole === ConstantRoles.CALL_CENTER ? null : updatedPlan.callCenter.id,
          userName: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2,
          userRole: currentContact.businessPartnerContactPersonRole,
          status: NotificationStatus.UNREAD,
          content: content as string,
          type: NotificationType.RESCHEDULED,
          callPlanId: updatedPlan.id,
          depotId: existingPlan.depotId,
          title: await i18n.translate('plan.call_plans.call_plan'),
          data: { outlet: existingPlan.outlet.businessPartnerKey, date: updatedPlan.day },
        })
        .catch((e) => printLog(e?.toString ? e.toString() : JSON.stringify(e)));
    }
    return updatedPlan;
  }

  /**
   *
   * @param contact
   * @param depot
   * @param distributor
   * @param cycleId
   * @param sortBy
   * @param order
   * @param page
   * @param limit
   */
  async getCallPlans({
    contactIds,
    depotIds,
    distributorIds,
    cycleId,
    sortBy = 'displayDay',
    order = 'ASC',
    offset = 0,
    limit = 10,
    search,
  }: {
    contactIds?: string[];
    depotIds?: string[];
    distributorIds?: string[];
    cycleId: string;
    sortBy?: string;
    order?: 'ASC' | 'DESC';
    offset?: number;
    limit?: number;
    search?: string;
  }) {
    try {
      const whereConditions: any[] = [];

      const baseConditions = {
        cycle: { id: cycleId },
        isDeleted: false,
        // rescheduled: false,
      };

      // if (!isEmptyObjectOrArray(contactIds)) {
      //   whereConditions.push({
      //     ...baseConditions,
      //     callCenter: { id: In(contactIds) },
      //   });
      // }

      // if (!isEmptyObjectOrArray(depotIds)) {
      //   whereConditions.push({
      //     ...baseConditions,
      //     depotId: In(depotIds),
      //   });
      // }

      if (!isEmptyObjectOrArray(distributorIds)) {
        whereConditions.push({
          ...baseConditions,
          distributorId: In(distributorIds),
        });
      }

      if (search) {
        whereConditions.push({
          ...baseConditions,
          outlet: {
            businessPartnerName1: ILike(`%${search}%`),
          },
        });
      }

      // const rescheduledConditions = {
      //   cycle: { id: cycleId },
      //   rescheduled: true,
      // };

      // if (!isEmptyObjectOrArray(contactIds)) {
      //   whereConditions.push({
      //     ...rescheduledConditions,
      //     callCenter: { id: In(contactIds) },
      //   });
      // }

      // if (!isEmptyObjectOrArray(depotIds)) {
      //   whereConditions.push({
      //     ...rescheduledConditions,
      //     depotId: In(depotIds),
      //   });
      // }

      // if (!isEmptyObjectOrArray(distributorIds)) {
      //   whereConditions.push({
      //     ...rescheduledConditions,
      //     distributorId: In(distributorIds),
      //   });
      // }

      // if (search) {
      //   whereConditions.push({
      //     ...rescheduledConditions,
      //     outlet: {
      //       businessPartnerName1: ILike(`%${search}%`),
      //     },
      //   });
      // }

      if (whereConditions.length < 1) {
        whereConditions.push(baseConditions);
      }

      const sortByPaths = sortBy?.split('.') || [];
      const sortOrder =
        sortByPaths.length < 2
          ? {
              [sortBy]: order,
            }
          : {
              [sortByPaths[0]]: {
                [sortByPaths[1]]: order,
              },
            };

      const [plans, total] = await this._repository.findAndCount({
        where: whereConditions,
        relations: ['outlet', 'callCenter', 'cycle', 'week'],
        order: sortOrder,
        skip: offset,
        take: limit,
      });

      const planDistributorIds = [],
        outlets = [];
      for (let i = 0; i < plans.length; i++) {
        const currentPlan = plans[i];
        planDistributorIds.push(currentPlan.distributorId);
        outlets.push(currentPlan.outlet);
      }

      const [distributors, outletsWithRelations] = await Promise.all([
        this._businessPartnerService.find({
          where: { id: In([...new Set([...planDistributorIds])]) },
        }),
        this._businessPartnerService.attachBusinessPartnersRelationData(outlets),
      ]);

      const outletMap = new Map(outletsWithRelations.map((outlet) => [outlet.id, outlet]));
      const distributorMap = new Map(distributors.map((distributor) => [distributor.id, distributor]));

      const transformedPlans = await this.transformOutletToCallPlan(
        plans.map((plan) => {
          const transformedPlan: any = {
            ...plan,
            outlet: outletMap.get(plan.outlet.id),
            distributor: distributorMap.get(plan.distributorId),
          };

          if (plan.day) {
            transformedPlan.dayNumber = getCallPlanDayNumber(plan.day);
          }

          return transformedPlan;
        }),
      );
      return {
        totalRecords: total,
        plans: transformedPlans,
      };
    } catch (e) {
      console.error('Error in getCallPlans:', e);
      return {
        totalRecords: 0,
        callCenter: null,
        plans: [],
      };
    }
  }

  /**
   * Calculate metrics from a list of call plans
   */
  async calculateMetrics(callPlans: ICallPlanItem[], statistics: any, callCenter: any): Promise<ICallPlanMetrics> {
    const strikeRate = await this.calculateStrikeRate(callCenter);
    const callCoverage = await this.calculateCoverage(callCenter);
    const activeSelling = await this.calculateASO(callCenter);
    return {
      strikeRate: {
        value: strikeRate.totalPlan > 0 ? this.calculatePercent(strikeRate.completedPlan, strikeRate.totalPlan) : 0,
        target: roundToNearestInt(strikeRate.target),
      },
      callCoverage: {
        value: callCoverage.totalPlan > 0 ? this.calculatePercent(callCoverage.completedPlan, callCoverage.totalPlan) : 0,
        target: roundToNearestInt(callCoverage.target),
      },
      activeSelling: {
        value: roundToNearestInt(activeSelling?.current),
        target: roundToNearestInt(activeSelling.target),
      },
    };
  }

  private async calculateASO(callCenter: BusinessPartnerContact, month?: number) {
    // If month is not provided, use current month
    const now = moment().tz(process.env.TZ);
    const targetMonth = month ? month : now.month() + 1; // moment month is 0-based
    const year = now.year();
    const res = {
      current: 0,
      target: 0,
      error: false,
      errorMessage: '',
    };

    //total outlet có invoice - get ASO -> maboValue
    const statistic = await this.getAgentStatistics([callCenter.businessPartnerContactKey]);
    const countOutlet = await this._businessPartnerRelationService.countRelations(callCenter.id, BusinessPartnerRelationType.CONTACT_OUTLET);
    res.current = statistic?.length && countOutlet > 0 ? (statistic[0]?.maboValue || 0) / countOutlet : 0;
    //Target
    const target = await this._targetSettingsService.findOne({ where: { agentId: callCenter.businessPartnerContactKey, outletId: null, month: targetMonth, year } });

    res.target = target?.activeSellingOutlet || 0;
    return res;
  }

  /**
   * @returns Promise<{ value: number; target: number; error: boolean; errorMessage: string; }>
   * Calculates the number of completed calls (value) and total calls (target) for a call center in a given month
   * @param callCenter
   * @param month
   */
  private async calculateStrikeRate(callCenter: BusinessPartnerContact, month?: number) {
    // If month is not provided, use current month
    const now = moment().tz(process.env.TZ);
    const targetMonth = month ? month : now.month() + 1; // moment month is 0-based
    const year = now.year();
    // Get start and end of the month
    const startOfMonth = moment.tz({ year, month: targetMonth - 1, day: 1 }, process.env.TZ).startOf('month');
    const endOfMonth = startOfMonth.clone().endOf('month');

    const endCurrentDate = now.clone().endOf('day');
    const endDateQuery = endOfMonth.isAfter(endCurrentDate) ? endCurrentDate : endOfMonth;

    const res = {
      completedPlan: 0,
      totalPlan: 0,
      target: 0,
      error: false,
      errorMessage: '',
    };
    try {
      // Count completed calls
      res.completedPlan = await this._repository.count({
        where: {
          callCenter: { id: callCenter.id },
          displayDay: Between(startOfMonth.toDate(), endDateQuery.toDate()),
          callStatus: CallCenterStatus.COMPLETED,
          hasOrder: true,
          creator: CallPlanCreatorType.MANAGER,
        },
      });
      // Count all calls
      res.totalPlan = await this._repository.count({
        where: {
          callCenter: { id: callCenter.id },
          displayDay: Between(startOfMonth.toDate(), endDateQuery.toDate()),
          callStatus: CallCenterStatus.COMPLETED,
          creator: CallPlanCreatorType.MANAGER,
        },
      });
      //Target
      const target = await this._targetSettingsService.findOne({ where: { agentId: callCenter.businessPartnerContactKey, outletId: null, month: targetMonth, year } });
      res.target = target?.strikeRate || 0;
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }
    return res;
  }

  private async calculateCoverage(callCenter: BusinessPartnerContact, month?: number) {
    // If month is not provided, use current month
    const now = moment().tz(process.env.TZ);
    const targetMonth = month ? month : now.month() + 1; // moment month is 0-based
    const year = now.year();
    // Get start and end of the month
    const startOfMonth = moment.tz({ year, month: targetMonth - 1, day: 1 }, process.env.TZ).startOf('month');
    const endOfMonth = startOfMonth.clone().endOf('month');

    const endCurrentDate = now.clone().endOf('day');
    const endDateQuery = endOfMonth.isAfter(endCurrentDate) ? endCurrentDate : endOfMonth;

    const res = {
      completedPlan: 0,
      totalPlan: 0,
      target: 0,
      error: false,
      errorMessage: '',
    };
    try {
      // Count completed calls
      res.completedPlan = await this._repository.count({
        where: {
          callCenter: { id: callCenter.id },
          displayDay: Between(startOfMonth.toDate(), endDateQuery.toDate()),
          callStatus: CallCenterStatus.COMPLETED,
          creator: CallPlanCreatorType.MANAGER,
        },
      });
      // Count all calls
      res.totalPlan = await this._repository.count({
        where: {
          callCenter: { id: callCenter.id },
          displayDay: Between(startOfMonth.toDate(), endDateQuery.toDate()),
          creator: CallPlanCreatorType.MANAGER,
        },
      });
      //Target
      const target = await this._targetSettingsService.findOne({ where: { agentId: callCenter.businessPartnerContactKey, outletId: null, month: targetMonth, year } });
      res.target = target?.callCoverage || 0;
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }
    return res;
  }

  async transformOutletToCallPlan(callPlans: any[]): Promise<ICallPlanItem[]> {
    const outletIds = [...new Set(callPlans.map((cp) => cp?.outlet?.id))];
    const startOfMonth = moment().tz(process.env.TZ).startOf('month').toDate();
    const endOfMonth = moment().tz(process.env.TZ).endOf('month').toDate();
    //Get visited data
    const [visitedData, outletsCallHistoriesCount] = await Promise.all([
      this.outletJourneyPlanningService.getLatestJourneyPlanVisits(callPlans.map((callPlan) => callPlan.outlet.businessPartnerKey)),
      this.callPlanningHistoriesService.getOutletCallHistoriesCount(outletIds, startOfMonth, endOfMonth),
    ]);
    const outletCallHistoriesCountMap = new Map<string, number>(outletsCallHistoriesCount.map((hc) => [hc.outletId, Number(hc.callCount || 0)]));
    return callPlans.map((callPlan) => {
      const outlet = callPlan.outlet;
      const outletCallHistoriesCount = outletCallHistoriesCountMap.get(callPlan.outlet.id) || 0;
      let contact = outlet.communications?.find((comm) => comm.communicationType === 'tel');
      if (!contact) {
        contact = outlet.communications?.find((comm) => comm.communicationType === 'phone');
      }

      const contactEmail = outlet.communications?.find((comm) => comm.communicationType === 'email');
      const customer = outlet?.customers?.length ? outlet?.customers[0] : null;
      const customerSalesOrganization = customer?.customerSalesOrganizations?.length ? customer?.customerSalesOrganizations[0] : null;
      const geoGraphicalLocations = outlet?.geoGraphicalLocations?.length ? outlet?.geoGraphicalLocations[0] : null;
      const planed = visitedData?.find((data) => data.outletUcc === outlet.businessPartnerKey);
      return {
        id: callPlan.id,
        day: callPlan.day,
        dayNumber: callPlan.dayNumber || getCallPlanDayNumber(callPlan.day),
        rescheduled: callPlan.rescheduled,
        rescheduledDay: callPlan.rescheduledDay,
        rescheduledDayNumber: callPlan.rescheduled && getCallPlanDayNumber(callPlan.rescheduledDay),
        displayDay: callPlan.displayDay,
        priority: callPlan.priority,
        hasOrder: callPlan.hasOrder,
        callStatus: callPlan.callStatus,
        checkList: callPlan.checkList,
        depotId: callPlan.depotId,
        distributorId: callPlan.distributorId,
        distributorUCC: callPlan?.distributor?.businessPartnerKey,
        distributorName: callPlan?.distributor?.businessPartnerName1 || callPlan?.distributor?.businessPartnerName2 || '',
        reasonKey: callPlan.reasonKey,
        rescheduledReason: callPlan.reasonKey ? CallRescheduleReasons.find((r) => r.key === callPlan.reasonKey) : '',
        cycle: {
          cycleName: callPlan.cycle.cycleName,
          year: callPlan.cycle.year,
        },
        week: {
          weekName: callPlan.week.weekName,
          weekNumber: callPlan.week.weekNumber,
          startTime: callPlan.week.startTime,
        },
        callCenter: {
          id: callPlan.callCenter.id,
          isActive: callPlan.callCenter.isActive,
          name: callPlan.callCenter.businessPartnerContactName1 || callPlan.callCenter.businessPartnerContactName2,
          callCenterId: callPlan.callCenter.businessPartnerContactKey,
          role: callPlan.callCenter.businessPartnerContactPersonRole,
          jobTitle: callPlan.callCenter.businessPartnerContactPersonJobTitle,
          status: callPlan.callCenter.businessPartnerContactStatus,
        },
        outlet: {
          id: outlet.id,
          name: outlet.businessPartnerName1 || outlet.businessPartnerName2 || '',
          outletUCC: outlet.businessPartnerKey,
          address:
            geoGraphicalLocations?.city || geoGraphicalLocations?.houseNumber || geoGraphicalLocations?.region
              ? `${geoGraphicalLocations?.street} ${geoGraphicalLocations?.houseNumber} ${geoGraphicalLocations?.postalCode} ${geoGraphicalLocations?.city} ${geoGraphicalLocations?.region}`
              : outlet.details?.length > 0
              ? outlet.details[0].rawAddress
              : '',
          status: outlet.businessPartnerStatus,
          contact: {
            name: contact?.communicationName || outlet?.businessPartnerName2,
            phoneNumber: contact?.communicationValue || '',
            email: contactEmail?.communicationValue || '',
            role: 'Outlet Owner',
          },
          visited: planed?.visited,
          avgCallRate: `${Number((outletCallHistoriesCount || 0) / 4).toFixed(2)}/Week`,
          nps: '60%',
        },
        segments: {
          channel: customer?.customerChannel || '',
          subChannel: customerSalesOrganization?.customerSubChannel || '',
          segment: customer?.businessSegment || '',
          outletClassification: customerSalesOrganization?.outletClassification || '',
          organizationalSegment: customer?.businessOrganizationalSegment || '',
        },
        histories: callPlan.histories?.map((history: CallPlanningHistories) => ({
          startCallTime: history.startCallTime,
          endCallTime: history.endCallTime,
          location: history.location,
          locationRange: history.locationRange,
          callCenterNote: history.callCenterNote,
          outletNote: history.outletNote,
          transcripts: history.transcripts,
          status: history.status,
        })),
      };
    });
  }

  /**
   * Get complete call plan with metrics and stats
   */
  async getCallPlan({ callCenter, currentUser, plans, i18n }: { callCenter: BusinessPartnerContact; currentUser: any; plans: any; i18n?: I18nContext }): Promise<ICallPlan> {
    const transformedOutlets = await this.transformOutletToCallPlan(plans);
    const statistics = await this.omsSalesRepStatisticsService.getStatisticsForCallCenter(callCenter.businessPartnerContactKey);
    const metrics = await this.calculateMetrics(plans, statistics, callCenter);

    return {
      callCenter: {
        id: callCenter.id || '',
        isActive: callCenter.isActive,
        name: callCenter.businessPartnerContactName1 || callCenter.businessPartnerContactName2 || '',
        contactID: callCenter.businessPartnerContactKey || '',
        contactType: callCenter.businessPartnerContactType || '',
        contactRole: callCenter.businessPartnerContactPersonRole || ConstantRoles.CALL_CENTER,
        jobTitle: callCenter.businessPartnerContactPersonJobTitle || '',
        status: callCenter.businessPartnerContactStatus,
        details: callCenter.details || [],
        communications: {
          name:
            callCenter.communications?.find((comm) => comm.communicationType === 'tel' || comm.communicationType === 'phone')?.communicationName ||
            callCenter.businessPartnerContactName1 ||
            callCenter.businessPartnerContactName2 ||
            '',
          phone: callCenter.communications?.find((comm) => comm.communicationType === 'tel' || comm.communicationType === 'phone')?.communicationValue || '',
          email: callCenter.communications?.find((comm) => comm.communicationType === 'email')?.communicationValue || '',
        },
        geoGraphicalLocations: callCenter.geoGraphicalLocations || [],
        images: callCenter.images || [],
        operatingHours: callCenter.operatingHours || [],
      },
      metrics,
      stats: null,
      plans: transformedOutlets,
      date: new Date(),
      totalOutlets: transformedOutlets.length,
    };
  }

  /**
   * Calculate outlet target metrics
   */
  private calculateOutletTarget(statistic: any) {
    const salesVolume = statistic?.salesVolume;
    return {
      value: salesVolume?.target || 0,
      avpOrder: 0,
      label: 'Outlet Target (Value)',
    };
  }

  /**
   * Calculate order frequency metrics
   */
  private calculateOrderTotalVolume(statistic: any) {
    const salesValue = statistic?.salesValue;
    return {
      value: salesValue.target || 0,
      percentage: Number(salesValue.percent?.toFixed(2)) || 0,
      since: new Date().toLocaleString('default', { month: 'short' }),
    };
  }

  /**
   * Calculate average value per order
   */
  private calculateAverageFrequency(orderCaches: any) {
    const numberOfOrder = orderCaches?.length || 0;
    const theOrder = 0; //orderCaches?.length ? Number((orderCaches.length / moment().date()).toFixed(2)) : 0;
    return {
      numberOfOrder,
      theOrder,
    };
  }

  /**
   * Calculate all statistics for call plan
   */
  async calculateStats(outletUCC: string, statistics: any, i18n: I18nContext): Promise<ICallPlanStats> {
    // const fromDate = moment().tz(process.env.TZ).startOf('day').toDate();
    // const toDate = moment().tz(process.env.TZ).endOf('day').toDate();
    const orderCaches = await this.omsService.getFullCachedDataByOutletExternalIds({
      outletExternalIds: [outletUCC],
      project: {
        orders: 1,
        outlet: 1,
        outletExternalId: 1,
      },
    });
    return {
      outletTarget: this.calculateOutletTarget(statistics),
      orderTotalVolume: this.calculateOrderTotalVolume(statistics),
      avgFrequency: this.calculateAverageFrequency(orderCaches),
    };
  }

  async startCallPlan(id: string, dto: StartCallPlanDto, currentContact: any, i18n: I18nContext) {
    const { location, callCenterNote, outletNote } = dto;
    const callPlan = await this._repository.findOne({
      where: { id },
      relations: ['histories', 'outlet', 'callCenter'],
    });

    if (!callPlan) {
      const errorMessage = await i18n.translate('plan.call_plans.plan_not_found');
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    }

    /* if (callPlan.callStatus === CallCenterStatus.COMPLETED) {
      const errorMessage = await i18n.translate('plan.call_plans.already_in_progress');
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    } */

    callPlan.callStatus = CallCenterStatus.IN_PROGRESS;
    await this._repository.save(callPlan);

    const communications = await this._businessPartnerCommunicationService.findByBusinessPartner(callPlan.outlet.id);

    const { mobilePhone, phone } = (communications || []).reduce(
      (acc, c) => {
        if (c.communicationType === BusinessPartnerRelationCommunication.TEL) {
          acc.phone = c.communicationValue;
        }

        if (c.communicationType === BusinessPartnerRelationCommunication.PHONE) {
          acc.mobilePhone = c.communicationValue;
        }

        return acc;
      },
      { mobilePhone: undefined, phone: undefined },
    );

    const twilioCall = await makePhoneCall(
      generateSIPURLfromPhoneNumber(phone || mobilePhone),
      // test number myanmar
      // generateSIPURLfromPhoneNumber('+959796629662'),
      id,
      callPlan?.callCenter?.businessPartnerContactName1 || callPlan?.callCenter?.businessPartnerContactName2,
    );

    // Create new history
    const historyObj = new CallPlanningHistories();
    historyObj.startCallTime = new Date();
    historyObj.callPlanning = callPlan;
    historyObj.callPlanningRefId = twilioCall.sid;

    if (location?.latitude && location?.longitude) {
      historyObj.location = {
        latitude: location?.latitude,
        longitude: location?.longitude,
      };
    }
    if (callCenterNote) {
      historyObj.callCenterNote = callCenterNote;
    }
    if (outletNote) {
      historyObj.outletNote = outletNote;
    }

    await this.callPlanningHistoriesService.save(historyObj);

    this.eventEmitter.emit('call_center.call_status_update', {
      status: CallCenterCallStatus.IN_A_CALL,
      callPlanId: callPlan.id,
      callCenterId: callPlan.callCenter.id,
      callCenterKey: callPlan.callCenter.businessPartnerContactKey,
      callCenterName: callPlan.callCenter.businessPartnerContactName1 || callPlan.callCenter.businessPartnerContactName1,
      distributorId: callPlan.distributorId,
      startTime: twilioCall.startTime,
    });

    const result = await this._repository.findOne({
      where: { id },
      relations: ['histories', 'outlet', 'callCenter', 'week', 'cycle'],
    });

    if (result) {
      const { histories, ...planData } = result;
      return {
        ...planData,
        histories: histories?.map((h) => ({
          id: h.id,
          startCallTime: h.startCallTime,
          endCallTime: h.endCallTime,
          location: h.location,
          callCenterNote: h.callCenterNote,
          outletNote: h.outletNote,
          transcripts: h.transcripts,
          callPlanningRefId: h.callPlanningRefId,
        })),
      };
    }
    if (currentContact) {
      const content = await i18n.translate(NotificationMessages[NotificationType.CALL_STARTED], {
        args: { outlet: callPlan.outlet?.businessPartnerName1 || callPlan.outlet?.businessPartnerName2 },
      });
      this.notificationService
        .create({
          fromUserId: currentContact.id,
          toUserId: null,
          userName: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2,
          userRole: currentContact.businessPartnerContactPersonRole,
          status: NotificationStatus.UNREAD,
          content: content as string,
          type: NotificationType.CALL_STARTED,
          callPlanId: callPlan.id,
          depotId: callPlan.depotId,
          title: await i18n.translate('plan.call_plans.call_plan'),
          data: { outlet: callPlan.outlet, date: callPlan.day },
        })
        .catch((e) => printLog(e?.toString ? e.toString() : JSON.stringify(e)));
    }
    return result;
  }

  async endCallPlan(id: string, dto: EndCallPlanDto, currentContact: any, i18n: I18nContext) {
    // const { location, callCenterNote, outletNote, transcripts } = dto;
    const callPlan = await this._repository.findOne({
      where: { id },
      relations: ['histories', 'outlet', 'callCenter'],
    });

    if (!callPlan) {
      const errorMessage = await i18n.translate('plan.call_plans.plan_not_found');
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    }

    if (callPlan.callStatus !== CallCenterStatus.IN_PROGRESS) {
      const errorMessage = await i18n.translate('plan.call_plans.not_in_progress');
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    }

    // Find history that started today and hasn't ended
    const today = moment().startOf('day');
    const historyObj = (callPlan.histories || []).find((h) => h?.callPlanningRefId && moment(h.startCallTime).isSame(today, 'day') && h.status === CallHistoryStatus.IN_PROGRESS);

    if (historyObj) {
      const twilioCallData = await endTwilioCall(historyObj.callPlanningRefId);
      if (twilioCallData) {
        const startCallMoment = moment.utc(twilioCallData?.startTime).tz(process.env.TZ);
        const endCallMoment = moment.utc(twilioCallData?.end).tz(process.env.TZ);
        historyObj.callDuration = endCallMoment.diff(startCallMoment, 'seconds');
        historyObj.startCallTime = startCallMoment.toDate();
        historyObj.endCallTime = endCallMoment.toDate();
        historyObj.status = this.callPlanningHistoriesService.convertTwilioStatusToCallHistoryStatus(twilioCallData?.status);
        await this.callPlanningHistoriesService.save(historyObj).catch((ex) => console.log(`Error update call history when end call`, ex));
        if (historyObj?.status === CallHistoryStatus.COMPLETED) {
          callPlan.callStatus = CallCenterStatus.COMPLETED;
        }
      }
    }

    await this._repository.save(callPlan);

    // historyObj.callPlanning = callPlan;
    // historyObj.endCallTime = new Date();
    // if (location?.latitude && location?.longitude) {
    //   historyObj.location = {
    //     latitude: location?.latitude,
    //     longitude: location?.longitude,
    //   };
    // }
    // if (callCenterNote) {
    //   historyObj.callCenterNote = callCenterNote;
    // }
    // if (outletNote) {
    //   historyObj.outletNote = outletNote;
    // }
    // if (!isEmptyObjectOrArray(transcripts)) {
    //   historyObj.transcripts = transcripts;
    // }

    // // Save history
    // await this.callPlanningHistoriesService.save(historyObj);

    this.eventEmitter.emit('call_center.call_status_update', {
      status: CallCenterCallStatus.IDLE,
      callPlanId: callPlan.id,
      callCenterId: callPlan.callCenter.id,
      callCenterKey: callPlan.callCenter.businessPartnerContactKey,
      callCenterName: callPlan.callCenter.businessPartnerContactName1 || callPlan.callCenter.businessPartnerContactName1,
      distributorId: callPlan.distributorId,
    });

    // Return clean call plan without circular references
    const result = await this._repository.findOne({
      where: { id },
      relations: ['histories', 'outlet', 'callCenter', 'week', 'cycle'],
    });

    if (result) {
      const { histories, ...planData } = result;
      return {
        ...planData,
        histories: histories?.map((h) => ({
          id: h.id,
          startCallTime: h.startCallTime,
          endCallTime: h.endCallTime,
          location: h.location,
          callCenterNote: h.callCenterNote,
          outletNote: h.outletNote,
          transcripts: h.transcripts,
        })),
      };
    }
    if (currentContact) {
      const content = await i18n.translate(NotificationMessages[NotificationType.CALL_ENDED], {
        args: { outlet: callPlan.outlet?.businessPartnerName1 || callPlan.outlet?.businessPartnerName2 },
      });
      this.notificationService
        .create({
          fromUserId: currentContact.id,
          toUserId: null,
          userName: currentContact.businessPartnerContactName1 || currentContact.businessPartnerContactName2,
          userRole: currentContact.businessPartnerContactPersonRole,
          status: NotificationStatus.UNREAD,
          content: content as string,
          type: NotificationType.CALL_STARTED,
          callPlanId: callPlan.id,
          depotId: callPlan.depotId,
          title: await i18n.translate('plan.call_plans.call_plan'),
          data: { outlet: callPlan.outlet, date: callPlan.day },
        })
        .catch((e) => printLog(e?.toString ? e.toString() : JSON.stringify(e)));
    }
    return result;
  }

  async updateCheckList(id: string, dto: UpdateCheckListDto, fromManager = false, i18n: I18nContext) {
    const callPlan = await this._repository.findOne({
      where: { id },
    });

    if (!callPlan) {
      const errorMessage = await i18n.translate('plan.call_plans.plan_not_found');
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    }

    if (!callPlan.checkList) {
      callPlan.checkList = [];
    }

    // Filter out any null or invalid items and update single item in checklist
    callPlan.checkList = callPlan.checkList.filter((item) => item && typeof item === 'object' && 'label' in item);
    const existingItemIndex = callPlan.checkList.findIndex((item) => item.label === dto.label);

    if (existingItemIndex !== -1) {
      // Update existing item's checked status
      callPlan.checkList[existingItemIndex].checked = dto.checked;
      callPlan.checkList[existingItemIndex].fromManager = fromManager;
    } else {
      // Add new item
      callPlan.checkList.push({
        label: dto.label,
        checked: dto.checked,
        fromManager: fromManager,
      });
    }

    await this._repository.save(callPlan);

    return callPlan.checkList;
  }

  async deleteCheckListItem(id: string, dto: DeleteCheckListItemDto, contact: BusinessPartnerContact, i18n: I18nContext) {
    const callPlan = await this._repository.findOne({
      where: { id },
    });

    if (!callPlan) {
      const errorMessage = await i18n.translate('plan.call_plans.plan_not_found');
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    }

    if (!callPlan.checkList) {
      callPlan.checkList = [];
    }

    // Find and remove the item with matching label
    const itemIndex = callPlan.checkList.findIndex((item) => item.label === dto.label);
    if (itemIndex === -1) {
      const errorMessage = await i18n.translate('plan.call_plans.checklist_item_not_found');
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    }

    if (callPlan.checkList[itemIndex].fromManager && contact.businessPartnerContactPersonRole !== ConstantRoles.CALL_CENTER_MANAGEMENT) {
      const errorMessage = await i18n.translate('plan.call_plans.only_manager_can_delete_checklist_item');
    }

    callPlan.checkList.splice(itemIndex, 1);
    await this._repository.save(callPlan);

    return callPlan.checkList;
  }

  private async determineViewTypeAndDateRange(
    data: { year: number; month?: number; week?: number; day?: number },
    i18n: I18nContext,
  ): Promise<{
    viewType: 'month' | 'week' | 'day';
    startDate: moment.Moment;
    endDate: moment.Moment;
  }> {
    const { year, month, week, day } = data;

    // Validate year
    if (!year || year < 2000 || year > 2100) {
      throw new HttpException(await i18n.translate('plan.call_plans.invalid_year'), HttpStatus.BAD_REQUEST);
    }

    // Validate month if provided
    if (month && (month < 1 || month > 12)) {
      throw new HttpException(await i18n.translate('plan.call_plans.invalid_month'), HttpStatus.BAD_REQUEST);
    }

    // Validate week if provided
    if (week && (week < 1 || week > 53)) {
      throw new HttpException(await i18n.translate('plan.call_plans.invalid_week'), HttpStatus.BAD_REQUEST);
    }

    // Validate day if provided
    if (day && (day < 1 || day > 31)) {
      throw new HttpException(await i18n.translate('plan.call_plans.invalid_day'), HttpStatus.BAD_REQUEST);
    }

    // Validate parameter combinations
    if (day > 0) {
      // If day is provided, week and month must also be provided
      if (!week || !month) {
        throw new HttpException(await i18n.translate('plan.call_plans.day_parameters_required'), HttpStatus.BAD_REQUEST);
      }
    }

    if (week > 0) {
      // If week is provided, month must also be provided
      if (!month) {
        throw new HttpException(await i18n.translate('plan.call_plans.week_required'), HttpStatus.BAD_REQUEST);
      }
    }

    let viewType: 'month' | 'week' | 'day';
    let startDate: moment.Moment;
    let endDate: moment.Moment;

    if (day) {
      // Day view - requires year, month, week, and day
      viewType = 'day';
      startDate = moment()
        .year(year)
        .month(month - 1)
        .date(day)
        .startOf('day');

      // Validate if the date is valid
      if (!startDate.isValid()) {
        throw new HttpException(await i18n.translate('plan.call_plans.invalid_date_combination'), HttpStatus.BAD_REQUEST);
      }

      endDate = startDate.clone().endOf('day');
    } else if (week) {
      // Week view - requires year, month, and week
      viewType = 'week';
      startDate = moment()
        .year(year)
        .month(month - 1)
        .isoWeek(week)
        .startOf('isoWeek');
      endDate = startDate.clone().endOf('isoWeek');
    } else if (month) {
      // Month view - requires year and month
      viewType = 'month';
      startDate = moment()
        .year(year)
        .month(month - 1)
        .startOf('month');
      endDate = startDate.clone().endOf('month');
    } else {
      throw new HttpException(await i18n.translate('plan.call_plans.month_required'), HttpStatus.BAD_REQUEST);
    }

    return { viewType, startDate, endDate };
  }

  async calendarPlans(
    data: {
      search: string;
      year: number;
      month?: number;
      week?: number;
      day?: number;
    },
    contacts: any,
    i18n: I18nContext,
  ) {
    if (isEmptyObjectOrArray(contacts)) return [];

    const { viewType, startDate, endDate } = await this.determineViewTypeAndDateRange(data, i18n);

    const whereConditions: any[] = [
      {
        callCenter: { id: contacts.contact.id },
        rescheduled: false,
        day: Between(startDate.toDate(), endDate.toDate()),
        ...(data.search && {
          outlet: {
            businessPartnerName1: ILike(`%${data.search}%`),
          },
        }),
      },
      {
        callCenter: { id: contacts.contact.id },
        rescheduled: true,
        rescheduledDay: Between(startDate.toDate(), endDate.toDate()),
        ...(data.search && {
          outlet: {
            businessPartnerName1: ILike(`%${data.search}%`),
          },
        }),
      },
    ];

    const plans = await this._repository.find({
      where: whereConditions,
      relations: ['outlet', 'callCenter', 'histories', 'cycle', 'week'],
      order: {
        displayDay: 'ASC',
      },
    });

    const outlets = await this._businessPartnerService.find({
      where: { id: In(plans.map((plan) => plan.outlet.id)) },
    });

    const outletsWithRelations = await this._businessPartnerService.attachBusinessPartnersRelationData(outlets);
    const outletMap = new Map(outletsWithRelations.map((outlet) => [outlet.id, outlet]));
    // const contactsWithRelations = await this._businessPartnerContactService.attachBusinessPartnerContactRelationData(contacts.contact);

    // Sort histories by updatedAt in descending order
    const callPlans = plans.map((plan) => {
      if (plan.histories?.length) {
        plan.histories.sort((a, b) => moment(b.updatedAt).valueOf() - moment(a.updatedAt).valueOf());
      }
      return {
        ...plan,
        outlet: outletMap.get(plan.outlet.id),
      };
    });

    const transformedPlans = await this.transformOutletToCallPlan(callPlans);

    let result;
    switch (viewType) {
      case 'month':
        const monthlyPlans = {};

        const firstWeekOfYear = moment().year(data.year).startOf('year').startOf('week');

        const firstDayOfMonth = startDate.clone().startOf('month');
        const lastDayOfMonth = endDate.clone().endOf('month');

        const firstDayOfCalendar = firstDayOfMonth.clone().startOf('week');
        const lastDayOfCalendar = lastDayOfMonth.clone().endOf('week');

        const currentDate = firstDayOfCalendar.clone();
        while (currentDate.isSameOrBefore(lastDayOfCalendar)) {
          const dateKey = currentDate.format('YYYY-MM-DD');
          const weekNumber = currentDate.diff(firstWeekOfYear, 'weeks') + 1;

          monthlyPlans[dateKey] = {
            week: weekNumber,
            total: 0,
            completed: 0,
            pending: 0,
            in_progress: 0,
            rescheduled: 0,
            missed: 0,
            isCurrentMonth: currentDate.month() === startDate.month(),
          };

          currentDate.add(1, 'day');
        }

        transformedPlans.forEach((plan) => {
          const planDate = moment(plan.rescheduled ? plan.rescheduledDay : plan.day).format('YYYY-MM-DD');
          const isPast = moment(planDate).isBefore(moment().tz(process.env.TZ), 'day');

          if (monthlyPlans[planDate]) {
            monthlyPlans[planDate].total++;
            if (plan.callStatus === CallCenterStatus.COMPLETED) {
              monthlyPlans[planDate].completed++;
            } else if (!isPast && plan.callStatus === CallCenterStatus.PENDING) {
              monthlyPlans[planDate].pending++;
            } else if (!isPast && plan.callStatus === CallCenterStatus.IN_PROGRESS) {
              monthlyPlans[planDate].in_progress++;
            } else if (!isPast && plan.callStatus === CallCenterStatus.RESCHEDULED) {
              monthlyPlans[planDate].rescheduled++;
            } else if (isPast || plan.callStatus === CallCenterStatus.MISSED) {
              monthlyPlans[planDate].missed++;
            }
          }
        });

        const groupedByWeek: Record<string, WeekData> = {};
        const typedMonthlyPlans = monthlyPlans as Record<string, DayData>;
        Object.entries(typedMonthlyPlans).forEach(([date, data]) => {
          const typedData = data as DayData;
          const weekNum = typedData.week;

          if (typedData.total > 0) {
            if (!groupedByWeek[weekNum]) {
              groupedByWeek[weekNum] = {
                weekStart: moment(date).startOf('week').format('YYYY-MM-DD'),
                weekEnd: moment(date).endOf('week').format('YYYY-MM-DD'),
                days: [],
              };
            }
            groupedByWeek[weekNum].days.push({
              day: date,
              total: typedData.total,
              completed: typedData.completed,
              pending: typedData.pending,
              in_progress: typedData.in_progress,
              missed: typedData.missed,
              rescheduled: typedData.rescheduled,
            });
          }
        });

        const filteredGroupedByWeek = Object.fromEntries(Object.entries(groupedByWeek).filter(([_, weekData]) => weekData.days.length > 0));

        // Convert object to array and sort by weekStart
        const weekArray = Object.values(filteredGroupedByWeek).sort((a, b) => moment(a.weekStart).diff(moment(b.weekStart)));

        // Sort days within each week by date
        weekArray.forEach((week) => {
          week.days.sort((a, b) => moment(a.day).diff(moment(b.day)));
        });

        return {
          data: weekArray,
          total: Object.values(typedMonthlyPlans).reduce((sum, data) => sum + data.total, 0),
          completed: Object.values(typedMonthlyPlans).reduce((sum, data) => sum + data.completed, 0),
          pending: Object.values(typedMonthlyPlans).reduce((sum, data) => sum + data.pending, 0),
          in_progress: Object.values(typedMonthlyPlans).reduce((sum, data) => sum + data.in_progress, 0),
          missed: Object.values(typedMonthlyPlans).reduce((sum, data) => sum + data.missed, 0),
          rescheduled: Object.values(typedMonthlyPlans).reduce((sum, data) => sum + data.rescheduled, 0),
        };

      case 'week':
        const weeklyPlans = {
          weekStart: startDate.format('YYYY-MM-DD'),
          weekEnd: endDate.format('YYYY-MM-DD'),
          days: [],
        };

        transformedPlans.forEach((plan) => {
          const planDate = moment(plan.rescheduled ? plan.rescheduledDay : plan.day).tz(process.env.TZ);
          const dayKey = planDate.format('YYYY-MM-DD');
          const outlet = outletMap.get(plan.outlet.id);
          let contact = outlet.communications?.find((comm) => comm.communicationType === 'tel');
          if (!contact) {
            contact = outlet.communications?.find((comm) => comm.communicationType === 'phone');
          }
          const contactEmail = outlet.communications?.find((comm) => comm.communicationType === 'email');
          weeklyPlans.days.push({
            id: plan.id,
            day: dayKey,
            time: planDate.format('HH:mm'),
            outlet: plan.outlet,
            outletId: plan.outlet.id,
            outletName: plan.outlet.name,
            outletUCC: plan.outlet.outletUCC,
            status: plan.callStatus,
            address: plan.outlet.address,
            contact: {
              name: contact?.communicationName || outlet?.businessPartnerName2,
              phoneNumber: contact?.communicationValue || '',
              email: contactEmail?.communicationValue || '',
              role: plan.outlet.contact.role,
            },
            nps: plan.outlet.nps,
            avgCallRate: plan.outlet.avgCallRate,
            startCallTime: plan.histories?.[0]?.startCallTime || '',
            endCallTime: plan.histories?.[0]?.endCallTime || '',
            duration:
              plan.histories?.[0]?.startCallTime && plan.histories?.[0]?.endCallTime
                ? moment.utc(moment(plan.histories[0].endCallTime).diff(moment(plan.histories[0].startCallTime))).format('HH:mm:ss')
                : '00:00:00',
            callCenter: plan.callCenter,
            segments: plan.segments,
          });
        });

        // Sort days by date and time
        weeklyPlans.days.sort((a, b) => {
          const dateCompare = moment(a.day).diff(moment(b.day));
          if (dateCompare !== 0) return dateCompare;
          return moment(a.time, 'HH:mm').diff(moment(b.time, 'HH:mm'));
        });

        result = weeklyPlans;
        break;

      case 'day':
        result = transformedPlans.map((plan) => {
          const outlet = outletMap.get(plan.outlet.id);
          let contact = outlet.communications?.find((comm) => comm.communicationType === 'tel');
          if (!contact) {
            contact = outlet.communications?.find((comm) => comm.communicationType === 'phone');
          }
          const contactEmail = outlet.communications?.find((comm) => comm.communicationType === 'email');
          const planDate = moment(plan.rescheduled ? plan.rescheduledDay : plan.day).tz(process.env.TZ);
          return {
            id: plan.id,
            day: planDate.format('YYYY-MM-DD'),
            time: planDate.format('HH:mm'),
            outlet: plan.outlet,
            outletId: plan.outlet.id,
            outletName: plan.outlet.name,
            outletUCC: plan.outlet.outletUCC,
            status: plan.callStatus,
            address: plan.outlet.address,
            contact: {
              name: contact?.communicationName || outlet?.businessPartnerName2,
              phoneNumber: contact?.communicationValue || '',
              email: contactEmail?.communicationValue || '',
              role: plan.outlet.contact.role,
            },
            nps: plan.outlet.nps,
            avgCallRate: plan.outlet.avgCallRate,
            checkList: plan.checkList,
            startCallTime: plan.histories?.[0]?.startCallTime || '',
            endCallTime: plan.histories?.[0]?.endCallTime || '',
            duration:
              plan.histories?.[0]?.startCallTime && plan.histories?.[0]?.endCallTime
                ? moment.utc(moment(plan.histories[0].endCallTime).diff(moment(plan.histories[0].startCallTime))).format('HH:mm:ss')
                : '00:00:00',
            visited: plan.outlet.visited,
            week: plan.week,
            cycle: plan.cycle,
            segments: plan.segments,
          };
        });
        break;
    }

    return {
      metadata: {
        viewType,
        year: data.year,
        month: data.month,
        week: data.week,
        day: data.day,
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
      },
      data: result,
    };
  }

  async getByOutlet(searchConditions: any) {
    return this._repository.find({
      where: searchConditions,
      relations: ['histories'],
      order: { updatedAt: 'desc' },
    });
  }

  transformInteractionData(callPlannings: CallPlanning[], callOrders: CallPlanningOrders[], contact: RawContact) {
    const result: any = {};

    // Process callPlannings
    (callPlannings || []).forEach((planning) => {
      const date = moment(planning.rescheduledDay || planning.day)
        .tz(process.env.TZ)
        .format('YYYY-MM-DD'); // Format the date as YYYY-MM-DD

      if (!result[date]) {
        result[date] = { date, interactions: [] };
      }

      (planning.histories || [])
        .filter((h) => h.status === CallHistoryStatus.COMPLETED || h.status === CallHistoryStatus.IN_PROGRESS)
        .forEach((history) => {
          const interaction = {
            ...history,
            type: 'CALL_HISTORY',
            callStatus: planning.callStatus,
            rescheduled: planning.rescheduled,
            contactName: contact?.communicationName || contact?.businessPartnerName2 || '',
          };
          result[date].interactions.push(interaction);
        });
    });

    // Process callOrders
    (callOrders || []).forEach((order) => {
      const date = moment(order.orderDate).tz(process.env.TZ).format('YYYY-MM-DD'); // Format the date as YYYY-MM-DD

      if (!result[date]) {
        result[date] = { date, interactions: [] };
      }

      const interaction = {
        ...order,
        orderVolume: _.isNumber(order.orderVolume) ? roundNumber(order.orderVolume / CONVERT_ML_TO_HL) : order.orderVolume,
        orderFrom: order.orderSource,
        type: 'CALL_ORDER',
        contactName: contact?.communicationName || contact?.businessPartnerName2 || '',
      };
      result[date].interactions.push(interaction);
    });

    // Convert result to array and sort interactions in descending order
    const finalResult = Object.values(result).map((item: any) => {
      const itemInteractions = item?.interactions || [];
      itemInteractions.sort((a, b) => {
        const aCompareDate = new Date(a.orderDate || a.startCallTime);
        const bCompareDate = new Date(b.orderDate || b.startCallTime);
        if (aCompareDate > bCompareDate) {
          return -1;
        }

        if (aCompareDate < bCompareDate) {
          return 1;
        }
        return 0;
      });
      item.interactions = itemInteractions;
      return item;
    });

    return finalResult
      .filter((result) => (result?.interactions || []).length > 0)
      .sort((a, b) => {
        const aCompareDate = new Date(a.date);
        const bCompareDate = new Date(b.date);
        if (aCompareDate > bCompareDate) {
          return -1;
        }

        if (aCompareDate < bCompareDate) {
          return 1;
        }
        return 0;
      });
  }

  async getOutletInteractionHistories(outletId: string, dateQuery: DateRangeDto) {
    const [callPlannings, callOrders, contact] = await Promise.all([
      this.getByOutlet([
        {
          outlet: {
            id: outletId,
          },
          ...buildSearchDateCriteria('day', dateQuery),
          callStatus: CallCenterStatus.COMPLETED,
          isDeleted: false,
        },
        {
          outlet: {
            id: outletId,
          },
          ...buildSearchDateCriteria('rescheduledDay', dateQuery),
          isDeleted: false,
        },
      ]),
      this.callPlanningOrderService.getByOutlet({
        outlet: {
          id: outletId,
        },
        ...buildSearchDateCriteria('orderDate', dateQuery),
        isDeleted: false,
      }),
      this._businessPartnerService.findContactByOutletId(outletId),
    ]);

    return this.transformInteractionData(callPlannings, callOrders, contact);
  }

  async getTimeFrameSettings(distributorId: string) {
    try {
      if (!distributorId) {
        return {
          startingTimeframe: '08:00',
          endingTimeframe: '23:55',
          timeInterval: 5,
        };
      }

      const timeFrameSettings = await this._planTimeFrameService.findOne({
        where: { distributorId, isDeleted: false },
      });

      return {
        startingTimeframe: timeFrameSettings?.startingTimeframe || '08:00',
        endingTimeframe: timeFrameSettings?.endingTimeframe || '23:55',
        timeInterval: timeFrameSettings?.timeInterval || 5,
      };
    } catch (e) {
      return {
        startingTimeframe: '08:00',
        endingTimeframe: '23:55',
        timeInterval: 5,
      };
    }
  }

  async getCallPlanActors(user: any) {
    const { outletIds, contactIds, originalContactId } = user?.businessPartnerRelations || {};
    if (!outletIds?.length || !contactIds?.length) {
      return { outlets: [], contacts: [] };
    }
    const callCenterIds = contactIds.filter((id) => id !== originalContactId);
    const [outlets, contacts, relations] = await Promise.all([
      this._businessPartnerService.find({
        where: {
          id: In(outletIds),
          isDeleted: false,
        },
      }),
      this._businessPartnerContactService.find({
        where: {
          id: In(callCenterIds),
          isDeleted: false,
          businessPartnerContactPersonRole: BusinessPartnerContactRole.CALL_CENTER,
        },
      }),
      this._businessPartnerRelationService.find({
        where: {
          businessPartner1: In(callCenterIds),
          isDeleted: false,
          businessPartnerRelationType: BusinessPartnerRelationType.CONTACT_OUTLET,
        },
      }),
    ]);

    return {
      outlets: outlets
        .map((outlet) => ({
          ...outlet,
          contactIds: relations.filter((relation) => relation.businessPartner2 === outlet.id).map((r) => r.businessPartner1),
        }))
        .filter((outlet) => outlet.contactIds.length),
      contacts: contacts.map((contact) => ({
        ...contact,
        outletIds: relations.filter((relation) => relation.businessPartner1 === contact.id).map((r) => r.businessPartner2),
      })),
    };
  }

  async getCallCenterOutletDatas(user: any) {
    return this._businessPartnerRelationService.findOutletRelationDataByCallCenterId(user?.saleRepId || user?.contactId);
  }

  async validateImportPlans(mapping: CallPlanImportMappingType, contactManagerRelations: ContactDistributorRelations): Promise<ContactDistributorRelationsDataMap> {
    const { contactIds, depotIds, distributorIds, outletIds, originalContactId } = contactManagerRelations || {};
    const [contacts, businessPartners] = await Promise.all([
      this._businessPartnerContactService.findWithOptions({
        where: {
          id: In(contactIds.filter((id) => id !== originalContactId)),
          isDeleted: false,
        },
        select: ['id', 'businessPartnerContactKey'],
      }),
      this._businessPartnerService.findWithOptions({
        where: {
          id: In([...depotIds, ...outletIds, ...distributorIds]),
          isDeleted: false,
        },
        select: ['id', 'businessPartnerKey', 'businessPartnerType'],
      }),
    ]);

    const callCenterMap = new Map(contacts.map((cc) => [cc.businessPartnerContactKey, cc]));
    const { distributorMap, depotMap, outletMap } = businessPartners.reduce(
      (acc, bP) => {
        if (bP.businessPartnerType === BusinessPartnerType.DISTRIBUTOR) {
          acc.distributorMap.set(bP.businessPartnerKey, bP);
        }

        if (bP.businessPartnerType === BusinessPartnerType.DEPOT) {
          acc.depotMap.set(bP.businessPartnerKey, bP);
        }

        if (bP.businessPartnerType === BusinessPartnerType.OUTLET) {
          acc.outletMap.set(bP.businessPartnerKey, bP);
        }
        return acc;
      },
      { distributorMap: new Map(), depotMap: new Map(), outletMap: new Map() },
    );
    const plannings = mapping.callPlanning || [];
    const existedPlanByOutletKey = new Map<string, number>();
    for (let i = 0; i < plannings.length; i++) {
      const { callCenter, depotId, distributorId, day, outlet, week } = plannings[i];
      const outletKey = String(outlet);
      if (!callCenterMap.get(String(callCenter))) {
        throw new BadRequestException('Invalid Call Center Key');
      }

      if (!depotMap.get(String(depotId))) {
        throw new BadRequestException('Invalid Depot Key');
      }

      if (!outletMap.get(outletKey)) {
        throw new BadRequestException('Invalid Outlet Key');
      }

      const outletWeekKey = `${outletKey}-${week}`;
      if (existedPlanByOutletKey.get(outletWeekKey)) {
        throw new BadRequestException('Only one plan for each outlet per week!');
      }

      existedPlanByOutletKey.set(outletWeekKey, 1);

      if (!distributorMap.get(String(distributorId))) {
        throw new BadRequestException('Invalid Distributor Key');
      }

      const dayNumber = Number(day);
      const weekNumber = Number(week);
      if (!Number.isInteger(dayNumber) || dayNumber < 1 || dayNumber > 7) {
        throw new BadRequestException('Invalid Day Number');
      }

      if (!Number.isInteger(weekNumber) || weekNumber < 1 || weekNumber > 4) {
        throw new BadRequestException('Invalid Week Number');
      }
    }

    return {
      contactMap: callCenterMap,
      depotMap,
      distributorMap,
      outletMap,
    };
  }

  async exportCallPlan(cycleId: string, contactDistributorRelations: ContactDistributorRelations) {
    if (!cycleId || !contactDistributorRelations) {
      return null;
    }
    const { distributorIds } = contactDistributorRelations;
    // Step 2: Custom raw query to get contact and it's relation id and key
    const query = `
      SELECT 
        cp."day",
        cp."rescheduled",
        cp."rescheduledDay",
        bp_distributor."businessPartnerKey" AS "distributorKey",
        bp_depot."businessPartnerKey" AS "depotKey",
        bp_outlet."businessPartnerKey" AS "outletKey",
        bpc."businessPartnerContactKey" AS "contactKey",
        pw."weekNumber",
        pw.id AS weekId
      FROM 
          call_plannings cp
      JOIN 
          business_partners bp_distributor ON cp."distributorId" = bp_distributor.id
      JOIN 
          business_partners bp_depot ON cp."depotId" = bp_depot.id
      JOIN 
          business_partners bp_outlet ON cp."outletId" = bp_outlet.id
      JOIN 
          business_partner_contacts bpc ON cp."callCenterId" = bpc.id
      JOIN 
          plan_weeks pw ON cp."weekId" = pw.id
      WHERE 
          cp."cycleId" = '${cycleId}'
          AND cp."isDeleted" = false
          AND cp."distributorId" IN(${(distributorIds || []).map((id) => `'${id}'`).join(', ')})
      ORDER BY cp.id DESC;
      `
      .replace(/\n/g, ' ')
      .trim();

    const callPlannings = await this._repository.query(query);

    const exportData = callPlannings.map((callPlanning) => {
      return {
        'Outlet Id': callPlanning.outletKey,
        'Call Center ID': callPlanning.contactKey,
        Week: callPlanning.weekNumber,
        Day: !!callPlanning.rescheduled && callPlanning.rescheduledDay ? getCallPlanDayNumber(callPlanning.rescheduledDay) : getCallPlanDayNumber(callPlanning.day),
        'Depot ID': callPlanning.depotKey,
        'Distributor ID': callPlanning.distributorKey,
      };
    });

    return this.fileService.exportXLSXFileWithMultipleSheet({
      fileName: `CallPlanning_Export`,
      sheets: [
        {
          name: 'call-planning',
          data: exportData,
        },
      ],
    });
  }

  async getCallPlanOutletMetrics(outletId: string, dateRange: DateRangeDtos = null, contact: BusinessPartnerContact, i18n: I18nContext) {
    try {
      const outletDepot = await this._businessPartnerService.findOneDepotByOutletId(outletId);
      if (!contact.businessPartnerContactKey || isEmptyObjectOrArray(outletDepot?.outlet) || isEmptyObjectOrArray(outletDepot?.depot)) {
        return null;
      }

      const currentTime = moment().tz(process.env.TZ);
      const startOfMonth = currentTime.clone().startOf('month');
      const endOfMonth = currentTime.clone().endOf('month');

      // Prepare all async calls
      const [agentTargetSetting, orders, statistics, performanceOutletSaleVolume] = await Promise.all([
        this._targetSettingsService.getAgentTargetSettings(contact.businessPartnerContactKey, outletDepot.outlet.businessPartnerKey, currentTime.month() + 1, currentTime.year()),
        this.callPlanningOrderService.find({
          where: {
            outlet: { id: outletId },
            orderDate: Between(startOfMonth.toDate(), endOfMonth.toDate()),
          },
        }),
        this.omsSalesRepStatisticsService.getStatisticsForCallCenter(contact.businessPartnerContactKey),
        this._callCenterPerformanceService.getPerformanceOutletVolume(
          outletDepot.depot.businessPartnerKey,
          outletDepot.outlet.businessPartnerKey,
          dateRange.startDate,
          dateRange.endDate,
        ),
      ]);

      // Calculate avgCallRate and noOfOrder

      const stats = await this.calculateStats(outletDepot.outlet.businessPartnerKey, statistics, i18n);

      const outletSaleVolume = await this.omsService.getPerformanceOutletSaleVolume(
        outletDepot.depot.businessPartnerKey,
        { outletExternalID: outletDepot.outlet.businessPartnerKey },
        dateRange.startDate,
        dateRange.endDate,
        true,
        true,
        true,
        true,
        true,
      );

      const last_month_total = Number(outletSaleVolume?.volume_performance?.last_month_total);
      const current_month_total = Number(outletSaleVolume?.volume_performance?.current_month_total);
      const last_month = Number(outletSaleVolume?.volume_performance?.last_month);
      const current_month = Number(outletSaleVolume?.volume_performance?.current_month);

      return {
        outletTarget: {
          // value: stats.outletTarget.value,
          value: Number(agentTargetSetting?.volumeTarget || 0).toFixed(2),
          label: stats.outletTarget.label,
          avpOrder: Number(outletSaleVolume?.average_volume?.value?.toFixed(2)) || 0,
        },
        orderTotalVolume: {
          value: outletSaleVolume?.volume_performance?.current_month_total || 0,
          percentage: last_month_total > 0 ? Number(((current_month_total * 100) / last_month_total).toFixed(2)) : current_month_total > 0 ? 100 : 0,
          since: moment()
            .month(last_month - 1)
            .format('MMM'),
          current: moment()
            .month(current_month - 1)
            .format('MMM'),
        },
        avgFrequency: {
          // numberOfOrder: stats.avgFrequency.numberOfOrder,
          numberOfOrder: orders?.length || 0,
          theOrder: performanceOutletSaleVolume?.year_to_date_vol.top_outlet || 0, //stats.avgFrequency.theOrder,
        },
      };
    } catch (e) {
      printLog('getCallPlanOutletMetrics', e);
      return null;
    }
  }

  async getAgentStatistics(callCenterIds: string[], statisticDay: string | Date = new Date().toISOString()) {
    const date = moment(statisticDay).tz(process.env.TZ).endOf('day');
    return await this.omsSalesRepStatisticsService.findAll({
      contactId: { $in: callCenterIds },
      statisticDay: date.toDate(),
    });
  }

  async updateCallPlanningCallData(payload: {
    callPlanId: string;
    recordingStatus?: string;
    recordingUrl?: string;
    callSid: string;
    callDuration?: number;
    callStatus?: string;
    startTime?: string;
  }) {
    if (!payload) {
      return;
    }

    const callPlanningHistory = await this.callPlanningHistoriesService.findOne({
      where: {
        isDeleted: false,
        callPlanningRefId: payload.callSid,
      },
    });

    if (!callPlanningHistory) {
      return;
    }

    const historyUpdates: DeepPartial<CallPlanningHistories> = {};

    if (payload.recordingUrl) {
      historyUpdates.recordUrl = payload.recordingUrl;
    }

    if (payload.startTime) {
      historyUpdates.startCallTime = moment.utc(payload.startTime).tz(process.env.TZ).toDate();
    }

    if (payload.callDuration) {
      historyUpdates.callDuration = payload.callDuration;
    }

    if (payload.startTime && payload.callDuration) {
      historyUpdates.endCallTime = moment.utc(payload.startTime).tz(process.env.TZ).add(payload.callDuration, 'seconds').toDate();
    }

    if (!payload.callStatus) {
      return this.callPlanningHistoriesService.save({
        ...historyUpdates,
        id: callPlanningHistory.id,
      });
    }

    historyUpdates.status = this.callPlanningHistoriesService.convertTwilioStatusToCallHistoryStatus(payload.callStatus);

    await this.callPlanningHistoriesService.save({
      ...historyUpdates,
      id: callPlanningHistory.id,
    });

    if (payload.callSid && payload.callStatus === TwilioDialCallStatus.FAILED) {
      await endTwilioConferenceRoom(payload.callPlanId);
    }

    if (payload.callStatus !== TwilioDialCallStatus.COMPLETED) {
      return;
    }

    const callPlanning = await this._callPlanningRepository.findOne({
      where: {
        id: payload.callPlanId,
      },
    });

    if (!callPlanning) {
      return;
    }

    return this._callPlanningRepository.save({
      id: callPlanning.id,
      callStatus: CallCenterStatus.COMPLETED,
    });
  }

  async calculateAvailableSlotsInDay(hour: string, existingPlan: DeepPartial<CallPlanning>, newPlanTime: moment.Moment, existedDayPlans: any[], i18n: I18nContext) {
    const timeFrame = await this._planTimeFrameService.getValidCallPlanTimeFrame(existingPlan.distributorId);
    const currentTime = moment().tz(process.env.TZ);
    if (!!hour) {
      // If hour is provided, validate and use it
      const [targetHour, targetMinute] = hour.split(':').map(Number);
      const targetTime = newPlanTime.clone().hour(targetHour).minute(targetMinute).second(0);

      // Validate if the target time is within allowed time frame
      const timeFrameStart = moment(timeFrame.startingTimeframe, 'HH:mm');
      const timeFrameEnd = moment(timeFrame.endingTimeframe, 'HH:mm').subtract(timeFrame.timeInterval, 'minutes');

      if (
        targetTime.hour() < timeFrameStart.hour() ||
        (targetTime.hour() === timeFrameStart.hour() && targetTime.minute() < timeFrameStart.minute()) ||
        targetTime.hour() > timeFrameEnd.hour() ||
        (targetTime.hour() === timeFrameEnd.hour() && targetTime.minute() > timeFrameEnd.minute()) ||
        (newPlanTime.isSame(currentTime, 'day') && targetTime.isBefore(currentTime, 'minute'))
      ) {
        throw new HttpException(await i18n.translate(`plan.call_plans.invalid_time_slot`), HttpStatus.BAD_REQUEST);
      }

      const baseCallPlanSearchCondition: FindOptionsWhere<CallPlanning> = {
        callCenter: { id: existingPlan.callCenter.id },
        isDeleted: false,
      };

      if (existingPlan?.id) {
        baseCallPlanSearchCondition.id = Not(existingPlan?.id); // Exclude current plan
      }

      const searchExistingPlanCondition: FindOptionsWhere<CallPlanning> = {
        ...baseCallPlanSearchCondition,
        displayDay: convertMomentToCallPlanDate(targetTime),
      };

      // Check if there is any plan at the exact same time
      const existingPlanAtTime = await this._repository.findOne({
        where: searchExistingPlanCondition,
      });

      if (existingPlanAtTime) {
        throw new HttpException(await i18n.translate(`plan.call_plans.time_slot_taken`), HttpStatus.BAD_REQUEST);
      }
      return [targetTime];
    }

    // Original logic for finding next available slot without hour input

    let startHour, startMinute;
    const [timeframeStartHour, timeframeStartMinute] = timeFrame.startingTimeframe.split(':').map(Number);
    const currentMinutes = currentTime.hour() * 60 + currentTime.minute();
    const startMinuteByTimeFrameSetting = timeframeStartHour * 60 + timeframeStartMinute;
    if (newPlanTime.isSame(currentTime, 'day') && startMinuteByTimeFrameSetting <= currentMinutes) {
      const nextSlotMinutes = Math.ceil(currentMinutes / timeFrame.timeInterval) * timeFrame.timeInterval;
      startHour = Math.floor(nextSlotMinutes / 60);
      startMinute = nextSlotMinutes % 60;
    } else {
      startHour = timeframeStartHour;
      startMinute = timeframeStartMinute;
    }

    const lastDaySlot = moment(timeFrame.endingTimeframe, 'HH:mm').subtract(timeFrame.timeInterval, 'minutes');
    const endHour = lastDaySlot.get('hour');
    const endMinute = lastDaySlot.get('minute');

    const startTimeInMinutes = startHour * 60 + startMinute;
    const endTimeInMinutes = endHour * 60 + endMinute;
    const totalMinutes = endTimeInMinutes - startTimeInMinutes;
    const totalSlots = Math.floor(totalMinutes / timeFrame.timeInterval) + 1;

    const allTimeSlots = Array.from({ length: totalSlots }, (_, index) => {
      return newPlanTime
        .clone()
        .tz(process.env.TZ)
        .startOf('day')
        .add(startHour, 'hours')
        .add(startMinute, 'minutes')
        .add(index * timeFrame.timeInterval, 'minutes')
        .second(0);
    });

    const availableSlots = allTimeSlots.filter((slot) => {
      const slotTime = slot.format('HH:mm');
      // const hasPendingPlan = pendingPlans.some((pendingTime) => pendingTime.format('HH:mm') === slotTime);
      return !existedDayPlans.some((plan) => moment(plan.displayDay).tz(process.env.TZ).format('HH:mm') === slotTime);
    });

    if (availableSlots.length === 0) {
      throw new HttpException(await i18n.translate(`plan.call_plans.no_available_slots`), HttpStatus.BAD_REQUEST);
    }

    return availableSlots;
  }

  async getCallPlanningRecording(callPlanId: string) {
    const callPlan = await this._repository.findOne({
      where: {
        id: callPlanId,
      },
      relations: ['histories'],
    });

    if (!callPlan || !callPlan?.histories?.length) {
      return;
    }

    const callHistoryHasRecord = callPlan?.histories.find((h) => !!h.recordUrl);
    if (!callHistoryHasRecord) {
      return;
    }

    return await getRecordingStream(callHistoryHasRecord.recordUrl, this._httpService);
  }

  async getNewestOrders(outletId: string) {
    try {
      const outletDepot = await this._businessPartnerRelationService.findBusinessPartnerRelationsDataAndRelationPartnerKeys(outletId, BusinessPartnerRelationType.OUTLET_DEPOT);
      if (isEmptyObjectOrArray(outletDepot)) {
        return [];
      }
      const orders = await this.omsService.getListOrdersOMS({
        outletExternalID: outletDepot[0].businessPartnerKey1,
        depotExternalID: outletDepot[0].businessPartnerKey2,
        start_date: null,
        end_date: null,
        limit: 20,
        offset: 0,
        search: null,
        status: null,
      });
      await this.callPlanningOrderService.syncOrderStatusToCallPlanningOrders(orders, outletDepot[0].businessPartnerKey1, outletDepot[0].businessPartnerKey2);
    } catch (e) {
      printLog(e);
    }
  }

  async requestManagerForCallPlan(callPlanId: string, callCenterKey: string) {
    const callPlan = await this._callPlanningRepository.findOne({
      where: {
        id: callPlanId,
        isDeleted: false,
      },
      relations: ['callCenter'],
    });

    if (callPlan.callCenter.businessPartnerContactKey !== callCenterKey) {
      throw new BadRequestException('Invalid call center');
    }

    this.eventEmitter.emit('call_center.call_request_manager', {
      callPlanId: callPlan.id,
      callCenterId: callPlan.callCenter.id,
      callCenterKey: callPlan.callCenter.businessPartnerContactKey,
      callCenterName: callPlan.callCenter.businessPartnerContactName1 || callPlan.callCenter.businessPartnerContactName1,
      distributorId: callPlan.distributorId,
    });

    return callPlan.id;
  }

  async querySalesOrderPerformanceData(callCenterIds: string[], startDate: Date, endDate: Date) {
    if (!callCenterIds.length) return [];
    // If month is not provided, use current month
    const now = moment().tz(process.env.TZ);
    if (!startDate && !endDate) {
      // If month is not provided, use current month
      // const now = moment().tz(process.env.TZ);
      const targetMonth = now.month() + 1; // moment month is 0-based
      const year = now.year();
      // Get start and end of the month
      const momentStartDate = moment.tz({ year, month: targetMonth - 1, day: 1 }, process.env.TZ).startOf('month');
      startDate = momentStartDate.toDate();
      endDate = momentStartDate.clone().endOf('month').endOf('day').toDate();
    } else {
      const endOfCurrentDate = now.clone().endOf('day');
      const momentEndDate = moment(endDate).endOf('day');
      endDate = !endDate && momentEndDate.isAfter(endOfCurrentDate) ? endOfCurrentDate.toDate() : momentEndDate.toDate();
    }

    const query = `
      SELECT
        "callCenterId" as "callCenterId",
        COUNT(id) as "completedPlans",
        SUM(CASE WHEN "hasOrder" = true THEN 1 ELSE 0 END) as "hasOrderPlans"
      FROM call_plannings
      WHERE "callCenterId" IN (${callCenterIds.map((id) => `'${id}'`).join(',')})
        AND "displayDay" BETWEEN $1 AND $2
        AND "callStatus" = $3
        AND "creator" = $4
      GROUP BY "callCenterId"
    `
      .replace(/\n/g, ' ')
      .trim();

    const result = await this._repository.query(query, [startDate, endDate, CallCenterStatus.COMPLETED, CallPlanCreatorType.MANAGER]);

    return result;
  }

  async syncCallPlanningHistoryData() {
    const baseCondition: FindOptionsWhere<CallPlanningHistories> = {
      isDeleted: false,
      callPlanningRefId: Not(IsNull()),
    };
    const needUpdateCallHistories = await this.callPlanningHistoriesService.find({
      where: [
        {
          ...baseCondition,
          startCallTime: IsNull(),
        },
        {
          ...baseCondition,
          endCallTime: IsNull(),
        },
        {
          ...baseCondition,
          status: CallHistoryStatus.COMPLETED,
          recordUrl: IsNull(),
        },
      ],
      relations: ['callPlanning'],
    });

    console.log(`Need to update ${needUpdateCallHistories?.length} histories`);
    const updateHistories: Partial<CallPlanningHistories>[] = [];

    for (let i = 0; i < needUpdateCallHistories?.length; i++) {
      const needUpdateCallHistory = needUpdateCallHistories[i];
      const twilioCallRefId = needUpdateCallHistory?.callPlanningRefId;
      const twilioCallData = await getTwilioCallDetails(twilioCallRefId);
      if (!twilioCallData) {
        console.log(`No data found for call history ${needUpdateCallHistory.id}`);
        continue;
      }
      const dataUpdate: Partial<CallPlanningHistories> = {
        id: needUpdateCallHistory.id,
      };

      const twilioCallDataRecordings = await twilioCallData?.recordings().list();
      if (!dataUpdate.recordUrl && twilioCallDataRecordings?.length) {
        dataUpdate.recordUrl = `https://api.twilio.com${(twilioCallDataRecordings[0]?.uri || '').replace(/\.json$/, '')}`;
      }

      const startCallMoment = moment.utc(twilioCallData?.startTime).tz(process.env.TZ);
      const endCallMoment = moment.utc(twilioCallData?.endTime).tz(process.env.TZ);
      dataUpdate.callDuration = endCallMoment.diff(startCallMoment, 'seconds');
      dataUpdate.startCallTime = startCallMoment.toDate();
      dataUpdate.endCallTime = endCallMoment.toDate();
      dataUpdate.status = this.callPlanningHistoriesService.convertTwilioStatusToCallHistoryStatus(twilioCallData?.status);
      updateHistories.push(dataUpdate);
    }

    return this.callPlanningHistoriesService.save(updateHistories);
  }

  calculatePercent(numerator: number, denominator: number, fixedNumber = 0) {
    if (!denominator) {
      return 0;
    }
    const percent = (numerator * 100) / denominator || 0;

    return fixedNumber > 0 ? parseFloat(percent?.toFixed(fixedNumber)) : roundToNearestInt(percent);
  }
}
