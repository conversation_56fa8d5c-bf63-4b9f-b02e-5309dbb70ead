import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { CallPlanningHistories } from '../entities/call-planning-histories.entity';
import { CallCenterStatus, CallHistoryStatus, TwilioDialCallStatus } from '../enums/call-center.enum';
import { calculateCallMetrics, calculateCallPercentages, groupAndSortByDate } from '../../utils/helpers/call-planning';
import { generateDateRange, fillMissingDates, formatDuration, formatDate, convertToShortDate } from '../../utils/helpers/date';
import { CallPlanningOrders } from '../entities/call-planning-order.entity';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { CallEfficiencyMetricsArgs } from '../dtos/call-efficiency-metrics.args';
import { ConstantCommons } from 'src/utils/constants';
import { AgentsMetricsArgs } from '../dtos/agents-metrics.args';
import { CallReportsArgs } from '../dtos/call-reports.args';
import { Brackets } from 'typeorm';
import { FilesService } from 'src/files/services/files.service';
import * as moment from 'moment-timezone';
import { CallPlanning } from '../entities/call-planning.entity';
import { I18nContext } from 'nestjs-i18n';
import { convertToHL } from '../../utils';
@Injectable()
export class CallPlanningHistoriesService extends BaseSQLService<CallPlanningHistories> {
  constructor(
    @InjectRepository(CallPlanningHistories)
    private readonly _callPlanningHistoriesRepository: Repository<CallPlanningHistories>,
    @InjectRepository(CallPlanning)
    private readonly _callPlanningRepository: Repository<CallPlanning>,
    private readonly _filesService: FilesService,
  ) {
    super();
    this._repository = this._callPlanningHistoriesRepository;
  }

  private mapOrdersToResponse(orders: CallPlanningOrders[]) {
    return orders.map((order: CallPlanningOrders) => ({
      id: order.id,
      orderId: order.orderId,
      orderCode: order.orderCode,
    }));
  }

  async getCallPlanningHistories(params: CallEfficiencyMetricsArgs & PaginationParams & OrderParams, callCenterIds: string[], selectOnlyCompletedCalls = false) {
    if (!callCenterIds?.length) {
      return {
        callRecords: [],
        total: 0,
      };
    }
    let { startDate, endDate } = params;
    const { offset, limit, orderBy, orderDesc = ConstantCommons.ORDER_DESC_DEFAULT, selectAll = false } = params;

    // If no date range provided, default to last week
    if (!startDate && !endDate) {
      endDate = moment().tz(process.env.TZ).endOf('day').toDate();
      startDate = moment().tz(process.env.TZ).subtract(7, 'days').startOf('day').toDate();
    } else {
      // Convert ISO strings to Date objects if they are strings
      if (typeof startDate === 'string') {
        startDate = moment(startDate).tz(process.env.TZ).startOf('day').toDate();
      }
      if (typeof endDate === 'string') {
        endDate = moment(endDate).tz(process.env.TZ).endOf('day').toDate();
      }
    }

    const DURATION_EXPRESSION = `
      CASE 
        WHEN history."callDuration" IS NOT NULL AND history.callDuration != 0 
        THEN history."callDuration" 
        ELSE EXTRACT(EPOCH FROM (history.endCallTime - history.startCallTime))
      END
    `;

    const query = this._repository
      .createQueryBuilder('history')
      .innerJoinAndSelect('history.callPlanning', 'callPlanning')
      .innerJoinAndSelect('callPlanning.callCenter', 'callCenter')
      .innerJoinAndSelect('callPlanning.outlet', 'outlet')
      .leftJoinAndSelect(
        (subQuery) => {
          return subQuery
            .select('call_planning_orders.callPlanningId', 'callPlanningId')
            .addSelect('JSON_AGG("call_planning_orders".*)', 'orderData')
            .addSelect('COALESCE(SUM(call_planning_orders.orderTotal), 0)', 'totalOrderValue')
            .from('call_planning_orders', 'call_planning_orders')
            .where('call_planning_orders.isDeleted = false')
            .groupBy('call_planning_orders.callPlanningId');
        },
        'orders',
        '"orders"."callPlanningId" = "callPlanning"."id"',
      )
      .addSelect(`${DURATION_EXPRESSION} as "durationInSeconds"`)
      .addSelect('"orders"."totalOrderValue"', 'ordersTotalOrderValue')
      .addSelect('"outlet"."businessPartnerName1"', 'tradingName')
      .addSelect('"history"."callPlanningRefId"', 'callPlanningRefId')
      .where('"callCenter"."id" IN (:...callCenterIds)', { callCenterIds })
      .andWhere('"callPlanning"."isDeleted" = false')
      .andWhere('"callCenter"."isDeleted" = false')
      .andWhere('"outlet"."isDeleted" = false');

    if (selectOnlyCompletedCalls) {
      query.andWhere('"callPlanning"."callStatus" = :callStatus', { callStatus: CallCenterStatus.COMPLETED });
    }
    // Apply sorting
    if (orderBy) {
      const orderDirection = orderDesc === ConstantCommons.ORDER_DESC_DEFAULT ? 'DESC' : 'ASC';
      switch (orderBy) {
        case 'callDuration':
          const sortableNull = orderDirection === 'DESC' ? 'NULLS LAST' : 'NULLS FIRST';
          query.orderBy(`${DURATION_EXPRESSION}`, orderDirection, sortableNull);
          break;
        case 'id':
          query.orderBy(`history.${orderBy}`, orderDirection);
          break;
        case 'tradingName':
          query.orderBy(`outlet.businessPartnerName1`, orderDirection);
          break;
        case 'calledDate':
          query.orderBy(`callPlanning.displayDay`, orderDirection);
          break;
        case 'orderValue':
          query.orderBy(`COALESCE("orders"."totalOrderValue", 0)`, orderDirection);
          break;
        default:
          break;
      }
    } else {
      query.orderBy(`callPlanning.displayDay`, 'DESC');
    }

    if (startDate) {
      query.andWhere('callPlanning.displayDay >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('callPlanning.displayDay <= :endDate', { endDate });
    }

    // Apply pagination
    if (!selectAll && offset && limit) {
      query.skip(offset).take(limit);
    }

    // Get total count and raw entities in one query
    const [total, { entities, raw }] = await Promise.all([query.getCount(), query.getRawAndEntities()]);

    const histories = entities.map((history, index) => {
      const tradingName = raw[index]?.tradingName;
      const calledDate = formatDate(new Date(history.callPlanning?.displayDay));
      const callDuration = raw[index]?.durationInSeconds ? formatDuration(Math.round(parseFloat(raw[index]?.durationInSeconds))) : '00:00:00';
      const orderPlaced = raw[index]?.orderData ? this.mapOrdersToResponse(raw[index]?.orderData) : [];
      const orderValue = raw[index]?.ordersTotalOrderValue ? parseInt(raw[index]?.ordersTotalOrderValue) : 0;
      return {
        id: history.callPlanningRefId,
        tradingName: tradingName,
        avatar: null,
        color: null,
        calledDate,
        callDuration,
        orderPlaced,
        orderValue,
      };
    });

    return {
      callRecords: histories,
      total,
      historiesRaw: entities, // Return histories raw for metrics calculation
    };
  }

  async getCallPlannings(params: CallEfficiencyMetricsArgs & PaginationParams & OrderParams, callCenterIds: string[]) {
    let { startDate, endDate } = params;
    const { offset, limit, orderBy, orderDesc = ConstantCommons.ORDER_DESC_DEFAULT, selectAll = false } = params;

    // If no date range provided, default to last week
    if (!startDate && !endDate) {
      endDate = moment().tz(process.env.TZ).endOf('day').toDate();
      startDate = moment().tz(process.env.TZ).subtract(7, 'days').startOf('day').toDate();
    } else {
      // Convert ISO strings to Date objects if they are strings
      if (typeof startDate === 'string') {
        startDate = moment(startDate).tz(process.env.TZ).startOf('day').toDate();
      }
      if (typeof endDate === 'string') {
        endDate = moment(endDate).tz(process.env.TZ).endOf('day').toDate();
      }
    }

    const query = this._callPlanningRepository
      .createQueryBuilder('callPlanning')
      .innerJoinAndSelect('callPlanning.callCenter', 'callCenter')
      .innerJoinAndSelect('callPlanning.outlet', 'outlet')
      .select([
        'callPlanning.id as "id"',
        'callPlanning.displayDay as "displayDay"',
        'callPlanning.callStatus as "callStatus"',
        'callPlanning.rescheduled as "rescheduled"',
        'callPlanning.creator as "creator"',
        'callCenter.businessPartnerContactName2 as "agentName"',
        'outlet.businessPartnerName2 as "outletName"',
      ])
      .where('callCenter.id IN (:...callCenterIds)', { callCenterIds })
      .andWhere('callPlanning.isDeleted = false')
      .andWhere('callCenter.isDeleted = false')
      .andWhere('outlet.isDeleted = false');

    if (startDate) {
      query.andWhere('callPlanning.displayDay >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('callPlanning.displayDay <= :endDate', { endDate });
    }

    // Apply sorting
    if (orderBy) {
      const orderDirection = orderDesc === ConstantCommons.ORDER_DESC_DEFAULT ? 'DESC' : 'ASC';
      switch (orderBy) {
        case 'displayDay':
          query.orderBy('callPlanning.displayDay', orderDirection);
          break;
        case 'callStatus':
          query.orderBy('callPlanning.callStatus', orderDirection);
          break;
        case 'agentName':
          query.orderBy('callCenter.businessPartnerContactName2', orderDirection);
          break;
        case 'outletName':
          query.orderBy('outlet.businessPartnerName2', orderDirection);
          break;
        default:
          query.orderBy('callPlanning.displayDay', 'DESC');
      }
    } else {
      query.orderBy('callPlanning.displayDay', 'DESC');
    }

    // Apply pagination
    if (!selectAll && offset && limit) {
      query.skip(offset).take(limit);
    }

    const [callPlannings, total] = await Promise.all([query.getRawMany(), query.getCount()]);

    return {
      callPlannings,
      total,
    };
  }

  async getCallPlanningMetrics(params: CallEfficiencyMetricsArgs & PaginationParams & OrderParams, callCenterIds: string[]) {
    const { historiesRaw, total } = await this.getCallPlanningHistories(params, callCenterIds);
    let { startDate, endDate } = params;
    // If no date range provided, default to last week
    if (!startDate && !endDate) {
      endDate = moment().tz(process.env.TZ).endOf('day').toDate();
      startDate = moment().tz(process.env.TZ).subtract(7, 'days').startOf('day').toDate();
    } else {
      // Convert ISO strings to Date objects if they are strings
      if (typeof startDate === 'string') {
        startDate = moment(startDate).tz(process.env.TZ).startOf('day').toDate();
      }
      if (typeof endDate === 'string') {
        endDate = moment(endDate).tz(process.env.TZ).endOf('day').toDate();
      }
    }

    const callPlannings = await this.getCallPlannings({ startDate, endDate }, callCenterIds);

    // Generate all dates in the range
    const allDates = generateDateRange(startDate, endDate);

    // Calculate daily trend data
    const dailyCallTrendDataArray = groupAndSortByDate(historiesRaw as unknown as CallPlanningHistories[], 'callPlanning.displayDay', (items, date) => {
      return {
        date,
        connected: items.filter((h) => h.callPlanning?.callStatus === CallCenterStatus.COMPLETED).length,
        total: items.length,
      };
    });

    // Fill missing dates for trend data
    const filledTrendData = fillMissingDates(dailyCallTrendDataArray, allDates, { connected: 0, total: 0 });

    // Calculate daily compliance data using callPlannings
    const dailyComplianceDataArray = groupAndSortByDate(callPlannings.callPlannings, 'displayDay', (items, date) => {
      const metrics = calculateCallMetrics(items);
      const percentages = calculateCallPercentages(metrics);

      return {
        date,
        ...percentages,
        metricsRaw: metrics,
      };
    });

    // Fill missing dates for compliance data
    const filledComplianceData = fillMissingDates(dailyComplianceDataArray, allDates, { compliance: 0, rescheduled: 0, missed: 0 });

    // Calculate completed calls
    const completedCalls = historiesRaw.filter((history) => history.callPlanning?.callStatus === CallCenterStatus.COMPLETED).length;

    // Calculate average connect rate
    const avgConnectRate = total > 0 ? `${Math.round((completedCalls / total) * 100)}%` : '0%';

    // Calculate total duration in seconds
    const totalDurationInSeconds = historiesRaw.reduce((total, history) => {
      if (history.callDuration > 0) return total + history.callDuration;
      if (!history.startCallTime || !history.endCallTime) return total;
      const diffInSeconds = Math.floor((new Date(history.endCallTime).getTime() - new Date(history.startCallTime).getTime()) / 1000);
      return total + (diffInSeconds > 0 ? diffInSeconds : 0);
    }, 0);

    // Calculate total working hours in seconds
    const workingHoursPerDay = 8; // 8 hours per day
    const secondsPerHour = 3600; // 60 minutes * 60 seconds
    const totalWorkingHoursInSeconds = allDates.length * workingHoursPerDay * secondsPerHour;

    // Calculate utilization rate
    const utilizationRate = totalWorkingHoursInSeconds > 0 ? ((totalDurationInSeconds / totalWorkingHoursInSeconds) * 100).toFixed(2) : '0.00';

    // Calculate average duration in minutes
    const avgDurationInMinutes = completedCalls > 0 ? totalDurationInSeconds / completedCalls / 60 : 0;

    // Format the duration: if it's a whole number, show without decimals, otherwise show 2 decimals
    const formattedDuration = Number.isInteger(avgDurationInMinutes) ? avgDurationInMinutes.toString() : avgDurationInMinutes.toFixed(2);

    const metricsData = [
      {
        title: 'Total Calls Made',
        value: total,
      },
      {
        title: 'Avg. Connect Rate',
        value: avgConnectRate,
      },
      {
        title: 'Avg Call Duration',
        value: `${formattedDuration} minutes`,
      },
      {
        title: 'Utilization Rate',
        value: `${utilizationRate}%`,
      },
    ];

    return {
      metricsData,
      dailyCallTrendData: convertToShortDate(filledTrendData),
      dailyComplianceData: convertToShortDate(filledComplianceData),
    };
  }

  async getOrdersAndSalesMetricsByAgents(params: AgentsMetricsArgs & PaginationParams & OrderParams, callCenterIds: string[]) {
    let { startDate, endDate } = params;
    // If no date range provided, default to current month
    const today = moment().tz(process.env.TZ);
    if (!startDate && !endDate) {
      startDate = today.clone().startOf('month').toDate();
      endDate = today.clone().endOf('month').toDate();
    } else {
      // Convert ISO strings to Date objects if they are strings
      if (typeof startDate === 'string') {
        startDate = moment(startDate).tz(process.env.TZ).startOf('day').toDate();
      }
      if (typeof endDate === 'string') {
        endDate = moment(endDate).tz(process.env.TZ).endOf('day').toDate();
      }
    }

    const endToday = today.clone().endOf('day');
    if (moment(endDate).isAfter(endToday)) {
      endDate = endToday.toDate();
    }
    const query = this._repository
      .createQueryBuilder('history')
      .innerJoinAndSelect('history.callPlanning', 'callPlanning')
      .innerJoinAndSelect('callPlanning.callCenter', 'callCenter')
      .leftJoinAndSelect(
        (subQuery) => {
          return subQuery
            .select('call_planning_orders.callPlanningId', 'callPlanningId')
            .addSelect('JSON_AGG("call_planning_orders".*)', 'orderData')
            .addSelect('COALESCE(SUM(call_planning_orders.orderTotal), 0)', 'totalOrderValue')
            .addSelect('COALESCE(SUM(call_planning_orders.orderVolume), 0)', 'totalOrderVolume')
            .addSelect('COUNT(call_planning_orders.id)', 'totalOrdersPlaced')
            .from('call_planning_orders', 'call_planning_orders')
            .where('"call_planning_orders"."isDeleted" = false')
            .groupBy('call_planning_orders.callPlanningId');
        },
        'orders',
        '"orders"."callPlanningId" = "callPlanning"."id"',
      )
      .select([
        'CAST(COUNT(DISTINCT history.id) AS INTEGER) as "totalHistories"',
        'CAST(COALESCE(SUM("orders"."totalOrderValue"), 0) AS INTEGER) as "totalOrderValue"',
        // 'CAST(COALESCE(SUM("orders"."totalOrderVolume"), 0) AS INTEGER) as "totalOrderVolume"',
        'CAST(COALESCE(SUM(CASE WHEN "history"."status" = \'COMPLETED\' THEN "orders"."totalOrderVolume" ELSE 0 END), 0) AS INTEGER) as "totalOrderVolume"',
        'CAST(COALESCE(SUM(CASE WHEN "history"."status" = \'COMPLETED\' THEN "orders"."totalOrdersPlaced" ELSE 0 END), 0) AS INTEGER) as "totalOrdersPlaced"',
      ])
      .where('callCenter.id IN (:...callCenterIds)', { callCenterIds })
      .andWhere('"callPlanning"."isDeleted" = false')
      .andWhere('"callCenter"."isDeleted" = false');

    if (startDate) {
      query.andWhere('"callPlanning"."displayDay" >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('"callPlanning"."displayDay" <= :endDate', { endDate });
    }

    const result = await query.getRawOne();
    const metrics = {
      totalCalls: result.totalHistories,
      totalOrderValue: result.totalOrderValue,
      totalOrderVolume: convertToHL(result.totalOrderVolume),
      totalOrdersPlaced: result.totalOrdersPlaced,
    };
    return {
      metrics,
    };
  }

  async getCallReports(params: CallReportsArgs & PaginationParams & OrderParams, callCenterIds: string[]) {
    if (!callCenterIds?.length) {
      return {
        histories: [],
        total: 0,
      };
    }
    let { startDate, endDate } = params;
    const { offset, limit, orderBy, orderDesc = ConstantCommons.ORDER_DESC_DEFAULT, selectAll = false, search } = params;

    // If no date range provided, default to current month
    if (!startDate && !endDate) {
      const today = moment().tz(process.env.TZ);
      startDate = today.startOf('month').toDate();
      endDate = today.endOf('month').toDate();
    } else {
      // Convert ISO strings to Date objects if they are strings
      if (typeof startDate === 'string') {
        startDate = moment(startDate).tz(process.env.TZ).startOf('day').toDate();
      }
      if (typeof endDate === 'string') {
        endDate = moment(endDate).tz(process.env.TZ).endOf('day').toDate();
      }
    }

    const DURATION_EXPRESSION = `
      CASE 
        WHEN history."callDuration" IS NOT NULL AND history."callDuration" != 0 
        THEN history."callDuration" 
        ELSE EXTRACT(EPOCH FROM (history."endCallTime" - history."startCallTime"))
      END
    `;

    const ORDER_DATA_EXPRESSION = `
      CASE 
        WHEN history."status" = 'COMPLETED' THEN "orders"."orderData"
        ELSE '[]'::json
      END
    `;

    const ORDER_VALUE_EXPRESSION = `
      CASE 
        WHEN history."status" = 'COMPLETED' THEN "orders"."totalOrderValue"
        ELSE 0
      END
    `;

    const query = this._repository
      .createQueryBuilder('history')
      .innerJoinAndSelect('history.callPlanning', 'callPlanning')
      .innerJoinAndSelect('callPlanning.callCenter', 'callCenter')
      .innerJoinAndSelect('callPlanning.outlet', 'outlet')
      .leftJoinAndSelect(
        (subQuery) => {
          return subQuery
            .select('call_planning_orders.callPlanningId', 'callPlanningId')
            .addSelect('JSON_AGG("call_planning_orders".*)', 'orderData')
            .addSelect('COALESCE(SUM(call_planning_orders.orderTotal), 0)', 'totalOrderValue')
            .from('call_planning_orders', 'call_planning_orders')
            .where('"call_planning_orders"."isDeleted" = false')
            .groupBy('call_planning_orders.callPlanningId');
        },
        'orders',
        '"orders"."callPlanningId" = "callPlanning"."id"',
      )
      .select([
        '"history"."callPlanningRefId" as "id"',
        '"callPlanning"."displayDay" as "displayDay"',
        '"callCenter"."businessPartnerContactName2" as "agentName"',
        '"callCenter"."businessPartnerContactKey" as "agentId"',
        '"outlet"."businessPartnerName2" as "outletName"',
        `${ORDER_DATA_EXPRESSION} as "orderData"`,
        `${ORDER_VALUE_EXPRESSION} as "ordersTotalOrderValue"`,
        `${DURATION_EXPRESSION} as "durationInSeconds"`,
        '"callPlanning"."callStatus" as "callStatus"',
      ])
      .where('callCenter.id IN (:...callCenterIds)', { callCenterIds })
      .andWhere('"callPlanning"."callStatus" = :callStatus', { callStatus: CallCenterStatus.COMPLETED })
      .andWhere('"callPlanning"."isDeleted" = false')
      .andWhere('"callCenter"."isDeleted" = false')
      .andWhere('"outlet"."isDeleted" = false');
    // Apply sorting
    if (orderBy) {
      const orderDirection = orderDesc === ConstantCommons.ORDER_DESC_DEFAULT ? 'DESC' : 'ASC';
      switch (orderBy) {
        case 'callDuration':
          const sortableNull = orderDirection === 'DESC' ? 'NULLS LAST' : 'NULLS FIRST';
          query.orderBy(`${DURATION_EXPRESSION}`, orderDirection, sortableNull);
          break;
        case 'id':
          query.orderBy(`history.${orderBy}`, orderDirection);
          break;
        case 'agentName':
          query.orderBy(`callCenter.businessPartnerContactName2`, orderDirection);
          break;
        case 'tradingName':
          query.orderBy(`outlet.businessPartnerName2`, orderDirection);
          break;
        case 'calledDate':
          query.orderBy(`callPlanning.displayDay`, orderDirection);
          break;
        case 'orderValue':
          query.orderBy(`COALESCE("orders"."totalOrderValue", 0)`, orderDirection);
          break;
        default:
          break;
      }
    } else {
      query.orderBy(`callPlanning.displayDay`, 'DESC');
    }

    // Apply search
    if (search) {
      // Search by agent name, outlet name, outlet key, agent key
      query.andWhere(
        new Brackets((qb) => {
          qb.where('callCenter.businessPartnerContactName2 ILIKE :search', { search: `%${search}%` })
            .orWhere('outlet.businessPartnerName2 ILIKE :search', { search: `%${search}%` })
            .orWhere('outlet.businessPartnerKey ILIKE :search', { search: `%${search}%` })
            .orWhere('callCenter.businessPartnerContactKey ILIKE :search', { search: `%${search}%` });
        }),
      );
    }
    if (startDate) {
      query.andWhere('callPlanning.displayDay >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('callPlanning.displayDay <= :endDate', { endDate });
    }

    // Apply pagination
    if (!selectAll && offset && limit) {
      query.skip(offset).take(limit);
    }

    // Get total count and raw entities in one query
    const [total, entities] = await Promise.all([query.getCount(), query.getRawMany()]);

    const histories = entities.map((history) => {
      const agentName = history.agentName;
      const calledDate = formatDate(new Date(history.displayDay));
      const callDuration = history.durationInSeconds ? formatDuration(Math.round(parseFloat(history.durationInSeconds))) : '00:00:00';
      const orderPlaced = history.orderData ? this.mapOrdersToResponse(history.orderData) : [];
      const orderValue = history.ordersTotalOrderValue ? parseInt(history.ordersTotalOrderValue) : 0;
      const outletName = history.outletName;
      const callStatus = history.callStatus;
      return {
        id: history.id,
        agentName,
        agentId: history.agentId,
        tradingName: outletName,
        avatar: null,
        color: null,
        calledDate,
        callDuration,
        orderPlaced,
        orderValue,
        callStatus,
      };
    });
    return {
      histories,
      total,
    };
  }

  async exportCallReports(params: CallReportsArgs & PaginationParams & OrderParams, callCenterIds: string[], i18n: I18nContext) {
    const { histories } = await this.getCallReports(params, callCenterIds);
    const xlsxData = histories.map((history) => ({
      [i18n.translate('callCenter.call_plannings.reports.exports.headers.call_id')]: history.id,
      [i18n.translate('callCenter.call_plannings.reports.exports.headers.agent')]: history.agentId,
      [i18n.translate('callCenter.call_plannings.reports.exports.headers.trading_name')]: history.tradingName,
      [i18n.translate('callCenter.call_plannings.reports.exports.headers.called_date')]: history.calledDate,
      [i18n.translate('callCenter.call_plannings.reports.exports.headers.call_duration')]: history.callDuration,
      [i18n.translate('callCenter.call_plannings.reports.exports.headers.order_value')]: history.orderValue,
      [i18n.translate('callCenter.call_plannings.reports.exports.headers.order_placed')]: history.orderPlaced.map((order) => order.orderCode).join(', '),
    }));

    const fileName = 'Call_Reports';
    return await this._filesService.exportXLSXFile(fileName, xlsxData, 'Call Reports', null, { wch: 20 });
  }

  convertTwilioStatusToCallHistoryStatus(twilioStatus: string) {
    if (twilioStatus === TwilioDialCallStatus.ANSWERED) {
      return CallHistoryStatus.ANSWERED;
    }

    if (twilioStatus === TwilioDialCallStatus.BUSY) {
      return CallHistoryStatus.BUSY;
    }

    if (twilioStatus === TwilioDialCallStatus.CANCELED) {
      return CallHistoryStatus.CANCELED;
    }

    if (twilioStatus === TwilioDialCallStatus.COMPLETED) {
      return CallHistoryStatus.COMPLETED;
    }

    if (twilioStatus === TwilioDialCallStatus.FAILED) {
      return CallHistoryStatus.FAILED;
    }

    if (twilioStatus === TwilioDialCallStatus.NO_ANSWER) {
      return CallHistoryStatus.NO_ANSWER;
    }

    return CallHistoryStatus.IN_PROGRESS;
  }

  async getOutletCallHistoriesCount(outletIds: string[], startOfMonth: Date, endOfMonth: Date) {
    const sql = `
      SELECT outlet.id as "outletId", COUNT(history.id) as "callCount"
        FROM public."call_planning_histories" history
        LEFT JOIN public."call_plannings" call_planning ON history."callPlanningId" = call_planning.id
        LEFT JOIN public."business_partners" outlet ON call_planning."outletId" = outlet.id
        WHERE outlet.id = ANY($1)
          AND history."startCallTime" BETWEEN $2 AND $3
          AND history."isDeleted"=false
        GROUP BY outlet.id
    `;

    // Execute the raw query
    return await this._repository.query(sql, [outletIds, startOfMonth, endOfMonth]);
  }
}
