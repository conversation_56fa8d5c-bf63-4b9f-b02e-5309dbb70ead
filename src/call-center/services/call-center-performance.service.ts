import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { DateRangeDto } from 'src/master-data/dtos/date-range.dto';
import { User } from 'src/users/schemas/user.schema';
import { I18nContext } from 'nestjs-i18n';
import { CallPlanningHistoriesService } from './call-planning-histories.service';
import { BusinessPartnersContactService } from 'src/master-data/services/business-partners-contact.service';
import { calculatePercent, convertToHL, isEmptyObjectOrArray, roundNumber, roundToNearestInt } from 'src/utils';
import { CallPlanningOrdersService } from './call-planning-orders.service';
import { SalesOrderMetricsArgs } from '../dtos/sales-order-metrics.args';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { CallEfficiencyMetricsArgs } from '../dtos/call-efficiency-metrics.args';
import { AgentsMetricsArgs } from '../dtos/agents-metrics.args';
import { CallPlanningService } from './call-planning.service';
import { Between, In } from 'typeorm';
import { BusinessPartnersService } from 'src/master-data/services/business-partners.service';
import * as moment from 'moment-timezone';
import { OmsService } from '../../external/services/oms.service';
import { BusinessPartnerContact } from 'src/master-data/entities/business-partner-contact/business-partner-contact.entity';
import { CallCenterStatus } from '../enums/call-center.enum';
import { TargetSettingsService } from './target-settings.service';

@Injectable()
export class CallCenterPerformanceService {
  constructor(
    private readonly callPlanningHistoriesService: CallPlanningHistoriesService,
    private readonly businessPartnersContactService: BusinessPartnersContactService,
    private readonly callPlanningOrdersService: CallPlanningOrdersService,
    @Inject(forwardRef(() => CallPlanningService))
    private readonly callPlanningService: CallPlanningService,
    private readonly businessPartnerService: BusinessPartnersService,
    private readonly omsService: OmsService,
    private readonly targetSettingsService: TargetSettingsService,
  ) {}

  async getCallEfficiencyHistories(params: CallEfficiencyMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }

    // Getting call histories with pagination and sorting
    const callPlanningHistories = await this.callPlanningHistoriesService.getCallPlanningHistories(params, [callCenterId], true);

    return {
      callRecords: callPlanningHistories.callRecords,
      total: callPlanningHistories.total,
    };
  }

  async getCallEfficiencyMetrics(params: CallEfficiencyMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    // Select all to calculate metrics
    const queryParams = {
      ...params,
      selectAll: true,
    };
    const callPlanningMetrics = await this.callPlanningHistoriesService.getCallPlanningMetrics(queryParams, [callCenterId]);
    const response = {
      metricsData: callPlanningMetrics.metricsData,
      dailyCallTrendData: callPlanningMetrics.dailyCallTrendData,
      dailyComplianceData: callPlanningMetrics.dailyComplianceData,
    };
    return response;
  }

  async getStrikeRateMetrics(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, callCenterIds: string[], i18n: I18nContext) {
    // TODO: get this value later
    const { startDate, endDate } = params;
    const salePerformanceData = await this.callPlanningService.querySalesOrderPerformanceData(callCenterIds, startDate, endDate);

    const strikeRates = salePerformanceData
      .filter((item) => Number(item.completedPlans) > 0) // Avoid division by zero
      .map((item) => calculatePercent(item.hasOrderPlans, item.completedPlans));

    const averageStrikeRate = strikeRates.length > 0 ? roundNumber(strikeRates.reduce((sum, rate) => sum + rate, 0) / strikeRates.length) : 0;
    return averageStrikeRate;
  }
  async getAvgVolumeOrder(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    return '3';
  }

  async getAvgSKUOrder(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    return '11';
  }

  async getActiveSellingOutlets(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    return 90;
  }

  async getAvgOutletsOrderRate(dateRange: DateRangeDto, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    return 75;
  }

  async getSalesPerformanceTrends(dateRange: DateRangeDto, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    // TODO: remove this mocking data later
    const generateDummyData = (startDate: Date, endDate: Date) => {
      const dummyData = [];
      const currentDate = moment(startDate).tz(process.env.TZ);
      const end = moment(endDate).tz(process.env.TZ);

      // Generate data for each day in the date range
      while (currentDate <= end) {
        // Generate random values between 20-60 for averageVol and 5-30 for strikeRate
        const averageVol = Math.floor(Math.random() * (60 - 20 + 1)) + 20;
        const strikeRate = Math.floor(Math.random() * (30 - 5 + 1)) + 5;

        dummyData.push({
          date: currentDate.format('MM/DD'),
          averageVol,
          strikeRate,
        });

        // Move to next day
        currentDate.add(1, 'day');
      }

      return dummyData;
    };

    const dummyData = generateDummyData(dateRange.startDate, dateRange.endDate);
    return dummyData;
  }

  async getSalesAndOrderMetrics(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    const [strikeRate, avgVolumeOrder, avgSkuOrder, activeSellingOutlets, avgOutletOrderRate, salesPerformanceTrend] = await Promise.all([
      this.getStrikeRateMetrics(params, [callCenterId], i18n),
      this.getAvgSKUOrder(params, user, i18n),
      this.getAvgVolumeOrder(params, user, i18n),
      this.getActiveSellingOutlets(params, user, i18n),
      this.getAvgOutletsOrderRate(params, user, i18n),
      this.getSalesPerformanceTrends(params, user, i18n),
    ]);
    const result = {
      metricsData: [
        {
          title: 'Strike Rate',
          value: strikeRate,
        },
        {
          title: 'Avg. Volume/Order',
          value: avgVolumeOrder,
        },
        {
          title: 'Avg SKUs/Order',
          value: avgSkuOrder,
        },
        {
          title: 'Active selling Outlets',
          value: activeSellingOutlets,
        },
      ],
      averageOutletData: {
        title: 'Average Outlet Order Rate',
        value: avgOutletOrderRate, // Percentage value
      },
      dailyCallSaleData: salesPerformanceTrend,
    };
    return result;
  }

  async getSalesAndOrderHistories(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    // Getting call histories with pagination and sorting
    const orderHistories = await this.callPlanningOrdersService.getOrdersAndSalesHistories(params, [callCenterId]);

    return {
      orderRecords: orderHistories.orderRecords,
      total: orderHistories.total,
    };
  }

  async getAllAgentsMetrics(params: AgentsMetricsArgs, currentUser: any, i18n: I18nContext) {
    const contacts = currentUser.businessPartnerRelations?.contactIds || [];
    const ccmContactId = currentUser?.businessPartnerRelations?.originalContactId;
    if (!contacts.length) {
      throw new BadRequestException(i18n.translate('callCenter.errors.no_contacts'));
    }

    const filteredContacts = contacts.filter((contact: string) => !!contact && contact !== ccmContactId);
    const { agents } = await this.businessPartnersContactService.getAgentsPerformance(params, filteredContacts);

    // Calculate total strike rate by summing up numerators and denominators
    const totalStrikeRate = agents.reduce(
      (acc, agent) => {
        if (!agent.strikeRate) return acc;
        const { numerator, denominator } = agent.strikeRate;
        return {
          totalNumerator: acc.totalNumerator + (numerator || 0),
          totalDenominator: acc.totalDenominator + (denominator || 0),
        };
      },
      { totalNumerator: 0, totalDenominator: 0 },
    );

    const finalStrikeRate = totalStrikeRate.totalDenominator > 0 ? ((totalStrikeRate.totalNumerator / totalStrikeRate.totalDenominator) * 100).toFixed(2) : '0.00';

    // Calculate metrics data
    const { metrics } = await this.callPlanningHistoriesService.getOrdersAndSalesMetricsByAgents(params, filteredContacts);

    const metricsData = [
      {
        title: await i18n.translate('callCenter.metricsData.totalCallsMade'),
        value: metrics.totalCalls,
      },
      {
        title: await i18n.translate('callCenter.metricsData.totalOrderPlaced'),
        value: metrics.totalOrdersPlaced,
      },
      {
        title: await i18n.translate('callCenter.metricsData.totalVolumeSold'),
        value: metrics.totalOrderVolume,
      },
      {
        title: await i18n.translate('callCenter.metricsData.totalStrikeRate'),
        value: `${roundToNearestInt(Number(finalStrikeRate)) || 0} %`,
        // value: `${finalStrikeRate}%`,
      },
    ];
    return {
      metricsData,
    };
  }

  async getAllAgentsPerformance(params: AgentsMetricsArgs, currentUser: any, i18n: I18nContext) {
    const contacts = currentUser.businessPartnerRelations?.contactIds || [];
    const distributorIds = currentUser.businessPartnerRelations?.distributorIds || [];
    if (!distributorIds.length) {
      throw new BadRequestException(i18n.translate('callCenter.errors.no_distributor'));
    }
    if (!contacts.length) {
      throw new BadRequestException(i18n.translate('callCenter.errors.no_contacts'));
    }
    const filteredContacts = contacts.filter((contact: string) => !!contact);
    const callCenters = await this.businessPartnersContactService.find({
      where: {
        id: In(filteredContacts),
      },
    });
    if (!callCenters.length) {
      throw new BadRequestException(i18n.translate('callCenter.errors.no_call_centers'));
    }
    const { agents, total } = await this.businessPartnersContactService.getAgentsPerformance(params, filteredContacts);

    // get the first distributor id only because currently, we only have one distributor for one manager
    const mainDistributorId = distributorIds[0];
    // Get distributor data
    const rawDistributor = await this.businessPartnerService.findById(mainDistributorId);
    const distributor = {
      id: rawDistributor.id,
      name: rawDistributor.businessPartnerName1,
      tradingName: rawDistributor.businessPartnerName2,
    };
    return {
      agentRecords: agents?.map((agent) => ({
        ...agent,
        strikeRate: {
          numerator: agent.strikeRate?.numerator || 0,
          denominator: agent.strikeRate?.denominator || 0,
          value: roundToNearestInt(agent.strikeRate?.value) || 0,
          target: roundToNearestInt(agent.strikeRate?.target) || 0,
        },
        callCoverage: {
          numerator: agent.callCoverage?.numerator || 0,
          denominator: agent.callCoverage?.denominator || 0,
          value: roundToNearestInt(agent.callCoverage?.value) || 0,
          target: roundToNearestInt(agent.callCoverage?.target) || 0,
        },
        volumeTarget: {
          denominator: agent.volumeTarget?.denominator,
          numerator: agent.volumeTarget?.numerator,
          value: roundToNearestInt(agent.volumeTarget?.value) || 0,
          target: roundToNearestInt(agent.volumeTarget?.target) || 0,
        },
      })),
      total,
      distributor,
    };
  }

  async getManagerCallEfficiencyMetrics(params: CallEfficiencyMetricsArgs & PaginationParams & OrderParams, currentUser: any, i18n: I18nContext) {
    const contacts = currentUser.businessPartnerRelations?.contactIds || [];
    if (!contacts.length) {
      throw new BadRequestException(i18n.translate('callCenter.errors.no_contacts'));
    }
    const filteredContacts = contacts.filter((contact: string) => !!contact);
    const callCenters = await this.businessPartnersContactService.find({
      where: {
        id: In(filteredContacts),
      },
    });
    if (!callCenters.length) {
      throw new BadRequestException(i18n.translate('callCenter.errors.no_call_centers'));
    }
    const callCenterIds = callCenters.map((callCenter) => callCenter.id);
    // Select all to calculate metrics
    const queryParams = {
      ...params,
      selectAll: true,
    };
    const callPlanningMetrics = await this.callPlanningHistoriesService.getCallPlanningMetrics(queryParams, callCenterIds);
    const response = {
      metricsData: callPlanningMetrics.metricsData,
      dailyCallTrendData: callPlanningMetrics.dailyCallTrendData,
      dailyComplianceData: callPlanningMetrics.dailyComplianceData,
    };
    return response;
  }

  async getManagerStrikeRateMetrics(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    return '25%';
  }
  async getManagerAvgVolumeOrder(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    return '3';
  }

  async getManagerAvgSKUOrder(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    return '11';
  }

  async getManagerActiveSellingOutlets(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    return 90;
  }

  async getManagerAvgOutletsOrderRate(dateRange: DateRangeDto, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    return 75;
  }

  async getManagerSalesPerformanceTrends(dateRange: DateRangeDto, user: User, i18n: I18nContext) {
    // TODO: get this value later
    const contacts = await this.businessPartnersContactService.getCurrentContact(user);
    const callCenterId = contacts?.contact?.id;
    if (isEmptyObjectOrArray(contacts) || !callCenterId) {
      throw new BadRequestException(i18n.translate('callCenter.errors.not_found'));
    }
    // TODO: remove this mocking data later
    const generateDummyData = (startDate: Date, endDate: Date) => {
      const dummyData = [];
      const currentDate = moment(startDate).tz(process.env.TZ);
      const end = moment(endDate).tz(process.env.TZ);

      // Generate data for each day in the date range
      while (currentDate <= end) {
        // Generate random values between 20-60 for averageVol and 5-30 for strikeRate
        const averageVol = Math.floor(Math.random() * (60 - 20 + 1)) + 20;
        const strikeRate = Math.floor(Math.random() * (30 - 5 + 1)) + 5;

        dummyData.push({
          date: currentDate.format('MM/DD'),
          averageVol,
          strikeRate,
        });

        // Move to next day
        currentDate.add(1, 'day');
      }

      return dummyData;
    };

    const dummyData = generateDummyData(dateRange.startDate, dateRange.endDate);
    return dummyData;
  }

  async getManagerSalesAndOrderMetrics(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, user: any, i18n: I18nContext) {
    const callCenterIds = (user?.businessPartnerRelations?.contactIds || []).filter((contactId) => !!contactId && contactId !== user?.businessPartnerRelations?.originalContactId);
    const [strikeRate, avgVolumeOrder, avgSkuOrder, activeSellingOutlets, avgOutletOrderRate, salesPerformanceTrend] = await Promise.all([
      this.getStrikeRateMetrics(params, callCenterIds, i18n),
      this.getManagerAvgSKUOrder(params, user, i18n),
      this.getManagerAvgVolumeOrder(params, user, i18n),
      this.getManagerActiveSellingOutlets(params, user, i18n),
      this.getManagerAvgOutletsOrderRate(params, user, i18n),
      this.getManagerSalesPerformanceTrends(params, user, i18n),
    ]);
    const result = {
      metricsData: [
        {
          title: 'Strike Rate',
          value: strikeRate,
        },
        {
          title: 'Avg. Volume/Order',
          value: avgVolumeOrder,
        },
        {
          title: 'Avg SKUs/Order',
          value: avgSkuOrder,
        },
        {
          title: 'Active selling Outlets',
          value: activeSellingOutlets,
        },
      ],
      averageOutletData: {
        title: 'Average Outlet Order Rate',
        value: avgOutletOrderRate, // Percentage value
      },
      dailyCallSaleData: salesPerformanceTrend,
    };
    return result;
  }

  /**
   *
   * @param depotBusinessPartnerKey
   * @param outletBusinessPartnerKey
   * @param startDate
   * @param endDate
   */
  async getPerformanceOutletVolume(
    depotBusinessPartnerKey: string,
    outletBusinessPartnerKey: string,
    startDate: Date = moment().startOf('month').toDate(),
    endDate: Date = moment().endOf('month').toDate(),
  ) {
    const performanceData = await this.omsService.getPerformanceOutletSaleVolume(
      depotBusinessPartnerKey,
      { outletExternalID: outletBusinessPartnerKey },
      startDate,
      endDate,
      true,
      true,
      true,
      true,
      true,
    );

    return {
      // average_volume_per_order: {
      //   value: parseFloat((performanceData?.average_volume?.value || 0)?.toFixed(2)),
      //   label: 'HL',
      // },
      // volume_performance: {
      //   value: parseFloat(Math.abs(performanceData?.volume_performance?.value || 0).toFixed(2)),
      //   is_increasing: performanceData?.volume_performance?.value > 0,
      //   current_month: performanceData?.volume_performance?.current_month,
      //   current_year: performanceData?.volume_performance?.current_year,
      //   current_month_total: parseFloat(performanceData?.volume_performance?.current_month_total?.toFixed(2)),
      //   last_month: performanceData?.volume_performance?.last_month,
      //   last_year: performanceData?.volume_performance?.last_year,
      //   last_month_total: parseFloat(performanceData?.volume_performance?.last_month_total?.toFixed(2)),
      //   label_total: 'HL',
      // },
      year_to_date_vol: {
        total: performanceData?.year_to_date?.total ? parseFloat(performanceData?.year_to_date?.total?.toFixed(2)) : 0,
        label: 'HL',
        top_outlet: 0,
      },
      // monthly_volume: generateFullMonthlyRange(performanceData?.monthly_volume || [], 'total'),
      // channel_monthly_average_volume: generateFullMonthlyRange(performanceData?.channel_monthly_average_volume || [], 'average')?.map((item) => ({
      //   ...item,
      //   average: parseFloat(Number(item.average).toFixed(2)),
      // })),
    };
  }
}
