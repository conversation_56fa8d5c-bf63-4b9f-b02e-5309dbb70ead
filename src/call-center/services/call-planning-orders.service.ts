import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BusinessPartnerType } from '../../master-data/constants/business-partner.enum';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { BusinessPartnersService } from '../../master-data/services/business-partners.service';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { isEmptyObjectOrArray, printLog } from '../../utils';
import { CallPlanningOrders } from '../entities/call-planning-order.entity';
import * as moment from 'moment-timezone';
import { formatDate } from 'src/utils/helpers/date';
import { ConstantCommons } from 'src/utils/constants';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { SalesOrderMetricsArgs } from '../dtos/sales-order-metrics.args';
import { CallPlanOrderSource } from '../enums/call-center.enum';

@Injectable()
export class CallPlanningOrdersService extends BaseSQLService<CallPlanningOrders> {
  constructor(
    @InjectRepository(CallPlanningOrders)
    private readonly _callPlanningOrdersRepository: Repository<CallPlanningOrders>,
    private readonly businessPartnerService: BusinessPartnersService,
    private readonly businessPartnersContactService: BusinessPartnersContactService,
  ) {
    super();
    this._repository = this._callPlanningOrdersRepository;
  }

  private mapOrderToResponse(order: CallPlanningOrders) {
    return {
      ...order,
      orderDate: formatDate(order.orderDate),
    };
  }

  async saveOrderToCallPlanningOrders(outletUCC: string, order: any, currentUser: any, planId?: string): Promise<CallPlanningOrders> {
    try {
      // Get Outlet from BusinessPartner
      const outlet = await this.businessPartnerService.findOne({
        where: {
          businessPartnerKey: outletUCC,
          businessPartnerType: BusinessPartnerType.OUTLET.toString(),
        },
      });
      if (!outlet) {
        throw new Error(`Outlet with UCC ${outletUCC} not found`);
      }
      // Get CallCenter from currentUser
      const contacts = await this.businessPartnersContactService.getCurrentContact(currentUser);
      const callCenterId = contacts?.contact?.id;
      if (isEmptyObjectOrArray(contacts) || !callCenterId) {
        throw new Error(`Call Center not found`);
      }

      return await this._callPlanningOrdersRepository.save({
        callPlanningId: planId || null,
        outlet: { id: outlet.id },
        callCenter: { id: callCenterId },
        orderDate: order?.created_at,
        orderDateUTC: moment(order?.created_at).utc(),
        orderId: order?.id,
        orderCode: order?.display_id,
        orderStatus: order?.status,
        orderTotal: order?.total || 0,
        orderVolume: (order?.items || []).reduce((sum, item) => sum + item.volume * item.quantity, 0),
        orderSource: CallPlanOrderSource.CALL_CENTER || 'CALL-CENTER',
        invoices: null,
      });
    } catch (error) {
      console.error('Error saving call center order:', error);
      throw error;
    }
  }

  async syncOrderStatusToCallPlanningOrders(orders: any, outletExternalID: string, depotExternalID: string): Promise<CallPlanningOrders[]> {
    try {
      if (isEmptyObjectOrArray(orders)) {
        return;
      }
      const results: CallPlanningOrders[] = [];

      for (const order of orders) {
        const orderId = order?.id;
        if (!orderId) {
          printLog(`Skipping order due to missing orderId.`);
          continue;
        }

        printLog('outletUCC', outletExternalID);
        if (!outletExternalID) {
          continue;
        }

        const outlet = await this.businessPartnerService.findOne({
          where: {
            businessPartnerKey: outletExternalID,
            businessPartnerType: BusinessPartnerType.OUTLET.toString(),
          },
        });
        if (!outlet) {
          continue;
        }

        const existingOrder = await this._callPlanningOrdersRepository.findOne({
          where: { orderId },
        });

        const orderData = {
          outlet: { id: outlet.id },
          orderId: orderId,
          orderCode: order?.display_id,
          orderDate: moment(order?.created_at),
          orderDateUTC: moment(order?.created_at).utc(),
          orderStatus: order?.status,
          orderSource: order?.source_system,
          orderTotal: order?.total || 0,
          orderVolume: (order?.items || []).reduce((sum, item) => sum + item.volume * item.quantity, 0),
          invoices:
            order?.invoices?.map((invoice: any) => ({
              invoiceId: invoice.id,
              amount: invoice.total,
              date: invoice.created_at,
            })) || [],
        };

        printLog('orderData', orderData);

        let savedOrder: any;
        if (existingOrder) {
          savedOrder = await this._callPlanningOrdersRepository.update(existingOrder.id, {
            ...orderData,
          });
        } else {
          savedOrder = await this._callPlanningOrdersRepository.save(orderData);
        }
        results.push(savedOrder);
      }
      return results;
    } catch (error) {
      printLog('Error syncing order status to call planning orders:', error);
      throw error;
    }
  }

  async getByOutlet(searchParams: any) {
    return this._repository.find({
      where: searchParams,
      order: { orderDate: 'desc' },
    });
  }

  async getOrdersAndSalesHistories(params: SalesOrderMetricsArgs & PaginationParams & OrderParams, callCenterIds: string[]) {
    let { startDate, endDate } = params;
    // If no date range provided, default to last week
    if (!startDate && !endDate) {
      endDate = moment().tz(process.env.TZ).endOf('day').toDate();
      startDate = moment().tz(process.env.TZ).subtract(7, 'days').startOf('day').toDate();
    } else {
      // Convert ISO strings to Date objects if they are strings
      if (typeof startDate === 'string') {
        startDate = moment(startDate).tz(process.env.TZ).startOf('day').toDate();
      }
      if (typeof endDate === 'string') {
        endDate = moment(endDate).tz(process.env.TZ).endOf('day').toDate();
      }
    }

    const { offset, limit, orderBy, orderDesc = ConstantCommons.ORDER_DESC_DEFAULT } = params;
    const query = this._repository
      .createQueryBuilder('order')
      .innerJoinAndSelect('order.callCenter', 'callCenter')
      .innerJoinAndSelect('order.outlet', 'outlet')
      .select([
        'order.id as "id"',
        'order.orderDateUTC as "orderDate"',
        'order.orderId as "orderId"',
        'order.orderCode as "orderCode"',
        'order.orderStatus as "orderStatus"',
        'order.isActive as "isActive"',
        'outlet.businessPartnerName1 as "tradingName"',
        'order.orderTotal as "orderValue"',
      ])
      .where('order.callCenterId IN (:...callCenterIds)', { callCenterIds })
      .andWhere('order.isDeleted = false')
      .andWhere('callCenter.isDeleted = false')
      .andWhere('outlet.isDeleted = false');

    if (startDate) {
      query.andWhere('order.orderDateUTC >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('order.orderDateUTC <= :endDate', { endDate });
    }

    // Apply sorting if specified
    if (orderBy) {
      const direction = orderDesc === ConstantCommons.ORDER_DESC_DEFAULT ? 'DESC' : 'ASC';
      query.orderBy(`"${orderBy}"`, direction);
    } else {
      // Default sorting by orderDate descending
      query.orderBy('"orderDate"', 'DESC');
    }

    // Apply pagination
    // In some case, if selectAll is true, don't apply pagination, for example: get all orderes for metrics calculation
    if (!params.selectAll && offset && limit) {
      query.skip(offset).take(limit);
    }

    const [orders, total] = await Promise.all([query.getRawMany(), query.getCount()]);
    let result = [];
    if (orders?.length) {
      result = orders.map((order: CallPlanningOrders) => this.mapOrderToResponse(order));
    }

    return {
      orderRecords: result,
      total,
    };
  }
}
