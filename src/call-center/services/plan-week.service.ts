import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, LessThanOrEqual, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { PlanWeek } from '../entities/plan-week.entity';
import * as moment from 'moment-timezone';

@Injectable()
export class PlanWeekService extends BaseSQLService<PlanWeek> {
  constructor(
    @InjectRepository(PlanWeek)
    private readonly _planWeekRepository: Repository<PlanWeek>,
  ) {
    super();
    this._repository = this._planWeekRepository;
  }

  async getLastWeek(): Promise<PlanWeek> {
    return this._planWeekRepository.findOne({ where: {}, order: { createdAt: 'desc' } });
  }

  async getTheNearestCycles() {
    const currentWeek = await this.getCurrentWeek();
    const currentCycleId = String(currentWeek.cycle);

    const currentCycleTimeRange = await this.getCycleTimeRange(currentCycleId);

    // 1 cycle = 4 weeks
    const startOfPastThreeCycles = moment(currentCycleTimeRange.startTime).tz(process.env.TZ).subtract(12, 'weeks');
    const endOfNextThreeCycles = moment(currentCycleTimeRange.endTime).tz(process.env.TZ).add(12, 'weeks');

    const weeks = await this.getWeeksByTimeRange({ startTime: startOfPastThreeCycles.toDate(), endTime: endOfNextThreeCycles.toDate() });

    const cycles: any[] = Object.values(
      weeks.reduce((initial, week) => {
        const cycleId = String(week.cycle.id);
        const previousCycle = initial[cycleId];

        const currentWeek = {
          id: week.id,
          name: `Week ${week.weekNumber}`,
          startTime: week.startTime,
        };

        if (!previousCycle) {
          return {
            ...initial,
            [cycleId]: {
              id: cycleId,
              name: week.cycle.cycleName,
              year: week.cycle.year,
              weeks: [currentWeek],
              isCurrentCycle: currentCycleId === cycleId,
            },
          };
        }

        return {
          ...initial,
          [cycleId]: {
            ...previousCycle,
            weeks: [...previousCycle.weeks, currentWeek],
          },
        };
      }, {}),
    );

    return cycles
      .map((cycle) => {
        const { startTime, endTime } = this.transformTimeRange(cycle.weeks);

        return {
          ...cycle,
          startTime: startTime.toDate(),
          endTime: endTime.toDate(),
        };
      })
      .sort((previous, next) => +previous.startTime - +next.startTime);
  }

  async getCycleTimeRange(cycleId: string) {
    const weeks = await this.getWeeksByCycleId(cycleId);
    return this.transformTimeRange(weeks);
  }

  async getCurrentWeek() {
    const currentTime = moment().tz(process.env.TZ);
    return this._repository.findOne({
      where: {
        startTime: LessThanOrEqual(currentTime.toDate()),
      },
      order: {
        startTime: 'DESC',
      },
    });
  }

  async getWeeksByCycleId(cycleId: string) {
    return this._planWeekRepository.find({
      where: {
        cycle: { id: cycleId },
      },
      order: { startTime: 'ASC' },
    });
  }

  async getWeeksByTimeRange({ startTime, endTime }: { startTime: Date; endTime: Date }) {
    return this._planWeekRepository.find({
      where: {
        startTime: Between(startTime, endTime),
      },
      relations: ['cycle'],
    });
  }

  async findWeekWithinSevenDaysOfDate(dateString: string): Promise<PlanWeek | null> {
    const targetDateMoment = moment(dateString).tz(process.env.TZ);

    return this._planWeekRepository.findOne({
      where: {
        startTime: LessThanOrEqual(targetDateMoment.toDate()),
      },
      relations: ['cycle'],
      order: { startTime: 'DESC' }, // Order by startTime, ASC is a common default.
    });
  }

  transformTimeRange(weeks: PlanWeek[]) {
    if (!weeks.length) {
      return null;
    }

    const sortedWeeks = weeks.sort((previous, next) => +previous.startTime - +next.startTime);

    const startTime = moment(sortedWeeks[0].startTime).tz(process.env.TZ);
    const endTime = moment(sortedWeeks[sortedWeeks.length - 1].startTime)
      .tz(process.env.TZ)
      .tz(process.env.TZ)
      .add(6, 'days')
      .endOf('date');

    return {
      startTime,
      endTime,
    };
  }

  getDateAndDayNameInWeek(week: PlanWeek) {
    const startTime = moment(week.startTime).tz(process.env.TZ);
    const initial = {};
    for (let i = 0; i < 7; i++) {
      const date = startTime.clone().add(i, 'day').format('DD-MM-YYYY');
      initial[date] = i + 1;
    }

    return initial;
  }

  getDayNameAndDateInWeek(week: PlanWeek) {
    const startTime = moment(week.startTime).tz(process.env.TZ);
    const initial: Record<string, Date> = {};
    for (let i = 0; i < 7; i++) {
      const date = startTime.clone().add(i, 'day').toDate();
      initial[i + 1] = date;
    }

    return initial;
  }
}
