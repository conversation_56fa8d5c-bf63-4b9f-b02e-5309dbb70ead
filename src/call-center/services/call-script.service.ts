import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository, InsertResult, IsNull, FindManyOptions } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { CallScript } from '../entities/call-script.entity';
import { I18nContext } from 'nestjs-i18n';
import { CallPlanImportMappingType, ContactDistributorRelationsDataMap } from '../constants/call-plan.type';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { BusinessPartnersService } from '../../master-data/services/business-partners.service';
import { BusinessPartnerType } from '../../master-data/constants/business-partner.enum';
import { isArray } from 'class-validator';

@Injectable()
export class CallScriptService extends BaseSQLService<CallScript> {
  constructor(
    @InjectRepository(CallScript)
    private readonly _callScriptRepository: Repository<CallScript>,

    private readonly _businessPartnerContactService: BusinessPartnersContactService,
    private readonly _businessPartnerService: BusinessPartnersService,
  ) {
    super();
    this._repository = this._callScriptRepository;
  }

  async validateImportScript(mappingResult: CallPlanImportMappingType, contactManagerRelations: any): Promise<ContactDistributorRelationsDataMap> {
    const { contactIds, depotIds, distributorIds, outletIds, originalContactId } = contactManagerRelations || {};
    const [contacts, businessPartners] = await Promise.all([
      this._businessPartnerContactService.findWithOptions({
        where: {
          id: In(contactIds.filter((id) => id !== originalContactId)),
          isDeleted: false,
        },
        select: ['id', 'businessPartnerContactKey'],
      }),
      this._businessPartnerService.findWithOptions({
        where: {
          id: In([...depotIds, ...outletIds, ...distributorIds]),
          isDeleted: false,
        },
        select: ['id', 'businessPartnerKey', 'businessPartnerType'],
      }),
    ]);

    const { distributorMap, outletMap } = businessPartners.reduce(
      (acc, bP) => {
        if (bP.businessPartnerType === BusinessPartnerType.DISTRIBUTOR) {
          acc.distributorMap.set(bP.businessPartnerKey, bP);
        }

        if (bP.businessPartnerType === BusinessPartnerType.OUTLET) {
          acc.outletMap.set(bP.businessPartnerKey, bP);
        }
        return acc;
      },
      { distributorMap: new Map(), outletMap: new Map() },
    );

    const callCenterMap = new Map(contacts.map((cc) => [cc.businessPartnerContactKey, cc]));
    const callCenterOutletPair = new Map<string, string>();
    const scripts = mappingResult.callScript || [];
    for (let i = 0; i < scripts.length; i++) {
      const { callCenter, outlet, distributor } = scripts[i];
      const outletKey = outlet ? String(outlet) : outlet;
      const callCenterKey = callCenter ? String(callCenter) : callCenter;
      const distributorKey = String(distributor);

      if (!distributorMap.get(distributorKey)) {
        throw new BadRequestException('Invalid Distributor Key');
      }

      if (!!callCenterKey && !callCenterMap.get(callCenterKey)) {
        throw new BadRequestException('Invalid Call Center Key');
      }

      if (!!outletKey && !outletMap.get(outletKey)) {
        throw new BadRequestException('Invalid Outlet Key');
      }

      const relationKeyPair = `${distributorKey}-${outletKey}-${callCenterKey}`;
      if (callCenterOutletPair.get(relationKeyPair)) {
        throw new BadRequestException('Duplicate script!');
      }
      callCenterOutletPair.set(relationKeyPair, relationKeyPair);
    }

    return {
      contactMap: callCenterMap,
      outletMap,
      distributorMap,
    };
  }

  async importCallScripts(
    mappingResult: CallPlanImportMappingType,
    contactDistributorRelationDataMap: ContactDistributorRelationsDataMap,
    i18n: I18nContext,
  ): Promise<CallScript[]> {
    const { contactMap, outletMap, distributorMap } = contactDistributorRelationDataMap;
    const scriptsToImport = mappingResult.callScript;

    if (!scriptsToImport || scriptsToImport.length === 0) {
      return [];
    }

    const callScriptsToCreate: CallScript[] = [];

    for (let i = 0; i < scriptsToImport.length; i++) {
      const scriptItem = scriptsToImport[i];
      const { outlet, callCenter, content, distributor } = scriptItem;

      const outletData = outletMap.get(String(outlet));
      const contactData = contactMap.get(String(callCenter));
      const distributorData = distributorMap.get(String(distributor));

      let parsedContent: object;
      if (typeof content === 'string') {
        try {
          parsedContent = JSON.parse(content);
          // Ensure the parsed content is a JSON object or array, not a primitive.
          if (typeof parsedContent !== 'object' || parsedContent === null) {
            throw new Error('Parsed JSON content is not an object or array.');
          }
        } catch (error) {
          const errorMessage = i18n.t('call_center.errors.invalidContentJsonFormat', {
            args: { row: i + 1, details: error.message },
          });
          throw new BadRequestException(errorMessage);
        }
      } else if (typeof content === 'object' && content !== null) {
        // Content is already an object (or array), suitable for jsonb.
        parsedContent = content;
      } else {
        // Content is not a string and not a non-null object (e.g., undefined, number, boolean, or explicit null not caught by `typeof content === 'object' && content !== null` for the object path).
        const errorMessage = i18n.t('call_center.errors.invalidContentType', {
          args: { row: i + 1 },
        });
        throw new BadRequestException(errorMessage);
      }

      let importedScript = await this._callScriptRepository.findOne({
        where: {
          isDeleted: false,
          distributorId: distributorData?.id,
          outlet: outletData?.id
            ? {
                id: outletData?.id,
              }
            : IsNull(),
          callCenter: contactData?.id
            ? {
                id: contactData?.id,
              }
            : IsNull(),
        },
      });

      if (importedScript) {
        importedScript.content = parsedContent;
      } else {
        importedScript = this._callScriptRepository.create({
          distributorId: distributorData?.id,
          outlet: outletData?.id
            ? {
                id: outletData?.id,
              }
            : null,
          callCenter: contactData?.id
            ? {
                id: contactData?.id,
              }
            : null,
          content: parsedContent,
        });
      }

      callScriptsToCreate.push(importedScript);
    }

    return this._callScriptRepository.save(callScriptsToCreate);
  }

  async getOutletAgentSript(outletId: string, contactId: string, distributorIds: string[]) {
    if (distributorIds.length < 1) {
      return {};
    }

    const agentScriptsQueries: any = [
      {
        outlet: IsNull(),
        callCenter: IsNull(),
        isDeleted: false,
        distributorId: In(distributorIds),
      },
    ];

    if (outletId && contactId) {
      agentScriptsQueries.push({
        isDeleted: false,
        distributorId: In(distributorIds),
        outlet: {
          id: outletId,
        },
        callCenter: {
          id: contactId,
        },
      });
    }
    if (outletId) {
      agentScriptsQueries.push({
        outlet: { id: outletId },
        callCenter: IsNull(),
        isDeleted: false,
        distributorId: In(distributorIds),
      });
    }
    if (contactId) {
      agentScriptsQueries.push({
        outlet: IsNull(),
        callCenter: { id: contactId },
        isDeleted: false,
        distributorId: In(distributorIds),
      });
    }

    const agentScripts = await this._callScriptRepository.find({
      where: agentScriptsQueries,
    });

    const { outletContactScript, outletScript, contactScript } = agentScripts.reduce(
      (acc, script) => {
        if (script.outlet?.id === outletId && script.callCenter?.id === contactId) {
          acc.outletContactScript = script;
        }
        if (script?.outlet?.id === outletId && !script?.callCenter?.id) {
          acc.outletScript = script;
        }
        if (script.callCenter?.id === contactId && !script?.outlet?.id) {
          acc.contactScript = script;
        }
        return acc;
      },
      { outletContactScript: null, outletScript: null, contactScript: null },
    );

    return outletContactScript || outletScript || contactScript || agentScripts[0];
  }
}
