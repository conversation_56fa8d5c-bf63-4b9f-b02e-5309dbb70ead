import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, In, Not, Repository } from 'typeorm';
import { Notification } from '../entities/notification.entity';
import { BaseSQLService } from 'src/shared/services/basesql.service';
import { NotificationStatus } from '../enums/call-center.enum';
import { isEmptyObjectOrArray, printLog } from '../../utils';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { I18nContext } from 'nestjs-i18n';

@Injectable()
export class NotificationService extends BaseSQLService<Notification> {
  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepo: Repository<Notification>,
    private readonly eventEmitter: EventEmitter2,
  ) {
    super();
    this._repository = this.notificationRepo;
  }

  /**
   * Create a notification
   * @param data
   * @returns
   */
  async create(data: Partial<Notification>): Promise<Notification> {
    try {
      const notification = this.notificationRepo.create(data);
      const savedNotification = await this.notificationRepo.save(notification);

      // Emit event after creating notification
      this.eventEmitter.emit('notification.created', savedNotification);

      return savedNotification;
    } catch (e) {
      printLog(e);
    }
  }

  /**
   * Mark a notification as read
   * @param id
   * @param fromUserId
   * @returns
   */
  async markAsRead(id: string): Promise<any> {
    // Find the notification using both primary keys
    const notification = await this.notificationRepo.findOne({ where: { id } });
    if (!notification) {
      throw new Error('Notification not found');
    }
    // Update using both primary keys
    await this.notificationRepo.update({ id, fromUserId: notification.fromUserId }, { status: NotificationStatus.READ });
    return this.notificationRepo.findOne({ where: { id } });
  }

  async markAsReadAll(ids: string[]): Promise<any> {
    // Find all notifications for the given ids
    const notifications = await this.notificationRepo.find({ where: { id: In(ids) } });
    if (!notifications.length) {
      throw new Error('Notifications not found');
    }
    // Update all notifications
    for (const notification of notifications) {
      await this.notificationRepo.update({ id: notification.id, fromUserId: notification.fromUserId }, { status: NotificationStatus.READ });
    }
    // Return all updated notifications
    return this.notificationRepo.find({ where: { id: In(ids) } });
  }

  /**
   * Delete a notification
   * @param id
   * @returns
   */
  async delete(id: string): Promise<DeleteResult> {
    return this.notificationRepo.delete(id);
  }

  /**
   * Update a notification (for fields like title, data, priority, icon)
   * @param id
   * @param updateData
   * @returns
   */
  async update(id: string, updateData: Partial<Notification>): Promise<Notification> {
    await this.notificationRepo.update(id, updateData);
    return this.notificationRepo.findOne({ where: { id } });
  }

  /**
   *
   * @param userId
   * @param depotIds
   * @param offset
   * @param limit
   * @param i18n
   * @param groupByDate
   */
  async getNotificationByManager(userId: string, depotIds: [], offset = 0, limit = 20, i18n: I18nContext, groupByDate = true) {
    const notifications = await this.notificationRepo.find({
      where: {
        fromUserId: Not(userId),
        depotId: In(depotIds),
      },
      order: { createdAt: 'DESC' },
      skip: offset,
      take: limit,
    });
    return groupByDate ? await this.notificationGroupByDate(notifications, i18n) : notifications;
  }

  /**
   *
   * @param userId
   * @param offset
   * @param limit
   * @param i18n
   * @param groupByDate
   */
  async getNotificationByAgent(userId: string, offset = 0, limit = 20, i18n: I18nContext, groupByDate = true) {
    const notifications = await this.notificationRepo.find({ where: { toUserId: userId }, order: { createdAt: 'DESC' }, skip: offset, take: limit });
    return groupByDate ? await this.notificationGroupByDate(notifications, i18n) : notifications;
  }

  async getUnReadCountByManager(userId: string, depotIds: []) {
    return await this.notificationRepo.count({
      where: {
        fromUserId: Not(userId),
        depotId: In(depotIds),
        status: NotificationStatus.UNREAD,
      },
      order: { createdAt: 'DESC' },
    });
  }

  async getUnReadCountByAgent(userId: string) {
    return await this.notificationRepo.count({
      where: { toUserId: userId, status: NotificationStatus.UNREAD },
      order: { createdAt: 'DESC' },
    });
  }

  async notificationGroupByDate(notifications: Notification[], i18n: I18nContext) {
    if (isEmptyObjectOrArray(notifications)) return [];
    const groupedNotifications = notifications.reduce((acc, notification) => {
      const date = new Date(notification.createdAt).toLocaleDateString(i18n.lang);

      if (!acc[date]) {
        acc[date] = [];
      }

      acc[date].push(notification);
      return acc;
    }, {} as Record<string, Notification[]>);

    return Object.entries(groupedNotifications).map(([date, items]) => ({
      date,
      items,
    }));
  }
}
