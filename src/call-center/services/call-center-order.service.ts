import { Injectable } from '@nestjs/common';
import { BusinessPartnerType, BusinessPartnerRelationType } from '../../master-data/constants/business-partner.enum';
import { BusinessPartnersService } from '../../master-data/services/business-partners.service';
import { BusinessPartnerRelationService } from '../../master-data/services/business-partners-relation.service';
import { OutletsService } from 'src/outlets/services/outlets.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BusinessPartner } from '../../master-data/entities/business-partner/business-partner.entity';

@Injectable()
export class CallCenterOrderService {
  constructor(
    private readonly outletService: OutletsService,
    private readonly businessPartnerService: BusinessPartnersService,
    private readonly businessPartnerRelationService: BusinessPartnerRelationService,
    @InjectRepository(BusinessPartner)
    private readonly businessPartnerRepository: Repository<BusinessPartner>,
  ) {}

  async getOutlet(outletUCC?: string): Promise<any> {
    if (!outletUCC) {
      return null;
    }
    try {
      const outlet = await this.outletService.findOne({ ucc: outletUCC, depotId: { $nin: [null, ''] } });
      return outlet;
    } catch (error) {
      return null;
    }
  }

  async checkOutletUCCExists(outletUCC?: string): Promise<boolean> {
    if (!outletUCC) {
      return false;
    }
    try {
      const outlet = await this.businessPartnerService.findOne({
        where: {
          businessPartnerKey: outletUCC,
          businessPartnerType: BusinessPartnerType.OUTLET.toString(),
        },
      });

      return !!outlet;
    } catch (error) {
      return false;
    }
  }

  async getDepotUCCFromOutletUCC(outletUCC: string): Promise<string | null> {
    try {
      const result = await this.businessPartnerRepository
        .createQueryBuilder('depot')
        .select('depot.businessPartnerKey', 'depotUCC')
        .innerJoin('business_partner_relations', 'relation', 'relation.businessPartner2 = depot.id')
        .innerJoin('business_partners', 'outlet', 'relation.businessPartner1 = outlet.id')
        .where('outlet.businessPartnerKey = :outletUCC', { outletUCC })
        .andWhere('outlet.businessPartnerType = :outletType', { outletType: BusinessPartnerType.OUTLET.toString() })
        .andWhere('depot.businessPartnerType = :depotType', { depotType: BusinessPartnerType.DEPOT.toString() })
        .andWhere('relation.businessPartnerRelationType = :relationType', { relationType: BusinessPartnerRelationType.OUTLET_DEPOT })
        .getRawOne();
      return result?.depotUCC || null;
    } catch (error) {
      return null;
    }
  }
}
