import { BadRequestException, Body, Controller, Get, HttpException, HttpStatus, Post, Put, Query, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { ConstantRoles } from 'src/utils/constants/role';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { ApiResponse } from '../../shared/response/api-response';
import { I18n, I18nContext } from 'nestjs-i18n';
import { TargetSettingsService } from '../services/target-settings.service';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { BusinessPartnerContactRole } from '../../master-data/constants/business-partner.enum';
import { In, IsNull } from 'typeorm';
import { isEmptyObjectOrArray } from 'src/utils';
import { CallTargetImportMappingType, CallTargetMapping } from '../constants/call-plan-target.mapping';
import * as xlsx from 'xlsx';
import { WorkBook } from 'xlsx';
import { PlanTimeFrameService } from '../services/plan-time-frame.service';
import { BusinessPartnersService } from 'src/master-data/services/business-partners.service';

@ApiTags('CallCenterSettings')
@Controller('api/call-center/settings')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CallCenterSettingController {
  constructor(
    private readonly targetSettingsService: TargetSettingsService,
    private readonly businessPartnersContactService: BusinessPartnersContactService,
    private readonly businessPartnersService: BusinessPartnersService,
    private readonly planTimeFrameService: PlanTimeFrameService,
  ) {}

  @Get('targets')
  @ApiOperation({ summary: 'Get target settings list' })
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getTargetSettings(
    @Query()
    query: {
      month?: number;
      year?: number;
      agentId?: string;
      offset?: number;
      limit?: number;
      orderBy?: string;
      orderDesc?: string;
    },
    @CurrentUser() user: any,
  ) {
    if (!user?.businessPartnerRelations?.contactIds) return [];
    const contacts = await this.businessPartnersContactService.find({
      where: {
        id: In(user.businessPartnerRelations.contactIds),
        businessPartnerContactPersonRole: BusinessPartnerContactRole.CALL_CENTER,
        isDeleted: false,
      },
    });

    if (isEmptyObjectOrArray(contacts)) return [];

    // Get target settings for all agents under this manager
    const agentIds = contacts.map((contact) => contact.businessPartnerContactKey);
    const result = await this.targetSettingsService.getTargetSettings({
      ...query,
      agentIds: query.agentId ? [query.agentId] : agentIds,
    });
    return new ApiResponse(result);
  }

  @Get('targets/export')
  @ApiOperation({ summary: 'Get target settings list' })
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async exportTargetSettings(
    @Query()
    query: {
      month?: number;
      year?: number;
    },
    @CurrentUser() user: any,
    @I18n() i18n: I18nContext,
  ) {
    if (!user?.businessPartnerRelations?.contactIds) return [];
    const contacts = await this.businessPartnersContactService.find({
      where: {
        id: In(user.businessPartnerRelations.contactIds),
        businessPartnerContactPersonRole: BusinessPartnerContactRole.CALL_CENTER,
        isDeleted: false,
      },
    });

    if (isEmptyObjectOrArray(contacts)) return [];

    // Get target settings for all agents under this manager
    const agentIds = contacts.map((contact) => contact.businessPartnerContactKey);
    const result = await this.targetSettingsService.exportTargetSettings(
      {
        ...query,
        agentIds: agentIds,
      },
      i18n,
    );
    return new ApiResponse(result);
  }

  @Post('targets')
  @ApiOperation({ summary: 'Create or update target settings' })
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async createTargetSettings(
    @Body()
    data: {
      agentId: string;
      outletId: string;
      volumeTarget: number;
      strikeRate: number;
      callCoverage: number;
      activeSellingOutlet: number;
      month: number;
      year: number;
    },
    @I18n() i18n: I18nContext,
  ) {
    // Get current year
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();

    // Validate month
    if (data.month < 1 || data.month > 12) {
      throw new HttpException(await i18n.translate(`dsrTargets.month.invalid`), HttpStatus.BAD_REQUEST);
    }

    // Validate year
    if (data.year < 2000 || data.year > currentYear + 1) {
      throw new BadRequestException(i18n.t('dsrTargets.year.invalid'));
    }

    const [contact, outlet] = await Promise.all([
      this.businessPartnersContactService.findOne({
        where: {
          businessPartnerContactKey: data.agentId,
          businessPartnerContactPersonRole: BusinessPartnerContactRole.CALL_CENTER,
          isDeleted: false,
        },
      }),
      (data.outletId &&
        this.businessPartnersService.findOne({
          where: {
            businessPartnerKey: data.outletId,
            isDeleted: false,
          },
        })) ||
        Promise.resolve(null),
    ]);
    if (isEmptyObjectOrArray(contact)) return null;

    const targetSettings = await this.targetSettingsService.findOne({
      where: {
        agentId: data.agentId,
        outletId: data.outletId ? data.outletId : IsNull(),
        month: data.month,
        year: data.year,
      },
    });

    if (!isEmptyObjectOrArray(targetSettings)) {
      throw new HttpException(await i18n.translate(`dsrTargets.target_already_existed`), HttpStatus.BAD_REQUEST);
    }

    const agentName = contact.businessPartnerContactName1 || contact.businessPartnerContactName2;
    const result = await this.targetSettingsService.createOrUpdateTargetSettings(
      {
        ...data,
        agentName,
        outletName: outlet?.businessPartnerName1 || outlet?.businessPartnerName2,
      },
      i18n,
    );
    return new ApiResponse(result);
  }

  @Put('targets')
  @ApiOperation({ summary: 'Create or update target settings' })
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async updateTargetSettings(
    @Body()
    data: {
      agentId: string;
      outletId: string;
      volumeTarget: number;
      strikeRate: number;
      callCoverage: number;
      activeSellingOutlet: number;
      month: number;
      year: number;
    },
    @I18n() i18n: I18nContext,
  ) {
    // Get current year
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();

    // Validate month
    if (data.month < 1 || data.month > 12) {
      throw new HttpException(await i18n.translate(`dsrTargets.month.invalid`), HttpStatus.BAD_REQUEST);
    }

    // Validate year
    if (data.year < 2000 || data.year > currentYear + 1) {
      throw new HttpException(await i18n.translate(`dsrTargets.year.invalid`), HttpStatus.BAD_REQUEST);
    }

    const [contact, outlet] = await Promise.all([
      this.businessPartnersContactService.findOne({
        where: {
          businessPartnerContactKey: data.agentId,
          businessPartnerContactPersonRole: BusinessPartnerContactRole.CALL_CENTER,
          isDeleted: false,
        },
      }),
      (data.outletId &&
        this.businessPartnersService.findOne({
          where: {
            businessPartnerKey: data.outletId,
            isDeleted: false,
          },
        })) ||
        Promise.resolve(null),
    ]);

    if (isEmptyObjectOrArray(contact)) return null;
    const agentName = contact.businessPartnerContactName1 || contact.businessPartnerContactName2;
    const result = await this.targetSettingsService.createOrUpdateTargetSettings(
      {
        ...data,
        agentName,
        outletName: outlet?.businessPartnerName1 || outlet?.businessPartnerName2,
      },
      i18n,
    );
    return new ApiResponse(result);
  }

  @Get('targets/agent')
  @ApiOperation({ summary: 'Get target settings for a specific agent' })
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.CALL_CENTER)
  async getAgentTargetSettings(
    @Query()
    query: {
      agentId: string;
      month: number;
      year: number;
    },
  ) {
    const result = await this.targetSettingsService.getAgentTargetSettings(query.agentId, null, query.month, query.year);
    return new ApiResponse(result);
  }

  @Get('agents')
  @ApiOperation({ summary: 'Get agents' })
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getAgents(@CurrentUser() user: any) {
    if (!user?.businessPartnerRelations?.contactIds) return [];
    const contacts = await this.businessPartnersContactService.find({
      where: {
        id: In(user.businessPartnerRelations.contactIds),
        businessPartnerContactPersonRole: BusinessPartnerContactRole.CALL_CENTER,
        isDeleted: false,
      },
    });

    // Attach additional relation data for each contact
    const businessPartnerContactsRelationData = await this.businessPartnersContactService.attachBusinessPartnerContactsRelationData(contacts);
    const agents = businessPartnerContactsRelationData.map((contact) => ({
      id: contact.id,
      agentName: contact.businessPartnerContactName1 || contact.businessPartnerContactName2,
      agentId: contact.businessPartnerContactKey,
    }));
    return new ApiResponse(agents);
  }

  @Post('targets/import')
  @ApiOperation({ summary: 'Import target settings from Excel' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async importTargetSettings(@UploadedFile() file: Express.Multer.File, @I18n() i18n: I18nContext) {
    if (!file) {
      throw new BadRequestException(i18n.translate('importExport.targetSettings.no_file'));
    }
    const workbook: WorkBook = xlsx.read(file.buffer, { type: 'buffer' });
    const mappingResult: CallTargetImportMappingType = { targetSettings: [] };

    Object.keys(CallTargetMapping).forEach((sheetName) => {
      let formattedSheetName = sheetName?.trim();
      if (sheetName.toLowerCase() === 'target-settings') {
        formattedSheetName = 'targetSettings';
      }
      if (workbook.SheetNames.includes(sheetName)) {
        const sheet = workbook.Sheets[sheetName];
        const rawData = xlsx.utils.sheet_to_json(sheet);
        if (rawData.length > 0) {
          const trimmedHeaders = Object.keys(rawData[0]).reduce((acc, key) => {
            acc[key.trim()] = key;
            return acc;
          }, {} as Record<string, string>);

          rawData.forEach((row: any) => {
            Object.keys(row).forEach((key) => {
              if (trimmedHeaders[key.trim()] && key.trim() !== key) {
                row[key.trim()] = row[key];
                delete row[key];
              }
            });
          });
        }
        mappingResult[formattedSheetName] = rawData.map((row) => {
          const mappedRow: Record<string, any> = {};
          const trimmedHeaders = Object.keys(CallTargetMapping).reduce((acc, key) => {
            acc[key.trim()] = CallTargetMapping[key?.trim()];
            return acc;
          }, {} as Record<string, any>);

          const mapping = trimmedHeaders[sheetName.trim()];

          Object.entries(mapping).forEach(([key, excelField]) => {
            let value = row[excelField.toString()?.trim()];

            // Format phone numbers if field contains 'phone' or 'tel' in its name
            if (value && ['phone_number', 'mobile_phone'].includes(excelField.toString().toLowerCase()) && !value.toString().startsWith('+')) {
              value = `+${value}`;
            }

            mappedRow[key?.trim()] = value;
          });

          return mappedRow;
        });
      }
    });

    const result = await this.targetSettingsService.importTargetSettings(mappingResult, i18n);
    return new ApiResponse(result);
  }

  @Get('timeframe-day-slots')
  @ApiOperation({ summary: 'Get Time Frame Day Slots Available' })
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getTimeFrameDaySlot(@CurrentUser() user: any) {
    return new ApiResponse(await this.planTimeFrameService.generateTimeSlot());
  }

  @Get('timeframe-settings')
  @ApiOperation({ summary: 'Get Time Frame Settings' })
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getTimeFrameSettings(@CurrentUser() user: any) {
    if (!user?.businessPartnerRelations?.distributorIds?.length) {
      return new ApiResponse({});
    }

    return new ApiResponse(await this.planTimeFrameService.getValidCallPlanTimeFrame(user?.businessPartnerRelations?.distributorIds[0]));
  }

  @Post('timeframe-settings')
  @ApiOperation({ summary: 'Update Time Frame Settings' })
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async updateTimeFrameSettings(@Body() { startingTimeframe }: { startingTimeframe: string }, @CurrentUser() user: any, @I18n() i18n: I18nContext) {
    if (!user?.businessPartnerRelations?.distributorIds?.length) {
      throw new HttpException(await i18n.translate(`distributor.not_found`), HttpStatus.BAD_REQUEST);
    }

    const ccmDistributorIds = user?.businessPartnerRelations?.distributorIds || [];

    const planTimeFrames = await Promise.all(
      ccmDistributorIds.map((ccmDistributorId) => this.planTimeFrameService.updateTimeFrameSetting(ccmDistributorId, startingTimeframe, i18n)),
    );

    return new ApiResponse(planTimeFrames[0]);
  }
}
