import { Body, Controller, Get, Post, Query, Request, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CallCenterService } from '../services/call-center.service';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { ConstantRoles } from 'src/utils/constants/role';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { getConferenceSipXmlTwimlConfig, getTwimlConfig, initializePhoneCall, makePhoneCall } from '../../shared/sms-provider/twilio';
import { ApiResponse } from '../../shared/response/api-response';
import { CallPlanningService } from '../services/call-planning.service';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { User } from 'src/users/schemas/user.schema';

@ApiTags('CallCenter Twillio callback endpoints')
@Controller('api/call-center/twilio')
export class CallCenterTwilioController {
  constructor(private readonly callPlanningService: CallPlanningService) {}

  @Get('refreshToken')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  async initializeTwilioToken(@Query('identity') identity: string) {
    return new ApiResponse(await initializePhoneCall(identity));
  }

  @Post('call')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(ConstantRoles.CALL_CENTER)
  async makePhoneCall(@Body() { targetNumber, callPlanId, callerId }: { targetNumber: string; callPlanId: string; callerId: string }) {
    return new ApiResponse(await makePhoneCall(targetNumber, callPlanId));
  }

  @Get('conference-twiml')
  async getConferenceTwimlConfig(@Query() queries: any) {
    console.log('conference-twiml queries', queries);
    return await getConferenceSipXmlTwimlConfig(queries);
  }

  @Post('twiml')
  async getTwimlConfig(@Body() body: any, @Query() queries: any) {
    const { callPlanId } = queries;
    return await getTwimlConfig(callPlanId, body);
  }

  @Post('record')
  async recordStatusCallBack(@Body() body: any, @Query() queries: any) {
    console.log('queries', queries);
    console.log('body', body);
    const { callPlanId } = queries;

    if (!callPlanId) {
      console.log(`Missing callPlanId for Twilio Call ${body?.CallSid}`);
      return new ApiResponse({});
    }
    if (body?.Direction && body?.Direction === 'outbound-api') {
      this.callPlanningService.updateCallPlanningCallData({
        callPlanId,
        callSid: body?.CallSid,
        callStatus: body?.CallStatus,
      });
    }

    if (body?.RecordingSid) {
      this.callPlanningService.updateCallPlanningCallData({
        callPlanId,
        recordingStatus: body?.RecordingStatus,
        recordingUrl: body?.RecordingUrl,
        callSid: body?.CallSid,
        callDuration: Number(body?.RecordingDuration),
        startTime: body?.RecordingStartTime,
      });
    }

    return new ApiResponse({});
  }
}
