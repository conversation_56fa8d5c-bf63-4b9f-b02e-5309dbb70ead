import { Controller, Get, Query, UseGuards, HttpException, HttpStatus } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { ConstantRoles } from 'src/utils/constants/role';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { ApiResponse } from '../../shared/response/api-response';
import { CurrentUser } from '../../shared/decorator/current-user.decorator';
import { User } from '../../users/schemas/user.schema';
import { I18n, I18nContext } from 'nestjs-i18n';
import { CallCenterPerformanceService } from '../services/call-center-performance.service';
import { printLog } from 'src/utils';
import { SalesOrderMetricsArgs, SalesOrderMetricsOrderParams } from '../dtos/sales-order-metrics.args';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { CallEfficiencyMetricsArgs, CallEfficiencyMetricsOrderParams } from '../dtos/call-efficiency-metrics.args';
import { AgentsMetricsArgs, AgentsMetricsOrderParams } from '../dtos/agents-metrics.args';

@ApiTags('CallCenter Performance')
@Controller('api/call-center/performance')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CallCenterPerformanceController {
  constructor(private readonly callCenterPerformanceService: CallCenterPerformanceService) {}

  @Get('call-efficiency/histories')
  @Roles(ConstantRoles.CALL_CENTER)
  @ApiOperation({ summary: 'Get call center performance histories' })
  async getCallEfficiencyHistories(
    @Query() args: CallEfficiencyMetricsArgs,
    @Query() pagination: PaginationParams,
    @Query() order: CallEfficiencyMetricsOrderParams,
    @CurrentUser() user: User,
    @I18n() i18n: I18nContext,
  ) {
    try {
      const params = {
        ...args,
        ...pagination,
        ...order,
      };
      const result = await this.callCenterPerformanceService.getCallEfficiencyHistories(params, user, i18n);
      return new ApiResponse(result);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('call-efficiency/metrics')
  @Roles(ConstantRoles.CALL_CENTER)
  @ApiOperation({
    summary: 'Get call center performance metrics',
    description: 'Returns call center performance metrics including total calls, connect rate, and duration statistics.',
  })
  async getCallEfficiencyMetrics(@Query() params: CallEfficiencyMetricsArgs, @CurrentUser() user: User, @I18n() i18n: I18nContext) {
    try {
      const result = await this.callCenterPerformanceService.getCallEfficiencyMetrics(params, user, i18n);
      return new ApiResponse(result);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('sales-orders-performance/histories')
  @Roles(ConstantRoles.CALL_CENTER)
  @ApiOperation({ summary: 'Get call center performance histories' })
  async getSalesAndOrderHistories(
    @Query() args: SalesOrderMetricsArgs,
    @Query() pagination: PaginationParams,
    @Query() order: SalesOrderMetricsOrderParams,
    @CurrentUser() user: User,
    @I18n() i18n: I18nContext,
  ) {
    try {
      const params = {
        ...args,
        ...pagination,
        ...order,
      };
      const result = await this.callCenterPerformanceService.getSalesAndOrderHistories(params, user, i18n);
      return new ApiResponse(result);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/sales-orders-performance/metrics')
  @Roles(ConstantRoles.CALL_CENTER)
  @ApiOperation({ summary: 'Get call center performance metrics' })
  async getSalesAndOrderMetrics(@Query() params: SalesOrderMetricsArgs, @CurrentUser() user: User, @I18n() i18n: I18nContext) {
    try {
      const result = await this.callCenterPerformanceService.getSalesAndOrderMetrics(params, user, i18n);
      return new ApiResponse(result);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/management/home/<USER>/metrics')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  @ApiOperation({ summary: 'Management home: Get metrics for agents' })
  async getAllAgentsMetrics(@Query() params: AgentsMetricsArgs, @CurrentUser() user: User, @I18n() i18n: I18nContext) {
    try {
      const result = await this.callCenterPerformanceService.getAllAgentsMetrics(params, user, i18n);
      return new ApiResponse(result);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/management/home/<USER>/histories')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  @ApiOperation({ summary: 'Management home: Get agents performance histories' })
  async getAllAgentsHistories(@Query() params: AgentsMetricsArgs, @Query() order: AgentsMetricsOrderParams, @CurrentUser() user: User, @I18n() i18n: I18nContext) {
    try {
      const result = await this.callCenterPerformanceService.getAllAgentsPerformance({ ...params, ...order }, user, i18n);
      return new ApiResponse(result);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/management/call-efficiency/metrics')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  @ApiOperation({
    summary: 'Get manager call center performance metrics',
    description: 'Returns call center performance metrics including total calls, connect rate, and duration statistics.',
  })
  async getManagerCallEfficiencyMetrics(@Query() params: CallEfficiencyMetricsArgs, @CurrentUser() user: User, @I18n() i18n: I18nContext) {
    try {
      const result = await this.callCenterPerformanceService.getManagerCallEfficiencyMetrics(params, user, i18n);
      return new ApiResponse(result);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/management/sales-orders-performance/metrics')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  @ApiOperation({ summary: 'Management home: Get sales and orders performance metrics' })
  async getManagerSalesAndOrderMetrics(@Query() params: SalesOrderMetricsArgs, @CurrentUser() user: User, @I18n() i18n: I18nContext) {
    try {
      const result = await this.callCenterPerformanceService.getManagerSalesAndOrderMetrics(params, user, i18n);
      return new ApiResponse(result);
    } catch (error) {
      printLog('error', error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
