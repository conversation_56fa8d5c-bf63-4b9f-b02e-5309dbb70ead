import { Controller, Sse, Query, MessageEvent, HttpException, HttpStatus } from '@nestjs/common';
import { Observable, Subject, interval, map, merge } from 'rxjs';
import { Notification } from '../entities/notification.entity';
import { ConstantRoles } from 'src/utils/constants/role';
import { AuthService } from 'src/auth/auth.service';
import { UsersService } from 'src/users/services/users.service';
import { BusinessPartnerRelationService } from '../../master-data/services/business-partners-relation.service';
import { BusinessPartnerRelationType } from '../../master-data/constants/business-partner.enum';
import { EventEmitter2 } from '@nestjs/event-emitter';

export const ccmSubjects = new Map<string, Subject<Notification>>();

@Controller('api/call-center-sse')
export class CallCenterSseController {
  constructor(
    private readonly authService: AuthService,
    private readonly userService: UsersService,
    private readonly _businessPartnersRelationService: BusinessPartnerRelationService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.eventEmitter.on('call_center.call_status_update', this.handleCCMEvent.bind(this));
  }

  @Sse('listen')
  async sseContactCenterManager(@Query() { token }: { token: string }): Promise<Observable<MessageEvent>> {
    if (!this.authService.jwtVerify(token)) {
      throw new HttpException('Unauthorized!', HttpStatus.UNAUTHORIZED);
    }
    const userData = await this.userService.getCurrentUserByToken(token);
    const ccmContactKey = userData?.saleRepId || userData?.contactId;

    if (!userData?.roles?.length || !userData?.roles.includes(ConstantRoles.CALL_CENTER_MANAGEMENT) || !ccmContactKey) {
      throw new HttpException('Unauthorized!', HttpStatus.UNAUTHORIZED);
    }

    const ccmKey = ccmContactKey;
    let ccmSubject = ccmSubjects.get(ccmKey);
    if (!ccmSubject) {
      ccmSubject = new Subject<Notification>();
      ccmSubjects.set(ccmKey, ccmSubject);
    }
    const keepAlive = interval(15000).pipe(map(() => ({ data: { type: 'keep-alive' } } as MessageEvent)));

    //Push realtime
    return merge(keepAlive, ccmSubject.pipe(map((notification) => ({ data: notification }))));
  }

  async handleCCMEvent(notification: any) {
    // Send notification through SSE
    const { status, callCenterId, callCenterKey, distributorId } = notification;
    if (!(callCenterId || callCenterKey) || !distributorId) {
      return;
    }

    const contactDistributorRelations = await this._businessPartnersRelationService.findBusinessPartnerContactRelationsPartnerKeys(
      distributorId,
      BusinessPartnerRelationType.CONTACT_DISTRIBUTOR,
    );

    if (!contactDistributorRelations?.length) {
      return;
    }

    for (let i = 0; i < contactDistributorRelations.length; i++) {
      const ccmSubject = ccmSubjects.get(contactDistributorRelations[i].businessPartnerKey1);
      if (ccmSubject) {
        ccmSubject.next(notification);
      }
    }
  }
} 