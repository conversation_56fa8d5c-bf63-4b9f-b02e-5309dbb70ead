import { Body, Controller, Get, HttpException, HttpStatus, Post, Query, Request, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { I18n, I18nContext } from 'nestjs-i18n';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { MockService } from '../../external/services/mock.service';
import { OmsService } from '../../external/services/oms.service';
import { OrderListFilterDto } from '../../orders/dtos/order-filter.dto';
import { PlaceOrderDto } from '../../orders/dtos/outlet-place-order.dto';
import { OrdersService } from '../../orders/services/orders.service';
import { ApiException } from '../../shared/api-exception.model';
import { CurrentUser } from '../../shared/decorator/current-user.decorator';
import { Roles } from '../../shared/decorator/roles.decorator';
import { RolesGuard } from '../../shared/guards/roles.guard';
import { ApiResponse } from '../../shared/response/api-response';
import { ConstantCommons } from '../../utils/constants';
import { ConstantRoles } from '../../utils/constants/role';
import { CallCenterOrderService } from '../services/call-center-order.service';
import { CallPlanningOrdersService } from '../services/call-planning-orders.service';
import { isEmptyObjectOrArray } from '../../utils';
import { CallPlanningService } from '../services/call-planning.service';
import { CallPlanOrderSource } from '../enums/call-center.enum';

@ApiTags('Call Center Orders')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/call-center/order')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CallCenterOrderController {
  constructor(
    private readonly callCenterOrderService: CallCenterOrderService,
    private readonly orderService: OrdersService,
    private readonly omsService: OmsService,
    private readonly mockService: MockService,
    private readonly _callPlanningService: CallPlanningService,
    private readonly callPlanningOrdersService: CallPlanningOrdersService,
  ) {}

  @Post('oms-place-order')
  @Roles(ConstantRoles.CALL_CENTER)
  @ApiOperation({
    summary: 'Place Order OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async placeOrderOMS(@Body() body: PlaceOrderDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      const outletUCC = body?.outletUCC;
      const existedOutletUCC = await this.callCenterOrderService.checkOutletUCCExists(outletUCC);
      if (!existedOutletUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'outletUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const outlet = await this.callCenterOrderService.getOutlet(outletUCC);
      const oldBody = {
        outletId: outlet.id,
        ...body,
        sourceSystem: CallPlanOrderSource.CALL_CENTER,
      };
      if (currentUser.isTestAccount) {
        const order = await this.mockService.placeOrderOMS(oldBody, `${currentUser.firstname} ${currentUser.lastname}`);
        return new ApiResponse(order);
      }
      const order = await this.orderService.createOrderOms(oldBody, i18n, currentUser);

      this.callPlanningOrdersService
        .saveOrderToCallPlanningOrders(outletUCC, order, currentUser, body?.planId)
        .then(async (res) => {
          if (body?.planId) {
            const plan = await this._callPlanningService.findOne({ where: { id: body?.planId } });
            if (!isEmptyObjectOrArray(plan)) {
              await this._callPlanningService.save({ ...plan, hasOrder: true });
            }
          }
        })
        .catch((error) => {
          console.error('Error saving call planning order:', error);
        });
      return new ApiResponse(order);
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Post('oms-place-temporary-order')
  @Roles(ConstantRoles.CALL_CENTER)
  @ApiOperation({
    summary: 'Place Temporary Order OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async placeTemporaryOrderOMS(@Body() body: PlaceOrderDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      const outletUCC = body?.outletUCC;
      const existedOutletUCC = await this.callCenterOrderService.checkOutletUCCExists(outletUCC);
      if (!existedOutletUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'outletUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const outlet = await this.callCenterOrderService.getOutlet(outletUCC);
      const oldBody = {
        outletId: outlet.id,
        ...body,
        sourceSystem: CallPlanOrderSource.CALL_CENTER,
      };
      if (currentUser.isTestAccount) {
        const order = await this.mockService.placeOrderOMS(oldBody, `${currentUser.firstname} ${currentUser.lastname}`);
        return new ApiResponse(order);
      }
      const order = await this.orderService.createTemporaryOrderOms(oldBody, i18n, currentUser);
      return new ApiResponse(order);
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Get('latest-order-oms')
  @Roles(ConstantRoles.CALL_CENTER)
  @ApiOperation({
    summary: 'Get Latest Order OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getLatestOrderOms(@Query('outletUCC') outletUCC: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      if (currentUser.isTestAccount) {
        const outlet = await this.callCenterOrderService.getOutlet(outletUCC);
        const latestOrder = await this.mockService.getLatestOrderOms({
          outlet: outlet?.id,
        });
        return new ApiResponse({ lastUpdate: latestOrder?.created_at || null, latestOrder: latestOrder || null });
      }
      const existedOutletUCC = await this.callCenterOrderService.checkOutletUCCExists(outletUCC);
      if (!existedOutletUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'outletUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const depotUCC = await this.callCenterOrderService.getDepotUCCFromOutletUCC(outletUCC);
      if (!depotUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'depotUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const orders =
        (await this.omsService.getListOrdersOMS({
          outletExternalID: outletUCC,
          depotExternalID: depotUCC,
          start_date: null,
          end_date: null,
          limit: 1,
          offset: 0,
          search: null,
          status: null,
        })) || [];
      let latestOrder = null;
      if (orders.length) {
        latestOrder = orders[0];
      }

      return new ApiResponse({ lastUpdate: latestOrder?.created_at || null, latestOrder: latestOrder || null });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Get('list-stock-locations-oms')
  @Roles(ConstantRoles.CALL_CENTER)
  @ApiOperation({
    summary: 'Get list stock locations OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getListStockLocationsOms(@Query('outletUCC') outletUCC: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      const existedOutletUCC = await this.callCenterOrderService.checkOutletUCCExists(outletUCC);
      if (!existedOutletUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'outletUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const depotUCC = await this.callCenterOrderService.getDepotUCCFromOutletUCC(outletUCC);
      if (!depotUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'depotUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const stockLocations = await this.omsService.getListStockLocations(depotUCC);
      return new ApiResponse({ stockLocations });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Get('list-products-oms')
  @Roles(ConstantRoles.CALL_CENTER)
  @ApiOperation({
    summary: 'Get list products OMS',
  })
  @ApiQuery({ name: 'locationId', type: String, required: false })
  @ApiBadRequestResponse({ type: ApiException })
  async getListProductsOms(
    @Query('outletUCC') outletUCC: string,
    @Query('locationId') locationId: string,
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser,
    @Request() request,
  ) {
    try {
      if (currentUser.isTestAccount) {
        const mockProducts = this.mockService.getListProductsOms();
        return new ApiResponse({ mustHaveSKUs: mockProducts || [], listProducts: mockProducts || [] });
      }
      const existedOutletUCC = await this.callCenterOrderService.checkOutletUCCExists(outletUCC);
      if (!existedOutletUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'outletUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const depotUCC = await this.callCenterOrderService.getDepotUCCFromOutletUCC(outletUCC);
      if (!depotUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'depotUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const dataCached = locationId
        ? await this.omsService.getCachedProductsByOutletAndLocation({
            depotExternalID: depotUCC,
            outletExternalID: outletUCC,
            locationExternalID: locationId,
          })
        : await this.omsService.getCachedDataByOutlet({
            useDb: true,
            project: {
              products: 1,
            },
            outletExternalId: outletUCC,
          });
      if (!dataCached) {
        throw new HttpException(await i18n.translate(`common.oms.not_connected`), HttpStatus.BAD_REQUEST);
      }
      const outlet = await this.callCenterOrderService.getOutlet(outletUCC);
      const products =
        dataCached?.products && dataCached?.products?.some((p) => p.average_quantity === undefined)
          ? await this.omsService.getOrderAverageQuantity(depotUCC, outlet, dataCached?.products)
          : dataCached?.products;
      const mustHaveSKUs = products?.filter((p) => p.tagging == ConstantCommons.MUST_HAVE_SKU_LABEL);

      const listProducts = products?.filter((p) => p.tagging != ConstantCommons.MUST_HAVE_SKU_LABEL);
      return new ApiResponse({ mustHaveSKUs: mustHaveSKUs || [], listProducts: listProducts || [] });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Get('list-promotions-oms')
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  @ApiOperation({
    summary: 'Get list promotions OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getListPromotionsOms(@Query('outletUCC') outletUCC: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      if (currentUser.isTestAccount) {
        const promotions = this.mockService.getListPromotionsOms();
        return new ApiResponse({ listPromotions: promotions || [] });
      }
      const existedOutletUCC = await this.callCenterOrderService.checkOutletUCCExists(outletUCC);
      if (!existedOutletUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'outletUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const depotUCC = await this.callCenterOrderService.getDepotUCCFromOutletUCC(outletUCC);
      if (!depotUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'depotUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const dataCached = await this.omsService.getCachedDataByOutlet({
        useDb: true,
        project: {
          promotions: 1,
        },
        outletExternalId: outletUCC,
      });
      if (!dataCached) {
        throw new HttpException(await i18n.translate(`common.oms.not_connected`), HttpStatus.BAD_REQUEST);
      }
      const outlet = await this.callCenterOrderService.getOutlet(outletUCC);
      const promotions =
        dataCached?.promotions && dataCached?.promotions?.some((p) => p.average_quantity === undefined)
          ? await this.omsService.getOrderAverageQuantity(depotUCC, outlet, dataCached?.promotions, true)
          : dataCached?.promotions;
      return new ApiResponse({ listPromotions: promotions || [] });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Get('list-orders-oms')
  @Roles(ConstantRoles.CALL_CENTER)
  @ApiOperation({
    summary: 'Get list orders OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getListOrdersOms(@Query() query: OrderListFilterDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      if (currentUser.isTestAccount) {
        const outlet = await this.callCenterOrderService.getOutlet(query?.outletUCC);
        const oldQuery = {
          outletId: outlet.id,
          ...query,
        };
        const orders = await this.mockService.getListOrdersOms(oldQuery);
        return new ApiResponse({ listOrders: orders || [] });
      }
      const outletUCC = query?.outletUCC || '';
      const existedOutletUCC = await this.callCenterOrderService.checkOutletUCCExists(outletUCC);
      if (!existedOutletUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'outletUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const depotUCC = await this.callCenterOrderService.getDepotUCCFromOutletUCC(outletUCC);
      if (!depotUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'depotUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const orders =
        (await this.omsService.getListOrdersOMS({
          outletExternalID: outletUCC,
          depotExternalID: depotUCC,
          start_date: query?.start_date,
          end_date: query?.end_date,
          limit: Number(query?.limit),
          offset: Number(query?.offset),
          search: query?.search || null,
          status: query?.status || null,
        })) || [];

      return new ApiResponse({ listOrders: orders || [] });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }

  @Get('recommendations')
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  @ApiOperation({
    summary: 'Get list recommendations OMS',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getListOrdersRecommendationOms(@Query('outletUCC') outletUCC: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      if (currentUser.isTestAccount) {
        const outlet = await this.callCenterOrderService.getOutlet(outletUCC);
        const products = this.mockService.getListOrdersOms({ outletId: outlet?.id, limit: 10, offset: 0 });
        return new ApiResponse({ mustHaveSKUs: products || [], listProducts: products || [] });
      }
      const existedOutletUCC = await this.callCenterOrderService.checkOutletUCCExists(outletUCC);
      if (!existedOutletUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'outletUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const depotUCC = await this.callCenterOrderService.getDepotUCCFromOutletUCC(outletUCC);
      if (!depotUCC) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'depotUCC' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const outlet = await this.callCenterOrderService.getOutlet(outletUCC);
      const listProducts = await this.omsService.getOrderRecommendation(depotUCC, outlet, []);
      return new ApiResponse({ listProducts: listProducts || [] });
    } catch (e) {
      throw new HttpException(e.message, e.status);
    }
  }
}
