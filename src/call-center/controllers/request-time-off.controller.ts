import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CallCenterService } from '../services/call-center.service';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { ConstantRoles } from 'src/utils/constants/role';
import { Roles } from 'src/shared/decorator/roles.decorator';

@ApiTags('CallCenter Requested Time Off')
@Controller('api/call-center/leave')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class RequestTimeOffController {
  constructor(private readonly service: CallCenterService) {}

  @Get()
  @Roles(ConstantRoles.CALL_CENTER)
  async getCallCenterStatus() {
    return await this.service.getStatus();
  }
}
