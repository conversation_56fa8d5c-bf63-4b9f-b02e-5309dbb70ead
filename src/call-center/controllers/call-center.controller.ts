import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Post,
  Query,
  StreamableFile,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CallCenterService } from '../services/call-center.service';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { ConstantRoles } from 'src/utils/constants/role';
import { Roles } from 'src/shared/decorator/roles.decorator';
import * as xlsx from 'xlsx';
import { WorkBook } from 'xlsx';
import { ApiResponse } from '../../shared/response/api-response';
import { CurrentUser } from '../../shared/decorator/current-user.decorator';
import { User } from '../../users/schemas/user.schema';
import { I18n, I18nContext } from 'nestjs-i18n';
import { FileInterceptor } from '@nestjs/platform-express';
import { CallPlanImportMappingType } from '../constants/call-plan.type';
import { CallPlanMapping } from '../constants/call-plan.mapping';
import { CallPlanningService } from '../services/call-planning.service';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { BusinessPartnerContact } from 'src/master-data/entities/business-partner-contact/business-partner-contact.entity';
import { isEmptyObjectOrArray } from '../../utils';
import { DeleteCheckListItemDto, EndCallPlanDto, StartCallPlanDto, UpdateCheckListDto } from '../dtos/call-plan.dto';
import { ICallPlan } from '../interfaces/call-plan.interface';
import { DateRangeDto } from 'src/master-data/dtos/date-range.dto';
import { BusinessPartnerContactRole } from '../../master-data/constants/business-partner.enum';
import { CallPlanCreatorType, CallRescheduleReasons } from '../enums/call-center.enum';
import { PlanCycleService } from '../services/plan-cycle.service';
import { MoreThanOrEqual } from 'typeorm';
import { DateRangeDtos } from '../dtos/date-range.dto';
import { CallReportsArgs } from '../dtos/call-reports.args';
import { CallPlanningHistoriesService } from '../services/call-planning-histories.service';
import { isCallCenterManagementUser } from 'src/shared/helpers';
import { CallScriptService } from '../services/call-script.service';

@ApiTags('CallCenter')
@Controller('api/call-center')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class CallCenterController {
  constructor(
    private readonly service: CallCenterService,
    private readonly _callPlanningService: CallPlanningService,
    private readonly _businessPartnersContactService: BusinessPartnersContactService,
    private readonly _planCycleService: PlanCycleService,
    private readonly _callPlanningHistoriesService: CallPlanningHistoriesService,
    private readonly _callScriptService: CallScriptService,
  ) {}

  @Get()
  @Roles(ConstantRoles.CALL_CENTER)
  async getCallCenterStatus() {
    return await this.service.getStatus();
  }

  @Post('import-plans')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importPlans(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 1000000000 })],
      }),
    )
    file: Express.Multer.File,
    @Body() { cycleId }: { cycleId: string },
    @CurrentUser() currentUser: any,
    @I18n() i18n: I18nContext,
  ) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(currentUser);
    if (isEmptyObjectOrArray(contacts)) return;

    const workbook: WorkBook = xlsx.read(file.buffer, { type: 'buffer' });
    const mappingResult: CallPlanImportMappingType = { callPlanning: [], callScript: [] };

    Object.keys(CallPlanMapping).forEach((sheetName) => {
      let formattedSheetName = sheetName?.trim();
      if (sheetName.toLowerCase() === 'call-planning') {
        formattedSheetName = 'callPlanning';
      }
      if (sheetName.toLowerCase() === 'call-script') {
        formattedSheetName = 'callScript';
      }
      if (workbook.SheetNames.includes(sheetName)) {
        const sheet = workbook.Sheets[sheetName];
        const rawData = xlsx.utils.sheet_to_json(sheet);
        if (rawData.length > 0) {
          const trimmedHeaders = Object.keys(rawData[0]).reduce((acc, key) => {
            acc[key.trim()] = key;
            return acc;
          }, {} as Record<string, string>);

          rawData.forEach((row: any) => {
            Object.keys(row).forEach((key) => {
              if (trimmedHeaders[key.trim()] && key.trim() !== key) {
                row[key.trim()] = row[key];
                delete row[key];
              }
            });
          });
        }
        mappingResult[formattedSheetName] = rawData.map((row) => {
          const mappedRow: Record<string, any> = {};
          const trimmedHeaders = Object.keys(CallPlanMapping).reduce((acc, key) => {
            acc[key.trim()] = CallPlanMapping[key?.trim()];
            return acc;
          }, {} as Record<string, any>);

          const mapping = trimmedHeaders[sheetName.trim()];

          Object.entries(mapping).forEach(([key, excelField]) => {
            let value = row[excelField.toString()?.trim()];

            // Format phone numbers if field contains 'phone' or 'tel' in its name
            if (
              formattedSheetName === 'callPlanning' &&
              value &&
              ['phone_number', 'mobile_phone'].includes(excelField.toString().toLowerCase()) &&
              !value.toString().startsWith('+')
            ) {
              value = `+${value}`;
            }

            mappedRow[key?.trim()] = value;
          });

          return mappedRow;
        });
      }
    });

    const [contactDistributorCallPlanRelationDataMap, contactDistributorCallScriptRelationDataMap] = await Promise.all([
      this._callPlanningService.validateImportPlans(mappingResult, currentUser?.businessPartnerRelations),
      this._callScriptService.validateImportScript(mappingResult, currentUser?.businessPartnerRelations),
    ]);
    // Start Update
    const [callPlans, callScripts] = await Promise.all([
      this._callPlanningService.importCallPlan(mappingResult, cycleId, contactDistributorCallPlanRelationDataMap, i18n, contacts.contact),
      this._callScriptService.importCallScripts(mappingResult, contactDistributorCallScriptRelationDataMap, i18n),
    ]);
    return new ApiResponse({ callPlans, callScripts });
  }

  @Get('today-plans')
  @Roles(ConstantRoles.CALL_CENTER)
  async getTodayPlans(@CurrentUser() user: any, @I18n() i18n: I18nContext) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(user);
    if (isEmptyObjectOrArray(contacts)) return;

    const result = await this._callPlanningService.getTodayCallPlans({
      contact: contacts?.contact,
      currentUser: contacts?.currentUser,
    });

    let callPlan: ICallPlan;
    if (!isEmptyObjectOrArray(result)) {
      callPlan = await this._callPlanningService.getCallPlan({ callCenter: result.callCenter, currentUser: user, plans: result.plans, i18n });
    }

    // Get time frame settings from the distributor ID
    const distributor = await this._businessPartnersContactService.getDistributorId(contacts.contact);
    const timeFrameSettings = await this._callPlanningService.getTimeFrameSettings(distributor?.distributorId);
    return new ApiResponse({ ...callPlan, rescheduleReasons: CallRescheduleReasons, timeFrameSettings });
  }

  @Get('today-plans/:callCenterId')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getAgentTodayPlans(@Param('callCenterId') callCenterId: string, @CurrentUser() user: any, @I18n() i18n: I18nContext) {
    const contact = await this._businessPartnersContactService.findOne({ where: { id: callCenterId, businessPartnerContactPersonRole: BusinessPartnerContactRole.CALL_CENTER } });
    if (isEmptyObjectOrArray(contact)) return;

    const result = await this._callPlanningService.getTodayCallPlans({
      contact: contact,
      currentUser: user,
    });

    let callPlan: ICallPlan;
    if (!isEmptyObjectOrArray(result)) {
      callPlan = await this._callPlanningService.getCallPlan({ callCenter: result.callCenter, currentUser: user, plans: result.plans, i18n });
    }

    // Get time frame settings from the distributor ID
    const timeFrameSettings = await this._callPlanningService.getTimeFrameSettings(user?.businessPartnerRelations?.distributorIds[0]);

    return new ApiResponse({ ...callPlan, rescheduleReasons: CallRescheduleReasons, timeFrameSettings });
  }

  @Post('start-call-plan/:id')
  @Roles(ConstantRoles.CALL_CENTER)
  async startCallPlan(@Param('id') id: string, @Body() dto: StartCallPlanDto, @CurrentUser() currentUser: User, @I18n() i18n: I18nContext) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(currentUser);
    if (isEmptyObjectOrArray(contacts)) return;
    const result = await this._callPlanningService.startCallPlan(id, dto, contacts.contact, i18n);
    return new ApiResponse(result);
  }

  @Post('end-call-plan/:id')
  @Roles(ConstantRoles.CALL_CENTER)
  async endCallPlan(@Param('id') id: string, @Body() dto: EndCallPlanDto, @CurrentUser() currentUser: User, @I18n() i18n: I18nContext) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(currentUser);
    if (isEmptyObjectOrArray(contacts)) return;
    const result = await this._callPlanningService.endCallPlan(id, dto, contacts.contact, i18n);
    return new ApiResponse(result);
  }

  @Get('plans')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getCallPlans(
    @Query()
    query: {
      cycleId: string;
      contact?: string;
      sortBy?: string;
      order?: 'ASC' | 'DESC';
      offset?: number;
      limit?: number;
    },
    @CurrentUser() user: any,
    @I18n() i18n: I18nContext,
  ) {
    const { cycleId } = query;
    if (!cycleId) {
      const errorMessage = i18n.translate('plan.call_plans.missing_required_fields', {
        args: { fieldName: 'cycleId' },
      });
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    }
    const { depotIds, distributorIds, contactIds } = user?.businessPartnerRelations || {};

    const result = await this._callPlanningService.getCallPlans({
      contactIds,
      depotIds,
      distributorIds,
      cycleId: query.cycleId,
      sortBy: query.sortBy,
      order: query.order,
      offset: query.offset ? Number(query.offset) : 0,
      limit: query.limit ? Number(query.limit) : 10,
    });
    return new ApiResponse(result);
  }

  @Post('create-plan')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.CALL_CENTER)
  async createPlan(
    @Body()
    data: {
      cycleId: string;
      day: number;
      week: number;
      outlet: string;
      contact: string;
    },
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser: any,
  ) {
    if (!data.cycleId || !data.week || !data.outlet || !data.contact) {
      const errorMessage = i18n.translate('plan.call_plans.missing_required_fields', {
        args: { fieldName: 'cycleId or week or outlet or contact' },
      });
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    }
    const contacts = await this._businessPartnersContactService.getCurrentContact(currentUser);
    if (isEmptyObjectOrArray(contacts)) return;
    const result = await this._callPlanningService.createCallPlan(
      {
        ...data,
        creator: isCallCenterManagementUser(currentUser) ? CallPlanCreatorType.MANAGER : CallPlanCreatorType.AGENT,
      },
      i18n,
      contacts.contact,
    );
    return new ApiResponse(result);
  }

  @Post('create-plan-by-date')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.CALL_CENTER)
  async createPlanByDate(
    @Body()
    data: {
      date: string;
      hour?: string;
      outlet: string;
      contact: string;
    },
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser: any,
  ) {
    if (!data.date || !data.outlet || !data.contact) {
      const errorMessage = i18n.translate('plan.call_plans.missing_required_fields', {
        args: { fieldName: 'date or depot or outlet or contact' },
      });
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    }
    const contacts = await this._businessPartnersContactService.getCurrentContact(currentUser);
    if (isEmptyObjectOrArray(contacts)) return;
    const result = await this._callPlanningService.createCallPlanByDate(
      {
        ...data,
        creator: isCallCenterManagementUser(currentUser) ? CallPlanCreatorType.MANAGER : CallPlanCreatorType.AGENT,
      },
      i18n,
      contacts.contact,
    );
    return new ApiResponse(result);
  }

  @Post('update-plan/:id')
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  async updatePlan(
    @Param('id') id: string,
    @Body()
    data: {
      day: number;
      week: number;
      reschedule: boolean;
    },
    @CurrentUser() currentUser: any,
    @I18n() i18n: I18nContext,
  ) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(currentUser);
    if (isEmptyObjectOrArray(contacts)) return;
    const result = await this._callPlanningService.updateCallPlan(
      id,
      {
        ...data,
        requester: isCallCenterManagementUser(currentUser) ? CallPlanCreatorType.MANAGER : CallPlanCreatorType.AGENT,
      },
      i18n,
      contacts.contact,
    );
    return new ApiResponse(result);
  }

  @Post('update-plan-by-date/:id')
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  async updatePlanByDate(
    @Param('id') id: string,
    @Body()
    data: {
      date: string;
      hour?: string;
      reasonKey: string;
      reschedule: boolean;
    },
    @CurrentUser() currentUser: any,
    @I18n() i18n: I18nContext,
  ) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(currentUser);
    if (isEmptyObjectOrArray(contacts)) return;
    const result = await this._callPlanningService.updateCallPlanByDate(
      id,
      {
        ...data,
        requester: isCallCenterManagementUser(currentUser) ? CallPlanCreatorType.MANAGER : CallPlanCreatorType.AGENT,
      },
      i18n,
      contacts.contact,
    );
    return new ApiResponse(result);
  }

  @Post('checklist/:plan_id')
  @ApiOperation({ summary: 'Update checklist item' })
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  async updateCheckList(@Param('plan_id') id: string, @Body() dto: UpdateCheckListDto, @CurrentUser() user: User, @I18n() i18n: I18nContext) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(user);
    if (isEmptyObjectOrArray(contacts)) return;
    return this._callPlanningService.updateCheckList(id, dto, contacts.contact.businessPartnerContactPersonRole === ConstantRoles.CALL_CENTER_MANAGEMENT, i18n);
  }

  @Delete('checklist/:plan_id')
  @ApiOperation({ summary: 'Delete checklist item' })
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  async deleteCheckListItem(@Param('plan_id') id: string, @Body() dto: DeleteCheckListItemDto, @CurrentUser() user: User, @I18n() i18n: I18nContext) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(user);
    if (isEmptyObjectOrArray(contacts)) return;
    return this._callPlanningService.deleteCheckListItem(id, dto, contacts.contact, i18n);
  }

  @Post('calendars')
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  async calendarPlans(
    @Body()
    data: {
      search: string;
      callCenterId: string;
      year: number;
      month?: number;
      week?: number; // 1-52
      day?: number;
    },
    @CurrentUser() user: any,
    @I18n() i18n: I18nContext,
  ) {
    let contacts: { contact: BusinessPartnerContact; currentUser: any };
    if (data.callCenterId) {
      if (!user.businessPartnerRelations?.contactIds?.includes(data.callCenterId)) {
        throw new HttpException(i18n.translate('plan.call_plans.not_allowed_to_view_other_call_center'), HttpStatus.FORBIDDEN);
      }
      contacts = {
        contact: await this._businessPartnersContactService.findOne({ where: { id: data.callCenterId, businessPartnerContactPersonRole: BusinessPartnerContactRole.CALL_CENTER } }),
        currentUser: user,
      };
    } else {
      contacts = await this._businessPartnersContactService.getCurrentContact(user);
    }
    if (isEmptyObjectOrArray(contacts)) return [];

    const calendars = await this._callPlanningService.calendarPlans(
      {
        search: data.search,
        year: data.year,
        month: data.month,
        week: data.week,
        day: data.day,
      },
      contacts,
      i18n,
    );
    // Get time frame settings from the distributor ID
    const distributor = await this._businessPartnersContactService.getDistributorId(contacts.contact);
    const timeFrameSettings = await this._callPlanningService.getTimeFrameSettings(distributor?.distributorId);
    return new ApiResponse({ ...calendars, rescheduleReasons: CallRescheduleReasons, timeFrameSettings });
  }

  @Get(':id/interaction-histories')
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getOutletInteractionHistories(@Query() dateQuery: DateRangeDto, @Param('id') outletId: string, @I18n() i18n: I18nContext, @CurrentUser() user) {
    await this._callPlanningService.getNewestOrders(outletId);
    return new ApiResponse(await this._callPlanningService.getOutletInteractionHistories(outletId, dateQuery));
  }

  @Get('plan-cycles')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getCallPlanCycles(@Query() { year }: { year: number }, @I18n() i18n: I18nContext) {
    const searchConditions: any = { isDeleted: false };
    if (!isNaN(year)) {
      searchConditions.year = MoreThanOrEqual(year);
    }
    return new ApiResponse(await this._planCycleService.getCallPlanCycleData(searchConditions));
  }

  @Get('call-plan-actors')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getCallPlanActors(@I18n() i18n: I18nContext, @CurrentUser() user: any) {
    return new ApiResponse(await this._callPlanningService.getCallPlanActors(user));
  }

  @Get('call-center-outlets')
  @Roles(ConstantRoles.CALL_CENTER)
  async getCallCenterOutlets(@I18n() i18n: I18nContext, @CurrentUser() user: any) {
    return new ApiResponse(await this._callPlanningService.getCallCenterOutletDatas(user));
  }

  @Post('export')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async exportCallPlan(
    @Body()
    { cycleId }: { cycleId: string },
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser: any,
  ) {
    return new ApiResponse(await this._callPlanningService.exportCallPlan(cycleId, currentUser?.businessPartnerRelations));
  }

  @Get('call-plan-outlet-metrics/:outletId')
  @Roles(ConstantRoles.CALL_CENTER)
  async getCallPlanOutletMetrics(@Query() dateRange: DateRangeDtos, @Param('outletId') outletId: string, @CurrentUser() user: any, @I18n() i18n: I18nContext) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(user);
    if (isEmptyObjectOrArray(contacts)) return;
    return new ApiResponse(await this._callPlanningService.getCallPlanOutletMetrics(outletId, dateRange, contacts.contact, i18n));
  }

  @Get('call-plan-outlet-metrics/manager/:callPlanId')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getCallPlanOutletMetricsForManager(@Query() dateRange: DateRangeDtos, @Param('callPlanId') callPlanId: string, @CurrentUser() user: any, @I18n() i18n: I18nContext) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(user);
    if (isEmptyObjectOrArray(contacts)) return;
    const callPlan = await this._callPlanningService.findOne({ where: { id: callPlanId }, relations: ['outlet', 'callCenter'] });
    if (isEmptyObjectOrArray(callPlan)) {
      throw new HttpException(await i18n.translate(`plan.call_plans.plan_not_found`), HttpStatus.BAD_REQUEST);
    }
    return new ApiResponse(await this._callPlanningService.getCallPlanOutletMetrics(callPlan.outlet.id, dateRange, callPlan.callCenter, i18n));
  }

  @Get('call-planning/reports')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getCallCenterReports(@Query() query: CallReportsArgs, @CurrentUser() user: any, @I18n() i18n: I18nContext) {
    const callCenterIds = user?.businessPartnerRelations?.contactIds;
    if (!callCenterIds?.length) {
      return new ApiResponse({
        histories: [],
        total: 0,
      });
    }
    const filteredCallCenterIds = callCenterIds?.filter((c: string) => !!c);
    return new ApiResponse(await this._callPlanningHistoriesService.getCallReports(query, filteredCallCenterIds));
  }

  @Get('call-planning/reports/export')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT)
  async exportCallCenterReports(@Query() query: CallReportsArgs, @CurrentUser() user: any, @I18n() i18n: I18nContext) {
    const callCenterIds = user?.businessPartnerRelations?.contactIds;
    if (!callCenterIds?.length) {
      return new ApiResponse({
        fileName: '',
        filePath: '',
        expiredDate: null,
      });
    }
    const filteredCallCenterIds = callCenterIds?.filter((c: string) => !!c);
    return new ApiResponse(await this._callPlanningHistoriesService.exportCallReports(query, filteredCallCenterIds, i18n));
  }

  @Get('call-planning/recording/:callPlanningId')
  @Roles(ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.CALL_CENTER)
  async getCallPlanRecording(@Param('callPlanningId') callPlanningId: string) {
    const { stream } = await this._callPlanningService.getCallPlanningRecording(callPlanningId);
    return new StreamableFile(stream);
  }

  @Get(':planId/agent-script')
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  async getOutletCallScript(@Param('planId') callPlanId: string, @CurrentUser() user: any) {
    const callPlan = await this._callPlanningService.findOne({
      where: {
        id: callPlanId,
      },
      select: ['id', 'distributorId'],
      relations: ['outlet', 'callCenter'],
    });
    return new ApiResponse(await this._callScriptService.getOutletAgentSript(callPlan?.outlet?.id, callPlan?.callCenter?.id, [callPlan.distributorId]));
  }

  @Post('sync-call-history')
  @Roles(ConstantRoles.SUPER_USER)
  async syncCallHistory() {
    return new ApiResponse(await this._callPlanningService.syncCallPlanningHistoryData());
  }
}
