import { Body, Controller, Delete, Get, MessageEvent, Param, Post, Query, Sse } from '@nestjs/common';
import { NotificationService } from '../services/notification.service';
import { Notification } from '../entities/notification.entity';
import { interval, merge, Observable, Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponse } from 'src/shared/response/api-response';
import { isEmptyObjectOrArray, printLog } from 'src/utils';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { NotificationType } from '../enums/call-center.enum';
import { CallPlanningService } from '../services/call-planning.service';
import { BusinessPartnersService } from 'src/master-data/services/business-partners.service';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { CurrentUser } from '../../shared/decorator/current-user.decorator';
import { I18n, I18nContext } from 'nestjs-i18n';
import { Roles } from '../../shared/decorator/roles.decorator';
import { ConstantRoles } from '../../utils/constants/role';

const notificationSubject = new Subject<Notification>();

@Controller('api/notifications')
export class NotificationController {
  private userId = '';
  constructor(
    private readonly notificationService: NotificationService,
    private readonly eventEmitter: EventEmitter2,
    private readonly _callPlanningService: CallPlanningService,
    private readonly _businessPartnersContactService: BusinessPartnersContactService,
    private readonly _businessPartnerService: BusinessPartnersService,
  ) {
    // Subscribe to notification created events
    this.eventEmitter.on('notification.created', this.handleNotificationCreated.bind(this));
  }

  // @params: Create notification
  @Post()
  async create(@Body() data: Partial<Notification>) {
    const notification = await this.notificationService.create(data);
    return new ApiResponse(notification);
  }

  @Get()
  @Roles(ConstantRoles.CALL_CENTER, ConstantRoles.CALL_CENTER_MANAGEMENT)
  async findByUser(
    @Query()
    query: {
      offset?: number;
      limit?: number;
    },
    @CurrentUser() currentUser: any,
    @I18n() i18n: I18nContext,
  ) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(currentUser);
    if (isEmptyObjectOrArray(contacts)) return;
    let result = [],
      totalUnRead = 0;
    if (contacts.contact.businessPartnerContactPersonRole === ConstantRoles.CALL_CENTER_MANAGEMENT) {
      result = await this.notificationService.getNotificationByManager(
        contacts.contact.id,
        currentUser.businessPartnerRelations.depotIds,
        query.offset || 0,
        query.limit || 20,
        i18n,
      );
      totalUnRead = await this.notificationService.getUnReadCountByManager(contacts.contact.id, currentUser.businessPartnerRelations.depotIds);
    } else {
      result = await this.notificationService.getNotificationByAgent(contacts.contact.id, query.offset || 0, query.limit || 20, i18n);
      totalUnRead = await this.notificationService.getUnReadCountByAgent(contacts.contact.id);
    }
    return new ApiResponse({ totalUnRead, notifications: result });
  }

  // @params: Mark as read
  @Post('read/:id')
  async markAsRead(@Param('id') id: string) {
    return new ApiResponse(await this.notificationService.markAsRead(id));
  }

  @Post('read-all')
  async markAsReadAll(@CurrentUser() currentUser: any, @I18n() i18n: I18nContext) {
    const contacts = await this._businessPartnersContactService.getCurrentContact(currentUser);
    if (isEmptyObjectOrArray(contacts)) return;
    let result = [];
    if (contacts.contact.businessPartnerContactPersonRole === ConstantRoles.CALL_CENTER_MANAGEMENT) {
      result = await this.notificationService.getNotificationByManager(contacts.contact.id, currentUser.businessPartnerRelations.depotIds, 0, 200, i18n, false);
    } else {
      result = await this.notificationService.getNotificationByAgent(contacts.contact.id, 0, 200, i18n, false);
    }
    return new ApiResponse(await this.notificationService.markAsReadAll(result?.map((item) => item.id)));
  }

  // @params: Delete notification
  @Delete(':id')
  async delete(@Param('id') id: string) {
    await this.notificationService.delete(id);
    return new ApiResponse({ success: true });
  }

  // @params: SSE endpoint for realtime notification
  @Sse('listen')
  sseListen(): Observable<MessageEvent> {
    return notificationSubject.asObservable().pipe(
      // Wrap notification in MessageEvent
      map((notification) => ({ data: notification })),
    );
  }

  // @params: SSE endpoint for user notifications with keep-alive
  @Sse('listen/:userId')
  sseUserListen(@Param('userId') userId: string): Observable<MessageEvent> {
    printLog('sseUserListen', userId);
    this.userId = userId;
    const keepAlive = interval(15000).pipe(map(() => ({ data: { type: 'keep-alive' } } as MessageEvent)));

    //Push realtime
    return merge(keepAlive, notificationSubject.pipe(map((notification) => ({ data: notification }))));
  }

  async handleNotificationCreated(notification: Notification) {
    // Send notification through SSE
    let plan = null;
    //Send to Call Center
    switch (notification.type) {
      case NotificationType.ASSIGNED:
        plan = notification.callPlanId ? await this._callPlanningService.findOne({ where: { id: notification.callPlanId } }) : null;
        if (plan.callCenter?.id === this.userId) {
          notificationSubject.next(notification);
        }
        break;

      //Send to Call Center Manager
      case NotificationType.RESCHEDULED:
      case NotificationType.CALL_STARTED:
      case NotificationType.CALL_ENDED:
        plan = notification.callPlanId ? await this._callPlanningService.findOne({ where: { id: notification.callPlanId } }) : null;
        if (plan?.callCenter?.id === this.userId) {
          notificationSubject.next(notification);
        }
        notificationSubject.next(notification);
        break;

      //Send to Call Center Manager
      case NotificationType.REQUEST_JOIN_CALLED:
        notificationSubject.next(notification);
        break;

      //Send to Call Center
      case NotificationType.REQUEST_JOIN_ACCEPTED:
      case NotificationType.REQUEST_JOIN_REJECTED:
        notificationSubject.next(notification);
        break;
    }
  }
}
