import { Body, Controller, Delete, FileTypeValidator, MaxFileSizeValidator, ParseFilePipe, Post, UploadedFile, UploadedFiles, UseGuards, UseInterceptors } from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ApiBadRequestResponse, ApiBearerAuth, ApiBody, ApiConsumes, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { I18n, I18nContext } from 'nestjs-i18n';
import { JwtAuthGuard } from 'src/auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';

import { ApiException } from '../shared/api-exception.model';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { Roles } from '../shared/decorator/roles.decorator';
import { Serialize } from '../shared/interceptor/serialize.interceptor';
import { ApiResponse } from '../shared/response/api-response';
import { User } from '../users/schemas/user.schema';
import { ConstantRoles } from '../utils/constants/role';
import { DeleteFileDto, FileDto } from './dtos';
import { FileType, ImageQuality } from './enums';
import { FilesService } from './services';

@ApiTags('Files')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/files')
@UseGuards(JwtAuthGuard, RolesGuard)
export class FilesController {
  constructor(
    private readonly _filesService: FilesService, // private readonly azureStorageService: AzureStorageService
  ) {}

  @Post('upload/image')
  @ApiBearerAuth()
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.SUPER_USER)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Upload an image',
  })
  @Serialize(FileDto)
  async uploadImageFile(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new FileTypeValidator({ fileType: /(jpg|jpeg|png)$/ }), new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 })],
      }),
    )
    file: Express.Multer.File,
    @CurrentUser() currentUser: User,
  ) {
    const { originalFileName, fileName, filePath, compressImages, folder } = await this._filesService.handleFile(file, FileType.IMAGE_FILE);
    const expiredDate = moment().tz(process.env.TZ).endOf('day').toDate();
    const result = await this._filesService.create({
      createdBy: new Types.ObjectId(currentUser._id),
      originalName: originalFileName,
      folder: folder,
      name: fileName,
      path: filePath,
      expiredDate,
      type: FileType.IMAGE_FILE,
      compressImages,
    });
    return new ApiResponse(result);
  }

  @Post('upload/images')
  @ApiBearerAuth()
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.SUPER_USER)
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Upload multiple images',
    description: 'Maximum files is 5',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
      },
    },
  })
  uploadImages(
    @CurrentUser() user,
    @UploadedFiles()
    files: Express.Multer.File[],
    @I18n() i18n: I18nContext,
  ) {
    return this._filesService.uploadMultipleFiles(user._id, files, i18n, {
      maxItems: 5,
      types: ['jpg', 'jpeg', 'png'],
      maxSize: 10 * 1024 * 1024,
    });
  }

  @Post('v2/upload/image')
  @ApiBearerAuth()
  // @Roles(ConstantRoles.SALE_REP, ConstantRoles.SUPER_USER)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Upload an image',
  })
  @Serialize(FileDto)
  async uploadImageFileV2(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new FileTypeValidator({ fileType: /(jpg|jpeg|png)$/ }), new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 })],
      }),
    )
    file: Express.Multer.File,
    @CurrentUser() currentUser: User,
  ) {
    const { originalFileName, fileName, filePath, compressImages, folder } = await this._filesService.handleFile(file, FileType.IMAGE_FILE);
    const result = await this._filesService.create({
      createdBy: new Types.ObjectId(currentUser._id),
      originalName: originalFileName,
      folder: folder,
      name: fileName,
      path: filePath,
      type: FileType.IMAGE_FILE,
      compressImages,
    });
    return new ApiResponse(result);
  }

  @Post('v2/upload/images')
  @ApiBearerAuth()
  // @Roles(ConstantRoles.SALE_REP, ConstantRoles.SUPER_USER)
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Upload multiple images',
    description: 'Maximum files is 5',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
      },
    },
  })
  uploadImagesV2(
    @CurrentUser() user,
    @UploadedFiles()
    files: Express.Multer.File[],
    @I18n() i18n: I18nContext,
  ) {
    return this._filesService.uploadMultipleFiles(
      user._id,
      files,
      i18n,
      {
        maxItems: 5,
        types: ['jpg', 'jpeg', 'png'],
        maxSize: 10 * 1024 * 1024,
      },
      true,
    );
  }
}
