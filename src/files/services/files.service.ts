import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as fs from 'fs';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { uid } from 'rand-token';
import * as sharp from 'sharp';
import * as xlsx from 'xlsx';
import { WorkBook, WorkSheet } from 'xlsx';

import { I18nContext } from 'nestjs-i18n';
import { ValidateFilesOptions } from 'src/shared/type/type';
import { BaseService } from '../../shared/services/base-service';
import { User } from '../../users/schemas/user.schema';
import { FileType, Folder, ImageQuality } from '../enums';
import { Files, FilesDocument } from '../schemas';
import { BlobServiceClient } from '@azure/storage-blob';
import { printLog } from '../../utils';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';

@Injectable()
export class FilesService extends BaseService<Files> {
  blobServiceClient = BlobServiceClient.fromConnectionString(process.env.AZURE_S3_CONNECTION);

  constructor(@InjectModel(Files.name) private readonly _model: Model<FilesDocument>, private readonly _httpService: HttpService) {
    super();
    this.model = _model;
    if (!process.env.NODE_APP_INSTANCE || (process.env.NODE_APP_INSTANCE && parseInt(process.env.NODE_APP_INSTANCE) === 0)) {
      this.deleteExpiredFiles().then().catch();
    }
  }

  async handleFile(file: Express.Multer.File, fileType: FileType) {
    let folder: any = Folder.FILES;
    switch (fileType) {
      case FileType.IMAGE_FILE:
        folder = Folder.IMAGES;
    }
    const currentTimestamp = new Date();
    const day = currentTimestamp.getUTCDate();
    const month = currentTimestamp.getUTCMonth() + 1;
    const year = currentTimestamp.getUTCFullYear();
    folder = `image`;
    const path = `${year}/${month}/${day}`;
    const fileExt = file.originalname.split('.').pop();
    const fileName = await this._generateFileName(fileExt);
    const url = await this.uploadBlobFromBuffer(file.buffer, `${path}/${fileName}`, folder);
    let compressImages = [];
    if (fileType === FileType.IMAGE_FILE) {
      compressImages = await this._compressImagesAndUploadS3(file.buffer, fileExt, folder, path);
    }
    return {
      originalFileName: file.originalname,
      folder: folder,
      fileName: `${path}/${fileName}`,
      filePath: url,
      compressImages,
    };
  }

  async deleteExpiredFiles(): Promise<void> {
    const currentISODate = new Date().toISOString();
    const expiredFiles = await this._model.find({ expiredDate: { $lte: currentISODate } }).exec();
    for (const file of expiredFiles) {
      await this.deleteBlobIfItExists(file.folder, file.name);
      await this.deleteMultipleBlobs(file?.compressImages);
    }
    await this._model.deleteMany({ expiredDate: { $lte: currentISODate } });
  }

  async removeExpiredDate(imageIds: string[]) {
    return this._model
      .updateMany(
        { _id: { $in: imageIds.map((imgId) => new Types.ObjectId(imgId)) } },
        {
          expiredDate: null,
          updatedAt: new Date(),
        },
      )
      .exec();
  }

  async removeImagesExpiredDate(imagePaths: string[]) {
    return this._model
      .updateMany(
        { path: { $in: imagePaths } },
        {
          expiredDate: null,
          updatedAt: new Date(),
        },
      )
      .exec()
      .catch((error) => console.log(`Cannot remove expired date for images ${imagePaths}`, error?.message));
  }

  async deleteFiles(images: Files[]) {
    if (images.length) {
      for (const di of images) {
        await this.deleteBlobIfItExists(di.folder, di.name);
        await this.deleteMultipleBlobs(di?.compressImages);
      }
      return this._model.deleteMany({ _id: { $in: images.map((i) => i._id) } }).exec();
    }
  }

  /**
   * Find files by IDs and return selected fields
   * @param fileIds - Array of file IDs to search for
   * @param selectFields - Fields to select (default: '_id path')
   * @returns Array of files with selected fields
   */
  async findFilesByIds(fileIds: string[], selectFields = '_id path') {
    if (!fileIds.length) {
      return [];
    }

    return this._model
      .find({
        _id: { $in: fileIds.map((id) => new Types.ObjectId(id)) },
      })
      .select(selectFields)
      .lean()
      .exec();
  }

  async exportXLSXFile(fileName: string, xlsxData: Record<string, any>[], sheetName: string, currentUser: User, colInfo?: xlsx.ColInfo) {
    const folderPath = await this.createFolder(Folder.TMP);
    const ts = moment().tz(process.env.TZ).format('yyyyMMDDHHmm');
    const fileNameWithTS = `${fileName}_${ts}.xlsx`;
    const expiredDate = moment().tz(process.env.TZ).endOf('day').toDate();
    const existedFile = await this.findOne({ name: fileNameWithTS, type: FileType.EXCEL_FILE });
    if (existedFile) {
      await this.deleteBlobIfItExists(existedFile.folder, existedFile.name);
      await this.update(existedFile._id, {
        expiredDate,
      });
    }
    const wb: WorkBook = xlsx.utils.book_new();
    const ws: WorkSheet = xlsx.utils.json_to_sheet(xlsxData, { cellStyles: true });
    if (colInfo && xlsxData.length) {
      ws['!cols'] = Object.keys(xlsxData[0]).map(() => colInfo);
    }

    const range = xlsx.utils.decode_range(ws['!ref']);

    // Iterate over each row in column A (column index 0) and set the wrapText style
    for (let R = range.s.r; R <= range.e.r; ++R) {
      if (R === 1) continue;
      const cellAddress = xlsx.utils.encode_cell({ c: 8, r: R });
      if (!ws[cellAddress]) continue; // Skip empty cells
      if (!ws[cellAddress].s) ws[cellAddress].s = {};
      if (!ws[cellAddress].s.alignment) ws[cellAddress].s.alignment = {};
      ws[cellAddress].s.alignment.wrapText = true;
    }

    xlsx.utils.book_append_sheet(wb, ws, sheetName);
    const buffer = xlsx.write(wb, { bookType: 'xlsx', type: 'buffer' });
    const folder: any = Folder.FILES;
    const currentTimestamp = new Date();
    const day = currentTimestamp.getUTCDate();
    const month = currentTimestamp.getUTCMonth() + 1;
    const year = currentTimestamp.getUTCFullYear();
    const path = `${year}/${month}/${day}`;
    const url = await this.uploadBlobFromBuffer(buffer, `${path}/${fileNameWithTS}`, folder);
    if (!existedFile && currentUser) {
      await this._model.create({
        createdBy: new Types.ObjectId(currentUser._id),
        originalName: fileName,
        folder: folder,
        name: `${path}/${fileNameWithTS}`,
        path: url,
        expiredDate,
        type: FileType.EXCEL_FILE,
      });
    }
    return { fileName: fileNameWithTS, filePath: url, expiredDate };
  }

  async exportXLSXFileWithMultipleSheet({
    fileName,
    sheets,
    currentUser,
    colInfo,
  }: {
    fileName: string;
    sheets: Array<{ name: string; data: Record<string, any>[] }>;
    currentUser?: User;
    colInfo?: xlsx.ColInfo;
  }) {
    const folderPath = await this.createFolder(Folder.TMP);
    const ts = moment().tz(process.env.TZ).format('yyyyMMDDHHmm');
    const fileNameWithTS = `${fileName}_${ts}.xlsx`;
    const expiredDate = moment().tz(process.env.TZ).endOf('day').toDate();
    const existedFile = await this.findOne({ name: fileNameWithTS, type: FileType.EXCEL_FILE });
    if (existedFile) {
      await this.deleteBlobIfItExists(existedFile.folder, existedFile.name);
      await this.update(existedFile._id, {
        expiredDate,
      });
    }
    const wb: WorkBook = xlsx.utils.book_new();
    sheets.forEach((sheet) => {
      const ws: WorkSheet = xlsx.utils.json_to_sheet(sheet.data);
      if (colInfo && sheet.data.length) {
        ws['!cols'] = Object.keys(sheet.data[0]).map(() => colInfo);
      }
      xlsx.utils.book_append_sheet(wb, ws, sheet.name);
    });
    const buffer = xlsx.write(wb, { bookType: 'xlsx', type: 'buffer' });
    const folder: any = Folder.FILES;
    const currentTimestamp = new Date();
    const day = currentTimestamp.getUTCDate();
    const month = currentTimestamp.getUTCMonth() + 1;
    const year = currentTimestamp.getUTCFullYear();
    const path = `${year}/${month}/${day}`;
    const url = await this.uploadBlobFromBuffer(buffer, `${path}/${fileNameWithTS}`, folder);
    if (!existedFile && currentUser) {
      await this._model.create({
        createdBy: new Types.ObjectId(currentUser._id),
        originalName: fileName,
        folder: folder,
        name: `${path}/${fileNameWithTS}`,
        path: url,
        expiredDate,
        type: FileType.EXCEL_FILE,
      });
    }
    return { fileName: fileNameWithTS, filePath: url, expiredDate };
  }

  async uploadMultipleFiles(userId: string, files: Express.Multer.File[], i18n: I18nContext, options?: ValidateFilesOptions, noExpired?: boolean) {
    this.validateFiles(files, i18n, options);
    const executedFiles = await Promise.all(files.map((file) => this.handleFile(file, FileType.IMAGE_FILE)));
    const expiredDate = moment().tz(process.env.TZ).endOf('day').toDate();
    return this._model.insertMany(
      executedFiles.map((file) => ({
        createdBy: new Types.ObjectId(userId),
        originalName: file.originalFileName,
        folder: file.folder,
        name: file.fileName,
        path: file.filePath,
        expiredDate: noExpired ? null : expiredDate,
        type: FileType.IMAGE_FILE,
        compressImages: file.compressImages,
      })),
    );
  }

  private validateFiles(files: Express.Multer.File[], i18n: I18nContext, options?: ValidateFilesOptions) {
    const { maxItems, types, maxSize } = options;

    // check max items
    if (maxItems && files.length > maxItems) {
      throw new BadRequestException(i18n.t('file.over_max_length', { args: { maxItems } }));
    }

    // check file type
    const fileTypeReg = new RegExp(`(${types.join('|')})$`);
    if (types?.length && !files.every((file) => fileTypeReg.test(file.mimetype))) {
      throw new BadRequestException(i18n.t('file.invalid_type', { args: { types: types.join(', ') } }));
    }

    // check max size
    if (maxSize && files.some((file) => file.size > maxSize)) {
      throw new BadRequestException(i18n.t('file.over_max_file_size', { args: { maxSize, unit: 'MB' } }));
    }
  }

  private async _compressImagesAndUploadS3(fileBuffer: Buffer, fileExt: string, folder: string, path: string) {
    const compressImages = [];
    const fileName = await this._generateFileName(fileExt);
    const buffer = await sharp(fileBuffer)
      .png({
        quality: ImageQuality.LOW,
        force: false,
      })
      .toBuffer();
    const url = await this.uploadBlobFromBuffer(buffer, `${path}/${fileName}`, folder);
    compressImages.push({
      folder,
      quality: ImageQuality.LOW,
      name: `${path}/${fileName}`,
      path: url,
    });
    return compressImages;
  }

  private _generateFileName(fileExt: string) {
    const currentTimestamp = +new Date();
    return `${currentTimestamp}_${uid(15)}.${fileExt}`;
  }

  async createFolder(folder: string) {
    const folderPath = `./${Folder.PUBLIC}/${folder}`;
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true });
    }
    return folderPath;
  }

  async getListExistsContainer() {
    try {
      // get list existing containers
      const listExistsContainer = [];
      const containers = this.blobServiceClient.listContainers();
      for await (const container of containers) {
        listExistsContainer.push(container.name);
      }
      return listExistsContainer;
    } catch (error) {
      throw error;
    }
  }

  async createContainer(containerName: string) {
    try {
      const containerClient = this.blobServiceClient.getContainerClient(containerName);
      const createContainerResponse = await containerClient.create({
        access: 'container',
      });
    } catch (error) {
      throw error;
    }
  }

  async uploadBlobFromBuffer(data: any, fileName: string, containerName: string) {
    try {
      const listContainers = await this.getListExistsContainer();
      if (!listContainers.includes(containerName)) {
        await this.createContainer(containerName);
      }
      const containerClient = this.blobServiceClient.getContainerClient(containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(fileName);
      const uploadBlobResponse = await blockBlobClient.uploadData(data);
      return blockBlobClient?.url;
    } catch (error) {
      throw error;
    }
  }

  async uploadBlobFromLocalPath(data: any, fileName: string, containerName: string) {
    try {
      const listContainers = await this.getListExistsContainer();
      if (!listContainers.includes(containerName)) {
        await this.createContainer(containerName);
      }
      const containerClient = this.blobServiceClient.getContainerClient(containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(fileName);
      const uploadBlobResponse = await blockBlobClient.uploadFile(data);
      return blockBlobClient?.url;
    } catch (error) {
      throw error;
    }
  }

  async deleteBlobIfItExists(containerName, blobName) {
    try {
      printLog('deleteBlobIfItExists:', { containerName, blobName });
      const options = {
        deleteSnapshots: 'include', // or 'only'
      } as any;
      const containerClient = this.blobServiceClient.getContainerClient(containerName);
      const blockBlobClient = await containerClient.getBlockBlobClient(blobName);
      await blockBlobClient.deleteIfExists(options);
    } catch (error) {
      printLog('🚀 ~deleteBlobIfItExists error:', error.message);
    }
  }

  async deleteMultipleBlobs(list) {
    printLog('deleteMultipleBlobs:', list);
    await Promise.all(
      list?.map(async (ci) => {
        await this.deleteBlobIfItExists(ci.folder, ci.name);
      }),
    );
  }

  async readXlsxFromLink(link) {
    try {
      const axiosResponse = await lastValueFrom(
        this._httpService.get(link, {
          responseType: 'arraybuffer',
        }),
      );
      const workbook = xlsx.read(axiosResponse.data);

      return workbook.SheetNames.map((sheetName) => {
        return { sheetName, data: xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]) };
      });
    } catch (error) {
      return null;
    }
  }
}
