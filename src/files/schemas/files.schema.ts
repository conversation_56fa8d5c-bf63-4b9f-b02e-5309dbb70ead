import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types, Document } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { FileType } from '../enums';
import { User } from '../../users/schemas/user.schema';
import { FileFeature } from '../enums/feature.enum';

export class CompressedImage {
  folder: string;
  name: string;
  path: string;
  quality: number;
}

export type FilesDocument = Files & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class Files extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  createdBy: User;

  @Prop()
  originalName: string;

  @Prop()
  folder: string;

  @Prop()
  name: string;

  @Prop()
  path: string;

  @Prop({
    enum: FileFeature,
    required: false,
  })
  feature: string;

  @Prop({ enum: FileType, default: FileType.IMAGE_FILE, index: true })
  type: string;

  @Prop()
  expiredDate: Date;

  @Prop([CompressedImage])
  compressImages: CompressedImage[];
}

export const FilesSchema = SchemaFactory.createForClass(Files);
