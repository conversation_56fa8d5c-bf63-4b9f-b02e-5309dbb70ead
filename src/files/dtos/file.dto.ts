import { Expose, Transform } from 'class-transformer';
import { CompressedImage } from '../schemas';

export class FileDto {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  originalName: string;

  @Expose()
  folder: string;

  @Expose()
  name: string;

  @Expose()
  path: string;

  @Expose()
  type: string;

  @Expose()
  compressImages: CompressedImage[];
}


