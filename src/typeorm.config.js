"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppDataSource = void 0;
const fs = require('fs');
const typeorm_1 = require("typeorm");
const config_1 = require("@nestjs/config");
const dotenv_1 = require("dotenv");
(0, dotenv_1.config)();
const configService = new config_1.ConfigService();
const isDevelop = !['production', 'development', 'staging'].includes(configService.get('NODE_ENV'));
const typeOrmConfig = {
    type: 'postgres',
    host: configService.get('POSTGRES_HOST'),
    port: Number(configService.get('POSTGRES_PORT')),
    username: configService.get('POSTGRES_USER'),
    password: configService.get('POSTGRES_PASSWORD'),
    database: configService.get('POSTGRES_DATABASE'),
    entities: configService.get('MIGRATION_MODE') === 'true' ? ['**/*.entity.ts'] : ['**/*.entity.js'],
    ssl: isDevelop ? false : { rejectUnauthorized: true, ca: fs.readFileSync(configService.get('POSTGRES_CERT_PATH')).toString() },
    synchronize: isDevelop,
    migrations: ['migrations/db/*.ts'],
    connectTimeoutMS: 90000,
    maxQueryExecutionTime: 90000,
    cache: configService.get('CACHE_MODE') === 'true'
        ? {
            type: 'redis',
            options: {
                host: configService.get('REDIS_HOST'),
                port: configService.get('REDIS_PORT'),
                auth_pass: configService.get('REDIS_PASSWORD'),
                password: configService.get('REDIS_PASSWORD'),
                tls: process.env.REDIS_TLS === 'true'
                    ? {
                        host: process.env.REDIS_HOST,
                    }
                    : false,
            },
            ignoreErrors: false,
        }
        : false,
};
const AppDataSource = new typeorm_1.DataSource(typeOrmConfig);
exports.AppDataSource = AppDataSource;
//# sourceMappingURL=typeorm.config.js.map
