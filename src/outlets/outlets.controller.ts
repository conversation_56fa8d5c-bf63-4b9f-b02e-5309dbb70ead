import { BadRequestException, Body, Controller, forwardRef, Get, HttpCode, HttpException, HttpStatus, Inject, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { I18n, I18nContext } from 'nestjs-i18n';
import { SaleRepFfcStoreService } from '../sale-rep/services';
import { AddOrUpdateCustomerMasterDto } from './../auth/dto/update-customer-master.dto';
import { isPhoneNumberValidation, standardPhoneNumber, toListResponse, validateFields } from './../utils/index';
import { SaleRepOutletRelationTrackingService } from './services/sale-rep-outlet-relation-tracking.service';

import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { DistributorUserRelationService } from '../distributor/services';
import { SettingsService } from '../settings/settings.service';
import { ApiException } from '../shared/api-exception.model';
import { Roles } from '../shared/decorator/roles.decorator';
import { ApiResponse } from '../shared/response/api-response';
import { UsersService } from '../users/services/users.service';
import { isEmptyObjectOrArray } from '../utils';
import { ConstantRoles } from '../utils/constants/role';
import { GetOutletDto } from './dtos/get-outlet.dto';
import { OutletStatus } from './enums/outlet-status.enum';
import { SaleRepOutletRelationStatusTracking } from './enums/salerep-outlet-relation-status-tracking.enum';
import { OutletsService } from './services/outlets.service';
import { SaleRepOutletRelationService } from './services/sale-rep-outlet-relation.service';
import { parsePhoneNumberWithError } from 'libphonenumber-js';
import { UpdateOutletDto } from './dtos/update-outlet.dto';

@ApiTags('Outlets')
@ApiHeader({ name: 'locale', description: 'en' })
@Controller('api/outlets')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class OutletsController {
  constructor(
    @Inject(forwardRef(() => OutletsService))
    private readonly _outletsService: OutletsService,
    private readonly _userService: UsersService,
    @Inject(forwardRef(() => SaleRepOutletRelationService))
    private readonly _saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly _settingService: SettingsService,
    @Inject(forwardRef(() => SaleRepFfcStoreService))
    private readonly _saleRepFfcStoreService: SaleRepFfcStoreService,
    private readonly _saleRepOutletRelationTrackingService: SaleRepOutletRelationTrackingService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
  ) {}

  @Post('')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  async createOutlet(@Body() customerDto: AddOrUpdateCustomerMasterDto, @I18n() i18n: I18nContext) {
    const { outletAddress, outletArea, outletClass, outletName, contactName, ucc, saleRepId, status, depotId } = customerDto;
    let { contactNumber, contactPhoneCode } = customerDto;
    await validateFields(
      {
        ucc,
        outletAddress,
        outletArea,
        outletClass,
        outletName,
        saleRepId,
        // contactName,
        // contactNumber,
        // contactPhoneCode,
      },
      `common.not_found`,
      i18n,
    );

    if (contactNumber && contactPhoneCode && !isPhoneNumberValidation(contactNumber, contactPhoneCode)) {
      throw new HttpException(await i18n.translate(`user.phone_invalid_field`), HttpStatus.BAD_REQUEST);
    }

    const findSaleRep = await this._userService.findOne({ saleRepId: saleRepId?.trim() });
    await validateFields({ SaleRep: findSaleRep }, `common.not_found`, i18n);

    const findExistUcc = await this._outletsService.findOne({ ucc: ucc?.toString()?.trim() });

    if (!isEmptyObjectOrArray(findExistUcc)) {
      throw new HttpException(await i18n.translate(`message.existed_ucc`), HttpStatus.BAD_REQUEST);
    }

    if (contactNumber && contactPhoneCode) {
      const mobilePhoneParse = parsePhoneNumberWithError(standardPhoneNumber(contactNumber, contactPhoneCode));
      contactNumber = mobilePhoneParse.number;
      contactPhoneCode = `+${mobilePhoneParse.countryCallingCode}`;
    }

    const createOutlet = await this._outletsService.create({
      ucc: ucc,
      name: outletName,
      area: outletArea,
      outletClass: outletClass,
      address: outletAddress,
      contactName: contactName,
      contactNumber,
      contactPhoneCode,
      status: status || OutletStatus.ACTIVE,
      depotId,
      saleRep: [findSaleRep._id],
    });
    if (isEmptyObjectOrArray(createOutlet)) {
      throw new HttpException(await i18n.translate(`saleRep.not_success`), HttpStatus.BAD_REQUEST);
    }

    await this._saleRepOutletRelationService.createRelation(findSaleRep, createOutlet);
    //Add a tracking data - SHUTDOWN
    await this._saleRepOutletRelationTrackingService.createRelationTracking(findSaleRep, createOutlet, SaleRepOutletRelationStatusTracking.CONNECTING);
    return new ApiResponse(createOutlet);
  }

  @Post('list')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.DISTRIBUTOR_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get Active Outlet of Sales Rep',
  })
  async filterActiveOutletOfSalesRep(@Body() getOutletDto: GetOutletDto, @CurrentUser() currentUser: any) {
    const { skip, limit, query, saleRepId, distributorId } = getOutletDto;
    let listSalesReps = [];
    let listSalesRepIds = [];
    if (saleRepId && saleRepId?.trim()) {
      const existedSaleRep = await this._userService.findOne({ saleRepId });
      if (!existedSaleRep) {
        throw new BadRequestException('saleRep.not_found_sales_rep');
      }
      listSalesRepIds = [saleRepId];
      listSalesReps = [existedSaleRep._id];
    } else {
      if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
        const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
        const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
        if (!distributor) {
          throw new BadRequestException('dsrTargets.not_found_distributor');
        }
        const allSaleReps = await this._distributorUserRelationService.getAllSalesRepOfGivenDistributor(distributor.distributor.distributorId);
        listSalesRepIds = allSaleReps.map((item) => item.saleRepId);
        listSalesReps = allSaleReps.map((item) => item._id);
      }
    }
    const [{ totalRecords, data }] = await this._saleRepOutletRelationService.filterActiveOutlet({ listSalesRepIds, listSalesReps }, query, skip, limit);
    return new ApiResponse(toListResponse([data, totalRecords?.[0]?.total ?? 0]));
  }

  @Get('details/:outletId')
  async getOutletDetails(@Param('outletId') outletId: string, @I18n() i18n: I18nContext, @CurrentUser() salesRep) {
    const outlet = await this._outletsService.getOutletDetailsForSalesRep({
      outletId,
      salesRepId: String(salesRep._id),
      i18n,
    });

    return new ApiResponse({ ...outlet, isOmsConnected: salesRep.isTestAccount ? true : outlet.isOmsConnected });
  }

  @Put('details/:outletId')
  async updateOutletDetails(@Param('outletId') outletId: string, @Body() dto: UpdateOutletDto, @I18n() i18n: I18nContext, @CurrentUser() salesRep) {
    const outlet = await this._outletsService.updateOutletDetails({
      outletId,
      dto,
      salesRepId: String(salesRep._id),
      i18n,
    });

    return new ApiResponse(outlet);
  }

  @Get('performance/volume/:outletId')
  async getPerformanceOutletSaleVolumeDetails(
    @Param('outletId') outletId: string,
    @Param('channelMonthlyAverageVolume') channelMonthlyAverageVolume: boolean,
    @Param('monthlyVolume') monthlyVolume: boolean,
    @Param('yearToDate') yearToDate: boolean,
    @Param('volumePerformance') volumePerformance: boolean,
    @Param('averageVolume') averageVolume: boolean,
    @I18n() i18n: I18nContext,
    @CurrentUser() salesRep,
  ) {
    const outlet = await this._outletsService.findById(outletId);
    if (!outlet) {
      throw new BadRequestException('plan.not_found_outlet');
    }
    const start = undefined; // moment().tz(process.env.TZ).subtract(6, 'months').startOf('month').toDate();
    const end = undefined; // moment().tz(process.env.TZ).endOf('month').toDate();
    const outletPerformances = await this._outletsService.getPerformanceOutletSaleVolumeDetails({
      depotExternalID: outlet.depotId,
      outlet,
      start,
      end,
      channelMonthlyAverageVolume,
      monthlyVolume,
      yearToDate,
      volumePerformance,
      averageVolume,
      i18n,
    });

    return new ApiResponse({ ...outletPerformances });
  }
}
