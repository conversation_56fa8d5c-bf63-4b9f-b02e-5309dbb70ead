import { ApiProperty } from '@nestjs/swagger';
import { OutletStatus } from '../enums/outlet-status.enum';

export class CreateOutletDto {
  @ApiProperty()
  readonly ucc: string;

  @ApiProperty()
  readonly name: string;

  @ApiProperty()
  readonly area: string;

  @ApiProperty()
  readonly outletClass: string;

  @ApiProperty()
  readonly type: string;

  @ApiProperty()
  readonly address: string;

  @ApiProperty()
  contactName: string;

  @ApiProperty()
  contactNumber: string;

  @ApiProperty()
  saleRepId: string;

  @ApiProperty({ default: OutletStatus.ACTIVE })
  status: string;
}
