import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { OutletStatus } from '../enums/outlet-status.enum';

export class CreateOutletDto {
  @ApiModelProperty()
  readonly ucc: string;

  @ApiModelProperty()
  readonly name: string;

  @ApiModelProperty()
  readonly area: string;

  @ApiModelProperty()
  readonly outletClass: string;

  @ApiModelProperty()
  readonly type: string;

  @ApiModelProperty()
  readonly address: string;

  @ApiModelProperty()
  contactName: string;

  @ApiModelProperty()
  contactNumber: string;

  @ApiModelProperty()
  saleRepId: string;

  @ApiModelProperty({ default: OutletStatus.ACTIVE })
  status: string;
}
