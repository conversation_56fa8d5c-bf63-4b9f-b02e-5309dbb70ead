import { IsOptional, IsString } from 'class-validator';

import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class GetOutletDto extends PaginationDto {
  @ApiPropertyOptional({ description: 'Search by name or ucc' })
  @IsString()
  @IsOptional()
  query: string;

  @ApiPropertyOptional({ description: 'Sales Rep Id' })
  @IsString()
  @IsOptional()
  saleRepId: string;

  @ApiPropertyOptional({ description: 'Distributor ID' })
  @IsString()
  distributorId: string;
}
