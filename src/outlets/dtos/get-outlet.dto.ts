import { ApiModelProperty, ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

import { PaginationDto } from '../../shared/dtos/pagination.dto';
export class GetOutletDto extends PaginationDto {
  @ApiModelPropertyOptional({ description: 'Search by name or ucc' })
  @IsString()
  @IsOptional()
  query: string;

  @ApiModelPropertyOptional({ description: 'Sales Rep Id' })
  @IsString()
  @IsOptional()
  saleRepId: string;

  @ApiModelPropertyOptional({ description: 'Distributor ID' })
  @IsString()
  distributorId: string;
}
