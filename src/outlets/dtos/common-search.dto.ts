import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';

import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { IsArray, IsOptional } from 'class-validator';

export class CommonSearchDto extends PaginationDto {
  @ApiModelPropertyOptional()
  query: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsArray()
  packageType: [string];
}
