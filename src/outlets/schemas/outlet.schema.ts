import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MSchema, Types } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { OutletClassType } from '../enums/outlet-class.enum';
import { OutletStatus } from '../enums/outlet-status.enum';
import { OutletType } from '../enums/outlet-type.enum';
import * as moment from 'moment-timezone';
import { User } from '../../users/schemas/user.schema';

export type OutletDocument = Outlet & Document;

export type Checklist = {
  label: string;
  checked: boolean;
};

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class Outlet extends BaseSchema {
  @Prop({ unique: true, index: true })
  ucc: string;

  @Prop({ index: true })
  name: string;

  @Prop({ index: true })
  area: string;

  @Prop({ default: OutletClassType.B1, index: true })
  outletClass: string;

  @Prop({ enum: OutletType, default: OutletType.MONT })
  type: string;

  @Prop({ index: true })
  address: string;

  @Prop({ index: true })
  contactName: string;

  @Prop({ index: true })
  contactNumber: string;

  @Prop({ index: true })
  contactPhoneCode: string;

  @Prop({ unique: false, nullable: true, default: moment().unix().toString() })
  outletDotId: string;

  @Prop({ required: false, default: false })
  isOmsConnected?: boolean;

  @Prop({ unique: false, nullable: true, index: true })
  customerType: string;

  @Prop({ unique: false, nullable: true, index: true })
  channel: string;

  @Prop({ unique: false, nullable: true, index: true })
  subChannel: string;

  @Prop({ unique: false, nullable: true, index: false })
  channelDescription: string;

  @Prop({ unique: false, nullable: true, index: false })
  subChannelDescription: string;

  @Prop({ unique: false, nullable: true })
  section: string;

  @Prop({ unique: false, nullable: true })
  businessSegment: string;

  @Prop({ unique: false, nullable: true, index: true, default: 0 })
  creditAmount: number;

  @Prop({ enum: OutletStatus, default: OutletStatus.ACTIVE, index: true })
  status: string;

  @Prop({ nullable: true, index: true })
  distributorId?: string;

  @Prop()
  region: string;

  @Prop({ nullable: true, type: [MSchema.Types.Mixed], default: [] })
  checklist?: Checklist[];

  @Prop({ nullable: true })
  lastUpdatedChecklist?: Date;

  @Prop({ nullable: true, index: true })
  depotId?: string;

  @Prop({ type: Number, default: 0 })
  totalProductAssigned?: number;

  @Prop({ type: Types.ObjectId, ref: User.name, index: true, required: false, default: null })
  saleRep: User[];
}

export const OutletSchema = SchemaFactory.createForClass(Outlet);
