import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types, Document } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';
import { Outlet } from './outlet.schema';
import { SaleRepOutletRelationStatusTracking } from '../enums/salerep-outlet-relation-status-tracking.enum';

export type SaleRepOutletRelationTrackingDocument = SaleRepOutletRelationTracking & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class SaleRepOutletRelationTracking extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  @Prop({ enum: SaleRepOutletRelationStatusTracking, default: SaleRepOutletRelationStatusTracking.CONNECTING, index: true })
  status: string;
}

export const SaleRepOutletRelationTrackingSchema = SchemaFactory.createForClass(SaleRepOutletRelationTracking);
