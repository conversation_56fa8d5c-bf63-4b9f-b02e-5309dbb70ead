import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';
import { Outlet } from './outlet.schema';

export type SaleRepOutletRelationDocument = SaleRepOutletRelation & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class SaleRepOutletRelation extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  @Prop({ nullable: true })
  note: string;

  @Prop({ nullable: true, index: true })
  noteUpdatedAt?: Date;

  @Prop({ default: false, index: true })
  disconnected: boolean;

  @Prop({ default: true, index: true })
  lastActive: boolean;
}

export const SaleRepOutletRelationSchema = SchemaFactory.createForClass(SaleRepOutletRelation);
SaleRepOutletRelationSchema.index({ outlet: 1, saleRep: 1 }, { unique: true });
