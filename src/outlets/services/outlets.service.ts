import { BadRequestException, ForbiddenException, forwardRef, HttpException, HttpStatus, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { parsePhoneNumberWithError } from 'libphonenumber-js';
import * as moment from 'moment';
import { Model, Types } from 'mongoose';
import { I18nContext } from 'nestjs-i18n';
import { GetOutletsDistributorArgs } from 'src/admin/args/get-outlets-distributor.args';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { normalizeQueryHelper } from 'src/shared/helpers';
import { SalesRepStatus } from 'src/users/enums';
import { User } from 'src/users/schemas/user.schema';
import { UsersService } from 'src/users/services/users.service';
import { ConstantRoles } from 'src/utils/constants/role';
import { BaseJourneyPlanService, DistributorService, DistributorUserRelationService } from '../../distributor/services';
import { SaleRepFfcStoreService } from '../../sale-rep/services';
import { BaseService } from '../../shared/services/base-service';
import { generateFullMonthlyRange, isEmptyObjectOrArray, isPhoneNumberValidation, printLog, standardPhoneNumber, toListResponse, validateFields } from '../../utils';
import { OutletClassType } from '../enums/outlet-class.enum';
import { OutletStatus } from '../enums/outlet-status.enum';
import { SaleRepOutletRelationStatusTracking } from '../enums/salerep-outlet-relation-status-tracking.enum';
import { Outlet, OutletDocument } from '../schemas/outlet.schema';
import { SaleRepOutletRelationTrackingService } from './sale-rep-outlet-relation-tracking.service';
import { SaleRepOutletRelationService } from './sale-rep-outlet-relation.service';
import { Distributor } from 'src/distributor/schemas';
import { ImportExportCustomerColumns, ImportExportCustomerColumnsID, requiredFieldsImportCustomer, requiredFieldsImportCustomerID } from 'src/admin/enums';
import { listLangSupportImport } from 'src/i18n';
import { UpdateOutletDto } from '../dtos/update-outlet.dto';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import { JourneyPlanMissedReasonHistory, JourneyPlanMissedReasonHistoryDocument } from 'src/journey-plannings/schemas/journey-plan-absence.schema';
import { NJRS_REQUEST } from 'nj-request-scope';
import { OmsService } from 'src/external/services/oms.service';
import { UserDetailService } from '../../users/services/user-detail.service';

@Injectable()
export class OutletsService extends BaseService<Outlet> {
  constructor(
    @InjectModel(Outlet.name)
    public readonly _outletDocumentModel: Model<OutletDocument>,
    @InjectModel(OutletJourneyPlanning.name)
    private readonly planModel: Model<OutletJourneyPlanningDocument>,
    @InjectModel(JourneyPlanMissedReasonHistory.name)
    private readonly absenceModel: Model<JourneyPlanMissedReasonHistoryDocument>,
    private readonly _saleRepFfcStoreService: SaleRepFfcStoreService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _distributorService: DistributorService,
    @Inject(forwardRef(() => OutletJourneyPlanningService))
    private readonly _outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly _saleRepOutletRelationTrackingService: SaleRepOutletRelationTrackingService,
    @Inject(forwardRef(() => BaseJourneyPlanService))
    private readonly _baseJourneyPlanService: BaseJourneyPlanService,
    @Inject(forwardRef(() => UsersService))
    private readonly _usersService: UsersService,
    @Inject(forwardRef(() => SaleRepOutletRelationService))
    private readonly _saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly _userDetailService: UserDetailService,
    @Inject(NJRS_REQUEST) private readonly request: Request,
    @Inject(forwardRef(() => OmsService))
    private readonly omsService: OmsService,
  ) {
    super();
    this.model = _outletDocumentModel;
  }
  async migrateData() {
    const allDistributors = await this._distributorService.findAll({});
    for (const distributor of allDistributors) {
      const allSalesrep = await this._distributorUserRelationService.findAll({
        distributor: distributor._id,
        user: { $ne: null },
      });
      if (allSalesrep.length > 0) {
        const listOutlets = await this._saleRepOutletRelationService.findAll({
          saleRep: { $in: allSalesrep.map((s) => s._id) },
          disconnected: false,
        });
        if (listOutlets?.length > 0) {
          this._outletDocumentModel.updateMany(
            {
              _id: { $in: listOutlets.map((o) => o.outlet) },
            },
            {
              $set: {
                distributorId: distributor.distributorId,
                region: distributor.region,
              },
            },
          );
        }
      }
    }
  }

  async findByOutletId(userId: string): Promise<OutletDocument[]> {
    return this._outletDocumentModel.find({ _id: new Types.ObjectId(userId) });
  }

  async findByUCCs(listUCCs: string[]): Promise<OutletDocument[]> {
    return this._outletDocumentModel.find({ ucc: { $in: listUCCs } });
  }

  async findByDepotIds(depotIds: string[]): Promise<OutletDocument[]> {
    return this._outletDocumentModel.find({ depotId: { $in: depotIds } }, { _id: 1 }).lean();
  }

  async findOneByOutletId(userId: string): Promise<OutletDocument> {
    return this._outletDocumentModel.findOne({ _id: new Types.ObjectId(userId) });
  }

  async getLastUpdatedOn(salesRepUUIDs: string[]) {
    if (salesRepUUIDs.length) {
      const saleRepOutletRelation = await this._saleRepOutletRelationService.findAll({ saleRep: { $in: salesRepUUIDs.map((sid) => new Types.ObjectId(sid)) } });
      if (isEmptyObjectOrArray(saleRepOutletRelation)) {
        return null;
      }
      return await this.findOne({ _id: { $in: saleRepOutletRelation.map((oid) => oid.outlet._id) } }, null, { sort: { updatedAt: -1 } });
    }
    return await this.findOne({}, null, { sort: { updatedAt: -1 } });
  }

  async getListOutlets(userId: string, query: GetOutletsDistributorArgs & PaginationParams & OrderParams) {
    const { search, distributorId: filterDistributorId, offset, limit, orderBy, orderDesc } = query;

    const userRole = await this._userDetailService.findUserRoles({ userId: userId.toString(), isUserAdmin: true });

    const statusOptions = search
      ? {}
      : {
          status: OutletStatus.ACTIVE,
        };
    let salesRepIds: string[] = [];
    let store: Distributor;
    if (userRole.some((r) => r.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(userId);
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId.toString() === filterDistributorId);
      if (!distributorAdmins || !distributorAdmins.length || !distributor) {
        return {
          ...toListResponse([[], 0]),
          lastUpdatedAt: null,
        };
      }
      store = distributor.distributor;
    } else {
      if (!filterDistributorId) {
        return {
          ...toListResponse([[], 0]),
          lastUpdatedAt: null,
        };
      }
      const distributorAdmin = await this._distributorService.findOne({ distributorId: filterDistributorId });
      if (!distributorAdmin) {
        return {
          ...toListResponse([[], 0]),
          lastUpdatedAt: null,
        };
      }
      store = distributorAdmin;
    }
    const salesReps = await this._distributorUserRelationService.findAll({
      user: { $exists: true },
      userAdmin: null,
      distributor: store._id,
    });
    salesRepIds = salesReps.map((sr) => sr.user._id.toString());
    const [result, lastUpdatedOutlet] = await Promise.all([
      this.getOutletsBySalesRepIds({
        salesRepIds,
        distributor: store,
        search,
        limit: +limit,
        offset: +offset,
        orderBy,
        orderDesc,
        ...statusOptions,
      }),
      this.getLastUpdatedOn(salesRepIds),
    ]);

    const [{ totalRecords, data }] = result;
    return {
      ...toListResponse([data, totalRecords?.[0]?.total ?? 0]),
      lastUpdatedAt: lastUpdatedOutlet?.updatedAt || null,
      distributorId: filterDistributorId,
    };
  }

  async getListOutletsBySalesRepIds(salesRepIds: any) {
    if (!salesRepIds?.length) {
      return [];
    }

    const salesRepObjectIds = salesRepIds.map((sid) => new Types.ObjectId(sid));
    const salesRepOutlets = await this._saleRepOutletRelationService.getOutletSalesRepRelationsBySaleRepIds(salesRepObjectIds);
    if (!salesRepOutlets?.length) {
      return [];
    }
    const outlets = await this.model.find({
      _id: { $in: salesRepOutlets?.map((item) => item.outlet) },
    });

    if (!outlets.length) {
      return [];
    }

    const outletIdToDepot = new Map<string, string>();
    for (const outlet of outlets) {
      if (outlet.depotId) {
        outletIdToDepot.set(String(outlet._id), String(outlet.depotId));
      }
    }

    const groupedByDepot: Record<string, Set<string>> = {};
    const salesRepToOutlets: Record<string, string[]> = {};

    for (const rel of salesRepOutlets) {
      const saleRepId = String(rel.saleRep);
      const outletId = String(rel.outlet);
      const depotId = outletIdToDepot.get(outletId);
      if (!depotId) continue;

      if (!salesRepToOutlets[saleRepId]) {
        salesRepToOutlets[saleRepId] = [];
      }
      salesRepToOutlets[saleRepId].push(outletId);

      if (!groupedByDepot[depotId]) {
        groupedByDepot[depotId] = new Set();
      }
      groupedByDepot[depotId].add(saleRepId);
    }

    return Object.entries(groupedByDepot).map(([depotId, salesRepSet]) => ({
      depotId,
      salesRepIds: Array.from(salesRepSet),
      outletIds: salesRepToOutlets,
    }));
  }

  private async getOutletsBySalesRepIds({
    salesRepIds,
    search,
    limit = 1,
    offset = 0,
    distributor,
    orderBy = 'updatedAt',
    orderDesc = 'desc',
    status = '',
  }: { salesRepIds: string[]; distributor?: Distributor } & GetOutletsDistributorArgs & PaginationParams & OrderParams) {
    const BATCH_SIZE = 1000;
    let offsetBatch = 0;
    const outletIdSet = new Set<string>();

    if (salesRepIds.length) {
      while (true) {
        const batchRelations = await this._saleRepOutletRelationService.findOutletBySaleIds(salesRepIds, offsetBatch, BATCH_SIZE);
        if (!batchRelations.length) break;

        for (const rel of batchRelations) {
          if (rel?.outlet) outletIdSet.add(String(rel.outlet));
        }

        offsetBatch += BATCH_SIZE;
      }
    }

    const outletIds = Array.from(outletIdSet).map((id) => new Types.ObjectId(id));

    // ===== Base Aggregation Pipeline =====
    const basePipeline: any[] = [];

    if (outletIds.length) {
      basePipeline.push({
        $match: { _id: { $in: outletIds } },
      });
    }

    if (status) {
      basePipeline.push({
        $match: {
          status,
          distributorId: distributor?.distributorId,
        },
      });
    }

    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      basePipeline.push({
        $match: {
          $or: [{ name: new RegExp(normalizedQuery, 'i') }, { ucc: new RegExp(normalizedQuery, 'i') }],
        },
      });
    }

    basePipeline.push(
      {
        $project: {
          _id: 0,
          o: '$$ROOT',
        },
      },
      {
        $lookup: {
          localField: 'o._id',
          from: 'salerepoutletrelations',
          foreignField: 'outlet',
          as: 'sor',
        },
      },
      { $unwind: { path: '$sor', preserveNullAndEmptyArrays: true } },
    );

    const condition: any[] = [
      {
        'sor.disconnected': true,
        'sor.lastActive': true,
        'o.distributorId': distributor ? distributor.distributorId : { $nin: [null, ''] },
      },
      {
        'sor.disconnected': null,
        'o.distributorId': distributor ? distributor.distributorId : { $nin: [null, ''] },
      },
    ];

    if (salesRepIds.length) {
      condition.push({
        'sor.saleRep': { $in: salesRepIds.map((sid) => new Types.ObjectId(sid)) },
        'sor.disconnected': false,
      });
    }

    basePipeline.push(
      { $match: { $or: condition } },
      {
        $lookup: {
          localField: 'sor.saleRep',
          from: 'users',
          foreignField: '_id',
          as: 'u',
        },
      },
      {
        $project: {
          sor: 1,
          o: 1,
          _id: '$sor._id',
          outletId: '$o._id',
          ucc: '$o.ucc',
          outletName: '$o.name',
          outletClass: '$o.outletClass',
          outletArea: { $ifNull: ['$o.area', ''] },
          status: { $ifNull: ['$o.status', ''] },
          outletAddress: '$o.address',
          contactName: '$o.contactName',
          contactNumber: '$o.contactNumber',
          contactPhoneCode: '$o.contactPhoneCode',
          updatedAt: '$o.updatedAt',
          disconnected: { $ifNull: ['$sor.disconnected', true] },
          channel: { $ifNull: ['$o.channel', ''] },
          subChannel: { $ifNull: ['$o.subChannel', ''] },
          businessSegment: { $ifNull: ['$o.businessSegment', ''] },
          saleRepId: { $ifNull: ['$u.saleRepId', ''] },
          saleRepUUID: { $ifNull: ['$u.saleRepId', ''] },
          distributorId: distributor.distributorId,
          distributorName: distributor.distributorName,
        },
      },
    );

    const totalResult = await this.model
      .aggregate([...basePipeline, { $count: 'total' }])
      .allowDiskUse(true)
      .exec();
    const totalRecords = totalResult[0]?.total || 0;

    const sort: Record<string, 1 | -1> = {
      [orderBy]: orderDesc === 'desc' ? -1 : 1,
    };
    const data = await this.model
      .aggregate([...basePipeline, { $sort: sort }, { $skip: offset >= 0 ? offset : 0 }, { $limit: limit >= 1 ? limit : 1 }])
      .collation({ locale: 'en' })
      .allowDiskUse(true)
      .exec();

    return [
      {
        totalRecords: [{ total: totalRecords }],
        data,
      },
    ];
  }

  private async getOutletsBySalesRepIdsOld({
    salesRepIds,
    search,
    limit = 1,
    offset = 0,
    distributor,
    orderBy = 'updatedAt',
    orderDesc = 'desc',
    status = '',
  }: { salesRepIds: string[]; distributor?: Distributor } & GetOutletsDistributorArgs & PaginationParams & OrderParams) {
    const aggregation = this.model.aggregate().allowDiskUse(true);

    if (salesRepIds.length) {
      const BATCH_SIZE = 1000;
      let offset = 0;
      const outletIdSet = new Set<string>();

      while (true) {
        const batchRelations = await this._saleRepOutletRelationService.findOutletBySaleIds(salesRepIds, offset, BATCH_SIZE);
        if (!batchRelations.length) break;

        for (const rel of batchRelations) {
          if (rel?.outlet) {
            outletIdSet.add(String(rel.outlet));
          }
        }

        offset += BATCH_SIZE;
      }
      const outletIds = Array.from(outletIdSet).map((id) => new Types.ObjectId(id));
      if (outletIds.length) {
        aggregation.match({
          _id: { $in: outletIds },
        });
      }
    }

    // filter outlets
    if (status) {
      aggregation.match({
        status,
        distributorId: distributor?.distributorId,
      });
    }

    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      aggregation.match({
        $or: [
          {
            name: new RegExp(normalizedQuery, 'i'),
          },
          {
            ucc: new RegExp(normalizedQuery, 'i'),
          },
        ],
      });
    }

    aggregation
      .project({
        _id: 0,
        o: '$$ROOT',
      })
      .lookup({
        localField: 'o._id',
        from: 'salerepoutletrelations',
        foreignField: 'outlet',
        as: 'sor',
      })
      .unwind({
        path: '$sor',
        preserveNullAndEmptyArrays: true,
      });

    const sort: Record<string, any> = {
      [orderBy]: orderDesc,
    };

    const condition: any[] = [
      {
        'sor.disconnected': true,
        'sor.lastActive': true,
        'o.distributorId': distributor
          ? distributor.distributorId
          : {
              $nin: [null, ''],
            },
      },
      {
        'sor.disconnected': null,
        'o.distributorId': distributor
          ? distributor.distributorId
          : {
              $nin: [null, ''],
            },
      },
    ];

    if (salesRepIds.length) {
      condition.push({
        'sor.saleRep': {
          $in: salesRepIds.map((sid) => new Types.ObjectId(sid)),
        },
        'sor.disconnected': false,
      });
    }

    const res = await aggregation
      .match({
        $or: condition,
      })
      .lookup({
        localField: 'sor.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .project({
        sor: 1,
        o: 1,
        _id: '$sor._id',
        outletId: '$o._id',
        ucc: '$o.ucc',
        outletName: '$o.name',
        outletClass: '$o.outletClass',
        outletArea: { $ifNull: ['$o.area', ''] },
        status: { $ifNull: ['$o.status', ''] },
        outletAddress: '$o.address',
        contactName: '$o.contactName',
        contactNumber: '$o.contactNumber',
        contactPhoneCode: '$o.contactPhoneCode',
        updatedAt: '$o.updatedAt',
        disconnected: { $ifNull: ['$sor.disconnected', true] },
        channel: { $ifNull: ['$o.channel', ''] },
        subChannel: { $ifNull: ['$o.subChannel', ''] },
        businessSegment: { $ifNull: ['$o.businessSegment', ''] },
        saleRepId: { $ifNull: ['$u.saleRepId', ''] },
        saleRepUUID: { $ifNull: ['$u.saleRepId', ''] },
        distributorId: distributor.distributorId,
        distributorName: distributor.distributorName,
      })
      .sort(sort)
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: offset >= 0 ? offset : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      });

    return res;
  }

  transformStatus(status: string, i18n: I18nContext) {
    status = status.toUpperCase();
    const listStatus = [...Object.values(OutletStatus)];
    for (const iterator of listStatus) {
      const listStatusTranslate = listLangSupportImport.map((lang) => i18n.translate(`importExport.${iterator}`, { lang }).toUpperCase());
      if (listStatusTranslate.includes(status)) {
        status = iterator;
        break;
      }
    }
    return status;
  }

  async uploadOutlets(userId: string, distributorId: string, file: Express.Multer.File, i18n: I18nContext) {
    const [existedDistributorUserRelation] = await this._distributorUserRelationService.getDistributorUserRelation({
      'ua._id': new Types.ObjectId(userId),
      'dis.distributorId': distributorId,
    });
    if (!existedDistributorUserRelation) {
      throw new BadRequestException(i18n.t('distributor.cannot_upload'));
    }

    const failure = [];
    const success = [];
    const insertManyOutletsDto: Array<{ outletDto: Record<string, any>; row: number; salesRep?: User; distributorId?: string }> = [];
    const updateManyOutletsDto: Array<{ outletDto: Record<string, any>; row: number; salesRep: User; oldOutlet: Outlet }> = [];
    const excelData = await this._distributorService.handleUploadingFile(
      file,
      ImportExportCustomerColumns.map((e) => i18n.translate(`importExport.${e}`)),
      ImportExportCustomerColumns,
      i18n,
    );

    const listDuplicateUcc = this.getDuplicateUcc(excelData.filter((item) => item.ucc).map((item) => item.ucc));
    listDuplicateUcc.forEach((ucc) => {
      const rowsNumber = excelData.map((item, index) => (item.ucc === ucc ? index : -1)).filter((item) => item > -1);
      failure.push({
        reason: {
          [i18n.t('importExport.message')]: i18n.t('distributor.customer_master_data.duplicate_ucc', { args: { rowNumber: rowsNumber.join(', '), ucc } }),
        },
      });
    });

    const validExelData = excelData.filter((record) => !listDuplicateUcc.includes(record.ucc));

    const validOutletClasses = Object.values(OutletClassType) as string[];
    const salesRepIdsData = validExelData.filter((item) => item.salesRepId).map((item) => item.salesRepId);
    const salesRepMap = await this.getVisibilityOfSalesReps(salesRepIdsData);
    const distributorUserRelationMap = await this.getVisibilityOfDistributorSalesRepRelations(
      Object.values(salesRepMap).map((item: User) => item._id.toString()),
      existedDistributorUserRelation.dur.distributor._id.toString(),
    );
    const uccMap = await this.getVisibilityOfListUcc(validExelData.filter((item) => item.ucc).map((item) => item.ucc));

    validExelData.forEach((row, index) => {
      const rowNumber = index + 2;
      // check required field
      const missingFields = this.checkUploadOutletMissingFields(row);
      if (missingFields.length) {
        failure.push({
          ...row,
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.missing_fields', { args: { fieldName: missingFields.join(', '), rowNumber } }),
          },
        });
        return;
      }
      row.contactPhoneCode = row.mobilePhoneCode;
      row.contactNumber = row.phoneNumber;
      const { ucc, outletClass, contactPhoneCode, contactNumber, salesRepId, status } = row;

      let listStatus: any = [...Object.values(OutletStatus)];
      listLangSupportImport.forEach((lang) => {
        listStatus = [...listStatus, ...Object.values(OutletStatus).map((e) => i18n.translate(`importExport.${e}`, { lang }))];
      });
      if (!listStatus.includes(status)) {
        failure.push({
          ...row,
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.customer_master_data.invalid_outlet_status', { args: { rowNumber } }),
          },
        });
        return;
      }
      row.status = this.transformStatus(row.status, i18n);

      // check outlet class
      if (!validOutletClasses.includes(outletClass)) {
        failure.push({
          ...row,
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.customer_master_data.invalid_outlet_class', { args: { rowNumber } }),
          },
        });
        return;
      }

      if (contactNumber && contactPhoneCode && !isPhoneNumberValidation(contactNumber, contactPhoneCode)) {
        failure.push({
          ...row,
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.customer_master_data.invalid_contact_number', { args: { rowNumber } }),
          },
        });
        return;
      }

      // check sales rep
      // if pass sales rep id, then check exist sales rep + exist relation with distributor
      const salesRep = salesRepMap[salesRepId];
      if (salesRepId) {
        if (!salesRep) {
          failure.push({
            ...row,
            reason: {
              [i18n.t('importExport.message')]: i18n.t('distributor.customer_master_data.not_found_sales_rep_id', { args: { rowNumber } }),
            },
          });
          return;
        }

        // check relation with distributor
        if (!distributorUserRelationMap[salesRep._id.toString()]) {
          failure.push({
            ...row,
            reason: {
              [i18n.t('importExport.message')]: i18n.t('distributor.customer_master_data.not_found_relation', { args: { rowNumber } }),
            },
          });
          return;
        }
      }

      // check existed outlet
      // if not exist, insert to db
      const existedOutlet = uccMap[ucc];
      if (!existedOutlet) {
        insertManyOutletsDto.push({
          outletDto: row,
          row: rowNumber,
          salesRep,
          distributorId: existedDistributorUserRelation.dis.distributorId,
        });
        return;
      }

      // if existed ucc but not pass sales rep id then throw error
      if (existedOutlet && existedOutlet.distributorId && !salesRep && existedOutlet.distributorId !== existedDistributorUserRelation.dis.distributorId) {
        failure.push({
          ...row,
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.customer_master_data.existed_ucc', { args: { rowNumber, ucc } }),
          },
        });
        return;
      }

      // update outlet if pass sales rep id and existed ucc
      updateManyOutletsDto.push({
        outletDto: { ...row, distributorId: existedDistributorUserRelation.dis.distributorId },
        row: rowNumber,
        salesRep,
        oldOutlet: existedOutlet,
      });
    });

    if (insertManyOutletsDto?.length || updateManyOutletsDto?.length) {
      await this.inactiveOutletInDistributor(existedDistributorUserRelation.dis, updateManyOutletsDto, i18n);
    }
    const [successInsert, failedInsert] = await this.insertManyOutlets(insertManyOutletsDto, i18n);
    const [successUpdate, failedUpdate] = await this.updateManyOutlets(updateManyOutletsDto, i18n);

    return {
      complete: true,
      failure: [...failure, ...failedInsert, ...failedUpdate],
      success: [...success, ...successInsert, ...successUpdate],
    };
  }

  async uploadOutletsID(userId: string, distributorId: string, file: Express.Multer.File, i18n: I18nContext) {
    const [existedDistributorUserRelation] = await this._distributorUserRelationService.getDistributorUserRelation({
      'ua._id': new Types.ObjectId(userId),
      'dis.distributorId': distributorId,
    });
    if (!existedDistributorUserRelation) {
      throw new BadRequestException(i18n.t('distributor.cannot_upload'));
    }

    const failure = [];
    const success = [];
    const updateManyOutletsDto: Array<{ outletDto: Record<string, any>; row: number; oldOutlet: Outlet }> = [];
    const excelData = await this._distributorService.handleUploadingFile(
      file,
      ImportExportCustomerColumnsID.map((e) => i18n.translate(`importExport.${e}`)),
      ImportExportCustomerColumnsID,
      i18n,
    );

    const listDuplicateUcc = this.getDuplicateUcc(excelData.filter((item) => item.ucc).map((item) => item.ucc));
    listDuplicateUcc.forEach((ucc) => {
      const rowsNumber = excelData.map((item, index) => (item.ucc === ucc ? index : -1)).filter((item) => item > -1);
      failure.push({
        reason: {
          [i18n.t('importExport.message')]: i18n.t('distributor.customer_master_data.duplicate_ucc', { args: { rowNumber: rowsNumber.join(', '), ucc } }),
        },
      });
    });

    const validExelData = excelData.filter((record) => !listDuplicateUcc.includes(record.ucc));
    const uccMap = await this.getVisibilityOfListUcc(validExelData.filter((item) => item.ucc).map((item) => item.ucc));

    validExelData.forEach((row, index) => {
      const rowNumber = index + 2;
      // check required field
      const missingFields = this.checkUploadOutletMissingFieldsID(row);
      if (missingFields.length) {
        failure.push({
          ...row,
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.missing_fields', { args: { fieldName: missingFields.join(', '), rowNumber } }),
          },
        });
        return;
      }
      const { ucc } = row;

      // check existed outlet
      const existedOutlet = uccMap[ucc];

      if (!existedOutlet) {
        failure.push({
          ...row,
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.customer_master_data.not_found_ucc', { args: { rowNumber, ucc } }),
          },
        });
        return;
      }

      // if existed ucc but not pass sales rep id then throw error
      if (existedOutlet && existedOutlet.distributorId && existedOutlet.distributorId !== existedDistributorUserRelation.dis.distributorId) {
        failure.push({
          ...row,
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.customer_master_data.existed_ucc', { args: { rowNumber, ucc } }),
          },
        });
        return;
      }

      // update outlet if pass sales rep id and existed ucc
      updateManyOutletsDto.push({
        outletDto: { ...row, distributorId: existedDistributorUserRelation.dis.distributorId },
        row: rowNumber,
        oldOutlet: existedOutlet,
      });
    });

    const [successUpdate, failedUpdate] = await this.updateManyOutletsV2(updateManyOutletsDto, i18n);

    return {
      complete: true,
      failure: [...failure, ...failedUpdate],
      success: [...success, ...successUpdate],
    };
  }

  private getDuplicateUcc = (listUcc: string[]) => {
    const countAppearanceTime = listUcc.reduce((pre, curr) => ({ ...pre, [curr]: (pre[curr] || 0) + 1 }), {});
    return Object.entries(countAppearanceTime)
      .filter(([, time]) => (time as number) > 1)
      .map(([ucc]) => ucc);
  };

  private checkUploadOutletMissingFields = (record: Record<string, any>) => {
    const requiredFields = requiredFieldsImportCustomer;
    const dataFields = Object.entries(record)
      .filter(([, value]) => !!value)
      .map(([key]) => key);

    return requiredFields.filter((field) => !dataFields.includes(field));
  };

  private checkUploadOutletMissingFieldsID = (record: Record<string, any>) => {
    const requiredFields = requiredFieldsImportCustomerID;
    const dataFields = Object.entries(record)
      .filter(([, value]) => !!value)
      .map(([key]) => key);

    return requiredFields.filter((field) => !dataFields.includes(field));
  };

  private getVisibilityOfSalesReps = async (saleRepIds: string[]): Promise<{ [key: string]: User }> => {
    const salesReps = await this._usersService.findAll({
      saleRepId: {
        $in: Array.from(new Set(saleRepIds)),
      },
    });

    return salesReps.reduce(
      (pre, curr) => ({
        ...pre,
        [curr.saleRepId]: curr,
      }),
      {},
    );
  };

  private getVisibilityOfDistributorSalesRepRelations = async (saleRepIds: string[], distributorId: string) => {
    const relations = await this._distributorUserRelationService.findAll({
      user: {
        $in: Array.from(new Set(saleRepIds)).map((id) => new Types.ObjectId(id)),
      },
      distributor: new Types.ObjectId(distributorId),
    });

    return relations.reduce(
      (pre, curr) => ({
        ...pre,
        [curr.user._id.toString()]: curr,
      }),
      {},
    );
  };

  private getVisibilityOfListUcc = async (listUcc: string[]) => {
    const outlets = await this.findAll({
      ucc: {
        $in: Array.from(new Set(listUcc)),
      },
    });

    return outlets.reduce((pre, curr) => ({ ...pre, [curr.ucc]: curr }), {});
  };

  private insertManyOutlets = async (dto: Array<{ outletDto: Record<string, any>; row: number; salesRep?: User; distributorId?: string }>, i18n: I18nContext) => {
    const success = [];
    const failed = [];

    await Promise.all(
      dto.map(async ({ outletDto, row, salesRep, distributorId }) => {
        try {
          const { ucc, outletName: name, outletClass, outletArea: area, outletAddress: address, contactName, status } = outletDto;
          let { contactPhoneCode, contactNumber } = outletDto;
          if (contactPhoneCode && contactNumber) {
            const mobilePhoneParse = parsePhoneNumberWithError(standardPhoneNumber(contactNumber, contactPhoneCode));
            contactNumber = mobilePhoneParse.number;
            contactPhoneCode = `+${mobilePhoneParse.countryCallingCode}`;
          }
          const payload = {
            ucc,
            name,
            area,
            outletClass,
            address,
            contactName,
            contactNumber,
            contactPhoneCode,
            status,
            outletDotId: ucc,
            salesRep: [salesRep._id],
          };

          if (distributorId) {
            payload['distributorId'] = distributorId;
          }

          const outlet = await this.create(payload);

          if (salesRep) {
            await this._saleRepOutletRelationService.createRelation(salesRep, outlet);
          }

          success.push(outletDto);
        } catch (e) {
          failed.push({
            ...outletDto,
            reason: {
              [i18n.t('importExport.message')]: `${row}: ${e.message}`,
            },
          });
        }
      }),
    );

    return [success, failed];
  };

  private updateManyOutlets = async (dto: Array<{ outletDto: Record<string, any>; row: number; salesRep?: User; oldOutlet: Outlet }>, i18n: I18nContext) => {
    const success = [];
    const failed = [];

    await Promise.all(
      dto.map(async ({ outletDto, row, salesRep, oldOutlet }) => {
        try {
          const { status, distributorId } = outletDto;
          if (salesRep) {
            const findOutletJourneyPlanning = await this._outletJourneyPlanningService.getInProgressTodayVisitBySaleRepIdAndOutletId(salesRep._id, oldOutlet._id);
            if (!isEmptyObjectOrArray(findOutletJourneyPlanning)) {
              if (oldOutlet.status === OutletStatus.ACTIVE && status == OutletStatus.INACTIVE) {
                failed.push({
                  ...outletDto,
                  reason: {
                    [i18n.t('importExport.cannotUpdateActiveStatus')]: status,
                  },
                });
              }
            }
          }
          await this.updateOutletFunc(
            {
              ...outletDto,
              outletId: oldOutlet._id,
              saleRepId: outletDto.salesRepId,
              distributorId,
            },
            i18n,
          );
          success.push(outletDto);
        } catch (e) {
          failed.push({
            ...outletDto,
            reason: {
              [i18n.t('importExport.message')]: `Row ${row}: ${e?.message}`,
            },
          });
        }
      }),
    );

    return [success, failed];
  };

  private updateManyOutletsV2 = async (dto: Array<{ outletDto: Record<string, any>; row: number; salesRep?: User; oldOutlet: Outlet }>, i18n: I18nContext) => {
    const success = [];
    const failed = [];
    for (const d of dto) {
      const { outletDto, row, oldOutlet, salesRep } = d;
      try {
        await this.updateOutletFuncV2(
          {
            ...outletDto,
            outletId: oldOutlet._id,
            saleRepId: outletDto.salesRepId,
            salesRep,
          },
          i18n,
        );
        success.push(outletDto);
      } catch (e) {
        failed.push({
          ...outletDto,
          reason: {
            [i18n.t('importExport.message')]: `Row ${row}: ${e?.message}`,
          },
        });
      }
    }

    // await Promise.all(dto.map(async ({ outletDto, row, salesRep, oldOutlet }) => {}));

    return [success, failed];
  };

  async updateOutletFunc(
    {
      outletId,
      outletAddress = '',
      outletArea = '',
      outletClass = '',
      outletName = '',
      contactName = '',
      contactNumber = '',
      contactPhoneCode = '',
      ucc,
      salesRep,
      saleRepId,
      status,
      distributorId = '',
      depotId = '',
    }: any,
    i18n: I18nContext,
  ) {
    // check Outlet
    const outlet = await this.findOne({ _id: new Types.ObjectId(outletId) });
    await validateFields({ outlet }, `common.not_found`, i18n);

    //Check UCC
    if (outlet.ucc != ucc) {
      const outletByUcc = await this.findOne({ ucc });
      if (!isEmptyObjectOrArray(outletByUcc)) {
        throw new HttpException(await i18n.translate(`message.existed_ucc`), HttpStatus.BAD_REQUEST);
      }
      outlet.outletDotId = moment().unix().toString();
    }

    if (isEmptyObjectOrArray(salesRep) && saleRepId) {
      salesRep = await this._usersService.findOne({ saleRepId: saleRepId?.trim() });
    }

    const isAddressChanged = outlet.address !== outletAddress || outlet.status !== status?.trim();

    if (contactNumber && contactPhoneCode) {
      const mobilePhoneParse = parsePhoneNumberWithError(standardPhoneNumber(contactNumber, contactPhoneCode));
      contactNumber = mobilePhoneParse.number;
      contactPhoneCode = `+${mobilePhoneParse.countryCallingCode}`;
    }
    outlet.address = outletAddress?.trim();
    outlet.contactName = contactName?.trim();
    outlet.contactNumber = contactNumber || '';
    outlet.contactPhoneCode = contactPhoneCode || '';
    outlet.ucc = ucc?.trim();
    outlet.area = outletArea?.trim();
    outlet.name = outletName?.trim();
    outlet.outletClass = outletClass?.trim();
    outlet.status = status?.trim();
    outlet.depotId = depotId?.trim();
    if (status === OutletStatus.INACTIVE) {
      outlet.saleRep = [...new Set([...(outlet.saleRep?.filter((s) => s._id !== salesRep._id) || [])])];
    } else if (status === OutletStatus.ACTIVE) {
      outlet.saleRep = [...new Set([...(outlet.saleRep?.filter((s) => s._id !== salesRep._id) || []), salesRep._id])];
    }

    //fix for report duplicate data
    if (saleRepId) {
      outlet.distributorId = null;
    } else {
      outlet.distributorId = distributorId;
    }
    const outletObj = await this.update(outlet._id, outlet);
    // check sales rep relation
    const existsSaleRepRelation = await this._saleRepOutletRelationService.findOneByOutletId(outlet._id);

    //Check Sale
    if (!saleRepId?.trim()) {
      if (existsSaleRepRelation) {
        await this._saleRepOutletRelationService.updateRelation(existsSaleRepRelation._id, { disconnected: true, lastActive: true }, outlet);
        await this._saleRepOutletRelationTrackingService.createRelationTracking(existsSaleRepRelation.saleRep, outlet, SaleRepOutletRelationStatusTracking.SHUTDOWN);
      }
      return outletObj;
    }

    if (!existsSaleRepRelation) {
      await this._saleRepOutletRelationService.createRelation(salesRep, outlet);
      await this._saleRepOutletRelationTrackingService.createRelationTracking(salesRep, outlet, SaleRepOutletRelationStatusTracking.CONNECTING);
    } else {
      //check the activation data for outlet and old sales rep
      if (status !== OutletStatus.INACTIVE && saleRepId == existsSaleRepRelation.saleRep.saleRepId && existsSaleRepRelation.saleRep.saleRepStatus !== SalesRepStatus.ACTIVE) {
        throw new HttpException(await i18n.translate(`plan.update_outlet_failed`), HttpStatus.BAD_REQUEST);
      }

      //check if change salerep
      if (saleRepId?.length > 0 && saleRepId !== existsSaleRepRelation.saleRep.saleRepId) {
        //check the activation data for outlet and new sales rep
        if (status !== OutletStatus.ACTIVE || salesRep.saleRepStatus !== SalesRepStatus.ACTIVE) {
          throw new HttpException(await i18n.translate(`plan.update_outlet_failed`), HttpStatus.BAD_REQUEST);
        }

        // Find outlet have plan is IN_PROGRESS in today
        // If have outlet planning => Don't change status from active to deactivate
        const outletJourneyPlanning = await this._outletJourneyPlanningService.getInProgressTodayVisitBySaleRepIdAndOutletId(existsSaleRepRelation.saleRep._id, outlet._id);
        if (!isEmptyObjectOrArray(outletJourneyPlanning) && (outlet.status === OutletStatus.ACTIVE || status !== OutletStatus.ACTIVE)) {
          throw new HttpException(await i18n.translate(`plan.update_status`), HttpStatus.BAD_REQUEST);
        }

        let oldSaleRep = null;
        let existedConnected = null;
        const saleRepRelations = await this._saleRepOutletRelationService.findByOutletId(outletId);

        //Update all relation outlet - sales rep to disconnected
        if (!isEmptyObjectOrArray(saleRepRelations)) {
          for (const saleRepRelation of saleRepRelations) {
            if (saleRepRelation.saleRep._id.toString() != salesRep._id.toString()) {
              if (saleRepRelation.disconnected == false) {
                oldSaleRep = saleRepRelation.saleRep;
              }
              await this._saleRepOutletRelationService.updateRelation(saleRepRelation._id, { disconnected: true, lastActive: true }, outlet);
              await this._saleRepOutletRelationTrackingService.createRelationTracking(saleRepRelation.saleRep, outlet, SaleRepOutletRelationStatusTracking.SHUTDOWN);
            } else {
              existedConnected = saleRepRelation;
            }
          }
        }
        if (isEmptyObjectOrArray(existedConnected)) {
          await this._saleRepOutletRelationService.createRelation(salesRep, outlet);
          await this._saleRepOutletRelationTrackingService.createRelationTracking(salesRep, outlet, SaleRepOutletRelationStatusTracking.CONNECTING);
        }

        if (!isEmptyObjectOrArray(existedConnected) && existedConnected.disconnected) {
          await this._saleRepOutletRelationService.updateRelation(existedConnected._id, { disconnected: false, lastActive: true }, outlet);
          await this._saleRepOutletRelationTrackingService.createRelationTracking(salesRep, outlet, SaleRepOutletRelationStatusTracking.CONNECTING);
        }

        //Check un-executed journey planing
        if (!isEmptyObjectOrArray(oldSaleRep)) {
          //Check Base Journey Planing
          const checkBaseJourneyPlaning = await this._baseJourneyPlanService.assignBasePlanned(oldSaleRep, salesRep, outlet);
          if (!checkBaseJourneyPlaning) {
            throw new HttpException(
              await i18n.t(`user.can_not_assigned_outlet_sale_rep_base_data`, {
                args: {
                  outlet: outlet.name,
                  saleRep: `${salesRep.firstname} ${salesRep.lastname}`,
                },
              }),
              HttpStatus.BAD_REQUEST,
            );
          }

          const checkUnExecuted = await this._outletJourneyPlanningService.assignUnExecutedPlannedVisits(oldSaleRep._id.toString(), salesRep._id, outlet);
          if (!checkUnExecuted) {
            throw new HttpException(
              await i18n.t(`user.can_not_assigned_outlet_sale_rep`, {
                args: {
                  outlet: outlet.name,
                  saleRep: `${salesRep.firstname} ${salesRep.lastname}`,
                },
              }),
              HttpStatus.BAD_REQUEST,
            );
          }
        }
      }

      // remove relation if status is deactive
      if (status == OutletStatus.INACTIVE) {
        // Delete base journey plans of Inactive outlet
        await this._baseJourneyPlanService.deleteByCondition({ outlet: outlet._id });
        //Delete un-executed JP of Inactive outlet
        await this._outletJourneyPlanningService.deleteUnExecutedPlannedVisitsByOutlet(outlet._id);
      }
      // PDH-2865 Change status to re-calculate route by GG Maps API
      if (isAddressChanged || status == OutletStatus.INACTIVE) {
        await this._usersService.update(salesRep._id, { isAddressChanged: true });
      }
    }
    return outletObj;
  }

  async updateOutletFuncV2({ outletId, outletArea = '', contactName = '', outletClass = '', salesRep = null }: any, i18n: I18nContext) {
    // check Outlet
    const outlet = await this.findOne({ _id: new Types.ObjectId(outletId) });
    await validateFields({ outlet }, `common.not_found`, i18n);

    outlet.contactName = contactName?.trim();
    outlet.area = outletArea?.trim();
    outlet.outletClass = outletClass?.trim();
    outlet.saleRep = [...new Set([...(outlet.saleRep?.filter((s) => s._id !== salesRep._id) || []), salesRep._id])];

    const outletObj = await this.update(outlet._id, outlet);

    return outletObj;
  }

  private async inactiveOutletInDistributor(distributor: Distributor, updateManyOutletsDto, i18n) {
    const listUccUpdate = updateManyOutletsDto.map((e) => e.oldOutlet.ucc);
    const salesReps = await this._distributorUserRelationService.findAll({
      user: { $exists: true },
      userAdmin: null,
      distributor: distributor._id,
    });
    const result = await this.getOutletsBySalesRepIds({
      salesRepIds: salesReps.map((sr) => sr.user._id.toString()),
      distributor,
      limit: 99999,
      offset: 0,
      status: OutletStatus.ACTIVE,
    });
    let [{ data }] = result;
    data = data.filter((e) => !listUccUpdate.includes(e.ucc));
    await Promise.all(
      data.map(async (outlet) => {
        try {
          await this.updateOutletFunc({ ...outlet, saleRepId: outlet?.saleRepId[0], status: OutletStatus.INACTIVE }, i18n);
        } catch (error) {
          console.log(error.message);
        }
      }),
    );
  }

  async findAllActiveOutletBySaleRepId(salesRepId: string) {
    return await this._saleRepOutletRelationService.findAllActiveOutletBySaleRepId(salesRepId);
  }

  async getOutletDetailsForSalesRep({ outletId, salesRepId, i18n }: { outletId: string; salesRepId: string; i18n: I18nContext }) {
    const outlet = await this.checkOutlet({ outletId, salesRepId, i18n });
    const [anotherInProgressPlan] = await Promise.all([
      this.getAnotherTodayInprogressPlan({
        outletId,
        salesRepId,
      }),
    ]);
    if (!outlet.isOmsConnected) {
      outlet.isOmsConnected = await this.omsService.isOmsOutletConnected(outlet._id, outlet.ucc, outlet.depotId);
    }

    //Start Preparing OMS data for Taking Order - Trigger step
    if (outlet.isOmsConnected) {
      this.omsService
        .getCachedDataByOutlet({ outletId: new Types.ObjectId(outletId), useDb: false })
        .then()
        .catch();
    }

    const absence = outlet.todayPlan?.missedReason ? await this.getAbsenceData(outlet.todayPlan._id) : null;
    return this.transformOutletDetails({ outlet, todayPlan: outlet.todayPlan, absence, anotherInProgressPlan, isOmsConnected: outlet.isOmsConnected });
  }

  async getAbsenceData(planId: string) {
    const absence = await this.absenceModel
      .findOne(
        { journeyPlan: new Types.ObjectId(planId) },
        {},
        {
          sort: {
            createdAt: -1,
          },
        },
      )
      .populate('missedReason evidenceImages');

    return absence;
  }

  private async getAnotherTodayInprogressPlan({ salesRepId, outletId }: { salesRepId: string; outletId: string }) {
    const startOfDay = moment().tz(process.env.TZ).startOf('day').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();

    const todayInprogressPlan = await this.planModel
      .findOne({
        outlet: { $ne: new Types.ObjectId(outletId) },
        saleRep: new Types.ObjectId(salesRepId),
        visitStatus: VisitStatus.IN_PROGRESS,
        $or: [
          { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
          { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
        ],
      } as any)
      .populate('outlet');

    return todayInprogressPlan;
  }

  async updateOutletDetails({ outletId, dto, salesRepId, i18n }: { outletId: string; dto: UpdateOutletDto; salesRepId: string; i18n: I18nContext }) {
    await this.checkOutlet({ outletId, salesRepId, i18n });

    if (!dto.checklist || !dto.checklist.every((item) => item.label?.trim() && typeof item.checked === 'boolean')) {
      throw new BadRequestException(i18n.translate('outlet.invalid_checklist_payload'));
    }

    await this._outletDocumentModel.findByIdAndUpdate(
      outletId,
      {
        $set: {
          checklist: dto.checklist.map((item) => ({
            label: item.label.trim(),
            checked: item.checked || false,
          })),
          lastUpdatedChecklist: new Date(),
        },
      },
      { new: true },
    );
    return this.getOutletDetailsForSalesRep({ outletId, salesRepId, i18n });
  }

  async updateCheckListOffline({ outletId, dto, i18n }: { outletId: string; dto: UpdateOutletDto; i18n: I18nContext }) {
    let success = false;
    let message = '';
    try {
      if (!dto.checklist || !dto.checklist.every((item) => item.label?.trim() && typeof item.checked === 'boolean')) {
        throw new BadRequestException(i18n.translate('outlet.invalid_checklist_payload'));
      }
      await this._outletDocumentModel.findByIdAndUpdate(
        outletId,
        {
          $set: {
            checklist: dto.checklist.map((item) => ({
              label: item.label.trim(),
              checked: item.checked || false,
            })),
            lastUpdatedChecklist: new Date(),
          },
        },
        { new: true },
      );
      success = true;
    } catch (error) {
      message = error.message;
    }
    return { key: 'checkList', success, message };
  }

  private async checkOutlet({ outletId, salesRepId, i18n }: { outletId: string; salesRepId: string; i18n: I18nContext }) {
    const outlet = await this._outletDocumentModel.findById(outletId);

    if (!outlet) {
      throw new NotFoundException(i18n.translate('outlet.not_found'));
    }

    const relation = await this._saleRepOutletRelationService.findOne({
      saleRep: new Types.ObjectId(salesRepId),
      outlet: new Types.ObjectId(outletId),
      disconnected: false,
    });

    if (!relation) {
      throw new ForbiddenException(i18n.translate('outlet.forbidden'));
    }

    const startOfDay = moment().tz(process.env.TZ).startOf('day').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();
    const todayPlan = await this._outletJourneyPlanningService.findOne({
      saleRep: relation.saleRep,
      outlet: outlet._id,
      $or: [
        {
          day: { $gte: startOfDay, $lte: endOfDay },
          rescheduled: false,
        },
        {
          rescheduledDay: { $gte: startOfDay, $lte: endOfDay },
          rescheduled: true,
        },
      ],
    } as any);

    return { ...(outlet.toJSON() as Outlet), todayPlan };
  }

  public async checkOutletSalesRepRelation({ outletId, salesRepId, i18n }: { outletId: string; salesRepId: string; i18n: I18nContext }) {
    const relation = await this._saleRepOutletRelationService.findOne({
      saleRep: new Types.ObjectId(salesRepId),
      outlet: new Types.ObjectId(outletId),
      disconnected: false,
    });

    if (!relation) {
      throw new ForbiddenException(i18n.translate('outlet.forbidden'));
    }
    return true;
  }

  /**
   *
   * @param salesRepId - External ID
   * @param startDate
   * @param endDate
   * @param offset
   * @param limit
   */
  public async getPerformanceSaleVolumeDetails(salesRepId: string, startDate: Date, endDate: Date, offset = 0, limit = 50) {
    try {
      //get user by salesRepId
      const salesRep = await this._usersService.findOne({ saleRepId: salesRepId });

      //get today plans
      const todayPlans = await this._outletJourneyPlanningService.getTodayPlans(salesRep._id);

      //get all today plans by date range
      const allPlans = await this._outletJourneyPlanningService.getPlansBySaleAndDateAndPagination(salesRep._id, startDate, endDate, offset, limit);
      if (isEmptyObjectOrArray(allPlans)) {
        return { todayList: [], visitList: [] };
      }

      //get outlet from today plans and all plans - set unique outlets
      const outlets = [...todayPlans, ...allPlans.data]
        .map((plan) => plan.outlet)
        .filter((outlet, index, self) => index === self.findIndex((o) => o._id.toString() === outlet._id.toString()));

      //get latest visited day for outlets
      const lastVisitedPlans = await this._outletJourneyPlanningService.getLatestVisitedByOutlets(outlets, startDate, endDate);

      //get cached data from omscachedata collection
      const cachedData = await this.omsService.getDBCachedDataByOutlets({
        filter: { outlet: outlets.map((o) => new Types.ObjectId(o._id)), updatedAt: { $gte: startDate, $lte: endDate } },
        project: {
          orderPerformanceData: 1,
          outlet: 1,
        },
      });

      const transformOutletData = (plan: any) => {
        const outletId = plan.outlet._id.toString();
        const latestVisitedDay = lastVisitedPlans?.find((item) => item?.outlet?.toString() === outletId);
        const orderPerformanceData = cachedData?.find((item) => item?.outlet?.toString() === outletId);
        const outlet = plan.outlet;
        const performanceData = orderPerformanceData?.orderPerformanceData || {};

        return {
          ucc: outlet.ucc,
          address: outlet.address,
          businessSegment: outlet.businessSegment,
          depotId: outlet.depotId,
          distributorId: outlet.distributorId,
          name: outlet.name,
          outletClass: outlet.outletClass,
          status: outlet.status,
          channel: outlet.channel,
          subChannel: outlet.subChannel,
          contactName: outlet.contactName || outlet.name,
          contactNumber: outlet.contactNumber,
          latestVisitedDay: latestVisitedDay?.visitedDay || null,
          performanceData: {
            average_volume_per_order: {
              value: parseFloat((performanceData?.average_volume?.value || 0)?.toFixed(2)),
              label: 'HL',
            },
            volume_performance: {
              value: parseFloat(Math.abs(performanceData?.volume_performance?.value || 0).toFixed(2)),
              is_increasing: performanceData?.volume_performance?.value > 0,
              current_month: performanceData?.volume_performance?.current_month,
              current_year: performanceData?.volume_performance?.current_year,
              current_month_total: parseFloat(performanceData?.volume_performance?.current_month_total?.toFixed(2)),
              last_month: performanceData?.volume_performance?.last_month,
              last_year: performanceData?.volume_performance?.last_year,
              last_month_total: parseFloat(performanceData?.volume_performance?.last_month_total?.toFixed(2)),
              label_total: 'HL',
            },
          },
        };
      };

      const todayList = todayPlans?.map(transformOutletData);
      const visitList = { total: allPlans?.total, data: allPlans?.data?.map(transformOutletData) };

      return { todayList: todayList, visitList: visitList };
    } catch (error) {
      printLog('getPerformanceSaleVolumeDetails', error);
      return { todayList: [], visitList: [] };
    }
  }

  public async getPerformanceOutletSaleVolumeDetails({
    depotExternalID,
    outlet,
    start,
    end,
    channelMonthlyAverageVolume,
    monthlyVolume,
    yearToDate,
    volumePerformance,
    averageVolume,
    i18n,
  }: {
    depotExternalID: string;
    outlet: any;
    start: Date;
    end: Date;
    channelMonthlyAverageVolume: boolean;
    monthlyVolume: boolean;
    yearToDate: boolean;
    volumePerformance: boolean;
    averageVolume: boolean;
    i18n: I18nContext;
  }) {
    //Get Latest Visited
    const latestPlanVisited = await this._outletJourneyPlanningService.getLatestPlanVisitedOfOutlet(
      outlet._id,
      moment().tz(process.env.TZ).startOf('month').toDate(),
      moment().tz(process.env.TZ).endOf('month').toDate(),
    );
    //Get Share Of Stock data
    const shareOfStocks = await this._outletJourneyPlanningService.calculateShareOfStock(
      outlet._id,
      moment().tz(process.env.TZ).startOf('month').toDate(),
      moment().tz(process.env.TZ).endOf('month').toDate(),
    );
    //Get Performance data from OMS
    const performanceData: any = await this.omsService.getPerformanceOutletSaleVolume(
      depotExternalID,
      outlet,
      start,
      end,
      channelMonthlyAverageVolume,
      monthlyVolume,
      yearToDate,
      volumePerformance,
      averageVolume,
    );

    //Get NPS Score
    const npsScore = {};

    return {
      share_of_stocks: shareOfStocks,
      latest_plan_visited: latestPlanVisited,
      nps_scores: npsScore,
      average_volume_per_order: {
        value: parseFloat((performanceData?.average_volume?.value || 0)?.toFixed(2)),
        label: 'HL',
      },
      volume_performance: {
        value: parseFloat(Math.abs(performanceData?.volume_performance?.value || 0).toFixed(2)),
        is_increasing: performanceData?.volume_performance?.value > 0,
        current_month: performanceData?.volume_performance?.current_month,
        current_year: performanceData?.volume_performance?.current_year,
        current_month_total: parseFloat(performanceData?.volume_performance?.current_month_total?.toFixed(2)),
        last_month: performanceData?.volume_performance?.last_month,
        last_year: performanceData?.volume_performance?.last_year,
        last_month_total: parseFloat(performanceData?.volume_performance?.last_month_total?.toFixed(2)),
        label_total: 'HL',
      },
      year_to_date_vol: {
        total: parseFloat(performanceData?.year_to_date?.total?.toFixed(2)),
        label: 'HL',
        top_outlet: 0,
      },
      monthly_volume: generateFullMonthlyRange(performanceData?.monthly_volume || [], 'total'),
      channel_monthly_average_volume: generateFullMonthlyRange(performanceData?.channel_monthly_average_volume || [], 'average')?.map((item) => ({
        ...item,
        average: parseFloat(Number(item.average).toFixed(2)),
      })),
    };
  }

  private transformOutletDetails({
    outlet,
    todayPlan,
    anotherInProgressPlan,
    absence,
    isOmsConnected,
  }: {
    outlet: Outlet;
    todayPlan?: OutletJourneyPlanning;
    anotherInProgressPlan?: OutletJourneyPlanning;
    absence?: JourneyPlanMissedReasonHistory;
    isOmsConnected: boolean;
  }) {
    return {
      id: outlet._id,
      name: outlet.name,
      address: outlet.address,
      contactName: outlet.contactName,
      contactNumber: outlet.contactNumber,
      ucc: outlet.ucc,
      channel: outlet.channel,
      subChannel: outlet.subChannelDescription || outlet.subChannel,
      class: outlet.outletClass,
      checklist: outlet.checklist || [],
      lastUpdatedChecklist: outlet.lastUpdatedChecklist,
      todayPlan: this.transformTodayPlan({ todayPlan, absence }),
      anotherOutletInProgressPlan: this.transformAnotherOutletTodayPlan(anotherInProgressPlan),
      isOmsConnected,
    };
  }

  private transformAnotherOutletTodayPlan(plan?: OutletJourneyPlanning) {
    if (!plan) {
      return null;
    }

    return {
      id: plan._id,
      outlet: {
        id: plan.outlet._id,
        name: plan.outlet.name,
      },
    };
  }

  private transformTodayPlan({ todayPlan, absence }: { todayPlan?: OutletJourneyPlanning; absence?: JourneyPlanMissedReasonHistory }) {
    if (!todayPlan) {
      return null;
    }

    const id = todayPlan._id;
    const visitStatus = todayPlan.visitStatus;

    return {
      id,
      visitStatus,
      isSkipped: visitStatus === VisitStatus.COMPLETED ? false : !!todayPlan.missedReason,
      skipReport: this.transformAbsence(absence),
    };
  }

  transformAbsence(absence?: JourneyPlanMissedReasonHistory) {
    if (!absence) {
      return null;
    }

    return {
      reason: {
        translations: absence.missedReason.translations,
      },
      evidenceImages: absence.evidenceImages.map((item) => ({ _id: item._id, path: item.path })),
    };
  }

  async findOneAndUpdateData(ucc: any, outletData: any, depotId: any, distributorId: any) {
    return this._outletDocumentModel.findOneAndUpdate({ ucc }, { $set: { ...outletData, depotId, distributorId } }, { upsert: true, new: true });
  }

  async findOneAndRemoveDistributorData(ucc: any) {
    return this._outletDocumentModel.updateOne(
      { ucc },
      {
        $set: {
          isActive: false,
          isDeleted: true,
          updatedAt: new Date(),
        },
      },
    );
  }

  async getOutletSalesRepRelationsByIds(outletId: string) {
    return this._saleRepOutletRelationService.findOne({ disconnected: false, outlet: new Types.ObjectId(outletId) });
  }
}
