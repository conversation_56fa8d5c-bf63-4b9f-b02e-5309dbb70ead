import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseService } from '../../shared/services/base-service';
import { SaleRepOutletRelationTracking, SaleRepOutletRelationTrackingDocument } from '../schemas/sale-rep-outlet-relation-tracking.schema';
import { User } from '../../users/schemas/user.schema';
import { Outlet } from '../schemas/outlet.schema';
import { SaleRepOutletRelationStatusTracking } from '../enums/salerep-outlet-relation-status-tracking.enum';

@Injectable()
export class SaleRepOutletRelationTrackingService extends BaseService<SaleRepOutletRelationTracking> {
  constructor(
    @InjectModel(SaleRepOutletRelationTracking.name)
    private readonly _model: Model<SaleRepOutletRelationTrackingDocument>,
  ) {
    super();
    this.model = _model;
  }

  async createRelationTracking(saleRep: User, outlet: Outlet, status: SaleRepOutletRelationStatusTracking): Promise<SaleRepOutletRelationTracking> {
    return await this.create({ outlet, saleRep, status });
  }
}
