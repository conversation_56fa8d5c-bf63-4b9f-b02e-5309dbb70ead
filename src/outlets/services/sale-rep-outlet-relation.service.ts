import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { normalizeQueryHelper } from '../../shared/helpers';
import { BaseService } from '../../shared/services/base-service';
import { SalesRepStatus } from '../../users/enums';
import { User, UserDocument } from '../../users/schemas/user.schema';
import { OutletStatus } from '../enums/outlet-status.enum';
import { ISaleRepOutletAggregation, ISor } from '../interfaces';
import { Outlet, OutletDocument } from '../schemas/outlet.schema';
import { SaleRepOutletRelation, SaleRepOutletRelationDocument } from '../schemas/sale-rep-outlet-relation.schema';
import { DistributorUserRelation, DistributorUserRelationDocument } from '../../distributor/schemas/distributor-user-relation.schema';
import { Distributor, DistributorDocument } from '../../distributor/schemas/distributor.schema';
import { isEmptyObjectOrArray } from '../../utils';

@Injectable()
export class SaleRepOutletRelationService extends BaseService<SaleRepOutletRelation> {
  constructor(
    @InjectModel(SaleRepOutletRelation.name)
    public readonly _saleRepOutletRelationDocumentModel: Model<SaleRepOutletRelationDocument>,
    @InjectModel(User.name)
    private readonly userModel: Model<UserDocument>,
    @InjectModel(Outlet.name)
    private readonly outletModel: Model<OutletDocument>,
    @InjectModel(DistributorUserRelation.name)
    private readonly distributorUserRelationModel: Model<DistributorUserRelationDocument>,
    @InjectModel(Distributor.name)
    private readonly distributorModel: Model<DistributorDocument>,
  ) {
    super();
    this.model = _saleRepOutletRelationDocumentModel;
  }

  async createRelation(saleRep: User, outlet: Outlet): Promise<SaleRepOutletRelation> {
    try {
      await this.updateByCondition({ outlet: outlet._id, lastActive: true }, { lastActive: false });
    } catch (e) {}

    const findOneRelation = await this.findOneAndUpdate({ outlet: outlet._id, saleRep: saleRep._id }, { disconnected: false, lastActive: true });
    if (!findOneRelation) {
      return this.create({ outlet, saleRep, lastActive: true });
    }
    return findOneRelation;
  }

  async updateRelation(id: string, updateDto: any, outlet: Outlet) {
    try {
      await this.updateByCondition({ outlet: outlet._id, lastActive: true }, { lastActive: false });
    } catch (e) {}

    const updatedAt = (updateDto as any).updatedAt || new Date();
    const lastActive = true;
    return await this.update(id, { ...updateDto, lastActive, updatedAt });
  }

  async findAllActiveOutletBySaleRepId(saleRepId) {
    const allOutletsOfSalesRep = await this._saleRepOutletRelationDocumentModel
      .find({ saleRep: new Types.ObjectId(saleRepId), disconnected: false })
      .select(['outlet'])
      .populate('outlet');
    return allOutletsOfSalesRep.filter((item) => item?.outlet?.status === OutletStatus.ACTIVE);
  }

  async getActiveSORByListSalesRepsIds(salesRepIds: string[]): Promise<Array<ISor>> {
    const allOutletsOfSalesRep = await this._saleRepOutletRelationDocumentModel.find({ saleRep: salesRepIds.map((sid) => new Types.ObjectId(sid)) }).populate('outlet');
    return allOutletsOfSalesRep
      .filter((item) => item?.outlet?.status === OutletStatus.ACTIVE || item?.outlet?.status === OutletStatus.INACTIVE)
      .map((item) => {
        return {
          disconnected: item.disconnected,
          outlet: item.outlet?._id,
          saleRep: item.saleRep?._id,
        };
      });
  }

  async findByOutletId(outletId: string): Promise<SaleRepOutletRelation[]> {
    return this._saleRepOutletRelationDocumentModel.find({ outlet: new Types.ObjectId(outletId) }).populate('saleRep');
  }

  async findOneByOutletId(outletId: string): Promise<SaleRepOutletRelation> {
    return this._saleRepOutletRelationDocumentModel.findOne({ outlet: new Types.ObjectId(outletId), disconnected: false }).populate('saleRep');
  }

  async findByOutletAndSaleRep(outletId: string, saleRepId: string) {
    return this._saleRepOutletRelationDocumentModel
      .findOne({
        outlet: new Types.ObjectId(outletId),
        saleRep: new Types.ObjectId(saleRepId),
      })
      .populate('outlet saleRep');
  }

  async findByOutletConnected(saleRepId: string, outletId: string): Promise<any> {
    return this._saleRepOutletRelationDocumentModel
      .findOne({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
      })
      .populate('outlet');
  }

  async findByOutletsConnected(salesRepOutletIds: { salesRepId: string; outletId: string }[]) {
    return this._saleRepOutletRelationDocumentModel
      .find({
        $or: salesRepOutletIds.map((item) => ({
          saleRep: new Types.ObjectId(item.salesRepId),
          outlet: new Types.ObjectId(item.outletId),
        })),
      })
      .populate('outlet');
  }

  async findAllByOutletsConnected(): Promise<any> {
    return this._saleRepOutletRelationDocumentModel
      .find({
        outlet: { $nin: [undefined, null, 'NULL', '', 'UNDEFINED'] },
        disconnected: false,
      })
      .populate({
        path: 'outlet',
        match: { outletDotId: { $nin: [undefined, null, 'NULL', '', 'UNDEFINED'] }, status: OutletStatus.ACTIVE },
      })
      .populate({
        path: 'saleRep',
        match: { saleRepStatus: SalesRepStatus.ACTIVE },
      });
  }

  /**
   * Optimized version using separate queries instead of complex aggregation lookups
   * @param salesRepUUIDs
   * @param skip
   * @param limit
   * @param sort
   * @param search
   * @param startDate
   * @param endDate
   * @returns
   */
  async getOutletsBySaleRepUUIDs(
    salesRepUUIDs: string[],
    skip = 0,
    limit = 1,
    sort: Record<string, any> = { updatedAt: -1 },
    search?: string,
    startDate: Date = null,
    endDate: Date = null,
  ): Promise<any> {
    try {
      // 1. Get relations with basic filters
      const matchCondition: any = {
        saleRep: { $in: salesRepUUIDs.map((sid) => new Types.ObjectId(sid)) },
        disconnected: false,
      };

      const relations = await this._saleRepOutletRelationDocumentModel.find(matchCondition).populate('saleRep', 'saleRepId _id').populate('outlet').lean();

      if (!relations.length) {
        return { totalRecords: [{ total: 0 }], data: [] };
      }

      // 2. Get distributor mappings in parallel
      const salesRepIds = relations.map((r) => r.saleRep._id);
      const distributorMappingsPromise = this.distributorUserRelationModel
        .find({ user: { $in: salesRepIds } })
        .populate('distributor', 'distributorId distributorName _id')
        .lean();

      const [distributorMappings] = await Promise.all([distributorMappingsPromise]);

      // 3. Create distributor lookup map
      const distributorMap = new Map();
      distributorMappings.forEach((mapping) => {
        distributorMap.set(String(mapping.user), {
          distributorId: mapping.distributor?.distributorId,
          distributorName: mapping.distributor?.distributorName,
        });
      });

      // 4. Transform and filter data
      let transformedData = relations
        .filter((relation) => relation.outlet && relation.saleRep) // Ensure populated data exists
        .map((relation) => {
          const outlet = relation.outlet as any;
          const saleRep = relation.saleRep as any;
          const distributorInfo = distributorMap.get(String(saleRep._id)) || {};

          return {
            _id: relation._id,
            outletId: outlet._id,
            ucc: outlet.ucc || '',
            outletName: outlet.name || '',
            outletClass: outlet.outletClass || '',
            outletArea: outlet.area || '',
            status: outlet.status || '',
            outletAddress: outlet.address || '',
            contactName: outlet.contactName || '',
            contactNumber: outlet.contactNumber || '',
            saleRepId: saleRep.saleRepId || '',
            saleRepUUID: saleRep._id,
            updatedAt: outlet.updatedAt,
            disconnected: relation.disconnected,
            distributorId: distributorInfo.distributorId || '',
            distributorName: distributorInfo.distributorName || '',
            channel: outlet.businessSegment || '',
            subChannel: outlet.subChannel || '',
            businessSegment: outlet.businessSegment || '',
          };
        });

      // 5. Apply search filter
      if (search) {
        const normalizedQuery = normalizeQueryHelper(search);
        const regex = new RegExp(normalizedQuery, 'i');
        transformedData = transformedData.filter((item) => regex.test(item.outletName) || regex.test(item.ucc));
      }

      // 6. Sort data
      const sortField = Object.keys(sort)[0] || 'updatedAt';
      const sortDirection = sort[sortField] || -1;
      transformedData.sort((a, b) => {
        const aVal = a[sortField];
        const bVal = b[sortField];
        if (aVal < bVal) return sortDirection === 1 ? -1 : 1;
        if (aVal > bVal) return sortDirection === 1 ? 1 : -1;
        return 0;
      });

      // 7. Apply pagination
      const total = transformedData.length;
      const paginatedData = transformedData.slice(skip >= 0 ? skip : 0, (skip >= 0 ? skip : 0) + (limit >= 1 ? limit : 1));

      return {
        totalRecords: [{ total }],
        data: paginatedData,
      };
    } catch (error) {
      console.error('Error in getOutletsBySaleRepUUIDs:', error);
      // Fallback to original aggregation method if optimized version fails
      return this.getOutletsBySaleRepUUIDsLegacy(salesRepUUIDs, skip, limit, sort, search, startDate, endDate);
    }
  }

  /**
   * Legacy version using aggregation with lookups (kept as fallback)
   */
  getOutletsBySaleRepUUIDsLegacy(
    salesRepUUIDs: string[],
    skip = 0,
    limit = 1,
    sort: Record<string, any> = { updatedAt: -1 },
    search?: string,
    startDate: Date = null,
    endDate: Date = null,
  ) {
    const aggregation = this._saleRepOutletRelationDocumentModel.aggregate().match({ saleRep: { $in: salesRepUUIDs.map((sid) => new Types.ObjectId(sid)) }, disconnected: false });
    if (startDate && endDate) {
      aggregation.match({ createdAt: { $lte: endDate } });
    }
    aggregation
      .project({
        _id: 0,
        sor: '$$ROOT',
      })
      .lookup({
        localField: 'sor.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .lookup({
        localField: 'sor.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      });

    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      aggregation.match({
        $or: [
          {
            'o.name': new RegExp(normalizedQuery, 'i'),
          },
          {
            'o.ucc': new RegExp(normalizedQuery, 'i'),
          },
        ],
      });
    }

    return aggregation
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: false,
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      })
      .project({
        _id: '$sor._id',
        outletId: '$o._id',
        ucc: '$o.ucc',
        outletName: '$o.name',
        outletClass: '$o.outletClass',
        outletArea: { $ifNull: ['$o.area', ''] },
        status: { $ifNull: ['$o.status', ''] },
        outletAddress: '$o.address',
        contactName: '$o.contactName',
        contactNumber: '$o.contactNumber',
        saleRepId: '$u.saleRepId',
        saleRepUUID: '$u._id',
        updatedAt: '$o.updatedAt',
        disconnected: '$sor.disconnected',
        distributorId: '',
        distributorName: '',
        channel: { $ifNull: ['$o.businessSegment', ''] },
        subChannel: { $ifNull: ['$o.subChannel', ''] },
        businessSegment: { $ifNull: ['$o.businessSegment', ''] },
      })
      .match({ saleRepUUID: { $in: salesRepUUIDs.map((sid) => new Types.ObjectId(sid)) }, disconnected: false })
      .sort(sort)
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      });
  }

  async getLastUpdatedOn(salesRepUUIDs: string[]) {
    const [lastUpdatedOutlet] = await this._saleRepOutletRelationDocumentModel
      .aggregate()
      .project({
        _id: 0,
        sor: '$$ROOT',
      })
      .lookup({
        localField: 'sor.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .lookup({
        localField: 'sor.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: false,
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: true,
      })
      .match({ 'u._id': { $in: salesRepUUIDs.map((sid) => new Types.ObjectId(sid)) } })
      .project({
        updatedAt: '$o.updatedAt',
      })
      .sort({ updatedAt: -1 })
      .limit(1)
      .exec();
    return lastUpdatedOutlet;
  }

  async findAllOutletOfSaleRep(saleReps: string[] | User[], skip: number, limit: number): Promise<any> {
    const aggregate = this._saleRepOutletRelationDocumentModel.aggregate().match({
      saleRep: { $in: saleReps },
    });
    if (skip >= 0 && limit > 0) {
      aggregate.skip(skip).limit(limit);
    }
    return await aggregate.exec();
  }

  async findOutletBySaleIds(salesRepIds: any[], skip = 0, limit = 1000) {
    return this._saleRepOutletRelationDocumentModel
      .find({
        saleRep: { $in: salesRepIds.map((sid) => new Types.ObjectId(sid)) },
      })
      .sort({ _id: 1 })
      .skip(skip)
      .limit(limit)
      .lean();
  }
  async getActiveSaleRepOutletRelations(saleRepId: string, query: string) {
    const normalizedQuery = normalizeQueryHelper(query);
    return this.aggregate({ 'u.saleRepStatus': SalesRepStatus.ACTIVE, 'o.status': OutletStatus.ACTIVE, 'sor.disconnected': false })
      .match({
        'u._id': new Types.ObjectId(saleRepId),
        'u.saleRepStatus': SalesRepStatus.ACTIVE,
        'o.status': OutletStatus.ACTIVE,
        'sor.disconnected': false,
        $or: [
          {
            'o.name': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
          {
            'o.ucc': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
        ],
      })
      .project({
        saleRepId: '$u._id',
        outletId: '$o._id',
      });
  }

  async getActiveSaleRepOutletRelationsV2(saleRepId: string, query: string) {
    const normalizedQuery = normalizeQueryHelper(query);
    const data = await this._saleRepOutletRelationDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        disconnected: false,
      })
      .populate('outlet');

    if (normalizedQuery) {
      const regex = new RegExp(`^.*${normalizedQuery}.*$`, 'i');
      return data?.filter((item) => {
        return regex.test(item.outlet?.name) || regex.test(item.outlet?.ucc);
      });
    }
    return data;
  }

  async filterActiveSaleRepOutletRelations(filter: Record<string, any>): Promise<Array<ISaleRepOutletAggregation>> {
    return this.aggregate({ 'u.saleRepStatus': SalesRepStatus.ACTIVE, 'o.status': OutletStatus.ACTIVE, 'sor.disconnected': false })
      .match({
        'u.saleRepStatus': SalesRepStatus.ACTIVE,
        'o.status': OutletStatus.ACTIVE,
        'sor.disconnected': false,
        ...filter,
      })
      .exec();
  }

  filterActiveOutlet(listSales: any, query: string, skip: number, limit: number) {
    const { listSalesRepIds, listSalesReps } = listSales;
    const matchCondition: any = {
      'o.status': OutletStatus.ACTIVE,
      'sor.disconnected': false,
    };

    if (listSalesRepIds?.length) {
      matchCondition['u.saleRepId'] = { $in: listSalesRepIds };
    }

    if (listSalesReps?.length) {
      matchCondition['sor.saleRep'] = { $in: listSalesReps };
    }

    const result = this.aggregate(matchCondition);
    if (query) {
      const normalizedQuery = normalizeQueryHelper(query);
      result.match({
        $or: [
          {
            'o.name': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
          {
            'o.ucc': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
        ],
      });
    }
    return result
      .project({
        ucc: '$o.ucc',
        outletId: '$o._id',
        outletName: '$o.name',
        saleRepId: '$u.saleRepId',
      })
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      });
  }

  aggregate(matchCondition: any = null) {
    const aggregate = this._saleRepOutletRelationDocumentModel.aggregate().project({
      _id: 0,
      sor: '$$ROOT',
    });
    if (matchCondition?.['sor.disconnected'] != undefined) {
      aggregate.match({ 'sor.disconnected': matchCondition?.['sor.disconnected'] });
    }
    if (!isEmptyObjectOrArray(matchCondition?.['sor.saleRep'])) {
      aggregate.match({ 'sor.disconnected': false, 'sor.saleRep': matchCondition?.['sor.saleRep'] });
    }

    aggregate
      .lookup({
        localField: 'sor.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .unwind({ path: '$u', preserveNullAndEmptyArrays: false });
    if (!isEmptyObjectOrArray(matchCondition?.['u.saleRepId'])) {
      aggregate.match({ 'u.saleRepId': matchCondition?.['u.saleRepId'] });
    }

    if (!isEmptyObjectOrArray(matchCondition?.['u.saleRepStatus'])) {
      aggregate.match({ 'u.saleRepStatus': matchCondition?.['u.saleRepStatus'] });
    }

    aggregate
      .lookup({
        localField: 'sor.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({ path: '$o', preserveNullAndEmptyArrays: false });
    if (!isEmptyObjectOrArray(matchCondition?.['o.status'])) {
      aggregate.match({ 'o.status': matchCondition?.['o.status'] });
    }

    return aggregate;
  }

  async getOutletSalesRepRelationsByIds(ids: string[]) {
    return this._saleRepOutletRelationDocumentModel
      .find({
        outlet: {
          $in: ids.map((id) => new Types.ObjectId(id)),
        },
        disconnected: false,
      })
      .populate('outlet');
  }

  async getAllOutletNoteBySalesrepObjectIds(salerepsObjectIds: Types.ObjectId[], search: string, skip?: number, limit?: number) {
    const aggregate = this._saleRepOutletRelationDocumentModel
      .aggregate()
      .match({
        note: { $nin: [null, ''] },
        saleRep: { $in: salerepsObjectIds },
        disconnected: false,
      })
      .lookup({
        localField: 'outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({ path: '$outlet', preserveNullAndEmptyArrays: false });

    aggregate
      .lookup({
        localField: 'saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'salesRepInfo',
      })
      .unwind({ path: '$salesRepInfo', preserveNullAndEmptyArrays: false });

    if (search) {
      search = search.trim();
      aggregate.match({
        $or: [
          {
            'outlet.name': new RegExp(`^.*${search}.*$`, 'i'),
          },
          {
            'outlet.ucc': new RegExp(`^.*${search}.*$`, 'i'),
          },
        ],
      });
    }
    aggregate
      .project({
        'outlet.ucc': 1,
        'outlet.name': 1,
        note: 1,
        noteUpdatedAt: 1,
        saleRep: 1,
        salesRepInfo: {
          _id: 1,
          username: 1,
          saleRepId: 1,
        },
      })
      .sort({
        'salesRepInfo.username': 1,
        noteUpdatedAt: -1,
      });

    if (limit) {
      aggregate.skip(skip).limit(limit);
    }

    aggregate
      .lookup({
        localField: 'saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'distributoruserrelations',
      })
      .unwind({ path: '$distributoruserrelations', preserveNullAndEmptyArrays: false })
      .lookup({
        localField: 'distributoruserrelations.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({ path: '$distributor', preserveNullAndEmptyArrays: false });

    return aggregate.exec();
  }

  /**
   *
   * @param saleRep
   * @param newRelations
   * @returns
   */
  async findOneUserRelationAndUpdateData(saleRep: any, outlets: Outlet[]) {
    await this._saleRepOutletRelationDocumentModel.updateMany(
      {
        saleRep: saleRep._id,
        outlet: { $ne: outlets.map((outlet) => outlet._id) },
      },
      {
        $set: {
          disconnected: true,
          lastActive: false,
        },
      },
    );
    for (const outlet of outlets) {
      await this._saleRepOutletRelationDocumentModel.updateOne(
        { saleRep: saleRep._id, outlet: outlet._id },
        {
          $set: {
            saleRep: saleRep._id,
            outlet: outlet._id,
            disconnected: false,
            lastActive: true,
          },
        },
        { upsert: true, new: true },
      );
    }
  }

  async getOutletSalesRepRelationsBySaleRepIds(salesRepObjectIds: any[]) {
    return this.model.aggregate([
      {
        $match: {
          saleRep: { $in: salesRepObjectIds },
          disconnected: false,
        },
      },
      {
        $sort: { _id: 1 },
      },
      {
        $group: {
          _id: '$saleRep',
          saleRep: { $first: '$saleRep' }, // Lấy giá trị saleRep thực tế
          outlet: { $first: '$outlet' },
        },
      },
    ]);
  }
}
