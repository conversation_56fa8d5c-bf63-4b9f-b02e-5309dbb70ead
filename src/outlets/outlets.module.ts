import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JourneyPlanningsModule } from 'src/journey-plannings/journey-plannings.module';

import { AuthModule } from '../auth/auth.module';
import { DistributorModule } from '../distributor/distributor.module';
import { ExternalModule } from '../external/external.module';
import { SaleRepModule } from '../sale-rep/sale-rep.module';
import { SettingsModule } from '../settings/settings.module';
import { UsersModule } from '../users/users.module';
import { OutletsController } from './outlets.controller';
import { Outlet, OutletSchema } from './schemas/outlet.schema';
import { SaleRepOutletRelationTracking, SaleRepOutletRelationTrackingSchema } from './schemas/sale-rep-outlet-relation-tracking.schema';
import { SaleRepOutletRelation, SaleRepOutletRelationSchema } from './schemas/sale-rep-outlet-relation.schema';
import { OutletsService } from './services/outlets.service';
import { SaleRepOutletRelationTrackingService } from './services/sale-rep-outlet-relation-tracking.service';
import { SaleRepOutletRelationService } from './services/sale-rep-outlet-relation.service';
import { OutletJourneyPlanning, OutletJourneyPlanningSchema } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { JourneyPlanMissedReasonHistory, JourneyPlanMissedReasonHistorySchema } from 'src/journey-plannings/schemas/journey-plan-absence.schema';
import { RequestScopeModule } from 'nj-request-scope';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Outlet.name, schema: OutletSchema },
      {
        name: SaleRepOutletRelation.name,
        schema: SaleRepOutletRelationSchema,
      },
      {
        name: SaleRepOutletRelationTracking.name,
        schema: SaleRepOutletRelationTrackingSchema,
      },
      {
        name: OutletJourneyPlanning.name,
        schema: OutletJourneyPlanningSchema,
      },
      {
        name: JourneyPlanMissedReasonHistory.name,
        schema: JourneyPlanMissedReasonHistorySchema,
      },
    ]),
    AuthModule,
    SaleRepModule,
    SettingsModule,
    DistributorModule,
    forwardRef(() => ExternalModule),
    forwardRef(() => UsersModule),
    forwardRef(() => JourneyPlanningsModule),
    RequestScopeModule,
  ],
  controllers: [OutletsController],
  providers: [OutletsService, SaleRepOutletRelationService, SaleRepOutletRelationTrackingService],
  exports: [OutletsService, SaleRepOutletRelationService, SaleRepOutletRelationTrackingService],
})
export class OutletsModule {}
