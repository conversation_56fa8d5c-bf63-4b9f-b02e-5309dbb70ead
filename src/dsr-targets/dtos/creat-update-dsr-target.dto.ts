import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsString, Max, Min } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { OpCos } from 'src/config';

export class CreatUpdateDsrTargetDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  saleRepUUID: string;

  @ApiProperty()
  @IsNumber()
  @Min(0, { message: 'dsrTargets.sales_target.min' })
  salesTarget: number;

  @ApiProperty()
  @IsNumber()
  @Min(0, { message: `dsrTargets.mabo_target.min${process.env.OPCO == OpCos.Indonesia ? '_ID' : ''}` })
  maboTarget: number;

  @ApiProperty()
  @IsNumber()
  @Min(1, { message: 'dsrTargets.month.min' })
  @Max(12, { message: 'dsrTargets.month.max' })
  month: number;

  @ApiProperty()
  @IsNumber()
  @Min(new Date().getFullYear(), { message: 'dsrTargets.year.min' })
  year: number;
}
