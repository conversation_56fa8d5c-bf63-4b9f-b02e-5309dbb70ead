import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNotEmpty, IsNumber, IsString, Max, Min } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { OpCos } from 'src/config';

export class CreatUpdateDsrTargetDto {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  saleRepUUID: string;

  @ApiModelProperty()
  @IsNumber()
  @Min(0, { message: 'dsrTargets.sales_target.min' })
  salesTarget: number;

  @ApiModelProperty()
  @IsNumber()
  @Min(0, { message: `dsrTargets.mabo_target.min${process.env.OPCO == OpCos.Indonesia ? '_ID' : ''}` })
  maboTarget: number;

  @ApiModelProperty()
  @IsNumber()
  @Min(1, { message: 'dsrTargets.month.min' })
  @Max(12, { message: 'dsrTargets.month.max' })
  month: number;

  @ApiModelProperty()
  @IsNumber()
  @Min(new Date().getFullYear(), { message: 'dsrTargets.year.min' })
  year: number;
}
