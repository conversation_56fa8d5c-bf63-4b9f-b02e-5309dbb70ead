import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { ApiModelProperty, ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { SortOrder } from 'mongoose';

export class DSRTargetSortOrder extends SortOrderDto {
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  name: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  maboTarget: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  salesTarget: SortOrder;
}

export class FetchDsrTargetsDto extends PaginationDto {
  @ApiModelProperty()
  @IsNumber()
  @Min(1, { message: 'dsrTargets.month.min' })
  @Max(12, { message: 'dsrTargets.month.max' })
  month: number;

  @ApiModelProperty()
  @IsNumber()
  year: number;

  @ApiModelPropertyOptional({ default: '' })
  @IsString()
  @IsOptional()
  distributorId: string;

  @ApiModelProperty({ type: () => DSRTargetSortOrder })
  sort: DSRTargetSortOrder;
}
