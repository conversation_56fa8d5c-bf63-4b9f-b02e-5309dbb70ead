import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { SortOrder } from 'mongoose';

export class DSRTargetSortOrder extends SortOrderDto {
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  name: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  maboTarget: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  salesTarget: SortOrder;
}

export class FetchDsrTargetsDto extends PaginationDto {
  @ApiProperty()
  @IsNumber()
  @Min(1, { message: 'dsrTargets.month.min' })
  @Max(12, { message: 'dsrTargets.month.max' })
  month: number;

  @ApiProperty()
  @IsNumber()
  year: number;

  @ApiPropertyOptional({ default: '' })
  @IsString()
  @IsOptional()
  distributorId: string;

  @ApiProperty({ type: () => DSRTargetSortOrder })
  sort: DSRTargetSortOrder;
}
