import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNotEmpty, IsNumber, IsString, Max, Min } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

export class FetchDsrTargetsDetailDto {
  @ApiModelProperty()
  @IsNumber()
  @Min(1, { message: 'dsrTargets.month.min' })
  @Max(12, { message: 'dsrTargets.month.max' })
  month: number;

  @ApiModelProperty()
  @IsNumber()
  year: number;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  saleRepUUID: string;
}
