import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsString, Max, Min } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

export class FetchDsrTargetsDetailDto {
  @ApiProperty()
  @IsNumber()
  @Min(1, { message: 'dsrTargets.month.min' })
  @Max(12, { message: 'dsrTargets.month.max' })
  month: number;

  @ApiProperty()
  @IsNumber()
  year: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  saleRepUUID: string;
}
