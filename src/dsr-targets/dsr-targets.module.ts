import { forwardRef, Module } from '@nestjs/common';
import { DsrTargetsController } from './dsr-targets.controller';
import { MongooseModule } from '@nestjs/mongoose';

import { DSRTarget, DSRTargetSchema } from './schemas';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';
import { DsrTargetService } from './services';
import { DistributorModule } from '../distributor/distributor.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: DSRTarget.name,
        schema: DSRTargetSchema,
      },
    ]),
    AuthModule,
    forwardRef(() => DistributorModule),
    // DistributorModule,
    forwardRef(() => UsersModule),
  ],
  controllers: [DsrTargetsController],
  providers: [DsrTargetService],
  exports: [DsrTargetService],
})
export class DsrTargetsModule {}
