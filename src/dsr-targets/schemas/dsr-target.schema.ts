import { Document, Types } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';

export type DSRTargetDocument = DSRTarget & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class DSRTarget extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  // Number
  // Sales Value
  @Prop({ default: 0 })
  salesTarget: number;

  // Percent
  // Active Selling Outlet
  @Prop({ default: 0 })
  maboTarget: number;

  // Percent
  @Prop({ default: 0 })
  callComplianceRate: number;

  // Percent
  @Prop({ default: 0 })
  callEffectiveness: number;

  // Number
  @Prop({ default: 0 })
  salesVolume: number;

  // Percent
  @Prop({ default: 0 })
  availability: number;

  // Percent
  @Prop({ default: 0 })
  visibility: number;

  @Prop({ index: true })
  month: number;

  @Prop({ index: true })
  year: number;
}

export const DSRTargetSchema = SchemaFactory.createForClass(DSRTarget);
