import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { DistributorUserRelationService } from '../../distributor/services';
import { BaseService } from '../../shared/services/base-service';
import { SalesRepStatus } from '../../users/enums';
import { IMonthYear } from '../interfaces';
import { DSRTarget, DSRTargetDocument } from '../schemas';
import { isEmptyObjectOrArray } from '../../utils';

@Injectable()
export class DsrTargetService extends BaseService<DSRTarget> {
  constructor(
    @InjectModel(DSRTarget.name)
    private readonly _model: Model<DSRTargetDocument>,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
  ) {
    super();
    this.model = _model;
  }

  async getDSRTargets(distributorId: string, month: number, year: number, skip: number, limit: number, sort: Record<string, any> = { name: 1 }) {
    return this.createDSRTargetsAggregation(month, year)
      .match({ saleRepId: { $ne: null }, saleRepStatus: SalesRepStatus.ACTIVE, distributorId, month: month, year: year })
      .sort(sort)
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      })
      .exec();
  }

  async getCurrentDSRTargetBySaleRepId(saleRepId: string): Promise<DSRTarget> {
    const currentTime = moment().tz(process.env.TZ);
    const currentMonth = currentTime.get('month') + 1;
    const currentYear = currentTime.get('year');
    return this._model
      .findOne({
        saleRep: new Types.ObjectId(saleRepId),
        month: currentMonth,
        year: currentYear,
      })
      .exec();
  }

  getDiffMonth(month: number, year: number) {
    const validatedMonthYear = new Date(year, month - 1, 15).toISOString();
    return moment(validatedMonthYear).tz(process.env.TZ).endOf('month').diff(moment().tz(process.env.TZ).endOf('month'), 'months');
  }

  createDSRTargetsAggregation(month: number, year: number) {
    return this._distributorUserRelationService
      .createDistributorUserRelationAggregation()
      .lookup({
        localField: 'dur.user',
        from: 'dsrtargets',
        foreignField: 'saleRep',
        as: 'dt',
      })
      .unwind({
        path: '$dt',
        preserveNullAndEmptyArrays: true,
      })
      .project({
        _id: '$u._id',
        name: '$u.username',
        salesTarget: { $ifNull: ['$dt.salesTarget', 0] },
        maboTarget: { $ifNull: ['$dt.maboTarget', 0] },
        month: { $ifNull: ['$dt.month', month] },
        year: { $ifNull: ['$dt.year', year] },
        saleRepId: '$u.saleRepId',
        saleRepStatus: '$u.saleRepStatus',
        distributorId: '$dis.distributorId',
        distributorName: '$dis.distributorName',
      });
  }

  getListMonthYear(): Array<IMonthYear> {
    const currentMonth = moment().tz(process.env.TZ).endOf('month');
    const lastMonth = currentMonth.clone().subtract(1, 'month');
    const last2Months = currentMonth.clone().subtract(2, 'month');
    const last3Months = currentMonth.clone().subtract(3, 'month');
    const nextMonth = currentMonth.clone().add(1, 'month');
    const next2Months = currentMonth.clone().add(2, 'month');
    const next3Months = currentMonth.clone().add(3, 'month');
    return [
      { month: last3Months.get('month') + 1, year: last3Months.get('year') },
      { month: last2Months.get('month') + 1, year: last2Months.get('year') },
      { month: lastMonth.get('month') + 1, year: lastMonth.get('year') },
      { month: currentMonth.get('month') + 1, year: currentMonth.get('year') },
      { month: nextMonth.get('month') + 1, year: nextMonth.get('year') },
      { month: next2Months.get('month') + 1, year: next2Months.get('year') },
      { month: next3Months.get('month') + 1, year: next3Months.get('year') },
    ];
  }

  async createTargetForSaleRep(saleRepObjectIds: string[] = null) {
    if (isEmptyObjectOrArray(saleRepObjectIds)) return [];

    const currentDate = moment().tz(process.env.TZ);
    const currentMonth = currentDate.month() + 1;
    const currentYear = currentDate.year();
    const targets = await this._model.find({
      saleRep: {
        $in: saleRepObjectIds.map((item) => new Types.ObjectId(item)),
      },
      month: currentMonth,
      year: currentYear,
    });
    const notExistTargetSalesReps = !isEmptyObjectOrArray(targets)
      ? saleRepObjectIds.filter((id) => !targets.map((item) => item.saleRep.toString()).includes(id.toString()))
      : saleRepObjectIds;
    if (isEmptyObjectOrArray(notExistTargetSalesReps)) return [];
    const newTargets = await Promise.all(
      notExistTargetSalesReps.map(async (item) => {
        return this._model.create({
          saleRep: new Types.ObjectId(item),
          month: currentMonth,
          year: currentYear,
          salesTarget: 0,
          maboTarget: 0,
        });
      }),
    );
    return [...targets, ...newTargets];
  }
}
