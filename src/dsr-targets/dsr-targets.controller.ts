import { BadRequestException, Body, Controller, HttpCode, HttpStatus, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { DistributorService, DistributorUserRelationService } from '../distributor/services';
import { ApiException } from '../shared/api-exception.model';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { Roles } from '../shared/decorator/roles.decorator';
import { RolesGuard } from '../shared/guards/roles.guard';
import { ApiResponse } from '../shared/response/api-response';
import { SalesRepStatus } from '../users/enums';
import { User } from '../users/schemas/user.schema';
import { toListResponse } from '../utils';
import { ConstantRoles } from '../utils/constants/role';
import { CreatUpdateDsrTargetDto, FetchDsrTargetsDetailDto } from './dtos';
import { FetchDsrTargetsDto } from './dtos/fetch-dsr-targets.dto';
import { DsrTargetService } from './services';

@ApiTags('DSR Targets')
@Controller('api/dsr-targets')
@ApiHeader({ name: 'locale', description: 'en' })
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class DsrTargetsController {
  constructor(
    private readonly _dsrTargetService: DsrTargetService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _distributorService: DistributorService,
  ) {}

  @Post('list')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get DSR Target List',
  })
  async getDSRTargets(@Body() fetchDSRTargets: FetchDsrTargetsDto, @CurrentUser() currentUser: any) {
    const { skip, limit, month, year, distributorId, sort } = fetchDSRTargets;

    const diffMonths = this._dsrTargetService.getDiffMonth(month, year);
    if (Math.abs(diffMonths) > 3) {
      throw new BadRequestException('dsrTargets.month.invalid');
    }

    let validDistributorId = null;
    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (distributor) {
        validDistributorId = distributor.distributor.distributorId;
      }
    } else {
      const existedDistributor = await this._distributorService.findOne({ distributorId });
      if (!existedDistributor) {
        throw new BadRequestException('dsrTargets.not_found_distributor');
      }
      validDistributorId = distributorId;
    }

    let result = [{ totalRecords: [{ total: 0 }], data: null }];
    if (validDistributorId) {
      result = await this._dsrTargetService.getDSRTargets(validDistributorId, month, year, skip, limit, sort);
    }

    const [{ totalRecords, data }] = result;
    return new ApiResponse(toListResponse([data, totalRecords?.[0]?.total ?? 0]));
  }

  @Post('detail')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get DSR Target Detail',
  })
  async getDSRTargetsDetail(@Body() fetchDsrTargetsDetailDto: FetchDsrTargetsDetailDto, @CurrentUser() currentUser: any) {
    const { month, year, saleRepUUID } = fetchDsrTargetsDetailDto;
    const diffMonths = this._dsrTargetService.getDiffMonth(month, year);
    if (Math.abs(diffMonths) > 3) {
      throw new BadRequestException('dsrTargets.month.invalid');
    }

    const distributorSaleRep = await this._distributorUserRelationService.findByUserId(saleRepUUID?.trim());
    if (!distributorSaleRep || distributorSaleRep.user.saleRepStatus !== SalesRepStatus.ACTIVE) {
      throw new BadRequestException('dsrTargets.not_found_sale_rep');
    }

    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorSaleRep.distributor.distributorId);
      if (!distributor) {
        throw new BadRequestException('dsrTargets.distributor_not_match');
      }
    }

    const dsrTarget = await this._dsrTargetService.findOne({ saleRep: distributorSaleRep.user._id, month, year });
    if (dsrTarget) {
      return new ApiResponse({
        _id: saleRepUUID?.trim(),
        name: distributorSaleRep.user.username,
        salesTarget: dsrTarget.salesTarget,
        maboTarget: dsrTarget.maboTarget,
        month: month,
        year: year,
        saleRepId: distributorSaleRep.user.saleRepId,
        distributorId: distributorSaleRep.distributor.distributorId,
        distributorName: distributorSaleRep.distributor.distributorName,
      });
    } else {
      return new ApiResponse({
        _id: saleRepUUID?.trim(),
        name: distributorSaleRep.user.username,
        salesTarget: 0,
        maboTarget: 0,
        month: month,
        year: year,
        saleRepId: distributorSaleRep.user.saleRepId,
        distributorId: distributorSaleRep.distributor.distributorId,
        distributorName: distributorSaleRep.distributor.distributorName,
      });
    }
  }

  @Put('update')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiOperation({
    summary: 'Update DSR Target',
  })
  async updateDSRTarget(@Body() creatUpdateDsrTargetDto: CreatUpdateDsrTargetDto, @CurrentUser() currentUser: User) {
    const { month, year, saleRepUUID, salesTarget, maboTarget } = creatUpdateDsrTargetDto;
    const diffMonths = this._dsrTargetService.getDiffMonth(month, year);
    if (diffMonths < 0) {
      throw new BadRequestException('dsrTargets.month.invalid_update');
    }
    const distributorSaleRep = await this._distributorUserRelationService.findByUserId(saleRepUUID?.trim());
    if (!distributorSaleRep) {
      throw new BadRequestException('dsrTargets.not_found_sale_rep');
    }
    const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
    const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorSaleRep.distributor.distributorId);
    if (!distributor) {
      throw new BadRequestException('dsrTargets.distributor_not_match');
    }

    const dsrTarget = await this._dsrTargetService.findOne({ saleRep: distributorSaleRep.user._id, month, year });
    if (dsrTarget) {
      await this._dsrTargetService.update(dsrTarget._id, { salesTarget, maboTarget });
    } else {
      await this._dsrTargetService.create({
        saleRep: distributorSaleRep.user._id,
        salesTarget,
        maboTarget,
        month,
        year,
      });
    }
    const listMonthYear = this._dsrTargetService.getListMonthYear();
    for (const my of listMonthYear) {
      if (!(my.month === month && my.year === year)) {
        const existed = await this._dsrTargetService.findOne({
          saleRep: distributorSaleRep.user._id,
          month: my.month,
          year: my.year,
        });
        if (!existed) {
          await this._dsrTargetService.create({
            saleRep: distributorSaleRep.user._id,
            month: my.month,
            year: my.year,
          });
        }
      }
    }
    return new ApiResponse({
      _id: saleRepUUID,
      name: distributorSaleRep.user.username,
      salesTarget,
      maboTarget,
      month,
      year,
      saleRepId: distributorSaleRep.user.saleRepId,
      distributorId: distributorSaleRep.distributor.distributorId,
      distributorName: distributorSaleRep.distributor.distributorName,
    });
  }
}
