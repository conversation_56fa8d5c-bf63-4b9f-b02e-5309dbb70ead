export const ConstantUser = {
  BLOCKED: 0,
  STEP_REGISTRATION_INFO: 2,
  RESET_PASSWORD: 3,
  STEP_VERIFIED_SMS_CODE: 4,
  NEED_TO_SET_PASSWORD: 5,
  IS_ACTIVE: 6,
  RESEND_OTP: 7,
  CODE_EXPIRED_SECONDS: 120,
  ACTION_TYPE_REGISTER: 'TYPE_REGISTER',
  ACTION_TYPE_FORGOT_PASSWORD: 'TYPE_FORGOT_PASSWORD',
  REFRESH_TOKEN_LENGTH: 64,
  ADMIN_EXTERNAL_USERS: [
    {
      defaultLanguage: 'en-MY',
      firstname: 'Dot',
      isActive: true,
      isSmsSend: false,
      lastname: 'System',
      password: 'DSR@salerep.2022',
      roleId: 1,
      saleRepStatus: 'ACTIVE',
      username: 'dot_system',
    },
  ],
  ADMIN_EMAILS: ['<EMAIL>'],
  ADMIN_PHONE_NUMBERS: [
    '0369666666',
    '0369888888',
    '0369555555',
    '0946532094',
    '0780000009',
    '0789364668',
    '0902999999',
    '+84902999999',
    '+60902999999',
    '+62902999999',
    '+65902999999',
    '+855902999999',
    '+95902999999',
  ],
  REVIEW_PHONE_NUMBER: ['01128392182', '+601128392182', '+621128392182', '+651128392182', '+841128392182', '+84981143497', '+8551128392182', '+95902999999', '+951128392182'],
  REVIEW_PHONE_NUMBER_OTP: '012210',
  DEFAULT_USER_CREATED: 'DEFAULT_USER_CREATED',
  DEFAULT_USER_ADMIN_CREATED: 'DEFAULT_USER_ADMIN_CREATED',
  DEFAULT_PASSWORD: ['REPApp@2025!'],
  SECRET_ADMIN_KEY: 'DSR_HEINEKEN_2023',
};

export const ALL_ORDER_STATUS = 'all';
export const ORDER_BY_ROLE_ID = 'roleId';
export const MAX_ATTEMPTS = 5;
export const LOCK_DURATION_MINUTES = 15;
