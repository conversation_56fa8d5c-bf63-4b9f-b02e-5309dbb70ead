export const ConstantCommons = {
  OFFSET_DEFAULT: 0,
  LIMIT_DEFAULT: 20,
  ORDER_BY_DEFAULT: 'updatedAt',
  ORDER_DESC_DEFAULT: 'DESC',
  ORDER_ASC_DEFAULT: 'ASC',
  IP_LOCALHOST: '127.0.0.1',
  DOMAIN_LOCALHOST: 'localhost',
  ISO_DATE_PATTERN:
    /^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24\:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/,
  VIETNAM_PHONE_NUMBER: /((^(0){1})(3|5|7|8|9))+([0-9]{8})$/,
  WEBP: '.webp',
  TIME_PATTERN: /^([0-1][0-9]|2[0-3]):([0-5][0-9])$/,
  AFFORDABILITY_MAX_VALUE: 10000000,
  GET_DESTINATION_ROUTER: 'get-destination-router',
  OMS_BRAND: 'oms_brands',
  MUST_HAVE_SKU: 'must_have_sku',
  OMS_ORDER_RECOMMENDATION: 'oms_order_recommendation',
  OMS_ORDER_AVERAGE_QUANTITY: 'oms_order_average_quantity',
  OMS_PERFORMANCE_OUTLET_SALE_VOLUME: 'oms_performance_outlet_sale_volume',
  MUST_HAVE_SKU_LABEL: 'Must Have SKUs',
  OPTIONAL_SKU_LABEL: 'Optional SKUs',
  HIGH_STOCK: 'high_stock',
  MEDIUM_STOCK: 'medium_stock',
  GOOGLE_SESSION_TOKEN: 'google_session_token',
  GOOGLE_GET_AUTO_COMPLETE: 'get_auto_complete',
  GOOGLE_GET_GEOCODE_BY_ADDRESS: 'get_geocode_by_address',
  GOOGLE_GET_GEOCODE_BY_LAT_LNG: 'get_geocode_by_lat_lng',
  GOOGLE_GET_GEOCODE_TIME_DELAY: 200,
  ALL: 'ALL',
  COMPLETED: 'COMPLETED',
  MISSED: 'MISSED',
  CONFIG_EXEL: 'config_excel',
};

export enum ENUM_HELPER_DATE_FORMAT {
  DATE = 'YYYY-MM-DD',
  FRIENDLY_DATE = 'MMM, DD YYYY',
  FRIENDLY_DATE_TIME = 'MMM, DD YYYY HH:MM:SS',
  YEAR_MONTH = 'YYYY-MM',
  MONTH_DATE = 'MM-DD',
  ONLY_YEAR = 'YYYY',
  ONLY_MONTH = 'MM',
  ONLY_DATE = 'DD',
  ISO_DATE = 'YYYY-MM-DDTHH:MM:SSZ',
}

export enum ENUM_HELPER_DATE_DIFF {
  MILIS = 'milis',
  SECONDS = 'seconds',
  HOURS = 'hours',
  DAYS = 'days',
  MINUTES = 'minutes',
}

export enum APP_SETTINGS {
  APP_CONFIG = 'APP_CONFIG',
  APP_VERSION = 'APP_VERSION',
}
export enum DOT_SETTINGS {
  DOT_OPCO_MY_STORE_SETTING = 'DOT_OPCO_MY_STORE_SETTING',
  BLANK_DATA = 'BLANK_DATA',
}

export enum LIMIT_STATISTIC_SALE {
  LIMIT = 500,
  COVER_SKIP = 30000,
}

export enum STATUS_SALE_STATISTIC {
  CONFIRMED = 'Confirmed',
  CANCELLED = 'Cancelled',
  NULL_VALUE = 'NULL',
}

export enum BRAND_TYPES {
  QT = 'Qt',
  PT = 'Pt',
  CAN = 'Can',
  BCAN = 'Bcan',
  KEG = 'Keg',
}
