import { ENUM_HELPER_DATE_DIFF, ENUM_HELPER_DATE_FORMAT } from '../constants';

export interface IHelperJwtOptions {
  expiredIn: string;
  notBefore?: string;
  secretKey: string;
}

export interface IHelperStringRandomOptions {
  upperCase?: boolean;
  safe?: boolean;
  prefix?: string;
}

export interface IHelperGeoCurrent {
  latitude: number;
  longitude: number;
}

export interface IHelperGeoRules extends IHelperGeoCurrent {
  radiusInMeters: number;
}

export interface IHelperDateOptions {
  timezone?: string;
}

export interface IHelperDateOptionsDiff extends IHelperDateOptions {
  format?: ENUM_HELPER_DATE_DIFF;
}

export interface IHelperDateOptionsCreate extends IHelperDateOptions {
  date?: string | number | Date;
}

export interface IHelperDateOptionsFormat extends IHelperDateOptions {
  format?: ENUM_HELPER_DATE_FORMAT | string;
}

export interface IHelperDateOptionsForward extends IHelperDateOptions {
  fromDate?: Date;
}

export type IHelperDateOptionsBackward = IHelperDateOptionsForward;

export interface IHelperDateOptionsMonth extends IHelperDateOptions {
  year?: number;
}

export interface JwtPayloadInterface {
  mobilePhone: string;
  iat?: Date;
  exp?: Date;
}

export interface PaginationResponse<T> {
  data: T[];
  totalItem: number;
}

export interface Reason {
  reason: {
    [key: string]: string;
  };
}

export interface UploadDataResponse {
  failure: Reason[];
  completed: boolean;
}
