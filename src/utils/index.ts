import { I18nContext } from 'nestjs-i18n';
import { HttpException, HttpStatus } from '@nestjs/common';
import { VisitStatus } from './../journey-plannings/enums/visit-status.enum';
import * as moment from 'moment-timezone';
import { ConstantRoles } from './constants/role';
import { isValidPhoneNumber, parsePhoneNumberWithError } from 'libphonenumber-js';
import { genSalt, hash } from 'bcryptjs';
import * as _ from 'lodash';
import { ConstantCaches } from './constants/cache';
import { CONVERT_ML_TO_HL } from '../external/constants';
import { customAlphabet } from 'nanoid';
import { ConstantUser } from './constants/user';
import { OpCos } from '../config';

export const isPhoneNumberValidation = (number: string, phoneCountryCode?: string) => {
  try {
    if (number[0] === '+' && phoneCountryCode[0] === '+') {
      number = number.replace(phoneCountryCode, '');
    }
    if (phoneCountryCode) {
      phoneCountryCode = phoneCountryCode.replace('+', '');
    }
    if (number[0] === '+') {
      return false;
    }

    if (number[0] !== '0' && number[0] !== '+') {
      number = `${phoneCountryCode || process.env.PHONE_COUNTRY_CODE_DEFAULT}${number}`;
    }

    if (number[0] === '0') {
      number = number.replace('0', phoneCountryCode || process.env.PHONE_COUNTRY_CODE_DEFAULT);
    }

    if (number[0] !== '+') {
      number = `+${number}`;
    }

    const codes = process.env.PHONE_COUNTRY_CODES?.split(',');
    let valid = false;
    for (const code of codes) {
      const _code = code.split('|')[0]?.toUpperCase();
      const numberParse = parsePhoneNumberWithError(number);
      valid = ConstantUser.REVIEW_PHONE_NUMBER.indexOf(number) > -1 || (numberParse.country === _code && isValidPhoneNumber(number, _code));
      if (valid) break;
    }
    return valid;
  } catch (e) {
    return false;
  }
};

export const standardPhoneNumber = (number: string, phoneCountryCode?: string) => {
  number = number?.trim();
  if (!number) {
    return number;
  }

  if (phoneCountryCode) {
    phoneCountryCode = phoneCountryCode.replace('+', '');
  }

  if (number[0] !== '0' && number[0] !== '+') {
    number = `${phoneCountryCode || process.env.PHONE_COUNTRY_CODE_DEFAULT}${number}`;
  }

  if (number[0] === '0') {
    number = number.replace('0', phoneCountryCode || process.env.PHONE_COUNTRY_CODE_DEFAULT);
  }

  if (number[0] !== '+') {
    number = `+${number}`;
  }
  return number;
};

export const convertMYPhoneFirstNumber = (number: string) => {
  if (!number) {
    return number;
  }

  if (number.indexOf('0') === 0) {
    return number?.trim();
  }

  number = number.replace('+', '')?.trim();
  return number.replace('84', '0')?.trim();
};

/**
 ^ The password string will start this way
 (?=.*[a-z])    The string must contain at least 1 lowercase alphabetical character
 (?=.*[A-Z])    The string must contain at least 1 uppercase alphabetical character
 (?=.*[0-9])    The string must contain at least 1 numeric character
 (?=.*[!@#$%^&*])    The string must contain at least one special character, but we are escaping reserved RegEx characters to avoid conflict
 (?=.{8,})    The string must be eight characters or longer
 * @param password
 */
/*export const isStrengthPassword = (password: string) => {
  return /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})$/.test(
    password,
  );
};*/

/**
 ^ The password string will start this way
 (?=.*\d)        should contain at least one digit
 (?=.*[A-Z])     should contain at least one upper case
 [a-zA-Z0-9]{8,} should contain at least 8 from the mentioned characters
 */
export const isValidPassword = (password: string) => {
  return /^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])[0-9a-zA-Z!@#$%^&*]{8,}$/.test(password);
};

export const isValidEmail = (email: string) => {
  return /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(email);
};

export const getRandomCode = (start = 100000) => {
  return Math.floor(start + Math.random() * 900000).toString();
};

export function isEmptyObject(objects: any) {
  if (!objects) {
    return true;
  }
  return !Object.keys(objects).length;
}

export function isEmptyArray(objects: any) {
  if (!objects) {
    return true;
  }
  return !objects.length;
}

export function isEmptyObjectOrArray(objects: any) {
  if (!objects) {
    return true;
  }
  if (Array.isArray(objects)) {
    return isEmptyArray(objects);
  }
  return isEmptyObject(objects);
}

/**
 *
 * @param objects: {
    startDate,
    endDate
  }
 */
export function formatDateRange(objects: any) {
  const startTime = moment(objects.startDate);
  let endTime = moment();
  if (objects.endDate) {
    endTime = moment(objects.endDate).add(23, 'h').add(59, 'minutes').add(59, 'seconds');
  }

  if (!startTime.isBefore(endTime)) {
    return null;
  }

  return {
    startTime: startTime.format('YYYY-MM-DD'),
    endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
  };
}

export const initialDate = () => {
  return {
    startTime: moment(new Date()).subtract(3, 'months').add(0, 'h').add(0, 'minutes').add(0, 'seconds').format('YYYY-MM-DD HH:mm:ss'),
    endTime: moment().add(23, 'h').add(59, 'minutes').add(59, 'seconds').format('YYYY-MM-DD HH:mm:ss'),
  };
};

export function consumerSMS(message: string, smsCode: string, baseUrl: string) {
  //return message + ` \n@` + process.env.CONSUMER_BASE_URL.replace(/(https?:\/\/)?(www.)?/i, '') + ' #' + smsCode + ' , HVB';
  return `${message} \n @muangay-vn #${smsCode} , HVB`;
}

export function toListResponse(objects: any) {
  let results = {
    totalItem: 0,
    data: [],
  };
  if (!isEmptyObjectOrArray(objects[0])) {
    results = {
      totalItem: objects[1],
      data: objects[0],
    };
  }
  return results;
}

export function sleep(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}

/**
 * Print the conssole log in debug mode
 * @param message
 */
export const printLog = (...message) => {
  if (process.env.DEBUG_MODE === 'true') {
    console.log(...message);
  }
};

export const isDebugMode = () => {
  return process.env.DEBUG_MODE?.toLowerCase() === 'true' && process.env.NODE_ENV === 'local';
};

export const isProductionEnv = () => {
  return process.env.NODE_ENV?.toLowerCase() === 'production' || process.env.NODE_ENV?.toLowerCase() === 'prod';
};

export const getEnvironmentString = (): string | undefined => {
  const PRODUCTION_ENVS = new Set(['production', 'prod']);
  const nodeEnv = process.env.NODE_ENV?.toLowerCase();
  return PRODUCTION_ENVS.has(nodeEnv || '') ? '' : `${nodeEnv}_${process.env.OPCO}`;
};

export const dotProductionEnv = (key: string) => {
  if (!isProductionEnv()) {
    return {
      [key]: process.env[key],
    };
  }
  return {
    DOT_API_BASE_URL: process.env.DOT_API_BASE_URL,
    DOT_API_SEARCH_ORDER: process.env.DOT_API_SEARCH_ORDER,
    DOT_API_SEARCH_ORDER_KEY: process.env.DOT_API_SEARCH_ORDER_KEY,
  };
};

export const isValidExternalOutletID = (id: string) => {
  return /^[0-9]{8}$/.test(id);
};

export const uniqueArray = async (arr: any) => {
  if (!isEmptyArray(arr)) {
    return arr?.filter((v, i, a) => a.indexOf(v) === i);
  }
  return arr;
};

export const getRandomNumberBetween = (min = 1000, max = 3500): number => {
  return Math.floor(Math.random() * (max - min + 1) + min);
};

const objectsEqual = (o1, o2) =>
  typeof o1 === 'object' && Object.keys(o1).length > 0 ? Object.keys(o1).length === Object.keys(o2).length && Object.keys(o1).every((p) => objectsEqual(o1[p], o2[p])) : o1 === o2;

export const secureFileOrFolder = (file: string = null) => {
  if (!file) {
    return false;
  }
  return !['./', './public/', './public', '/public', 'public', './public/images', './public/images/'].includes(file);
};

export const fullAddress = (str, dataLocation) => {
  const subStr = str.split(', ');
  return subStr.reduce((preValue, currentValue, index) => (preValue += dataLocation[currentValue] + (index !== subStr.length - 1 ? ', ' : '')), '');
};

export const getRoleUser = async (userData: any = null, userRoleService, roleService) => {
  let roles: any = [];
  const userRoles = await userRoleService.findOne({ userId: userData._id });
  if (!isEmptyObjectOrArray(userRoles)) {
    roles = ((await roleService.findAll({ roleKey: { $in: userRoles?.roleKey } })) as any)?.map((r) => r.roleKey);
  } else {
    roles.push(ConstantRoles.SALE_REP);
  }
  return roles;
};

export const generateOutletKey = (outlet) => {
  if (!outlet) {
    return null;
  }

  if (!outlet?.ucc || !outlet?.outletDotId) {
    return `${outlet?._id}_${outlet?.ucc}`;
  }
  const outletDataKey = `${outlet?._id}_${outlet?.ucc}_${outlet?.outletDotId}`;
  return outletDataKey;
};

export const convertKeyRoles = (key: string) => {
  return key?.toString()?.trim()?.split(' ')?.join('_')?.toLocaleUpperCase();
};

export const passwordGenerate = async (password: string) => {
  const salt = await genSalt(10);
  return await hash(password, salt);
};

export const isValidUrl = (urlString) => {
  const urlPattern = new RegExp(
    '^(https?:\\/\\/)?' + // validate protocol
      '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // validate domain name
      '((\\d{1,3}\\.){3}\\d{1,3}))' + // validate OR ip (v4) address
      '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // validate port and path
      '(\\?[;&a-z\\d%_.~+=-]*)?' + // validate query string
      '(\\#[-a-z\\d_]*)?$',
    'i',
  ); // validate fragment locator
  return !!urlPattern.test(urlString);
};

export const securePhoneNumber = (phone: string) => {
  return phone?.replace(phone.substring(3, 7), '****');
};

export const formatCurrentDay = (displayDay: string | Date, startWeek: string | Date) => {
  return moment(displayDay).tz(process.env.TZ).endOf('d').diff(moment(startWeek).tz(process.env.TZ).endOf('d'), 'd') + 1;
};

export const isEditJourneyPlanning = (displayDay: string | Date, visitedStatus: string, startTimeWeek: string | Date, isEdited: boolean) => {
  if (moment(displayDay).isSame(new Date()) && visitedStatus === VisitStatus.IN_PROGRESS) return false;
  if (moment(displayDay).isBefore(startTimeWeek)) return false;
  return isEdited;
};

export const canEdit = (displayDay: string | Date, visitedStatus: string, startOfCycleTime: string | Date, endOfCycleTime: string | Date, isCancelPlan: boolean) => {
  if (moment().isBefore(moment(startOfCycleTime))) {
    return true;
  }
  if (moment().isAfter(endOfCycleTime)) {
    return false;
  }
  if (isCancelPlan) {
    return false;
  }
  if (moment(displayDay).tz(process.env.TZ).isSame(moment().tz(process.env.TZ), 'day') && visitedStatus === VisitStatus.IN_PROGRESS) {
    return false;
  }
  return visitedStatus !== VisitStatus.COMPLETED;
};

export const validateFields = async (fields: any, message: string, i18n: I18nContext) => {
  for (const field in fields) {
    if (!fields[field] || ((_.isArray(fields[field]) || _.isObject(fields[field])) && isEmptyObjectOrArray(fields[field]))) {
      throw new HttpException(
        await i18n.translate(message, {
          args: { fieldName: field },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
};

export const splitTime = (startTime: any, endTime: any, duration = 30) => {
  const returnArray = [];
  startTime = moment(startTime);
  endTime = moment(endTime);
  while (startTime <= endTime) {
    returnArray.push(startTime.add(duration, 'minutes'));
  }
  return returnArray;
};

export const makeCurrentUserTokenCacheKey = (token: any, env: string = process.env.NODE_ENV) => {
  return `${ConstantCaches.CURRENT_USER}_${token}_${env}`;
};

export const clearCurrentUserTokenCache = async (accessToken: string = null, cacheManager: any = null, request: any = null) => {
  try {
    const token = accessToken ? accessToken : request?.headers?.authorization?.split(' ')[1];
    const cacheKey = makeCurrentUserTokenCacheKey(token, process.env.NODE_ENV);
    return await cacheManager.del(cacheKey);
  } catch (e) {}
};

export const slugify = (str: string = null): string => {
  if (!str) return null;

  let slugify = str.toLowerCase()?.trim();

  // remove accents, swap ñ for n, etc
  const from = 'ÁÄÂÀÃÅČÇĆĎÉĚËÈÊẼĔȆĞÍÌÎÏİŇÑÓÖÒÔÕØŘŔŠŞŤÚŮÜÙÛÝŸŽáäâàãåčçćďéěëèêẽĕȇğíìîïıňñóöòôõøðřŕšşťúůüùûýÿžþÞĐđßÆa·/_,:;';
  const to = 'AAAAAACCCDEEEEEEEEGIIIIINNOOOOOORRSSTUUUUUYYZaaaaaacccdeeeeeeeegiiiiinnooooooorrsstuuuuuyyzbBDdBAa------';
  for (let i = 0, l = from.length; i < l; i++) {
    slugify = slugify.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
  }
  return slugify
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

export const getTDZTime = () => {
  //return moment().format('YYYY-MM-DDTHH:MM:SS.sD[Z]');
  return new Date().toISOString();
};

export const getSTime = () => {
  return moment().format('YYYY-MM-DDTHH:MM:SS[Z]');
};

export const setSalePasswordLink = (_id: string, userId: string, email: string) => {
  return getBaseUrlData().OPEN_APP_LINK + `?at=/setpass/sale/${_id}?${new URLSearchParams({ userId, email }).toString()}`;
};

export const setWebPasswordLink = (_id: string, userId: string, email: string) => {
  return getBaseUrlData().BASE_WEB_URL + `/create-password?${new URLSearchParams({ sessionId: _id, userId, email }).toString()}`;
};
export const shortenPasswordLink = (_id: string) => {
  return getBaseUrlData().BASE_URL + '/v/' + _id;
};
export const createUniqueCode = (_id: string = null, length = 10) => {
  const nanoid = customAlphabet('1234567890abcdefghijklmnopqrstuvxyzwABCDEFGHIJKLMNOPQRSTUVXYZW', length);
  const nanoidValue = nanoid();
  return _id ? `${nanoidValue}${_id}` : nanoidValue;
};

export const obscureEmail = (email: string) => {
  if (!email) {
    return '';
  }
  const atIndex = email.indexOf('@');
  const length = email.length;
  let username = email.substring(0, atIndex);
  let domain = email.substring(atIndex + 1, length);

  //first 2 characters and last 1 character before the '@'
  let first2CharactersBefore = email.substring(0, 2);
  let last1ChractersBefore = email.substring(atIndex - 1, atIndex);
  //first 1 characters and last 2 character after the '@'
  let first1CharactersAfter = email.substring(atIndex + 1, atIndex + 2);
  let last2ChractersAfter = email.substring(length - 2, length);
  //short case
  if (username.length < 3) {
    first2CharactersBefore = email.substring(0, 1);
  }
  if (domain.length < 3) {
    last2ChractersAfter = email.substring(length - 1, length);
  }
  if (username.length == 1) {
    last1ChractersBefore = '';
  }
  if (domain.length == 1) {
    first1CharactersAfter = '';
  }
  // merge
  username = `${first2CharactersBefore}***${last1ChractersBefore}`.replace('@', '');
  domain = `${first1CharactersAfter}***${last2ChractersAfter}`.replace('@', '');
  return `${username}@${domain}`;
};

export const getBaseUrlData = () => {
  const BASE_WEB_URL = process.env.BASE_WEB_URL || `https://id.tigertribe.dev`;
  const OPEN_APP_LINK = process.env.OPEN_APP_LINK || (process.env.BASE_URL + `/open-app.html`).replace('//open-app.html', '/open-app.html');
  return { BASE_URL: process.env.BASE_URL, BASE_WEB_URL, OPEN_APP_LINK };
};

interface Coordinate {
  lat: any;
  lng: any;
  priority?: number;
  distance?: any;
}

export const calculateDistance = (coord1: Coordinate, coord2: Coordinate): number => {
  const R = 6371;
  const rad = Math.PI / 180;
  const deltaLat = (coord2.lat - coord1.lat) * rad;
  const deltaLon = (coord2.lng - coord1.lng) * rad;

  const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) + Math.cos(coord1.lat * rad) * Math.cos(coord2.lat * rad) * Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c;
};

export const sortWaypoints = (startPoint: string, waypoints: string[]) => {
  const [lat, lng] = startPoint.replace(/\s/g, '').split(',');
  const startPointCoord = { lat: parseFloat(lat), lng: parseFloat(lng) };

  const waypointsCoord = waypoints.map((item: any) => {
    const [lat, lng] = item.replace(/\s/g, '').split(',');
    return { lat: parseFloat(lat), lng: parseFloat(lng) };
  });

  return waypointsCoord.sort((a: Coordinate, b: Coordinate) => {
    const distA = calculateDistance(startPointCoord, a);
    const distB = calculateDistance(startPointCoord, b);
    return distA - distB;
  });
};

export const isValidCoord = (coord) => coord.lat >= -90 && coord.lat <= 90 && coord.lng >= -180 && coord.lng <= 180;
export const isValidCoordString = (coordStr) => {
  const parts = coordStr.split(',');
  if (parts.length !== 2) return false;

  const lat = parseFloat(parts[0]);
  const lng = parseFloat(parts[1]);

  return !isNaN(parts[0]) && !isNaN(parts[1]) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
};

export const findClosestInputCoord = (inputCoords: Coordinate[], outputCoord: Coordinate): Coordinate | null => {
  let closest: Coordinate | null = null;
  let closestDistance = Infinity;

  for (const current of inputCoords) {
    const distance = calculateDistance(current, outputCoord);
    if (distance < closestDistance) {
      closestDistance = distance;
      closest = current;
    }
  }

  return { ...closest, distance: outputCoord.distance, priority: outputCoord.priority };
};

export const findDifference = (arr1: Coordinate[], arr2: Coordinate[]) => {
  return arr1.filter((item1: Coordinate) => !arr2.some((item2: Coordinate) => item1.lat === item2.lat && item1.lng === item2.lng));
};

export const findClosetCoordinates = (inputCoords: Coordinate[], outputCoords: Coordinate[]): (Coordinate | null)[] => {
  const closetData = [];
  for (let i = 0; i < outputCoords.length; i++) {
    const outputCoord = outputCoords[i];
    const closestCoord = findClosestInputCoord(findDifference(inputCoords, closetData), outputCoord);
    closetData.push(closestCoord);
  }
  const difference = findDifference(inputCoords, closetData);
  return difference?.length ? closetData.concat([...difference]) : closetData;
};

export const isBeforeHours = (hours = 10) => {
  const now = moment.tz(process.env.TZ);
  const hoursAM = now.clone().startOf('day').add(hours, 'hours');
  return now.isBefore(hoursAM);
};

export const mappingChannelData = () => {
  const subChannelMapping = {
    'PRR (PRemium Restaurant)': '111',
    'STR (STandard Restaurant)': '112',
    'LCK (Local Bar & Karaoke)': '212',
    'LCK (Local Bar, Karaoke)': '212',
    'PDC (Premium Disco Club)': '221',
    'PAK (Premium Adult Karaoke)': '231',
    'FMK (FaMily Karaoke)': '311',
    'PRB (Premium Bar)': '321',
    'STB (STandard Bar/Pub/Cafe)': '322',
    'WSM (WiSMa/SPA)': '411',
    'PRH (PRemium Hotel)': '511',
    'STH (STandard Hotel)': '512',
    'DJK (Depot Jamu & Kiosk)': '611',
    'DJK (Depot Jamu, Kiosk)': '611',
    'CVS (ConVenience Stores)': '621',
    'CVS (ConVenience Stores) NAB': '621',
    'HSM (Hypers Super Market)': '711',
    'PND (P&D - Proviand en Drank)': '721',
    'EVF (EVents & Functions)': '911',
    'WHS (WHoleSalers)': 'W11',
    'SBD (Subdistributors)': 'W12',
    'SBD-IN (Subdist Selling IN)': 'W13',
    RELASI: 'Z11',
    'BESI (Business, Education, Services, Institutions)': '113',
    'LS (Liquor Store)': '631',
    'ECOM (E-Commerce)': '731',
  };

  const channelMapping = {
    'Modern On': 'MODERN-ON',
    'Traditional On': 'TRAD-ON',
    'Modern Off': 'MODERN-OFF',
    'Traditional Off': 'TRAD-OFF',
    Wholesalers: 'RTM',
    '-': 'OTHERS',
  };

  const convertKeysToUpperCase = (obj: any) => {
    const newObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        newObj[key.toUpperCase().trim()] = obj[key]?.trim();
      }
    }
    return newObj;
  };

  const upperCaseChannelMapping = convertKeysToUpperCase(channelMapping);
  const upperCaseSubChannelMapping = convertKeysToUpperCase(subChannelMapping);

  return {
    channelMapping: upperCaseChannelMapping,
    subChannelMapping: upperCaseSubChannelMapping,
  };
};

export const generateFullMonthlyRange = (data, key, startMonth = 6) => {
  const currentDate = new Date();
  const sixMonthsAgo = new Date(currentDate.getFullYear(), currentDate.getMonth() - 5, 1);
  const monthsRange = [];
  for (let i = 0; i < startMonth; i++) {
    const date = new Date(sixMonthsAgo.getFullYear(), sixMonthsAgo.getMonth() + i, 1);
    monthsRange.push({ month: date.getMonth() + 1, year: date.getFullYear() });
  }
  return monthsRange.map(({ month, year }) => {
    const existingItem = data.find((item) => item.month === month && item.year === year);
    return existingItem || { month, year, [key]: 0 };
  });
};

export const mapDataFromDtoToEntity = <T, U>(originalData: T | null, dataDto: U, ctor: new () => T): T => {
  const mergedData = isEmptyObjectOrArray(originalData) ? new ctor() : originalData;

  return mergeNestedData(mergedData, dataDto);
};

export const mergeNestedData = (original: any, updates: any): any => {
  Object.keys(updates).forEach((key) => {
    const value = updates[key];
    if (value == undefined) {
      original[key] = value;
      return;
    }

    if (typeof value !== 'object' || Array.isArray(value)) {
      original[key] = value;
      return;
    }

    original[key] = mergeNestedData(original[key] || {}, value);
  });
  return original;
};

export const extractImageInformation = (fullImageUrl: string) => {
  try {
    const url = new URL(fullImageUrl);
    const server = url.origin;
    const relativePath = url.pathname;
    const imageName = relativePath.split('/').pop(); // Get the last part of the path

    return { server, path: relativePath[0] === '/' ? relativePath.slice(1) : relativePath, imageName };
  } catch (error) {
    console.error('Error extract URL:', error);
    throw error;
  }
};

export const getData = (obj: any, path: any, defaultValue = undefined) => {
  return path.split('.').reduce((acc: any, key: any) => (acc && acc[key] !== undefined ? acc[key] : undefined), obj) || defaultValue;
};
export const findData = (arr, predicate: any) => arr.find(predicate);

export const base64Encrypt = (data: string): string => {
  const buff: Buffer = Buffer.from(data, 'utf8');
  return buff.toString('base64');
};

export const base64Decrypt = (data: string): string => {
  const buff: Buffer = Buffer.from(data, 'base64');
  return buff.toString('utf8');
};

export const getLangCodeByOpCo = (opCo: string = null): string => {
  switch (opCo) {
    case OpCos.Cambodia:
      return 'kh';
    case OpCos.Indonesia:
      return 'id';
    case OpCos.Malaysia:
      return 'ms';
    case OpCos.Myanmar:
      return 'my';
    default:
      return 'en';
  }
};

export const getBusinessPartnerRequestTargetEmails = (): string[] => {
  return process.env.BUSINESS_PARTNER_REQUEST_TARGET_EMAILS?.split(',');
};

export const checkImportDataRequiredFields = <T>(data: any, fieldNames: (keyof T)[], mappingFieldName: T) => {
  for (let i = 0; i < fieldNames.length; i++) {
    const fieldMappingName = fieldNames[i];
    if (!data[fieldMappingName]) {
      return mappingFieldName[fieldNames[i]];
    }
  }
  return null;
};

export const isValidDate = (dateString) => {
  try {
    return moment.parseZone(dateString).isValid();
  } catch (e) {
    return false;
  }
};

export const excelNumberToDate = (value: any) => {
  try {
    if (isNaN(value)) {
      return value;
    }
    // Excel's base date is December 30, 1899
    const baseDate = new Date(Date.UTC(1899, 11, 30));
    const resultDate = new Date(baseDate.getTime() + Number(value) * 24 * 60 * 60 * 1000);
    return resultDate;
  } catch (err) {
    console.log(`error convert ${value} to date`, err?.message);
    return new Date(value);
  }
};

export const excelNumberToString = (value: any) => {
  try {
    if (isNaN(value)) {
      return value;
    }
    // Excel's base date is December 30, 1899
    const baseDate = new Date(Date.UTC(1899, 11, 30));
    const resultDate = new Date(baseDate.getTime() + Number(value) * 24 * 60 * 60 * 1000);
    return resultDate.toLocaleDateString();
  } catch (err) {
    console.log(`error convert ${value} to date`, err?.message);
    return new Date(value);
  }
};

export const formatDate = (value: Date, pattern: string) => {
  return moment(value).format(pattern);
};

export const getNameFromCode = (code, data) => {
  for (const [name, value] of Object.entries(data)) {
    if (value === code) {
      return name;
    }
  }
  return code;
};

export const getEndTimeOfWeek = (startDate) => {
  const date = new Date(startDate); // Calculate the difference to the end of the week (Sunday)

  const diffToEndOfWeek = 6 - date.getUTCDay(); // Set the date to the end of the week

  date.setUTCDate(date.getUTCDate() + diffToEndOfWeek); // Set the time to the end of the day (23:59:59)

  date.setHours(23, 59, 59, 999);

  return date;
};

export const getCallPlanDayNumber = (date: Date) => {
  if (!date) {
    return undefined;
  }
  try {
    const utcDay = moment(date).tz(process.env.TZ).day();
    if (utcDay === 0) {
      return 7;
    }

    return utcDay;
  } catch (ex) {
    console.log('Error convert date to day number', ex);
    return undefined;
  }
};

export const convertDateToLocalTimeStamp = (date: Date) => {
  return moment(date).tz(process.env.TZ).toDate();
};

export const roundNumber = (originalNumber: number) => {
  if (originalNumber === 0) {
    return 0;
  }

  const abs = Math.abs(originalNumber);

  if (abs >= 1) {
    return originalNumber.toFixed(2);
  }

  // Calculate the magnitude (order of the first significant digit)
  // Example: log10(0.00123) ≈ -2.9, floor is -3
  // Example: log10(0.123) ≈ -0.9, floor is -1
  const magnitude = Math.floor(Math.log10(abs));

  // Determine the number of decimal places needed:
  // We want the first non-zero digit (at position -magnitude)
  // PLUS the next digit. So, we need (-magnitude + 1) decimal places total.
  // Example: magnitude = -3 => places = -(-3) + 1 = 4 (e.g., 0.0012)
  // Example: magnitude = -1 => places = -(-1) + 1 = 2 (e.g., 0.12)
  const decimalPlaces = -magnitude + 1;

  // Safety clamp: Avoid potential issues with extremely small numbers or log10 inaccuracies
  // Ensure at least 2 decimal places and set a reasonable upper limit (e.g., 20)
  const clampedDecimalPlaces = Math.max(2, Math.min(20, decimalPlaces));

  // 5. Apply toFixed with the calculated number of places
  return originalNumber.toFixed(clampedDecimalPlaces);
};

export const convertToHL = (mlNumber: number) => {
  if (Number.isNaN(mlNumber)) {
    return mlNumber;
  }

  return roundNumber(mlNumber / CONVERT_ML_TO_HL);
};

export const safeJsonParse = (data) => {
  try {
    return JSON.parse(data);
  } catch {
    return null;
  }
};

export const getRandomTTL = (ttl?: number): number => {
  if (typeof ttl === 'number') {
    return ttl;
  }
  const min = 86400; // 1 day
  const max = 172800; // 2 days
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 *
 * @param value
 */
export const roundToNearestInt = (value: number): number => {
  const decimal = value - Math.floor(value);
  if (decimal >= 0.5) {
    return Math.ceil(value);
  } else {
    return Math.floor(value);
  }
};

export const calculatePercent = (numerator: number, denominator: number, fixedNumber = 0): number => {
  if (!denominator) {
    return 0;
  }
  const percent = (numerator * 100) / denominator || 0;

  return fixedNumber > 0 ? parseFloat(percent?.toFixed(fixedNumber)) : roundToNearestInt(percent);
};
