/**
 * Compares two dates by their date part only (ignoring time)
 * @param date1 First date to compare
 * @param date2 Second date to compare
 * @returns -1 if date1 is before date2, 0 if equal, 1 if date1 is after date2
 */
export const compareDates = (date1: Date, date2: Date): number => {
  const d1 = new Date(date1);
  const d2 = new Date(date2);

  // Set both dates to start of day (00:00:00) for date-only comparison
  d1.setHours(0, 0, 0, 0);
  d2.setHours(0, 0, 0, 0);

  if (d1 < d2) return -1;
  if (d1 > d2) return 1;
  return 0;
};

/**
 * Checks if a date is in the past (before today)
 * @param date Date to check
 * @returns true if the date is in the past
 */
export const isDateInPast = (date: Date): boolean => {
  const today = new Date();
  return compareDates(date, today) === -1;
};

/**
 * Checks if a date is today or in the future
 * @param date Date to check
 * @returns true if the date is today or in the future
 */
export const isDateTodayOrFuture = (date: Date): boolean => {
  const today = new Date();
  return compareDates(date, today) >= 0;
};

/**
 * Generates an array of dates in MM/DD format for a given date range
 * @param startDate Start date of the range
 * @param endDate End date of the range
 * @returns Array of dates in MM/DD format
 */
export const generateDateRange = (startDate: Date, endDate: Date): string[] => {
  const dates: string[] = [];
  const currentDate = new Date(startDate);
  const today = new Date();
  const end = new Date(endDate);

  // Set all dates to start of day for consistent comparison
  currentDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);
  end.setHours(0, 0, 0, 0);

  // If endDate is in the future, use today's date instead
  const finalEndDate = end > today ? today : end;

  while (currentDate <= finalEndDate) {
    dates.push(
      currentDate.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric',
      }),
    );
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
};

/**
 * Fills in missing dates with default values
 * @param data Array of objects with date property
 * @param allDates Array of all dates in the range
 * @param defaultValue Default values for missing dates
 * @returns Array of objects with all dates filled
 */
export const fillMissingDates = <T extends { date: string }>(data: T[], allDates: string[], defaultValue: Omit<T, 'date'>): T[] => {
  const dateMap = new Map(data.map((item) => [item.date, item]));
  return allDates.map(
    (date) =>
      ({
        date,
        ...(dateMap.get(date) || defaultValue),
      } as T),
  );
};

/**
 * Formats duration in minutes to HH:MM:SS format
 * @param minutes Duration in minutes
 * @returns Formatted duration string in HH:MM:SS format
 */
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const remainingMinutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${hours.toString().padStart(2, '0')}:${remainingMinutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * Formats a date to DD MMM YYYY format
 * @param date Date to format
 * @returns Formatted date string in DD MMM YYYY format
 */
export const formatDate = (date: Date): string => {
  return date
    .toLocaleDateString('en-US', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    })
    .replace(/(\w+)\s(\d+),\s(\d+)/, '$2 $1 $3');
};

/**
 * Converts date format from MM/DD/YYYY to MM/DD
 * @param data Array of objects with date property in MM/DD/YYYY format
 * @returns Array of objects with date property in MM/DD format
 */
export const convertToShortDate = <T extends { date: string }>(data: T[]): T[] => {
  return data.map((item) => ({
    ...item,
    date: item.date.split('/').slice(0, 2).join('/'),
  }));
};
