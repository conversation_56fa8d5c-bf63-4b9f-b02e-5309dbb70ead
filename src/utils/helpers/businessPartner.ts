import { BusinessPartner } from '../../master-data/entities/business-partner/business-partner.entity';
import { BusinessPartnerRelationCommunication } from '../../master-data/constants/business-partner.enum';
import * as _ from 'lodash';
import { isEmptyObjectOrArray } from '..';

export const extractBusinessPartnerCommunicationData = (
  data: BusinessPartner,
  communicationType: BusinessPartnerRelationCommunication = BusinessPartnerRelationCommunication.TEL,
  fieldName: string = 'communicationValue'
) => {
  const phoneCommunication = data.communications.find((communication) => communication.communicationType === communicationType);
  return _.get(phoneCommunication, fieldName, '');
};

export const extractBusinessPartnerRelatedData = (data: BusinessPartner, relatedDataKey: string, fieldName: string) => {
  const values = _.get(data, relatedDataKey, []).filter((value) => !isEmptyObjectOrArray(value) && !value.isDeleted);
  const firstNonNullValue = values.find((value) => !_.isNil(_.get(value, fieldName)));
  return (firstNonNullValue && _.get(firstNonNullValue, fieldName)) || '';
};

export const extractBusinessPartnerOperatingHoursData = (data: BusinessPartner) => {
  const operatingHours = data.operatingHours || [];
  return operatingHours.map((operatingHour) => `${operatingHour.day}-${!operatingHour.isClosed}-${operatingHour.openTime}-${operatingHour.closeTime}`).join('|');
};
