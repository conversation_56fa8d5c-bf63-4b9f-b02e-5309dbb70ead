import { BadRequestException } from '@nestjs/common';

/**
 * Validates that a string value is not empty or only whitespace
 * @param value - The string value to validate
 * @param message - The error message to throw if validation fails
 * @throws BadRequestException if the value is empty or only whitespace
 */
export function validateNotEmpty(value: string, message: string): void {
  if (!value || value.trim() === ''
    || value === null
    || value === undefined) {
    throw new BadRequestException(message);
  }
}
