import { Between, <PERSON><PERSON>ty<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>han } from 'typeorm';
import {AppDataSourceTransaction} from '../../typeormTransaction.config';
import { DateRangeDto } from 'src/master-data/dtos/date-range.dto';

export const runTransaction = async (actions: ((transactionalEntityManager: EntityManager, transactionContext: any) => Promise<any>)[]): Promise<any[]> => {
  const responses = [];
  await AppDataSourceTransaction.transaction(async (transactionalEntityManager) => {
    const context = {};
    for (const action of actions) {
      responses.push(await action(transactionalEntityManager, context));
    }
  });
  return responses;
};

export const buildSearchDateCriteria = (fieldName: string, dateParams: DateRangeDto) => {
  const {startDate, endDate} = dateParams;
  const startDateSearchCriteria = startDate && new Date(startDate);
  startDateSearchCriteria?.setHours(0, 0, 0, 0);
  const endDateSearchCriteria = endDate && new Date(endDate)
  endDateSearchCriteria?.setHours(23, 59, 59, 999);
  if (startDateSearchCriteria && endDateSearchCriteria) {
    return {
      [`${fieldName}`]: Between(startDateSearchCriteria, endDateSearchCriteria)
    }
  }
  if (startDateSearchCriteria) {
    return {
      [`${fieldName}`]: MoreThan(startDateSearchCriteria)
    }
  }
  if (endDateSearchCriteria) {
    return {
      [`${fieldName}`]: LessThan(endDateSearchCriteria)
    }
  }
  return {};
};
