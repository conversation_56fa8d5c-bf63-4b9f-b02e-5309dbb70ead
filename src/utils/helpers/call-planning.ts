import { CallCenterStatus, CallPlanCreatorType } from '../../call-center/enums/call-center.enum';
import * as moment from 'moment-timezone';

export interface CallMetrics {
  managerCallsTotal: number;
  total: number;
  managerCallsCompleted: number;
  totalCompleted: number;
  managerCallsMissed: number;
  totalMissed: number;
  managerCallsRescheduled: number;
  totalRescheduled: number;
}

export interface CallPercentages {
  compliance: number;
  rescheduled: number;
  missed: number;
}

export interface DailyData {
  date: string;
  [key: string]: any;
}

/**
 * Gets a nested property value from an object using a dot-notation path
 */
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

/**
 * Calculates the duration between two dates in HH:MM:SS format
 * @param startTime Start time of the call
 * @param endTime End time of the call
 * @returns Formatted duration string (HH:MM:SS)
 */
export const calculateCallDuration = (startTime: Date, endTime: Date): string => {
  if (!startTime || !endTime) return '00:00:00';

  const diffInSeconds = Math.floor((new Date(endTime).getTime() - new Date(startTime).getTime()) / 1000);
  if (diffInSeconds < 0) return '00:00:00';

  const hours = Math.floor(diffInSeconds / 3600);
  const minutes = Math.floor((diffInSeconds % 3600) / 60);
  const seconds = diffInSeconds % 60;

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

/**
 * Groups and sorts data by date
 * @param data Array of items to group
 * @param dateField Field containing the date to group by (can be a nested path like 'callPlanning.displayDay')
 * @param groupFn Function to transform the grouped items
 * @returns Sorted array of grouped data
 */
export const groupAndSortByDate = <T>(data: T[], dateField: string, groupFn: (items: T[], date: string) => DailyData): DailyData[] => {
  // Group data by date
  const groupedData = data.reduce((acc, item) => {
    const dateValue = getNestedValue(item, dateField);
    if (!dateValue) return acc;

    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return acc;

    const formattedDate = moment(date).tz(process.env.TZ).format('MM/DD/YYYY');

    if (!acc[formattedDate]) {
      acc[formattedDate] = [];
    }
    acc[formattedDate].push(item);
    return acc;
  }, {});

  // Transform and sort the grouped data
  return Object.entries(groupedData)
    .map(([date, items]) => groupFn(items as T[], date))
    .sort((a, b) => {
      const [aMonth, aDay] = a.date.split('/').map(Number);
      const [bMonth, bDay] = b.date.split('/').map(Number);
      return aMonth === bMonth ? aDay - bDay : aMonth - bMonth;
    });
};

/**
 * Calculates call metrics for a given set of calls
 */
export const calculateCallMetrics = (items: any[]): CallMetrics => {
  const today = moment().tz(process.env.TZ).startOf('day').toDate();

  const { managerCallsTotal, totalCompleted, managerCallsCompleted, managerCallsMissed, totalMissed, managerCallsRescheduled, totalRescheduled } = (items || []).reduce(
    (acc, cp) => {
      const isManagerCreatedCall = cp.creator === CallPlanCreatorType.MANAGER;
      if (isManagerCreatedCall) {
        acc.managerCallsTotal++;
      }
      if (cp.callStatus === CallCenterStatus.COMPLETED) {
        acc.totalCompleted++;
        acc.managerCallsCompleted = isManagerCreatedCall ? acc.managerCallsCompleted + 1 : acc.managerCallsCompleted;
      }

      const displayDay = moment(cp.displayDay).tz(process.env.TZ).startOf('day').toDate();
      if (displayDay < today && cp.callStatus !== CallCenterStatus.COMPLETED) {
        acc.totalMissed++;
        acc.managerCallsMissed = isManagerCreatedCall ? acc.managerCallsMissed + 1 : acc.managerCallsMissed;
      }

      if (cp.rescheduled && cp.callStatus !== CallCenterStatus.MISSED && cp.callStatus !== CallCenterStatus.COMPLETED) {
        acc.totalRescheduled++;
        acc.managerCallsRescheduled = isManagerCreatedCall ? acc.managerCallsRescheduled + 1 : acc.managerCallsRescheduled;
      }
      return acc;
    },
    { managerCallsTotal: 0, managerCallsCompleted: 0, totalCompleted: 0, managerCallsMissed: 0, totalMissed: 0, managerCallsRescheduled: 0, totalRescheduled: 0 },
  );

  return {
    total: items.length,
    managerCallsTotal,
    managerCallsCompleted,
    totalCompleted,
    totalMissed,
    managerCallsMissed,
    totalRescheduled,
    managerCallsRescheduled,
  };
};

/**
 * Calculates percentages based on call metrics
 */
export const calculateCallPercentages = (metrics: CallMetrics): CallPercentages => {
  const { total, totalMissed, totalRescheduled, managerCallsTotal, managerCallsCompleted, managerCallsMissed, managerCallsRescheduled } = metrics;
  const totalManagementValidCalls = managerCallsTotal - (managerCallsMissed + managerCallsRescheduled);

  return {
    compliance: totalManagementValidCalls > 0 ? Math.round((managerCallsCompleted / totalManagementValidCalls) * 100) : 0,
    rescheduled: total > 0 ? Math.round((totalRescheduled / total) * 100) : 0,
    missed: total > 0 ? Math.round((totalMissed / total) * 100) : 0,
  };
};

/**
 * Groups calls by date
 */
export const groupCallsByDate = (calls: any[], dateField: string) => {
  return calls.reduce((acc, call) => {
    const date = new Date(call[dateField]);
    const formattedDate = `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;

    if (!acc[formattedDate]) {
      acc[formattedDate] = [];
    }
    acc[formattedDate].push(call);
    return acc;
  }, {});
};

export const convertMomentToCallPlanDate = (momentObject: moment.Moment) => {
  return new Date(momentObject.format('YYYY-MM-DD HH:mm'));
}