const isLatitude = (maybeLat: any) => {
  const latF = parseFloat(maybeLat);
  if (isNaN(latF)) return false;
  return latF >= -90 && latF <= 90;
};
const isLongitude = (maybeLong: any) => {
  const lonF = parseFloat(maybeLong);
  if (isNaN(lonF)) return false;
  return lonF >= -180 && lonF <= 180;
};
export const isValidGeoAddress = (address: string) => {
  try {
    const [lat, long] = address?.split(',');

    return isLatitude(lat) && isLongitude(long);
  } catch {
    return false;
  }
};
