{"ucc": "UCC", "outletName": "Outlet Name", "outletClass": "Outlet Class", "outletArea": "Area", "outletAddress": "Outlet Address", "contactName": "Contact Name", "salesRepName": "Sales Rep Name", "status": "Status", "channel": "Channel", "subChannel": "Sub Channel", "distributorId": "Distributor ID", "distributorName": "Distributor Name", "depotId": "Depot ID", "depotName": "Depot Name", "depotIDs": "Depot IDs", "name": "Name", "mobilePhoneCode": "Phone Code", "phoneNumber": "Phone Number", "dotUsername": "DOT Username", "cpsr": "CPSR Achievement (%)", "cpsrValue": "CPSR (Visited)", "cpsrTarget": "CPSR (Planned)", "salesAchievement": "Sales Achievement (%)", "salesActual": "Sales (Actual)", "salesTarget": "Sales (Target)", "mabo": "MABO Achievement (%)", "maboValue": "MABO \n(Positive Sales Outlets)", "maboTarget": "MABO \n(Active Outlets)", "cpsr_ID": "Call Completion Rate (%)", "cpsrValue_ID": "Call Completion Rate (Visited)", "cpsrTarget_ID": "Call Completion Rate (Planned)", "salesAchievement_ID": "Volume Achievement (%)", "salesActual_ID": "Volume Actual (HL)", "salesTarget_ID": "Volume Target (HL)", "mabo_ID": "Active Selling Outlet Achievement (%)", "maboValue_ID": "Active Selling Outlet (Actual)", "maboTarget_ID": "Active Selling Outlet (Assigned)", "plannedDate": "Planned Date", "visitedDate": "Visited Date", "visitDate": "Visit Date", "startTime": "Start Time", "endTime": "End Time", "outletID": "Outlet ID", "availability": "Availability", "visibility": "Visibility", "missedDate": "Missed Date", "missedReason": "Missed reason", "salesRepId": "Sales Rep ID", "area": "Area", "week": "Week", "day": "Day", "ACTIVE": "ACTIVE", "INACTIVE": "INACTIVE", "BLOCKED": "BLOCKED", "DELETED": "DELETED", "message": "Message", "cannotUpdateActiveStatus": "Can not update active status", "uncontrollable": "Uncontrollable", "note": "Note", "lastUpdatedTime": "Last updated time", "plannedJourneyPlans": "Planned", "controllableJourneyPlans": "C-Miss", "uncontrollableJourneyPlans": "UC-Miss", "successJourneyPlans": "Success", "cpsrPercent": "CPSR (%)", "cpsrPercent_ID": "Call Completion Rate (%)", "reason": "Reason", "pt": "Pt", "qt": "Qt", "can": "Can", "keg": "<PERSON>g", "forwardStocks": "Forward Stocks", "images": "Images", "location": "Location", "geoAddress": "Geographic address", "strikeRateAchievement": "Call Strike Rate Achievement (%)", "strikeRateActual": "Call Strike Rate (Actual)", "strikeRateTarget": "Call Strike Rate (No. of visited Outlet)", "taskName": "Task name", "taskSubHeading": "Sub-heading", "startDate": "Start date", "endDate": "End date", "targetSetting": {"salesRepId": "Rep ID", "salesRepName": "Rep name", "month": "Month", "year": "Year", "callComplianceRate": "Call compliance rate", "callEffectiveness": "Call effectiveness", "salesVolume": "Sales volume", "salesValue": "Sales value", "activeSellingOutlet": "Active selling outlet", "availability": "Availability", "visibility": "Visibility"}, "plansOutletUcc": "UCC", "plansOutletName": "Outlet name", "plansSalesRepId": "Rep ID", "plansWeek": "Week", "plansDay": "Day", "plansDistributorId": "Distributor ID", "plansDistributorName": "Distributor name", "repReports": {"ucc": "UCC", "outletName": "Outlet name", "repId": "Rep ID", "repName": "Rep name", "distId": "Dist ID", "distName": "Dist name", "callComplianceRate": "Call compliance rate", "callEffectiveness": "Call effectiveness", "salesVolume": "Sales volume", "salesValue": "Sales value", "activeSellingOutlet": "Active selling outet", "availability": "Availability", "visibility": "Visibility", "checklist": "Checklist", "checked": "Checked", "yes": "Yes", "no": "No", "lastUpdated": "Last updated"}, "outletReports": {"ucc": "UCC", "outletName": "Outlet name", "repId": "Rep ID", "distId": "Distributor ID", "distName": "Distributor name", "visitedDate": "Visited date", "plannedDate": "Planned date", "skippedReason": "Skipped reason", "uncontrollable": "Uncontrollable", "evidenceImages": "Evidence images", "startTime": "Start time", "endTime": "End time", "coldStock": {"availableFridge": "Is there a fridge/chiller available?", "isDedicatedFridge": "Is it our dedicated fridge/chiller?", "isProductsInDedicatedFridge": "Is our products in our dedicated fridge?", "photoOfFridge": "Photo of the fridge/chiller"}, "visibility": {"taskName": "Task name", "subHeading": "Sub-heading"}, "checkStock": {"product": "Product", "stockCount": "Stock count", "check": "Check", "checked": "Checked", "unchecked": "Unchecked", "affordability": "Affordability"}}}