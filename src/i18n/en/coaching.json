{"session": {"day_must_be_future": "Coaching session day must be today or in the future. Sales Rep ID: {salesRepId}, Day: {day}", "sales_rep_not_found": "Sales rep with ID {id} not found", "session_already_exists": "Coaching session already exists for sales rep {id} on {day}", "invalid_distributor_depot": "Invalid distributor or depot. Please ensure you have valid distributor and depot assignments.", "not_found": "Coaching session with ID {id} not found", "visit_not_found": "Visit with ID {id} not found", "status_not_allowed": "Session with ID {id} has status {status} which does not allow operations", "status_not_allowed_for_start": "Cannot start coaching session. Session {sessionId} has status {currentStatus}, but only {requiredStatus} status is allowed to start coaching", "invalid_status_transition": "Invalid status transition from {currentStatus} to {newStatus}. Allowed transitions: {allowedTransitions}", "outlet_not_found": "Outlet with key {outlet<PERSON>ey} not found", "session_history_already_exists": "Coaching session history already exists for session {sessionId} and outlet {outletKey}", "has_ongoing_histories": "Cannot update session status. Session {sessionId} has ongoing visits that must be completed first", "invalid_status_for_reschedule": "Cannot reschedule session {sessionId}. Current status is {currentStatus}, but only {allowedStatus} status is allowed for rescheduling", "cannot_reschedule_with_histories": "Cannot reschedule session {sessionId}. Session has existing histories and cannot be rescheduled", "reschedule_date_must_be_future": "Reschedule date {rescheduleDate} must be greater than current date {currentDate}", "started_successfully": "Coaching session started successfully", "ended_successfully": "Coaching session visit ended successfully"}}