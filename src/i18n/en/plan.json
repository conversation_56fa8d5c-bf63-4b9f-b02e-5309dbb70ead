{"not_found": "Plan not found", "not_found_today": "Today plan not found", "not_found_cycle": "Cycle not found", "not_found_week": "Week not found", "not_found_sales_rep": "Sales Rep not found", "not_found_outlet": "Outlet not found", "not_found_distributor": "Distributor not found", "not_found_evidence": "Evidence file not found", "not_found_report_outlet": "Report outlet not found", "not_found_relation_sales_rep_outlet": "Not found relation between sales rep and outlet", "not_found_relation_sales_rep_distributor": "Not found relation between sales rep and distributor", "invalid_schedule_day": "Invalid schedule day", "existed_time_slot": "Time slot is existed", "not_available_time_slot": "Time slot is not available", "exceeded_time": "The automation time slot is exceeded end of day", "invalid_time_range": "End time must be greater than start time", "unauthorized": "You do not have permission to perform this action", "week": {"not_found": "Not found week"}, "cannot_edit_data": "Can not edit data for this plan", "cannot_reschedule": "<PERSON><PERSON> reschedule for the cancel plan", "cannot_reschedule_uncontrollable_missed_visit": "<PERSON><PERSON> reschedule the uncontrollable missed visit", "cannot_reschedule_in_progress_plan": "<PERSON><PERSON> reschedule for the in progress plan in today", "cannot_set_reason_cancel_plan": "<PERSON><PERSON> set reason for the cancel plan", "cannot_set_reason": "Cannot set reason", "not_found_reason": "The provided miss reason is invalid", "missed_visit_reason": {"public_holiday": "Public Holiday", "annual_medical_leave": "Annual / Medical Leave", "meeting_training_special_event": "Meeting / Training / Special Event", "tasks_assigned_by_hmb": "Tasks assigned by HMB", "vehicle_breakdown": "Vehicle breakdown", "natural_disaster": "Natural disaster", "outlet_shutdown": "Outlet shutdown", "invalid_from_date": "From date must be after or equal today", "invalid_to_date": "To date must be after from date and before end of cycle date"}, "update_status": "Can not change outlet status to InActive due to existing an in-progress journey planning need to complete", "update_outlet_failed": "You can not change this data due to this outlet or sales rep id is inactive", "exceed_plan_per_outlet": "The number of plans of outlet per week is exceed", "cannot_create_plan_for_past_date": "Cannot create new plan for past date", "cannot_update_plan_to_past_week_day": "Cannot update to past week & day", "cannot_update_plan_to_another_week": "Only reschedule a plan in same assigned week", "cannot_update_executed_plan": "Cannot update executed visit for UCC: {ucc}", "cycle_id_required": "CycleId is required", "evidence_required": "Evidence is required", "invalid_week_day": "The week and day must be future time", "existed_outlet_plan": "Outlet cannot be updated this week anymore as it's assigned already", "no_data_to_execute": "There is no data to execute", "existed_absence_reason": "Missed reason already existed", "invalid_date_sync": "Data cannot be synchronized for this plan because it has been offline for more than 7 days from the scheduled date", "cannot_update_past_plan": "You can only update Today's Plan"}