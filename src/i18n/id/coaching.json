{"session": {"day_must_be_future": "<PERSON> sesi coaching harus hari ini atau di masa depan. ID Sales Rep: {salesRepId}, Hari: {day}", "sales_rep_not_found": "Sales rep dengan ID {id} tidak ditemukan", "session_already_exists": "Sesi coaching sudah ada untuk sales rep {id} pada {day}", "invalid_distributor_depot": "Distributor atau depot tidak valid. Pastikan Anda memiliki penugasan distributor dan depot yang valid.", "not_found": "Sesi coaching dengan ID {id} tidak ditemukan", "visit_not_found": "Kunjungan dengan ID {id} tidak ditemukan", "status_not_allowed": "Sesi dengan ID {id} memiliki status {status} yang tidak mengizinkan operasi", "status_not_allowed_for_start": "Tidak dapat memulai sesi coaching. Sesi {sessionId} memiliki status {currentStatus}, tetapi hanya status {requiredStatus} yang di<PERSON>inkan untuk memulai coaching", "invalid_status_transition": "Transisi status tidak valid dari {currentStatus} ke {newStatus}. Transisi yang diizinkan: {allowedTransitions}", "outlet_not_found": "Outlet dengan kunci {outletKey} tidak ditemukan", "session_history_already_exists": "Riwayat sesi coaching sudah ada untuk sesi {sessionId} dan outlet {outletKey}", "has_ongoing_histories": "Tidak dapat memperbarui status sesi. Sesi {sessionId} memiliki kunjungan yang sedang berlangsung yang harus diselesaikan terlebih dahulu", "invalid_status_for_reschedule": "Tidak dapat menjadwal ulang sesi {sessionId}. Status saat ini adalah {currentStatus}, tetapi hanya status {allowedStatus} yang diizinkan untuk penjadwalan ulang", "cannot_reschedule_with_histories": "Tidak dapat menjadwal ulang sesi {sessionId}. Sesi memiliki riwayat yang ada dan tidak dapat dijadwal ulang", "reschedule_date_must_be_future": "<PERSON><PERSON> penjad<PERSON>n ulang {rescheduleDate} harus lebih besar dari tanggal saat ini {currentDate}", "started_successfully": "<PERSON><PERSON> coaching ber<PERSON><PERSON>i"}}