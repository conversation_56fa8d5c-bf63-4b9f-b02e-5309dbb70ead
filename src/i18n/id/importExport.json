{"ucc": "UCC", "outletName": "<PERSON><PERSON>", "outletClass": "<PERSON><PERSON> k<PERSON>", "outletArea": "<PERSON><PERSON><PERSON>", "outletAddress": "Alamat outlet", "contactName": "<PERSON><PERSON>", "salesRepName": "Nama <PERSON>ju<PERSON>", "status": "Status", "channel": "<PERSON><PERSON><PERSON>", "subChannel": "Sub-Saluran", "distributorId": "ID Distributor", "distributorName": "Nama Distributor", "depotId": "ID Depo", "depotName": "<PERSON><PERSON>", "depotIDs": "Depo IDs", "name": "<PERSON><PERSON>", "mobilePhoneCode": "Kode Telepon", "phoneNumber": "Nomor Telepon", "dotUsername": "Nama Pengguna DOT", "cpsr": "Pencapaian CPSR (%)", "cpsrValue": "CPSR (Dikunjungi)", "cpsrTarget": "CPSR (Direncanakan)", "salesAchievement": "Pencapaian Penjualan (%)", "salesActual": "Penjualan (Aktual)", "salesTarget": "Penjualan (Target)", "mabo": "Pencapaian MABO (%)", "maboValue": "MABO \n(Outlet Penjualan Positif)", "maboTarget": "MABO \n(Outlet Aktif)", "cpsr_ID": "Tingkat Penyelesaian Panggilan (%)", "cpsrValue_ID": "Tingkat Penyeles<PERSON> (Dikunjungi)", "cpsrTarget_ID": "Tingkat Penyeles<PERSON> (Direncanakan)", "salesAchievement_ID": "Pencapaian Volume (%)", "salesActual_ID": "Volume Aktual (HL)", "salesTarget_ID": "Sasaran Volume (HL)", "mabo_ID": "Pencapaian Outlet Penjualan Aktif (%)", "maboValue_ID": "Outlet Penjualan Aktif (Sebenarnya)", "maboTarget_ID": "Outlet Penjualan Aktif (Ditugaskan)", "plannedDate": "Tang<PERSON> yang <PERSON>", "visitedDate": "<PERSON>gal <PERSON>", "visitDate": "Tanggal Kunjungan", "startTime": "<PERSON><PERSON><PERSON>", "endTime": "<PERSON><PERSON><PERSON>", "outletID": "ID keluaran", "ketersediaan": "<PERSON><PERSON><PERSON><PERSON>", "visibilitas": "Visibilitas", "missedDate": "Tanggal yang terlewatkan", "missedReason": "<PERSON>asan yang terle<PERSON>kan", "salesRepId": "ID Perwakilan Penjualan", "area": "<PERSON><PERSON><PERSON>", "week": "<PERSON><PERSON>", "day": "<PERSON>", "ACTIVE": "AKTIF", "INACTIVE": "TIDAK AKTIF", "BLOCKED": "DIBLOKIR", "DELETED": "DIHAPUS", "message": "<PERSON><PERSON>", "cannotUpdateActiveStatus": "Tidak dapat memperbarui status aktif", "uncontrollable": "Uncontrollable", "note": "Catatan", "lastUpdatedTime": "<PERSON><PERSON><PERSON>", "plannedJourneyPlans": "Planned", "controllableJourneyPlans": "C-Miss", "uncontrollableJourneyPlans": "UC-Miss", "successJourneyPlans": "Success", "cpsrPercent": "CPSR (%)", "cpsrPercent_ID": "Tingkat Penyelesaian Panggilan (%)", "reason": "Reason", "pt": "Pt", "qt": "Qt", "can": "Can", "keg": "<PERSON>g", "forwardStocks": "<PERSON><PERSON><PERSON>", "images": "Gambar", "location": "<PERSON><PERSON>", "geoAddress": "<PERSON><PERSON><PERSON> geo<PERSON>", "strikeRateAchievement": "Pencapaian Tingkat Pukulan Panggilan (%)", "strikeRateActual": "Tingkat Pukulan <PERSON>gg<PERSON>n (Aktual)", "strikeRateTarget": "Tingkat Puku<PERSON> (Jumlah Outlet yang Dikunjungi)", "taskName": "<PERSON><PERSON>", "taskSubHeading": "Sub-judul", "startDate": "<PERSON><PERSON>", "endDate": "<PERSON><PERSON>", "targetSetting": {"salesRepId": "ID Sales Rep", "salesRepName": "Nama Sales Rep", "month": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>", "callComplianceRate": "Ting<PERSON>", "callEffectiveness": "Efektivitas Panggilan", "salesVolume": "Volume Penjualan", "salesValue": "<PERSON><PERSON>", "activeSellingOutlet": "Outlet Penjualan Aktif", "availability": "<PERSON><PERSON><PERSON><PERSON>", "visibility": "Visibilitas", "agentId": "ID Agent", "agentName": "<PERSON>a <PERSON>", "outletId": "ID Gerai", "outletName": "<PERSON><PERSON>", "strikeRate": "Tingkat Pukulan Panggilan", "callCoverage": "Pencapaian Tingkat Penyelesaian Panggilan", "volumeTarget": "Volume Target", "importSuccess": "Your data has been imported successfully!", "agent_not_found": "Agent {agentId} tidak di<PERSON>n"}, "plansOutletUcc": "UCC", "plansOutletName": "<PERSON><PERSON>", "plansSalesRepId": "ID Sales Rep", "plansWeek": "<PERSON><PERSON>", "plansDay": "<PERSON>", "plansDistributorId": "ID Distributor", "plansDistributorName": "Nama Distributor", "repReports": {"ucc": "UCC", "outletName": "<PERSON><PERSON>", "repId": "ID Rep", "repName": "Nama Rep", "distId": "ID Dist", "distName": "<PERSON><PERSON>", "callComplianceRate": "Ting<PERSON>", "callEffectiveness": "Efektivitas Panggilan", "salesVolume": "Volume Penjualan", "salesValue": "<PERSON><PERSON>", "activeSellingOutlet": "Outlet Penjualan Aktif", "availability": "<PERSON><PERSON><PERSON><PERSON>", "visibility": "Visibilitas", "checklist": "Daftar Perik<PERSON>", "checked": "Diperiksa", "yes": "Ya", "no": "Tidak", "lastUpdated": "<PERSON><PERSON><PERSON>"}, "outletReports": {"ucc": "UCC", "outletName": "<PERSON><PERSON>", "repId": "ID Rep", "distId": "ID Distributor", "distName": "Nama Distributor", "visitedDate": "<PERSON>gal <PERSON>", "plannedDate": "<PERSON><PERSON>", "skippedReason": "<PERSON><PERSON><PERSON>", "uncontrollable": "Tidak Te<PERSON>endali", "evidenceImages": "<PERSON><PERSON><PERSON>", "startTime": "<PERSON><PERSON><PERSON>", "endTime": "<PERSON><PERSON><PERSON>", "coldStock": {"availableFridge": "A<PERSON><PERSON>h ada lemari es/pendingin tersedia?", "isDedicatedFridge": "Apakah itu lemari es/<PERSON>in khus<PERSON> kita?", "isProductsInDedicatedFridge": "<PERSON><PERSON><PERSON><PERSON> produk kita ada di lemari es/<PERSON>in khusus kita?", "photoOfFridge": "Foto lemari es/pendingin"}, "visibility": {"taskName": "<PERSON><PERSON>", "subHeading": "Subjudul"}, "checkStock": {"product": "Produk", "stockCount": "<PERSON><PERSON><PERSON>", "check": "Periksa", "checked": "<PERSON><PERSON>", "unchecked": "Belum diperiksa", "affordability": "Kemampuan"}}, "masterData": {"importDataRequiredError": "Kesalahan Impor Data {fileType}: {dataType} baris {row} - data {field} diperlukan!", "importDataInvalidError": "Kesalahan Impor Data {fileType}: {dataType} baris {row} - data {field} tidak valid!", "importExternalKeyDuplicated": "Kesalahan Impor Data {fileType}: {dataType} baris {row} - <PERSON><PERSON><PERSON> eksternal duplikat!", "outlet_not_found": "Outlet {outletId} tidak ditemukan", "incorrect_relation": "Hubungan yang salah antara outlet {outletId} dan agent {agentId}"}}