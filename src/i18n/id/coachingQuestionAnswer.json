{"question": {"not_found": "<PERSON><PERSON><PERSON> tidak ditemukan: {questionIds}", "cannot_update_type_with_answers": "Tidak dapat memperbarui tipe untuk pertanyaan {questionId} karena memiliki jawaban yang ada. <PERSON>ya pembaruan teks yang diizinkan.", "created_successfully": "<PERSON><PERSON><PERSON> ber<PERSON>il dibuat", "updated_successfully": "<PERSON><PERSON><PERSON> be<PERSON>", "deleted_successfully": "<PERSON><PERSON><PERSON> be<PERSON>"}, "answer": {"required_field_empty": "<PERSON><PERSON><PERSON> diperlukan untuk pertanyaan {questionId}", "invalid_number_value": "<PERSON><PERSON> angka tidak valid untuk pertanyaan {questionId}", "invalid_string_value": "Nilai string tidak valid (kosong atau hanya spasi) untuk pertanyaan {questionId}", "invalid_object_value": "<PERSON><PERSON> objek tidak valid (objek kosong) untuk pertanyaan {questionId}", "invalid_rating_value": "<PERSON><PERSON> rating tidak valid (harus 1 atau lebih tinggi) untuk pertanyaan {questionId}"}}