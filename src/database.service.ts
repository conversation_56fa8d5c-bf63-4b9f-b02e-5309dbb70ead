import { Injectable, OnModuleInit } from '@nestjs/common';
import { AppDataSourceTransaction } from './typeormTransaction.config';

@Injectable()
export class DatabaseService implements OnModuleInit {
  async onModuleInit() {
    try {
      await AppDataSourceTransaction.initialize();
      console.log('Data Source has been initialized!');
    } catch (err) {
      console.error('Error during Data Source initialization:', err);
    }
  }
}
