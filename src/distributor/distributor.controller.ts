import {
  BadRequestException,
  Body,
  Controller,
  forwardRef,
  Get,
  Inject,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBadRequestResponse, ApiBearerAuth, ApiBody, ApiConsumes, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import * as _ from 'lodash';
import { Types } from 'mongoose';
import { I18n, I18nContext } from 'nestjs-i18n';

import { EventEmitter2 } from '@nestjs/event-emitter';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { SaleRepService } from 'src/sale-rep/services/salerep.service';
import { SharedEvent } from 'src/shared/events/event/shared.event';
import { ConstantEventName } from 'src/utils/events';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { JourneyPlanWeekService } from '../journey-plannings/services/journey-plan-week.service';
import { OutletJourneyPlanningService } from '../journey-plannings/services/outlet-journey-planning.service';
import { ISaleRepOutletAggregation } from '../outlets/interfaces';
import { OutletsService } from '../outlets/services/outlets.service';
import { SaleRepOutletRelationService } from '../outlets/services/sale-rep-outlet-relation.service';
import { ApiException } from '../shared/api-exception.model';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { Roles } from '../shared/decorator/roles.decorator';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Serialize } from '../shared/interceptor/serialize.interceptor';
import { ApiResponse } from '../shared/response/api-response';
import { User } from '../users/schemas/user.schema';
import { UserAdminsService } from '../users/services/user-admins.service';
import { ConstantRoles } from '../utils/constants/role';
import { GetListDistributorsArgs } from './args';
import { CreateDistributorDto, CreateRelationDto, FetchedGetDistributorDto, UpdateBaseJourneyPlanSettingDto, UpdateDistributorDto, UploadBaseJourneyPlanDto } from './dtos';
import { BaseJourneyPlanColumns, Days, DistributorUploadTypes, Weeks } from './enums';
import { IDistributorUserRelation } from './interfaces';
import { BaseJourneyPlanService, BaseJourneyPlanSettingService, DistributorService, DistributorUploadTrackingService, DistributorUserRelationService } from './services';
import { JourneyPlanWeek } from 'src/journey-plannings/schemas/journey-plan-week.schema';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import { toListResponse } from '../utils';
import { OpCos } from 'src/config';
import { UserDetailService } from '../users/services/user-detail.service';
import { OmsService } from 'src/external/services/oms.service';

@ApiTags('Distributor')
@Controller('api/distributor')
@ApiHeader({ name: 'locale', description: 'en' })
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class DistributorController {
  constructor(
    private readonly _distributorService: DistributorService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _userAdminService: UserAdminsService,
    private readonly _userDetailService: UserDetailService,
    @Inject(forwardRef(() => OutletsService))
    private readonly _outletsService: OutletsService,
    private readonly _saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly _outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly _distributorUploadTrackingService: DistributorUploadTrackingService,
    private readonly _baseJourneyPlanService: BaseJourneyPlanService,
    private readonly _baseJourneyPlanSettingService: BaseJourneyPlanSettingService,
    private readonly _journeyPlanWeekService: JourneyPlanWeekService,
    private readonly _salerepService: SaleRepService,
    private readonly omsService: OmsService,
    private eventEmitter: EventEmitter2,
  ) {}

  @Post()
  @Roles(ConstantRoles.SUPER_USER)
  @ApiOperation({
    summary: 'Create a distributor',
  })
  async createDistributor(@Body() dto: CreateDistributorDto, @I18n() i18n) {
    const distributor = await this._distributorService.createDistributor(dto, i18n);
    return new ApiResponse(distributor);
  }

  @Get('detail/:id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiOperation({
    summary: 'Get distributor detail by object id',
  })
  async getDistributorDetail(@Param('id') id: string, @I18n() i18n) {
    const distributor = await this._distributorService.getDetailByObjectId(id, i18n);
    return new ApiResponse(distributor);
  }

  @Put(':id')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiOperation({
    summary: 'Update distributor',
  })
  async updateDistributor(@Param('id') id: string, @Body() dto: UpdateDistributorDto, @I18n() i18n) {
    const distributor = await this._distributorService.updateDistributor(id, dto, i18n);
    return new ApiResponse(distributor);
  }

  @Post('relation')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiOperation({
    summary: 'Create a relation between distributor admin user and distributor',
  })
  async createRelation(@Body() createRelationDto: CreateRelationDto) {
    const { userId, distributorId } = createRelationDto;
    const existedDistributor = await this._distributorService.findOne({ distributorId: distributorId?.trim() });
    if (!existedDistributor) {
      throw new BadRequestException('distributor.not_found');
    }
    const existedUser = await this._userAdminService.findOne({ _id: new Types.ObjectId(userId) });
    if (!existedUser) {
      throw new BadRequestException('distributor.not_found_user');
    }

    const existedRelation = await this._distributorUserRelationService.findOne({
      userAdmin: existedUser._id,
      distributor: existedDistributor._id,
    });
    if (existedRelation) {
      throw new BadRequestException('distributor.existed_relation');
    }
    const userRole = await this._userDetailService.findUserRoles({ userId: existedUser._id.toString(), isUserAdmin: true });
    if (!userRole.some((role) => role.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      throw new BadRequestException('distributor.invalid_role');
    }

    await this._distributorUserRelationService.create({
      userAdmin: existedUser._id,
      distributor: existedDistributor._id,
    });
    return new ApiResponse({
      user: existedUser,
      distributor: existedDistributor,
    });
  }

  @Post('upload/sales-rep-master-data/:distributorId')
  @ApiBadRequestResponse({ type: ApiException })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiOperation({
    summary: 'Upload sales rep master data',
  })
  async uploadSalesRepMasterData(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 })],
      }),
    )
    file: Express.Multer.File,
    @CurrentUser() currentUser: User,
    @I18n() i18n: I18nContext,
    @Param('distributorId') distributorId: string,
  ) {
    if (process.env.OPCO == OpCos.Indonesia) {
      const { success, failure } = await this._salerepService.importSalesRepID({ distributorId, currentUser, i18n, file });
      return new ApiResponse({
        completed: true,
        success,
        failure,
      });
    }
    if (process.env.OPCO == OpCos.Cambodia) {
      const { success, failure } = await this._salerepService.importSalesRepKH({ distributorId, currentUser, i18n, file });
      return new ApiResponse({
        completed: true,
        success,
        failure,
      });
    }
    const { success, failure } = await this._salerepService.importSalesRep({ distributorId, currentUser, i18n, file });
    return new ApiResponse({
      completed: true,
      success,
      failure,
    });
  }

  @Post('upload/customer-master-data/:distributorId')
  @ApiBadRequestResponse({ type: ApiException })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiOperation({
    summary: 'Upload customer master data',
  })
  async uploadCustomerMasterData(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 })],
      }),
    )
    file: Express.Multer.File,
    @CurrentUser() currentUser: User,
    @I18n() i18n: I18nContext,
    @Param('distributorId') distributorId: string,
  ) {
    if (process.env.OPCO == OpCos.Indonesia || process.env.OPCO == OpCos.Cambodia) {
      const result = await this._outletsService.uploadOutletsID(currentUser._id.toString(), distributorId, file, i18n);
      return new ApiResponse(result);
    }
    const result = await this._outletsService.uploadOutlets(currentUser._id.toString(), distributorId, file, i18n);
    return new ApiResponse(result);
  }

  @Get('')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @Serialize(FetchedGetDistributorDto)
  async getAllOutletDistributor(@CurrentUser() currentUser: User): Promise<ApiResponse> {
    const userRole = await this._userDetailService.findUserRoles({ userId: currentUser._id.toString(), isUserAdmin: true });
    if (userRole.some((r) => r.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      if (distributorAdmins) {
        return new ApiResponse(
          distributorAdmins.map((distributorAdmin) => {
            return {
              _id: distributorAdmin.distributor._id,
              distributorId: distributorAdmin.distributor.distributorId,
              distributorName: distributorAdmin.distributor.distributorName,
            };
          }),
        );
      }
      return new ApiResponse(null);
    }
    const result = await this._distributorService.findAllDistributor();
    return new ApiResponse(result);
  }

  @Get('list')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  async getListDistributors(@Query() params: GetListDistributorsArgs, @Query() order: OrderParams) {
    const result = await this._distributorService.getListDistributor({ ...params, ...order });
    const { data, totalItem, lastUpdatedAt } = result;
    return new ApiResponse({
      ...toListResponse([data, totalItem]),
      lastUpdatedAt,
    });
  }

  @Get('list/export')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  async exportListDistributors(@Query() params: GetListDistributorsArgs, @Query() order: OrderParams, @CurrentUser() currentUser, @I18n() i18n: I18nContext) {
    let file;
    if (process.env.OPCO === OpCos.Indonesia) {
      file = await this._distributorService.exportWithDepots({ currentUser, params, order, i18n });
    } else {
      file = await this._distributorService.export({ currentUser, params, order, i18n });
    }

    return new ApiResponse(file);
  }

  @Post('upload')
  @ApiBadRequestResponse({ type: ApiException })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiOperation({
    summary: 'Upload distributors',
  })
  async uploadDistributors(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 })],
      }),
    )
    file: Express.Multer.File,
    @CurrentUser() user: User,
    @I18n() i18n: I18nContext,
  ) {
    if (process.env.OPCO === OpCos.Indonesia) {
      const result = await this._distributorService.uploadDistributorsWithDepots(file, user._id.toString(), i18n);
      return new ApiResponse(result);
    }

    const result = await this._distributorService.uploadDistributors(file, user._id.toString(), i18n);
    return new ApiResponse(result);
  }

  @Get('base-journey-plan/setting')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Get base journey plan setting',
  })
  async getBaseJourneyPlanSetting(@CurrentUser() currentUser: User, @Query() query: any) {
    const { distributorId } = query;
    const [existedDistributorUserRelation] = await this._distributorUserRelationService.getDistributorUserRelation({
      'ua._id': currentUser._id,
      'dis.distributorId': distributorId,
    });
    if (!existedDistributorUserRelation) {
      throw new BadRequestException('distributor.not_found_relation');
    }
    const result = await this._baseJourneyPlanSettingService.getBaseJourneyPlanSettingByDistributor(existedDistributorUserRelation.dis._id.toString());
    return new ApiResponse(result);
  }

  @Put('base-journey-plan/setting')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Update base journey plan setting',
  })
  async updateBaseJourneyPlanSetting(@Body() updateDto: UpdateBaseJourneyPlanSettingDto, @CurrentUser() currentUser: User) {
    const { timeInterval, startingTimeframe, distributorId } = updateDto;
    const [existedDistributorUserRelation] = await this._distributorUserRelationService.getDistributorUserRelation({
      'ua._id': currentUser._id,
      'dis.distributorId': distributorId,
    });
    if (!existedDistributorUserRelation) {
      throw new BadRequestException('distributor.not_found_relation');
    }
    const existed = await this._baseJourneyPlanSettingService.findOne({ distributor: existedDistributorUserRelation.dis._id });
    if (existed) {
      await this._baseJourneyPlanSettingService.update(existed._id, {
        timeInterval,
        startingTimeframe,
      });
    } else {
      await this._baseJourneyPlanSettingService.create({
        distributor: existedDistributorUserRelation.dis._id,
        startingTimeframe,
        timeInterval,
      });
    }
    return new ApiResponse({ timeInterval, startingTimeframe });
  }

  @Post('upload/base-journey-plan')
  @ApiBadRequestResponse({ type: ApiException })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiOperation({
    summary: 'Upload base journey plan',
  })
  async uploadBaseJourneyPlan(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 })],
      }),
    )
    file: Express.Multer.File,
    @Body() uploadBaseJourneyPlanDto: UploadBaseJourneyPlanDto,
    @CurrentUser() currentUser: User,
    @I18n() i18n: I18nContext,
  ) {
    const { cycleId, distributorId } = uploadBaseJourneyPlanDto;
    const timeRangeGivenCycle = await this._journeyPlanWeekService.getTimeRangeOfGivenCycle(cycleId);
    if (!timeRangeGivenCycle) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_cycle');
    }
    const { startOfCycle, endOfCycle, allWeeks } = timeRangeGivenCycle;
    if (+new Date() - +endOfCycle > 0) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_cycle_time');
    }
    const currentDate = new Date();
    const isCurrentCycle = +startOfCycle <= +currentDate && +currentDate <= +endOfCycle;

    const [existedDistributorUserRelation] = await this._distributorUserRelationService.getDistributorUserRelation2({
      userAdmin: currentUser._id,
      distributorId: distributorId,
    });
    if (!existedDistributorUserRelation) {
      throw new BadRequestException('distributor.not_found_relation');
    }
    const excelData = await this._distributorService.handleUploadingFile(
      file,
      BaseJourneyPlanColumns.map((e) => i18n.translate(`importExport.${e}`)),
      BaseJourneyPlanColumns,
      i18n,
    );
    const groupByWeekAndUCC = this._distributorService.groupDataByFields(excelData, 'week', 'ucc');

    Object.entries(groupByWeekAndUCC).forEach(([key, value]) => {
      const [week, ucc] = key.split(':');
      if (value > 1) {
        throw new BadRequestException(
          i18n.t('distributor.base_journey_plan.invalid_number_plan_per_week', {
            args: {
              week,
              ucc,
            },
          }),
        );
      }
    });
    const groupByWeekAndDayAndSalesRepID = this._distributorService.groupDataByFields(excelData, 'week', 'day', 'salesRepId');
    const { startingTimeframe, timeInterval } = await this._baseJourneyPlanSettingService.getBaseJourneyPlanSettingByDistributor(existedDistributorUserRelation.dis._id.toString());
    const numberOfTimeSlotPerDay = this._baseJourneyPlanSettingService.calculateTimeSlotByTimeIntervalAndStartingTimeframe(startingTimeframe, timeInterval);

    Object.entries(groupByWeekAndDayAndSalesRepID).forEach(([key, value]) => {
      const [week, day, saleRepId] = key.split(':');
      if (value > numberOfTimeSlotPerDay) {
        throw new BadRequestException(
          i18n.t('distributor.base_journey_plan.invalid_number_plan_per_day', {
            args: {
              numberOfPlansPerDay: numberOfTimeSlotPerDay,
              week,
              day,
              salesRepId: saleRepId,
            },
          }),
        );
      }
    });

    const validData = [];

    const listSalesRepDistributorRelations = await this._distributorUserRelationService.getDistributorUserRelation2({
      saleRepIds: excelData.map((r) => r.salesRepId),
      distributorId,
    });

    const listActiveSOR = await this._saleRepOutletRelationService.filterActiveSaleRepOutletRelations({
      'o.ucc': { $in: excelData.map((r) => r.ucc) },
    });

    const { week: currentCycleWeek, day: currentCycleDay } = this._baseJourneyPlanService.getWeekAndDayByGivenDay(new Date(), allWeeks);

    await Promise.all(
      excelData.map((rowData) =>
        this.validateBaseJourneyPlanData(
          rowData,
          distributorId,
          i18n,
          isCurrentCycle,
          currentCycleWeek,
          currentCycleDay,
          listSalesRepDistributorRelations,
          listActiveSOR,
          validData,
          allWeeks,
        ),
      ),
    );
    if (!isCurrentCycle) {
      //await this._baseJourneyPlanService.clearBaseJourneyPlanByDistributor(existedDistributorUserRelation.dis._id.toString(), cycleId);
    }
    const baseJourneyPlans = validData.map((item) => ({
      distributor: existedDistributorUserRelation.dis._id,
      outlet: item.saleRepOutletRelation.o._id,
      saleRep: item.saleRepOutletRelation.u._id,
      day: Number(item.day),
      week: Number(item.week),
      cycle: new Types.ObjectId(cycleId),
    }));

    this.eventEmitter.emit(
      ConstantEventName.SHARED_EVENT,
      new SharedEvent({
        callback: async () => {
          await this._baseJourneyPlanService.createBaseJourneyPlans(baseJourneyPlans);
        },
      }),
    );

    await this._baseJourneyPlanService.handleJourneyPlanByDistributor(existedDistributorUserRelation.dis._id, baseJourneyPlans, allWeeks, true, isCurrentCycle, listActiveSOR, {
      startingTimeframe,
      timeInterval,
    });
    await this._distributorUploadTrackingService.create({
      distributor: existedDistributorUserRelation.dis._id,
      uploadedBy: currentUser._id,
      uploadedAt: new Date(),
      type: DistributorUploadTypes.JOURNEY_PLANNING_DATA,
    });
    return new ApiResponse(excelData);
  }

  private async validateBaseJourneyPlanData(
    rowData: Record<string, string>,
    distributorId: string,
    i18n: I18nContext,
    isCurrentCycle: boolean,
    currentCycleWeek,
    currentCycleDay,
    listSalesRepDistributorRelations: IDistributorUserRelation[],
    listActiveSOR: ISaleRepOutletAggregation[],
    validData: any[],
    allWeeksOfCycle: JourneyPlanWeek[],
  ) {
    const { ucc, salesRepId, week, day } = rowData;
    if (!ucc && !salesRepId && !week && !day) {
      return;
    }
    const existedSaleRepDistributorRelation = listSalesRepDistributorRelations.find((s) => s.u.saleRepId === salesRepId);
    if (!existedSaleRepDistributorRelation) {
      throw new BadRequestException(
        i18n.t('distributor.base_journey_plan.invalid_sales_rep_distributor_relation', {
          args: {
            distributorId,
            salesRepId,
          },
        }),
      );
    }
    const existedSaleRepOutletRelation = listActiveSOR.find((item) => item.o.ucc === ucc && item.u.saleRepId === salesRepId);
    if (!existedSaleRepOutletRelation) {
      throw new BadRequestException(
        i18n.t('distributor.base_journey_plan.invalid_sales_rep_outlet_relation', {
          args: {
            ucc,
            salesRepId,
          },
        }),
      );
    }
    if (!Object.values(Days).some((nd) => nd === Number(day))) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_day');
    }
    if (!Object.values(Weeks).some((nw) => nw === Number(week))) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_week');
    }
    if (isCurrentCycle) {
      if (Number(week) < currentCycleWeek || (Number(week) === currentCycleWeek && Number(day) < currentCycleDay)) {
        throw new BadRequestException('distributor.base_journey_plan.invalid_week_day_current_cycle');
      }
      const planedWeek = this._baseJourneyPlanService.getWeekByWeekNumber(Number(week), allWeeksOfCycle);
      const exitedJp = await this._outletJourneyPlanningService.findByOutletAndSalRepAndWeek(
        existedSaleRepOutletRelation.o._id,
        existedSaleRepOutletRelation.u._id,
        planedWeek._id,
      );

      if (exitedJp && exitedJp.visitStatus !== VisitStatus.START_VISIT) {
        throw new BadRequestException(
          i18n.t('plan.cannot_update_executed_plan', {
            args: {
              ucc,
            },
          }),
        );
      }
    }
    validData.push({ ...rowData, saleRepOutletRelation: existedSaleRepOutletRelation });
  }

  @Post('get-list-salesrep-by-distributor')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.DISTRIBUTOR_ADMIN)
  async getAllSalesrepsByDistributorObjectIds(@Body() body: { distributorObjectIds: string[] }, @I18n() i18n) {
    const distributorObjectIds = body.distributorObjectIds ? body.distributorObjectIds.map((e) => new Types.ObjectId(e)) : [];
    let listRelations = await this._distributorUserRelationService.getAllSalesrepsByDistributorObjectIds(distributorObjectIds);
    if (listRelations.length) {
      listRelations = _.chain(listRelations)
        .groupBy('distributor')
        .map((value, key) => ({ distributor: key, salesRep: value.map((e) => e.user) }))
        .value();
    }
    return new ApiResponse(listRelations);
  }

  @Post('update-region')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER)
  @ApiOperation({
    summary: 'update region',
  })
  async updateRegion(@CurrentUser() user: User, @I18n() i18n: I18nContext) {
    const res = await this.omsService.getConfigMustHaveSku(true);
    return new ApiResponse(res);
  }

  @Post('clear-cache-oms')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER)
  @ApiOperation({
    summary: 'clear cache oms',
  })
  async clearCacheOms(@Query() query: { ucc?: string; mustHaveSku?: boolean; variant?: boolean }, @CurrentUser() user: User, @I18n() i18n: I18nContext) {
    const { ucc, mustHaveSku, variant } = query;
    await this.omsService.clearAllCache(ucc, mustHaveSku, variant);
    return new ApiResponse();
  }
}
