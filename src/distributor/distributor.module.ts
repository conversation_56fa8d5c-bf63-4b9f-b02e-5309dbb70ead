import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { DsrTargetsModule } from 'src/dsr-targets/dsr-targets.module';
import { AuthModule } from '../auth/auth.module';
import { FilesModule } from '../files/files.module';
import { JourneyPlanningsModule } from '../journey-plannings/journey-plannings.module';
import { OutletsModule } from '../outlets/outlets.module';
import { SaleRepModule } from '../sale-rep/sale-rep.module';
import { UsersModule } from '../users/users.module';
import { DistributorController } from './distributor.controller';
import {
  BaseJourneyPlan,
  BaseJourneyPlanSchema,
  BaseJourneyPlanSetting,
  BaseJourneyPlanSettingSchema,
  Distributor,
  DistributorSchema,
  DistributorUploadTracking,
  DistributorUploadTrackingSchema,
  DistributorUserRelation,
  DistributorUserRelationSchema,
} from './schemas';
import { BaseJourneyPlanService, BaseJourneyPlanSettingService, DistributorService, DistributorUploadTrackingService, DistributorUserRelationService } from './services';
import { ExternalModule } from 'src/external/external.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Distributor.name,
        schema: DistributorSchema,
      },
      {
        name: DistributorUserRelation.name,
        schema: DistributorUserRelationSchema,
      },
      {
        name: DistributorUploadTracking.name,
        schema: DistributorUploadTrackingSchema,
      },
      {
        name: BaseJourneyPlan.name,
        schema: BaseJourneyPlanSchema,
      },
      {
        name: BaseJourneyPlanSetting.name,
        schema: BaseJourneyPlanSettingSchema,
      },
    ]),
    AuthModule,
    DsrTargetsModule,
    forwardRef(() => JourneyPlanningsModule),
    forwardRef(() => OutletsModule),
    forwardRef(() => UsersModule),
    forwardRef(() => SaleRepModule),
    forwardRef(() => FilesModule),
    forwardRef(() => ExternalModule),
  ],
  controllers: [DistributorController],
  providers: [DistributorService, DistributorUserRelationService, DistributorUploadTrackingService, BaseJourneyPlanSettingService, BaseJourneyPlanService],
  exports: [DistributorService, DistributorUserRelationService, DistributorUploadTrackingService, BaseJourneyPlanSettingService, BaseJourneyPlanService],
})
export class DistributorModule {}
