import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { normalizeQueryHelper } from 'src/shared/helpers';
import { SalesRepSortOrder } from '../../sale-rep/dtos';
import { BaseService } from '../../shared/services/base-service';
import { IDistributorUserRelation } from '../interfaces';
import { DistributorUserRelation, DistributorUserRelationDocument } from '../schemas';

@Injectable()
export class DistributorUserRelationService extends BaseService<DistributorUserRelation> {
  constructor(
    @InjectModel(DistributorUserRelation.name)
    private readonly _model: Model<DistributorUserRelationDocument>,
  ) {
    super();
    this.model = _model;
  }

  /**
   *
   * @param filter
   * @description
   * - find by user._id => filter = {'u._id': user._id}
   * - find by user._id and distributorId => filter = {'u._id': user._id, 'dis.distributorId': distributorId}
   * - find by saleRepId and distributorId => filter = {'u.saleRepId': user._id, 'dis.distributorId': distributorId}
   */
  async getDistributorUserRelation(filter: Record<string, any>): Promise<Array<IDistributorUserRelation>> {
    const aggregation = this.createDistributorUserRelationAggregation();
    return aggregation.match(filter).exec();
  }

  async getDistributorUserRelation2({ userAdmin = null, saleRepIds = [], distributorId }): Promise<Array<IDistributorUserRelation>> {
    const aggregation = this._model.aggregate().project({
      _id: 0,
      dur: '$$ROOT',
    });
    if (userAdmin) {
      aggregation.match({
        'dur.userAdmin': new Types.ObjectId(userAdmin),
      });
    }
    if (distributorId) {
      aggregation
        .lookup({
          localField: 'dur.distributor',
          from: 'distributors',
          foreignField: '_id',
          as: 'dis',
        })
        .unwind({
          path: '$dis',
          preserveNullAndEmptyArrays: true,
        })
        .match({
          'dis.distributorId': distributorId,
        });
    }
    if (saleRepIds.length) {
      aggregation
        .lookup({
          localField: 'dur.user',
          from: 'users',
          foreignField: '_id',
          as: 'u',
        })
        .unwind({
          path: '$u',
          preserveNullAndEmptyArrays: true,
        })
        .match({
          'u.saleRepId': { $in: saleRepIds },
        });
    }
    return aggregation.exec();
  }

  async getSalesRepObjectIdsInDistributor(disId: string): Promise<string[]> {
    const raw = await this._model
      .aggregate()
      .match({
        distributor: disId,
        user: {
          $ne: null,
        },
      })
      .project({
        _id: 0,
        salesRepId: '$user',
      })
      .exec();
    return raw.map((item) => item.salesRepId.toString()).filter((item, index, self) => self.indexOf(item) === index);
  }

  async findByUserId(userId: string): Promise<DistributorUserRelation> {
    return this._model
      .findOne({
        user: new Types.ObjectId(userId),
      })
      .populate('user distributor')
      .exec();
  }

  async findByUserIds(userIds: string[]): Promise<DistributorUserRelation[]> {
    return this._model
      .find({
        user: {
          $in: userIds.map((id) => new Types.ObjectId(id)),
        },
      })
      .populate('user distributor')
      .exec();
  }

  async findByUserAdminId(userId: string): Promise<DistributorUserRelation> {
    return this._model
      .findOne({
        userAdmin: new Types.ObjectId(userId),
      })
      .populate('userAdmin distributor')
      .exec();
  }

  async findManyByUserAdminId(userId: string): Promise<DistributorUserRelation[]> {
    return this._model
      .find({
        userAdmin: new Types.ObjectId(userId),
      })
      .populate('userAdmin distributor')
      .exec();
  }

  async findAllByUserAdminId(userId: string): Promise<DistributorUserRelation[]> {
    return this._model
      .find({
        userAdmin: new Types.ObjectId(userId),
      })
      .populate('userAdmin distributor')
      .exec();
  }

  async getSalesRep(salesRepUUIDs: string[], distributorIds: string[], skip = 0, limit = 1, sort: Partial<SalesRepSortOrder> = { updatedAt: -1 }, search?: string) {
    const aggregation = this.createDistributorUserRelationAggregation(false);

    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      aggregation.match({
        $or: [
          {
            'u.username': new RegExp(normalizedQuery, 'i'),
          },
          {
            'u.mobilePhone': new RegExp(normalizedQuery, 'i'),
          },
          {
            'u.saleRepId': new RegExp(normalizedQuery, 'i'),
          },
        ],
      });
    }

    // aggregation
    //   .lookup({
    //     localField: 'dur.user',
    //     from: 'salerepffcstores',
    //     foreignField: 'saleRep',
    //     as: 'srffc',
    //   })
    //   .unwind({
    //     path: '$srffc',
    //     preserveNullAndEmptyArrays: false,
    //   });
    let filterSaleRep = {};
    if (salesRepUUIDs.length) {
      filterSaleRep = { _id: { $in: salesRepUUIDs.map((sid) => new Types.ObjectId(sid)) } };
    }
    const matchCondition: Record<string, any> = { ...filterSaleRep };
    if (distributorIds) {
      matchCondition.distributorId = { $in: distributorIds };
    }
    return aggregation
      .project({
        _id: '$u._id',
        name: '$u.username',
        firstName: '$u.firstname',
        lastName: '$u.lastname',
        mobilePhone: '$u.mobilePhone',
        mobilePhoneCode: '$u.mobilePhoneCode',
        saleRepId: '$u.saleRepId',
        status: '$u.saleRepStatus',
        // dotUsername: '$srffc.customerUserName',
        distributorId: '$dis.distributorId',
        distributorName: '$dis.distributorName',
        updatedAt: '$u.updatedAt',
        isTestAccount: '$u.isTestAccount',
        geoAddress: '$u.geoAddress',
      })
      .match(matchCondition)
      .sort(sort)
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      })
      .exec();
  }

  async getLastUpdatedSalesRep(salesRepUUIDs: string[], distributorIds: string[]) {
    const aggregation = this.createDistributorUserRelationAggregation();
    // .lookup({
    //   localField: 'dur.user',
    //   from: 'salerepffcstores',
    //   foreignField: 'saleRep',
    //   as: 'srffc',
    // })
    // .unwind({
    //   path: '$srffc',
    //   preserveNullAndEmptyArrays: false,
    // });
    let filterSaleRep = {};
    if (salesRepUUIDs.length) {
      filterSaleRep = { _id: { $in: salesRepUUIDs.map((sid) => new Types.ObjectId(sid)) } };
    }
    const matchCondition: Record<string, any> = { ...filterSaleRep, distributorId: { $in: distributorIds } };
    const [lastUpdatedSalesRep] = await aggregation
      .project({
        _id: '$u._id',
        distributorId: '$dis.distributorId',
        updatedAt: '$u.updatedAt',
      })
      .match(matchCondition)
      .sort({ updatedAt: -1 })
      .exec();
    return lastUpdatedSalesRep;
  }

  async getAllSalesRepOfGivenDistributor(distributorId: string) {
    return this.createDistributorUserRelationAggregation()
      .project({
        _id: '$u._id',
        name: '$u.username',
        mobilePhone: '$u.mobilePhone',
        email: { $ifNull: ['$u.email', ''] },
        saleRepId: '$u.saleRepId',
        saleRepStatus: '$u.saleRepStatus',
        distributorId: '$dis.distributorId',
        distributorName: '$dis.distributorName',
      })
      .match({ saleRepId: { $ne: null }, distributorId })
      .exec();
  }

  async getJourneyPlaningFollowByRelationDistributorUser(userId: string, skip = 0, limit = 1) {
    const aggregate = await this._model
      .aggregate()
      .match({ user: new Types.ObjectId(userId) })
      .project({
        _id: 0,
        dur: '$$ROOT',
      })
      .lookup({
        localField: 'dur.user',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: true,
      })
      // .lookup({
      //   localField: 'dur.userAdmin',
      //   from: 'useradmins',
      //   foreignField: '_id',
      //   as: 'ua',
      // })
      // .unwind({
      //   path: '$ua',
      //   preserveNullAndEmptyArrays: true,
      // })
      .lookup({
        localField: 'dur.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'dis',
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'du.user',
        from: 'salerepoutletrelations',
        foreignField: 'saleRep',
        as: 'so',
      })
      .unwind({
        path: '$so',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'dur.user',
        from: 'outletjourneyplannings',
        foreignField: 'saleRep',
        as: 'oj',
      })
      .unwind({
        path: '$oj',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'so.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'ot',
      })
      .unwind({
        path: '$ot',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'oj.week',
        from: 'journeyplanweeks',
        foreignField: '_id',
        as: 'ojw',
      })
      .unwind({
        path: '$ojw',
        preserveNullAndEmptyArrays: true,
      })

      .project({
        userId: '$u._id',
        ucc: '$ot._ucc',
        outletId: '$ot._id',
        outletName: '$ot.name',
        outletClass: '$ot.outletClass',
        outletArea: { $ifNull: ['$ot.area', ''] },
        outletAddress: '$ot.address',
        saleRepId: '$u.saleRepId',
        distributorName: '$dis.distributorName',
        distributorId: '$dis.distributorId',
        visitedDay: '$oj.visitedDay',
        visitStatus: '$oj.visitStatus',
        day: '$oj.day',
        outletJourneyPlanningId: '$oj._id',
        weekId: '$ojw._id',
        weekName: '$ojw.weekName',
        startTime: '$ojw.startTime',
      })

      .match({ userId: new Types.ObjectId(userId) })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      })
      .exec();
    return aggregate;
  }

  createDistributorUserRelationAggregation(preserveNullAndEmptyArraysSalesrep = true) {
    return this._model
      .aggregate()
      .project({
        _id: 0,
        dur: '$$ROOT',
      })
      .lookup({
        localField: 'dur.user',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: preserveNullAndEmptyArraysSalesrep,
      })
      .lookup({
        localField: 'dur.userAdmin',
        from: 'useradmins',
        foreignField: '_id',
        as: 'ua',
      })
      .unwind({
        path: '$ua',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'dur.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'dis',
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: false,
      });
  }

  createDistributorUserAdminRelationAggregation() {
    return this._model
      .aggregate()
      .project({
        _id: 0,
        dur: '$$ROOT',
      })
      .lookup({
        localField: 'dur.userAdmin',
        from: 'useradmins',
        foreignField: '_id',
        as: 'u',
      })
      .lookup({
        localField: 'dur.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'dis',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: false,
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: false,
      });
  }
  async getAllSalesrepsByDistributorObjectIds(distributorObjectIds: Types.ObjectId[]) {
    return this._model
      .aggregate()
      .match({
        distributor: { $in: distributorObjectIds },
        user: {
          $ne: null,
        },
      })
      .lookup({
        localField: 'user',
        from: 'users',
        foreignField: '_id',
        as: 'user',
      })
      .unwind({
        path: '$user',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'user.isActive': true,
      })
      .project({
        'user._id': 1,
        'user.username': 1,
        'user.saleRepId': 1,
        distributor: 1,
      })
      .exec();
  }

  async findOneUserAdminRelationAndUpdateData(userAdmin: any, newRelations: any) {
    await this._model.deleteMany({ userAdmin: userAdmin._id });

    // Create new relation
    return this._model.insertMany(newRelations);
  }

  async findOneUserRelationAndUpdateData(user: any, distributor: any) {
    // First remove any existing relations for this userAdmin
    // await this._model.deleteMany({ user: user._id });

    // Create new relation
    return this._model.findOneAndUpdate(
      {
        user: user._id,
        distributor: distributor._id,
      },
      {
        $set: {
          user: user._id,
          distributor: distributor._id,
          userAdmin: null,
          isActive: true,
          isDeleted: false,
          updatedAt: new Date(),
        },
      },
      { upsert: true, new: true },
    );
  }
}
