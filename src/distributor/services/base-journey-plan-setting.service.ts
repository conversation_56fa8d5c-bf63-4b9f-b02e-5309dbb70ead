import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import * as moment from 'moment-timezone';

import { BaseService } from '../../shared/services/base-service';
import { BaseJourneyPlanSetting, BaseJourneyPlanSettingDocument } from '../schemas';

@Injectable()
export class BaseJourneyPlanSettingService extends BaseService<BaseJourneyPlanSetting> {
  constructor(
    @InjectModel(BaseJourneyPlanSetting.name)
    private readonly _model: Model<BaseJourneyPlanSettingDocument>,
  ) {
    super();
    this.model = _model;
  }

  async getBaseJourneyPlanSettingByDistributor(distributorUUID: string): Promise<Record<string, any>> {
    const existed = await this._model.findOne({ distributor: new Types.ObjectId(distributorUUID) });
    if (existed) {
      return {
        timeInterval: existed.timeInterval,
        startingTimeframe: existed.startingTimeframe,
      };
    }
    return {
      timeInterval: 15,
      startingTimeframe: '08:00',
    };
  }

  calculateTimeSlotByTimeIntervalAndStartingTimeframe(startingTimeframe: string, timeInterval: number) {
    const currentDay = new Date().toISOString().split('T')[0];
    const startTime = moment(`${currentDay}T${startingTimeframe}`);
    const endTime = moment(`${currentDay}T23:59`);
    let count = 0;
    while (startTime.isBefore(endTime)) {
      count++;
      startTime.add(timeInterval, 'minutes');
    }
    return count;
  }

  calculateTimeSlotByTimeIntervalAndStartingTime(startingTime: string, timeInterval: number) {
    const startTime = moment(startingTime);
    const endTime = moment(`${startingTime}T23:59`);
    let count = 0;
    while (startTime.isBefore(endTime)) {
      count++;
      startTime.add(timeInterval, 'minutes');
    }
    return count;
  }
}
