import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import * as moment from 'moment-timezone';
import { Moment } from 'moment-timezone';

import { BaseService } from '../../shared/services/base-service';
import { BaseJourneyPlan, BaseJourneyPlanDocument, Distributor } from '../schemas';
import { OutletJourneyPlanningService } from '../../journey-plannings/services/outlet-journey-planning.service';
import { BaseJourneyPlanSettingService } from './base-journey-plan-setting.service';
import { JourneyPlanWeekService } from '../../journey-plannings/services/journey-plan-week.service';
import { WeekNameType } from '../../journey-plannings/enums/week-type.enum';
import { JourneyPlanWeek } from '../../journey-plannings/schemas/journey-plan-week.schema';
import { DistributorUserRelationService } from './distributor-user-relation.service';
import { SaleRepOutletRelationService } from '../../outlets/services/sale-rep-outlet-relation.service';
import { DistributorService } from './distributor.service';
import { Days, Weeks } from '../enums';
import { User } from '../../users/schemas/user.schema';
import { Outlet } from '../../outlets/schemas/outlet.schema';
import { isEmptyObjectOrArray, sleep } from '../../utils';
import { JourneyPlanCycle } from '../../journey-plannings/schemas/journey-plan-cycle.schema';
import { ISaleRepOutletAggregation } from '../../outlets/interfaces';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import * as _ from 'lodash';
import { UsersService } from 'src/users/services/users.service';
import { OpCos } from 'src/config';

@Injectable()
export class BaseJourneyPlanService extends BaseService<BaseJourneyPlan> {
  constructor(
    @InjectModel(BaseJourneyPlan.name)
    private readonly _model: Model<BaseJourneyPlanDocument>,
    private readonly _outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly _baseJourneyPlanSettingService: BaseJourneyPlanSettingService,
    private readonly _journeyPlanWeekService: JourneyPlanWeekService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly _distributorService: DistributorService,
    @Inject(forwardRef(() => UsersService))
    private readonly _usersService: UsersService,
  ) {
    super();
    this.model = _model;
  }

  async createBaseJourneyPlans(createDto) {
    for (let i = 0; i < createDto.length; i++) {
      const data = createDto[i];
      await this._model.updateOne(_.pick(data, ['distributor', 'outlet', 'saleRep', 'week', 'cycle']), data, { upsert: true });
      if (i % 100 === 0) {
        await sleep(150);
      }
    }
    // await Promise.all(
    //   createDto.map(async (data) => {
    //     await this._model.updateOne(_.pick(data, ['distributor', 'outlet', 'saleRep', 'week', 'cycle']), data, { upsert: true });
    //   }),
    // );
  }

  async clearBaseJourneyPlanByDistributor(distributorUUID: string, cycleId: string) {
    return this._model.deleteMany({
      distributor: new Types.ObjectId(distributorUUID),
      cycle: new Types.ObjectId(cycleId),
    });
  }

  // async getAllBaseJourneyPlanGroupByDistributor() {
  //   return this._model
  //     .aggregate()
  //     .group({
  //       _id: '$distributor',
  //       baseJourneyPlans: { $push: { outlet: '$outlet', saleRep: '$saleRep', day: '$day', week: '$week' } },
  //     })
  //     .project({
  //       _id: 0,
  //       distributor: '$_id',
  //       baseJourneyPlans: '$baseJourneyPlans',
  //     })
  //     .exec();
  // }

  async cloneJourneyPlan(cycleId: string, shouldClearData = true) {
    const allWeekOfGivenCycle = await this._journeyPlanWeekService.getWeeksOfGivenCycle(cycleId);
    const previousCycleOfGivenCycle = await this._journeyPlanWeekService.getPreviousCycleOfGivenCycle(cycleId);
    const allDistributors = await this._distributorService.findAll({});
    await Promise.all(
      allDistributors.map((distributor) => this.cloneJourneyPlanForDistributor(distributor, cycleId, allWeekOfGivenCycle, shouldClearData, previousCycleOfGivenCycle)),
    );
  }

  private async cloneJourneyPlanForDistributor(
    distributor: Distributor,
    cycleId: string,
    allWeekOfGivenCycle: JourneyPlanWeek[],
    shouldClearData: boolean,
    previousCycleOfGivenCycle: JourneyPlanCycle,
  ) {
    const baseJourneyPlansByDistributor = await this._model.find({
      distributor: distributor._id,
      cycle: new Types.ObjectId(cycleId),
    });
    if (baseJourneyPlansByDistributor?.length > 0) {
      await this.handleJourneyPlanByDistributor(distributor._id, baseJourneyPlansByDistributor, allWeekOfGivenCycle, shouldClearData);
    } else {
      if (previousCycleOfGivenCycle) {
        const baseJourneyPlansByDistributorOfPreviousCycle = await this._model.find({
          distributor: distributor._id,
          cycle: previousCycleOfGivenCycle._id,
        });
        if (baseJourneyPlansByDistributorOfPreviousCycle?.length > 0) {
          const clonedBaseJourneyPlanForGivenCycle = baseJourneyPlansByDistributorOfPreviousCycle.map((item) => ({
            distributor: distributor._id,
            outlet: item.outlet,
            saleRep: item.saleRep,
            day: item.day,
            week: item.week,
            cycle: new Types.ObjectId(cycleId),
          }));
          await this.createBaseJourneyPlans(clonedBaseJourneyPlanForGivenCycle);
          await this.handleJourneyPlanByDistributor(distributor._id, baseJourneyPlansByDistributorOfPreviousCycle, allWeekOfGivenCycle, shouldClearData);
        }
      }
    }
  }

  async getJourneyPlansByDistributor(distributor, allWeeksOfCycle: JourneyPlanWeek[]) {
    const distributorSalesReps = await this._distributorUserRelationService.findAll({
      user: { $ne: null },
      distributor: distributor,
    });
    const salesRepIds = distributorSalesReps.map((ds) => ds.user.toString());
    return this._outletJourneyPlanningService.findJourneyPlansBySalesRepIdsAndCycleWeeks(salesRepIds, allWeeksOfCycle);
  }

  async handleJourneyPlanByDistributor<U>(
    distributor,
    baseJourneyPlans,
    allWeeksOfCycle: JourneyPlanWeek[],
    shouldClearData: boolean,
    isCurrentCycle?: boolean,
    listActiveSORs = null,
    baseJourneyPlanSetting = null,
  ) {
    const distributorSalesReps = await this._distributorUserRelationService.findAll({
      user: { $ne: null },
      distributor: distributor,
    });
    const salesRepIds = await Promise.all(distributorSalesReps.map((ds) => ds.user.toString()));
    if (shouldClearData) {
      if (!isCurrentCycle) {
        await this._outletJourneyPlanningService.deleteJourneyPlansBySalesRepIdsAndWeeks(salesRepIds, allWeeksOfCycle);
      }
    } else {
      const existedData = await this._outletJourneyPlanningService.findJourneyPlansBySalesRepIdsAndWeeks(salesRepIds, allWeeksOfCycle);
      if (existedData?.length) {
        return;
      }
    }
    const calculatePlannedTimeByDayAndWeekOfSalesRep: Record<string, Date> = {};
    const { startingTimeframe, timeInterval } = baseJourneyPlanSetting?.startingTimeframe
      ? baseJourneyPlanSetting
      : await this._baseJourneyPlanSettingService.getBaseJourneyPlanSettingByDistributor(distributor.toString());

    const listActiveSOR = listActiveSORs?.length
      ? listActiveSORs
      : await this._saleRepOutletRelationService.filterActiveSaleRepOutletRelations({
          'o._id': { $in: await Promise.all(baseJourneyPlans.map((bjp) => new Types.ObjectId(bjp.outlet))) },
        });

    for (let i = 0; i < baseJourneyPlans.length; i++) {
      const jp = baseJourneyPlans[i];
      await this.createJourneyPlan(jp, allWeeksOfCycle, calculatePlannedTimeByDayAndWeekOfSalesRep, timeInterval, startingTimeframe, listActiveSOR);
      if (i % 200 === 0) {
        await sleep(150);
      }
    }
    return true;
  }

  private async createJourneyPlan(
    jp,
    allWeeksOfCycle: JourneyPlanWeek[],
    calculatePlannedTimeByDayAndWeekOfSalesRep: Record<string, Date>,
    timeInterval: number,
    startingTimeframe: string,
    listActiveSOR: ISaleRepOutletAggregation[],
  ) {
    const { outlet, saleRep, day, week } = jp;
    const existedSaleRepOutletRelation = listActiveSOR.find((item) => item.o._id.toString() === outlet.toString() && item.u._id.toString() === saleRep.toString());
    if (!existedSaleRepOutletRelation) {
      return;
    }
    const plannedWeek = this.getWeekByWeekNumber(week, allWeeksOfCycle);
    const startOfWeek = plannedWeek.startTime;
    const dayAndWeekOfSalesRepKey = `${saleRep}_${week}_${day}`;
    const dayAndWeekOfSalesRepValue = calculatePlannedTimeByDayAndWeekOfSalesRep[dayAndWeekOfSalesRepKey];
    let plannedDateTime: Moment;
    let plannedDate: Moment;
    if (dayAndWeekOfSalesRepValue) {
      plannedDate = moment(dayAndWeekOfSalesRepValue);
      plannedDateTime = plannedDate.clone().add(timeInterval, 'minutes');
    } else {
      const [hour, minute] = startingTimeframe.split(':');
      plannedDate = moment(startOfWeek).add(day - 1, 'days');
      plannedDateTime = plannedDate.clone().add(Number(hour), 'hours').add(Number(minute), 'minutes');
    }
    calculatePlannedTimeByDayAndWeekOfSalesRep[dayAndWeekOfSalesRepKey] = plannedDateTime.toDate();
    if (week === Weeks.WEEK_4 && day === Days.DAY_7 && !plannedDateTime.isSame(plannedDate, 'day')) {
      return;
    }
    const exitedJp = await this._outletJourneyPlanningService.findByOutletAndSalRepAndWeek(outlet, saleRep, plannedWeek._id);
    if (exitedJp) {
      if (exitedJp.visitStatus === VisitStatus.START_VISIT) {
        exitedJp
          .updateOne({
            saleRep: new Types.ObjectId(saleRep),
            outlet: new Types.ObjectId(outlet),
            day: plannedDateTime.toDate(),
            week: plannedWeek._id,
            $unset: {
              missedReason: 1,
              outletReport: 1,
            },
          })
          .then();
      }
      return;
    }
    this._outletJourneyPlanningService
      .deleteByCondition({
        saleRep: new Types.ObjectId(saleRep),
        outlet: new Types.ObjectId(outlet),
        week: null,
        day: null,
      })
      .then();
    await this._outletJourneyPlanningService.create({
      saleRep: new Types.ObjectId(saleRep),
      outlet: new Types.ObjectId(outlet),
      day: plannedDateTime.toDate(),
      week: plannedWeek._id,
    });
    if (process.env.OPCO == OpCos.Cambodia) {
      await this._usersService.update(saleRep, { isAddressChanged: true });
    }
  }

  async getBaseJourneyPlansByCycleIdAndDistributorUUID(cycleId: string, distributorUUID: string): Promise<Array<BaseJourneyPlan>> {
    return this._model
      .find({
        cycle: new Types.ObjectId(cycleId),
        distributor: new Types.ObjectId(distributorUUID),
      })
      .populate('outlet saleRep cycle')
      .exec();
  }

  getWeekByWeekNumber(week, allWeeksOfCycle: JourneyPlanWeek[]) {
    switch (week) {
      case 1:
        return allWeeksOfCycle.find((w) => w.weekName === WeekNameType.WEEK_1);
      case 2:
        return allWeeksOfCycle.find((w) => w.weekName === WeekNameType.WEEK_2);
      case 3:
        return allWeeksOfCycle.find((w) => w.weekName === WeekNameType.WEEK_3);
      case 4:
        return allWeeksOfCycle.find((w) => w.weekName === WeekNameType.WEEK_4);
      default:
        break;
    }
  }

  getWeekAndDayByGivenDay(date = new Date(), allWeeksOfCycle: JourneyPlanWeek[]) {
    const firstWeek = allWeeksOfCycle.find((w) => w.weekName === WeekNameType.WEEK_1);
    const diffDays = moment(date).tz(process.env.TZ).startOf('day').diff(moment(firstWeek.startTime), 'days');
    const day = (diffDays % 7) + 1;
    const week = Math.floor(diffDays / 7) + 1;
    return { day, week };
  }

  async countByGivenCondition(condition: Record<string, any>) {
    return this._model.count(condition);
  }

  async assignBasePlanned(saleRepOld: User, saleRep: User, outlet: Outlet) {
    let result = true;
    //get base journey plan by outlet and old salerep
    const oldBaseData = await this._model
      .find({
        outlet: outlet._id,
        saleRep: saleRepOld._id,
      })
      //.populate('cycle')
      .exec();

    if (isEmptyObjectOrArray(oldBaseData)) {
      return true;
    }
    const distributorUser = await this._distributorUserRelationService.findByUserId(saleRep._id);
    const { startingTimeframe, timeInterval } = await this._baseJourneyPlanSettingService.getBaseJourneyPlanSettingByDistributor(distributorUser.distributor._id.toString());
    const totalSlotPerDay = this._baseJourneyPlanSettingService.calculateTimeSlotByTimeIntervalAndStartingTimeframe(startingTimeframe, timeInterval);

    const createData = [];
    //loop and check slot -> error ->throw exception
    for (const base of oldBaseData) {
      //new number slot
      const newNumberSlot = oldBaseData.filter((d) => d.cycle == base.cycle && d.week == base.week && d.day == base.day)?.length || 0;

      //check free slot
      const currentNumberSlot = await this._model.count({
        cycle: base.cycle,
        week: base.week,
        day: base.day,
        saleRep: saleRep._id,
      });

      if (newNumberSlot > totalSlotPerDay - currentNumberSlot) {
        result = false;
        return false;
      }

      createData.push({
        ...base,
        saleRep: saleRep,
      });
    }
    if (result) {
      //add new data for new sales rep
      for (const row of createData) {
        await this.create(row);
      }

      //delete old data
      for (const row of oldBaseData) {
        await this.delete(row._id);
      }
    }
    return result;
  }
}
