import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { BaseService } from '../../shared/services/base-service';
import { DistributorUploadTracking, DistributorUploadTrackingDocument } from '../schemas';
import { DistributorUploadTypes } from '../enums';

@Injectable()
export class DistributorUploadTrackingService extends BaseService<DistributorUploadTracking> {
  constructor(
    @InjectModel(DistributorUploadTracking.name)
    private readonly _model: Model<DistributorUploadTrackingDocument>,
  ) {
    super();
    this.model = _model;
  }

  async getLastUploaded(distributorUUID: string, type: DistributorUploadTypes): Promise<Date> {
    let filter: Record<string, any> = {
      type,
    };
    if (distributorUUID) {
      filter.distributor = distributorUUID;
    }
    const [lastUploadedDocument] = await this._model.find(filter).sort({ uploadedAt: -1 }).limit(1);
    return lastUploadedDocument?.uploadedAt ?? null;
  }
}
