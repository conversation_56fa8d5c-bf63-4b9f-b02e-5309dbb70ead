import { BadRequestException, ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import * as xlsx from 'xlsx';
import { WorkBook } from 'xlsx';

import * as moment from 'moment-timezone';
import { I18nContext } from 'nestjs-i18n';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { normalizeQueryHelper } from 'src/shared/helpers';
import { PaginationResponse, Reason, UploadDataResponse } from 'src/utils/interfaces';
import { BaseService } from '../../shared/services/base-service';
import { GetListDistributorsArgs } from '../args';
import { CreateDistributorDto, UpdateDistributorDto } from '../dtos';
import { DistributorBaseColumns, DepotColumns } from '../enums';
import { UploadDistributor } from '../interfaces';
import { Distributor, DistributorDocument } from '../schemas';
import { listLangSupportImport } from 'src/i18n';
import { UploadDepot } from '../interfaces/upload-depot';
import { DepotStatus } from '../enums/depot-status.enum';
import { Depot } from '../dtos/depot.dto';
import { User } from 'src/users/schemas/user.schema';
import { FilesService } from 'src/files/services';

@Injectable()
export class DistributorService extends BaseService<Distributor> {
  constructor(
    @InjectModel(Distributor.name)
    private readonly _model: Model<DistributorDocument>,
    private readonly filesService: FilesService,
  ) {
    super();
    this.model = _model;
  }

  findAllDistributor() {
    return this._model.find().sort({ distributorName: 1 });
  }

  async handleUploadingFile(
    file: Express.Multer.File,
    columnNames: Array<string>,
    fieldNames: Array<string>,
    i18n: I18nContext,
    sheetIndex = 0,
  ): Promise<Array<Record<string, string>>> {
    const wb: WorkBook = xlsx.read(file.buffer, { type: 'buffer' });
    let sheet;
    let range;
    try {
      sheet = wb.Sheets[wb.SheetNames[sheetIndex]];
      range = xlsx.utils.decode_range(sheet['!ref']);
    } catch (e) {
      throw new BadRequestException('distributor.invalid_excel_format');
    }

    const objectFNameAndCName = {};
    for (const iterator of fieldNames) {
      objectFNameAndCName[iterator] = [];
      for (const iterator2 of listLangSupportImport) {
        objectFNameAndCName[iterator].push(i18n.t(`importExport.${iterator}`, { lang: iterator2 })?.toLocaleLowerCase());
      }
    }

    const tempIndex = {};
    for (let i = 0; i <= range.e.c; i++) {
      const cName = sheet[xlsx.utils.encode_cell({ c: i, r: 0 })]?.v?.trim()?.toLocaleLowerCase();
      for (const key in objectFNameAndCName) {
        if (Object.prototype.hasOwnProperty.call(objectFNameAndCName, key)) {
          const element = objectFNameAndCName[key];
          if (element.includes(cName)) {
            tempIndex[`${key}`] = i;
            break;
          }
        }
      }
    }

    for (const [index, key] of fieldNames.entries()) {
      if (!Object.keys(tempIndex).includes(key)) {
        const columnName = columnNames[index];
        throw new BadRequestException(
          i18n.t('distributor.invalid_excel_format_column', {
            args: {
              columnName,
            },
          }),
        );
      }
    }

    const result = [];
    for (let row = 1; row <= range.e.r; row++) {
      const rowData: Record<string, any> = {};
      for (const key in tempIndex) {
        if (Object.prototype.hasOwnProperty.call(tempIndex, key)) {
          const index = tempIndex[key];
          const text = sheet[xlsx.utils.encode_cell({ c: index, r: row })]?.v;
          rowData[key] = text ? text.toString()?.trim() : '';
        }
      }
      let check = false;
      for (const key in rowData) {
        if (Object.prototype.hasOwnProperty.call(rowData, key)) {
          const element = rowData[key];
          if (element) {
            check = true;
            break;
          }
        }
      }
      if (check) {
        result.push(rowData);
      }
    }
    return result;
  }

  groupDataByFields(data: Record<string, any>, ...fields: string[]): Record<string, number> {
    return data.reduce((previousValue, currentValue) => {
      const key = fields.map((field) => currentValue[field]).join(':');
      if (previousValue[key]) {
        previousValue[key]++;
      } else {
        previousValue[key] = 1;
      }
      return previousValue;
    }, {});
  }

  async getListDistributor(params: GetListDistributorsArgs & OrderParams): Promise<PaginationResponse<Distributor> & { lastUpdatedAt?: Date }> {
    const { search, offset, limit, orderBy, orderDesc } = params;
    const aggregation = this._model.aggregate();

    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      aggregation.match({
        $or: [{ distributorName: new RegExp(normalizedQuery, 'i') }, { distributorId: new RegExp(normalizedQuery, 'i') }],
      });
    }
    let sort: Record<string, any> = {
      createdAt: -1,
    };
    if (orderBy) {
      sort = { [`${orderBy}`]: orderDesc || 1 };
    }

    const result = await aggregation
      .sort(sort)
      .facet({
        totalRecords: [
          {
            $count: 'totalItem',
          },
        ],
        data: [
          {
            $skip: offset ? +offset : 0,
          },
          {
            $limit: limit ? +limit : 10,
          },
        ],
      })
      .exec();

    const [{ totalRecords, data }] = result;

    if (!data.length || !totalRecords.length) {
      return {
        data: [],
        totalItem: 0,
      };
    }

    const [lastUpdatedDistributor] = await this._model.aggregate().sort({ updatedAt: -1 }).limit(1).exec();

    return {
      data,
      totalItem: totalRecords[0].totalItem,
      lastUpdatedAt: lastUpdatedDistributor.updatedAt,
    };
  }

  async createDistributor(dto: CreateDistributorDto, i18n: I18nContext) {
    const { distributorId, distributorName } = dto;

    const allDistributors = await this.findAll({});
    const existedDistributorId = allDistributors.find((distributor) => distributor.distributorId === distributorId);
    if (existedDistributorId) {
      throw new ConflictException(i18n.t('distributor.existed_id'));
    }

    const depots = (dto.depots || []).map((item) => ({ ...item, id: item.id.trim(), name: item.name.trim() }));

    const existedDepotIds = allDistributors.reduce((pre, curr) => [...pre, ...(curr.depots?.map((depot) => depot.id) || [])], []);
    const duplicateIds = depots.filter((depot) => existedDepotIds.includes(depot.id)).map((item) => item.id);

    if (duplicateIds.length) {
      throw new ConflictException(`${i18n.t('depot.duplicateId')} (${duplicateIds.join(', ')})`);
    }

    return this.create({
      distributorId,
      distributorName,
      depots,
    });
  }

  async getDetailByObjectId(id: string, i18n: I18nContext) {
    const distributor = await this.findOne({
      _id: new Types.ObjectId(id),
    });
    if (!distributor) {
      throw new NotFoundException(i18n.t('distributor.not_found'));
    }
    return distributor;
  }

  async updateDistributor(id: string, dto: UpdateDistributorDto, i18n: I18nContext) {
    const distributor = await this.findOne({
      _id: new Types.ObjectId(id),
    });
    if (!distributor) {
      throw new NotFoundException(i18n.t('distributor.not_found'));
    }
    // check new distributor id is exist or not
    const { distributorId } = dto;
    const allDistributors = await this.findAll({});

    if (distributorId) {
      const existedDistributorId = allDistributors.find((distributor) => distributor.distributorId === distributorId && distributorId !== distributor.distributorId);
      if (existedDistributorId) {
        throw new ConflictException(i18n.t('distributor.existed_id'));
      }
    }

    if (dto.depots) {
      const oldDepots = distributor.depots || [];
      const oldDepotIds = oldDepots.map((oldDepot) => oldDepot.id);
      const newDepots = dto.depots.filter((depot) => !oldDepotIds.includes(depot.id));

      const existedDepotIds = allDistributors.reduce((pre, curr) => [...pre, ...(curr.depots?.map((depot) => depot.id) || [])], []);
      const duplicateIds = newDepots.filter((depot) => existedDepotIds.includes(depot.id)).map((item) => item.id);

      if (duplicateIds.length) {
        throw new ConflictException(`${i18n.t('depot.duplicateId')} (${duplicateIds.join(', ')})`);
      }
    }

    return this.update(id, {
      ...dto,
      depots: (dto.depots || distributor.depots || []).map((item) => ({ ...item, id: item.id.trim(), name: item.name.trim() })),
    });
  }

  private checkDuplicateDepotId({ depotId, depotIds }: { depotId: string; depotIds: string[] }) {
    const counter = depotIds.reduce((pre, curr) => {
      if (!pre[curr]) {
        return {
          ...pre,
          [curr]: 1,
        };
      }

      return {
        ...pre,
        [curr]: pre[curr] + 1,
      };
    }, {} as Record<string, number>);

    const count = counter[depotId] || 0;

    return count > 1;
  }

  async uploadDistributors(file: Express.Multer.File, userId: string, i18n: I18nContext): Promise<UploadDataResponse> {
    const distributors = (await this.handleUploadingFile(
      file,
      DistributorBaseColumns.map((e) => i18n.translate(`importExport.${e}`)),
      DistributorBaseColumns,
      i18n,
      0,
    )) as UploadDistributor[];

    if (!distributors.length) {
      return {
        completed: true,
        failure: [],
      };
    }

    // get existed distributor ids
    const allDistributors = await this.findAll({});
    const existedDistributorIds = allDistributors.map((item) => item.distributorId);

    // count distributor id in file
    const distributorIdCounter = distributors.reduce((pre, curr) => {
      // ignore empty id
      if (!curr.distributorId) {
        return pre;
      }
      return {
        ...pre,
        [curr.distributorId]: (pre[curr.distributorId] || 0) + 1,
      };
    }, {});

    // get i18n text
    const distributorSheetName = i18n.t('distributor.sheetName');
    const rowText = i18n.t('common.row');
    const invalidIdText = i18n.t('distributor.invalid_id');
    const invalidNameText = i18n.t('distributor.invalid_name');
    const nameCanNotEmptyText = i18n.t('distributor.can_not_empty_name');
    const idCanNotEmptyText = i18n.t('distributor.can_not_empty_id');
    const existedIdText = i18n.t('distributor.existed_id');
    const duplicateIdText = i18n.t('distributor.duplicate_id');

    // validate data
    const failure: Reason[] = [];
    distributors.forEach((distributor, index) => {
      const { distributorId, distributorName } = distributor;
      const message: Reason = {
        reason: {},
      };
      if (!distributorName) {
        message.reason[`${distributorSheetName}: ${rowText} ${index + 2}: ${invalidNameText}`] = nameCanNotEmptyText;
      }
      if (!distributorId) {
        message.reason[`${distributorSheetName}: ${rowText} ${index + 2}: ${invalidIdText}`] = idCanNotEmptyText;
      }
      if (distributorIdCounter[distributorId] > 1) {
        message.reason[`${distributorSheetName}: ${rowText} ${index + 2}: ${invalidIdText}`] = duplicateIdText;
      }
      if (existedDistributorIds.includes(distributorId)) {
        message.reason[`${distributorSheetName}: ${rowText} ${index + 2}: ${invalidIdText}`] = existedIdText;
      }
      if (Object.keys(message.reason).length) {
        failure.push(message);
      }
    });

    if (failure.length) {
      return {
        completed: true,
        failure,
      };
    }

    await this._model.insertMany(
      distributors.map((item) => ({
        ...item,
        depots: [],
        createdAt: moment().tz(process.env.TZ),
        updatedAt: moment().tz(process.env.TZ),
      })),
    );

    return {
      completed: true,
      failure: [],
    };
  }

  async uploadDistributorsWithDepots(file: Express.Multer.File, userId: string, i18n: I18nContext): Promise<UploadDataResponse> {
    const distributors = (await this.handleUploadingFile(
      file,
      DistributorBaseColumns.map((e) => i18n.translate(`importExport.${e}`)),
      DistributorBaseColumns,
      i18n,
      0,
    )) as UploadDistributor[];

    const depots = (await this.handleUploadingFile(
      file,
      DepotColumns.map((e) => i18n.translate(`importExport.${e}`)),
      DepotColumns,
      i18n,
      1,
    )) as UploadDepot[];

    if (!distributors.length) {
      return {
        completed: true,
        failure: [],
      };
    }

    const distributorDepotsMap = depots.reduce((pre, curr) => {
      const depot: Depot = {
        id: curr.depotId.trim(),
        name: curr.depotName.trim(),
        status: curr.status.toUpperCase() === DepotStatus.ACTIVE ? DepotStatus.ACTIVE : DepotStatus.INACTIVE,
      };

      if (pre[curr.distributorId]) {
        return {
          ...pre,
          [curr.distributorId]: [...pre[curr.distributorId], depot],
        };
      }

      return {
        ...pre,
        [curr.distributorId]: [depot],
      };
    }, {});

    // get existed distributor ids
    const allDistributors = await this.findAll({});
    const existedDistributorIds = allDistributors.map((item) => item.distributorId);

    // count distributor id in file
    const distributorIdCounter = distributors.reduce((pre, curr) => {
      // ignore empty id
      if (!curr.distributorId) {
        return pre;
      }
      return {
        ...pre,
        [curr.distributorId]: (pre[curr.distributorId] || 0) + 1,
      };
    }, {});

    // get i18n text
    const distributorSheetName = i18n.t('distributor.sheetName');
    const rowText = i18n.t('common.row');
    const invalidIdText = i18n.t('distributor.invalid_id');
    const invalidNameText = i18n.t('distributor.invalid_name');
    const nameCanNotEmptyText = i18n.t('distributor.can_not_empty_name');
    const idCanNotEmptyText = i18n.t('distributor.can_not_empty_id');
    const existedIdText = i18n.t('distributor.existed_id');
    const duplicateIdText = i18n.t('distributor.duplicate_id');

    // validate data
    const failure: Reason[] = [];
    distributors.forEach((distributor, index) => {
      const { distributorId, distributorName } = distributor;
      const message: Reason = {
        reason: {},
      };
      if (!distributorName) {
        message.reason[`${distributorSheetName}: ${rowText} ${index + 2}: ${invalidNameText}`] = nameCanNotEmptyText;
      }
      if (!distributorId) {
        message.reason[`${distributorSheetName}: ${rowText} ${index + 2}: ${invalidIdText}`] = idCanNotEmptyText;
      }
      if (distributorIdCounter[distributorId] > 1) {
        message.reason[`${distributorSheetName}: ${rowText} ${index + 2}: ${invalidIdText}`] = duplicateIdText;
      }
      if (existedDistributorIds.includes(distributorId)) {
        message.reason[`${distributorSheetName}: ${rowText} ${index + 2}: ${invalidIdText}`] = existedIdText;
      }
      if (Object.keys(message.reason).length) {
        failure.push(message);
      }
    });

    const oldDepots = allDistributors.reduce((pre, curr) => [...pre, ...(curr.depots?.map((pre) => pre.id) || [])], []);

    const depotSheetName = i18n.t('distributor.sheetName');
    const invalidDistributorId = i18n.t('depot.invalidDistributorId');
    const emptyDistributorId = i18n.t('depot.emptyDistributorId');
    const invalidDepotId = i18n.t('depot.invalidId');
    const duplicateDepotId = i18n.t('depot.duplicateId');
    const emptyDepotId = i18n.t('depot.emptyId');
    const invalidDepotName = i18n.t('depot.invalidName');
    const emptyDepotName = i18n.t('depot.emptyName');
    const invalidDepotStatus = i18n.t('depot.invalidStatus');
    const emptyDepotStatus = i18n.t('depot.emptyStatus');
    const statusNotMatch = i18n.t('depot.statusNotMatch');
    depots.forEach((depot, index) => {
      const { distributorId, depotId, depotName, status } = depot;
      const message: Reason = {
        reason: {},
      };
      if (!distributorId) {
        message.reason[`${depotSheetName}: ${rowText} ${index + 2}: ${invalidDistributorId}`] = emptyDistributorId;
      }
      if (!depotId) {
        message.reason[`${depotSheetName}: ${rowText} ${index + 2}: ${invalidDepotId}`] = emptyDepotId;
      }
      if (!depotName) {
        message.reason[`${depotSheetName}: ${rowText} ${index + 2}: ${invalidDepotName}`] = emptyDepotName;
      }
      if (!status) {
        message.reason[`${depotSheetName}: ${rowText} ${index + 2}: ${invalidDepotStatus}`] = emptyDepotStatus;
      }
      if (
        !status ||
        !Object.values(DepotStatus)
          .map((item) => i18n.t(`importExport.${item.toUpperCase()}`))
          .includes(status.toUpperCase())
      ) {
        message.reason[`${rowText} ${index + 2}: ${invalidDepotStatus}`] = statusNotMatch;
      }
      const isDuplicateDepotId = this.checkDuplicateDepotId({
        depotId,
        depotIds: [...oldDepots, ...depots.map((item) => item.depotId)],
      });
      if (isDuplicateDepotId) {
        message.reason[`${depotSheetName}: ${rowText} ${index + 2}: ${invalidDepotId}`] = duplicateDepotId;
      }

      if (Object.keys(message.reason).length) {
        failure.push(message);
      }
    });

    if (failure.length) {
      return {
        completed: true,
        failure,
      };
    }

    await this._model.insertMany(
      distributors.map((item) => ({
        ...item,
        depots: distributorDepotsMap[item.distributorId] || [],
        createdAt: moment().tz(process.env.TZ),
        updatedAt: moment().tz(process.env.TZ),
      })),
    );

    return {
      completed: true,
      failure: [],
    };
  }

  async exportWithDepots({ currentUser, params, order, i18n }: { currentUser: User; params: GetListDistributorsArgs; order: OrderParams; i18n: I18nContext }) {
    const result = await this.getListDistributor({ ...params, ...order });
    const { data } = result;
    const fileName = `Distributor_Data`;

    const distributorData = data?.map((item) => {
      return {
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
      };
    });

    const depotData = data.reduce((pre, distributor) => {
      const depots = distributor.depots;
      if (!depots?.length) {
        return pre;
      }

      return [
        ...pre,
        ...depots.map((depot) => ({
          [i18n.translate(`importExport.distributorId`)]: distributor.distributorId,
          [i18n.translate(`importExport.depotId`)]: depot.id,
          [i18n.translate(`importExport.depotName`)]: depot.name,
          [i18n.translate(`importExport.status`)]: i18n.translate(`importExport.${depot.status.toUpperCase()}`),
        })),
      ];
    }, []);

    const file = await this.filesService.exportXLSXFileWithMultipleSheet({
      fileName,
      sheets: [
        {
          name: 'Distributor',
          data: distributorData,
        },
        {
          name: 'Depot',
          data: depotData,
        },
      ],
      currentUser,
      colInfo: { wch: 20 },
    });

    return file;
  }

  async export({ currentUser, params, order, i18n }: { currentUser: User; params: GetListDistributorsArgs; order: OrderParams; i18n: I18nContext }) {
    const result = await this.getListDistributor({ ...params, ...order });
    const { data } = result;
    const fileName = `Distributor_Data`;

    const distributorData = data?.map((item) => {
      return {
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
      };
    });

    const file = await this.filesService.exportXLSXFile(fileName, distributorData, 'Distributor', currentUser, { wch: 20 });

    return file;
  }
}
