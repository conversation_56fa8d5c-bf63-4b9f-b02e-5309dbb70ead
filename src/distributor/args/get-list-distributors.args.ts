import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';
import { PaginationParams } from 'src/shared/common-params/pagination.params';

export class GetListDistributorsArgs extends PaginationParams {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.trim())
  search?: string;
}
