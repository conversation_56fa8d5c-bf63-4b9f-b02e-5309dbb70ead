import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { Transform } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';
import { PaginationParams } from 'src/shared/common-params/pagination.params';

export class GetListDistributorsArgs extends PaginationParams {
  @ApiModelPropertyOptional()
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.trim())
  search?: string;
}
