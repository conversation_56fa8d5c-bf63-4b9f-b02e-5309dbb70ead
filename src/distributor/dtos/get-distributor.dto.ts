import { SortOrder } from 'mongoose';

import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';

export class DistributorSortOrder extends SortOrderDto {
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorName: SortOrder;
}

export class GetDistributorDto {
  @ApiModelPropertyOptional()
  sort: Partial<DistributorSortOrder>;
}
