import { SortOrder } from 'mongoose';

import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class DistributorSortOrder extends SortOrderDto {
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorName: SortOrder;
}

export class GetDistributorDto {
  @ApiPropertyOptional()
  sort: Partial<DistributorSortOrder>;
}
