import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Depot } from './depot.dto';

export class UpdateDistributorDto {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  @IsString()
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  distributorName?: string;

  @ApiModelPropertyOptional()
  @IsNotEmpty()
  @IsString()
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  distributorId?: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Depot)
  depots?: Depot[];
}
