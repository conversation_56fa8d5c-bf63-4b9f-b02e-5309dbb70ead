import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsString, Matches, Max, Min } from 'class-validator';

import { ConstantCommons } from '../../utils/constants';

export class UpdateBaseJourneyPlanSettingDto {
  @ApiProperty({ default: 15 })
  @IsNumber()
  @Min(0, { message: 'distributor.base_journey_plan.time_interval.min' })
  @Max(1440, { message: 'distributor.base_journey_plan.time_interval.max' })
  timeInterval: number;

  @ApiProperty({ default: '08:00' })
  @IsString()
  @Matches(new RegExp(ConstantCommons.TIME_PATTERN), { message: 'distributor.base_journey_plan.start_time' })
  startingTimeframe: string;

  @ApiProperty({ default: '' })
  @IsString()
  distributorId: string;
}
