import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNumber, IsString, Matches, Max, Min } from 'class-validator';

import { ConstantCommons } from '../../utils/constants';

export class UpdateBaseJourneyPlanSettingDto {
  @ApiModelProperty({ default: 15 })
  @IsNumber()
  @Min(0, { message: 'distributor.base_journey_plan.time_interval.min' })
  @Max(1440, { message: 'distributor.base_journey_plan.time_interval.max' })
  timeInterval: number;

  @ApiModelProperty({ default: '08:00' })
  @IsString()
  @Matches(new RegExp(ConstantCommons.TIME_PATTERN), { message: 'distributor.base_journey_plan.start_time' })
  startingTimeframe: string;

  @ApiModelProperty({ default: '' })
  @IsString()
  distributorId: string;
}
