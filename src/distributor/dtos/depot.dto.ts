import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { DepotStatus } from '../enums/depot-status.enum';

export class Depot {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsEnum(DepotStatus)
  status: DepotStatus;
}
