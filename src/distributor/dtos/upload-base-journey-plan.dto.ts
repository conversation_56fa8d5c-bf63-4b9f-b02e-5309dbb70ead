import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNotEmpty, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

export class UploadBaseJourneyPlanDto {
  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  cycleId: string;

  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  distributorId: string;

  @ApiModelProperty({ type: 'string', format: 'binary', required: true })
  file: Express.Multer.File;
}
