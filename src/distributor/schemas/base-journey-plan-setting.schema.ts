import { Document, Types } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { Distributor } from './distributor.schema';

export type BaseJourneyPlanSettingDocument = BaseJourneyPlanSetting & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class BaseJourneyPlanSetting extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: Distributor.name, index: true })
  distributor: Distributor;

  @Prop({ default: 15 })
  timeInterval: number;

  @Prop({ default: '08:00' })
  startingTimeframe: string;
}

export const BaseJourneyPlanSettingSchema = SchemaFactory.createForClass(BaseJourneyPlanSetting);
