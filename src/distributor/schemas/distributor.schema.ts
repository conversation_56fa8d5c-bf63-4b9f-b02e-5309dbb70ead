import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MSchema } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { Depot } from '../dtos/depot.dto';

export type DistributorDocument = Distributor & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class Distributor extends BaseSchema {
  @Prop({ index: true })
  distributorName: string;

  @Prop({ unique: true, index: true })
  distributorId: string;

  @Prop()
  region: string;

  @Prop({ type: [MSchema.Types.Mixed], index: true })
  depots: Depot[];
}

export const DistributorSchema = SchemaFactory.createForClass(Distributor);

// Hooks
DistributorSchema.pre<DistributorDocument>('save', function (next) {
  this.distributorId = this.distributorId?.trim();
  this.distributorName = this.distributorName?.trim();
  next();
});
