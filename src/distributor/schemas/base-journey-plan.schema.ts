import { Document, Types } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { Distributor } from './distributor.schema';
import { User } from '../../users/schemas/user.schema';
import { Outlet } from '../../outlets/schemas/outlet.schema';
import { Days, Weeks } from '../enums';
import { JourneyPlanCycle } from '../../journey-plannings/schemas/journey-plan-cycle.schema';

export type BaseJourneyPlanDocument = BaseJourneyPlan & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class BaseJourneyPlan extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: Distributor.name, index: true })
  distributor: Distributor;

  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  @Prop({ enum: Days, default: Days.DAY_1, index: true })
  day: number;

  @Prop({ enum: Weeks, default: Weeks.WEEK_1, index: true })
  week: number;

  @Prop({ type: Types.ObjectId, ref: JourneyPlanCycle.name, index: true })
  cycle: JourneyPlanCycle;
}

export const BaseJourneyPlanSchema = SchemaFactory.createForClass(BaseJourneyPlan);
