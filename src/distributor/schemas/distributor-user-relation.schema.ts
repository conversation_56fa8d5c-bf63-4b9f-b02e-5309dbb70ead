import { Document, Types } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { Distributor } from './distributor.schema';
import { User } from '../../users/schemas/user.schema';
import { UserAdmin } from '../../users/schemas/user-admin.schema';

export type DistributorUserRelationDocument = DistributorUserRelation & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class DistributorUserRelation extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, default: null, index: true })
  user: User;

  @Prop({ type: Types.ObjectId, ref: UserAdmin.name, default: null, index: true })
  userAdmin: UserAdmin;

  @Prop({ type: Types.ObjectId, ref: Distributor.name, index: true })
  distributor: Distributor;
}

export const DistributorUserRelationSchema = SchemaFactory.createForClass(DistributorUserRelation);
