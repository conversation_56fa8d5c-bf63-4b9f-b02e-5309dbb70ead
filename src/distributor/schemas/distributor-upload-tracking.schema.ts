import { Document, Types } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';
import { Distributor } from './distributor.schema';
import { DistributorUploadTypes } from '../enums';

export type DistributorUploadTrackingDocument = DistributorUploadTracking & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class DistributorUploadTracking extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  uploadedBy: User;

  @Prop({ type: Types.ObjectId, ref: Distributor.name, index: true })
  distributor: Distributor;

  @Prop({ enum: DistributorUploadTypes, default: DistributorUploadTypes.SALES_REP_MASTER_DATA, index: true })
  type: string;

  @Prop({ index: true })
  uploadedAt: Date;
}

export const DistributorUploadTrackingSchema = SchemaFactory.createForClass(DistributorUploadTracking);
