import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ConstantRoles } from '../../utils/constants/role';

export class AddUserDto {
  @ApiModelProperty({ required: false })
  mobilePhone: string;

  @ApiModelProperty({ required: false })
  mobilePhoneCode: string;

  @ApiModelProperty({ required: false })
  userId: string;

  @ApiModelProperty({ required: true })
  email: string;

  @ApiModelProperty({ required: true })
  username: string;

  @ApiModelProperty({ required: false })
  firstName: string;

  @ApiModelProperty({ required: false })
  lastName: string;

  @ApiModelProperty({ required: false })
  password: string;

  @ApiModelProperty({ required: false })
  rePassword: string;

  @ApiModelProperty({ required: false, default: [ConstantRoles.SALE_REP] })
  roleKey: string[];

  @ApiModelProperty({ required: false })
  roleId: string[];

  @ApiModelProperty({ default: false })
  isActive: boolean;

  @ApiModelProperty({ required: false })
  distributorIds: string[];
}
