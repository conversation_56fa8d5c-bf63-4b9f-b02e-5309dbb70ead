import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { OutletStatus } from '../../outlets/enums/outlet-status.enum';

export class AddOrUpdateCustomerMasterDto {
  @ApiModelProperty({ required: true })
  ucc: string;

  @ApiModelProperty()
  outletId: string;

  @ApiModelProperty({ required: true })
  outletName: string;

  @ApiModelProperty({ required: true })
  outletClass: string;

  @ApiModelProperty({ required: true })
  outletArea: string;

  @ApiModelProperty({ required: true })
  outletAddress: string;

  @ApiModelProperty({ required: true })
  contactName: string;

  @ApiModelProperty({ required: true })
  contactNumber: string;

  @ApiModelProperty({ required: true })
  contactPhoneCode: string;

  @ApiModelProperty({ required: false })
  saleRepId?: string;

  @ApiModelProperty({ required: false })
  status?: string;

  @ApiModelProperty({ required: false })
  depotId?: string;
}
