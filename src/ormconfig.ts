import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { config } from 'dotenv';
import { isDebugMode } from './utils';

const fs = require('fs');
config();
const configService = new ConfigService();
export default (): TypeOrmModuleOptions => ({
  // Adding logs for query in debug mode
  logging: isDebugMode(),
  type: 'postgres',
  host: configService.get('POSTGRES_HOST'),
  port: Number(configService.get('POSTGRES_PORT')),
  username: configService.get('POSTGRES_USER'),
  password: configService.get('POSTGRES_PASSWORD'),
  database: configService.get('POSTGRES_DATABASE'),
  entities: configService.get('MIGRATION_MODE') === 'true' ? ['**/*.entity.ts'] : ['**/*.entity.js'],
  ssl: !['production', 'development', 'staging'].includes(configService.get('NODE_ENV'))
    ? false
    : {
        rejectUnauthorized: true,
        ca: fs.readFileSync(configService.get('POSTGRES_CERT_PATH')).toString(),
      },
  synchronize: !['production', 'development', 'staging'].includes(configService.get('NODE_ENV')),
  // migrations: ['migrations/db/*.js'],
  connectTimeoutMS: 90000,
  maxQueryExecutionTime: 90000,
  cache:
    configService.get('CACHE_MODE') === 'true'
      ? {
          type: 'redis',
          options: {
            host: configService.get('REDIS_HOST'),
            port: configService.get('REDIS_PORT'),
            auth_pass: configService.get('REDIS_PASSWORD'),
            password: configService.get('REDIS_PASSWORD'),
            tls:
              configService.get('REDIS_TLS') === 'true'
                ? {
                    host: configService.get('REDIS_HOST'),
                  }
                : false,
          },
          ignoreErrors: false,
        }
      : false,
});
