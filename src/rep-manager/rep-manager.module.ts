import { forwardRef, Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { RepManagerController } from './controllers/rep-manager.controller';
import { AuthModule } from '../auth/auth.module';
import { OrdersModule } from '../orders/orders.module';
import { OutletsModule } from '../outlets/outlets.module';
import { ExternalModule } from '../external/external.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MasterDataModule } from '../master-data/master-data.module';
import { OmsModule } from '../oms/oms.module';
import { FilesModule } from '../files/files.module';
import { UsersModule } from 'src/users/users.module';
import { RepTargetSettingsService } from './services/rep-target-settings.service';
import { RepCoachingSessions } from './entities/rep-coaching-session.entity';
import { RepCoachingSessionHistories } from './entities/rep-coaching-session-histories.entity';
import { RepCoachingOrders } from './entities/rep-coaching-order.entity';
import { RepTargetSettings } from './entities/rep-target-settings.entity';
import { RepRequestTimeOff } from './entities/rep-request-time-off.entity';
import { RepPerformanceKpi } from './entities/rep-performance-kpi.entity';
import { RepCoachingSessionChecklist } from './entities/rep-coaching-session-checklist.entity';
import { RepCoachingQuestion } from './entities/rep-coaching-question.entity';
import { RepCoachingAnswer } from './entities/rep-coaching-answer.entity';
import { RepCoachingOrderService } from './services/rep-coaching-orders.service';
import { RepCoachingSessionHistoriesService } from './services/rep-coaching-session-histories.service';
import { RepPerformanceKpiService } from './services/rep-performance-kpi.service';
import { RepRequestTimeOffService } from './services/rep-request-time-off.service';
import { CoachingSessionController } from './controllers/coaching-session.controller';
import { CoachingPerformanceKpiController } from './controllers/coaching-performance-kpi.controller';
import { CoachingPerformanceController } from './controllers/coaching-performance.controller';
import { CoachingQuestionAnswerController } from './controllers/coaching-question-answer.controller';
import { CoachingOutletVisitController } from './controllers/coaching-outlet.controller';
import { RepCoachingSessionsService } from './services/rep-coaching-sessions.service';
import { RepCoachingSessionChecklistService } from './services/rep-coaching-session-checklist.service';
import { RepCoachingPerformanceService } from './services/rep-coaching-performance.service';
import { RepCoachingQuestionService } from './services/rep-coaching-question.service';
import { RepCoachingAnswerService } from './services/rep-coaching-answer.service';
import { RepCoachingOutletService } from './services/rep-coaching-outlet.service';
import { RepManagerService } from './services/rep-manager.service';
import { JourneyPlanningsModule } from 'src/journey-plannings/journey-plannings.module';
import { SaleRepModule } from 'src/sale-rep/sale-rep.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      RepCoachingSessions,
      RepCoachingOrders,
      RepCoachingSessionHistories,
      RepTargetSettings,
      RepRequestTimeOff,
      RepPerformanceKpi,
      RepCoachingSessionChecklist,
      RepCoachingQuestion,
      RepCoachingAnswer,
    ]),
    AuthModule,
    HttpModule,
    forwardRef(() => OrdersModule),
    forwardRef(() => OutletsModule),
    forwardRef(() => ExternalModule),
    forwardRef(() => MasterDataModule),
    forwardRef(() => OmsModule),
    forwardRef(() => FilesModule),
    forwardRef(() => UsersModule),
    forwardRef(() => JourneyPlanningsModule),
    forwardRef(() => SaleRepModule),
  ],
  controllers: [
    RepManagerController,
    CoachingSessionController,
    CoachingPerformanceKpiController,
    CoachingPerformanceController,
    CoachingQuestionAnswerController,
    CoachingOutletVisitController,
  ],
  providers: [
    RepTargetSettingsService,
    RepCoachingSessionsService,
    RepCoachingSessionHistoriesService,
    RepRequestTimeOffService,
    RepPerformanceKpiService,
    RepCoachingOrderService,
    RepCoachingSessionChecklistService,
    RepCoachingPerformanceService,
    RepCoachingQuestionService,
    RepCoachingAnswerService,
    RepCoachingOutletService,
    RepManagerService,
  ],
  exports: [
    RepTargetSettingsService,
    RepCoachingSessionsService,
    RepCoachingSessionHistoriesService,
    RepPerformanceKpiService,
    RepCoachingOrderService,
    RepCoachingPerformanceService,
    RepManagerService,
  ],
})
export class RepManagerModule {}
