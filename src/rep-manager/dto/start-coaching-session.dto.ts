import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class StartCoachingSessionDto {
  @ApiProperty({
    description: 'Outlet key from BusinessPartner',
    example: 'OUTLET001',
  })
  @IsNotEmpty()
  @IsString()
  outletKey: string;

  @ApiProperty({
    description: 'Coaching session ID',
    example: 'uuid',
  })
  @IsNotEmpty()
  @IsString()
  sessionId: string;
}
