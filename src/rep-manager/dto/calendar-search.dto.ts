import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

export class CalendarSearchDto {
  @IsDateString()
  @IsNotEmpty()
  @ApiProperty({ type: Date })
  start_date: string;

  @IsDateString()
  @ApiProperty({ type: Date })
  @IsNotEmpty()
  end_date: string;

  @IsUUID()
  @ApiProperty({ required: false })
  @IsOptional()
  salesRepId?: string;
}
