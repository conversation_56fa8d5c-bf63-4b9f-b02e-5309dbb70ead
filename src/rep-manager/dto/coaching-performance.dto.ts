import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CoachingPerformanceQueryDto {
  @ApiProperty({
    description: 'Month (1-12)',
    example: 6,
    minimum: 1,
    maximum: 12,
  })
  @IsNumber()
  @Min(1)
  @Max(12)
  @Transform(({ value }) => parseInt(value))
  month: number;

  @ApiProperty({
    description: 'Year (4 digits)',
    example: 2024,
    minimum: 2020,
    maximum: 2030,
  })
  @IsNumber()
  @Min(2020)
  @Max(2030)
  @Transform(({ value }) => parseInt(value))
  year: number;

  @ApiProperty({
    description: 'Sales Representative ID (optional)',
    example: '550e8400-e29b-41d4-a716-446655440000',
    required: false,
  })
  @IsOptional()
  @IsString()
  salesRepId?: string;
}
