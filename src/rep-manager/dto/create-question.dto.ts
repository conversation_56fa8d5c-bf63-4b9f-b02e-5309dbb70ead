import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString, IsBoolean, IsO<PERSON>al, <PERSON>Int, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { QuestionType } from '../enums/rep-manager.enum';

export class CreateQuestionDto {
  @ApiProperty({
    description: 'Question title',
    example: 'Customer Satisfaction Survey',
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Question text/content',
    example: 'How satisfied are you with our service?',
  })
  @IsNotEmpty()
  @IsString()
  text: string;

  @ApiProperty({
    description: 'Question type',
    enum: QuestionType,
    example: QuestionType.RATING,
  })
  @IsNotEmpty()
  @IsEnum(QuestionType)
  type: QuestionType;

  @ApiProperty({
    description: 'Whether the question is required to be answered',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  required?: boolean;

  @ApiProperty({
    description: 'Sort order for the question',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  sortOrder?: number;
}

export class CreateQuestionsDto {
  @ApiProperty({
    description: 'Array of questions to create',
    type: [CreateQuestionDto],
  })
  @IsNotEmpty()
  questions: CreateQuestionDto[];
}
