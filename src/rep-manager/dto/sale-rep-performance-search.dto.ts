import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsIn, IsNotEmpty, IsOptional } from 'class-validator';

export enum ExecutionDisciplineType {
  AVAILABILITY = 'availability',
  VISIBILITY = 'visibility',
}

export class SaleRepPerformanceSearchDto {
  @IsDateString()
  @IsNotEmpty()
  @ApiProperty({ type: Date })
  startDate: Date;

  @IsDateString()
  @ApiProperty({ type: Date })
  @IsNotEmpty()
  endDate: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  depotKey?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  salesRepKeys?: string[];
}

export class SaleRepExecutionDisciplineSearchDto extends SaleRepPerformanceSearchDto {
  @ApiProperty({
    required: true,
    enum: ExecutionDisciplineType,
    description: 'Type of execution discipline to retrieve',
    example: ExecutionDisciplineType.AVAILABILITY,
  })
  @IsIn(Object.values(ExecutionDisciplineType))
  @IsNotEmpty()
  type: ExecutionDisciplineType;
}
