import { IsBoolean, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsString, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateChecklistItemDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  label: string;

  @IsUUID()
  @IsOptional()
  @ApiProperty()
  sessionId: string;

  @IsNotEmpty()
  @ApiProperty()
  outletKey: string;
}

export class UpdateCheckedStatusDto {
  @IsBoolean()
  @IsNotEmpty()
  @ApiProperty({ description: 'Whether the checklist item is checked or not' })
  checked: boolean;
}
