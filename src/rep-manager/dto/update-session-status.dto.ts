import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SessionStatus } from '../enums/rep-manager.enum';

export class UpdateSessionStatusDto {
  @IsEnum(SessionStatus)
  @ApiProperty({
    enum: SessionStatus,
    description: 'The new status for the coaching session',
    example: SessionStatus.IN_PROGRESS,
  })
  status: SessionStatus;

  @IsOptional()
  @IsString()
  @ApiProperty({
    description: 'Optional note for the status change',
    example: 'Session started as scheduled',
    required: false,
  })
  note?: string;
}
