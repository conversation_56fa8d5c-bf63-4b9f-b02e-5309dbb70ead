import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateOrUpdateAnswerDto {
  @ApiProperty({
    description: 'Question ID',
    example: 'uuid',
  })
  @IsNotEmpty()
  @IsString()
  questionId: string;

  @ApiProperty({
    description: 'History ID (session history ID)',
    example: 'uuid',
  })
  @IsNotEmpty()
  @IsString()
  historyId: string;

  @ApiProperty({
    description: 'Answer value (can be any type)',
    example: 'Sample answer text',
    required: false,
  })
  @IsOptional()
  value: any;
}
