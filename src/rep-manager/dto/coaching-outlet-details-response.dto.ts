import { ApiProperty } from '@nestjs/swagger';
import { RepCoachingSessionHistories } from '../entities/rep-coaching-session-histories.entity';
import { ChecklistItemResponseDto } from './checklist-item-response.dto';

export class OutletInfoDto {
  @ApiProperty({ description: 'Outlet ID' })
  id: string;

  @ApiProperty({ description: 'Outlet name' })
  name: string;
}

export class ProgressDto {
  @ApiProperty({ description: 'Total number of questions', example: 24 })
  totalQuestions: number;

  @ApiProperty({ description: 'Number of answered questions', example: 2 })
  answeredQuestions: number;

  @ApiProperty({ description: 'Completion percentage', example: 12 })
  completion: number;
}

export class CoachingOutletDetailsResponseDto {
  @ApiProperty({ description: 'Outlet information', type: OutletInfoDto })
  outlet: OutletInfoDto;

  @ApiProperty({ description: 'Progress information', type: ProgressDto })
  progress: ProgressDto;

  @ApiProperty({ description: 'All fields from RepCoachingSessionHistories' })
  sessionHistory: RepCoachingSessionHistories;

  @ApiProperty({
    description: 'Checklist items',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        label: { type: 'string' },
        checked: { type: 'boolean' },
        isCoaching: { type: 'boolean' },
        sessionId: { type: 'string' },
        outletId: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        isPrevSession: { type: 'boolean' },
      },
    },
  })
  checklist: ChecklistItemResponseDto[];
}
