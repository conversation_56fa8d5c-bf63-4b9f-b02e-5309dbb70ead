import { <PERSON><PERSON><PERSON>, IsNotEmpty, Is<PERSON><PERSON>al, IsString, IsU<PERSON>D, IsBoolean, IsInt, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { QuestionType } from '../enums/rep-manager.enum';

export class UpdateQuestionDto {
  @ApiProperty({
    description: 'Question ID',
    example: 'uuid',
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Question title',
    example: 'Updated Customer Satisfaction Survey',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Question text/content',
    example: 'How satisfied are you with our updated service?',
    required: false,
  })
  @IsOptional()
  @IsString()
  text?: string;

  @ApiProperty({
    description: 'Question type',
    enum: QuestionType,
    example: QuestionType.RATING,
    required: false,
  })
  @IsOptional()
  @IsEnum(QuestionType)
  type?: QuestionType;

  @ApiProperty({
    description: 'Whether the question is required to be answered',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  required?: boolean;

  @ApiProperty({
    description: 'Sort order for the question',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  sortOrder?: number;
}

export class UpdateQuestionsDto {
  @ApiProperty({
    description: 'Array of questions to update',
    type: [UpdateQuestionDto],
  })
  @IsNotEmpty()
  questions: UpdateQuestionDto[];
}
