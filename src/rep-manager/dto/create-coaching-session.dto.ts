import { IsArray, IsDate, IsDateString, IsNotEmpty, IsUUID, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCoachingSessionItemDto {
  @IsUUID()
  @IsNotEmpty()
  @ApiProperty()
  salesRepId: string;

  @IsDateString()
  @IsNotEmpty()
  @ApiProperty({ type: Date, example: '2025-06-16' })
  day: Date;
}

export class CreateCoachingSessionDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCoachingSessionItemDto)
  @ApiProperty({
    type: [CreateCoachingSessionItemDto],
    example: [
      {
        salesRepId: '550e8400-e29b-41d4-a716-************',
        day: '2025-06-16',
      },
    ],
  })
  sessions: CreateCoachingSessionItemDto[];
}
