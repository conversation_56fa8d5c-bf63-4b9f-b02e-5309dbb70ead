import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString, IsNumber, IsBoolean, IsEnum, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { SessionHistoryStatus, CoachingStepType } from '../enums/rep-manager.enum';

class LocationDto {
  @ApiProperty({ description: 'Latitude coordinate' })
  @IsNumber()
  latitude: number;

  @ApiProperty({ description: 'Longitude coordinate' })
  @IsNumber()
  longitude: number;
}

export class UpdateCoachingSessionHistoryDto {
  @ApiProperty({ required: false, description: 'Location coordinates', type: LocationDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => LocationDto)
  location?: LocationDto;

  @ApiProperty({ required: false, description: 'Location range in meters' })
  @IsOptional()
  @IsNumber()
  locationRange?: number;

  @ApiProperty({ required: false, description: 'Whether the session has an order' })
  @IsOptional()
  @IsBoolean()
  hasOrder?: boolean;

  @ApiProperty({ required: false, description: 'Manager notes' })
  @IsOptional()
  @IsString()
  managerNote?: string;

  @ApiProperty({ required: false, description: 'Sales representative notes' })
  @IsOptional()
  @IsString()
  salesRepNote?: string;

  @ApiProperty({ required: false, description: 'Outlet notes' })
  @IsOptional()
  @IsString()
  outletNote?: string;

  @ApiProperty({ required: false, enum: CoachingStepType, description: 'Coaching step type' })
  @IsOptional()
  @IsEnum(CoachingStepType)
  step?: CoachingStepType;
}
