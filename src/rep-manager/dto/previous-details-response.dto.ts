import { ApiProperty } from '@nestjs/swagger';

export class SalesRepDto {
  @ApiProperty({ description: 'Sales representative name' })
  name: string;

  @ApiProperty({ description: 'Sales representative ID' })
  salesRepId: string;

  @ApiProperty({ description: 'Coaching score', type: 'number' })
  coachingScore: number;
}

export class TaskItemDto {
  @ApiProperty({ description: 'Task ID' })
  id: string;

  @ApiProperty({ description: 'Task label' })
  label: string;

  @ApiProperty({ description: 'Whether task is checked', type: 'boolean' })
  checked: boolean;

  @ApiProperty({ description: 'Whether task is coaching related', type: 'boolean' })
  isCoaching: boolean;

  @ApiProperty({ description: 'Session ID' })
  sessionId: string;
}

export class ChecklistOutletDto {
  @ApiProperty({ description: 'Outlet name' })
  outletName: string;

  @ApiProperty({ description: 'List of tasks', type: [TaskItemDto] })
  tasks: TaskItemDto[];
}

export class PreviousDetailsResponseDto {
  @ApiProperty({ description: 'Session date' })
  sessionDate: string;

  @ApiProperty({ description: 'Sales representative information', type: SalesRepDto })
  salesRep: SalesRepDto;

  @ApiProperty({ description: 'Coaching note' })
  coachingNote: string;

  @ApiProperty({ description: 'List of visits' })
  visitList: any[];

  @ApiProperty({ description: 'List of checklist items grouped by outlet', type: [ChecklistOutletDto] })
  checklist: ChecklistOutletDto[];
}
