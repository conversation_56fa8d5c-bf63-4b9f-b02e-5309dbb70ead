import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDateString, IsOptional } from 'class-validator';

export class SaleRepSearchRequestDto {
  @ApiProperty({ required: false })
  @IsOptional()
  searchText?: string;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  offset?: number;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  limit?: number;

  @IsOptional()
  @Type(() => String)
  orderBy?: 'name' | 'displayDay';

  @IsOptional()
  @Type(() => String)
  orderDesc?: 'ASC' | 'DESC';

  @IsOptional()
  saleRepIds?: string[];

  @ApiProperty({
    description: 'Start date of the range (YYYY-MM-DD)',
    required: false,
    example: '2024-04-01',
  })
  @IsOptional()
  @IsDateString()
  startDate?: Date;

  @ApiProperty({
    description: 'End date of the range (YYYY-MM-DD)',
    required: false,
    example: '2024-04-01',
  })
  @IsOptional()
  @IsDateString()
  endDate?: Date;
}
