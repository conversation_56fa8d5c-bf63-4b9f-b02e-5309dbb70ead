import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpt<PERSON>, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class DeleteQuestionsDto {
  @ApiProperty({
    description: 'Array of question IDs to delete',
    type: [String],
    example: ['uuid1', 'uuid2', 'uuid3'],
  })
  @IsNotEmpty()
  @IsArray()
  @IsUUID('4', { each: true })
  questionIds: string[];
}
