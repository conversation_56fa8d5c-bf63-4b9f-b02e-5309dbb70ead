import { ApiProperty } from '@nestjs/swagger';
import { ChecklistItemResponseDto } from './checklist-item-response.dto';

export class MetricsDto {
  @ApiProperty({ example: 200, description: 'Year to date volume in HL' })
  yearToDateVolume: number;

  @ApiProperty({ example: 123, description: 'Average volume per order in HL' })
  avgVolumePerOrder: number;

  @ApiProperty({ example: 3, description: 'Average NPS score' })
  avgNPSScore: number;
}

export class SessionVisitDetailResponseDto {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        label: { type: 'string' },
        checked: { type: 'boolean' },
        isCoaching: { type: 'boolean' },
        sessionId: { type: 'string' },
        outletId: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        isPrevSession: { type: 'boolean' },
      },
    },
  })
  checklist: ChecklistItemResponseDto[];

  @ApiProperty()
  metrics: MetricsDto;
}
