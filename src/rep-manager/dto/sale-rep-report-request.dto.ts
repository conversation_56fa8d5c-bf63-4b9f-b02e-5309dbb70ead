import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsIn, IsNotEmpty, IsOptional } from 'class-validator';

export class SaleRepReportRequestDto {
  @ApiProperty({ required: false })
  @IsOptional()
  month?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  year?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  salesRepKeys?: string[];

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  offset?: number;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  limit?: number;

  @IsOptional()
  @Type(() => String)
  orderBy?: 'name' | 'displayDay';

  @IsOptional()
  @Type(() => String)
  orderDesc?: 'ASC' | 'DESC';
}
