import { IsDateString, <PERSON>NotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RescheduleCoachingSessionDto {
  @IsDateString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The new date for the coaching session (YYYY-MM-DD format)',
    example: '2024-12-25',
  })
  rescheduleDate: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    description: 'Optional reason for rescheduling',
    example: 'Sales rep requested due to personal emergency',
    required: false,
  })
  rescheduleReason?: string;
}
