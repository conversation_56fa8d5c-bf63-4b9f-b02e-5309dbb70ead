import { Controller, UseGuards, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { RepCoachingPerformanceService } from '../services/rep-coaching-performance.service';
import { CoachingPerformanceQueryDto } from '../dto/coaching-performance.dto';
import { CoachingPerformanceResponse } from '../dto/coaching-performance-response.dto';
import { ApiResponse as ApiResponseShared } from '../../shared/response/api-response';

@ApiTags('coaching-session')
@Controller('api/performance')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class CoachingPerformanceController {
  constructor(private readonly coachingPerformanceService: RepCoachingPerformanceService) {}

  @Get('coaching')
  @ApiOperation({ summary: 'Get coaching performance data for a specific month' })
  @ApiResponse({
    status: 200,
    description: 'Coaching performance data retrieved successfully.',
    schema: {
      type: 'object',
      properties: {
        coachingSession: {
          type: 'object',
          properties: {
            totalSessions: { type: 'number', example: 32 },
            completed: { type: 'number', example: 12 },
            canceled: { type: 'number', example: 20 },
            remaining: { type: 'number', example: 20 },
            lastMonthChangePercent: { type: 'number', example: 20 },
          },
        },
        tasks: {
          type: 'object',
          properties: {
            totalTasks: { type: 'number', example: 25 },
            completed: { type: 'number', example: 17 },
            pending: { type: 'number', example: 7 },
            completionRate: { type: 'number', example: 70 },
            pendingRate: { type: 'number', example: 30 },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - invalid parameters.' })
  async getCoachingPerformance(@Query() query: CoachingPerformanceQueryDto) {
    const result = await this.coachingPerformanceService.getPerformancePerMonth(query.month, query.year, query.salesRepId);
    return new ApiResponseShared(result);
  }
}
