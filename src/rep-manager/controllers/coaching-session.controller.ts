import { Controller, UseGuards, Post, Body, Get, Query, Delete, Param, Put, Patch } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse, ApiPropertyOptional, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { RepCoachingSessionsService } from '../services/rep-coaching-sessions.service';
import { CreateCoachingSessionDto } from '../dto/create-coaching-session.dto';
import { RepCoachingSessions } from '../entities/rep-coaching-session.entity';
import { RepCoachingSessionHistories } from '../entities/rep-coaching-session-histories.entity';
import { ApiResponse as ApiResponseShared } from '../../shared/response/api-response';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { CalendarSearchDto } from '../dto/calendar-search.dto';
import { CalendarDayGroup } from '../dto/calendar-response.dto';
import { I18n, I18nContext } from 'nestjs-i18n';
import { RepCoachingSessionChecklistService } from '../services/rep-coaching-session-checklist.service';
import { CreateChecklistItemDto, UpdateCheckedStatusDto } from '../dto/coaching-session-checklist.dto';
import { SessionVisitDetailResponseDto } from '../dto/session-visit-detail-response.dto';
import { PreviousDetailsResponseDto } from '../dto/previous-details-response.dto';
import { UpdateSessionStatusDto } from '../dto/update-session-status.dto';
import { CoachingPerformanceQueryDto } from '../dto/coaching-performance.dto';
import { PaginationRequestParamsDto } from '../../shared/dtos/pagination-request-params.dto';
import { RescheduleCoachingSessionDto } from '../dto/reschedule-coaching-session.dto';

@ApiTags('coaching-session')
@Controller('api/coaching-session')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class CoachingSessionController {
  constructor(private readonly repCoachingSessionsService: RepCoachingSessionsService, private readonly checklistService: RepCoachingSessionChecklistService) {}

  @Post()
  @ApiOperation({ summary: 'Create coaching sessions' })
  @ApiResponse({
    status: 201,
    description: 'The coaching sessions have been successfully created.',
    type: [RepCoachingSessions],
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  async create(@Body() body: CreateCoachingSessionDto, @CurrentUser() currentUser: any, @I18n() i18n: I18nContext) {
    return new ApiResponseShared(await this.repCoachingSessionsService.createSessions(body, currentUser, i18n));
  }

  @ApiOperation({ summary: 'Search coaching sessions calendar' })
  @ApiResponse({ status: 200, type: [CalendarDayGroup] })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @Get('calendar')
  async calendar(@Query() query: CalendarSearchDto, @I18n() i18n: I18nContext): Promise<CalendarDayGroup[]> {
    return this.repCoachingSessionsService.searchCalendar(query, i18n);
  }

  @Get('sessionVisitDetail')
  @ApiOperation({ summary: 'Get session visit detail' })
  @ApiResponse({
    status: 200,
    description: 'Session visit detail retrieved successfully.',
    type: SessionVisitDetailResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - invalid session or visit.' })
  async getSessionVisitDetail(@Query('sessionId') sessionId: string, @Query('outletKey') outletKey: string, @I18n() i18n: I18nContext) {
    return new ApiResponseShared(await this.repCoachingSessionsService.getSessionVisitDetail(sessionId, outletKey, i18n));
  }

  @Get('list')
  @ApiOperation({ summary: 'List coaching sessions with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Coaching sessions retrieved successfully.',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'uuid' },
              agent: {
                type: 'object',
                properties: {
                  name: { type: 'string', example: 'John Doe' },
                  id: { type: 'string', example: 'uuid' },
                  key: { type: 'string', example: 'BP001' },
                },
              },
              date: { type: 'string', example: '2024-01-15' },
              status: { type: 'string', enum: ['PLANNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELED'] },
              coachingScore: { type: 'number', example: 85 },
              comment: { type: 'string', example: 'Good performance today' },
            },
          },
        },
        count: { type: 'number', example: 100 },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - invalid parameters.' })
  async list(@Query() queryDto: CoachingPerformanceQueryDto, @Query() paginationParams: PaginationRequestParamsDto) {
    const result = await this.repCoachingSessionsService.listCoachingSessions(queryDto, paginationParams);
    return new ApiResponseShared(result);
  }

  @Get('checklist')
  @ApiOperation({ summary: 'List coaching session checklist items with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Checklist items retrieved successfully.',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: '1' },
              label: { type: 'string', example: 'Check in with them to uncover any challenges they may be encountering.' },
              salesRep: {
                type: 'object',
                properties: {
                  name: { type: 'string', example: 'Helly R' },
                  id: { type: 'string', example: 'H' },
                  key: { type: 'string', example: '2321u4S8DNBD' },
                },
              },
              source: { type: 'string', example: 'Crafty Brews Lounge' },
              day: { type: 'string', example: '27/08/2025' },
              checked: { type: 'boolean', example: true },
              isCoaching: { type: 'boolean', example: true },
            },
          },
        },
        count: { type: 'number', example: 100 },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - invalid parameters.' })
  @ApiQuery({ name: 'checked', type: 'boolean', required: false, description: 'Filter by checked status (true/false)' })
  async checklist(@Query() queryDto: CoachingPerformanceQueryDto, @Query() paginationParams: PaginationRequestParamsDto, @Query('checked') checked?: boolean) {
    const result = await this.checklistService.listChecklistItems(queryDto, paginationParams, checked);
    return new ApiResponseShared(result);
  }

  @ApiOperation({ summary: 'Get coaching Session Details' })
  @Get(':id')
  async getCoachingSessionDetails(@Param('id') coachingSessionId: string, @I18n() i18n: I18nContext) {
    return new ApiResponseShared(await this.repCoachingSessionsService.getCoachingSessionDetails(coachingSessionId, i18n));
  }

  @Get('previous-details/:id')
  @ApiOperation({ summary: 'Get previous coaching session details' })
  @ApiResponse({
    status: 200,
    description: 'Previous coaching session details retrieved successfully.',
    type: PreviousDetailsResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - session not found.' })
  async getPreviousDetails(@Param('id') sessionId: string, @I18n() i18n: I18nContext) {
    return new ApiResponseShared(await this.repCoachingSessionsService.getPreviousDetails(sessionId, i18n));
  }

  @Post(':sessionId/checklist')
  @ApiOperation({ summary: 'Create a checklist item for a coaching session' })
  @ApiResponse({ status: 201, description: 'Checklist item created.' })
  async createChecklistItem(@Param('sessionId') sessionId: string, @Body() createDto: CreateChecklistItemDto, @I18n() i18n: I18nContext) {
    createDto.sessionId = sessionId;
    const result = await this.checklistService.createChecklistItem(createDto, i18n);
    return new ApiResponseShared(result);
  }

  @Get(':sessionId/checklist')
  @ApiOperation({ summary: 'Get checklist items for a coaching session' })
  @ApiResponse({ status: 200, description: 'List of checklist items.' })
  async getSessionChecklist(@Param('sessionId') sessionId: string, @Query('outletKey') outletKey: string, @I18n() i18n: I18nContext) {
    const result = await this.checklistService.getChecklistBySessionAndOutletKey(sessionId, outletKey, i18n);
    return new ApiResponseShared(result);
  }

  @Patch('checklist/:id/checked')
  @ApiOperation({ summary: 'Update checked status of a checklist item' })
  @ApiResponse({ status: 200, description: 'Checklist item checked status updated.' })
  async updateCheckedStatus(@Param('id') id: string, @Body() body: UpdateCheckedStatusDto, @I18n() i18n: I18nContext) {
    const result = await this.checklistService.updateCheckedStatus(id, body.checked, i18n);
    return new ApiResponseShared(result);
  }

  @Delete('checklist/:id')
  @ApiOperation({ summary: 'Delete a checklist item' })
  @ApiResponse({ status: 204, description: 'Checklist item deleted.' })
  async deleteChecklistItem(@Param('id') id: string, @I18n() i18n: I18nContext) {
    await this.checklistService.deleteChecklistItem(id, i18n);
    return new ApiResponseShared(null);
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update coaching session status' })
  @ApiResponse({ status: 200, description: 'Session status updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad request - invalid status transition or session not found.' })
  async updateStatus(@Param('id') sessionId: string, @Body() updateDto: UpdateSessionStatusDto, @I18n() i18n: I18nContext) {
    await this.repCoachingSessionsService.updateSessionStatus(sessionId, updateDto, i18n);
    return new ApiResponseShared(null);
  }

  @Post(':id/reschedule')
  @ApiOperation({ summary: 'Reschedule a coaching session' })
  @ApiResponse({ status: 200, description: 'Session rescheduled successfully.' })
  @ApiResponse({ status: 400, description: 'Bad request - invalid reschedule date, session status, or session has histories.' })
  async rescheduleSession(@Param('id') sessionId: string, @Body() rescheduleDto: RescheduleCoachingSessionDto, @I18n() i18n: I18nContext) {
    await this.repCoachingSessionsService.rescheduleSession(sessionId, rescheduleDto, i18n);
    return new ApiResponseShared({ message: 'Session rescheduled successfully' });
  }
}
