import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { ApiResponse } from '../../shared/response/api-response';
import { CurrentUser } from '../../shared/decorator/current-user.decorator';
import { RepPerformanceKpiService } from '../services/rep-performance-kpi.service';
import { User } from '../../users/schemas/user.schema';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { ConstantRoles } from 'src/utils/constants/role';

@ApiTags('coaching-session')
@Controller('api/performance-kpi')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class CoachingPerformanceKpiController {
  constructor(private readonly service: RepPerformanceKpiService) {}

  @Get('agent-performances')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  async getBusinessPartnerRequestDetail(@Query() { month, year }: { month: number; year: number }, @CurrentUser() user: User) {
    return new ApiResponse(await this.service.getASMAgentPerformances(user.saleRepId || user.contactId, month, year));
  }
}
