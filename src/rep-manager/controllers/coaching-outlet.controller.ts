import { Controller, UseGuards, Get, Param, Post, Body, Patch } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { RepCoachingOutletService } from '../services/rep-coaching-outlet.service';
import { RepCoachingAnswerService } from '../services/rep-coaching-answer.service';
import { RepCoachingSessionHistoriesService } from '../services/rep-coaching-session-histories.service';
import { CoachingOutletDetailsResponseDto } from '../dto/coaching-outlet-details-response.dto';
import { CreateOrUpdateAnswerDto } from '../dto/create-or-update-answer.dto';
import { UpdateCoachingSessionHistoryDto } from '../dto/update-coaching-session-history.dto';
import { StartCoachingSessionDto } from '../dto/start-coaching-session.dto';
import { RepCoachingAnswer } from '../entities/rep-coaching-answer.entity';
import { RepCoachingSessionHistories } from '../entities/rep-coaching-session-histories.entity';
import { ApiResponse as ApiResponseShared } from '../../shared/response/api-response';
import { I18n, I18nContext } from 'nestjs-i18n';

@ApiTags('coaching-outlet-visit')
@Controller('api/coaching-outlet-visit')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class CoachingOutletVisitController {
  constructor(
    private readonly repCoachingOutletService: RepCoachingOutletService,
    private readonly repCoachingAnswerService: RepCoachingAnswerService,
    private readonly repCoachingSessionHistoriesService: RepCoachingSessionHistoriesService,
  ) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get coaching outlet visit details' })
  @ApiResponse({
    status: 200,
    description: 'Coaching outlet visit details retrieved successfully.',
    type: CoachingOutletDetailsResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Visit not found.' })
  async getDetails(@Param('id') id: string, @I18n() i18n: I18nContext) {
    const result = await this.repCoachingOutletService.getDetails(id, i18n);
    return new ApiResponseShared(result);
  }

  @Post('create-or-update-answer')
  @ApiOperation({ summary: 'Create or update RepCoachingAnswer' })
  @ApiResponse({
    status: 201,
    description: 'Answer created or updated successfully.',
    type: RepCoachingAnswer,
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation failed or required field empty.' })
  @ApiResponse({ status: 404, description: 'Question or session history not found.' })
  async createOrUpdateAnswer(@Body() body: CreateOrUpdateAnswerDto, @I18n() i18n: I18nContext) {
    const result = await this.repCoachingAnswerService.createOrUpdateAnswer(body.questionId, body.historyId, body.value, i18n);
    return new ApiResponseShared(result);
  }

  @Patch('session-history/:id')
  @ApiOperation({ summary: 'Update RepCoachingSessionHistories by ID' })
  @ApiResponse({
    status: 200,
    description: 'Coaching session history updated successfully.',
    type: RepCoachingSessionHistories,
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation failed.' })
  @ApiResponse({ status: 404, description: 'Session history not found.' })
  async updateSessionHistory(@Param('id') id: string, @Body() updateDto: UpdateCoachingSessionHistoryDto, @I18n() i18n: I18nContext) {
    const result = await this.repCoachingSessionHistoriesService.updateById(id, updateDto, i18n);
    return new ApiResponseShared(result);
  }

  @Post('start')
  @ApiOperation({ summary: 'Start coaching session' })
  @ApiResponse({
    status: 201,
    description: 'The coaching session has been successfully started.',
    type: RepCoachingSessionHistories,
  })
  @ApiResponse({ status: 400, description: 'Bad request - session not found, outlet not found, session history already exists, or session status not IN_PROGRESS.' })
  async start(@Body() body: StartCoachingSessionDto, @I18n() i18n: I18nContext) {
    return new ApiResponseShared(await this.repCoachingOutletService.startCoaching(body.outletKey, body.sessionId, i18n));
  }

  @Post('end/:id')
  @ApiOperation({ summary: 'End coaching session visit' })
  @ApiResponse({
    status: 200,
    description: 'The coaching session visit has been successfully ended.',
    type: RepCoachingSessionHistories,
  })
  @ApiResponse({ status: 400, description: 'Bad request - visit not found or status not ONGOING.' })
  @ApiResponse({ status: 404, description: 'Visit not found.' })
  async end(@Param('id') id: string, @I18n() i18n: I18nContext) {
    const result = await this.repCoachingOutletService.endvisit(id, i18n);
    return new ApiResponseShared(result);
  }
}
