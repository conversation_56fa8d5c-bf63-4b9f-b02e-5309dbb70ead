import { Controller, Post, Put, Get, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { I18n, I18nContext } from 'nestjs-i18n';
import { RepCoachingQuestionService } from '../services/rep-coaching-question.service';
import { CreateQuestionsDto } from '../dto/create-question.dto';
import { UpdateQuestionsDto } from '../dto/update-question.dto';
import { DeleteQuestionsDto } from '../dto/delete-questions.dto';
import { PaginationRequestParamsDto } from '../../shared/dtos/pagination-request-params.dto';
import { PaginatedResponse } from '../../shared/dtos/paginated-response.dto';
import { RepCoachingQuestion } from '../entities/rep-coaching-question.entity';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';

@ApiTags('Coaching Question Answer')
@Controller('coaching-question-answer')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CoachingQuestionAnswerController {
  constructor(private readonly repCoachingQuestionService: RepCoachingQuestionService) {}

  @Post('questions')
  @ApiOperation({ summary: 'Create list of questions' })
  @ApiResponse({
    status: 201,
    description: 'Questions created successfully',
    type: [RepCoachingQuestion],
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async createQuestions(@Body() createQuestionsDto: CreateQuestionsDto): Promise<RepCoachingQuestion[]> {
    return await this.repCoachingQuestionService.createQuestions(createQuestionsDto);
  }

  @Put('questions')
  @ApiOperation({ summary: 'Update list of questions' })
  @ApiResponse({
    status: 200,
    description: 'Questions updated successfully',
    type: [RepCoachingQuestion],
  })
  @ApiResponse({ status: 400, description: 'Bad request - Cannot update questions with existing answers' })
  @ApiResponse({ status: 404, description: 'Question not found' })
  async updateQuestions(@Body() updateQuestionsDto: UpdateQuestionsDto, @I18n() i18n: I18nContext): Promise<RepCoachingQuestion[]> {
    return await this.repCoachingQuestionService.updateQuestions(updateQuestionsDto, i18n);
  }

  @Get('questions')
  @ApiOperation({ summary: 'Get all questions with pagination' })
  @ApiQuery({ name: 'offset', required: false, type: Number, description: 'Number of records to skip' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of records to return' })
  @ApiQuery({ name: 'orderBy', required: false, type: String, description: 'Field name to order by' })
  @ApiQuery({ name: 'orderDesc', required: false, enum: ['ASC', 'DESC'], description: 'Sort direction' })
  @ApiQuery({ name: 'type', required: false, enum: ['YES_NO', 'RATING', 'NUMERIC', 'TEXT'], description: 'Filter by question type' })
  @ApiQuery({ name: 'searchText', required: false, type: String, description: 'Search in question text' })
  @ApiResponse({
    status: 200,
    description: 'Questions retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        count: { type: 'number', description: 'Total number of questions' },
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/RepCoachingQuestion' },
          description: 'Array of questions for the current page',
        },
      },
    },
  })
  async getAllQuestions(
    @Query() paginationParams: PaginationRequestParamsDto,
    @Query('type') type?: string,
    @Query('searchText') searchText?: string,
  ): Promise<PaginatedResponse<RepCoachingQuestion>> {
    const filters = {
      type,
      searchText,
    };

    return await this.repCoachingQuestionService.getAllQuestionsPaginated(paginationParams, filters);
  }

  @Get('questions/:id')
  @ApiOperation({ summary: 'Get question by ID' })
  @ApiResponse({
    status: 200,
    description: 'Question retrieved successfully',
    type: RepCoachingQuestion,
  })
  @ApiResponse({ status: 404, description: 'Question not found' })
  async getQuestionById(@Param('id') id: string): Promise<RepCoachingQuestion> {
    return await this.repCoachingQuestionService.getQuestionById(id);
  }

  @Delete('questions')
  @ApiOperation({ summary: 'Delete questions by list of IDs' })
  @ApiResponse({
    status: 200,
    description: 'Questions deleted successfully',
    schema: {
      type: 'object',
      properties: {
        deletedCount: { type: 'number', description: 'Number of successfully deleted questions' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async deleteQuestions(@Body() deleteQuestionsDto: DeleteQuestionsDto): Promise<{ deletedCount: number }> {
    return await this.repCoachingQuestionService.deleteQuestions(deleteQuestionsDto);
  }
}
