import { Body, Controller, Get, HttpException, HttpStatus, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from '../../shared/guards/roles.guard';
import { Roles } from '../../shared/decorator/roles.decorator';
import { ConstantRoles } from '../../utils/constants/role';
import { CurrentUser } from '../../shared/decorator/current-user.decorator';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { In } from 'typeorm';
import { BusinessPartnerContactRole } from '../../master-data/constants/business-partner.enum';
import { ApiResponse } from '../../shared/response/api-response';
import { RepManagerService } from '../services/rep-manager.service';
import { SaleRepExecutionDisciplineSearchDto, SaleRepPerformanceSearchDto } from '../dto/sale-rep-performance-search.dto';
import { I18n, I18nContext } from 'nestjs-i18n';
// import { BusinessPartnersService } from '../../master-data/services/business-partners.service';
import { BusinessPartnerRelationService } from '../../master-data/services/business-partners-relation.service';
import { RepPerformanceKpiService } from '../services/rep-performance-kpi.service';
import { SaleRepReportRequestDto } from '../dto/sale-rep-report-request.dto';
import { SaleRepSearchRequestDto } from '../dto/sale-rep-search-request.dto';
import { PaginationRequestParamsDto } from '../../shared/dtos/pagination-request-params.dto';

@ApiTags('REP Manager')
@Controller('api/rep-manager')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
export class RepManagerController {
  constructor(
    private readonly businessPartnerContactService: BusinessPartnersContactService,
    private readonly repManagerService: RepManagerService,
    private readonly businessPartnerRelationsService: BusinessPartnerRelationService,
    private readonly repPerformanceKpiService: RepPerformanceKpiService,
  ) {}

  @Post('asm/sale-reps/search')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  async searchASMSaleReps(@Body() request: SaleRepSearchRequestDto, @CurrentUser() user: any) {
    const relatedContactIds: string[] = user?.businessPartnerRelations?.contactIds || [];
    const originalContactId = user?.businessPartnerRelations?.originalContactId;
    request.saleRepIds = relatedContactIds.filter((cI) => !!cI && cI !== originalContactId);

    return new ApiResponse(await this.repManagerService.searchSaleReps(request));
  }

  @Get('asm/sale-reps')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  async getASMSaleReps(@CurrentUser() user: any) {
    const relatedContactIds: string[] = user?.businessPartnerRelations?.contactIds || [];
    const originalContactId = user?.businessPartnerRelations?.originalContactId;
    const filterContactIds = relatedContactIds.filter((cI) => !!cI && cI !== originalContactId);
    const saleReps = await this.businessPartnerContactService.find({
      where: {
        id: In(filterContactIds),
        isDeleted: false,
        businessPartnerContactPersonRole: BusinessPartnerContactRole.SALE_REP,
      },
      select: ['id', 'businessPartnerContactKey', 'businessPartnerContactName1', 'businessPartnerContactName2'],
      order: {
        businessPartnerContactName1: 'ASC',
        businessPartnerContactName2: 'ASC',
      },
    });
    return new ApiResponse(saleReps);
  }

  @Get('asm/depots-saleReps')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  async getASMDepots(@CurrentUser() user: any) {
    return new ApiResponse(await this.businessPartnerRelationsService.findDepotsAndRelatedSaleRepsDataByASMKey(user?.saleRepId || user?.contactId));
  }

  @Post('asm/sale-reps/report')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  async getASMSaleRepReports(@Body() request: SaleRepReportRequestDto, @CurrentUser() user: any) {
    const { month, year } = request;
    const relatedContactIds: string[] = user?.businessPartnerRelations?.contactIds || [];
    const originalContactId = user?.businessPartnerRelations?.originalContactId;
    const filterContactIds = relatedContactIds.filter((cI) => !!cI && cI !== originalContactId);

    return new ApiResponse(await this.repManagerService.getASMSaleRepsMetrics(filterContactIds, month, year));
  }

  @Get('asm/sale-reps/:saleRepId/today-coaching-sessions')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  @ApiOperation({
    summary: 'Get route plan data of home screen',
  })
  async getTodayCoachingSession(@Param('saleRepId') saleRepId: string) {
    return new ApiResponse(await this.repManagerService.getSaleRepTodayCoachingSessionData(saleRepId));
  }

  @Post('asm/sale-reps/performance')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  async getSaleRepPerformances(@Body() payload: SaleRepPerformanceSearchDto, @I18n() i18n: I18nContext) {
    const { endDate, startDate, depotKey, salesRepKeys } = payload;
    if (!depotKey?.length) {
      throw new HttpException(await i18n.translate('saleRep.invalid_depot_key'), HttpStatus.BAD_REQUEST);
    }

    const depotSaleReps = await this.businessPartnerRelationsService.findSaleRepsByDepot(depotKey);
    const saleRepsMapByKey = new Map(depotSaleReps.map((sr) => [sr?.saleRepContactKey, sr]));
    const validSaleReps = salesRepKeys?.length ? salesRepKeys.filter((sRKey) => saleRepsMapByKey.get(sRKey)) : (depotSaleReps || []).map((dSR) => dSR.saleRepContactKey);
    return new ApiResponse(await this.repPerformanceKpiService.getK360MetricsForSalesRep(depotKey, validSaleReps, startDate, endDate, i18n));
  }

  @Get('asm/sale-reps/:saleRepId')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  @ApiOperation({
    summary: 'Get sale rep details',
  })
  async getSaleRepDetails(@Param('saleRepId') saleRepId: string, @Query() { startDate, endDate }: { startDate: Date; endDate: Date }, @I18n() i18n: I18nContext) {
    return new ApiResponse(await this.repManagerService.getDetailSaleRepData(saleRepId, i18n, startDate, endDate));
  }

  @Get('asm/sale-reps/:saleRepId/visit-data')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  @ApiOperation({
    summary: 'Get visit data for sale rep details',
  })
  async getVisitData(
    @Param('saleRepId') saleRepId: string,
    @Query() { startDate, endDate }: { startDate: Date; endDate: Date },
    @Query() paginationParams: PaginationRequestParamsDto,
    @I18n() i18n: I18nContext,
  ) {
    return new ApiResponse(await this.repManagerService.getVisitData(saleRepId, i18n, startDate, endDate, paginationParams.offset, paginationParams.limit));
  }

  @Get('asm/sale-reps/:saleRepId/performance')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  @ApiOperation({
    summary: 'Get performance for saleRep',
  })
  async getSaleRepDetailPerformance(
    @Param('saleRepId') saleRepId: string,
    @Query() { startDate, endDate }: { startDate: Date; endDate: Date },
    @CurrentUser() user: any,
    @I18n() i18n: I18nContext,
  ) {
    const relatedContactIds = user?.businessPartnerRelations?.contactIds || [];
    const saleRepRelationData = await this.businessPartnerRelationsService.findRelationKeyForContact(saleRepId, null, BusinessPartnerContactRole.SALE_REP);
    const { depotKey, contactKey, contactId } = saleRepRelationData;
    if (!depotKey || !contactKey || !relatedContactIds.includes(contactId)) {
      return new ApiResponse(null);
    }
    return new ApiResponse(await this.repPerformanceKpiService.getK360MetricsForSalesRep(depotKey, [contactKey], startDate, endDate, i18n));
  }

  @Post('asm/sale-reps/execution/discipline')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  async getExecutionDiscipline(@Body() payload: SaleRepExecutionDisciplineSearchDto, @I18n() i18n: I18nContext) {
    const { endDate, startDate, depotKey, salesRepKeys, type } = payload;
    if (!depotKey?.length) {
      throw new HttpException(await i18n.translate('saleRep.invalid_depot_key'), HttpStatus.BAD_REQUEST);
    }

    const depotSaleReps = await this.businessPartnerRelationsService.findSaleRepsByDepot(depotKey);
    const saleRepsMapByKey = new Map(depotSaleReps.map((sr) => [sr?.saleRepContactKey, sr]));
    const validSaleReps = salesRepKeys?.length ? salesRepKeys.filter((sRKey) => saleRepsMapByKey.get(sRKey)) : (depotSaleReps || []).map((dSR) => dSR.saleRepContactKey);
    const result = await this.repPerformanceKpiService.getExecutionDiscipline(validSaleReps, startDate, endDate, type, i18n);
    return new ApiResponse(result);
  }

  @Post('asm/sale-reps/in-active-outlet')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  async getInActiveOutlet(@Body() payload: SaleRepExecutionDisciplineSearchDto, @I18n() i18n: I18nContext) {
    const { endDate, startDate, depotKey, salesRepKeys, type } = payload;
    if (!depotKey?.length) {
      throw new HttpException(await i18n.translate('saleRep.invalid_depot_key'), HttpStatus.BAD_REQUEST);
    }

    const depotSaleReps = await this.businessPartnerRelationsService.findSaleRepsByDepot(depotKey);
    const saleRepsMapByKey = new Map(depotSaleReps.map((sr) => [sr?.saleRepContactKey, sr]));
    const validSaleReps = salesRepKeys?.length ? salesRepKeys.filter((sRKey) => saleRepsMapByKey.get(sRKey)) : (depotSaleReps || []).map((dSR) => dSR.saleRepContactKey);
    const result = await this.repPerformanceKpiService.getInActiveOutlet(validSaleReps, startDate, endDate, type, i18n);
    return new ApiResponse(result);
  }

  @Post('asm/sale-reps/adhoc-order')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  async getAdhocOrder(@Body() payload: SaleRepExecutionDisciplineSearchDto, @I18n() i18n: I18nContext) {
    const { endDate, startDate, depotKey, salesRepKeys, type } = payload;
    if (!depotKey?.length) {
      throw new HttpException(await i18n.translate('saleRep.invalid_depot_key'), HttpStatus.BAD_REQUEST);
    }

    const depotSaleReps = await this.businessPartnerRelationsService.findSaleRepsByDepot(depotKey);
    const saleRepsMapByKey = new Map(depotSaleReps.map((sr) => [sr?.saleRepContactKey, sr]));
    const validSaleReps = salesRepKeys?.length ? salesRepKeys.filter((sRKey) => saleRepsMapByKey.get(sRKey)) : (depotSaleReps || []).map((dSR) => dSR.saleRepContactKey);
    const result = await this.repPerformanceKpiService.getAdHocOrder(validSaleReps, startDate, endDate, type, i18n);
    return new ApiResponse(result);
  }

  @Post('asm/sale-reps/visited-outlet')
  @Roles(ConstantRoles.AREA_SALES_REP_MANAGER)
  async getVisitedOutlet(@Body() payload: SaleRepExecutionDisciplineSearchDto, @I18n() i18n: I18nContext) {
    const { endDate, startDate, depotKey, salesRepKeys, type } = payload;
    if (!depotKey?.length) {
      throw new HttpException(await i18n.translate('saleRep.invalid_depot_key'), HttpStatus.BAD_REQUEST);
    }

    const depotSaleReps = await this.businessPartnerRelationsService.findSaleRepsByDepot(depotKey);
    const saleRepsMapByKey = new Map(depotSaleReps.map((sr) => [sr?.saleRepContactKey, sr]));
    const validSaleReps = salesRepKeys?.length ? salesRepKeys.filter((sRKey) => saleRepsMapByKey.get(sRKey)) : (depotSaleReps || []).map((dSR) => dSR.saleRepContactKey);
    const result = await this.repPerformanceKpiService.getVisitedOutlet(validSaleReps, startDate, endDate, type, i18n);
    return new ApiResponse(result);
  }
}
