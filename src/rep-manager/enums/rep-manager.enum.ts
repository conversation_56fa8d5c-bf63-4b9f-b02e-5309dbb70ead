export enum RepTimeOffStatus {
  PENDING = 'PENDING',
  CANCELED = 'CANCELED',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
}

export enum RepLeaveTypes {
  PAID_LEAVE = 'PAID_LEAVE',
  SICK_LEAVE = 'SICK_LEAVE',
}

export enum SessionStatus {
  CANCELED = 'CANCELED',
  PLANNED = 'PLANNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

export enum SessionCreatorType {
  MANAGER = 'MANAGER',
  AGENT = 'AGENT',
}

export enum SessionHistoryStatus {
  ONGOING = 'ONGOING',
  COMPLETED = 'COMPLETED',
}

export enum CoachingStepType {
  STEP_QUESTION = 'STEP_QUESTION',
  STEP_REPORTVIEW = 'STEP_REPORTVIEW',
}

export enum SessionSalesRepStatus {
  IDLE = 'IDLE',
  VISITING = 'VISITING',
}

export enum QuestionType {
  NUMBER = 'NUMBER',
  STRING = 'STRING',
  BOOLEAN = 'BOOLEAN',
  OBJECT = 'OBJECT',
  RATING = 'RATING',
}
