import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { BusinessPartnerContact } from '../../master-data/entities/business-partner-contact/business-partner-contact.entity';

@Entity('rep_performance_kpis')
export class RepPerformanceKpi extends BaseSQLEntity {
  @ManyToOne(() => BusinessPartnerContact, { nullable: false })
  @JoinColumn({ name: 'salesRepId', referencedColumnName: 'id' })
  @Index()
  salesRep: BusinessPartnerContact;

  @Column({ type: 'uuid' })
  salesRepId: string;

  @Column({ nullable: false })
  month: number;

  @Column({ nullable: false })
  year: number;

  @Column({ nullable: true, type: 'decimal', default: 0 })
  volume: number;

  @Column({
    type: 'jsonb',
    nullable: true,
    default: { value: 0, target: 0 },
  })
  volumes: object;

  @Column({ nullable: false, default: 0 })
  eoe: number;

  @Column({ nullable: false, type: 'decimal', precision: 10, scale: 0, default: 0 })
  activeSellingOutlet: number;

  @Column({ nullable: false, type: 'decimal', precision: 10, scale: 0, default: 0 })
  strikeRate: number;

  @Column({
    type: 'jsonb',
    nullable: true,
    default: {
      value: 0,
      target: 0,
    },
  })
  strikeRates: object;

  @Column({ nullable: true, default: 0 })
  mustHaveSku: number;

  @Column({
    type: 'jsonb',
    nullable: true,
    default: {
      totalMustHaveSkuQuantity: 0,
      totalSkuQuantity: 0,
    },
  })
  mustHaveSkus: object;

  @Column({ type: 'int', default: 0 })
  priority: number;

  @Column({ type: 'uuid', nullable: true })
  depotId: string;

  @Column({ type: 'uuid', nullable: true, default: null })
  distributorId: string;
}
