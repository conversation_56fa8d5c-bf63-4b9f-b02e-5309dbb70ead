import { BaseSQLEntity } from 'src/shared/basesql.entity';
import { Column, Entity } from 'typeorm';

@Entity('rep-target_settings')
export class RepTargetSettings extends BaseSQLEntity {
  @Column({ nullable: false })
  salesRepName: string;
  /**
   * The ID of the agent this target is assigned to
   * @type {string}
   */
  @Column({ nullable: false })
  salesRepExternalId: string;
  /**
   * The strike rate target value
   * @type {number}
   */
  @Column({ type: 'decimal', default: 0 })
  volumeTarget: number;

  /**
   * The strike rate target value
   * @type {number}
   */
  @Column({ type: 'decimal', precision: 10, scale: 0, default: 0 })
  strikeRate: number;

  /**
   * The active selling outlet target value
   * @type {number}
   */
  @Column({ type: 'decimal', precision: 10, scale: 0, default: 0 })
  activeSellingOutlet: number;

  /**
   * The month this target is for (1-12)
   * @type {number}
   */
  @Column({ type: 'int' })
  month: number;

  /**
   * The year this target is for
   * @type {number}
   */
  @Column({ type: 'int' })
  year: number;
}
