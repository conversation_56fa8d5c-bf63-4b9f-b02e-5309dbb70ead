import { BaseSQLEntity } from 'src/shared/basesql.entity';
import { Colum<PERSON>, Enti<PERSON>, ManyToOne, JoinColumn } from 'typeorm';
import { RepCoachingQuestion } from './rep-coaching-question.entity';
import { RepCoachingSessionHistories } from './rep-coaching-session-histories.entity';

@Entity('rep-coaching-answer')
export class RepCoachingAnswer extends BaseSQLEntity {
  @Column({ nullable: false })
  questionId: string;

  @ManyToOne(() => RepCoachingQuestion, { nullable: false })
  @JoinColumn({ name: 'questionId' })
  question: RepCoachingQuestion;

  @Column({ nullable: true })
  contentValue: string;

  @Column({ nullable: false })
  repCoachingSessionHistoriesId: string;

  @ManyToOne(() => RepCoachingSessionHistories, { nullable: false })
  @JoinColumn({ name: 'repCoachingSessionHistoriesId' })
  repCoachingSessionHistories: RepCoachingSessionHistories;
}
