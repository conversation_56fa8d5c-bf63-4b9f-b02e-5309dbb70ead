import { Column, Entity, Index, Join<PERSON>olumn, ManyToOne } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { RepCoachingSessions } from './rep-coaching-session.entity';
import { BusinessPartner } from '../../master-data/entities/business-partner/business-partner.entity';

@Entity('rep_coaching_session_checklist')
export class RepCoachingSessionChecklist extends BaseSQLEntity {
  @Column({ nullable: true })
  label: string;

  @Column({ default: false })
  checked: boolean;

  @Column({ default: false })
  isCoaching: boolean;

  @Column({ type: 'uuid' })
  sessionId: string;

  @ManyToOne(() => RepCoachingSessions, { nullable: true })
  @JoinColumn({ name: 'sessionId', referencedColumnName: 'id' })
  @Index()
  session: RepCoachingSessions;

  @Column({ type: 'uuid' })
  outletId: string;

  @ManyToOne(() => BusinessPartner, { nullable: true })
  @JoinColumn({ name: 'outletId', referencedColumnName: 'id' })
  @Index()
  outlet: BusinessPartner;
}
