import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { RepCoachingSessions } from './rep-coaching-session.entity';
import { CoachingStepType, SessionHistoryStatus } from '../enums/rep-manager.enum';
import { BusinessPartner } from '../../master-data/entities/business-partner/business-partner.entity';
import { RepCoachingAnswer } from './rep-coaching-answer.entity';

@Entity('rep_coaching_session_histories')
export class RepCoachingSessionHistories extends BaseSQLEntity {
  @ManyToOne(() => RepCoachingSessions, (call) => call.histories, { nullable: false })
  session: RepCoachingSessions;

  @ManyToOne(() => BusinessPartner, { nullable: false })
  @JoinColumn({ referencedColumnName: 'id' })
  @Index()
  outlet: BusinessPartner;

  @Column({ type: 'timestamptz', nullable: true })
  startTime: Date;

  @Column({ type: 'timestamptz', nullable: true })
  endTime: Date;

  @Column({ type: 'jsonb', nullable: true })
  location: {
    latitude: number;
    longitude: number;
  };

  @Column({ type: 'float', nullable: true })
  locationRange: number;

  @Column({ type: 'boolean', default: false })
  hasOrder: boolean;

  @Column({ type: 'enum', enum: SessionHistoryStatus, default: SessionHistoryStatus.ONGOING })
  status: SessionHistoryStatus;

  @Column({ nullable: true })
  managerNote: string;

  @Column({ nullable: true })
  salesRepNote: string;

  @Column({ nullable: true })
  outletNote: string;

  @OneToMany(() => RepCoachingAnswer, (answer) => answer.repCoachingSessionHistories)
  answers: RepCoachingAnswer[];

  @Column({
    type: 'enum',
    enum: CoachingStepType,
    nullable: true,
  })
  step: CoachingStepType;
}
