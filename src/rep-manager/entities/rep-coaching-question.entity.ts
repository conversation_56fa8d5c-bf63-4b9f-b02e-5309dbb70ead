import { BaseSQLEntity } from 'src/shared/basesql.entity';
import { Column, Entity, OneToMany } from 'typeorm';
import { QuestionType } from '../enums/rep-manager.enum';
import { RepCoachingAnswer } from './rep-coaching-answer.entity';

@Entity('rep-coaching-question')
export class RepCoachingQuestion extends BaseSQLEntity {
  @Column({ type: 'text', nullable: false })
  title: string;

  @Column({ type: 'text', nullable: false })
  text: string;

  @Column({
    type: 'enum',
    enum: QuestionType,
    nullable: false,
  })
  type: QuestionType;

  @Column({ type: 'boolean', default: false })
  required: boolean;

  @Column({ type: 'int', nullable: true })
  sortOrder: number;

  @OneToMany(() => RepCoachingAnswer, (answer) => answer.question)
  answers: RepCoachingAnswer[];
}
