import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { BusinessPartnerContact } from '../../master-data/entities/business-partner-contact/business-partner-contact.entity';
import { RepLeaveTypes, RepTimeOffStatus } from '../enums/rep-manager.enum';

@Entity('rep-request_time_offs')
export class RepRequestTimeOff extends BaseSQLEntity {
  @ManyToOne(() => BusinessPartnerContact, { nullable: false })
  @JoinColumn()
  @Index()
  salesRep: BusinessPartnerContact;

  @ManyToOne(() => BusinessPartnerContact, { nullable: false })
  @JoinColumn()
  @Index()
  repManagement: BusinessPartnerContact;

  @Column({ type: 'timestamptz', nullable: false })
  fromTime: Date;

  @Column({ type: 'timestamptz', nullable: false })
  toTime: Date;

  @Column({ type: 'enum', enum: RepTimeOffStatus, default: RepTimeOffStatus.PENDING, nullable: false })
  status: RepTimeOffStatus;

  @Column({ type: 'enum', enum: RepLeaveTypes, default: RepLeaveTypes.PAID_LEAVE, nullable: false })
  type: RepLeaveTypes;

  @Column({ type: 'varchar', nullable: false })
  saleNote: string;

  @Column({ type: 'varchar', nullable: true })
  managementNote: string;
}
