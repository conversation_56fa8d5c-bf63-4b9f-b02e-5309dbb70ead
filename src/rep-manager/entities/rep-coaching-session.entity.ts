import { BeforeInsert, BeforeUpdate, Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { BaseSQLEntity } from '../../shared/basesql.entity';
import { BusinessPartner } from '../../master-data/entities/business-partner/business-partner.entity';
import { BusinessPartnerContact } from '../../master-data/entities/business-partner-contact/business-partner-contact.entity';
import { printLog } from '../../utils';
import { SessionCreatorType, SessionStatus } from '../enums/rep-manager.enum';
import { RepCoachingSessionHistories } from './rep-coaching-session-histories.entity';
import { RepCoachingOrders } from './rep-coaching-order.entity';

@Entity('rep_coaching_sessions')
export class RepCoachingSessions extends BaseSQLEntity {
  @ManyToOne(() => BusinessPartner, { nullable: true })
  @JoinColumn({ referencedColumnName: 'id' })
  @Index()
  outlet: BusinessPartner;

  @Column({ type: 'uuid' })
  salesRepId: string;

  @ManyToOne(() => BusinessPartnerContact, { nullable: false })
  @JoinColumn({ name: 'salesRepId', referencedColumnName: 'id' })
  @Index()
  salesRep: BusinessPartnerContact;

  @Column({ type: 'timestamptz', nullable: false })
  day: Date;

  @Column({ default: false })
  rescheduled: boolean;

  @Column({ type: 'timestamptz', nullable: true })
  rescheduledDay: Date;

  @Column({ nullable: true })
  rescheduleReason: string;

  @Column({ type: 'enum', enum: SessionStatus, default: SessionStatus.PLANNED })
  sessionStatus: SessionStatus;

  @Column({ type: 'int', default: 0 })
  priority: number;

  @Column({ type: 'timestamptz', nullable: true })
  displayDay: Date;

  @BeforeInsert()
  @BeforeUpdate()
  updateDisplayDay() {
    printLog('BeforeSave', this.rescheduled, this.rescheduledDay, this.day);
    this.displayDay = this.rescheduled && this.rescheduledDay ? this.rescheduledDay : this.day;
  }

  @Column({ type: 'uuid', nullable: false })
  depotId: string;

  @Column({ type: 'uuid', nullable: false })
  distributorId: string;

  @Column({ type: 'enum', enum: SessionCreatorType, nullable: true })
  creator: SessionCreatorType;

  @OneToMany(() => RepCoachingSessionHistories, (session) => session.session)
  histories: RepCoachingSessionHistories[];

  @Column({ type: 'boolean', default: false })
  hasOrder: boolean;

  @OneToMany(() => RepCoachingOrders, (order) => order.session)
  orders: RepCoachingOrders[];

  @Column({ nullable: true })
  coachingNote: string;
}
