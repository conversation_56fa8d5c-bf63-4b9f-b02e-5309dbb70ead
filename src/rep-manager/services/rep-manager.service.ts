import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { UsersService } from '../../users/services/users.service';
import { OutletJourneyPlanningService } from '../../journey-plannings/services/outlet-journey-planning.service';
import { OutletJourneyPlanning } from '../../journey-plannings/schemas/outlet-journey-planning.schema';
import { VisitStatus } from '../../journey-plannings/enums/visit-status.enum';
import * as moment from 'moment-timezone';
import { RepPerformanceKpiService } from './rep-performance-kpi.service';
import { RepPerformanceKpi } from '../entities/rep-performance-kpi.entity';
import { Between, FindOptionsWhere, In } from 'typeorm';
import { RoutePlanService } from '../../sale-rep/services/route-plan.service';
import { RepCoachingSessionsService } from './rep-coaching-sessions.service';
import { SessionStatus } from '../enums/rep-manager.enum';
import { BusinessPartnerContactRole } from 'src/master-data/constants/business-partner.enum';
import { SaleRepSearchRequestDto } from '../dto/sale-rep-search-request.dto';
import { RepCoachingSessionChecklistService } from './rep-coaching-session-checklist.service';
import { CoachingPerformanceQueryDto } from '../dto/coaching-performance.dto';
import { BusinessPartnerRelationService } from 'src/master-data/services/business-partners-relation.service';
import { I18nContext } from 'nestjs-i18n';
import { OmsSalesRepStatisticsService } from '../../oms/services/sales-rep-statistics.service';
import { BusinessPartnerOutletService } from '../../master-data/services/business-partner-outlet.service';
import { OutletsService } from '../../outlets/services/outlets.service';

@Injectable()
export class RepManagerService {
  constructor(
    private readonly userService: UsersService,
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly repPerformanceKpiService: RepPerformanceKpiService,
    private readonly routePlanService: RoutePlanService,
    private readonly businessPartnerContactService: BusinessPartnersContactService,
    private readonly coachingSessionService: RepCoachingSessionsService,
    private readonly checklistService: RepCoachingSessionChecklistService,

    @Inject(forwardRef(() => BusinessPartnerRelationService))
    private readonly businessPartnerRelationService: BusinessPartnerRelationService,
    @Inject(forwardRef(() => BusinessPartnerOutletService))
    private readonly businessPartnerOutletService: BusinessPartnerOutletService,
    @Inject(forwardRef(() => OutletsService))
    private readonly outletsService: OutletsService,
    private readonly omsSalesRepStatisticsService: OmsSalesRepStatisticsService,
  ) {}

  async getASMSaleRepsMetrics(saleRepIds: string[], month: number, year: number) {
    const current = moment().tz(process.env.TZ);
    if (!year) {
      year = current.get('year');
    }

    if (isNaN(month) || 1 > month || month > 12) {
      month = current.get('month') + 1;
    }

    const saleReps = await this.businessPartnerContactService.find({
      where: {
        isDeleted: false,
        id: In(saleRepIds),
        businessPartnerContactPersonRole: BusinessPartnerContactRole.SALE_REP,
      },
      select: ['id', 'businessPartnerContactKey'],
    });

    const saleRepKeys = [],
      saleRepPostgresIds = [];
    for (let i = 0; i < saleReps.length; i++) {
      const saleRep = saleReps[i];
      saleRepKeys.push(saleRep.businessPartnerContactKey);
      saleRepPostgresIds.push(saleRep.id);
    }
    // const saleRepKeys = [...new Set(saleReps.map((sR) => sR.businessPartnerContactKey))];
    // const saleRepUserDetails = await this.userService.getSalesRepDetailsByIds(saleRepKeys as string[]);

    const [todaySessionsCount, { totalVolumes, totalASO, totalStrikeRate }] = await Promise.all([
      this.getSaleRepTodayCoachingSessions(saleRepPostgresIds),
      this.getPerformanceKpiBySaleReps(saleReps, month, year, 6),
    ]);

    let totalJp = 0,
      totalJpCompleted = 0;
    for (let i = 0; i < todaySessionsCount.length; i++) {
      const sessionCount = todaySessionsCount[i];
      const sessionCountTotal = Number(sessionCount?.total || 0);
      if (sessionCount?.sessionStatus === SessionStatus.COMPLETED) {
        totalJpCompleted = sessionCountTotal;
      }
      totalJp += sessionCountTotal;
    }

    return {
      todayJp: {
        totalJp,
        totalJpCompleted,
      },
      totalVolumes,
      totalASO,
      totalStrikeRate,
    };
  }

  async searchSaleReps(request: SaleRepSearchRequestDto) {
    const { limit, offset, orderBy, orderDesc, saleRepIds, searchText, startDate, endDate } = request;

    const current = moment().tz(process.env.TZ);
    const year = current.get('year');
    const month = current.get('month') + 1;

    const startDateMoment = (startDate && moment.tz(startDate, process.env.TZ).startOf('day')) || moment.tz({ year, month: month - 1, day: 1 }, process.env.TZ).startOf('month');
    const endDateMoment = (endDate && moment.tz(endDate, process.env.TZ).endOf('day')) || startDateMoment.clone().endOf('month');

    const saleReps = await this.getSaleRepAndCoachingSessionsForASMReport(
      searchText,
      saleRepIds,
      null,
      startDateMoment.toDate(),
      endDateMoment.toDate(),
      orderBy,
      orderDesc,
      limit,
      offset,
      !!(startDate || endDate),
    );
    const saleRepKeys = [...new Set(saleReps.map((sR) => sR.businessPartnerContactKey))];
    const saleRepUserDetails = await this.userService.getSalesRepDetailsByIds(saleRepKeys as string[]);

    const [{ jpBySaleRepMap }, { kpisBySRMap }] = await Promise.all([
      this.getJourneyPlansBySaleReps(saleRepUserDetails),
      this.getPerformanceKpiBySaleReps(saleReps, month, year, 1),
    ]);

    return {
      saleReps: saleReps.map(({ totalCount, ...sR }) => {
        const jpsBySaleRep = jpBySaleRepMap.get(sR.businessPartnerContactKey) || [];
        let saleRepVisitStatus = 'IDLE';

        // let outlet;
        // temporaly set mock data for journey planning customer
        let outlet;

        const { inprogressJp, dueDateJp } = jpsBySaleRep?.reduce(
          (acc, jp) => {
            const inprogressJpCurrentDay = acc.inprogressJp?.rescheduled ? acc.inprogressJp?.rescheduledDay : acc.inprogressJp?.day;
            // const dueDateJpCurrentDay = acc.dueDateJp?.rescheduled ? acc.dueDateJp?.rescheduledDay : acc.dueDateJp?.day;
            const jpVisitDate = moment(jp.rescheduled ? jp.rescheduledDay : jp.day).tz(process.env.TZ);
            const isSameOrBeforeCurrentTime = jpVisitDate.isSameOrBefore(current);
            if (
              ((!inprogressJpCurrentDay || (isSameOrBeforeCurrentTime && moment(inprogressJpCurrentDay).tz(process.env.TZ).isSameOrAfter(jpVisitDate))) &&
                jp.visitStatus === VisitStatus.IN_PROGRESS) ||
              jp.visitStatus === VisitStatus.START_VISIT
            ) {
              acc.inprogressJp = jp;
            }

            // if (!dueDateJpCurrentDay || (isSameOrBeforeCurrentTime && moment(dueDateJpCurrentDay).tz(process.env.TZ).isSameOrAfter(jpVisitDate))) {
            //   acc.dueDateJp = jp;
            // }
            return acc;
          },
          { inprogressJp: undefined, dueDateJp: undefined },
        );

        if (inprogressJp) {
          saleRepVisitStatus = 'VISITING';
          outlet = inprogressJp?.outlet;
        }

        return {
          ...sR,
          visitStatus: saleRepVisitStatus,
          currentCustomer: outlet,
          performanceKPIs: (kpisBySRMap.get(sR.businessPartnerContactKey) || []).find((kpi) => kpi?.month === month) || {},
        };
      }),
      total: saleReps[0]?.totalCount,
    };
  }

  async getSaleRepAndCoachingSessionsForASMReport(
    searchText: string,
    saleRepIds: string[],
    saleRepKeys: string[],
    startDate: Date,
    endDate: Date,
    orderBy: 'name' | 'displayDay',
    orderDesc: 'ASC' | 'DESC',
    limit: number,
    offset: number,
    mustHaveCoachingSession: boolean = false,
  ) {
    let searchKeyCondition = `contact."isDeleted" = false
      AND contact."businessPartnerContactPersonRole" = '${BusinessPartnerContactRole.SALE_REP}'`;
    if (saleRepIds?.length) {
      searchKeyCondition = `${searchKeyCondition} AND contact.id IN (${saleRepIds.map((id) => `'${id}'`).join(', ')})`;
    }

    if (saleRepKeys?.length) {
      searchKeyCondition = `${searchKeyCondition} AND contact."businessPartnerContactKey" IN (${saleRepKeys.map((key) => `'${key}'`).join(', ')})`;
    }

    if (!!searchText) {
      searchKeyCondition = `${searchKeyCondition} AND (contact."businessPartnerContactKey" = '${searchText}' 
        OR contact."businessPartnerContactName1" = '${searchText}'
        OR contact."businessPartnerContactName2" = '${searchText}')`;
    }

    const sqlQuery = `
      WITH ranked_sessions AS (
        SELECT
            bpc.id,
            bpc."businessPartnerContactKey",
            bpc."businessPartnerContactName1",
            bpc."businessPartnerContactName2",
            rcs.id as "coachingSessionId",
            rcs."displayDay",
            rcs."sessionStatus",
            rcs."createdAt",
            rcs."updatedAt",
            ROW_NUMBER() OVER (
                PARTITION BY bpc.id 
                ORDER BY rcs."displayDay" DESC NULLS LAST
            ) as rn,
            bpc."totalCount"
        FROM (
          SELECT *, COUNT(*) OVER () as "totalCount"
          FROM "business_partner_contacts" contact
          WHERE ${searchKeyCondition}
        ) bpc
        ${(mustHaveCoachingSession && 'JOIN') || 'LEFT JOIN'} rep_coaching_sessions rcs
          ON bpc.id = rcs."salesRepId"
          AND rcs."isDeleted" = false
          AND rcs."displayDay" BETWEEN $1 AND $2
    )
    SELECT
      id,
      "totalCount",
      "businessPartnerContactKey",
      "businessPartnerContactName1",
      "businessPartnerContactName2",
      "coachingSessionId",
      "displayDay",
      "sessionStatus",
      "createdAt",
      "updatedAt"
      FROM ranked_sessions
      WHERE rn = 1
      ${
        orderBy
          ? `ORDER BY ${
              (orderBy === 'name' && `"businessPartnerContactName1" ${orderDesc || ''} , "businessPartnerContactName2" ${orderDesc || ''}`) || `"displayDay" ${orderDesc || ''}`
            }`
          : ''
      }
      ${(limit && `LIMIT ${limit}`) || ''} ${(offset && `OFFSET ${offset}`) || ''};
    `
      .replace(/\n/g, ' ')
      .trim();

    return this.businessPartnerContactService.rawQuery(sqlQuery, [startDate, endDate]);
  }

  async getJourneyPlansBySaleReps(saleRepUserDetails: { id: string; saleRepId: string }[]) {
    const saleRepUserIds = saleRepUserDetails.map((d) => d.id);
    const journeyPlannings = await this.outletJourneyPlanningService.getAllTodayPlans(saleRepUserIds);

    const { journeyPlanningMap, totalJpCompleted } = journeyPlannings.reduce(
      (acc, crr) => {
        let saleRepJps = acc.journeyPlanningMap.get(crr?.saleRep?._id);
        if (!saleRepJps) {
          saleRepJps = [];
        }
        saleRepJps.push(crr);
        acc.journeyPlanningMap.set(crr?.saleRep?._id?.toString(), saleRepJps);
        if (crr.visitStatus === VisitStatus.COMPLETED) {
          acc.totalJpCompleted++;
        }
        return acc;
      },
      {
        journeyPlanningMap: new Map<string, OutletJourneyPlanning[]>(),
        totalJpCompleted: 0,
      },
    );

    const saleRepKeysToIds = saleRepUserDetails.map((d) => [d.saleRepId, d.id]);
    return {
      jpBySaleRepMap: new Map<string, any[]>(saleRepKeysToIds.map(([key, id]) => [key, journeyPlanningMap.get(id)])),
      totalJp: journeyPlannings.length,
      totalJpCompleted,
    };
  }

  async getSaleRepTodayCoachingSessions(saleRepIds: string[]) {
    const current = moment.tz(process.env.TZ);
    const startDate = current.clone().startOf('day').toDate();
    const endDate = current.clone().endOf('day').toDate();
    return this.coachingSessionService.countCoachingSessionsInRange(saleRepIds, startDate, endDate);
  }

  async getPerformanceKpiBySaleReps(saleReps: any[], endMonth: number, year: number, numberOfMonthCount: number) {
    const saleRepIds = saleReps.map((sRD) => sRD.id);
    const baseSearchCondition: FindOptionsWhere<RepPerformanceKpi> = {
      isDeleted: false,
      salesRep: {
        id: In(saleRepIds),
      },
    };
    const result = new Map<string, number[]>();
    const searchMonthYearKeys: string[] = [];
    for (let i = 0; i < numberOfMonthCount; i++) {
      const monthsAgo = endMonth - i;
      const currentYear = year - (monthsAgo > 0 ? 0 : 1);
      const monthNumberInYear = monthsAgo > 0 ? monthsAgo : monthsAgo + 12;

      if (!result[`${currentYear}`]) {
        result[`${currentYear}`] = [];
      }
      result[`${currentYear}`].push(monthNumberInYear);
      searchMonthYearKeys.push(`${monthNumberInYear}-${currentYear}`);
    }
    const searchYears = Object.keys(result);
    let searchConditions: FindOptionsWhere<RepPerformanceKpi> | FindOptionsWhere<RepPerformanceKpi>[] =
      searchYears.length > 1
        ? searchYears
            .map((searchYear) => {
              const months = result[searchYear];
              if (!months?.length) {
                return null;
              }
              return {
                ...baseSearchCondition,
                month: In(months),
                year: +searchYear,
              };
            })
            .filter((sC) => !!sC)
        : {
            ...baseSearchCondition,
            month: In(result[year]),
            year,
          };

    const saleRepKpis = await this.repPerformanceKpiService.getPerformanceForSaleReps({
      where: searchConditions,
    });

    const { performanceKpisBySR, performanceKpiByMonth } = (saleRepKpis || []).reduce(
      (acc, crr) => {
        let saleRepKpis = acc.performanceKpisBySR.get(crr?.salesRepId);
        const monthYearKey = `${crr?.month}-${crr?.year}`;
        let saleRepTotalPerformanceKpiByMonth = acc.performanceKpiByMonth.get(monthYearKey);
        if (!saleRepTotalPerformanceKpiByMonth) {
          saleRepTotalPerformanceKpiByMonth = {
            totalVolumes: 0,
            totalStrikeRate: 0,
            totalASO: 0,
          };
        }
        const { volumes, strikeRates, activeSellingOutlet } = crr || {};
        const crrVolumesValue = Number((volumes as any)?.value);
        const crrStrikeRateValue = Number((strikeRates as any)?.value);
        // const crrMustHaveSkusValue = Number((mustHaveSkus as any)?.value)
        if (crrVolumesValue) {
          saleRepTotalPerformanceKpiByMonth.totalVolumes = (saleRepTotalPerformanceKpiByMonth?.totalVolumes || 0) + crrVolumesValue;
        }
        if (crrStrikeRateValue) {
          saleRepTotalPerformanceKpiByMonth.totalStrikeRate = (saleRepTotalPerformanceKpiByMonth?.totalStrikeRate || 0) + crrStrikeRateValue;
        }
        if (activeSellingOutlet) {
          saleRepTotalPerformanceKpiByMonth.totalASO = (saleRepTotalPerformanceKpiByMonth?.totalASO || 0) + Number(activeSellingOutlet);
        }

        if (!saleRepKpis) {
          saleRepKpis = [];
        }
        saleRepKpis.push(crr);
        acc.performanceKpiByMonth.set(monthYearKey, saleRepTotalPerformanceKpiByMonth);
        acc.performanceKpisBySR.set(crr?.salesRepId, saleRepKpis);
        return acc;
      },
      {
        performanceKpisBySR: new Map<string, RepPerformanceKpi[]>(),
        performanceKpiByMonth: new Map<string, any>(),
      },
    );

    const totalVolumesMonthlyData = [],
      totalStrikeRateMonthlyData = [],
      totalASOMonthlyData = [];

    for (let i = searchMonthYearKeys.length - 1; i >= 0; i--) {
      const dataKey = searchMonthYearKeys[i];
      const monthPerformance = performanceKpiByMonth.get(searchMonthYearKeys[i]);
      totalVolumesMonthlyData.push({ month: dataKey, value: monthPerformance?.totalVolumes || 0 });
      totalStrikeRateMonthlyData.push({ month: dataKey, value: monthPerformance?.totalStrikeRate || 0 });
      totalASOMonthlyData.push({ month: dataKey, value: monthPerformance?.totalASO || 0 });
    }

    const saleRepKeysToIds = saleReps.map((d) => [d.businessPartnerContactKey, d.id]);
    const currentMonthYearKey = searchMonthYearKeys[0];
    // const prevMonthYearKey = searchMonthYearKeys[1];
    const kpiSelectedMonth = performanceKpiByMonth.get(currentMonthYearKey) || {
      totalVolumes: 0,
      totalStrikeRate: 0,
      totalASO: 0,
    };
    const kpiPrevMonth = performanceKpiByMonth.get(searchMonthYearKeys[1]) || {
      totalVolumes: 0,
      totalStrikeRate: 0,
      totalASO: 0,
    };
    return {
      kpisBySRMap: new Map(saleRepKeysToIds.map(([key, id]) => [key, performanceKpisBySR.get(id)])),
      totalVolumes: {
        month: currentMonthYearKey,
        selectedMonth: kpiSelectedMonth?.totalVolumes,
        prevMonth: kpiPrevMonth?.totalVolumes,
        values: totalVolumesMonthlyData,
      },
      totalStrikeRate: {
        month: currentMonthYearKey,
        selectedMonth: kpiSelectedMonth?.totalStrikeRate,
        prevMonth: kpiPrevMonth?.totalStrikeRate,
        values: totalStrikeRateMonthlyData,
      },
      totalASO: {
        month: currentMonthYearKey,
        selectedMonth: kpiSelectedMonth?.totalASO,
        prevMonth: kpiPrevMonth?.totalASO,
        values: totalASOMonthlyData,
      },
    };
  }

  async getSaleRepTodayCoachingSessionData(saleRepId: string) {
    const today = moment().tz(process.env.TZ);
    const [contactData, user] = await Promise.all([
      this.businessPartnerContactService.findOne({
        where: {
          businessPartnerContactKey: saleRepId,
        },
        select: ['id', 'businessPartnerContactKey', 'businessPartnerContactName1', 'businessPartnerContactName2'],
      }),
      this.userService.findOne({
        saleRepId: saleRepId,
      }),
    ]);
    const [journeyPlannings, coachingSessions] = await Promise.all([
      this.routePlanService.getRoutePlanData({ salesRep: user }),
      this.coachingSessionService.find({
        where: {
          salesRep: {
            businessPartnerContactKey: saleRepId,
          },
          isDeleted: false,
          displayDay: Between(today.startOf('day').toDate(), today.endOf('day').toDate()),
        },
        relations: ['salesRep'],
      }),
    ]);

    return {
      contactData,
      user,
      journeyPlannings,
      coachingSessions,
    };
  }

  async getDetailSaleRepData(saleRepId: string, i18n: I18nContext, startDate?: Date, endDate?: Date) {
    const current = moment().tz(process.env.TZ);
    if (!startDate) {
      startDate = current.clone().startOf('day').toDate();
    }
    if (!endDate) {
      endDate = current.clone().endOf('day').toDate();
    }
    // const currentMonth = current.get('month') + 1;

    const checklistQueryDto = new CoachingPerformanceQueryDto();
    checklistQueryDto.month = current.get('month') + 1;
    checklistQueryDto.salesRepId = saleRepId;
    const saleRepOutlets = (await this.businessPartnerRelationService.findOutletsAndDepotsByContact(saleRepId, null)) || [];
    const depotKey = saleRepOutlets[0]?.depotKey;
    const saleRepKey = saleRepOutlets[0]?.contactKey;
    const outletIds = new Set<string>(),
      outletKeys = new Set<string>();
    for (let i = 0; i < saleRepOutlets.length; i++) {
      outletIds.add(saleRepOutlets[i].outletId);
      outletKeys.add(saleRepOutlets[i].outletKey);
    }

    const errorLogs = (error, name) => console.log(`Error fetch saleRep details data ${name}`, error);

    const [checklists, k360Performance, outlets] = await Promise.all([
      this.checklistService.listChecklistItems(checklistQueryDto, null).catch((error) => errorLogs(error, 'checklists')),
      this.repPerformanceKpiService.getK360MetricsForSalesRep(depotKey, [saleRepKey], startDate, endDate, i18n).catch((error) => errorLogs(error, 'k360 metrics')),
      this.businessPartnerOutletService.getOutletCustomerData([...outletKeys]),
    ]);

    // const {checklistsMap} = (checklists && checklists?.data || []).reduce((acc, checklist) => {
    //   let checkListsByOutletId = acc.checklistsMap.get(checklist.outletId);
    //   if (!checkListsByOutletId) {
    //     checkListsByOutletId = [checklist];
    //   }
    //   acc.checklistsMap.set(checklist.outletId, checkListsByOutletId);
    //   checkListsByOutletId?.push()
    //   return acc;
    // }, {checklistsMap: new Map<string, any[]>()})

    return {
      checklists: (checklists && checklists?.data) || [],
      k360Performance,
      outlets,
    };
  }

  async getVisitData(saleRepId: string, i18n: I18nContext, startDate: Date, endDate: Date, offset: number, limit: number) {
    const saleRepOutlets = (await this.businessPartnerRelationService.findOutletsAndDepotsByContact(saleRepId, null)) || [];
    const depotKey = saleRepOutlets[0]?.depotKey;
    const saleRepKey = saleRepOutlets[0]?.contactKey;
    return this.outletsService.getPerformanceSaleVolumeDetails(saleRepKey, startDate, endDate, offset, limit);
  }

  async getRecentOrders(saleRepId: string, startDate: Date, endDate: Date, offset: number, limit: number) {
    const saleRepOutlets = (await this.businessPartnerRelationService.findOutletsAndDepotsByContact(saleRepId, null)) || [];
    const depotKey = saleRepOutlets[0]?.depotKey;
    const saleRepKey = saleRepOutlets[0]?.contactKey;
    return this.ordersService.getOrdersByConditions(saleRepKey, startDate, endDate, offset, limit);
  }
}
