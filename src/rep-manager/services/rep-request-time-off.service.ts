import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { RepRequestTimeOff } from '../entities/rep-request-time-off.entity';

@Injectable()
export class RepRequestTimeOffService extends BaseSQLService<RepRequestTimeOff> {
  constructor(
    @InjectRepository(RepRequestTimeOff)
    private readonly _targetSettingsRepository: Repository<RepRequestTimeOff>,
  ) {
    super();
    this._repository = this._targetSettingsRepository;
  }
}
