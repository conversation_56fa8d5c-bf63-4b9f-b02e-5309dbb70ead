import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { RepCoachingOrders } from '../entities/rep-coaching-order.entity';

@Injectable()
export class RepCoachingOrderService extends BaseSQLService<RepCoachingOrders> {
  constructor(
    @InjectRepository(RepCoachingOrders)
    private readonly _targetSettingsRepository: Repository<RepCoachingOrders>,
  ) {
    super();
    this._repository = this._targetSettingsRepository;
  }
}
