import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, In, Raw, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { RepCoachingSessions } from '../entities/rep-coaching-session.entity';
import { CreateCoachingSessionDto, CreateCoachingSessionItemDto } from '../dto/create-coaching-session.dto';
import { CalendarSearchDto } from '../dto/calendar-search.dto';
import { RescheduleCoachingSessionDto } from '../dto/reschedule-coaching-session.dto';
import { I18nContext } from 'nestjs-i18n';
import * as moment from 'moment-timezone';
import { CalendarDayGroup, CalendarSessionItem } from '../dto/calendar-response.dto';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { UsersService } from '../../users/services/users.service';
import { RoutePlanService } from '../../sale-rep/services/route-plan.service';
import { BusinessPartnerOutletService } from 'src/master-data/services/business-partner-outlet.service';
import { PreviousCoachingSessionDto } from '../dto/previous-coaching-session.dto';
import { OutletJourneyPlanningService } from '../../journey-plannings/services/outlet-journey-planning.service';
import { RepCoachingSessionChecklistService } from '../services/rep-coaching-session-checklist.service';
import { SessionVisitDetailResponseDto } from '../dto/session-visit-detail-response.dto';
import { PreviousDetailsResponseDto } from '../dto/previous-details-response.dto';
import { BusinessPartner } from '../../master-data/entities/business-partner/business-partner.entity';
import { OutletsService } from 'src/outlets/services/outlets.service';
import { SessionSalesRepStatus, SessionStatus } from '../enums/rep-manager.enum';
import { UpdateSessionStatusDto } from '../dto/update-session-status.dto';
import { CoachingPerformanceQueryDto } from '../dto/coaching-performance.dto';
import { PaginationRequestParams } from '../../shared/dtos/pagination-request-params.dto';
import { PaginatedResponse } from '../../shared/dtos/paginated-response.dto';
import { getMonthDateRange } from '../../shared/helpers';
import { RepCoachingSessionHistories } from '../entities/rep-coaching-session-histories.entity';
import { SessionHistoryStatus } from '../enums/rep-manager.enum';

@Injectable()
export class RepCoachingSessionsService extends BaseSQLService<RepCoachingSessions> {
  constructor(
    @InjectRepository(RepCoachingSessions)
    private readonly _repCoachingSessionsRepository: Repository<RepCoachingSessions>,
    private readonly _businessPartnersContactService: BusinessPartnersContactService,
    private readonly _businessPartnersOutletService: BusinessPartnerOutletService,
    private readonly userService: UsersService,
    private readonly routePlanService: RoutePlanService,
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly checklistService: RepCoachingSessionChecklistService,
    private readonly outletService: OutletsService,
  ) {
    super();
    this._repository = this._repCoachingSessionsRepository;
  }

  async createSessions(createDto: CreateCoachingSessionDto, currentUser: any, i18n: I18nContext) {
    const distributorIds = currentUser?.businessPartnerRelations?.distributorIds || [];
    const depotIds = currentUser?.businessPartnerRelations?.depotIds || [];

    if (!distributorIds.length || !depotIds.length) {
      throw new BadRequestException(i18n.t('coaching.session.invalid_distributor_depot'));
    }

    const now = moment().startOf('day');

    // Validate all days first before any database calls
    this.validateAllDays(createDto.sessions, now, i18n);

    const salesRepIds = this.extractSalesRepIds(createDto);
    const existingSessionMap = await this.getExistingSessionMap(salesRepIds, createDto.sessions);

    // Validate remaining data
    for (const sessionData of createDto.sessions) {
      this.validateSessionData(sessionData, existingSessionMap, i18n);
    }

    // Create all entities after validation passes
    const sessions = this.createSessionEntities(createDto.sessions, distributorIds[0], depotIds[0]);

    return this._repCoachingSessionsRepository.save(sessions);
  }

  private validateAllDays(sessions: CreateCoachingSessionItemDto[], today: moment.Moment, i18n: I18nContext): void {
    for (const sessionData of sessions) {
      const sessionDate = moment(sessionData.day).startOf('day');
      if (sessionDate.isBefore(today)) {
        throw new BadRequestException(
          i18n.t('coaching.session.day_must_be_future', {
            args: {
              salesRepId: sessionData.salesRepId,
              day: sessionDate.format(),
            },
          }),
        );
      }
    }
  }

  private extractSalesRepIds(createDto: CreateCoachingSessionDto): string[] {
    return createDto.sessions.map((session) => session.salesRepId);
  }

  private async getExistingSessionMap(salesRepIds: string[], sessions: CreateCoachingSessionItemDto[]): Promise<Map<string, RepCoachingSessions>> {
    // Get unique dates from sessions
    const uniqueDates = [...new Set(sessions.map((s) => moment(s.day).startOf('day').toISOString()))];

    const existingSessions = await this._repCoachingSessionsRepository.find({
      select: ['id', 'salesRepId', 'displayDay'],
      where: {
        salesRepId: In(salesRepIds),
        displayDay: Raw((alias) => {
          const dateConditions = uniqueDates.map((date) => `DATE(${alias}) = DATE('${date}')`).join(' OR ');
          return `(${dateConditions})`;
        }),
      },
    });

    return new Map(existingSessions.map((session) => [this.getSessionKey(session.salesRepId, session.displayDay), session]));
  }

  private getSessionKey(salesRepId: string, day: Date): string {
    const sessionDate = moment(day).startOf('day');
    return `${salesRepId}_${sessionDate.toISOString()}`;
  }

  private validateSessionData(sessionData: CreateCoachingSessionItemDto, existingSessionMap: Map<string, RepCoachingSessions>, i18n: I18nContext): void {
    if (!sessionData.salesRepId) {
      throw new BadRequestException(
        i18n.t('coaching.session.sales_rep_not_found', {
          args: { id: sessionData.salesRepId },
        }),
      );
    }

    const sessionKey = this.getSessionKey(sessionData.salesRepId, sessionData.day);
    if (existingSessionMap.has(sessionKey)) {
      throw new BadRequestException(
        i18n.t('coaching.session.session_already_exists', {
          args: {
            id: sessionData.salesRepId,
            day: moment(sessionData.day).startOf('day').format(),
          },
        }),
      );
    }
  }

  private createSessionEntities(sessions: CreateCoachingSessionItemDto[], distributorId: string, depotId: string): RepCoachingSessions[] {
    return sessions.map((sessionData) => {
      const day = moment(sessionData.day).startOf('day');
      return this._repCoachingSessionsRepository.create({
        salesRepId: sessionData.salesRepId,
        day,
        displayDay: day,
        distributorId,
        depotId,
      });
    });
  }

  async searchCalendar(dto: CalendarSearchDto, i18n: I18nContext): Promise<CalendarDayGroup[]> {
    const { start_date, end_date, salesRepId } = dto;
    // Validate salesRepId if provided
    if (salesRepId) {
      const exists = await this._businessPartnersContactService.findOne({ where: { id: salesRepId } });
      if (!exists) {
        throw new BadRequestException(i18n.t('coaching.session.sales_rep_not_found'));
      }
    }
    // Query sessions in date range (inclusive)
    const qb = this._repCoachingSessionsRepository
      .createQueryBuilder('session')
      .select(['session.id', 'session.salesRepId', 'session.displayDay', 'salesRep.businessPartnerContactName1', 'salesRep.businessPartnerContactName2'])
      .innerJoin('session.salesRep', 'salesRep')
      .where(`DATE(session.day) >= DATE(:start)`, { start: moment(start_date).utc().startOf('day') })
      .andWhere(`DATE(session.day) <= DATE(:end)`, { end: moment(end_date).utc().endOf('day') });
    if (salesRepId) {
      qb.andWhere('session.salesRepId = :salesRepId', { salesRepId });
    }
    const sessions = await qb.getMany();
    // Group by day
    const grouped: Record<string, CalendarSessionItem[]> = {};
    for (const s of sessions) {
      const date = moment(s.displayDay).toISOString(true).slice(0, 10);
      if (!grouped[date]) grouped[date] = [];
      grouped[date].push({
        sessionId: s.id,
        salesRepId: s.salesRepId,
        salesRepName:
          s.salesRep?.businessPartnerContactName1 && s.salesRep?.businessPartnerContactName1 !== ''
            ? s.salesRep.businessPartnerContactName1
            : s.salesRep?.businessPartnerContactName2 || '',
      });
    }

    return Object.entries(grouped).map(([date, items]) => ({ date, items }));
  }

  async getCoachingSessionDetails(coachingSessionId: string, i18n: I18nContext) {
    const coachingSession = await this.findCoachingSessionWithRelations(coachingSessionId, i18n);
    const previousSessionWithNote = await this.getPreviousSessionWithNote(coachingSession);

    const saleRepExternalKey = coachingSession.salesRep.businessPartnerContactKey;
    if (!saleRepExternalKey) {
      return {
        ...coachingSession,
        previousSession: previousSessionWithNote,
      };
    }

    const saleRepUser = await this.userService.findOne({
      saleRepId: saleRepExternalKey,
    });

    const journeyPlans = await this.routePlanService.getRoutePlanData({ salesRep: saleRepUser });
    const enrichedOutlets = await this.enrichOutletsWithHistoryData(coachingSession, journeyPlans);

    return {
      ...coachingSession,
      previousSession: previousSessionWithNote,
      journeyPlans: {
        ...(journeyPlans || {}),
        todayOutlets: enrichedOutlets,
      },
    };
  }

  private async findCoachingSessionWithRelations(coachingSessionId: string, i18n: I18nContext): Promise<RepCoachingSessions> {
    const coachingSession = await this._repository.findOne({
      where: {
        id: coachingSessionId,
        isDeleted: false,
      },
      relations: ['salesRep', 'histories'],
    });

    if (!coachingSession) {
      throw new BadRequestException(
        i18n.t('coaching.session.not_found', {
          args: { id: coachingSessionId },
        }),
      );
    }

    return coachingSession;
  }

  private async enrichOutletsWithHistoryData(coachingSession: RepCoachingSessions, journeyPlans: any): Promise<any[]> {
    const jpOutlets = journeyPlans?.todayOutlets || [];
    const sessionHistoryMap = this.createSessionHistoryMap(coachingSession);
    const missingOutletKeys = this.findMissingOutletKeys(coachingSession, jpOutlets);

    const allOutletUCCs = this.getAllOutletUCCs(jpOutlets, missingOutletKeys);
    const outletClassificationMap = await this.getOutletClassificationMap(allOutletUCCs);

    const additionalOutlets = this.createAdditionalOutlets(missingOutletKeys, sessionHistoryMap, outletClassificationMap);
    const allOutlets = [...jpOutlets, ...additionalOutlets];

    return this.mapOutletsWithStatusAndData(allOutlets, sessionHistoryMap, outletClassificationMap);
  }

  private createSessionHistoryMap(coachingSession: RepCoachingSessions): Map<string, string> {
    const sessionHistoryMap = new Map();

    if (coachingSession.histories && coachingSession.histories.length > 0) {
      coachingSession.histories.forEach((history) => {
        if (history.outlet && history.outlet.businessPartnerKey) {
          sessionHistoryMap.set(history.outlet.businessPartnerKey, history.status);
        }
      });
    }

    return sessionHistoryMap;
  }

  private findMissingOutletKeys(coachingSession: RepCoachingSessions, jpOutlets: any[]): string[] {
    const historyOutletKeys = new Set<string>();

    if (coachingSession.histories && coachingSession.histories.length > 0) {
      coachingSession.histories.forEach((history) => {
        if (history.outlet && history.outlet.businessPartnerKey) {
          historyOutletKeys.add(history.outlet.businessPartnerKey);
        }
      });
    }

    return Array.from(historyOutletKeys).filter((key: string) => {
      return !jpOutlets.some((outlet) => outlet?.businessPartnerKey === key);
    });
  }

  private getAllOutletUCCs(jpOutlets: any[], missingOutletKeys: string[]): string[] {
    const outletUCCs = [...new Set(jpOutlets.map((outlet) => outlet?.ucc))];
    return [...outletUCCs, ...missingOutletKeys];
  }

  private async getOutletClassificationMap(allOutletUCCs: string[]): Promise<Map<string, any>> {
    const outletClassificationData = await this._businessPartnersOutletService.getOutletCustomerData(allOutletUCCs);
    return new Map((outletClassificationData || []).map((data) => [data.outletUCC, data]));
  }

  private createAdditionalOutlets(missingOutletKeys: string[], sessionHistoryMap: Map<string, string>, outletClassificationMap: Map<string, any>): any[] {
    if (missingOutletKeys.length === 0) {
      return [];
    }

    return missingOutletKeys.map((key) => {
      const outletData = outletClassificationMap.get(key);
      return {
        businessPartnerKey: key,
        ucc: key,
        status: sessionHistoryMap.get(key) || 'pending',
        ...outletData,
      };
    });
  }

  private mapOutletsWithStatusAndData(allOutlets: any[], sessionHistoryMap: Map<string, string>, outletClassificationMap: Map<string, any>): any[] {
    return allOutlets.map((outlet) => ({
      ...outlet,
      customerData: outletClassificationMap.get(outlet?.ucc),
      status: sessionHistoryMap.get(outlet?.businessPartnerKey) || 'pending',
    }));
  }

  private async getPreviousSessionWithNote(coachingSession: RepCoachingSessions): Promise<PreviousCoachingSessionDto | null> {
    const previousSession = await this.findMostRecentPreviousSession(coachingSession);

    if (!this.hasValidCoachingNote(previousSession)) {
      return null;
    }

    return this.mapToPreviousSessionDto(previousSession);
  }

  private async findMostRecentPreviousSession(coachingSession: RepCoachingSessions): Promise<RepCoachingSessions | null> {
    return await this._repository.findOne({
      where: {
        salesRepId: coachingSession.salesRepId,
        displayDay: Raw((alias) => `${alias} < '${coachingSession.displayDay.toISOString()}'`),
        isDeleted: false,
      },
      order: {
        displayDay: 'DESC',
      },
    });
  }

  private hasValidCoachingNote(session: RepCoachingSessions | null): boolean {
    return session != null && session.coachingNote?.trim() !== '' && session.coachingNote !== null && session.coachingNote !== undefined;
  }

  private mapToPreviousSessionDto(session: RepCoachingSessions): PreviousCoachingSessionDto {
    return {
      sessionId: session.id,
      salesRepId: session.salesRepId,
      day: session.displayDay,
      coachingNote: session.coachingNote,
    };
  }

  async getSessionVisitDetail(sessionId: string, outletKey: string, i18n: I18nContext): Promise<SessionVisitDetailResponseDto> {
    const checklistData = await this.checklistService.getChecklistBySessionAndOutletKey(sessionId, outletKey, i18n);

    // Mock data for metrics and outlet - in real implementation, these would come from actual services
    const metrics = {
      yearToDateVolume: 200, // HL
      avgVolumePerOrder: 123, // HL
      avgNPSScore: 3,
    };

    return {
      checklist: checklistData,
      metrics,
    };
  }

  async getLatestCoachingSessionBySaleReps(saleRepIds: string[], startTime: Date, endTime: Date) {
    const sessions = await this._repository.find({
      where: {
        salesRep: {
          id: In(saleRepIds),
        },
        displayDay: Between(startTime, endTime),
        isDeleted: false,
      },
      order: {
        displayDay: 'DESC',
      },
    });
    const latestSessionMap = new Map<string, RepCoachingSessions>();
    for (const s of sessions) {
      if (!latestSessionMap.has(s.salesRepId)) {
        latestSessionMap.set(s.salesRepId, s);
      }
    }
    return latestSessionMap;
  }

  async getPreviousDetails(sessionId: string, i18n: I18nContext): Promise<PreviousDetailsResponseDto> {
    const coachingSession = await this.findCoachingSessionWithHistories(sessionId, i18n);

    const salesRep = this.buildSalesRepInfo(coachingSession);
    const visitList = await this.buildVisitList(coachingSession);
    const checklist = await this.buildChecklist(sessionId, coachingSession, i18n);

    return {
      sessionDate: coachingSession.displayDay.toISOString(),
      salesRep,
      coachingNote: coachingSession.coachingNote || '',
      visitList,
      checklist,
    };
  }

  private async findCoachingSessionWithHistories(sessionId: string, i18n: I18nContext): Promise<RepCoachingSessions> {
    const coachingSession = await this._repository.findOne({
      where: {
        id: sessionId,
        isDeleted: false,
      },
      relations: ['salesRep', 'histories', 'histories.outlet'],
    });

    if (!coachingSession) {
      throw new BadRequestException(
        i18n.t('coaching.session.not_found', {
          args: { id: sessionId },
        }),
      );
    }

    return coachingSession;
  }

  private buildSalesRepInfo(coachingSession: RepCoachingSessions) {
    return {
      name: coachingSession.salesRep?.businessPartnerContactName1 || coachingSession.salesRep?.businessPartnerContactName2 || '',
      salesRepId: coachingSession.salesRep?.businessPartnerContactKey,
      coachingScore: 0, // This would need to be calculated based on business logic
      status: SessionSalesRepStatus.IDLE,
    };
  }

  private async buildVisitList(coachingSession: RepCoachingSessions): Promise<any[]> {
    const saleRepUser = await this.userService.findOne({
      saleRepId: coachingSession.salesRep.businessPartnerContactKey,
    });

    const sessionHistoryMap = this.createSessionHistoryMap(coachingSession);
    const sessionOutletKeys = Array.from(sessionHistoryMap.keys());

    // Early return if no outlet keys found
    if (!sessionOutletKeys || sessionOutletKeys.length === 0) {
      return [];
    }

    const mongoOutlets = await this.outletService.findByUCCs(sessionOutletKeys);
    const mongoOutletIds = mongoOutlets.map((outlet) => outlet._id.toString());

    const journeyPlans = await this.outletJourneyPlanningService.getOutletJourneyPlanningBySalesRepAndOutletAndDay(saleRepUser._id, mongoOutletIds, coachingSession.displayDay);

    // Early return if no journey plans found
    if (!journeyPlans || journeyPlans.length === 0) {
      return [];
    }

    const transformedOutlets = await this.routePlanService.transformTodayOutlets({
      plans: journeyPlans,
      salesRep: saleRepUser,
    });

    const outletClassificationMap = await this.getOutletClassificationMap(sessionOutletKeys);

    return this.mapOutletsWithStatusAndData(transformedOutlets, sessionHistoryMap, outletClassificationMap);
  }

  private async buildChecklist(sessionId: string, coachingSession: RepCoachingSessions, i18n: I18nContext): Promise<any[]> {
    const allChecklistItems = await this.checklistService.getChecklistBySessionId(sessionId, i18n);
    const outletMap = this.createOutletMapFromHistories(coachingSession);

    return this.groupChecklistItemsByOutlet(allChecklistItems, outletMap);
  }

  private createOutletMapFromHistories(coachingSession: RepCoachingSessions): Map<string, BusinessPartner> {
    const outletMap = new Map<string, BusinessPartner>();

    coachingSession.histories.forEach((history) => {
      if (history.outlet) {
        outletMap.set(history.outlet.id, history.outlet);
      }
    });

    return outletMap;
  }

  private groupChecklistItemsByOutlet(allChecklistItems: any[], outletMap: Map<string, BusinessPartner>): any[] {
    const checklistMap = new Map<string, any[]>();

    for (const item of allChecklistItems) {
      const outlet = outletMap.get(item.outletId) as BusinessPartner;
      const outletName = outlet?.businessPartnerName1 || outlet?.businessPartnerName2 || '';

      if (!checklistMap.has(outletName)) {
        checklistMap.set(outletName, []);
      }

      checklistMap.get(outletName)?.push({
        id: item.id,
        label: item.label,
        checked: item.checked,
        isCoaching: item.isCoaching,
        sessionId: item.sessionId,
      });
    }

    return Array.from(checklistMap.entries()).map(([outletName, tasks]) => ({
      outletName,
      tasks,
    }));
  }

  async updateSessionStatus(sessionId: string, updateDto: UpdateSessionStatusDto, i18n: I18nContext): Promise<void> {
    // Find the session with histories
    const session = await this._repository.findOne({
      where: {
        id: sessionId,
        isDeleted: false,
      },
      relations: ['histories'],
    });

    if (!session) {
      throw new BadRequestException(
        i18n.t('coaching.session.not_found', {
          args: { id: sessionId },
        }),
      );
    }

    // Validate that no ongoing histories exist
    this.validateNoOngoingHistories(session, i18n);

    // Validate status transition
    this.validateStatusTransition(session.sessionStatus, updateDto.status, i18n);

    // Update the session status
    session.sessionStatus = updateDto.status;

    // Update coaching note if provided
    if (updateDto.note && (updateDto.status === SessionStatus.COMPLETED || updateDto.status === SessionStatus.CANCELED)) {
      session.coachingNote = updateDto.note;
    }

    await this._repository.save(session);
  }

  public validateNoOngoingHistories(session: RepCoachingSessions, i18n: I18nContext): void {
    const ongoingHistories = session.histories?.filter((history) => history.status === SessionHistoryStatus.ONGOING && !history.isDeleted) || [];

    if (ongoingHistories.length > 0) {
      throw new BadRequestException(
        i18n.t('coaching.session.has_ongoing_histories', {
          args: { sessionId: session.id },
        }),
      );
    }
  }

  async countCoachingSessionsInRange(saleRepIds: string[], startDate: Date, endDate: Date) {
    const query = `SELECT 
        COUNT(*) as "total",
        rcs."sessionStatus"
      FROM 
        public."rep_coaching_sessions" rcs
      WHERE
        rcs."isDeleted" = false AND rcs."displayDay" BETWEEN $1 AND $2
        and "salesRepId" IN (${saleRepIds.map((id) => `'${id}'`).join(',')})
      GROUP BY rcs."sessionStatus"
      `
      .replace(/\n/g, ' ')
      .trim();
    return this._repository.query(query, [startDate, endDate]);
  }

  private validateStatusTransition(currentStatus: SessionStatus, newStatus: SessionStatus, i18n: I18nContext): void {
    const statusTransitionRules = {
      [SessionStatus.IN_PROGRESS]: [SessionStatus.PLANNED],
      [SessionStatus.PLANNED]: [], // Cannot transition to PLANNED
      [SessionStatus.CANCELED]: [SessionStatus.PLANNED, SessionStatus.IN_PROGRESS],
      [SessionStatus.COMPLETED]: [SessionStatus.IN_PROGRESS],
    };

    const allowedTransitions = statusTransitionRules[newStatus] || [];

    if (!allowedTransitions.includes(currentStatus)) {
      throw new BadRequestException(
        i18n.t('coaching.session.invalid_status_transition', {
          args: {
            currentStatus,
            newStatus,
            allowedTransitions: allowedTransitions.join(', '),
          },
        }),
      );
    }
  }

  async listCoachingSessions(queryDto: CoachingPerformanceQueryDto, paginationParams: PaginationRequestParams = {}): Promise<PaginatedResponse<any>> {
    const filters = this.buildCoachingSessionFilters(queryDto);

    const result = await this.findPaginated(filters, paginationParams, ['salesRep'], 'displayDay');
    const transformedData = this.transformCoachingSessionsData(result.data);

    return {
      data: transformedData,
      count: result.count,
    };
  }

  private buildCoachingSessionFilters(queryDto: CoachingPerformanceQueryDto): any {
    const { month, year, salesRepId } = queryDto;
    const filters: any = { isDeleted: false };

    if (month && year) {
      const { startDate, endDate } = getMonthDateRange(month, year);
      filters.displayDay = Between(startDate, endDate);
    }

    if (salesRepId) {
      filters.salesRepId = salesRepId;
    }

    return filters;
  }

  private transformCoachingSessionsData(sessions: RepCoachingSessions[]): any[] {
    return sessions.map((session: RepCoachingSessions) => ({
      id: session.id,
      agent: {
        name: session.salesRep?.businessPartnerContactName1 || session.salesRep?.businessPartnerContactName2 || '',
        id: session.salesRepId,
        key: session.salesRep?.businessPartnerContactKey || '',
      },
      date: moment(session.displayDay).format('DD/MM/YYYY'),
      status: session.sessionStatus,
      coachingScore: 0, // Fake score as requested
      comment: session.coachingNote || '',
    }));
  }

  async rescheduleSession(sessionId: string, rescheduleDto: RescheduleCoachingSessionDto, i18n: I18nContext): Promise<void> {
    // Find the session with histories
    const session = await this._repository.findOne({
      where: {
        id: sessionId,
        isDeleted: false,
      },
      relations: ['histories'],
    });

    if (!session) {
      throw new BadRequestException(
        i18n.t('coaching.session.not_found', {
          args: { id: sessionId },
        }),
      );
    }

    // Validate session status - allow IN_PROGRESS and PLANNED sessions
    if (session.sessionStatus !== SessionStatus.IN_PROGRESS && session.sessionStatus !== SessionStatus.PLANNED) {
      throw new BadRequestException(
        i18n.t('coaching.session.invalid_status_for_reschedule', {
          args: {
            sessionId: sessionId,
            currentStatus: session.sessionStatus,
            allowedStatus: `${SessionStatus.IN_PROGRESS}, ${SessionStatus.PLANNED}`,
          },
        }),
      );
    }

    // Check if there are any histories - don't allow reschedule if histories exist
    const hasHistories = session.histories && session.histories.length > 0;
    if (hasHistories) {
      throw new BadRequestException(
        i18n.t('coaching.session.cannot_reschedule_with_histories', {
          args: { sessionId: sessionId },
        }),
      );
    }

    // Validate reschedule date - must be greater than current date
    const rescheduleDate = moment(rescheduleDto.rescheduleDate).startOf('day');
    const currentDate = moment().startOf('day');

    if (rescheduleDate.isSameOrBefore(currentDate)) {
      throw new BadRequestException(
        i18n.t('coaching.session.reschedule_date_must_be_future', {
          args: {
            rescheduleDate: rescheduleDate.format('YYYY-MM-DD'),
            currentDate: currentDate.format('YYYY-MM-DD'),
          },
        }),
      );
    }

    // Update session with reschedule information
    session.rescheduled = true;
    session.rescheduledDay = rescheduleDate.toDate();
    session.rescheduleReason = rescheduleDto.rescheduleReason || null;

    // The displayDay will be automatically updated by the @BeforeUpdate decorator
    await this._repository.save(session);
  }
}
