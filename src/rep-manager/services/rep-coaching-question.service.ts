import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { I18nContext } from 'nestjs-i18n';
import { RepCoachingQuestion } from '../entities/rep-coaching-question.entity';
import { RepCoachingAnswer } from '../entities/rep-coaching-answer.entity';
import { CreateQuestionsDto } from '../dto/create-question.dto';
import { UpdateQuestionsDto } from '../dto/update-question.dto';
import { DeleteQuestionsDto } from '../dto/delete-questions.dto';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { PaginationRequestParams } from '../../shared/dtos/pagination-request-params.dto';
import { PaginatedResponse } from '../../shared/dtos/paginated-response.dto';

@Injectable()
export class RepCoachingQuestionService extends BaseSQLService<RepCoachingQuestion> {
  constructor(
    @InjectRepository(RepCoachingQuestion)
    private readonly repCoachingQuestionRepository: Repository<RepCoachingQuestion>,
    @InjectRepository(RepCoachingAnswer)
    private readonly repCoachingAnswerRepository: Repository<RepCoachingAnswer>,
  ) {
    super();
    this._repository = this.repCoachingQuestionRepository;
  }

  async createQuestions(createQuestionsDto: CreateQuestionsDto): Promise<RepCoachingQuestion[]> {
    const questions = createQuestionsDto.questions.map((questionDto) => {
      const question = new RepCoachingQuestion();
      question.title = questionDto.title;
      question.text = questionDto.text;
      question.type = questionDto.type;
      question.required = questionDto.required ?? false;
      question.sortOrder = questionDto.sortOrder;
      return question;
    });

    return await this.repCoachingQuestionRepository.save(questions);
  }

  async updateQuestions(updateQuestionsDto: UpdateQuestionsDto, i18n: I18nContext): Promise<RepCoachingQuestion[]> {
    const questionIds = updateQuestionsDto.questions.map((q) => q.id);

    // Batch check if questions exist
    const existingQuestions = await this.repCoachingQuestionRepository.find({
      where: { id: In(questionIds), isDeleted: false },
    });

    if (existingQuestions.length !== questionIds.length) {
      const existingIds = existingQuestions.map((q) => q.id);
      const missingIds = questionIds.filter((id) => !existingIds.includes(id));
      throw new BadRequestException(
        i18n.t('coachingQuestionAnswer.question.not_found', {
          args: { questionIds: missingIds.join(', ') },
        }),
      );
    }

    // Batch check if any questions have answers
    const questionsWithAnswers = await this.repCoachingAnswerRepository
      .createQueryBuilder('answer')
      .select('DISTINCT answer.questionId', 'questionId')
      .where('answer.questionId IN (:...questionIds)', { questionIds })
      .andWhere('answer.isDeleted = :isDeleted', { isDeleted: false })
      .getRawMany();

    const questionIdsWithAnswers = questionsWithAnswers.map((q) => q.questionId);

    // Batch update questions
    const questionsToUpdate = existingQuestions.map((existingQuestion) => {
      const updateDto = updateQuestionsDto.questions.find((q) => q.id === existingQuestion.id);
      const hasAnswers = questionIdsWithAnswers.includes(existingQuestion.id);

      if (updateDto?.title !== undefined) {
        existingQuestion.title = updateDto.title;
      }

      if (updateDto?.text !== undefined) {
        existingQuestion.text = updateDto.text;
      }

      // Only allow type update if question has no answers
      if (updateDto?.type !== undefined) {
        if (hasAnswers) {
          throw new BadRequestException(
            i18n.t('coachingQuestionAnswer.question.cannot_update_type_with_answers', {
              args: { questionId: existingQuestion.id },
            }),
          );
        }
        existingQuestion.type = updateDto.type;
      }

      if (updateDto?.required !== undefined) {
        existingQuestion.required = updateDto.required;
      }

      if (updateDto?.sortOrder !== undefined) {
        existingQuestion.sortOrder = updateDto.sortOrder;
      }

      return existingQuestion;
    });

    return await this.repCoachingQuestionRepository.save(questionsToUpdate);
  }

  async getAllQuestionsPaginated(
    paginationParams: PaginationRequestParams = {},
    filters: {
      type?: string;
      searchText?: string;
    } = {},
  ): Promise<PaginatedResponse<RepCoachingQuestion>> {
    // Build filter conditions
    const whereConditions: any = {
      isDeleted: false, // Always filter by isDeleted: false
    };

    // Add type filter if provided
    if (filters.type) {
      whereConditions.type = filters.type;
    }

    // Add text search filter if provided
    if (filters.searchText) {
      whereConditions.text = { $like: `%${filters.searchText}%` };
    }

    return await this.findPaginated(whereConditions, paginationParams, [], 'sortOrder');
  }

  async getQuestionById(id: string): Promise<RepCoachingQuestion> {
    return await this.repCoachingQuestionRepository.findOne({
      where: { id, isDeleted: false },
    });
  }

  async getAllQuestions(): Promise<RepCoachingQuestion[]> {
    return await this.repCoachingQuestionRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'ASC' },
    });
  }

  async getQuestionsCount(): Promise<number> {
    return await this.repCoachingQuestionRepository.count({
      where: { isDeleted: false },
    });
  }

  async deleteQuestions(deleteQuestionsDto: DeleteQuestionsDto): Promise<{ deletedCount: number }> {
    const { questionIds } = deleteQuestionsDto;

    // Batch soft delete questions without validation
    const questionsToDelete = await this.repCoachingQuestionRepository.find({
      where: { id: In(questionIds), isDeleted: false },
    });

    if (questionsToDelete.length > 0) {
      const questionsToUpdate = questionsToDelete.map((question) => {
        question.isDeleted = true;
        return question;
      });

      await this.repCoachingQuestionRepository.save(questionsToUpdate);
    }

    return {
      deletedCount: questionsToDelete.length,
    };
  }
}
