import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { I18nContext } from 'nestjs-i18n';
import { RepCoachingSessionHistories } from '../entities/rep-coaching-session-histories.entity';
import { RepCoachingSessions } from '../entities/rep-coaching-session.entity';
import { CoachingOutletDetailsResponseDto, OutletInfoDto, ProgressDto } from '../dto/coaching-outlet-details-response.dto';
import { RepCoachingSessionChecklistService } from './rep-coaching-session-checklist.service';
import { RepCoachingQuestionService } from './rep-coaching-question.service';
import { RepCoachingSessionHistoriesService } from './rep-coaching-session-histories.service';
import { BusinessPartnersService } from '../../master-data/services/business-partners.service';
import { RepCoachingSessionsService } from './rep-coaching-sessions.service';
import { SessionStatus, SessionHistoryStatus, CoachingStepType } from '../enums/rep-manager.enum';

@Injectable()
export class RepCoachingOutletService {
  constructor(
    @InjectRepository(RepCoachingSessionHistories)
    private readonly sessionHistoriesRepository: Repository<RepCoachingSessionHistories>,
    private readonly checklistService: RepCoachingSessionChecklistService,
    private readonly questionService: RepCoachingQuestionService,
    private readonly repCoachingSessionHistoriesService: RepCoachingSessionHistoriesService,
    private readonly businessPartnersService: BusinessPartnersService,
    private readonly repCoachingSessionsService: RepCoachingSessionsService,
  ) {}

  async getDetails(id: string, i18n: I18nContext): Promise<CoachingOutletDetailsResponseDto> {
    // Find the session history by ID
    const sessionHistory = await this.sessionHistoriesRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['outlet', 'session', 'answers'],
    });

    if (!sessionHistory) {
      throw new NotFoundException(
        i18n.t('coaching.session.visit_not_found', {
          args: { id },
        }),
      );
    }

    // Get outlet information
    const outlet: OutletInfoDto = {
      id: sessionHistory.outlet.id,
      name: sessionHistory.outlet.businessPartnerName1 || sessionHistory.outlet.businessPartnerName2 || '',
    };

    // Get checklist items for this session and outlet using the service
    const checklist = await this.checklistService.getChecklistBySessionAndOutlet(sessionHistory.session.id, sessionHistory.outlet.id, i18n);

    // Get total questions count from the question service
    const totalQuestions = await this.questionService.getQuestionsCount();

    // Get answered questions by checking questionId in sessionHistory.answers
    const answeredQuestionIds = new Set(sessionHistory.answers.filter((answer) => !answer.isDeleted).map((answer) => answer.questionId));
    const answeredQuestions = answeredQuestionIds.size;

    const completion = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

    const progress: ProgressDto = {
      totalQuestions,
      answeredQuestions,
      completion,
    };

    return {
      outlet,
      progress,
      sessionHistory,
      checklist,
    };
  }

  async startCoaching(outletKey: string, sessionId: string, i18n: I18nContext): Promise<RepCoachingSessionHistories> {
    const session = await this.validateAndGetSession(sessionId, i18n);
    const outlet = await this.validateAndGetOutlet(outletKey, i18n);

    await this.validateNoExistingHistory(sessionId, outlet.id, i18n);
    this.repCoachingSessionsService.validateNoOngoingHistories(session, i18n);

    return await this.createSessionHistory(session, outlet);
  }

  private async validateAndGetSession(sessionId: string, i18n: I18nContext): Promise<RepCoachingSessions> {
    const session = await this.repCoachingSessionsService.findOne({
      where: {
        id: sessionId,
        isDeleted: false,
      },
      relations: ['histories'],
    });

    if (!session) {
      throw new BadRequestException(
        i18n.t('coaching.session.not_found', {
          args: { id: sessionId },
        }),
      );
    }

    // Check if session status is IN_PROGRESS - only allow start if status is IN_PROGRESS
    if (session.sessionStatus !== SessionStatus.IN_PROGRESS) {
      throw new BadRequestException(
        i18n.t('coaching.session.status_not_allowed_for_start', {
          args: {
            sessionId,
            currentStatus: session.sessionStatus,
            requiredStatus: SessionStatus.IN_PROGRESS,
          },
        }),
      );
    }

    return session;
  }

  private async validateAndGetOutlet(outletKey: string, i18n: I18nContext): Promise<any> {
    const outlet = await this.businessPartnersService.findOne({
      where: {
        businessPartnerKey: outletKey,
        isDeleted: false,
      },
    });

    if (!outlet) {
      throw new BadRequestException(
        i18n.t('coaching.session.outlet_not_found', {
          args: { outletKey },
        }),
      );
    }

    return outlet;
  }

  private async validateNoExistingHistory(sessionId: string, outletId: string, i18n: I18nContext): Promise<void> {
    const existingHistory = await this.repCoachingSessionHistoriesService.findOne({
      where: {
        session: { id: sessionId },
        outlet: { id: outletId },
        isDeleted: false,
      },
    });

    if (existingHistory) {
      throw new BadRequestException(
        i18n.t('coaching.session.session_history_already_exists', {
          args: { sessionId, outletId },
        }),
      );
    }
  }

  private async createSessionHistory(session: RepCoachingSessions, outlet: any): Promise<RepCoachingSessionHistories> {
    const sessionHistory = new RepCoachingSessionHistories();
    sessionHistory.session = session;
    sessionHistory.outlet = outlet;
    sessionHistory.startTime = new Date();
    sessionHistory.status = SessionHistoryStatus.ONGOING;
    sessionHistory.step = CoachingStepType.STEP_QUESTION;

    return await this.repCoachingSessionHistoriesService.save(sessionHistory);
  }

  async endvisit(id: string, i18n: I18nContext): Promise<RepCoachingSessionHistories> {
    // Find the session history by ID
    const sessionHistory = await this.sessionHistoriesRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!sessionHistory) {
      throw new NotFoundException(
        i18n.t('coaching.session.visit_not_found', {
          args: { id },
        }),
      );
    }

    // Validate that the status is ONGOING
    if (sessionHistory.status !== SessionHistoryStatus.ONGOING) {
      throw new BadRequestException(
        i18n.t('coaching.session.status_not_allowed', {
          args: {
            id,
            status: sessionHistory.status,
          },
        }),
      );
    }

    // Set end time to current UTC time and change status to COMPLETED
    sessionHistory.endTime = new Date();
    sessionHistory.status = SessionHistoryStatus.COMPLETED;

    return await this.repCoachingSessionHistoriesService.save(sessionHistory);
  }
}
