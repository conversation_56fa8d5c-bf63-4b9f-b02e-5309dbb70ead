import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not, In } from 'typeorm';
import { I18nContext } from 'nestjs-i18n';
import { RepCoachingAnswer } from '../entities/rep-coaching-answer.entity';
import { RepCoachingQuestionService } from './rep-coaching-question.service';
import { RepCoachingSessionHistoriesService } from './rep-coaching-session-histories.service';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { SessionHistoryStatus, QuestionType } from '../enums/rep-manager.enum';

@Injectable()
export class RepCoachingAnswerService extends BaseSQLService<RepCoachingAnswer> {
  constructor(
    @InjectRepository(RepCoachingAnswer)
    private readonly repCoachingAnswerRepository: Repository<RepCoachingAnswer>,
    private readonly repCoachingQuestionService: RepCoachingQuestionService,
    private readonly repCoachingSessionHistoriesService: RepCoachingSessionHistoriesService,
  ) {
    super();
    this._repository = this.repCoachingAnswerRepository;
  }

  async createOrUpdateAnswer(questionId: string, historyId: string, value: any, i18n: I18nContext): Promise<RepCoachingAnswer> {
    await this.validateSessionHistory(historyId, i18n);
    const question = await this.validateQuestion(questionId, i18n);
    this.validateRequiredField(question, value, i18n);

    const existingAnswer = await this.findExistingAnswer(questionId, historyId);

    return existingAnswer ? await this.updateAnswer(existingAnswer, value) : await this.createAnswer(questionId, historyId, value);
  }

  private async validateSessionHistory(historyId: string, i18n: I18nContext): Promise<void> {
    // First check if session exists
    const sessionHistory = await this.repCoachingSessionHistoriesService.findOne({
      where: { id: historyId, isDeleted: false },
    });

    if (!sessionHistory) {
      throw new BadRequestException(i18n.t('coaching.session.visit_not_found', { args: { id: historyId } }));
    }

    // Then check if status allows operations
    if (sessionHistory.status === SessionHistoryStatus.COMPLETED) {
      throw new BadRequestException(
        i18n.t('coaching.session.status_not_allowed', {
          args: {
            id: historyId,
            status: sessionHistory.status,
          },
        }),
      );
    }
  }

  private async validateQuestion(questionId: string, i18n: I18nContext) {
    const question = await this.repCoachingQuestionService.getQuestionById(questionId);

    if (!question) {
      throw new BadRequestException(i18n.t('coachingQuestionAnswer.question.not_found', { args: { questionIds: questionId } }));
    }

    return question;
  }

  private validateRequiredField(question: any, value: any, i18n: I18nContext): void {
    // First check if field is required and has a value
    if (question.required && (value === null || value === undefined || value === '')) {
      throw new BadRequestException(i18n.t('coachingQuestionAnswer.answer.required_field_empty', { args: { questionId: question.id } }));
    }

    // If field has a value, validate the type
    if (value !== null && value !== undefined && value !== '') {
      this.validateFieldType(question, value, i18n);
    }
  }

  private validateFieldType(question: any, value: any, i18n: I18nContext): void {
    switch (question.type) {
      case QuestionType.NUMBER:
        if (isNaN(Number(value))) {
          throw new BadRequestException(i18n.t('coachingQuestionAnswer.answer.invalid_number_value', { args: { questionId: question.id } }));
        }
        break;

      case QuestionType.STRING:
        if (String(value).trim() === '') {
          throw new BadRequestException(i18n.t('coachingQuestionAnswer.answer.invalid_string_value', { args: { questionId: question.id } }));
        }
        break;

      case QuestionType.BOOLEAN:
        // Boolean can accept any truthy/falsy value, so no additional validation needed
        break;

      case QuestionType.OBJECT:
        if (typeof value === 'object' && Object.keys(value).length === 0) {
          throw new BadRequestException(i18n.t('coachingQuestionAnswer.answer.invalid_object_value', { args: { questionId: question.id } }));
        }
        break;

      case QuestionType.RATING:
        if (isNaN(Number(value)) || Number(value) < 1) {
          throw new BadRequestException(i18n.t('coachingQuestionAnswer.answer.invalid_rating_value', { args: { questionId: question.id } }));
        }
        break;

      default:
        // No additional type validation for unknown types
        break;
    }
  }

  private async findExistingAnswer(questionId: string, historyId: string): Promise<RepCoachingAnswer | null> {
    return await this.repCoachingAnswerRepository.findOne({
      where: {
        questionId,
        repCoachingSessionHistoriesId: historyId,
        isDeleted: false,
      },
    });
  }

  private async updateAnswer(existingAnswer: RepCoachingAnswer, value: any): Promise<RepCoachingAnswer> {
    existingAnswer.contentValue = this.formatValue(value);
    return await this.repCoachingAnswerRepository.save(existingAnswer);
  }

  private async createAnswer(questionId: string, historyId: string, value: any): Promise<RepCoachingAnswer> {
    const newAnswer = new RepCoachingAnswer();
    newAnswer.questionId = questionId;
    newAnswer.repCoachingSessionHistoriesId = historyId;
    newAnswer.contentValue = this.formatValue(value);
    return await this.repCoachingAnswerRepository.save(newAnswer);
  }

  private formatValue(value: any): string | null {
    return value !== null && value !== undefined ? String(value) : null;
  }
}
