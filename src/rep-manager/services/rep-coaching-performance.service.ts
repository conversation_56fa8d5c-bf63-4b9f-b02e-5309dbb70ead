import { Injectable } from '@nestjs/common';
import { Between } from 'typeorm';
import { RepCoachingSessions } from '../entities/rep-coaching-session.entity';
import { SessionStatus } from '../enums/rep-manager.enum';
import { CoachingPerformanceResponse } from '../dto/coaching-performance-response.dto';
import { RepCoachingSessionsService } from './rep-coaching-sessions.service';
import { RepCoachingSessionChecklistService } from './rep-coaching-session-checklist.service';
import { getMonthDateRange, getPreviousMonthDateRange } from '../../shared/helpers';

@Injectable()
export class RepCoachingPerformanceService {
  constructor(private readonly coachingSessionsService: RepCoachingSessionsService, private readonly coachingSessionChecklistService: RepCoachingSessionChecklistService) {}

  async getPerformancePerMonth(month: number, year: number, salesRepId?: string): Promise<CoachingPerformanceResponse> {
    const { startDate, endDate } = getMonthDateRange(month, year);
    const { startDate: previousStartDate, endDate: previousEndDate } = getPreviousMonthDateRange(month, year);

    // Build where conditions
    const whereConditions: any = {
      isDeleted: false,
      displayDay: Between(startDate, endDate),
    };

    const previousWhereConditions: any = {
      isDeleted: false,
      displayDay: Between(previousStartDate, previousEndDate),
    };

    if (salesRepId) {
      whereConditions.salesRepId = salesRepId;
      previousWhereConditions.salesRepId = salesRepId;
    }

    // Get coaching sessions data
    const [currentMonthSessions, previousMonthSessions] = await Promise.all([
      this.coachingSessionsService.find({
        where: whereConditions,
        select: ['sessionStatus'],
      }),
      this.coachingSessionsService.find({
        where: previousWhereConditions,
        select: ['sessionStatus'],
      }),
    ]);

    // Calculate coaching session stats
    const coachingSessionStats = this.calculateCoachingSessionStats(currentMonthSessions, previousMonthSessions);

    // Get tasks data
    const tasksStats = await this.calculateTasksStats(whereConditions);

    return {
      coachingSession: coachingSessionStats,
      tasks: tasksStats,
    };
  }

  private calculateCoachingSessionStats(currentMonthSessions: RepCoachingSessions[], previousMonthSessions: RepCoachingSessions[]) {
    const totalSessions = currentMonthSessions.length;
    const completed = currentMonthSessions.filter((s) => s.sessionStatus === SessionStatus.COMPLETED).length;
    const canceled = currentMonthSessions.filter((s) => s.sessionStatus === SessionStatus.CANCELED).length;
    const remaining = totalSessions - completed - canceled;

    // Calculate last month change percent
    const previousTotal = previousMonthSessions.length;
    let lastMonthChangePercent = 0;

    if (previousTotal > 0) {
      const change = totalSessions - previousTotal;
      lastMonthChangePercent = Math.round((change / previousTotal) * 100);
    } else if (totalSessions > 0) {
      lastMonthChangePercent = 100; // If no sessions last month but sessions this month
    }

    return {
      totalSessions,
      completed,
      canceled,
      remaining,
      lastMonthChangePercent,
    };
  }

  private async calculateTasksStats(whereConditions: any) {
    // Get sessions for the month to get their IDs
    const sessions = await this.coachingSessionsService.find({
      where: whereConditions,
      select: ['id'],
    });

    const sessionIds = sessions.map((s) => s.id);

    if (sessionIds.length === 0) {
      return {
        totalTasks: 0,
        completed: 0,
        pending: 0,
        completionRate: 0,
        pendingRate: 0,
      };
    }

    // Get checklist items for these sessions
    const checklistItems = await this.coachingSessionChecklistService.getChecklistItemsBySessionIds(sessionIds);

    const totalTasks = checklistItems.length;
    const completed = checklistItems.filter((item) => item.checked).length;
    const pending = totalTasks - completed;
    const completionRate = totalTasks > 0 ? Math.round((completed / totalTasks) * 100) : 0;
    const pendingRate = totalTasks > 0 ? 100 - completionRate : 0;

    return {
      totalTasks,
      completed,
      pending,
      completionRate,
      pendingRate,
    };
  }
}
