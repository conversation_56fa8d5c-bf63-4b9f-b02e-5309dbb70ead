import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { RepCoachingSessionHistories } from '../entities/rep-coaching-session-histories.entity';
import { UpdateCoachingSessionHistoryDto } from '../dto/update-coaching-session-history.dto';
import { I18nContext } from 'nestjs-i18n';
import { SessionHistoryStatus } from '../enums/rep-manager.enum';

@Injectable()
export class RepCoachingSessionHistoriesService extends BaseSQLService<RepCoachingSessionHistories> {
  constructor(
    @InjectRepository(RepCoachingSessionHistories)
    private readonly _repCoachingSessionHistoriesRepository: Repository<RepCoachingSessionHistories>,
  ) {
    super();
    this._repository = this._repCoachingSessionHistoriesRepository;
  }

  async updateById(id: string, updateDto: UpdateCoachingSessionHistoryDto, i18n: I18nContext): Promise<RepCoachingSessionHistories> {
    // Use the repository directly with the correct TypeORM syntax
    const existingHistory = await this._repCoachingSessionHistoriesRepository.findOne({
      where: { id },
    });

    if (!existingHistory) {
      throw new NotFoundException(i18n.t('coaching.session.visit_not_found', { args: { id } }));
    }

    // Then check if status allows operations
    if (existingHistory.status === SessionHistoryStatus.COMPLETED) {
      throw new BadRequestException(
        i18n.t('coaching.session.status_not_allowed', {
          args: {
            id: id,
            status: existingHistory.status,
          },
        }),
      );
    }

    // Update the entity with the new data
    const updatedHistory = await this._repository.save({
      ...existingHistory,
      ...updateDto,
    });

    return updatedHistory;
  }
}
