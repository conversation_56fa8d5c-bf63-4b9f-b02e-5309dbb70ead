import { Injectable, NotFoundException, Inject, forwardRef, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not, In, Between } from 'typeorm';
import { RepCoachingSessionChecklist } from '../entities/rep-coaching-session-checklist.entity';
import { CreateChecklistItemDto } from '../dto/coaching-session-checklist.dto';
import { RepCoachingSessionsService } from './rep-coaching-sessions.service';
import { BusinessPartnersService } from '../../master-data/services/business-partners.service';
import { I18nContext } from 'nestjs-i18n';
import { ChecklistItemResponseDto } from '../dto/checklist-item-response.dto';
import { SessionStatus } from '../enums/rep-manager.enum';
import { validateNotEmpty } from '../../utils/helpers/validation.helper';
import { CoachingPerformanceQueryDto } from '../dto/coaching-performance.dto';
import { PaginationRequestParams } from '../../shared/dtos/pagination-request-params.dto';
import { BaseSQLService } from '../../shared/services/basesql.service';
import * as moment from 'moment-timezone';
import { getMonthDateRange } from '../../shared/helpers';

@Injectable()
export class RepCoachingSessionChecklistService extends BaseSQLService<RepCoachingSessionChecklist> {
  constructor(
    @InjectRepository(RepCoachingSessionChecklist)
    private readonly checklistRepository: Repository<RepCoachingSessionChecklist>,
    @Inject(forwardRef(() => RepCoachingSessionsService))
    private readonly repCoachingSessionsService: RepCoachingSessionsService,
    @Inject(forwardRef(() => BusinessPartnersService))
    private readonly businessPartnersService: BusinessPartnersService,
  ) {
    super();
    this._repository = checklistRepository;
  }

  private async validateSessionAndOutlet(sessionId: string | undefined, outletId: string | undefined, i18n: I18nContext) {
    validateNotEmpty(sessionId, i18n.t('checklist.session_id_required'));
    validateNotEmpty(outletId, i18n.t('checklist.outlet_id_required'));

    const session = await this.repCoachingSessionsService.findOne({ where: { id: sessionId } });
    if (!session) throw new NotFoundException(i18n.t('checklist.session_not_found'));

    const outlet = await this.businessPartnersService.findById(outletId);
    if (!outlet) throw new NotFoundException(i18n.t('checklist.outlet_not_found'));

    return { session, outlet };
  }

  private async validateSessionAndOutletKey(sessionId: string | undefined, outletKey: string | undefined, i18n: I18nContext) {
    validateNotEmpty(sessionId, i18n.t('checklist.session_id_required'));
    validateNotEmpty(outletKey, i18n.t('checklist.outlet_key_required'));

    const session = await this.repCoachingSessionsService.findOne({ where: { id: sessionId } });
    if (!session) throw new NotFoundException(i18n.t('checklist.session_not_found'));

    const outlet = await this.businessPartnersService.findOne({ where: { businessPartnerKey: outletKey } });
    if (!outlet) throw new NotFoundException(i18n.t('checklist.outlet_not_found'));

    return { session, outlet };
  }

  async createChecklistItem(createDto: CreateChecklistItemDto, i18n: I18nContext): Promise<RepCoachingSessionChecklist> {
    const { session, outlet } = await this.validateSessionAndOutletKey(createDto.sessionId, createDto.outletKey, i18n);

    // Check for duplicate label in the same session and outlet
    const existing = await this.checklistRepository.findOne({
      where: { sessionId: createDto.sessionId, outletId: outlet.id, label: createDto.label },
    });
    if (existing) {
      throw new BadRequestException(i18n.t('checklist.duplicate_label'));
    }
    const checklistItem = this.checklistRepository.create({
      outletId: outlet.id,
      sessionId: session.id,
      label: createDto.label,
      checked: false,
      isCoaching: session.sessionStatus === SessionStatus.IN_PROGRESS,
    });
    return this.checklistRepository.save(checklistItem);
  }

  private async getCurrentSessionChecklist(sessionId: string, outletId: string): Promise<ChecklistItemResponseDto[]> {
    const items = await this.checklistRepository.find({
      where: { sessionId, outletId },
      order: { createdAt: 'DESC' },
    });
    return items.map((item) => ({
      ...item,
      isPrevSession: false,
    }));
  }

  private async getPreviousUncheckedItems(sessionId: string, outletId: string): Promise<ChecklistItemResponseDto[]> {
    const items = await this.checklistRepository.find({
      where: {
        outletId,
        checked: false,
        sessionId: Not(sessionId),
      },
      order: { createdAt: 'DESC' },
    });
    return items.map((item) => ({
      ...item,
      isPrevSession: true,
    }));
  }

  async getChecklistBySessionAndOutletKey(sessionId: string, outletKey: string, i18n: I18nContext): Promise<ChecklistItemResponseDto[]> {
    const { outlet } = await this.validateSessionAndOutletKey(sessionId, outletKey, i18n);

    // Get current session checklist items
    const currentItems = await this.getCurrentSessionChecklist(sessionId, outlet.id);

    // Get unchecked items from previous sessions
    const previousItems = await this.getPreviousUncheckedItems(sessionId, outlet.id);

    // Combine both lists
    return [...currentItems, ...previousItems];
  }

  async getChecklistBySessionAndOutlet(sessionId: string, outletId: string, i18n: I18nContext): Promise<ChecklistItemResponseDto[]> {
    await this.validateSessionAndOutlet(sessionId, outletId, i18n);

    // Get current session checklist items
    const currentItems = await this.getCurrentSessionChecklist(sessionId, outletId);

    // Get unchecked items from previous sessions
    const previousItems = await this.getPreviousUncheckedItems(sessionId, outletId);

    // Combine both lists
    return [...currentItems, ...previousItems];
  }

  async getChecklistBySessionId(sessionId: string, i18n: I18nContext): Promise<ChecklistItemResponseDto[]> {
    validateNotEmpty(sessionId, i18n.t('checklist.session_id_required'));

    const session = await this.repCoachingSessionsService.findOne({ where: { id: sessionId } });
    if (!session) throw new NotFoundException(i18n.t('checklist.session_not_found'));

    const items = await this.checklistRepository.find({
      where: { sessionId },
      order: { createdAt: 'DESC' },
    });

    return items.map((item) => ({
      ...item,
      isPrevSession: false,
    }));
  }

  async updateCheckedStatus(id: string, checked: boolean, i18n: I18nContext): Promise<RepCoachingSessionChecklist> {
    const checklistItem = await this.checklistRepository.findOne({ where: { id } });
    if (!checklistItem) {
      throw new NotFoundException(i18n.t('checklist.not_found'));
    }
    checklistItem.checked = checked;
    return this.checklistRepository.save(checklistItem);
  }

  async deleteChecklistItem(id: string, i18n: I18nContext): Promise<void> {
    const result = await this.checklistRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(i18n.t('checklist.not_found'));
    }
  }

  async getChecklistItemsBySessionIds(sessionIds: string[]): Promise<RepCoachingSessionChecklist[]> {
    if (sessionIds.length === 0) {
      return [];
    }

    return this.checklistRepository.find({
      where: {
        sessionId: In(sessionIds),
        isDeleted: false,
      },
      select: ['checked'],
    });
  }

  async listChecklistItems(queryDto: CoachingPerformanceQueryDto, paginationParams: PaginationRequestParams = {}, checked?: boolean): Promise<{ data: any[]; count: number }> {
    const filters = this.buildChecklistFilters(queryDto, checked);

    const result = await this.findPaginatedWithQueryBuilder((query) => this.buildChecklistQuery(query, filters), paginationParams, 'session.displayDay');

    const transformedData = this.transformChecklistData(result.data);

    return {
      data: transformedData,
      count: result.count,
    };
  }

  private buildChecklistFilters(queryDto: CoachingPerformanceQueryDto, checked?: boolean): any {
    const { month, year, salesRepId } = queryDto;
    const filters: any = { isDeleted: false };

    if (month && year) {
      const { startDate, endDate } = getMonthDateRange(month, year);
      filters.createdAt = { startDate, endDate };
    }

    if (salesRepId) {
      filters.session = { salesRepId };
    }

    if (checked !== undefined) {
      filters.checked = checked;
    }

    return filters;
  }

  private transformChecklistData(checklistItems: RepCoachingSessionChecklist[]): any[] {
    return checklistItems.map((item) => ({
      id: item.id,
      label: item.label,
      salesRep: {
        name: item.session?.salesRep?.businessPartnerContactName1 || item.session?.salesRep?.businessPartnerContactName2 || '',
        id: item.session?.salesRepId || '',
        key: item.session?.salesRep?.businessPartnerContactKey || '',
      },
      source: item.outlet?.businessPartnerName1 || item.outlet?.businessPartnerName2 || '',
      day: moment(item.session?.displayDay || item.createdAt).format('DD/MM/YYYY'),
      checked: item.checked,
      isCoaching: item.isCoaching,
    }));
  }

  private buildChecklistQuery(query: any, filters: any) {
    query = query
      .innerJoinAndSelect('entity.session', 'session')
      .innerJoinAndSelect('session.salesRep', 'salesRep')
      .innerJoinAndSelect('entity.outlet', 'outlet')
      .where('entity.isDeleted = :isDeleted', { isDeleted: false });

    if (filters.createdAt) {
      query = query.andWhere('session.displayDay BETWEEN :startDate AND :endDate', {
        startDate: filters.createdAt.startDate,
        endDate: filters.createdAt.endDate,
      });
    }

    if (filters.session?.salesRepId) {
      query = query.andWhere('session.salesRepId = :salesRepId', { salesRepId: filters.session.salesRepId });
    }

    if (filters.checked !== undefined) {
      query = query.andWhere('entity.checked = :checked', { checked: filters.checked });
    }

    return query;
  }
}
