import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, In, Repository } from 'typeorm';
import * as moment from 'moment-timezone';

import { BaseSQLService } from '../../shared/services/basesql.service';
import { RepPerformanceKpi } from '../entities/rep-performance-kpi.entity';
import { BusinessPartnerContactRole, BusinessPartnerRelationType } from '../../master-data/constants/business-partner.enum';
import { getRandomTTL, isEmptyObjectOrArray, printLog, roundNumber, roundToNearestInt } from 'src/utils';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { BusinessPartnersService } from '../../master-data/services/business-partners.service';
import { ChartReportingService } from '../../external/services/chart-reporting.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { I18nContext } from 'nestjs-i18n';
import { OutletJourneyPlanningService } from '../../journey-plannings/services/outlet-journey-planning.service';
import { ConstantCommons } from '../../utils/constants';
import { ExecutionDisciplineType } from '../dto/sale-rep-performance-search.dto';

@Injectable()
export class RepPerformanceKpiService extends BaseSQLService<RepPerformanceKpi> {
  private kpiMetrics: any;
  constructor(
    @InjectRepository(RepPerformanceKpi)
    private readonly _repPerformanceKpiRepository: Repository<RepPerformanceKpi>,
    private readonly _businessPartnersService: BusinessPartnersService,
    private readonly _businessPartnerContactService: BusinessPartnersContactService,
    private readonly chartReportingService: ChartReportingService,
    @Inject(forwardRef(() => OutletJourneyPlanningService))
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    super();
    this._repository = this._repPerformanceKpiRepository;
  }

  async getASMAgentPerformances(asmExternalKey: string, month: number, year: number) {
    const current = moment().tz(process.env.TZ);
    if (!year) {
      year = current.get('year');
    }

    if (!month) {
      month = current.get('month') + 1;
    }

    const query = `
      SELECT DISTINCT
          asm_contact."businessPartnerContactKey" AS "supervisorKey",
          COALESCE(asm_contact."businessPartnerContactName1", asm_contact."businessPartnerContactName2") AS "supervisor",
          sr_contact.id AS "salesRepId",
          sr_contact."businessPartnerContactKey" AS "agentKey",
          COALESCE(sr_contact."businessPartnerContactName1", sr_contact."businessPartnerContactName2") AS "agent",
          rpk.volume,
          rpk.eoe,
          rpk."activeSellingOutlet",
          rpk."strikeRate",
          rpk."mustHaveSku",
          rpk."month",
          rpk."year",
          rpk.priority,
          rpk."depotId",
          rpk."distributorId"
      FROM business_partner_contacts AS asm_contact
      JOIN business_partner_relations AS asm_dist_rel
          ON asm_dist_rel."businessPartner1" = asm_contact.id
          AND asm_dist_rel."businessPartnerRelationType" = '${BusinessPartnerRelationType.CONTACT_DISTRIBUTOR}'
          AND asm_dist_rel."isDeleted" = false
          AND asm_contact."businessPartnerContactPersonRole" = '${BusinessPartnerContactRole.AREA_SALES_REP_MANAGER}'
          AND asm_contact."isDeleted" = false
          AND asm_contact."businessPartnerContactKey" = '${asmExternalKey}'
      JOIN business_partner_relations AS dist_depot_rel
          ON dist_depot_rel."businessPartner2" = asm_dist_rel."businessPartner2" 
          AND dist_depot_rel."businessPartnerRelationType" = '${BusinessPartnerRelationType.DEPOT_DISTRIBUTOR}'
          AND dist_depot_rel."isDeleted" = false
      JOIN business_partner_relations AS depot_outlet_rel
          ON depot_outlet_rel."businessPartner2" = dist_depot_rel."businessPartner1" 
          AND depot_outlet_rel."businessPartnerRelationType" = '${BusinessPartnerRelationType.OUTLET_DEPOT}'
          AND depot_outlet_rel."isDeleted" = false
      JOIN business_partner_relations AS outlet_sr_rel
          ON outlet_sr_rel."businessPartner2" = depot_outlet_rel."businessPartner1" 
          AND outlet_sr_rel."businessPartnerRelationType" = '${BusinessPartnerRelationType.CONTACT_OUTLET}'
          AND outlet_sr_rel."isDeleted" = false
      JOIN business_partner_contacts AS sr_contact
          ON sr_contact.id = outlet_sr_rel."businessPartner1"
          AND sr_contact."businessPartnerContactPersonRole" = '${BusinessPartnerContactRole.SALE_REP}'
          AND sr_contact."isDeleted" = false
      JOIN rep_performance_kpis AS rpk
          ON rpk."salesRepId" = sr_contact.id
          AND rpk."isDeleted" = false
          ${(month && `AND rpk."month" = ${month}`) || ''}
          ${(year && `AND rpk."year" = ${year}`) || ''}
      ORDER BY "agent";
    `
      .replace(/\n/g, ' ')
      .trim();

    const agentPerformances = await this._repository.query(query);

    if (!agentPerformances || agentPerformances.length === 0) {
      return agentPerformances;
    }

    const numAgents = agentPerformances.length;

    const sums = agentPerformances.reduce(
      (acc, p) => {
        acc.volume += +p.volume || 0;
        acc.eoe += +p.eoe || 0;
        acc.activeSellingOutlet += +p.activeSellingOutlet || 0;
        acc.strikeRate += +p.strikeRate || 0;
        acc.mustHaveSku += +p.mustHaveSku || 0;
        return acc;
      },
      {
        volume: 0,
        eoe: 0,
        activeSellingOutlet: 0,
        strikeRate: 0,
        mustHaveSku: 0,
      },
    );

    const totalRow = {
      supervisorKey: agentPerformances[0].supervisorKey,
      supervisor: agentPerformances[0].supervisor,
      salesRepId: null,
      agentKey: 'Total',
      agent: 'Total',
      volume: roundNumber(sums.volume / numAgents),
      eoe: roundToNearestInt(sums.eoe / numAgents),
      activeSellingOutlet: roundToNearestInt(sums.activeSellingOutlet / numAgents),
      strikeRate: roundToNearestInt(sums.strikeRate / numAgents),
      mustHaveSku: roundToNearestInt(sums.mustHaveSku / numAgents),
      month,
      year,
      priority: null,
      depotId: null,
      distributorId: null,
      avgScore: 50,
    };

    return {
      total: totalRow,
      agents: agentPerformances.map((aP, id) => ({
        ...aP,
        priority: id + 1,
        avgScore: Math.random() * 100,
      })),
    };
  }

  async syncKpiMetrics(kpisData: any[], date: Date) {
    try {
      if (!kpisData || kpisData.length === 0) {
        return { success: false, message: 'No KPI data provided' };
      }

      const month = date.getMonth() + 1; // JavaScript months are 0-indexed
      const year = date.getFullYear();

      const depotIds = kpisData.map((kpiData) => kpiData.depotId);
      const contactIds = kpisData.map((kpiData) => kpiData.salesRepId);
      const depots = await this._businessPartnersService.find({
        where: {
          businessPartnerKey: In(depotIds),
          isDeleted: false,
        },
        select: ['id', 'businessPartnerKey'],
      });
      const contacts = await this._businessPartnerContactService.find({
        where: {
          businessPartnerContactKey: In(contactIds),
          isDeleted: false,
        },
        select: ['id', 'businessPartnerContactKey'],
      });

      // Process each KPI data entry
      const results = await Promise.all(
        kpisData.map(async (kpiData) => {
          if (!kpiData.salesRepId) {
            return { success: false, message: 'Missing salesRepId', data: kpiData };
          }

          const contactId = contacts.find((contact) => contact.businessPartnerContactKey === kpiData.salesRepId)?.id;
          if (!contactId) {
            return;
          }

          try {
            // Check if data for this month and year already exists
            const existingKpi = await this._repository.findOne({
              where: {
                salesRepId: contactId,
                month,
                year,
                isDeleted: false,
              },
            });

            const kpiValues = {
              month,
              year,
              ...kpiData,
              salesRepId: contactId,
              depotId: depots.find((depot) => depot.businessPartnerKey === kpiData.depotId)?.id,
            };

            if (existingKpi) {
              await this._repository.update(existingKpi.id, kpiValues);
              return {
                success: true,
                message: 'KPI updated successfully',
                data: { ...kpiData, id: existingKpi.id },
              };
            } else {
              const newKpi = this._repository.create({
                ...kpiValues,
              });

              const savedKpi = await this._repository.save(newKpi);
              return {
                success: true,
                message: 'KPI created successfully',
                data: { ...savedKpi },
              };
            }
          } catch (error) {
            printLog(error);
            return {
              success: false,
              message: `Error processing KPI for salesRepId ${kpiData.salesRepId}: ${error.message}`,
              data: kpiData,
            };
          }
        }),
      );

      // Count successes and failures
      const successCount = results?.filter((r) => r?.success)?.length;
      const failureCount = results?.filter((r) => !r?.success)?.length;

      return {
        success: failureCount === 0,
        message: `Processed ${results.length} KPI records: ${successCount} successful, ${failureCount} failed`,
        results,
      };
    } catch (e) {
      printLog(e);
    }
  }

  async getPerformanceForSaleReps(findOptions: FindManyOptions<RepPerformanceKpi>) {
    return this._repository.find(findOptions);
  }

  async getK360MetricsForSalesRep(depotId: string, saleRepIds: string[], startDate: Date, endDate: Date, i18n?: I18nContext) {
    try {
      const brandVolumePerformance = await this.getBrandVolumePerformance(depotId, saleRepIds, startDate, endDate);
      const activeSellingOutlet = await this.getActiveSellingOutlet(depotId, saleRepIds, startDate, endDate);
      const inVisitOrders = await this.getInVisitOrders(depotId, saleRepIds, startDate, endDate);
      const averageVisitTime = await this.getAverageVisitTimeSpent(depotId, saleRepIds, startDate, endDate);
      const brandVolumeBySaleRep = await this.getBrandVolumeBySaleRep(depotId, saleRepIds, startDate, endDate);
      const availability = await this.geAvailability(depotId, saleRepIds, startDate, endDate);
      const visibility = await this.getVisibility(depotId, saleRepIds, startDate, endDate);
      const salesValueVolume = await this.getSalesValueVolume(depotId, saleRepIds, startDate, endDate);
      const callCompliance = await this.getCallCompliance(depotId, saleRepIds, startDate, endDate);
      const outletVisitsRange = await this.getOutletVisitsRange(depotId, saleRepIds, startDate, endDate);
      const picosOutletCompliance = await this.getPicosOutletCompliance(depotId, saleRepIds, startDate, endDate);
      const planMissedOutlets = await this.getPlanMissedOutlets(saleRepIds, startDate, endDate, i18n);
      const upcomingPlanedOutlets = await this.getUpcomingPlanedOutlets(saleRepIds, i18n);
      const activeAndInActiveOutlets = await this.chartReportingService.getActiveAndInactiveOutlets(saleRepIds, startDate, endDate);

      return {
        brandVolumePerformance,
        activeSellingOutlet: {
          ...activeSellingOutlet,
          totalActiveOutlet: activeAndInActiveOutlets?.active?.length || 0,
          totalInactiveOutlet: activeAndInActiveOutlets?.inactive?.length || 0,
        },
        inVisitOrders,
        averageVisitTime,
        brandVolumeBySaleRep,
        availability,
        visibility,
        salesValueVolume,
        callCompliance,
        outletVisitsRange,
        picosOutletCompliance,
        planMissedOutlets,
        upcomingPlanedOutlets,
      };
    } catch (e) {
      console.log('error getK360MetricsForSalesRep', e);
    }
  }

  /**
   * @param depotId
   * @param saleRepIds
   * @param fromDate
   * @param toDate
   */
  async getOmNiKpiMetrics(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date): Promise<any> {
    try {
      const salesRepIds = Array.from(new Set(saleRepIds));
      if (!isEmptyObjectOrArray(this.kpiMetrics)) {
        return this.kpiMetrics;
      }
      this.kpiMetrics = await this.chartReportingService.calculate360Kpi({ fromDate, toDate, salesRepIds: salesRepIds, depotId });
      return this.kpiMetrics;
    } catch (e) {
      console.log('Error get omni kpi metrics', e);
    }
  }

  /**
   * @param depotId
   * @param saleRepIds
   * @param fromDate
   * @param toDate
   */
  async getOMSKpiMetrics(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date): Promise<any> {
    try {
      const salesRepIds = Array.from(new Set([...saleRepIds]));
      const key = `getOmNiKpiMetrics-${saleRepIds.join(', ')}-${depotId}-${fromDate}-${toDate}`;
      const cacheRes = await this.cacheManager.get(key);
      if (!isEmptyObjectOrArray(cacheRes)) {
        return cacheRes;
      }
      const kpi360 = await this.getK360MetricsForSalesRep(depotId, salesRepIds, fromDate, toDate);
      await this.cacheManager.set(key, kpi360 || [], getRandomTTL(300));
      return kpi360;
    } catch (e) {
      console.log(`Error get OMS KPI Metrics for depot ${depotId} and saleReps ${saleRepIds.join(', ')}`, e);
    }
  }

  /**
   * Waiting for OMS
   * @param depotId
   * @param saleRepIds
   * @param fromDate
   * @param toDate
   */
  async getBrandVolumePerformance(depotId: string, saleRepId: string[], fromDate: Date, toDate: Date) {
    try {
      return {
        timeGone: {
          current: Math.round(Math.random() * 100),
          target: Math.round(Math.random() * 100),
          targetPercent: Math.round(Math.random() * 100),
        },
        volumes: [
          {
            name: 'Total',
            current: Math.round(Math.random() * 100),
            yesterday: Math.round(Math.random() * 100),
            target: Math.round(Math.random() * 100),
            mtg: Math.round(Math.random() * 100),
          },
          {
            name: 'Brand 1',
            current: Math.round(Math.random() * 100),
            yesterday: Math.round(Math.random() * 100),
            target: Math.round(Math.random() * 100),
            mtg: Math.round(Math.random() * 100),
          },
          {
            name: 'Brand 2',
            current: Math.round(Math.random() * 100),
            yesterday: Math.round(Math.random() * 100),
            target: Math.round(Math.random() * 100),
            mtg: Math.round(Math.random() * 100),
          },
        ],
      }; // await this.getOmNiKpiMetrics(depotId, saleRepId, fromDate, toDate);
    } catch (e) {}
  }

  async getActiveSellingOutlet(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date) {
    try {
      return (await this.getOmNiKpiMetrics(depotId, saleRepIds, fromDate, toDate))?.aso;
    } catch (e) {}
  }

  async getInVisitOrders(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date) {
    try {
      return (await this.getOmNiKpiMetrics(depotId, saleRepIds, fromDate, toDate))?.order;
    } catch (e) {}
  }

  async getAverageVisitTimeSpent(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date) {
    try {
      return (await this.getOmNiKpiMetrics(depotId, saleRepIds, fromDate, toDate))?.averageVisitTime;
    } catch (e) {}
  }

  /**
   * Waiting for OMS
   * @param depotId
   * @param saleRepIds
   * @param fromDate
   * @param toDate
   */
  async getBrandVolumeBySaleRep(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date) {
    try {
      return null; // await this.getOmNiKpiMetrics(depotId, saleRepId, fromDate, toDate);
    } catch (e) {}
  }

  async geAvailability(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date) {
    try {
      return (await this.getOmNiKpiMetrics(depotId, saleRepIds, fromDate, toDate))?.availability;
    } catch (e) {}
  }

  async getVisibility(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date) {
    try {
      return (await this.getOmNiKpiMetrics(depotId, saleRepIds, fromDate, toDate))?.visibility;
    } catch (e) {}
  }

  async getSalesValueVolume(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date) {
    try {
      return await this.chartReportingService.getVolumeSalesRepStatisticsOMS({ fromDate, toDate, salesRepIds: saleRepIds, depotId });
    } catch (e) {}
  }

  async getCallCompliance(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date) {
    try {
      return (await this.getOmNiKpiMetrics(depotId, saleRepIds, fromDate, toDate))?.callComplianceRate;
    } catch (e) {}
  }

  async getOutletVisitsRange(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date) {
    try {
      return (await this.getOmNiKpiMetrics(depotId, saleRepIds, fromDate, toDate))?.outletVisitsRange;
    } catch (e) {}
  }

  async getPicosOutletCompliance(depotId: string, saleRepIds: string[], fromDate: Date, toDate: Date) {
    try {
      const availability = (await this.getOmNiKpiMetrics(depotId, saleRepIds, fromDate, toDate))?.availability;
      const visibility = (await this.getOmNiKpiMetrics(depotId, saleRepIds, fromDate, toDate))?.visibility;
      return {
        availability,
        visibility,
      };
    } catch (e) {}
  }

  /**
   *
   * @param saleRepIds KEY
   * @param fromDate
   * @param toDate
   * @param i18n
   */
  async getPlanMissedOutlets(saleRepIds: string[], fromDate: Date, toDate: Date, i18n: I18nContext) {
    try {
      return await this.outletJourneyPlanningService.findPlannedOutletByMultiSaleRep(
        {
          saleRepIds: saleRepIds,
          distributorId: null,
          firstDay: fromDate,
          lastDay: toDate,
          filterVisitedStatus: ConstantCommons.MISSED,
          skip: 0,
          limit: 10,
        },
        i18n,
      );
    } catch (e) {}
  }

  /**
   *
   * @param saleRepIds KEY
   * @param i18n
   */
  async getUpcomingPlanedOutlets(saleRepIds: string[], i18n: I18nContext) {
    try {
      return await this.outletJourneyPlanningService.findUpcomingOutletPlans(
        {
          saleRepIds: saleRepIds,
          skip: 0,
          limit: 10,
        },
        i18n,
      );
    } catch (e) {}
  }

  /**
   *
   * @param saleRepIds
   * @param fromDate
   * @param toDate
   * @param type
   * @param i18n
   */
  async getExecutionDiscipline(saleRepIds: string[], fromDate: Date, toDate: Date, type: string, i18n: I18nContext) {
    try {
      switch (type) {
        case ExecutionDisciplineType.AVAILABILITY:
          return await this.chartReportingService.getAvailableProducts(saleRepIds, fromDate, toDate, i18n);
        case ExecutionDisciplineType.VISIBILITY:
          return await this.chartReportingService.getAvailableVisibilityTasks(saleRepIds, fromDate, toDate, i18n);
      }
    } catch (e) {}
  }

  async getInActiveOutlet(saleRepIds: string[], fromDate: Date, toDate: Date, type: string, i18n: I18nContext) {
    try {
    } catch (e) {}
  }

  async getAdHocOrder(saleRepIds: string[], fromDate: Date, toDate: Date, type: string, i18n: I18nContext) {
    try {
    } catch (e) {}
  }

  async getVisitedOutlet(saleRepIds: string[], fromDate: Date, toDate: Date, type: string, i18n: I18nContext) {
    try {
    } catch (e) {}
  }
}
