import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { RepTargetSettings } from '../entities/rep-target-settings.entity';

@Injectable()
export class RepTargetSettingsService extends BaseSQLService<RepTargetSettings> {
  constructor(
    @InjectRepository(RepTargetSettings)
    private readonly _targetSettingsRepository: Repository<RepTargetSettings>,
  ) {
    super();
    this._repository = this._targetSettingsRepository;
  }
}
