import { Twi<PERSON> } from 'twilio';
import { printLog } from '../../utils';
import { LogsService } from '../../settings/logs.service';

export const sendSMS = async (message: string, from = '', to = '', logServices: LogsService = null) => {
  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    from = from || process.env.TWILIO_PHONE_NUMBER;
    if (!to || !message || !from) {
      return { success: false, message: "Don't enough params." };
    }
    const client = new Twilio(accountSid, authToken);
    const resMesssage = await client.messages.create({
      body: message,
      from,
      to,
    });
    printLog('sendSMS:', resMesssage);
    return { success: true, message: '' };
  } catch (error) {
    logServices
      ?.saveLogByKey('twilio-send-sms', {
        userId: null,
        isUserAdmin: false,
        key: 'twilio-send-sms',
        feature: 'twilio-send-sms',
        code: 500,
        request: { from, to, message },
        response: error?.message,
      })
      .then()
      .catch();
    printLog('sendSMS ERROR:', error.message);
    return { success: false, message: error.message };
  }
};
