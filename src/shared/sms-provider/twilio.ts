import { Twilio, jwt, twiml } from 'twilio';
import { printLog } from '../../utils';
import { LogsService } from '../../settings/logs.service';

import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';

export const sendSMS = async (message: string, from = '', to = '', logServices: LogsService = null) => {
  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    from = from || process.env.TWILIO_PHONE_NUMBER;
    if (!to || !message || !from) {
      return { success: false, message: "Don't enough params." };
    }
    const client = new Twilio(accountSid, authToken);
    const resMesssage = await client.messages.create({
      body: message,
      from,
      to,
    });
    printLog('sendSMS:', resMesssage);
    return { success: true, message: '' };
  } catch (error) {
    logServices
      ?.saveLogByKey('twilio-send-sms', {
        userId: null,
        isUserAdmin: false,
        key: 'twilio-send-sms',
        feature: 'twilio-send-sms',
        code: 500,
        request: { from, to, message },
        response: error?.message,
      })
      .then()
      .catch();
    printLog('sendSMS ERROR:', error.message);
    return { success: false, message: error.message };
  }
};

export const initializePhoneCall = async (identity: string) => {
  const AccessToken = jwt.AccessToken;
  const VoiceGrant = AccessToken.VoiceGrant;

  const voiceGrant = new VoiceGrant({
    outgoingApplicationSid: process.env.TWILIO_TWIML_APP_ID,
    incomingAllow: true,
  });

  const token = new AccessToken(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_API_TOKEN, process.env.TWILIO_API_SECRET, {
    identity: identity || process.env.TWILIO_PHONE_NUMBER,
  });
  token.addGrant(voiceGrant);

  return {
    token: token.toJwt(),
  };
};

export const makePhoneCall = async (to: string, callPlanId: string, callerId?) => {
  try {
    const client = new Twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    const call = await client.calls.create({
      from: process.env.TWILIO_PHONE_NUMBER,
      to,
      callerId: callerId || process.env.TWILIO_PHONE_NUMBER,
      // applicationSid: process.env.TWILIO_TWIML_APP_ID,
      twiml: getConferenceSipXmlTwimlConfig({ callPlanId, callerId: callerId?.replace(/\s+/g, '_') || process.env.TWILIO_PHONE_NUMBER }),
      // url: `${process.env.TWILIO_TWIML_CALLBACK}?callPlanId=${callPlanId}&callerId=${callerId?.replace(/\s+/g, '_') || process.env.TWILIO_PHONE_NUMBER}`,
      // method: 'GET',
      statusCallback: `${process.env.TWILIO_RECORD_STATUS_CALLBACK}?callPlanId=${callPlanId}`,
      statusCallbackMethod: 'POST',
      statusCallbackEvent: ['answered', 'completed'],
      // callToken: callPlanId,
      // record: true,
      // recordingStatusCallbackMethod: 'POST',
      // recordingStatusCallback: `${process.env.TWILIO_RECORD_STATUS_CALLBACK}?callPlanId=${callPlanId}?conference=${conferenceName}`,
      // recordingStatusCallbackEvent: ['in-progress', 'completed', 'absent'],
    });
    return call;
  } catch (error) {
    console.log(`Error create outbound call for callPlan ${callPlanId} as ${callerId}`, error);
    return;
  }
};

export const generateSIPURLfromPhoneNumber = (to: string) => {
  return `sip:${to}@${process.env.OOREDOO_SBC_IP};edge=${process.env.OOREDOO_SBC_REGION}`;
};

const getCallSipXmlTwimlConfig = (payload: any) => {
  const { to, callPlanId } = payload;
  const voiceResponse = new twiml.VoiceResponse();
  const sipUrl = generateSIPURLfromPhoneNumber(to);
  const dial = voiceResponse.dial({
    action: process.env.TWILIO_ACTION_URL,
    callerId: process.env.TWILIO_PHONE_NUMBER,
    record: 'record-from-answer-dual',
    recordingStatusCallback: `${process.env.TWILIO_RECORD_STATUS_CALLBACK}?callPlanId=${callPlanId}`,
    recordingStatusCallbackEvent: ['in-progress', 'completed', 'absent'],
  });

  dial.sip(sipUrl);

  return dial.toString();
};

export const getConferenceSipXmlTwimlConfig = (payload: any) => {
  const { callPlanId, callerId } = payload;
  // const voiceResponse = new twiml.VoiceResponse();
  // const conferenceDialOptions: VoiceResponse.DialAttributes = {};
  // if (callPlanId) {
  //   conferenceDialOptions.callerId = callerId || process.env.TWILIO_PHONE_NUMBER;
  //   conferenceDialOptions.record = 'record-from-answer-dual';
  //   conferenceDialOptions.recordingStatusCallback = `${process.env.TWILIO_RECORD_STATUS_CALLBACK}?callPlanId=${callPlanId}`;
  //   conferenceDialOptions.recordingStatusCallbackEvent = ['in-progress', 'completed', 'absent'];
  // }
  // const dial = voiceResponse.dial(conferenceDialOptions);

  // dial.conference(callPlanId || 'Default Conference Room');

  return `
  <?xml version="1.0" encoding="UTF-8"?>
  <Response>
    <Dial callerId="${
      callerId || process.env.TWILIO_PHONE_NUMBER
    }" record="record-from-answer-dual" recordingStatusCallback="${`${process.env.TWILIO_RECORD_STATUS_CALLBACK}?callPlanId=${callPlanId}`}" recordingStatusCallbackEvent="in-progress completed absent">
      <Conference endConferenceOnExit="true">${callPlanId || 'Default Conference Room'}</Conference>
    </Dial>
  </Response>`
    .replace(/\\"/g, '"')
    .replace(/\\n/g, '')
    .trim();

  // return dial.toString();
};

export const getTwimlConfig = async (callPlanId, payload: any) => {
  const { type } = payload;

  if (type && type === 'conference') {
    return getConferenceSipXmlTwimlConfig({ ...payload, callPlanId });
  }

  return getCallSipXmlTwimlConfig({ ...payload, callPlanId });
};

export const getTwilioCallDetails = async (callSid: string) => {
  try {
    const client = new Twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    const clientCallContext = await client.calls.get(callSid);
    return await clientCallContext.fetch();
  } catch (error) {
    console.log(`Error get twilio call detail ${callSid}`, error);
    return;
  }
};

export const endTwilioCall = async (callSid: string) => {
  let twilioCall;
  try {
    const client = new Twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    twilioCall = await client.calls.get(callSid).update({ status: 'completed' });
  } catch (error) {
    console.log(`Error end twilio call ${callSid}`, error);
  }
  return twilioCall;
};

export const endTwilioConferenceRoom = async (conferenceName: string) => {
  try {
    const client = new Twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    const endedConferenceRooms = [];
    const twilioConferenceRooms = await client.conferences.list({ friendlyName: conferenceName });
    for (let i = 0; i < (twilioConferenceRooms || []).length; i++) {
      const endedConferenceRoom = await client.conferences.get(twilioConferenceRooms[i].sid).update({ status: 'completed' }).catch();
      endedConferenceRooms.push(endedConferenceRoom);
    }
    return endedConferenceRooms;
  } catch (error) {
    console.log(`Error end twilio conference room ${conferenceName}`, error);
  }
};

export const getRecordingUrl = async (recordingSid: string) => {
  const client = new Twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
  const recording = await client.recordings(recordingSid).fetch();

  const auth = Buffer.from(`${process.env.TWILIO_ACCOUNT_SID}:${process.env.TWILIO_AUTH_TOKEN}`).toString('base64');
  const headers = {
    Authorization: `Basic ${auth}`,
  };

  const response = await fetch(`https://api.twilio.com${recording.uri}.mp3`, { headers });
  return await response.arrayBuffer();
};

// export const streamRecording = async(recordingSid: string, res: Response) => {
//   // Construct the Twilio recording media URL (use .mp3 or .wav)
//   const recordingUrl = `https://api.twilio.com/2010-04-01/Accounts/ACfd28009b9a887d908edaf3c3727e745d/Recordings/RE9676e0677025e1bb032735e03293b6b6.mp3`;

export const getRecordingStream = async (recordingUrl: string, httpService: HttpService) => {
  // const recordingUrl = `https://api.twilio.com/2010-04-01/Accounts/ACfd28009b9a887d908edaf3c3727e745d/Recordings/RE9676e0677025e1bb032735e03293b6b6.mp3`;

  // Make the request to Twilio, getting the response object
  const response = await lastValueFrom(
    httpService.get(recordingUrl, {
      responseType: 'stream',
      auth: {
        username: process.env.TWILIO_API_TOKEN,
        password: process.env.TWILIO_API_SECRET,
      },
    }),
  );

  // Extract necessary data from Twilio's response
  const streamData = {
    stream: response.data,
    contentType: response.headers['content-type'] || 'audio/mpeg',
    contentLength: response.headers['content-length'],
  };

  // Handle potential errors on the stream itself *after* returning it
  // The controller/framework will handle piping
  response.data.on('error', (streamError) => {
    console.log(`Error during streaming for ${recordingUrl}:`, streamError);
    // Handling depends on when this happens; often the connection is already closed
  });

  response.data.on('end', () => {
    console.log(`Twilio stream ended for ${recordingUrl}`);
  });

  return streamData;
};
