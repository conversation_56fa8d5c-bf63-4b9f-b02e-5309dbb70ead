class ApiError {
  code: number;
  message: string;

  constructor(errorCode = 0, message = '') {
    this.code = errorCode;
    this.message = message;
  }
}

export class ApiResponse {
  status: boolean;
  error: ApiError | null;
  data: any;

  constructor(data: any = {}, status = true, error: ApiError | null = null) {
    this.status = status;
    this.error = error && error.code !== 0 ? error : null;
    this.data = data;
  }
}
