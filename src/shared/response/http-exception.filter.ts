import { ArgumentsHost, Catch, ExceptionFilter, HttpException, Injectable, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import * as _ from 'lodash';
import { getI18nContextFromArgumentsHost } from 'nestjs-i18n';
import { AIService } from '../application-insights/appinsight.service';
import { printLog } from '../../utils';
import { ErrorResponse } from './error-response';

@Injectable()
@Catch(Error)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(
    private readonly aiService: AIService
  ) { }

  private getErrorDetails(exception: HttpException): { status: number; errorType: string; messages: any } {
    const status = exception?.getStatus ? exception.getStatus() : 500;
    const errorType = exception.message;
    const messages = ((exception?.getResponse ? exception.getResponse() : null) as any)?.message ?? exception.message;

    return { status, errorType, messages };
  }

  private formatErrorResponse(status: number, errorType: string, message: string, stack?: string): ErrorResponse {
    return {
      code: status,
      error: errorType,
      message,
      ...(stack && { stack })
    };
  }

  private async translateError(i18n: any, status: number, errorType: string, message: string): Promise<ErrorResponse> {
    if (!i18n.lang) {
      return this.formatErrorResponse(status, errorType, message);
    }

    try {
      return {
        code: status,
        error: await i18n.t(errorType),
        message: await i18n.t(message)
      };
    } catch (e) {
      return this.formatErrorResponse(status, errorType, message);
    }
  }

  private shouldLogToAppInsights(status: number): boolean {
    // Log both client errors (4xx) and server errors (5xx)
    return status >= HttpStatus.BAD_REQUEST;
  }

  private logErrorToAppInsights(exception: HttpException, request: any, errorResponse: ErrorResponse): void {
    const { method, url, headers, query, params } = request;
    const status = exception?.getStatus ? exception.getStatus() : 500;

    // For 4xx errors, use logError
    if (status >= HttpStatus.BAD_REQUEST && status < HttpStatus.INTERNAL_SERVER_ERROR) {
      this.aiService.logError('Error occurred', {
        error: errorResponse,
        request: {
          method,
          url,
          query,
          params
        }
      });
    } 
    // For 5xx errors, use logException
    else {
      this.aiService.logException(exception, {
        error: errorResponse,
        request: {
          method,
          url,
          query,
          params
        }
      });
    }
  }

  async catch(exception: HttpException, host: ArgumentsHost) {
    // Log original exception
    printLog('🚀 ~ exception:', exception);

    // Get HTTP context
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    // Extract error details
    const { status, errorType, messages } = this.getErrorDetails(exception);
    const i18n = getI18nContextFromArgumentsHost(host);

    // Format message
    const message = messages ? (_.isArray(messages) ? messages[0] : messages) : errorType;

    // Translate and format error response
    const error = await this.translateError(i18n, status, errorType, message);

    // Log to Application Insights for both client and server errors
    if (this.shouldLogToAppInsights(status)) {
      this.logErrorToAppInsights(exception, request, error);
    }

    // Send response
    response.status(status).send({
      status: false,
      error,
      data: {}
    });
  }
}
