import { ArgumentsHost, Catch, ExceptionFilter, HttpException, Injectable } from '@nestjs/common';
import { Response } from 'express';
import * as _ from 'lodash';
import { I18nContext } from 'nestjs-i18n';
import { LogsService } from '../../settings/logs.service';
import { printLog } from '../../utils';

@Injectable()
@Catch(Error)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(private readonly _logService: LogsService) {}

  async catch(exception: HttpException, host: ArgumentsHost) {
    printLog('🚀 ~ exception:', exception);
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception?.getStatus ? exception.getStatus() : 500;
    const errorType = exception.message;
    const messages = ((exception?.getResponse ? exception.getResponse() : null) as any)?.message ?? exception.message;
    const i18n = I18nContext.current();
    const message = messages ? (_.isArray(messages) ? messages[0] : messages) : errorType;
    let error = { code: status, error: errorType, message };
    const data = {};
    if (i18n.lang) {
      try {
        error = { code: status, error: await i18n.t(errorType), message: await i18n.t(message) };
      } catch (e) {}
    }

    /*try {
      const request = ctx.getRequest();
      const token = request?.headers?.authorization?.split(' ')[1];
      const action = request.url?.split('?')[0];

      await this._logService.saveActionLog(token, action, {
        code: status,
        request: { method: request.method, params: request?.params, query: request?.query, body: request?.body },
        response: { error: { ...error, trace: exception.stack }, data },
      });
    } catch (e) {}*/

    response.status(status).send({
      status: false,
      error,
      data,
    });
  }
}
