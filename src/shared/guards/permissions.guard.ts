import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { I18nService } from 'nestjs-i18n';
import { SalesRepStatus } from '../../users/enums';
import { isEmptyArray } from '../../utils';
import { ConstantUser } from '../../utils/constants/user';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private readonly _reflector: Reflector, private readonly i18n: I18nService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const user = context.switchToHttp()?.getRequest()?.currentUser ?? null;

    if (user?.status == ConstantUser.BLOCKED || user?.saleRepStatus == SalesRepStatus.INACTIVE) {
      throw new HttpException(await this.i18n.t('user.unauthorized_inactive'), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
    }
    const roles = this._reflector.get<string[]>('roles', context.getHandler());
    const permissions = this._reflector.get<string[]>('permissions', context.getHandler());
    if (!roles || !permissions) {
      return true;
    }

    if (user && !isEmptyArray(user?.roles)) {
      //-- code here
      return true;
    } else {
      return false;
    }
  }
}
