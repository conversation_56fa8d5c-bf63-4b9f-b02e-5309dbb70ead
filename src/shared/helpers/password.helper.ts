import * as moment from 'moment-timezone';
import { PASSWORD_MAX_AGE, PASSWORD_MIN_AGE } from '../constants';
import { isProductionEnv } from 'src/utils';

export const isPasswordExpired = (lastUpdatedPassword: Date = null) => {
  if (!lastUpdatedPassword) return false;
  const currentDate = moment().tz(process.env.TZ);
  const diff = currentDate.diff(moment(lastUpdatedPassword).tz(process.env.TZ), 'second');
  return diff > PASSWORD_MAX_AGE * 24 * 60 * 60;
};

export const isMinAgePassword = (lastUpdatedPassword: Date = null) => {
  if (!lastUpdatedPassword) return false; //for case don't have password.
  const currentDate = moment().tz(process.env.TZ);
  const diff = currentDate.diff(moment(lastUpdatedPassword).tz(process.env.TZ), 'second');
  return diff < PASSWORD_MIN_AGE * 24 * 60 * 60;
};
