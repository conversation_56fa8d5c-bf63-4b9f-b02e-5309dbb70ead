import * as moment from 'moment-timezone';

/**
 * Get start and end dates for a specific month and year
 * @param month - Month (1-12)
 * @param year - Year (4 digits)
 * @param timezone - Timezone (defaults to process.env.TZ)
 * @returns Object with startDate and endDate
 */
export function getMonthDateRange(month: number, year: number, timezone: string = process.env.TZ): { startDate: Date; endDate: Date } {
  const startDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, timezone).startOf('month');
  const endDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, timezone).endOf('month');

  return {
    startDate: startDate.toDate(),
    endDate: endDate.toDate(),
  };
}

/**
 * Get previous month's date range
 * @param month - Current month (1-12)
 * @param year - Current year (4 digits)
 * @param timezone - Timezone (defaults to process.env.TZ)
 * @returns Object with startDate and endDate for previous month
 */
export function getPreviousMonthDateRange(month: number, year: number, timezone: string = process.env.TZ): { startDate: Date; endDate: Date } {
  let previousMonth = month - 1;
  let previousYear = year;

  if (previousMonth === 0) {
    previousMonth = 12;
    previousYear = year - 1;
  }

  return getMonthDateRange(previousMonth, previousYear, timezone);
}
