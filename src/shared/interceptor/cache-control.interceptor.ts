import { CallH<PERSON>ler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ApiResponse } from '../response/api-response';

@Injectable()
export class CacheControlInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    if (
      request.url &&
      (request.url.includes('/api/notifications/listen') || request.url.includes('/api/call-center/lister') || request.url.includes('/api/notifications/stream'))
    ) {
      return next.handle();
    }

    if (!response.headersSent) {
      response.setHeader('Cache-Control', 'no-store');
      response.setHeader('Pragma', 'no-cache');
    }

    return next.handle().pipe(
      tap(() => {
        // Additional logic if needed
      }),
    );
  }
}
 