import { InternalServerErrorException } from '@nestjs/common';
import { Document, FilterQuery, Model } from 'mongoose';
import { BaseDto } from '../dtos/base.dto';
import { BaseSchema } from '../schemas/base.schema';

/**
 * Abstract base service that other services can extend to provide base CRUD
 * functionality such as to create, find, update and delete data.
 */
export abstract class BaseService<T extends BaseSchema> {
  protected model: Model<T & Document>;

  /**
   * Find one entry and return the result.
   *
   * @throws InternalServerErrorException
   */
  async findOne(conditions: FilterQuery<T>, projection: string | Record<string, unknown> = {}, options: Record<string, unknown> = {}): Promise<T> {
    try {
      return await this.model.findOne(conditions as FilterQuery<T>, projection, options);
    } catch (err) {
      return null;
    }
  }

  async findAll(conditions: FilterQuery<T>, projection: string | Record<string, unknown> = {}, options: Record<string, unknown> = {}): Promise<T[]> {
    return await this.model.find(conditions as FilterQuery<T>, projection, options).exec();
  }

  async findById(id: string): Promise<T> {
    return await this.model.findById(id).exec();
  }

  async create(createDto: T | BaseDto): Promise<T> {
    return await new this.model({
      ...createDto,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).save();
  }

  async update(id: string, updateDto: T | BaseDto, populate = ''): Promise<T> {
    const updatedAt = (updateDto as any).updatedAt || new Date();
    const updated = this.model.findByIdAndUpdate(id, { ...updateDto, updatedAt }, { new: true });
    if (populate?.trim()) {
      updated.populate(populate?.trim());
    }
    return updated;
  }

  async updateByCondition(filter: Record<string, any>, updateDto: T | BaseDto) {
    return this.model.updateMany(filter, { ...updateDto, updatedAt: new Date() }, { new: true }).exec();
  }

  async findOneAndUpdate(filter: Record<string, any>, updateDto: T | BaseDto): Promise<T> {
    return this.model.findOneAndUpdate(filter, { ...updateDto, updatedAt: new Date() }, { new: true });
  }

  async findOneOrCreate(filter: Record<string, any>, updateDto: T | BaseDto): Promise<T> {
    const order = await this.model.findOne(filter);
    return order ? this.update(order.id, updateDto) : this.create(updateDto);
  }

  async delete(id: string): Promise<T> {
    return await this.model.findByIdAndDelete(id).exec();
  }

  async deleteByCondition(filter: Record<string, any>) {
    return this.model.deleteMany(filter).exec();
  }
}
