import { HttpException, HttpStatus } from '@nestjs/common';
import { DeepPartial, FindOptionsOrder, Repository, SelectQueryBuilder } from 'typeorm';
import { I18nContext } from 'nestjs-i18n';
import * as moment from 'moment';
import { BaseSQLEntity } from '../basesql.entity';
import { PaginationRequestParams } from '../dtos/pagination-request-params.dto';
import { PaginatedResponse } from '../dtos/paginated-response.dto';
import { ConstantCommons } from '../../utils/constants';

export abstract class BaseSQLService<T extends BaseSQLEntity> {
  protected _repository: Repository<T>;

  /**
   * Generic paginated find method that handles offset, limit, orderBy, orderDesc, and optional relations
   * @param filters - Where conditions for the query
   * @param params - Pagination and ordering parameters
   * @param relations - Optional array of relation names to include
   * @param defaultOrderBy - Default field to order by (defaults to 'updatedAt')
   * @returns Promise<PaginatedResponse<T>> - Object containing count and data array
   */
  async findPaginated(
    filters: any = {},
    params: PaginationRequestParams = {},
    relations: string[] = [],
    defaultOrderBy: string = ConstantCommons.ORDER_BY_DEFAULT,
  ): Promise<PaginatedResponse<T>> {
    const { offset, limit, orderBy, orderDesc } = this.buildPaginationOptions(params, defaultOrderBy);

    // Build order object if orderBy is provided
    const order: FindOptionsOrder<T> = orderBy ? ({ [orderBy]: orderDesc } as FindOptionsOrder<T>) : {};

    // Execute findAndCount with pagination, ordering, and optional relations
    const [data, count] = await this._repository.findAndCount({
      where: filters,
      order: Object.keys(order).length > 0 ? order : undefined,
      skip: offset,
      take: limit,
      relations: relations.length > 0 ? relations : undefined,
    });

    return {
      count,
      data,
    };
  }

  /**
   * Generic paginated find method using QueryBuilder for complex joins and selects
   * @param queryBuilder - Function that builds the query with joins and conditions
   * @param params - Pagination and ordering parameters
   * @param defaultOrderBy - Default field to order by (defaults to 'updatedAt')
   * @returns Promise<PaginatedResponse<T>> - Object containing count and data array
   */
  async findPaginatedWithQueryBuilder(
    queryBuilder: (qb: SelectQueryBuilder<T>) => SelectQueryBuilder<T>,
    params: PaginationRequestParams = {},
    defaultOrderBy: string = ConstantCommons.ORDER_BY_DEFAULT,
  ): Promise<PaginatedResponse<T>> {
    const { offset, limit, orderBy, orderDesc } = this.buildPaginationOptions(params, defaultOrderBy);

    // Build the base query
    let query = this._repository.createQueryBuilder('entity');

    // Apply custom query builder logic
    query = queryBuilder(query);

    // Apply pagination and ordering
    const [data, count] = await query.skip(offset).take(limit).orderBy(orderBy, orderDesc).getManyAndCount();

    return {
      count,
      data,
    };
  }

  /**
   * Build pagination options with default values
   * @param paginationParams - Raw pagination parameters
   * @param defaultOrderBy - Default field to order by (defaults to 'updatedAt')
   * @returns Processed pagination options
   */
  protected buildPaginationOptions(paginationParams: PaginationRequestParams, defaultOrderBy: string = ConstantCommons.ORDER_BY_DEFAULT) {
    const { offset = 0, limit = 10, orderBy = defaultOrderBy, orderDesc = ConstantCommons.ORDER_DESC_DEFAULT } = paginationParams;

    return {
      offset,
      limit,
      orderBy,
      orderDesc: orderDesc as 'ASC' | 'DESC',
    };
  }

  /**
   * For using hook purpose, should use this function before creating persistent data
   * @param entity
   */
  async createEntityInstance(entity: DeepPartial<T>): Promise<T> {
    return this._repository.create(entity);
  }

  async save(entity: DeepPartial<T> | any): Promise<T> {
    return this._repository.save(entity);
  }

  async saveList(entities: Array<DeepPartial<T>>): Promise<Array<T>> {
    return this._repository.save(entities);
  }

  async saveRows(entity: any): Promise<T> {
    return this._repository.save(entity);
  }

  async findAll(): Promise<T[]> {
    return this._repository.find();
  }

  async find(filter = {}): Promise<T[]> {
    return this._repository.find(filter);
  }

  async findWithOptions(options = {}): Promise<T[]> {
    return this._repository.find(options);
  }

  async findWithOrder(filters: any = {}, orderBy: keyof T, orderDesc: 'ASC' | 'DESC' = 'ASC'): Promise<T[] | any> {
    const order: FindOptionsOrder<T> = { [orderBy]: orderDesc } as FindOptionsOrder<T>;
    return this._repository.find({
      where: filters,
      order,
    });
  }

  async findAndCount(filter = {}): Promise<[T[], number]> {
    return this._repository.findAndCount(filter);
  }

  async findOne(filter = {}): Promise<T> {
    return this._repository.findOne(filter);
  }

  async checkExisted(filter = {}, select: any = ['id']): Promise<T> {
    return this._repository.findOne({
      select: select,
      where: filter,
    });
  }

  /**
   * criteria: string | string[] | number | number[] | Date | Date[] | ObjectID | ObjectID[] | FindConditions<Entity>
   * @param criteria
   */
  async delete(criteria: any) {
    return this._repository.delete(criteria);
  }

  /**
   * entities: Entity[]
   * @param entities
   */
  async remove(entities: T[]) {
    return this._repository.remove(entities);
  }

  async getAndValidateExistedData(filter = {}, i18n: I18nContext, fieldName = ''): Promise<T> {
    let existedData;
    try {
      existedData = await this.findOne(filter);
    } catch (e) {
      throw new HttpException(e, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    if (!existedData) {
      throw new HttpException(
        await i18n.translate(`message.NOT_EXISTS_DATA`, {
          args: {
            fieldName: fieldName ? fieldName : this.constructor.name.split('Service')[0],
          },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
    return existedData;
  }

  async validateDateTime(dateTime: string, filedName: string, i18n: I18nContext) {
    if (!moment(dateTime).isValid()) {
      throw new HttpException(
        await i18n.translate('message.INVALID_FIELD', {
          args: {
            fieldName: filedName,
          },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
    return moment(dateTime);
  }

  async validateStartDateAndEndDate(
    startDate: string,
    endDate: string,
    i18n: I18nContext,
    compareWithCurrentDate = true,
    currentDateCompareEndDateMessage = 'message.CURRENT_DATE_MUST_BE_LESS_OR_EQUAL_THAN_END_DATE',
    startDateCompareEndDateMessage = 'message.START_DATE_MUST_BE_LESS_OR_EQUAL_THAN_END_DATE',
  ) {
    await this.validateDateTime(startDate, 'Start Date', i18n);
    await this.validateDateTime(endDate, 'End Date', i18n);
    if (compareWithCurrentDate && moment().isAfter(moment(endDate), 'day')) {
      throw new HttpException(await i18n.translate(currentDateCompareEndDateMessage), HttpStatus.BAD_REQUEST);
    }
    const startDateIsAfterEndDate = moment(startDate).isAfter(endDate);
    if (startDateIsAfterEndDate) {
      throw new HttpException(await i18n.translate(startDateCompareEndDateMessage), HttpStatus.BAD_REQUEST);
    }
  }
}
