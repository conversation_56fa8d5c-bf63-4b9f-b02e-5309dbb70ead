import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { SharedEvent } from '../event/shared.event';
import { ConstantEventName } from '../../../utils/events';
import { printLog } from '../../../utils';

@Injectable()
export class SharedListener {
  @OnEvent(ConstantEventName.SHARED_EVENT)
  handleSharedEvent(event: SharedEvent) {
    try {
      (async () => {
        if (event?.callback) {
          await event?.callback();
        }
      })();
    } catch (e) {
      printLog(e);
    }
  }
}
