import { IsOptional, IsString, Matches } from 'class-validator';
import { ConstantCommons } from '../../utils/constants';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class TimeRangeParams {
  @ApiPropertyOptional({ default: new Date().toISOString().slice(0, 10), description: 'yyyy-MM-ddTHH:mm:ss.SSSZ' })
  @IsOptional()
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  startTime: string;

  @ApiPropertyOptional({ default: new Date().toISOString().slice(0, 10), description: 'yyyy-MM-ddTHH:mm:ss.SSSZ' })
  @IsOptional()
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  endTime: string;
}
