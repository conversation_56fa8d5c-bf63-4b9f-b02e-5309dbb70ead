import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsOptional, IsString, Matches } from 'class-validator';
import { ConstantCommons } from '../../utils/constants';

export class TimeRangeParams {
  @ApiModelPropertyOptional({ default: new Date().toISOString().slice(0, 10), description: 'yyyy-MM-ddTHH:mm:ss.SSSZ' })
  @IsOptional()
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  startTime: string;

  @ApiModelPropertyOptional({ default: new Date().toISOString().slice(0, 10), description: 'yyyy-MM-ddTHH:mm:ss.SSSZ' })
  @IsOptional()
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  endTime: string;
}
