import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

export function IsValidDateRange(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidDateRange',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const startDate = args.object['startDate'];
          const endDate = args.object['endDate'];

          // If both dates are undefined, it's valid (will use default last week)
          if (!startDate && !endDate) {
            return true;
          }

          // If only one date is provided, it's invalid
          if (!startDate || !endDate) {
            return false;
          }

          // Check if dates are valid
          const start = new Date(startDate);
          const end = new Date(endDate);

          if (isNaN(start.getTime()) || isNaN(end.getTime())) {
            return false;
          }

          // Check if start date is not greater than end date
          return start <= end;
        },
        defaultMessage(args: ValidationArguments) {
          const startDate = args.object['startDate'];
          const endDate = args.object['endDate'];

          if (!startDate || !endDate) {
            return 'Both startDate and endDate must be provided or both must be undefined';
          }

          if (isNaN(new Date(startDate).getTime()) || isNaN(new Date(endDate).getTime())) {
            return 'startDate and endDate must be valid date strings';
          }

          return 'startDate cannot be greater than endDate';
        },
      },
    });
  };
}
