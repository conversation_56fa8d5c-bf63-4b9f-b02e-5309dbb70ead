import { BeforeUpdate, Column, CreateDate<PERSON>olumn, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export abstract class BaseSQLEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isDeleted: boolean;

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = new Date();
  }
}
