import {
  OMS_API_ADMIN_AUTH,
  OMS_API_ASO_FOR_SALES_REP,
  OMS_API_DEPOT_ORDER_CREATE,
  OMS_API_DEPOT_ORDERS,
  OMS_API_DEPOT_PROMOTIONS,
  OMS_API_DEPOT_VARIANTS,
  OMS_API_SALES_VOLUME_BY_SALES_REP,
} from 'src/external/constants/oms';
import {
  DOT_API_ADD_TO_CART,
  DOT_API_CART_INFO,
  DOT_API_CATALOG_SUGGESTIONS,
  DOT_API_CREATE_ORDER,
  DOT_API_FULFILLMENT_CENTERS,
  DOT_API_GET_INVOICE_ORDER,
  DOT_API_ORDER,
  DOT_API_PRODUCT_SEARCH,
  DOT_API_PROMOTION,
  DOT_API_SEARCH_CUSTOMER_ORDER,
  DOT_API_SEARCH_MEMBER,
  DOT_API_STORE_SETTING,
  DOT_API_USER_INFO,
  DOT_API_USER_LOGIN,
} from '../../external/constants';

export const ISO_DATE_PATTERN =
  /^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24\:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;

export const PASSWORD_MAX_AGE = 365; // in day
export const PASSWORD_MIN_AGE = 1; // in day
export const TIME_LINK_CREATE_PASSWORD = 24; //hour

type Method = 'post' | 'delete' | 'put' | 'get' | 'patch';

export const API_KEY_FEATURE_NAME_MAPPING: Record<string, { [key in Method]?: string }> = {
  // User
  ['/api/users/login']: {
    post: 'Login',
  },
  ['/api/users/login-otp']: {
    post: 'Login by otp',
  },
  ['/api/users/login-password-and-otp']: {
    post: 'Login by password and otp',
  },
  ['/api/users/login-verify-otp']: {
    post: 'Verify otp',
  },
  ['/api/users/refresh-token']: {
    post: 'Refresh token',
  },
  ['/api/users/resend-otp']: {
    post: 'Resend otp',
  },
  ['/api/users/refresh-admin-token']: {
    post: 'Refresh admin token',
  },
  ['/api/users/resend-admin-otp']: {
    post: 'Resend admin otp',
  },
  ['/api/users/device-token/:fcmToken']: {
    delete: 'Delete fcm token',
  },
  ['/api/users/login-email']: {
    post: 'Login by email',
  },
  ['/api/users/login-email-verify-otp']: {
    post: 'Verify otp of login by email',
  },
  ['/api/users/login-email-password']: {
    post: 'Login by email and password',
  },
  ['/api/users/admin-forgot-password']: {
    post: 'Admin forgot password',
  },
  ['/api/users/user-forgot-password']: {
    post: 'User forgot password',
  },
  ['/api/users/verify-otp-user-forgot-password']: {
    post: 'Verify otp user forgot password',
  },
  ['/api/users/create-password']: {
    post: 'Create password',
  },
  ['/api/users/check-link-create-password']: {
    post: 'Check link create password',
  },
  ['/api/users/:id']: {
    get: 'Get user detail',
  },
  ['/api/users/device-token']: {
    post: 'Create or update device token of user',
  },
  ['/api/users/logout']: {
    post: 'User logout',
  },
  ['/api/users/get-otp']: {
    post: 'Get otp',
  },
  // Auth
  ['/api/auth/verify-token']: {
    post: 'Verify token',
  },
  // Outlet
  ['/api/outlets']: {
    post: 'Create an outlet',
  },
  ['/api/outlets/map-to-dot-ucc']: {
    put: 'Map outlet with a UCC on DOT',
  },
  ['/api/outlets/dot-outlet-information']: {
    get: 'Get summary outlet data',
  },
  ['/api/outlets/promotions/:outletId']: {
    get: 'Get list promotions of outlet',
  },
  ['/api/outlets/list']: {
    post: 'Get active outlets of sales rep',
  },
  // Sales rep
  ['/api/sale-rep/:saleRepId/overview']: {
    get: 'Get overview data for homepage of mobile application',
  },
  ['/api/sale-rep/outlet-connected/:saleRepId/:outletId']: {
    get: 'Get connected outlet',
    put: 'Update connected outlet',
  },
  ['/api/sale-rep/:saleRepId/missed-outlets']: {
    get: 'Get missed outlets of sales rep',
  },
  ['/api/sale-rep/:saleRepId/outlets/searching']: {
    get: 'Search outlets',
  },
  ['/api/sale-rep/:saleRepId/outlets/:outletId/products/searching']: {
    get: 'Search product of outlet (sku)',
  },
  ['/api/sale-rep/:saleRepId/outlets']: {
    get: 'Get all outlets of sales rep',
  },
  ['/api/sale-rep/map-to-dot-store']: {
    put: 'Map sales rep with a store id on DOT',
  },
  ['/api/sale-rep/outlets/:outletId/execution/visibility']: {
    get: 'Get visibility of outlet',
    put: 'Create or update visibility of today plan by outlet',
  },
  ['/api/sale-rep/outlets/:outletId/execution/availability']: {
    get: 'Get availability of outlet',
    put: 'Create or update availability of today plan by outlet',
  },
  ['/api/sale-rep/outlets/:outletId/execution/affordability']: {
    get: 'Get affordability of outlet',
    put: 'Create or update affordability of today plan by outlet',
  },
  ['/api/sale-rep/outlets/:outletId/execution/affordabilities']: {
    put: 'Create or update multiple affordabilities of today plan by outlet',
  },
  ['/api/sale-rep/outlets/:outletId/execute-visit']: {
    post: 'Execute visit',
  },
  ['/api/sale-rep/list']: {
    post: 'Get list sales rep',
  },
  ['/api/sale-rep/list/export']: {
    post: 'Export list sales rep',
  },
  ['/api/sale-rep/option-list']: {
    post: 'Get sales rep option list',
  },
  ['/api/sale-rep/:saleRepUUID']: {
    get: 'Get sales rep detail by sales rep uuuid',
    put: 'Update a sales rep',
  },
  ['/api/sale-rep']: {
    post: 'Create a sales rep',
  },
  ['/api/sale-rep/today-outlet-details']: {
    post: 'Get detail of today outlet',
  },
  // Settings
  ['/api/setting/configs']: {
    get: 'Get setting configs',
  },
  ['/api/setting/search/logs']: {
    get: 'Get logs',
  },
  ['/api/setting/logs/export']: {
    post: 'Export logs',
  },
  ['/api/setting/redis-cache/get']: {
    get: 'Get redis cache',
  },
  ['/api/setting/redis-cache/update']: {
    post: 'Update redis cache',
  },
  ['/api/setting/add_or_update']: {
    post: 'Add or update setting',
  },
  ['/api/setting/is_send_sms']: {
    post: 'Check is send sms or not',
  },
  ['/api/setting/collection-list-data/:collection']: {
    get: 'Get data by collection',
  },
  // Orders
  ['/api/orders/latest']: {
    get: 'Get latest order',
  },
  ['/api/orders/list']: {
    get: 'Get list orders',
  },
  ['/api/orders/cart/:outletId']: {
    get: 'Get cart of outlet from DOT',
    put: 'Update cart of outlet',
  },
  ['/api/orders/place/:outletId']: {
    post: 'Place order',
  },
  // Distributor
  ['/api/distributor']: {
    post: 'Create or update distributor',
    get: 'Get all distributors',
  },
  ['/api/distributor/detail/:id']: {
    get: 'Get distributor detail',
  },
  ['/api/distributor/:id']: {
    put: 'Update distributor',
  },
  ['/api/distributor/relation']: {
    post: 'Create a relation between distributor admin user and distributor',
  },
  ['/api/distributor/upload/sales-rep-master-data/:distributorId']: {
    post: 'Upload sales rep master data',
  },
  ['/api/distributor/upload/customer-master-data/:distributorId']: {
    post: 'Upload customer master data',
  },
  ['/api/distributor/list']: {
    get: 'Get list distributors',
  },
  ['/api/distributor/list/export']: {
    get: 'Export list distributors',
  },
  ['/api/distributor/upload']: {
    post: 'Upload list distributors',
  },
  ['/api/distributor/base-journey-plan/setting']: {
    get: 'Get base journey plan setting',
    put: 'Update base journey plan setting',
  },
  ['/api/distributor/upload/base-journey-plan']: {
    post: 'Upload base journey plan',
  },
  // DSR Targets
  ['/api/dsr-targets/list']: {
    post: 'Get list DSR Target',
  },
  ['/api/dsr-targets/detail']: {
    post: 'Get DSR Target detail',
  },
  ['/api/dsr-targets/update']: {
    put: 'Update DSR Target',
  },
  // Journey planning
  ['/api/journey-plannings/cycles']: {
    post: 'Create cycle',
  },
  ['/api/journey-plannings/weeks']: {
    get: 'Get weeks',
    post: 'Create week',
  },
  ['/api/journey-plannings/sale-rep/:saleRepId/today']: {
    get: 'Get journey plans of today',
  },
  ['/api/journey-plannings/sale-rep/:saleRepId/upcoming']: {
    get: 'Get upcoming journey plans',
  },
  ['/api/journey-plannings/sale-rep/:saleRepId/:outletId/upcoming-in-current-week']: {
    get: 'Get upcoming journey plans in current week',
  },
  ['/api/journey-plannings/:journeyPlanId/reschedule']: {
    put: 'Reschedule a plan',
  },
  ['/api/journey-plannings/:journeyPlanId/missed-visit-reason']: {
    put: 'Set missed visit reason',
  },
  ['/api/journey-plannings/missed-reason-visits']: {
    put: 'Set missed reason for multiple visits',
  },
  ['/api/journey-plannings/missed-reason-outlet']: {
    put: 'Set missed reason for outlet',
  },
  ['/api/journey-plannings/missed-reason-outlets']: {
    put: 'Set missed reason for multiple outlets',
  },
  ['/api/journey-plannings/list-absences']: {
    get: 'Get list absence',
  },
  ['/api/journey-plannings/options-cycle']: {
    get: 'Get options cycle',
  },
  ['/api/journey-plannings/list']: {
    post: 'Get list journey plans',
  },
  ['/api/journey-plannings/export']: {
    post: 'Export journey plans',
  },
  ['/api/journey-plannings/base-journey-plan/export']: {
    post: 'Export base journey plan',
  },
  ['/api/journey-plannings/:journeyPlanningId']: {
    get: 'Get journey plan by id',
  },
  ['/api/journey-plannings/update']: {
    put: 'Update journey plan',
  },
  ['/api/journey-plannings']: {
    post: 'Create a journey plan',
  },
  ['/api/journey-plannings/cycle/:cycleId']: {
    post: 'Create a journey plan by a cycle (For testing purpose)',
  },
  // Files
  ['/api/files/upload/image']: {
    post: 'Upload an image',
  },
  ['/api/files/upload/images']: {
    post: 'Upload multiple images',
  },
  ['/api/files']: {
    delete: 'Delete given file',
  },
  ['/api/job/journey-plan/clone']: {
    post: 'Manual trigger cloning journey plan for next cycle',
  },
  // Brand
  ['/api/brand']: {
    get: 'Get all brands',
    post: 'Create a brand',
  },
  ['/api/brand/hint']: {
    post: 'Create or update hint',
  },
  // Message
  ['/api/message/send-push-notification']: {
    post: 'For testing push notification purpose',
  },
  ['/api/message/users/:userId/logs']: {
    get: 'Get notification logs by user id',
  },
  ['/api/message/users/:userId/logs/:logId/read']: {
    patch: 'Set a notification is read',
  },
  ['/api/message/push-notification/send']: {
    post: 'Send push notification',
  },
  ['/api/message/push-notification/list']: {
    post: 'Get list push notifications',
  },
  ['/api/message/push-notification/:pushNotificationId']: {
    get: 'Get push notification detail',
  },
  // Visit
  ['/v/:sessionId']: {
    get: 'Get session',
  },
  // Job
  ['/api/job/sale-rep/:saleRepId/manual-trigger/missed-visit-outlet']: {
    post: 'Manual trigger missed visit outlet',
  },
  ['/api/job']: {
    get: 'Get all jobs',
  },
  // Report
  ['/api/report/success-visit/list']: {
    post: 'Get list success visits',
  },
  ['/api/report/success-visit/export']: {
    post: 'Export list success visits',
  },
  ['/api/report/missed-visit/list']: {
    post: 'Get list missed visits',
  },
  ['/api/report/missed-visit/export']: {
    post: 'Export list missed visits',
  },
  ['/api/report/success-visit/:availabilitiesId/:visibilitiesId']: {
    get: 'Get availability and visibility',
  },
  ['/api/report/dsr/list']: {
    post: 'Get DSR report',
  },
  ['/api/report/dsr/export']: {
    post: 'Export DSR report',
  },
  // Admin
  ['/api/admin/add-roles']: {
    post: 'Add roles',
  },
  ['/api/admin/add-user']: {
    post: 'Add user',
  },
  ['/api/admin/add-user-role']: {
    post: 'Add user role',
  },
  ['/api/admin/remove-user-role']: {
    post: 'Remove user role',
  },
  ['/api/admin/update-user']: {
    post: 'Update user',
  },
  ['/api/admin/get-all-users']: {
    get: 'Get all users',
  },
  ['/api/admin/users/:userId']: {
    get: 'Get user by id',
  },
  ['/api/admin/list-outlets-distributor']: {
    get: 'Get outlets of current user',
  },
  ['/api/admin/list-outlets-distributor/export']: {
    get: 'Export outlets of current user',
  },
  ['/api/admin/outlets-master/:outletId']: {
    get: 'Get outlet master data by id',
  },
  ['/api/admin/outlets-master/update']: {
    post: 'Update outlet master data',
  },
  // OMS
  ['/api/check-stock/sync-data']: {
    post: 'Sync check-stock',
  },
  ['/api/orders/latest-order-oms']: {
    get: 'latest order oms',
  },
  ['/api/orders/list-products-oms']: {
    get: 'get list product oms',
  },
  ['/api/orders/list-promotions-oms']: {
    get: 'get list promotions oms',
  },
  ['/api/orders/list-orders-oms']: {
    get: 'get list orders oms',
  },
  ['/api/orders/oms-place-order']: {
    post: 'place order oms',
  },
  ['/api/offline-mode-v2/sync-offline-data']: {
    post: 'sync offline data',
  },
  ['/api/offline-mode-v2/get-offline-data']: {
    get: 'get offline data',
  },
};
