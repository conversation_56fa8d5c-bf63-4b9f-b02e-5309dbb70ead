import * as applicationinsights from 'applicationinsights';

export class AIService {
  private client: applicationinsights.TelemetryClient;
  private readonly minLogLevel: applicationinsights.Contracts.SeverityLevel;

  constructor() {
    this.minLogLevel = this.getLogLevelFromEnv();
    this.initializeAppInsights();
  }

  private getLogLevelFromEnv(): applicationinsights.Contracts.SeverityLevel {
    const configuredLevel = (process.env.APPINSIGHTS_LOG_LEVEL || 'info').toLowerCase();
    switch (configuredLevel) {
      case 'verbose':
        return applicationinsights.Contracts.SeverityLevel.Verbose;
      case 'info':
        return applicationinsights.Contracts.SeverityLevel.Information;
      case 'warning':
        return applicationinsights.Contracts.SeverityLevel.Warning;
      case 'error':
        return applicationinsights.Contracts.SeverityLevel.Error;
      case 'critical':
        return applicationinsights.Contracts.SeverityLevel.Critical;
      default:
        return applicationinsights.Contracts.SeverityLevel.Information;
    }
  }

  private shouldLog(level: applicationinsights.Contracts.SeverityLevel): boolean {
    return level >= this.minLogLevel;
  }

  private initializeAppInsights(): void {
    const appInsightConnectionString = process.env.APPINSIGHTS_CONNECTIONSTRING;
    if (!appInsightConnectionString) {
      console.warn('Application Insights connection string is not configured');
      return;
    }

    try {
      applicationinsights
        .setup(appInsightConnectionString)
        .setAutoDependencyCorrelation(true)
        .setAutoCollectRequests(true)
        .setAutoCollectPerformance(true, true)
        .setAutoCollectExceptions(true)
        .setAutoCollectDependencies(true)
        .setAutoCollectConsole(true)
        .setUseDiskRetryCaching(true)
        .setSendLiveMetrics(true)
        .setDistributedTracingMode(applicationinsights.DistributedTracingModes.AI_AND_W3C)
        .start();

      this.client = applicationinsights.defaultClient;
      console.log(`Application Insights initialized with log level: ${this.minLogLevel}`);
    } catch (error) {
      console.error('Failed to initialize Application Insights:', error);
    }
  }

  private checkClient(): boolean {
    if (!this.client) {
      console.warn('Application Insights client not initialized');
      return false;
    }
    return true;
  }

  // Trace methods with different severity levels
  logVerbose(message: string, properties: Record<string, any> = {}): void {
    if (!this.shouldLog(applicationinsights.Contracts.SeverityLevel.Verbose)) return;
    if (!this.checkClient()) return;

    this.client.trackTrace({
      message,
      severity: applicationinsights.Contracts.SeverityLevel.Verbose,
      properties,
    });
  }

  logInfo(message: string, properties: Record<string, any> = {}): void {
    if (!this.shouldLog(applicationinsights.Contracts.SeverityLevel.Information)) return;
    if (!this.checkClient()) return;

    this.client.trackTrace({
      message,
      severity: applicationinsights.Contracts.SeverityLevel.Information,
      properties,
    });
  }

  logWarning(message: string, properties: Record<string, any> = {}): void {
    if (!this.shouldLog(applicationinsights.Contracts.SeverityLevel.Warning)) return;
    if (!this.checkClient()) return;

    this.client.trackTrace({
      message,
      severity: applicationinsights.Contracts.SeverityLevel.Warning,
      properties,
    });
  }

  logError(message: string, properties: Record<string, any> = {}): void {
    if (!this.shouldLog(applicationinsights.Contracts.SeverityLevel.Error)) return;
    if (!this.checkClient()) return;

    this.client.trackTrace({
      message,
      severity: applicationinsights.Contracts.SeverityLevel.Error,
      properties,
    });
  }

  logCritical(message: string, properties: Record<string, any> = {}): void {
    if (!this.shouldLog(applicationinsights.Contracts.SeverityLevel.Critical)) return;
    if (!this.checkClient()) return;

    this.client.trackTrace({
      message,
      severity: applicationinsights.Contracts.SeverityLevel.Critical,
      properties,
    });
  }

  // Event tracking
  logEvent(name: string, properties: Record<string, any> = {}): void {
    if (!this.checkClient()) return;
    this.client.trackEvent({ name, properties });
  }

  // Exception tracking
  logException(exception: Error, properties: Record<string, any> = {}): void {
    if (!this.checkClient()) return;
    this.client.trackException({
      exception,
      properties,
    });
  }

  // Metric tracking
  logMetric(name: string, value: number, properties: Record<string, any> = {}): void {
    if (!this.checkClient()) return;
    this.client.trackMetric({
      name,
      value,
      properties,
    });
  }

  // Request tracking
  logRequest(name: string, url: string, duration: number, properties: Record<string, any> = {}): void {
    if (!this.checkClient()) return;
    this.client.trackRequest({
      name,
      url,
      duration,
      properties,
      resultCode: '200',
      success: true
    });
  }
}
