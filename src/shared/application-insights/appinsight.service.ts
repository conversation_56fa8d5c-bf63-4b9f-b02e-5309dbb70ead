import * as applicationinsights from 'applicationinsights';

export class AIService {
  constructor() {
    const appInsightConnectionString = process.env.APPINSIGHTS_CONNECTIONSTRING || null;
    if (appInsightConnectionString) {
      applicationinsights
        .setup(appInsightConnectionString)
        .setAutoDependencyCorrelation(true)
        .setAutoCollectRequests(true)
        .setAutoCollectPerformance(true, true)
        .setAutoCollectExceptions(true)
        .setAutoCollectDependencies(true)
        .setAutoCollectConsole(true)
        .setUseDiskRetryCaching(true)
        .setSendLiveMetrics(true)
        .setDistributedTracingMode(applicationinsights.DistributedTracingModes.AI_AND_W3C)
        .start();
    }
  }
}
