import { LoggerService } from '@nestjs/common';
import { printLog } from '../../utils';

export class UMenuLoggerService implements LoggerService {
  constructor /*@InjectRepository(Logs) //Need to add Repository for access to DB
    private readonly _outletsRepository: Repository<Logs>,*/() {}

  /**
   * Write a 'log' level log.
   */
  log(message: any, ...optionalParams: any[]) {
    //printLog('*log', message, JSON.stringify(optionalParams));
  }

  /**
   * Write an 'error' level log.
   */
  error(message: any, ...optionalParams: any[]) {
    //printLog('*error', message, JSON.stringify(optionalParams));
  }

  /**
   * Write a 'warn' level log.
   */
  warn(message: any, ...optionalParams: any[]) {
    //printLog('*warn', message, JSON.stringify(optionalParams));
  }

  /**
   * Write a 'debug' level log.
   */
  debug?(message: any, ...optionalParams: any[]) {
    //printLog('*debug', message, JSON.stringify(optionalParams));
  }

  /**
   * Write a 'verbose' level log.
   */
  verbose?(message: any, ...optionalParams: any[]) {
    printLog('*verbose', message, JSON.stringify(optionalParams));
  }

  startRequest?(message: any, ...optionalParams: any[]) {
    //.......................................................................
    //Save something to file or log db
    printLog('*startApp', message, JSON.stringify(optionalParams));
  }

  endRequest?(message: any, ...optionalParams: any[]) {
    //.......................................................................
    //Save something to file or log db
    printLog('*endRequest', message, JSON.stringify(optionalParams));
  }
}
