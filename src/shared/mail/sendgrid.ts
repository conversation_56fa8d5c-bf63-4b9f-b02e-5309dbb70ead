import { printLog } from 'src/utils';
import * as fs from 'fs';
import * as path from 'path';
import * as MailService from '@sendgrid/mail';

const TOKEN = process.env.SENDGRID_API_KEY || '';
const sender = process.env.SENDGRID_SENDER_EMAIL;
const systemAdmin = ['<EMAIL>'];
MailService.setApiKey(TOKEN);

export class SendGridService {
  sendTemplateMail(to: string, subject: string, templateData: any, templateName: string) {
    const publicPath = path.join(process.cwd(), 'public');
    const templatePath = path.join(`${publicPath}/templates/${templateName}.html`);

    if (!fs.existsSync(templatePath)) {
      return null;
    }

    let htmlContent = fs.readFileSync(templatePath, 'utf8');

    // Replace placeholders in the template with actual values
    printLog('templateData', templateData);
    for (const [key, value] of Object.entries(templateData)) {
      htmlContent = htmlContent.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    }

    MailService.send({
      from: sender,
      to,
      subject,
      html: htmlContent,
    }).then(
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      () => {
        printLog('Email sent successfully');
      },
      (error) => {
        console.error(error);

        if (error.response) {
          printLog('sendMail Error=======', error.response.body);
        }
      },
    );
  }

  sendMail(to: string, subject: string, html: string) {
    MailService.send({
      from: sender,
      to,
      subject,
      html,
    }).then(
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      () => {},
      (error) => {
        console.error(error);

        if (error.response) {
          //printLog('sendMail Error=======', error.response.body);
        }
      },
    );
  }

  sendMailToAdmin(subject: string, html: string) {
    MailService.send({
      from: sender,
      to: systemAdmin,
      subject,
      html,
    }).then(
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      () => {},
      (error) => {
        console.error(error);

        if (error.response) {
          printLog('sendMailToAdmin Error=======', error.response.body);
        }
      },
    );
  }
}
