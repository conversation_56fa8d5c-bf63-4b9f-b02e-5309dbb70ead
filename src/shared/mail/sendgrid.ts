import { printLog } from 'src/utils';
import * as MailService from '@sendgrid/mail';

const TOKEN = process.env.SENDGRID_API_KEY || '';
const sender = process.env.SENDGRID_SENDER_EMAIL || '<EMAIL>';
const systemAdmin = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
MailService.setApiKey(TOKEN);

export class SendGridService {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  constructor() {}
  sendMail(to: string, subject: string, html: string) {
    MailService.send({
      from: sender,
      to,
      subject,
      html,
    }).then(
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      () => {},
      (error) => {
        console.error(error);

        if (error.response) {
          //printLog('sendMail Error=======', error.response.body);
        }
      },
    );
  }
  sendMailToAdmin(subject: string, html: string) {
    MailService.send({
      from: sender,
      to: systemAdmin,
      subject,
      html,
    }).then(
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      () => {},
      (error) => {
        console.error(error);

        if (error.response) {
          printLog('sendMailToAdmin Error=======', error.response.body);
        }
      },
    );
  }
}
