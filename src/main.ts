import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';
import { ServiceAccount } from 'firebase-admin';

import { AppModule } from './app.module';
import { isProductionEnv } from './utils';
import { VERSION_NEUTRAL, VersioningType } from '@nestjs/common';
import { SolaceService } from './third-parties/services/solace.service';
import { BE_VERSION } from './config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  app.enableVersioning({
    type: VersioningType.HEADER,
    defaultVersion: VERSION_NEUTRAL,
    header: 'version',
  });

  const configService: ConfigService = app.get(ConfigService);
  // Set the config options
  const adminConfig: ServiceAccount = {
    projectId: configService.get<string>('FIREBASE_PROJECT_ID'),
    privateKey: configService.get<string>('FIREBASE_PRIVATE_KEY')?.replace(/\\n/g, '\n'),
    clientEmail: configService.get<string>('FIREBASE_CLIENT_EMAIL'),
  };
  // Initialize the firebase admin app
  admin.initializeApp({
    credential: admin.credential.cert(adminConfig),
  });

  const solaceService: SolaceService = app.get(SolaceService);
  if (configService.get<string>('SOLACE_OUTLET_QUEUE_NAME') && configService.get<string>('SOLACE_SALES_REP_QUEUE_NAME')) {
    solaceService.consumeMessage({ queueName: configService.get<string>('SOLACE_OUTLET_QUEUE_NAME') });
    solaceService.consumeMessage({ queueName: configService.get<string>('SOLACE_SALES_REP_QUEUE_NAME') });
  }
  // solaceService.subscribeTopic({ topic: configService.get<string>('SOLACE_TOPIC_NAME') });
  // solaceService.subscribeTopic({ topic: configService.get<string>('SOLACE_SALES_REP_QUEUE_NAME') });

  if (!isProductionEnv()) {
    const config = new DocumentBuilder()
      .setTitle('DSR')
      .setDescription('DSR API description')
      .setVersion(BE_VERSION || '1.0')
      .addBearerAuth()
      .build();
    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('swagger', app, document);
  }
  app.enableCors();

  const server = await app.listen(process.env.PORT, '0.0.0.0', () => {
    console.log('\x1b[33m%s\x1b[0m', `Server :: Running @ 'http://localhost:${process.env.PORT}'`);
    console.log('\x1b[33m%s\x1b[0m', `Swagger :: Running @ 'http://localhost:${process.env.PORT}/swagger'`);
  });
  server.setTimeout(Number(process.env.APP_TIME_OUT) * 2);
}
bootstrap();
