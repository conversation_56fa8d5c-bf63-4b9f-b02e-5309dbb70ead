import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, MinLength } from 'class-validator';

export class OtpLoginDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhone: string;

  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhoneCode: string;

  @ApiModelProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  isQuickLogin?: boolean;
}

export class PasswordAndOtpLoginDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhone: string;

  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhoneCode: string;

  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @MinLength(12)
  password: string;
}

export class OtpAdminLoginDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;
}

export class OtpVerificationDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhone: string;

  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhoneCode: string;

  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  smsCode: string;

  @ApiModelProperty({ required: true, default: false })
  isRemember: boolean;
}

export class EmailLoginDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiModelProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  isQuickLogin?: boolean;
}

export class EmailPasswordLoginDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @MinLength(12)
  password: string;
}

export class AdminForgotPasswordDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;
}

export class VerifyForgotPasswordOTPDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  otp: string;
}

export class EmailVerificationDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  smsCode: string;

  @ApiModelProperty({ required: true, default: false })
  isRemember: boolean;
}

export class CreatePasswordDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  sessionId: string;

  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @MinLength(12)
  newPassword: string;
}

export class CheckLinkCreatePasswordDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  sessionId: string;
}
