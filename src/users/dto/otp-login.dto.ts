import { IsBoolean, <PERSON><PERSON><PERSON>Empty, <PERSON><PERSON><PERSON>al, IsString, MinLength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class OtpLoginDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhone: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhoneCode: string;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  isQuickLogin?: boolean;
}

export class PasswordAndOtpLoginDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhone: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhoneCode: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @MinLength(12)
  password: string;
}

export class OtpAdminLoginDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;
}

export class OtpVerificationDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhone: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  mobilePhoneCode: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  smsCode: string;

  @ApiProperty({ required: true, default: false })
  isRemember: boolean;
}

export class EmailLoginDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  isQuickLogin?: boolean;
}

export class EmailPasswordLoginDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @MinLength(12)
  password: string;
}

export class AdminForgotPasswordDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;
}

export class VerifyForgotPasswordOTPDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  otp: string;
}

export class EmailVerificationDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  smsCode: string;

  @ApiProperty({ required: true, default: false })
  isRemember: boolean;
}

export class CreatePasswordDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  sessionId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @MinLength(12)
  newPassword: string;
}

export class CheckLinkCreatePasswordDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  sessionId: string;
}
