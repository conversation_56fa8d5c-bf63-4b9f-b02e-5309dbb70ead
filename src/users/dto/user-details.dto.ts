import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';

export class UserDetailsDto {
  @ApiModelProperty({ required: false })
  @IsString()
  userId?: string;

  @ApiModelProperty({ required: false })
  @IsString()
  @IsOptional()
  email?: string;

  @ApiModelProperty({ required: false })
  @IsString()
  @IsOptional()
  mobilePhone?: string;

  @ApiModelProperty({ required: false })
  @IsBoolean()
  isUserAdmin?: boolean;
}
