import { IsBoolean, IsEmail, IsNotEmpty, IsNumber, IsO<PERSON>al, IsString, IsUrl } from 'class-validator';

export class CreateUserDto {
  @IsNotEmpty()
  @IsString()
  mobilePhone: string;

  @IsNotEmpty()
  @IsString()
  username: string;

  @IsOptional()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsString()
  password: string;

  @IsOptional()
  @IsString()
  firstname: string;

  @IsOptional()
  @IsString()
  lastname: string;

  @IsOptional()
  @IsNumber()
  roleId: string;

  @IsNotEmpty()
  @IsNumber()
  status: string;

  @IsNotEmpty()
  @IsBoolean()
  isActive: string;
}
