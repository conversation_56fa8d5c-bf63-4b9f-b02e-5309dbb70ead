import { PaginationMongooseParams } from '../../shared/common-params/pagination-mongoose.params';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SearchUserActionsDto extends PaginationMongooseParams {
  @ApiPropertyOptional()
  search?: string;

  @ApiProperty({ required: false })
  key?: string;

  @ApiProperty({ required: false })
  userId?: string;

  @ApiProperty({ required: false })
  startTime?: Date;

  @ApiProperty({ required: false, default: new Date().toISOString() })
  endTime?: Date;

  @ApiProperty({ required: false })
  feature?: string;

  @ApiProperty({ required: false, default: 0 })
  skip?: number;

  @ApiProperty({ required: false, default: 100 })
  limit?: number;
}
