import { ApiModelProperty, ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { PaginationMongooseParams } from '../../shared/common-params/pagination-mongoose.params';

export class SearchUserActionsDto extends PaginationMongooseParams {
  @ApiModelPropertyOptional()
  search?: string;

  @ApiModelProperty({ required: false })
  key?: string;

  @ApiModelProperty({ required: false })
  userId?: string;

  @ApiModelProperty({ required: false })
  startTime?: Date;

  @ApiModelProperty({ required: false, default: new Date().toISOString() })
  endTime?: Date;

  @ApiModelProperty({ required: false })
  feature?: string;

  @ApiModelProperty({ required: false, default: 0 })
  skip?: number;

  @ApiModelProperty({ required: false, default: 100 })
  limit?: number;
}
