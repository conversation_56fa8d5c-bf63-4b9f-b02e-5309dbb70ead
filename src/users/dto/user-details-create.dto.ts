import { IsArray, IsBoolean, Is<PERSON>ptional, IsString, ValidateNested } from 'class-validator';
import { UserActive, UserDevice, UserRole, UserToken } from '../type/sales-rep';
import { ApiProperty } from '@nestjs/swagger';

export class UserDetailsCreateDto {
  @ApiProperty({ required: false })
  userId: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  email: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  mobilePhone: string;

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  userToken: UserToken[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  userActive: UserActive[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  userDevice: UserDevice[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  userRole: UserRole[];

  @ApiProperty({ required: false })
  @IsBoolean()
  isUserAdmin: boolean;
}
