import { IsArray, IsBoolean, IsOptional, IsString, ValidateNested } from 'class-validator';
import { UserActive, UserDevice, UserRole, UserToken } from '../type/sales-rep';
import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';

export class UserDetailsCreateDto {
  @ApiModelProperty({ required: false })
  userId: string;

  @ApiModelProperty({ required: false })
  @IsString()
  @IsOptional()
  email: string;

  @ApiModelProperty({ required: false })
  @IsString()
  @IsOptional()
  mobilePhone: string;

  @ApiModelProperty({ required: false })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  userToken: UserToken[];

  @ApiModelProperty({ required: false })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  userActive: UserActive[];

  @ApiModelProperty({ required: false })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  userDevice: UserDevice[];

  @ApiModelProperty({ required: false })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  userRole: UserRole[];

  @ApiModelProperty({ required: false })
  @IsBoolean()
  isUserAdmin: boolean;
}
