import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class RefreshTokenDto {
  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @IsOptional()
  currentToken: string;

  @ApiModelProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  currentRefreshToken: string;

  @ApiModelProperty({ required: false })
  @IsOptional()
  isRemember: boolean;
}
