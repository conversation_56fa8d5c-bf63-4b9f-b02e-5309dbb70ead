import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RefreshTokenDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @IsOptional()
  currentToken: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  currentRefreshToken: string;

  @ApiProperty({ required: false })
  @IsOptional()
  isRemember: boolean;
}
