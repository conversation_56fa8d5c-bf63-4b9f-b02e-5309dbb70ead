import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from '../../shared/schemas/base.schema';
import { Document } from 'mongoose';

export type UserDocument = User & Document;

@Schema()
export class User extends BaseSchema {
  @Prop({
    required: true,
    index: true,
    uppercase: true,
    unique: true,
    trim: true,
  })
  mobilePhone: string;

  @Prop({ required: false })
  password: string;

  @Prop({
    required: false,
    lowercase: true,
  })
  email: string;

  @Prop({ required: true })
  username: string;

  @Prop({ required: false })
  firstname: string;

  @Prop({ required: false })
  lastname: string;

  @Prop({ required: false })
  roleId: number;

  @Prop({ required: false })
  lastLoggedIn?: Date;

  @Prop({ required: false, default: false })
  isActive: boolean;
}
export const UserSchema = SchemaFactory.createForClass(User);

// Hooks
UserSchema.pre<UserDocument>('save', function (next) {
  this.email = this.email.toLowerCase();
  next();
});
