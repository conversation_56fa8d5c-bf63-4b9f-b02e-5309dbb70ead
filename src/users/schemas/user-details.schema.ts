import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { UserActive, UserDevice, UserRole, UserToken } from '../type/sales-rep';

export type UserDetailsDocument = UserDetails & Document;

@Schema()
export class UserDetails extends BaseSchema {
  @Prop({
    required: true,
    index: true,
  })
  userId: string;

  @Prop({
    required: false,
    index: true,
    trim: true,
  })
  email?: string;

  @Prop({
    required: false,
    index: true,
    trim: true,
  })
  mobilePhone?: string;

  @Prop({
    required: true,
    index: true,
  })
  userToken?: UserToken[];

  @Prop({
    required: true,
    index: true,
  })
  userActive?: UserActive[];

  @Prop({
    required: false,
    index: true,
  })
  userDevice?: UserDevice[];

  @Prop({
    required: false,
    index: true,
  })
  userRole?: UserRole[];

  @Prop({ required: false, default: false, index: true })
  isUserAdmin?: boolean;
}

export const UserDetailsSchema = SchemaFactory.createForClass(UserDetails);
