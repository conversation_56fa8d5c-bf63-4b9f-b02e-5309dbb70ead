import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Exclude } from 'class-transformer';
import { Document } from 'mongoose';
import { BaseSchema } from '../../shared/schemas/base.schema';
import { SalesRepStatus } from '../enums';
import { OtpRuleSecures } from '../type/user';

export type UserDocument = User & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
})
export class User extends BaseSchema {
  @Prop({
    required: true,
    default: null,
    index: true,
  })
  mobilePhone: string;

  @Prop({
    required: false,
    default: process.env.PHONE_COUNTRY_CODE_DEFAULT || '+60',
    index: true,
  })
  mobilePhoneCode: string;

  @Exclude()
  @Prop({ required: false, default: null, index: true })
  password?: string;

  @Exclude()
  @Prop({ required: false, default: null, index: true })
  lastUpdatedPassword?: Date;

  @Prop({
    required: false,
    default: null,
    index: true,
  })
  email?: string;

  @Prop({ required: true, index: true })
  username: string;

  @Prop({ required: false, index: true })
  firstname: string;

  @Prop({ required: false, index: true })
  lastname: string;

  @Prop({ required: false })
  roleId: string[];

  @Prop({ required: false, index: true })
  lastLoggedIn?: Date;

  @Prop({ required: false, default: 0, index: true })
  status?: number;

  @Prop({ required: false, default: true, index: true })
  isActive?: boolean;

  @Prop({ required: false, index: true })
  twoFactorEnabled: string;

  @Prop({ required: false, index: true })
  userType: string;

  @Prop({ required: false })
  contactId: string;

  @Prop({ required: false, index: true })
  storeId: string;

  @Prop({ required: false, index: true })
  defaultLanguage: string;

  @Prop({ required: false, default: true, index: true })
  isSmsSend?: boolean;

  @Prop({ default: null, index: true })
  saleRepId: string;

  @Prop({ enum: SalesRepStatus, default: SalesRepStatus.ACTIVE, index: true })
  saleRepStatus: string;

  @Prop({ required: false, default: false, index: true })
  isTestAccount?: boolean;

  @Prop({ required: false, default: false, index: true })
  isAddressChanged?: boolean;

  @Prop({ required: false, default: null, index: true })
  geoAddress: string;

  @Prop({ index: true, default: null })
  depotId: string;

  @Prop({
    type: {
      count: { type: Number, default: 0 },
      lockedEndTime: { type: Date, default: null },
    },
    default: { count: 0, lockedEndTime: null },
  })
  otpRuleSecures: OtpRuleSecures;
}

export const UserSchema = SchemaFactory.createForClass(User);

// Hooks
UserSchema.pre<UserDocument>('save', function (next) {
  this.email = this.email?.trim()?.toLowerCase();
  this.saleRepId = this.saleRepId?.trim();
  this.contactId = this.saleRepId?.trim();
  this.mobilePhone = this.mobilePhone?.trim();
  next();
});
