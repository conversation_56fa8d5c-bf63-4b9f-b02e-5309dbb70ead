import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Exclude } from 'class-transformer';
import { Document } from 'mongoose';
import { BaseSchema } from '../../shared/schemas/base.schema';

export type UserAdminDocument = UserAdmin & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
})
export class UserAdmin extends BaseSchema {
  @Prop({
    required: true,
    default: null,
    index: true,
  })
  mobilePhone: string;

  @Prop({
    required: false,
    default: process.env.PHONE_COUNTRY_CODE_DEFAULT || '+60',
    index: true,
  })
  mobilePhoneCode: string;

  @Exclude()
  @Prop({ default: null, index: true })
  password?: string;

  @Exclude()
  @Prop({ required: false, default: null, index: true })
  lastUpdatedPassword?: Date;

  @Prop({
    required: false,
    lowercase: true,
    default: null,
    index: true,
  })
  email?: string;

  @Prop({ required: true, index: true })
  username: string;

  @Prop({ required: false, index: true })
  firstname: string;

  @Prop({ required: false, index: true })
  lastname: string;

  @Prop({ required: false })
  roleId: string[];

  @Prop({ required: false })
  lastLoggedIn?: Date;

  @Prop({ required: false, default: 0 })
  status?: number;

  @Prop({ required: false, default: true, index: true })
  isActive?: boolean;

  @Prop({ required: false, index: true })
  twoFactorEnabled: string;

  @Prop({ required: false, index: true })
  userType: string;

  @Prop({ required: false })
  contactId: string;

  @Prop({ required: false, index: true })
  storeId: string;

  @Prop({ required: false, index: true })
  defaultLanguage: string;

  @Prop({ required: false, default: true, index: true })
  isSmsSend?: boolean;

  @Prop({ index: true, default: null })
  depotId: string;
}

export const UserAdminSchema = SchemaFactory.createForClass(UserAdmin);

// Hooks
UserAdminSchema.pre<UserAdminDocument>('save', function (next) {
  this.email = this.email?.toLowerCase();
  next();
});
