import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';
import { Document } from 'mongoose';
import { BaseSchema } from '../../shared/schemas/base.schema';

export type UserActionsDocument = UserActions & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
})
export class UserActions extends BaseSchema {
  @Prop({ index: true })
  key: string;

  @Prop({ index: true })
  feature: string;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  request: any;

  @Prop({ index: true })
  method: string;

  @Prop({ index: true })
  userId: string;

  @Prop({ default: false, index: true })
  isUserAdmin: boolean;
}

export const UserActionsSchema = SchemaFactory.createForClass(UserActions);
