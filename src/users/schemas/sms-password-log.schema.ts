import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from '../../shared/schemas/base.schema';
import { Document, ObjectId, Types } from 'mongoose';
import { User } from './user.schema';
import { UserAdmin } from './user-admin.schema';

export type SmsPasswordLogDocument = SmsPasswordLog & Document;

@Schema()
export class SmsPasswordLog extends BaseSchema {
  @Prop({
    required: true,
    trim: true,
  })
  mobilePhone: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: User.name,
  })
  user: User;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: UserAdmin.name,
  })
  userAdmin: UserAdmin;

  @Prop({
    required: true,
    trim: true,
  })
  sessionId: string;

  @Prop({
    required: false,
    trim: true,
  })
  link: string;

  @Prop({
    required: false,
    trim: true,
  })
  message: string;

  @Prop({
    required: false,
    trim: true,
  })
  errorMessage: string;

  @Prop({
    required: false,
    default: false,
  })
  sent: boolean;

  @Prop({ required: false, default: false })
  isActive: boolean;
}
export const SmsPasswordLogSchema = SchemaFactory.createForClass(SmsPasswordLog);
