export type Role = {
  roleKey: string;
  roleName: string;
  isActive: boolean;
};

export type UserToken = {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: string;
  accessTokenDot?: string;
  refreshTokenDot?: string;
  tokenTypeDot?: string;
  expiresInDot?: string;
  omsToken?: string;
  createdAt?: Date;
};

export type UserActive = {
  status: number;
  smsCode: string;
  activeLink?: string;
  codeExpiredSecond?: number;
  createdAt?: Date;
};

export type UserRole = {
  roleKey: string;
};

export type UserDevice = {
  fcmToken: string;
  createdAt?: Date;
};
