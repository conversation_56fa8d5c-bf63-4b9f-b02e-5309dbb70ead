import { OrdersModule } from '../orders/orders.module';
import { forwardRef, MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './services/users.service';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from './schemas/user.schema';
import { ExternalModule } from '../external/external.module';
import { AuthModule } from '../auth/auth.module';
import { CurrentUserMiddleware } from './middlewares/current-user.middleware';
import { OutletsModule } from '../outlets/outlets.module';
import { SaleRepModule } from '../sale-rep/sale-rep.module';
import { SettingsModule } from '../settings/settings.module';
import { UserAdmin, UserAdminSchema } from './schemas/user-admin.schema';
import { UserAdminsService } from './services/user-admins.service';
import { DistributorModule } from '../distributor/distributor.module';
import { SmsPasswordLogService } from './services/sms-password-log.service';
import { SmsPasswordLog, SmsPasswordLogSchema } from './schemas/sms-password-log.schema';
import { UserActions, UserActionsSchema } from './schemas/user-action.schema';
import { UserActionsService } from './services/user-actions.service';
import { ThirdPartiesModule } from 'src/third-parties/third-parties.module';
import { UserDetails, UserDetailsSchema } from './schemas/user-details.schema';
import { UserDetailService } from './services/user-detail.service';
import { UserDevicesService } from './services/user-devices.service';
import { JwtModule } from '@nestjs/jwt';
import { UserTokensService } from './services/user-tokens.service';
import { MasterDataModule } from '../master-data/master-data.module';
import { DsrTargetsModule } from '../dsr-targets/dsr-targets.module';

@Module({
  controllers: [UsersController],
  providers: [UsersService, UserAdminsService, SmsPasswordLogService, UserActionsService, UserDetailService, UserDevicesService, UserTokensService],
  exports: [UsersService, UserAdminsService, SmsPasswordLogService, UserActionsService, UserDetailService, UserTokensService],
  imports: [
    MongooseModule.forFeature([
      {
        name: User.name,
        schema: UserSchema,
      },
      {
        name: UserAdmin.name,
        schema: UserAdminSchema,
      },
      {
        name: SmsPasswordLog.name,
        schema: SmsPasswordLogSchema,
      },
      {
        name: UserActions.name,
        schema: UserActionsSchema,
      },
      {
        name: UserDetails.name,
        schema: UserDetailsSchema,
      },
    ]),

    // AuthModule,
    ExternalModule,
    forwardRef(() => AuthModule),
    OutletsModule,
    SaleRepModule,
    SettingsModule,
    forwardRef(() => OrdersModule),
    DistributorModule,
    ThirdPartiesModule,
    JwtModule,
    forwardRef(() => MasterDataModule),
    DsrTargetsModule,
  ],
})
export class UsersModule implements NestModule {
  configure(user: MiddlewareConsumer) {
    user
      .apply(CurrentUserMiddleware)
      .exclude(
        'api/users/login-email',
        'api/users/login-email-verify-otp',
        'api/users/login-otp',
        'api/users/login-verify-otp',
        'api/users/login',
        'api/auth/verify-token',
        'api/users/get-otp',
        'api/dsr/chart-reporting/(.*)',
      )
      .forRoutes('*');
  }
}
