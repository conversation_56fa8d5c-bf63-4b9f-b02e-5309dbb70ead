import { CACHE_MANAGER, Inject, Injectable, NestMiddleware } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { NextFunction, Request, Response } from 'express';
import { AuthService } from '../../auth/auth.service';
import { makeCurrentUserTokenCacheKey } from '../../utils';

@Injectable()
export class CurrentUserMiddleware implements NestMiddleware {
  constructor(private _authService: AuthService, @Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      const token = req?.headers?.authorization?.split(' ')[1];
      const cacheKey = makeCurrentUserTokenCacheKey(token, process.env.NODE_ENV);
      let user: any = await this.cacheManager.get(cacheKey);
      if (!user?._id || !user?.roles) {
        user = await this._authService.getUserAuth(token);
        await this.cacheManager.set(cacheKey, user);
      }

      if (user) {
        req.currentUser = user;
        await this.cacheManager.set(cacheKey, user);
      }
    } catch (e) {}
    next();
  }
}
