import { forwardRef, Inject, Injectable, NestMiddleware } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { NextFunction, Request, Response } from 'express';
import { AuthService } from '../../auth/auth.service';
import { makeCurrentUserTokenCacheKey, printLog } from '../../utils';
import { BusinessPartnersContactService } from 'src/master-data/services/business-partners-contact.service';
import { ConstantRoles } from '../../utils/constants/role';

@Injectable()
export class CurrentUserMiddleware implements NestMiddleware {
  constructor(
    private _authService: AuthService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @Inject(forwardRef(() => BusinessPartnersContactService))
    readonly _businessPartnerContactService: BusinessPartnersContactService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      const token = req?.headers?.authorization?.split(' ')[1];
      const cacheKey = makeCurrentUserTokenCacheKey(token, process.env.NODE_ENV);
      let user: any = await this.cacheManager.get(cacheKey);
      if (!user?._id || !user?.roles) {
        user = await this._authService.getUserAuth(token);
        await this.cacheManager.set(cacheKey, user);
      }

      const skipBusinessPartnerRelationPaths = ['/api/rep-manager/asm/depots-saleReps', '/api/rep-manager/asm/sale-reps/performance'];

      if (
        !skipBusinessPartnerRelationPaths.includes(req.baseUrl) &&
        (user?.roles || []).filter((role) => [ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER].includes(role)).length >
          0
      ) {
        user.businessPartnerRelations = await this._businessPartnerContactService.queryContactRelationIds(
          user?.saleRepId ? user?.saleRepId : user?.contactId ? user?.contactId : '',
        );
      }

      printLog(user);

      if (user) {
        req.currentUser = user;
        await this.cacheManager.set(cacheKey, user);
      }
    } catch (e) {}
    next();
  }
}
