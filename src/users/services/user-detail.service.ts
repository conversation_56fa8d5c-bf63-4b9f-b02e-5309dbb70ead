import { forwardRef, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BaseService } from '../../shared/services/base-service';
import { UserDetails, UserDetailsDocument } from '../schemas/user-details.schema';
import { UserDetailsUpdateDto } from '../dto/user-details-update.dto';
import { UserDetailsCreateDto } from '../dto/user-details-create.dto';
import { UserDetailsDto } from '../dto/user-details.dto';
import { UserActive, UserDevice, UserRole, UserToken } from '../type/sales-rep';
import { UserAdminsService } from './user-admins.service';
import { UsersService } from './users.service';
import { isEmptyObjectOrArray } from '../../utils';

@Injectable()
export class UserDetailService extends BaseService<UserDetails> {
  constructor(
    @InjectModel(UserDetails.name) protected readonly modelUserDetail: Model<UserDetailsDocument>, // @Inject(forwardRef(() => UsersService))
    @Inject(forwardRef(() => UsersService))
    private readonly _usersService: UsersService,
    @Inject(forwardRef(() => UserAdminsService))
    private readonly _userAdminsService: UserAdminsService,
  ) {
    super();
    this.model = modelUserDetail;
  }

  async findOneData(userDetailsDto: UserDetailsDto): Promise<UserDetails> {
    return this.model.findOne(userDetailsDto);
  }

  async findUserDetailsByUserIds(userIds: string[]): Promise<UserDetails[]> {
    return this.model.find({ userId: { $in: userIds } }).exec();
  }

  async findUserRoles(userDetailsDto: UserDetailsDto): Promise<UserRole[]> {
    let roles = (await this.findOneData(userDetailsDto))?.userRole || [];
    if (isEmptyObjectOrArray(roles)) {
      let user = null;
      if (userDetailsDto.isUserAdmin) {
        user = await this._userAdminsService.findOne({ _id: new Types.ObjectId(userDetailsDto.userId) });
      } else {
        user = await this._usersService.findOne({ _id: new Types.ObjectId(userDetailsDto.userId) });
      }
      roles = user?.roleId.map((r) => ({ roleKey: r }));
    }
    return roles;
  }

  async findUserTokens(userDetailsDto: UserDetailsDto): Promise<UserToken[]> {
    return (await this.findOneData(userDetailsDto))?.userToken || [];
  }

  async findUserActives(userDetailsDto: UserDetailsDto): Promise<UserActive[]> {
    return (await this.findOneData(userDetailsDto))?.userActive || [];
  }

  async findUserDevices(userDetailsDto: UserDetailsDto): Promise<UserDevice[]> {
    return (await this.findOneData(userDetailsDto))?.userDevice || [];
  }

  async findUserDeviceByFcmToken(fcmToken: string): Promise<UserDetails> {
    return this.model.findOne({ 'userDevice.fcmToken': fcmToken });
  }

  async findAllUserDeviceByFcmToken(fcmTokens: string[]): Promise<UserDetails[]> {
    return this.model.find({ 'userDevice.fcmToken': { $in: fcmTokens } });
  }

  async findUserTokenByAccessToken(accessToken: string): Promise<UserDetails> {
    return this.model.findOne({ 'userToken.accessToken': accessToken });
  }

  async findSalRepUsers(roleKey: string): Promise<UserDetails[]> {
    return this.model.find({ 'userRole.roleKey': roleKey }).exec();
  }

  async create(createUserDetailsDto: UserDetailsCreateDto): Promise<UserDetails> {
    const createdUserDetails = new this.model(createUserDetailsDto);
    return createdUserDetails.save();
  }

  async update(id: string, updateUserDetailsDto: UserDetailsUpdateDto): Promise<UserDetails> {
    const updatedUserDetails = await this.model.findByIdAndUpdate(id, updateUserDetailsDto, { new: true }).exec();
    if (!updatedUserDetails) {
      throw new NotFoundException(`UserDetails with id ${id} not found`);
    }
    return updatedUserDetails;
  }

  async remove(id: string): Promise<UserDetails> {
    const removedUserDetails = await this.model.findByIdAndRemove(id).exec();
    if (!removedUserDetails) {
      throw new NotFoundException(`UserDetails with id ${id} not found`);
    }
    return removedUserDetails;
  }

  async addOrUpdate(updateUserDetailsDto: UserDetailsUpdateDto) {
    if (!updateUserDetailsDto || !updateUserDetailsDto.userId) {
      throw new NotFoundException(`User data is required`);
    }
    const diff = {} as any;
    if (updateUserDetailsDto.mobilePhone) {
      diff.mobilePhone = updateUserDetailsDto.mobilePhone;
    }
    if (updateUserDetailsDto.userRole) {
      diff.userRole = updateUserDetailsDto.userRole;
    }
    if (updateUserDetailsDto.userDevice) {
      diff.userDevice = updateUserDetailsDto.userDevice;
    }
    if (updateUserDetailsDto.userToken) {
      diff.userToken = updateUserDetailsDto.userToken;
    }
    if (updateUserDetailsDto.userActive) {
      diff.userActive = updateUserDetailsDto.userActive;
    }
    diff.updatedAt = new Date();
    const res = await this.model.findOneAndUpdate({ userId: updateUserDetailsDto.userId, isUserAdmin: updateUserDetailsDto.isUserAdmin }, diff, {
      upsert: true,
      returnOriginal: false,
      returnDocument: 'after',
    });
    return res;
  }

  async deleteAllUserDeviceByFcmToken(fcmTokens: string[]): Promise<any> {
    return this.model.deleteMany({ 'userDevice.fcmToken': { $in: fcmTokens } });
  }
}
