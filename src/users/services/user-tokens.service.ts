import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService, JwtSignOptions } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { printLog } from 'src/utils';
import { UserDetails, UserDetailsDocument } from '../schemas/user-details.schema';
import { FilterQuery, Model, Types } from 'mongoose';
import { I18nContext } from 'nestjs-i18n';

@Injectable()
export class UserTokensService {
  constructor(
    @InjectModel(UserDetails.name) protected readonly userDetailModel: Model<UserDetailsDocument>,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {}

  async refreshToken({ refreshToken, i18n }: { refreshToken: string; i18n: I18nContext }) {
    const isValidToken = this.verifyToken(refreshToken);
    if (!isValidToken) {
      printLog('Invalid refresh token');
      throw new BadRequestException(
        i18n.translate(`common.not_found`, {
          args: { fieldName: 'Refresh token' },
        }),
      );
    }

    const tokenPayload = this.decryptToken(refreshToken);
    const userQueryCondition: FilterQuery<UserDetailsDocument> = {};
    if (tokenPayload.userId) {
      userQueryCondition.userId = String(tokenPayload.userId);
    } else {
      userQueryCondition.mobilePhone = tokenPayload.mobilePhone;
    }

    const userDetails = await this.userDetailModel.findOne(userQueryCondition);
    if (!userDetails) {
      printLog('User details not found');
      throw new BadRequestException(
        i18n.translate(`common.not_found`, {
          args: { fieldName: 'User details' },
        }),
      );
    }
    const currentUserToken = userDetails.userToken.find((userToken) => userToken.refreshToken === refreshToken);
    if (currentUserToken) {
      printLog('Current user token not found');
      throw new BadRequestException(
        i18n.translate(`common.not_found`, {
          args: { fieldName: 'Refresh token' },
        }),
      );
    }

    const pairTokens = this.generatePairTokens({ userId: userDetails.userId, mobilePhone: userDetails.mobilePhone });
    const updatedUserDetails = await this.userDetailModel.findByIdAndUpdate(userDetails._id, {
      $set: {
        userToken: [
          {
            ...currentUserToken,
            ...pairTokens,
          },
          ...userDetails.userToken.filter((userToken) => userToken.refreshToken !== refreshToken),
        ],
      },
    });

    return this.transformTokenData({ userDetails: updatedUserDetails, ...pairTokens });
  }

  transformTokenData({ userDetails, accessToken, refreshToken }: { userDetails: UserDetails; accessToken: string; refreshToken: string }) {
    return {
      accessToken,
      refreshToken,
      _id: userDetails._id,
      createdAt: userDetails.createdAt,
      updatedAt: userDetails.updatedAt,
      userId: userDetails.userId,
      mobilePhone: userDetails.mobilePhone,
      isUserAdmin: userDetails.isUserAdmin,
    };
  }

  generatePairTokens(payload: Record<string, any>) {
    const accessToken = this.generateToken({
      payload,
      options: {
        secret: process.env.JWT_PRIVATE_KEY,
        expiresIn: this.configService.get('JWT_EXPIRED_TIME') || '2h',
        notBefore: 0,
      },
    });

    const refreshToken = this.generateToken({
      payload: {
        ...payload,
        accessToken,
      },
      options: {
        secret: process.env.JWT_PRIVATE_KEY,
        expiresIn: this.configService.get('JWT_REFRESH_TOKEN_EXPIRED_TIME') || '1y',
        notBefore: 0,
      },
    });

    return {
      accessToken,
      refreshToken,
    };
  }

  private verifyToken(token: string) {
    try {
      this.jwtService.verify(token, {
        secret: this.configService.get('JWT_PRIVATE_KEY'),
      });
      return true;
    } catch (error) {
      printLog('Error when verifying token', error);
      return false;
    }
  }

  private decryptToken(token: string) {
    return this.jwtService.decode(token) as {
      mobilePhone: string;
      userId?: string; // Old token has no userId in token payload.
    };
  }

  private generateToken({ payload, options }: { payload: Record<string, any>; options?: JwtSignOptions }) {
    return this.jwtService.sign(payload, options);
  }
}
