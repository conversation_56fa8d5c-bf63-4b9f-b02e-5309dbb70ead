import { Inject, Injectable } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { InjectModel } from '@nestjs/mongoose';
import { UserDetails, UserDetailsDocument } from '../schemas/user-details.schema';
import { Model } from 'mongoose';
import { Cache } from 'cache-manager';
import { UserActionsService } from './user-actions.service';

@Injectable()
export class UserDevicesService {
  constructor(
    @InjectModel(UserDetails.name) protected readonly userDetailModel: Model<UserDetailsDocument>,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly userActionsService: UserActionsService,
  ) {}

  async logout({ deviceToken, accessToken }: { deviceToken: string; accessToken: string }) {
    // Find user details by device token
    const userDetails = await this.userDetailModel.findOne({ 'userDevice.fcmToken': deviceToken });
    if (!userDetails) {
      return true;
    }

    const promises = [];
    // Remove device token
    promises.push(
      this.userDetailModel.updateOne(
        {
          _id: userDetails._id,
        },
        {
          $set: {
            userDevice: userDetails.userDevice.filter((userDevice) => userDevice.fcmToken !== deviceToken),
            userToken: userDetails.userToken.filter((userToken) => userToken.accessToken !== accessToken),
          },
        },
      ),
    );

    if (userDetails.mobilePhone) {
      const cacheKey = `UserAuth-${userDetails.mobilePhone}-${process.env.NODE_ENV}`;
      promises.push(this.cacheManager.del(cacheKey));
    }

    promises.push(
      this.userActionsService.saveActionLog(accessToken, 'logged-out', {
        request: {
          deviceToken,
          accessToken,
        },
      }),
    );

    await Promise.all(promises);
    return true;
  }
}
