import { BadRequestException, forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { InjectModel } from '@nestjs/mongoose';
import { compare } from 'bcryptjs';
import { Cache } from 'cache-manager';
import { Model, Types } from 'mongoose';
import { I18nContext, I18nService } from 'nestjs-i18n';
import { isMinAgePassword, isPasswordExpired, normalizeQueryHelper } from 'src/shared/helpers';
import { AuthService } from '../../auth/auth.service';
import { BaseService } from '../../shared/services/base-service';
import {
  createUniqueCode,
  getRandomCode,
  isEmptyObjectOrArray,
  isProductionEnv,
  isValidEmail,
  obscureEmail,
  passwordGenerate,
  printLog,
  securePhoneNumber,
  setWebPasswordLink,
  shortenPasswordLink,
  standardPhoneNumber,
} from '../../utils';
import { ConstantCaches } from '../../utils/constants/cache';
import { ConstantUser } from '../../utils/constants/user';
import { AdminForgotPasswordDto, EmailLoginDto, EmailPasswordLoginDto, EmailVerificationDto, OtpAdminLoginDto, VerifyForgotPasswordOTPDto } from '../dto/otp-login.dto';
import { RefreshTokenDto } from '../dto/refresh-token.dto';
import { UserAdmin, UserAdminDocument } from '../schemas/user-admin.schema';
import { User } from '../schemas/user.schema';
import { ConstantCommons } from '../../utils/constants';
import { SmsPasswordLogService } from './sms-password-log.service';
import { UsersService } from './users.service';
import { BE_VERSION, OpCos } from 'src/config';
import { UserActionsService } from './user-actions.service';
import { UserDetailService } from './user-detail.service';
import { ConstantRoles } from '../../utils/constants/role';
import { UserTokensService } from './user-tokens.service';
import { SmsService } from '../../third-parties/services/sms.service';

@Injectable()
export class UserAdminsService extends BaseService<UserAdmin> {
  constructor(
    @InjectModel(UserAdmin.name) private readonly modelUser: Model<UserAdminDocument>,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
    @Inject(forwardRef(() => SmsPasswordLogService))
    private readonly smsPasswordLogService: SmsPasswordLogService,
    private readonly smsService: SmsService,
    @Inject(forwardRef(() => UserActionsService))
    private readonly userActionsService: UserActionsService,
    @Inject(forwardRef(() => UserDetailService))
    private readonly userDetailService: UserDetailService,
    private readonly userTokensService: UserTokensService,
    private i18nService: I18nService,
  ) {
    super();
    this.model = modelUser;
  }

  async loginEmail({ email, isQuickLogin }: EmailLoginDto, i18n: I18nContext) {
    const user = await this.getUserByEmail(email, i18n);

    const userActive = await this.sendOtpToUser(user, i18n, isQuickLogin);

    return {
      userId: user._id,
      email,
      mobilePhone: securePhoneNumber(userActive?.mobilePhone),
      env: process.env.NODE_ENV,
      smsCode: !isProductionEnv() ? userActive?.smsCode : '',
      codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
    };
  }

  async loginEmailPassword({ email, password }: EmailPasswordLoginDto, i18n: I18nContext) {
    const user = await this.getUserByEmail(email, i18n);
    const isMatchPassword = user.password && (await compare(password, user.password));
    if (!isMatchPassword) {
      throw new BadRequestException(i18n.t('user.wrong_password'));
    }
    if (isPasswordExpired(user.lastUpdatedPassword)) {
      throw new BadRequestException(i18n.t('user.expired_password'));
    }
    const userActive = await this.sendOtpToUser(user, i18n);
    return {
      userId: user._id,
      email,
      mobilePhone: securePhoneNumber(userActive?.mobilePhone),
      env: process.env.NODE_ENV,
      smsCode: !isProductionEnv() ? userActive?.smsCode : '',
      codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
    };
  }

  async adminForgotPassword({ email }: AdminForgotPasswordDto, i18n: I18nContext) {
    const user = await this.getUserByEmail(email, i18n);
    if (process.env.OPCO == OpCos.Malaysia && isMinAgePassword(user.lastUpdatedPassword)) {
      throw new BadRequestException(i18n.t('user.min_password'));
    }
    const userActive = await this.sendOtpToUser(user, i18n);

    return {
      userId: user._id,
      email,
      mobilePhone: securePhoneNumber(userActive?.mobilePhone),
      env: process.env.NODE_ENV,
      smsCode: !isProductionEnv() ? userActive?.smsCode : '',
      codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
    };
  }

  async adminVerifyForgotPasswordOTP({ email, otp }: VerifyForgotPasswordOTPDto, i18n: I18nContext) {
    const user = await this.getUserByEmail(email, i18n);

    await this.usersService.verifyOTP({
      mobilePhone: standardPhoneNumber(user.mobilePhone),
      userId: user._id.toString(),
      smsCode: otp,
      isUserAdmin: true,
      i18n: i18n,
    });

    const log = await this.smsPasswordLogService.create({
      mobilePhone: user.mobilePhone,
      isActive: true,
      message: 'created by adminVerifyForgotPasswordOTP',
      userAdmin: user,
      sent: false,
      errorMessage: '',
      sessionId: createUniqueCode(),
    });
    return {
      sessionId: log.sessionId,
    };
  }

  async loginEmailVerifyOtp(dto: EmailVerificationDto, i18n: I18nContext) {
    try {
      const { email, smsCode, isRemember } = dto;

      const user = await this.getUserByEmail(email, i18n);
      await this.usersService.verifyOTP({
        mobilePhone: user.mobilePhone,
        userId: user._id.toString(),
        smsCode,
        i18n,
        isUserAdmin: true,
      });

      const { accessToken, refreshToken } = this.userTokensService.generatePairTokens({
        userId: String(user._id),
        mobilePhone: standardPhoneNumber(user.mobilePhone),
      });

      const userDetail = await this.userDetailService.findOne({ userId: user._id, isUserAdmin: true });
      await this.userDetailService.addOrUpdate({
        userId: user._id,
        mobilePhone: user.mobilePhone,
        isUserAdmin: true,
        userToken: [
          ...(userDetail?.userToken || []),
          ...[
            {
              accessToken,
              refreshToken,
              tokenType: null,
              expiresIn: isRemember ? process.env.EXPIRED_TIME_REMEMBER_LOGGED_IN : process.env.JWT_EXPIRED_TIME,
            },
          ],
        ],
        userRole: !isEmptyObjectOrArray(userDetail?.userRole) ? userDetail?.userRole : [{ roleKey: user.roleId[0] }],
      });

      //Update status for user active
      await this.update(user?._id, { status: ConstantUser.IS_ACTIVE });

      const userRole = (await this.userDetailService.findOneData({ userId: user._id, isUserAdmin: true }))?.userRole;
      const accessTokenDecode = this.authService.jwtDecrypt(accessToken);
      return {
        userId: user?._id,
        username: user.username,
        email: user.email,
        roles: userRole?.map((r) => r.roleKey) ?? [],
        firstname: user.firstname,
        lastname: user.lastname,
        accessToken,
        refreshToken,
        tokenType: null,
        expiresIn: accessTokenDecode?.exp * 1000,
        expiresDate: new Date(accessTokenDecode?.exp * 1000),
        isRemember,
      };
    } catch (e) {
      printLog(e);
      throw new HttpException(e.message || (await i18n.translate(`user.your_dsr_verification_code_invalid`)), HttpStatus.BAD_REQUEST);
    }
  }

  async refreshToken(dto: RefreshTokenDto, i18n: I18nContext) {
    try {
      const { currentToken, isRemember } = dto;
      const tokenData = await this.userDetailService.findUserTokenByAccessToken(currentToken);
      if (!tokenData) {
        throw new HttpException(
          await i18n.t(`common.not_found`, {
            args: { fieldName: 'tokenData' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const { mobilePhone, isUserAdmin, userId, userToken } = tokenData;
      const { accessToken, refreshToken } = this.userTokensService.generatePairTokens({
        userId,
        mobilePhone: standardPhoneNumber(mobilePhone),
      });
      await this.userDetailService.addOrUpdate({
        userId,
        isUserAdmin,
        mobilePhone: standardPhoneNumber(mobilePhone),
        userToken: [
          ...(userToken.filter((u) => u.accessToken !== currentToken) || []),
          ...[
            {
              accessToken: accessToken,
              refreshToken: refreshToken,
              tokenType: null,
              expiresIn: isRemember ? process.env.EXPIRED_TIME_REMEMBER_LOGGED_IN : process.env.JWT_EXPIRED_TIME,
              accessTokenDot: userToken.find((u) => u.accessToken === currentToken).accessTokenDot,
              refreshTokenDot: userToken.find((u) => u.accessToken === currentToken).refreshTokenDot,
            },
          ],
        ],
      });

      this.userActionsService
        .saveActionLog(accessToken, 'refresh-token', {
          request: { method: 'POST', params: { ...dto }, query: null, body: { ...dto } },
        })
        .then();

      return {
        userId: tokenData.userId,
        mobilePhone: standardPhoneNumber(mobilePhone),
        accessToken: accessToken,
        refreshToken: refreshToken,
        tokenType: null,
        expiresIn: isRemember ? process.env.EXPIRED_TIME_REMEMBER_LOGGED_IN : process.env.JWT_EXPIRED_TIME,
        accessTokenDot: userToken.find((u) => u.accessToken === currentToken).accessTokenDot,
        refreshTokenDot: userToken.find((u) => u.accessToken === currentToken).refreshTokenDot,
      };
    } catch (error) {
      printLog('refreshToken Admin:', error);
      throw new HttpException(error, HttpStatus.BAD_REQUEST);
    }
  }

  async resendOTP(dto: OtpAdminLoginDto, i18n: I18nContext) {
    try {
      const { email } = dto;
      const user = await this.findOne({ email });
      if (!user) {
        throw new HttpException(await i18n.t(`user.unauthorized`), HttpStatus.BAD_REQUEST);
      }
      if (!user || !user?.isActive) {
        throw new HttpException(await i18n.translate(`user.unauthorized_inactive`), HttpStatus.BAD_REQUEST);
      }

      if (user?.status == ConstantUser.BLOCKED) {
        throw new HttpException(await i18n.translate(`user.unauthorized_blocked`), HttpStatus.BAD_REQUEST);
      }
      const userDetail = await this.userDetailService.findOne({ userId: user._id, isUserAdmin: true });
      if (!userDetail || !userDetail?.userActive) {
        throw new HttpException(await i18n.translate(`user.sms_invalid_field`), HttpStatus.BAD_REQUEST);
      }
      const userActive = userDetail?.userActive[userDetail?.userActive.length - 1];
      const { createdAt } = userActive;
      const now = new Date().getTime();
      const getTimeCreateAt = new Date(createdAt).getTime();

      const secondBetween = Math.abs((now - getTimeCreateAt) / 1000);

      if (secondBetween > ConstantUser.CODE_EXPIRED_SECONDS) {
        const smsCode = getRandomCode();
        const data = {
          mobilePhone: standardPhoneNumber(user.mobilePhone),
          smsCode,
          status: ConstantUser.RESEND_OTP,
          codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
        };
        await this.userDetailService.addOrUpdate({
          userId: user._id,
          isUserAdmin: true,
          mobilePhone: standardPhoneNumber(user.mobilePhone),
          userActive: [
            {
              smsCode,
              status: ConstantUser.STEP_VERIFIED_SMS_CODE,
              codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
              createdAt: new Date(),
            },
          ],
        });

        if (user?.isSmsSend) {
          this.smsService
            .sendSMS({
              message: await i18n.translate(`user.your_dsr_verification_code`, {
                args: { code: smsCode },
              }),
              to: standardPhoneNumber(user.mobilePhone),
            })
            .then();
        }
        return {
          mobilePhone: user?.mobilePhone,
          smsCode: !isProductionEnv() ? smsCode : '',
          codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
        };
      }
      throw new HttpException('Do not resend otp', HttpStatus.BAD_REQUEST);
    } catch (error) {
      printLog(error);
      throw new HttpException(error, HttpStatus.BAD_REQUEST);
    }
  }

  async logOut(token: any, currentUser: User): Promise<any> {
    try {
      //delete user token
      const userDetail = await this.userDetailService.findUserTokenByAccessToken(token?.trim());
      if (!userDetail) {
        return false;
      }
      const { userId, mobilePhone, isUserAdmin, userToken } = userDetail;
      if (userToken) {
        await this.userDetailService.addOrUpdate({
          userId,
          mobilePhone,
          isUserAdmin,
          userToken: userToken.filter((t) => t.accessToken !== token),
        });
      }

      if (currentUser?.mobilePhone) {
        const cacheKey = `UserAuth-${currentUser?.mobilePhone}-${process.env.NODE_ENV}`;
        await this.cacheManager.del(cacheKey);
      }
      this.userActionsService
        .saveActionLog(token, 'logged-out', {
          request: { method: 'POST', params: { ...userToken }, query: null, body: null },
        })
        .then();

      return true;
    } catch (e) {
      console.log('logout error', e);
      return false;
    }
  }

  async getInforDistributorUser(
    search: string,
    skip: number,
    limit: number,
    orderBy: string = ConstantCommons.ORDER_BY_DEFAULT,
    orderDesc: string = ConstantCommons.ORDER_DESC_DEFAULT,
  ): Promise<any> {
    const aggregate = this.modelUser
      .aggregate()
      .project({
        _id: 0,
        users: '$$ROOT',
      })
      .lookup({
        from: 'distributoruserrelations',
        localField: '_id',
        foreignField: '_id',
        as: 'distributors',
      })
      .match({
        $or: [
          {
            'users.username': { $eq: search },
          },
          {
            'users.mobilePhone': { $eq: search },
          },
          {
            'users.email': { $eq: search },
          },
          {
            'users.distributorName': { $eq: search },
          },
          {
            'users.distributorId': { $eq: search },
          },
        ],
      })
      .sort({
        [`users.${orderBy}`]: orderDesc === ConstantCommons.ORDER_DESC_DEFAULT ? -1 : 1,
      });

    if (+skip >= 0 && +limit > 0) {
      aggregate.skip(skip).limit(limit);
    }
    await aggregate.exec();
  }

  async getUsers(
    distributorId: string,
    search: string,
    listRoles: string[],
    skip: number,
    limit: number,
    // sort: Record<string, any> = { updatedAt: -1 }
    orderBy: string = ConstantCommons.ORDER_BY_DEFAULT,
    orderDesc: string = ConstantCommons.ORDER_DESC_DEFAULT,
  ) {
    const normalizedQuery = normalizeQueryHelper(search);
    const aggregation = this.modelUser
      .aggregate()
      .project({
        _id: 0,
        ua: '$$ROOT',
      })
      .lookup({
        localField: 'ua._id',
        from: 'distributoruserrelations',
        foreignField: 'userAdmin',
        as: 'dur',
      })
      .lookup({
        localField: 'dur.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'dis',
      });

    if (distributorId) {
      aggregation.match({
        'dis.distributorId': distributorId,
      });
    }

    aggregation
      .project({
        userId: '$ua._id',
        username: '$ua.username',
        firstname: '$ua.firstname',
        lastname: '$ua.lastname',
        isActive: '$ua.isActive',
        status: '$ua.status',
        mobilePhone: '$ua.mobilePhone',
        mobilePhoneCode: '$ua.mobilePhoneCode',
        email: { $ifNull: ['$ua.email', ''] },
        saleRepId: '$ua.saleRepId',
        distributorId: '$dis.distributorId',
        distributorName: '$dis.distributorName',
        roleId: '$ua.roleId',
        createdAt: '$ua.createdAt',
        updatedAt: '$ua.updatedAt',
      })
      .match({ roleId: { $in: listRoles } });

    if (search) {
      aggregation.match({
        $or: [
          {
            email: new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
          {
            mobilePhone: new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
          {
            username: new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
        ],
      });
    }

    const queryRes = await aggregation
      .sort({ [`${orderBy}`]: orderDesc.toLocaleUpperCase() === ConstantCommons.ORDER_DESC_DEFAULT ? -1 : 1 })
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      })
      .exec();

    const [{ totalRecords, data }] = queryRes;

    return [data, totalRecords?.[0]?.total ?? 0];
  }

  async createDefaultUsers(): Promise<any> {
    const cacheKey = `${ConstantCaches.DEFAULT_USER_ADMIN_CREATED}-${BE_VERSION}-${process.env.NODE_ENV}`;
    if (await this.cacheManager.get(cacheKey)) {
      return true;
    }
    const adminUsers = ConstantUser.ADMIN_EMAILS;
    let isCreated = false;
    if (!isEmptyObjectOrArray(adminUsers)) {
      for (const email of adminUsers) {
        try {
          const admin = await this.findOne({ email });
          if (isEmptyObjectOrArray(admin)) {
            const user = await this.create({
              email,
              mobilePhone: ConstantUser.ADMIN_PHONE_NUMBERS.pop(),
              isActive: true,
              status: ConstantUser.IS_ACTIVE,
              isSmsSend: false,
              username: 'supper_admin',
              firstname: 'Supper',
              lastname: 'Admin',
              roleId: [ConstantRoles.SUPER_USER],
              password: await passwordGenerate(ConstantUser.DEFAULT_PASSWORD.toString()),
              lastUpdatedPassword: new Date(),
            });

            await this.userDetailService.addOrUpdate({
              userId: user._id,
              mobilePhone: user.mobilePhone,
              isUserAdmin: true,
              userRole: [{ roleKey: ConstantRoles.SUPER_USER }],
            });
            isCreated = true;
          } else {
            await this.update(admin._id, {
              isActive: true,
              status: ConstantUser.IS_ACTIVE,
              roleId: [ConstantRoles.SUPER_USER],
              password: await passwordGenerate(ConstantUser.DEFAULT_PASSWORD.toString()),
              lastUpdatedPassword: new Date(),
            });
            isCreated = true;
          }
        } catch (e) {
          console.log(e);
        }
      }
    }

    return await this.cacheManager.set(cacheKey, isCreated);
  }

  async createDefaultExternalUsers(): Promise<any> {
    const cacheKey = `${ConstantCaches.DEFAULT_EXTERNAL_USER_ADMIN_CREATED}-${process.env.NODE_ENV}`;
    if (await this.cacheManager.get(cacheKey)) {
      return true;
    }
    const adminUsers = ConstantUser.ADMIN_EXTERNAL_USERS;
    let isCreated = false;
    if (!isEmptyObjectOrArray(adminUsers)) {
      for (const userObj of adminUsers) {
        try {
          const admin = await this.findOne({ username: userObj.username });
          if (isEmptyObjectOrArray(admin)) {
            const user = await this.create({
              mobilePhone: ConstantUser.ADMIN_PHONE_NUMBERS.pop(),
              isActive: true,
              status: ConstantUser.IS_ACTIVE,
              isSmsSend: false,
              username: userObj.username,
              password: await passwordGenerate(userObj.password),
              firstname: userObj.firstname,
              lastname: userObj.lastname,
              roleId: [ConstantRoles.SUPER_USER],
            });
            isCreated = !isEmptyObjectOrArray(user);
          } else {
            isCreated = true;
          }
        } catch (e) {
          console.log(e);
        }
      }
    }

    return await this.cacheManager.set(cacheKey, isCreated);
  }

  async updatePasswordAdmin(user: string, newPassword: string, i18n: I18nContext): Promise<any> {
    const admin = await this.findOne({ _id: new Types.ObjectId(user) });
    if (!admin) {
      throw new HttpException(await i18n.t(`user.account_not_existed`), HttpStatus.BAD_REQUEST);
    }
    if (process.env.OPCO == OpCos.Malaysia && isMinAgePassword(admin.lastUpdatedPassword)) {
      throw new BadRequestException(i18n.t('user.min_password'));
    }
    await this.update(admin._id, {
      password: await passwordGenerate(newPassword),
      lastUpdatedPassword: new Date(),
    });
  }

  private async getUserByEmail(email: string, i18n: I18nContext) {
    if (!isValidEmail(email)) {
      throw new HttpException(await i18n.translate(`user.email_invalid_field`), HttpStatus.BAD_REQUEST);
    }

    const user = await this.findOne({ email });
    if (!user) {
      throw new HttpException(await i18n.t(`user.account_not_existed`), HttpStatus.BAD_REQUEST);
    }

    if (!user || !user?.isActive) {
      throw new HttpException(await i18n.translate(`user.unauthorized_inactive`), HttpStatus.BAD_REQUEST);
    }

    if (user?.status == ConstantUser.BLOCKED) {
      throw new HttpException(await i18n.translate(`user.unauthorized_blocked`), HttpStatus.BAD_REQUEST);
    }

    return user;
  }

  private async sendOtpToUser(user: UserAdmin, i18n: I18nContext, isQuickLogin = false) {
    const smsCode =
      ConstantUser.REVIEW_PHONE_NUMBER.concat(ConstantUser.ADMIN_PHONE_NUMBERS).indexOf(user.mobilePhone) > -1 ? ConstantUser.REVIEW_PHONE_NUMBER_OTP : getRandomCode();

    await this.userDetailService.addOrUpdate({
      userId: user._id,
      isUserAdmin: true,
      mobilePhone: standardPhoneNumber(user.mobilePhone),
      userActive: [
        {
          smsCode,
          status: ConstantUser.STEP_VERIFIED_SMS_CODE,
          codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
          createdAt: new Date(),
        },
      ],
    });
    this.update(user?._id, { status: ConstantUser.IS_ACTIVE }).then().catch();

    if (user?.isSmsSend && ConstantUser.REVIEW_PHONE_NUMBER.indexOf(user.mobilePhone) <= -1 && !isQuickLogin) {
      this.smsService
        .sendSMS({
          message: await i18n.translate(`user.your_dsr_verification_code`, {
            args: { code: smsCode },
          }),
          to: standardPhoneNumber(user.mobilePhone),
        })
        .then();
    }

    return { mobilePhone: standardPhoneNumber(user.mobilePhone), smsCode };
  }

  async sendCreatePasswordAdmin({ mobilePhone, userAdmin }: { mobilePhone: string; userAdmin: UserAdmin }, i18n: I18nContext) {
    try {
      await this.smsPasswordLogService.inactiveAllLogByUserId(userAdmin._id);
      const sessionId = createUniqueCode();
      const link = setWebPasswordLink(sessionId, userAdmin._id, obscureEmail(userAdmin.email));
      const message = (i18n ? i18n : this.i18nService).t(`message.invite_sale_sms`, {
        args: { fieldName1: mobilePhone, fieldName2: shortenPasswordLink(sessionId) },
      });
      const smsData = await this.smsService.sendSMS({
        message,
        to: mobilePhone,
      });

      return await this.smsPasswordLogService.create({
        mobilePhone,
        userAdmin,
        sessionId,
        link,
        message: message,
        sent: smsData.success,
        errorMessage: smsData.message,
        isActive: smsData.success,
      });
    } catch (e) {
      printLog('sendCreatePasswordAdmin', e);
      return null;
    }
  }

  async findOneAndUpdateData(contactId: string = null, userAdminData: any) {
    return this.modelUser.findOneAndUpdate({ contactId: contactId }, { $set: userAdminData }, { upsert: true, new: true });
  }

  async findOneAndUpdateDataBK(email: string = null, phone: string = null, userAdminData: any) {
    return email
      ? this.modelUser.findOneAndUpdate({ email: email }, { $set: userAdminData }, { upsert: true, new: true })
      : this.modelUser.findOneAndUpdate({ mobilePhone: phone }, { $set: userAdminData }, { upsert: true, new: true });
  }
}
