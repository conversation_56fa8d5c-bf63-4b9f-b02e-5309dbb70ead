import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UserActions, UserActionsDocument } from '../schemas/user-action.schema';
import { BaseService } from '../../shared/services/base-service';
import { LogsService } from '../../settings/logs.service';
import { SearchUserActionsDto } from '../dto/user-actions.dto';
import { normalizeQueryHelper } from '../../shared/helpers';
import * as moment from 'moment-timezone';
import { UserDetailService } from './user-detail.service';
import { printLog, sleep } from '../../utils';

@Injectable()
export class UserActionsService extends BaseService<UserActions> {
  constructor(
    @InjectModel(UserActions.name)
    private readonly _modelLog: Model<UserActionsDocument>,
    private readonly _userDetailService: UserDetailService,
    private readonly _logService: LogsService,
  ) {
    super();
    this.model = _modelLog;
  }

  /**
   * Delete old user action logs in batches to avoid performance issues.
   * @params none
   * @returns {Promise<void>}
   */
  async clearOldData(): Promise<void> {
    const time = moment().subtract(2, 'month').toISOString();
    const batchSize = 200;
    let count = 0;
    while (true) {
      const oldDocs = await this.model
        .find({ createdAt: { $lte: time } })
        .select('_id')
        .limit(batchSize)
        .lean();

      if (!oldDocs?.length || ++count > batchSize * 2000) break;

      const ids = oldDocs.map((doc) => doc._id);
      await this.model.deleteMany({ _id: { $in: ids } });
      printLog(ids);
      await sleep(500);
    }
  }

  async saveActionLog(token: string = null, actionKey: string, data: any): Promise<any> {
    try {
      if (!actionKey) {
        return;
      }

      const userToken = await this._userDetailService.findUserTokenByAccessToken(token?.trim());
      if (!userToken) {
        return null;
      }

      const method = data?.request?.method?.toLowerCase();
      const featureName = this._logService.getFeatureNameByApiKeyAndParams(actionKey, method, data?.request?.params);
      delete data?.request?.method;

      await this.create({
        userId: userToken?.userId,
        isUserAdmin: userToken?.isUserAdmin,
        key: actionKey,
        feature: featureName,
        method,
        request: { ...data?.request, token },
      });
    } catch (e) {
      return false;
    }
  }

  async searchActionLogs(filters: SearchUserActionsDto, orderBy: string = null, orderDesc: string = null): Promise<any> {
    const params: any = {};
    let sort: any = { createdAt: -1 };

    if (filters.search) {
      const normalizedQuery = normalizeQueryHelper(filters.search);
      params['$or'] = [
        {
          key: new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
        },
        {
          feature: new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
        },
      ];
    }
    if (filters.key) {
      params.key = filters.key;
    }
    if (filters.feature) {
      params.feature = filters.feature;
    }
    if (filters.userId) {
      params.userId = filters.userId;
    }
    if (filters.startTime) {
      params.createdAt = {
        $gte: filters.startTime,
        $lte: filters.endTime || new Date().toISOString(),
      };
    }

    orderDesc = orderDesc?.toLowerCase();
    if (orderBy && ['asc', 'desc'].indexOf(orderDesc) > -1) {
      sort = { [orderBy]: orderDesc == 'asc' ? 1 : -1 };
    }

    return this._modelLog
      .aggregate()
      .match(params)
      .sort(sort)
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: Number(filters.skip),
          },
          {
            $limit: Number(filters.limit),
          },
        ],
      });
  }

  async getOneActionLogByKeyId(key: string, method: string, date: string): Promise<any> {
    try {
      const start = new Date(`${date}T00:00:00.000Z`);
      const end = new Date(`${date}T23:59:59.999Z`);
      return await this.model
        .findOne({
          key,
          method,
          createdAt: {
            $gte: start,
            $lt: end,
          },
        })
        .sort({ createdAt: 1 });
    } catch (e) {
      return null;
    }
  }

  async getPlanFirstAction(keys: string[], methods: string[], date: string): Promise<any> {
    try {
      const start = new Date(`${date}T00:00:00.000Z`);
      const end = new Date(`${date}T23:59:59.999Z`);
      return await this.model
        .findOne({
          key: { $in: keys },
          method: { $in: methods },
          createdAt: {
            $gte: start,
            $lt: end,
          },
        })
        .sort({ createdAt: 1 });
    } catch (e) {
      return null;
    }
  }
}
