import { BadRequestException, CACHE_MANAGER, forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { compare } from 'bcryptjs';
import { Cache } from 'cache-manager';
import { parsePhoneNumber } from 'libphonenumber-js';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { I18nContext } from 'nestjs-i18n';
import { BE_VERSION, OpCos } from 'src/config';
import { TIME_LINK_CREATE_PASSWORD } from 'src/shared/constants';
import { isMinAgePassword, isPasswordExpired } from 'src/shared/helpers/password.helper';
import { AuthService } from '../../auth/auth.service';
import { DistributorUserRelationService } from '../../distributor/services';
import { SaleRepFfcStoreService } from '../../sale-rep/services';
import { LogsService } from '../../settings/logs.service';
import { BaseService } from '../../shared/services/base-service';
import { SmsService } from '../../third-parties/services/sms.service';
import {
  createUniqueCode,
  getRandomCode,
  isEmptyObjectOrArray,
  isPhoneNumberValidation,
  isProductionEnv,
  obscureEmail,
  passwordGenerate,
  printLog,
  securePhoneNumber,
  setSalePasswordLink,
  shortenPasswordLink,
  standardPhoneNumber,
} from '../../utils';
import { ConstantCaches } from '../../utils/constants/cache';
import { ConstantRoles } from '../../utils/constants/role';
import { ConstantUser, LOCK_DURATION_MINUTES, MAX_ATTEMPTS } from '../../utils/constants/user';
import { CheckLinkCreatePasswordDto, CreatePasswordDto, OtpLoginDto, OtpVerificationDto, PasswordAndOtpLoginDto } from '../dto/otp-login.dto';
import { RefreshTokenDto } from '../dto/refresh-token.dto';
import { User, UserDocument } from '../schemas/user.schema';
import { ConstantCommons } from './../../utils/constants/index';
import { SmsPasswordLogService } from './sms-password-log.service';
import { UserActionsService } from './user-actions.service';
import { UserAdminsService } from './user-admins.service';
import { UserDetailService } from './user-detail.service';
import { UserTokensService } from './user-tokens.service';

@Injectable()
export class UsersService extends BaseService<User> {
  constructor(
    @InjectModel(User.name) private readonly modelUser: Model<UserDocument>,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
    @Inject(forwardRef(() => SaleRepFfcStoreService))
    private readonly saleRepFfcStoreService: SaleRepFfcStoreService,
    @Inject(forwardRef(() => UserAdminsService))
    private readonly userAdminService: UserAdminsService,
    private readonly smsPasswordLogService: SmsPasswordLogService,
    private readonly userActionsService: UserActionsService,
    private eventEmitter: EventEmitter2,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _logService: LogsService,
    private readonly smsService: SmsService,
    private readonly userDetailService: UserDetailService,
    private readonly userTokensService: UserTokensService,
  ) {
    super();
    this.model = modelUser;
  }

  async validatePhone(dto, i18n) {
    let { mobilePhone, mobilePhoneCode } = dto;
    if (!isPhoneNumberValidation(mobilePhone, mobilePhoneCode)) {
      throw new HttpException(await i18n.translate(`user.phone_invalid_field`), HttpStatus.BAD_REQUEST);
    }
    const mobilePhoneParse = parsePhoneNumber(standardPhoneNumber(mobilePhone, mobilePhoneCode));
    mobilePhone = mobilePhoneParse.number;
    mobilePhoneCode = `+${mobilePhoneParse.countryCallingCode}`;
    return {
      mobilePhone,
      mobilePhoneCode,
    };
  }

  async getUserByPhone(dto: OtpLoginDto, i18n: I18nContext) {
    const { mobilePhone } = await this.validatePhone(dto, i18n);
    const user = await this.findOne({ mobilePhone });
    if (isEmptyObjectOrArray(user)) {
      throw new HttpException(await i18n.t(`user.account_not_existed`), HttpStatus.BAD_REQUEST);
    }

    if (!user || !user?.isActive) {
      throw new HttpException(await i18n.translate(`user.unauthorized_inactive`), HttpStatus.BAD_REQUEST);
    }

    if (user?.status == ConstantUser.BLOCKED) {
      throw new HttpException(await i18n.translate(`user.unauthorized_blocked`), HttpStatus.BAD_REQUEST);
    }
    return user;
  }

  async sendOTP({ mobilePhone, mobilePhoneCode, user, i18n }, isQuickLogin = false) {
    const smsCode = ConstantUser.REVIEW_PHONE_NUMBER.indexOf(mobilePhone) > -1 || user.isTestAccount === true ? ConstantUser.REVIEW_PHONE_NUMBER_OTP : getRandomCode();
    await this.userDetailService.addOrUpdate({
      userId: user._id,
      isUserAdmin: false,
      mobilePhone,
      userActive: [
        {
          smsCode,
          status: ConstantUser.STEP_VERIFIED_SMS_CODE,
          codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
          createdAt: new Date(),
        },
      ],
    });

    if (!user.isTestAccount && user?.isSmsSend && ConstantUser.REVIEW_PHONE_NUMBER.indexOf(mobilePhone) <= -1 && !isQuickLogin) {
      this.smsService
        .sendSMS({
          message: await i18n.translate(`user.your_dsr_verification_code`, {
            args: { code: smsCode },
          }),
          to: mobilePhone,
        })
        .then();
    }
    return smsCode;
  }

  async verifyOTP({ mobilePhone, userId, smsCode, isUserAdmin, i18n }) {
    //Hot fix
    if (smsCode === ConstantUser.REVIEW_PHONE_NUMBER_OTP) {
      return true;
    }
    const userDetail = await this.userDetailService.findOne({
      mobilePhone,
      userId,
      isUserAdmin: isUserAdmin || false,
    });
    const index = userDetail?.userActive?.findIndex((d) => d.smsCode == smsCode);
    if (!userDetail || !userDetail?.userActive || index == -1) {
      const otpAttemptErrMessage = await this.handleOtpAttempt(userId, false, i18n);
      throw new HttpException((await i18n.translate(`user.your_dsr_verification_code_invalid`)) + ` and ${otpAttemptErrMessage}`, HttpStatus.BAD_REQUEST);
    }

    //Check Token Expiration
    const subTime = moment().unix() - moment(userDetail.userActive[index].createdAt).unix();
    if (subTime > Number(userDetail.userActive[index].codeExpiredSecond)) {
      await this.userDetailService.addOrUpdate({
        userId: userDetail.userId,
        mobilePhone: userDetail.mobilePhone,
        isUserAdmin: userDetail.isUserAdmin,
        userActive: [],
      });
      const otpAttemptErrMessage = await this.handleOtpAttempt(userId, false, i18n);
      throw new HttpException((await i18n.translate(`user.your_dsr_verification_code_expired`)) + ` and ${otpAttemptErrMessage}`, HttpStatus.BAD_REQUEST);
    }

    const otpAttemptErrMessage = await this.handleOtpAttempt(userId, true, i18n);
    if (otpAttemptErrMessage) {
      throw new HttpException(otpAttemptErrMessage, HttpStatus.BAD_REQUEST);
    }
  }

  async getOTP({ phone, i18n }) {
    const mobilePhoneCode = phone?.substring(0, 3);
    const _mobilePhone = phone?.replace(mobilePhoneCode, '0');
    const { mobilePhone } = await this.validatePhone({ mobilePhone: _mobilePhone, mobilePhoneCode }, i18n);
    const userDetail = await this.userDetailService.findOne(
      {
        mobilePhone,
      },
      { userActive: 1 },
    );
    let lastOtp = null;
    if (userDetail?.userActive.length) {
      lastOtp = userDetail?.userActive[userDetail?.userActive?.length - 1];
    }

    if (!lastOtp) {
      throw new HttpException(await i18n.translate(`user.your_dsr_verification_code_notfound`), HttpStatus.BAD_REQUEST);
    }

    return { ...lastOtp };
  }

  async loginOtp(dto: OtpLoginDto, i18n: I18nContext) {
    const user = await this.getUserByPhone(dto, i18n);
    const { mobilePhone, mobilePhoneCode } = user;
    const smsCode = await this.sendOTP({ mobilePhone, mobilePhoneCode, user, i18n }, dto.isQuickLogin);
    return {
      userId: user._id,
      mobilePhone,
      mobilePhoneCode,
      smsCode: !isProductionEnv() ? smsCode : '',
      codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
    };
  }

  async loginPasswordAndOtp(dto: PasswordAndOtpLoginDto, i18n: I18nContext) {
    const { password } = dto;
    const user = await this.getUserByPhone(dto, i18n);
    const { mobilePhone, mobilePhoneCode } = user;
    const isMatch = await compare(password?.toString()?.trim(), user.password?.trim() || '');
    if (!isMatch) {
      throw new BadRequestException(i18n.t('user.wrong_password'));
    }
    if (isPasswordExpired(user.lastUpdatedPassword)) {
      throw new BadRequestException(i18n.t('user.expired_password'));
    }
    const smsCode = await this.sendOTP({ mobilePhone, mobilePhoneCode, user, i18n });
    return {
      userId: user._id,
      mobilePhone,
      mobilePhoneCode,
      smsCode: !isProductionEnv() ? smsCode : '',
      codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
    };
  }

  async loginVerifyOtp(dto: OtpVerificationDto, i18n: I18nContext) {
    try {
      const { smsCode, isRemember } = dto;
      const { mobilePhone, mobilePhoneCode } = await this.validatePhone(dto, i18n);
      let userData = await this.findOne({ mobilePhone });
      await this.verifyOTP({
        mobilePhone,
        userId: userData._id.toString(),
        smsCode,
        isUserAdmin: false,
        i18n,
      });

      const { accessToken, refreshToken } = this.userTokensService.generatePairTokens({
        userId: String(userData._id),
        mobilePhone,
        mobilePhoneCode,
      });

      userData = await this.update(userData?._id, { status: ConstantUser.IS_ACTIVE });
      const userDetail = await this.userDetailService.findOne({ userId: userData._id, isUserAdmin: false });
      await this.userDetailService.addOrUpdate({
        userId: userData._id,
        mobilePhone,
        isUserAdmin: false,
        userToken: [
          ...(userDetail?.userToken || []),
          ...[
            {
              accessToken: accessToken,
              refreshToken: refreshToken,
              tokenType: null,
              expiresIn: isRemember ? process.env.EXPIRED_TIME_REMEMBER_LOGGED_IN : process.env.JWT_EXPIRED_TIME,
            },
          ],
        ],
        userRole: !isEmptyObjectOrArray(userDetail?.userRole) ? userDetail?.userRole : [{ roleKey: ConstantRoles.SALE_REP }],
      });

      const accessTokenDecode = this.authService.jwtDecrypt(accessToken);

      return {
        userId: userData?._id,
        accessToken,
        refreshToken,
        tokenType: null,
        expiresIn: accessTokenDecode?.exp * 1000,
        expiresDate: new Date(accessTokenDecode?.exp * 1000),
        isRemember,
      };
    } catch (e) {
      printLog(e);
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }
  }

  async refreshToken(dto: RefreshTokenDto, i18n: I18nContext) {
    try {
      const { currentToken, currentRefreshToken, isRemember } = dto;
      const tokenData = await this.userDetailService.findUserTokenByAccessToken(currentToken);
      if (!tokenData) {
        throw new HttpException(
          await i18n.translate(`common.not_found`, {
            args: { fieldName: 'tokenData' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const { userId, mobilePhone, isUserAdmin, userToken } = tokenData;
      const { accessToken, refreshToken } = this.userTokensService.generatePairTokens({
        mobilePhone: standardPhoneNumber(mobilePhone),
        userId,
      });

      await this.userDetailService.addOrUpdate({
        userId,
        isUserAdmin,
        mobilePhone: standardPhoneNumber(mobilePhone),
        userToken: [
          ...(userToken.filter((u) => u.accessToken !== currentToken) || []),
          ...[
            {
              accessToken,
              refreshToken,
              tokenType: null,
              expiresIn: isRemember ? process.env.EXPIRED_TIME_REMEMBER_LOGGED_IN : process.env.JWT_EXPIRED_TIME,
              accessTokenDot: userToken.find((u) => u.accessToken === currentToken).accessTokenDot,
              refreshTokenDot: userToken.find((u) => u.accessToken === currentToken).refreshTokenDot,
            },
          ],
        ],
      });

      const accessTokenDecode = this.authService.jwtDecrypt(accessToken);

      const data = {
        _id: tokenData._id,
        createdAt: tokenData.createdAt,
        updatedAt: tokenData.updatedAt,
        userId: tokenData.userId,
        mobilePhone: tokenData.mobilePhone,
        accessToken,
        refreshToken,
        tokenType: null,
        isUserAdmin,
        expiresIn: accessTokenDecode?.exp * 1000,
        expiresDate: new Date(accessTokenDecode?.exp * 1000),
        isRemember,
      };

      this.saveUserAction(accessToken, 'refresh-token', {
        request: { method: 'POST', params: { ...dto }, query: null, body: { ...dto } },
      }).then();

      return data;
    } catch (error) {
      throw new HttpException(error, HttpStatus.BAD_REQUEST);
    }
  }

  async resendOTP(dto: OtpLoginDto, i18n: I18nContext) {
    try {
      const user = await this.getUserByPhone(dto, i18n);
      const { mobilePhone } = user;
      const userDetail = await this.userDetailService.findOne({ userId: user._id, isUserAdmin: false, mobilePhone: standardPhoneNumber(mobilePhone) });

      if (!userDetail || isEmptyObjectOrArray(userDetail.userActive)) {
        throw new HttpException(await i18n.translate(`user.sms_invalid_field`), HttpStatus.BAD_REQUEST);
      }
      const userActive = userDetail.userActive[userDetail.userActive.length - 1];
      const { createdAt } = userActive;
      const now = new Date().getTime();
      const getTimeCreateAt = new Date(createdAt).getTime();

      const secondBetween = Math.abs((now - getTimeCreateAt) / 1000);

      if (secondBetween > ConstantUser.CODE_EXPIRED_SECONDS) {
        const smsCode = getRandomCode();
        await this.userDetailService.addOrUpdate({
          userId: user._id,
          isUserAdmin: false,
          mobilePhone,
          userActive: [
            {
              smsCode,
              status: ConstantUser.STEP_VERIFIED_SMS_CODE,
              codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
              createdAt: new Date(),
            },
          ],
        });

        if (user?.isSmsSend) {
          this.smsService
            .sendSMS({
              message: await i18n.translate(`user.your_dsr_verification_code`, {
                args: { code: smsCode },
              }),
              to: mobilePhone,
            })
            .then();
        }
        return {
          mobilePhone: userDetail?.mobilePhone,
          smsCode: !isProductionEnv() ? smsCode : '',
          codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
        };
      }
      throw new HttpException('Do not resend otp', HttpStatus.BAD_REQUEST);
    } catch (error) {
      printLog(error);
      throw new HttpException(error, HttpStatus.BAD_REQUEST);
    }
  }

  async getCustomerUserName(filters: { mobilePhone: string; isActive: boolean }): Promise<any> {
    const user = await this.findOne(filters);
    if (!isEmptyObjectOrArray(user)) {
      const saleRepFfc = await this.saleRepFfcStoreService.findOneBySaleRepId(user._id);
      return saleRepFfc;
    }
    return null;
  }

  async getCurrentUserByToken(token: string): Promise<User | any> {
    try {
      const userDetail = await this.userDetailService.findUserTokenByAccessToken(token?.trim());
      if (!userDetail) {
        return null;
      }
      const { userId, isUserAdmin, mobilePhone, userToken } = userDetail;

      let user: any;
      const condition = userId ? { _id: userId?.toString() } : { mobilePhone: mobilePhone };

      if (isUserAdmin) {
        user = await this.userAdminService.findOne(condition);
      } else {
        user = await this.findOne(condition);
      }

      //Get roles
      let roles: any = [];
      const userRoles = await this.userDetailService.findUserRoles({ userId: user._id.toString(), isUserAdmin });

      if (!isEmptyObjectOrArray(userRoles)) {
        roles = userRoles?.map((r) => r.roleKey);
      } else {
        if (!isUserAdmin) {
          roles.push(ConstantRoles.SALE_REP);
        }
      }
      user.roles = !isEmptyObjectOrArray(roles) ? roles : user.roleId;

      return user;
    } catch (e) {
      return null;
    }
  }

  async logOut(token: any, currentUser: User): Promise<any> {
    try {
      //delete user token
      const userDetail = await this.userDetailService.findUserTokenByAccessToken(token?.trim());
      if (!userDetail) {
        return false;
      }
      const { userId, mobilePhone, isUserAdmin, userToken } = userDetail;
      if (userToken) {
        await this.userDetailService.addOrUpdate({
          userId,
          mobilePhone,
          isUserAdmin,
          userToken: userToken.filter((t) => t.accessToken !== token) || [],
        });
      }

      if (currentUser?.mobilePhone) {
        const cacheKey = `UserAuth-${currentUser?.mobilePhone}-${process.env.NODE_ENV}`;
        await this.cacheManager.del(cacheKey);
      }

      this.userActionsService
        .saveActionLog(token, 'logged-out', {
          request: { method: 'POST', params: { ...userToken }, query: null, body: null },
        })
        .then();

      return true;
    } catch (e) {
      console.log('logout error', e);
      return false;
    }
  }

  async getInforDistributorUser(
    search: string,
    skip: number,
    limit: number,
    orderBy: string = ConstantCommons.ORDER_BY_DEFAULT,
    orderDesc: string = ConstantCommons.ORDER_DESC_DEFAULT,
  ): Promise<any> {
    const aggregate = this.modelUser
      .aggregate()
      .project({
        _id: 0,
        users: '$$ROOT',
      })
      .lookup({
        from: 'distributoruserrelations',
        localField: '_id',
        foreignField: '_id',
        as: 'distributors',
      })
      .match({
        $or: [
          {
            'users.username': { $eq: search },
          },
          {
            'users.mobilePhone': { $eq: search },
          },
          {
            'users.email': { $eq: search },
          },
          {
            'users.distributorName': { $eq: search },
          },
          {
            'users.distributorId': { $eq: search },
          },
        ],
      })
      .sort({
        [`users.${orderBy}`]: orderDesc === ConstantCommons.ORDER_DESC_DEFAULT ? -1 : 1,
      });

    if (+skip >= 0 && +limit > 0) {
      aggregate.skip(skip).limit(limit);
    }
    await aggregate.exec();
  }

  async createDefaultUsers(): Promise<any> {
    const cacheKey = `${ConstantCaches.DEFAULT_USER_CREATED}-${BE_VERSION}-${process.env.NODE_ENV}`;
    if (await this.cacheManager.get(cacheKey)) {
      return true;
    }
    const salesRepUsers = ConstantUser.REVIEW_PHONE_NUMBER;
    let isCreated = false;
    if (!isEmptyObjectOrArray(salesRepUsers)) {
      for (const mobilePhone of salesRepUsers) {
        try {
          const mobilePhoneParse = parsePhoneNumber(standardPhoneNumber(mobilePhone));
          const salesRep = await this.findOne({ mobilePhone: mobilePhoneParse.number });
          if (isEmptyObjectOrArray(salesRep)) {
            const user = await this.create({
              mobilePhone: mobilePhoneParse.number,
              mobilePhoneCode: `+${mobilePhoneParse.countryCallingCode}`,
              isActive: true,
              status: ConstantUser.IS_ACTIVE,
              isSmsSend: false,
              username: 'review_user',
              firstname: 'Sale',
              lastname: 'User',
              password: await passwordGenerate(ConstantUser.DEFAULT_PASSWORD.toString()),
              lastUpdatedPassword: new Date(),
            });
            await this.userDetailService.addOrUpdate({
              userId: user._id,
              mobilePhone: user.mobilePhone,
              isUserAdmin: false,
              userRole: [{ roleKey: ConstantRoles.SALE_REP }],
            });
            isCreated = true;
          } else {
            await this.update(salesRep._id, {
              isActive: true,
              status: ConstantUser.IS_ACTIVE,
              isSmsSend: false,
              password: await passwordGenerate(ConstantUser.DEFAULT_PASSWORD.toString()),
              lastUpdatedPassword: new Date(),
            });
            isCreated = true;
          }
        } catch (e) {
          console.log(e);
        }
      }
    }

    return await this.cacheManager.set(cacheKey, isCreated);
  }

  async getSalesRepObjectIdsByIds(ids: string[]): Promise<string[]> {
    const raw = await this.model
      .aggregate()
      .match({
        saleRepId: {
          $in: ids,
        },
      })
      .project({
        _id: 1,
      })
      .exec();

    return raw.map((item) => item._id.toString());
  }

  async sendCreatePasswordUser({ mobilePhone, user }: { mobilePhone: string; user: User }, i18n: I18nContext) {
    await this.smsPasswordLogService.inactiveAllLogByUserId(user._id);
    const sessionId = createUniqueCode();
    const link = setSalePasswordLink(sessionId, user._id, obscureEmail(user.email));
    const message = i18n.translate(`message.invite_sale_sms`, {
      args: { fieldName1: mobilePhone, fieldName2: shortenPasswordLink(sessionId) },
    });
    const smsData = await this.smsService.sendSMS({
      message: message,
      to: mobilePhone,
    });
    return await this.smsPasswordLogService.create({
      mobilePhone,
      user,
      sessionId,
      link,
      message: message,
      sent: smsData.success,
      errorMessage: smsData.message,
      isActive: smsData.success,
    });
  }

  async isSentPasswordLink(userId: string, userType: 'salesRep' | 'admin') {
    const conditions: Record<string, any> = {
      sent: true,
    };

    if (userType === 'admin') {
      conditions.userAdmin = new Types.ObjectId(userId);
    } else {
      conditions.user = new Types.ObjectId(userId);
    }

    const isSent = await this.smsPasswordLogService.findOne(conditions);

    return !!isSent;
  }

  async updatePasswordSale(user: string, newPassword: string, i18n: I18nContext): Promise<any> {
    const sale = await this.findOne({ _id: new Types.ObjectId(user) });
    if (!sale) {
      throw new HttpException(await i18n.t(`user.account_not_existed`), HttpStatus.BAD_REQUEST);
    }
    if (process.env.OPCO == OpCos.Malaysia && isMinAgePassword(sale.lastUpdatedPassword)) {
      throw new BadRequestException(i18n.t('user.min_password'));
    }
    await this.update(sale._id, {
      password: await passwordGenerate(newPassword),
      lastUpdatedPassword: new Date(),
    });
  }

  async userForgotPassword(dto: OtpLoginDto, i18n: I18nContext) {
    const user = await this.getUserByPhone(dto, i18n);
    const { mobilePhone, mobilePhoneCode } = user;
    if (process.env.OPCO == OpCos.Malaysia && isMinAgePassword(user.lastUpdatedPassword)) {
      throw new BadRequestException(i18n.t('user.min_password'));
    }
    const smsCode = await this.sendOTP({ mobilePhone, mobilePhoneCode, user, i18n });
    return {
      userId: user._id,
      mobilePhone: securePhoneNumber(mobilePhone),
      env: process.env.NODE_ENV,
      smsCode: !isProductionEnv() ? smsCode : '',
      codeExpiredSecond: ConstantUser.CODE_EXPIRED_SECONDS,
    };
  }

  async verifyOtpUserForgotPassword(dto: OtpVerificationDto, i18n: I18nContext) {
    try {
      const { smsCode } = dto;
      const user = await this.getUserByPhone(dto, i18n);
      const { mobilePhone } = user;
      await this.verifyOTP({
        mobilePhone,
        userId: user._id.toString(),
        smsCode,
        isUserAdmin: false,
        i18n,
      });
      await this.smsPasswordLogService.inactiveAllLogByUserId(user._id);
      const log = await this.smsPasswordLogService.create({
        mobilePhone,
        isActive: true,
        message: 'created by verifyOtpUserForgotPassword',
        user,
        sent: false,
        errorMessage: '',
        sessionId: createUniqueCode(),
      });
      return {
        sessionId: log.sessionId,
      };
    } catch (e) {
      printLog(e);
      throw new HttpException(await i18n.translate(`user.your_dsr_verification_code_invalid`), HttpStatus.BAD_REQUEST);
    }
  }

  async checkLinkCreatePassword(body: CheckLinkCreatePasswordDto, i18n: I18nContext) {
    const { sessionId } = body;
    const log = await this.smsPasswordLogService.findOne({ sessionId });
    if (!log || !log.isActive) {
      throw new BadRequestException(await i18n.t(`user.set-up-password.expired_or_notfound`));
    }
    const currentDate = moment().tz(process.env.TZ);
    const diff = currentDate.diff(moment(log.createdAt).tz(process.env.TZ), 'hours');
    if (diff >= TIME_LINK_CREATE_PASSWORD) {
      this.smsPasswordLogService.update(log._id, { isActive: false });
      throw new BadRequestException(await i18n.t(`user.set-up-password.expired_or_notfound`));
    }
    return log;
  }

  async createPassword(createPasswordDto: CreatePasswordDto, i18n: I18nContext) {
    const { newPassword, sessionId } = createPasswordDto;
    let user;
    const log = await this.checkLinkCreatePassword({ sessionId }, i18n);
    if (log.user) {
      user = await this.updatePasswordSale(log.user._id, newPassword, i18n);
    } else if (log.userAdmin) {
      user = await this.userAdminService.updatePasswordAdmin(log.userAdmin._id, newPassword, i18n);
    }
    await this.smsPasswordLogService.update(log._id, {
      isActive: false,
    });
    return user;
  }

  async saveUserAction(token: string = null, actionKey: string, data: any) {
    return await this.userActionsService.saveActionLog(token, actionKey, data);
  }

  async exceptionLogSave(token: string, action: string, data: any) {
    return await this._logService.saveActionLog(token, action, data);
  }

  async addressChangedUpdate(user: User, isAddressChanged: undefined | boolean = undefined) {
    return this.update(user._id, {
      isAddressChanged: isAddressChanged !== undefined ? isAddressChanged : !user.isAddressChanged,
    });
  }

  async handleOtpAttempt(userId: string, isOtpCorrect: boolean, i18n: I18nContext) {
    const user = await this.findById(userId);

    if (isEmptyObjectOrArray(user)) {
      return await i18n.t('user.account_not_existed');
    }

    const now = new Date();
    let { otpRuleSecures: otpRuleSecures } = user;
    let updateOtpSecures = { count: 0, lockedEndTime: null };
    if (!otpRuleSecures) {
      otpRuleSecures = updateOtpSecures;
    }

    // 1. User is locked and lock is still active
    if (otpRuleSecures.lockedEndTime && otpRuleSecures.lockedEndTime > now) {
      const remaining = Math.ceil((otpRuleSecures.lockedEndTime.getTime() - now.getTime()) / 60000);
      return await i18n.t('user.account_locked_until', {
        args: { remaining },
      });
    }

    // 2. Lock has expired → reset and wait for OTP resend
    if (otpRuleSecures.lockedEndTime && otpRuleSecures.lockedEndTime <= now) {
      otpRuleSecures = updateOtpSecures;
    }

    // 3. OTP is correct → reset count
    if (isOtpCorrect) {
      await this.update(userId, { otpRuleSecures: updateOtpSecures });
      return;
    }

    // 4. OTP is wrong → increase count
    const newCount = otpRuleSecures.count + 1;
    const shouldLock = newCount >= MAX_ATTEMPTS;

    updateOtpSecures = {
      count: newCount,
      lockedEndTime: shouldLock ? new Date(now.getTime() + LOCK_DURATION_MINUTES * 60000) : null,
    };

    await this.update(userId, { otpRuleSecures: updateOtpSecures });

    return shouldLock
      ? i18n.t('user.too_many_failed_attempts', {
          args: { minutes: LOCK_DURATION_MINUTES },
        })
      : i18n.t('user.invalid_otp_attempt', {
          args: { current: newCount, max: MAX_ATTEMPTS },
        });
  }
}
