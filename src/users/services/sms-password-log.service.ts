import { Injectable } from '@nestjs/common';

import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { BaseService } from '../../shared/services/base-service';
import { SmsPasswordLog, SmsPasswordLogDocument } from '../schemas/sms-password-log.schema';

@Injectable()
export class SmsPasswordLogService extends BaseService<SmsPasswordLog> {
  constructor(
    @InjectModel(SmsPasswordLog.name)
    private readonly _smsPasswordLog: Model<SmsPasswordLogDocument>,
  ) {
    super();
    this.model = _smsPasswordLog;
  }

  inactiveAllLogByPhone(phone: string) {
    return this.model.updateMany(
      { mobilePhone: phone },
      {
        $set: {
          isActive: false,
        },
      },
    );
  }

  inactiveAllLogByUserId(userId: string) {
    return this.model.updateMany(
      {
        $or: [
          {
            user: new Types.ObjectId(userId),
          },
          {
            userAdmin: new Types.ObjectId(userId),
          },
        ],
      },
      {
        $set: {
          isActive: false,
        },
      },
    );
  }
}
