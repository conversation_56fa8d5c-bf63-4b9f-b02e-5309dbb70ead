import { BadRequestException, Body, Controller, Delete, Get, Headers, Param, Post, Request, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiCreatedResponse, ApiHeader, ApiTags } from '@nestjs/swagger';
import { I18n, I18nContext } from 'nestjs-i18n';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { ApiException } from '../shared/api-exception.model';
import { ApiResponse } from '../shared/response/api-response';
import { printLog } from '../utils';
import { CreateUserDeviceDto } from './dto/create-user-device.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import {
  AdminForgotPasswordDto,
  CheckLinkCreatePasswordDto,
  CreatePasswordDto,
  EmailLoginDto,
  EmailPasswordLoginDto,
  EmailVerificationDto,
  OtpAdminLoginDto,
  OtpLoginDto,
  OtpVerificationDto,
  PasswordAndOtpLoginDto,
  VerifyForgotPasswordOTPDto,
} from './dto/otp-login.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { UsernameLoginDto } from './dto/username-login.dto';
import { UserAdminsService } from './services/user-admins.service';
import { ConstantUser } from '../utils/constants/user';
import { UsersService } from './services/users.service';
import { Roles } from '../shared/decorator/roles.decorator';
import { ConstantRoles } from '../utils/constants/role';
import { UserActionsService } from './services/user-actions.service';
import { UserDetailService } from './services/user-detail.service';
import { UserDevicesService } from './services/user-devices.service';
import { UserTokensService } from './services/user-tokens.service';

@ApiTags('User')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly userAdminService: UserAdminsService,
    private readonly _userActionsService: UserActionsService,
    private readonly _userDetailService: UserDetailService,
    private readonly userDevicesService: UserDevicesService,
    private readonly userTokensService: UserTokensService,
  ) {}

  @Post('login')
  @ApiCreatedResponse({ type: LoginResponseDto })
  @ApiBadRequestResponse({ type: ApiException })
  async login(@Body() loginDto: UsernameLoginDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {}

  @Post('login-otp')
  @ApiCreatedResponse({ type: LoginResponseDto })
  @ApiBadRequestResponse({ type: ApiException })
  async loginOtp(@Body() loginDto: OtpLoginDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    return new ApiResponse(await this.usersService.loginOtp(loginDto, i18n));
  }

  @Post('login-password-and-otp')
  @ApiBadRequestResponse({ type: ApiException })
  async loginPasswordAndOtp(@Body() loginDto: PasswordAndOtpLoginDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    return new ApiResponse(await this.usersService.loginPasswordAndOtp(loginDto, i18n));
  }

  @Post('login-verify-otp')
  @ApiCreatedResponse({ type: LoginResponseDto })
  @ApiBadRequestResponse({ type: ApiException })
  async loginVerifyOtp(@Body() loginDto: OtpVerificationDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    return new ApiResponse(await this.usersService.loginVerifyOtp(loginDto, i18n));
  }

  @Post('refresh-token')
  @ApiCreatedResponse({ type: LoginResponseDto })
  @ApiBadRequestResponse({ type: ApiException })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    return new ApiResponse(await this.usersService.refreshToken(refreshTokenDto, i18n));
  }

  @Post('resend-otp')
  @ApiCreatedResponse({ type: LoginResponseDto })
  @ApiBadRequestResponse({ type: ApiException })
  async resendOTP(@Body() resendOTPDto: OtpLoginDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    return new ApiResponse(await this.usersService.resendOTP(resendOTPDto, i18n));
  }

  @Post('refresh-admin-token')
  @ApiCreatedResponse({ type: LoginResponseDto })
  @ApiBadRequestResponse({ type: ApiException })
  async refreshTokenAdmin(@Body() refreshTokenDto: RefreshTokenDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    const data = await this.userTokensService.refreshToken({
      refreshToken: refreshTokenDto.currentRefreshToken,
      i18n,
    });
    return new ApiResponse(data);
  }

  @Post('resend-admin-otp')
  @ApiCreatedResponse({ type: LoginResponseDto })
  @ApiBadRequestResponse({ type: ApiException })
  async resendOTPAdmin(@Body() resendOTPDto: OtpAdminLoginDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    return new ApiResponse(await this.userAdminService.resendOTP(resendOTPDto, i18n));
  }

  @Delete('device-token/:fcmToken')
  @ApiBadRequestResponse({ type: ApiException })
  async deleteDeviceToken(@Param('fcmToken') fcmToken: string, i18n: I18nContext) {
    const existedDeviceToken = await this._userDetailService.findUserDeviceByFcmToken(fcmToken);
    if (existedDeviceToken?.userDevice) {
      await this._userDetailService.addOrUpdate({
        userId: existedDeviceToken.userId,
        isUserAdmin: existedDeviceToken.isUserAdmin,
        userDevice: existedDeviceToken?.userDevice.filter((d) => d.fcmToken !== fcmToken) || [],
      });
    }
    return new ApiResponse({
      deleted: true,
    });
  }

  @Post('login-email')
  @ApiCreatedResponse({ type: LoginResponseDto })
  @ApiBadRequestResponse({ type: ApiException })
  async loginEmail(@Body() loginDto: EmailLoginDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    return new ApiResponse(await this.userAdminService.loginEmail(loginDto, i18n));
  }

  @Post('login-email-verify-otp')
  @ApiCreatedResponse({ type: LoginResponseDto })
  @ApiBadRequestResponse({ type: ApiException })
  async loginEmailVerifyOtp(@Body() loginDto: EmailVerificationDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    return new ApiResponse(await this.userAdminService.loginEmailVerifyOtp(loginDto, i18n));
  }

  @Post('login-email-password')
  @ApiBadRequestResponse({ type: ApiException })
  async loginEmailPassword(@Body() loginDto: EmailPasswordLoginDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    return new ApiResponse(await this.userAdminService.loginEmailPassword(loginDto, i18n));
  }

  @Post('admin-forgot-password')
  @ApiBadRequestResponse({ type: ApiException })
  async adminForgotPassword(@Body() dto: AdminForgotPasswordDto, @I18n() i18n: I18nContext) {
    return new ApiResponse(await this.userAdminService.adminForgotPassword(dto, i18n));
  }

  @Post('admin-verify-forgot-password-otp')
  @ApiBadRequestResponse({ type: ApiException })
  async adminVerifyForgotPasswordOTP(@Body() dto: VerifyForgotPasswordOTPDto, @I18n() i18n: I18nContext) {
    return new ApiResponse(await this.userAdminService.adminVerifyForgotPasswordOTP(dto, i18n));
  }

  @Post('user-forgot-password')
  @ApiBadRequestResponse({ type: ApiException })
  async userForgotPassword(@Body() dto: OtpLoginDto, @I18n() i18n: I18nContext) {
    return new ApiResponse(await this.usersService.userForgotPassword(dto, i18n));
  }

  @Post('verify-otp-user-forgot-password')
  @ApiBadRequestResponse({ type: ApiException })
  async verifyOtpUserForgotPassword(@Body() loginDto: OtpVerificationDto, @I18n() i18n: I18nContext): Promise<LoginResponseDto | any> {
    return new ApiResponse(await this.usersService.verifyOtpUserForgotPassword(loginDto, i18n));
  }

  @Post('create-password')
  @ApiBadRequestResponse({ type: ApiException })
  async createPassword(@Body() createPasswordDto: CreatePasswordDto, @I18n() i18n: I18nContext): Promise<any> {
    await this.usersService.createPassword(createPasswordDto, i18n);
    return new ApiResponse();
  }

  @Post('check-link-create-password')
  @ApiBadRequestResponse({ type: ApiException })
  async checkLinkCreatePassword(@Body() body: CheckLinkCreatePasswordDto, @I18n() i18n: I18nContext): Promise<any> {
    await this.usersService.checkLinkCreatePassword(body, i18n);
    return new ApiResponse();
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiBadRequestResponse({ type: ApiException })
  async findOne(@Param('id') id: string): Promise<any> {
    try {
      const user = await this.usersService.findOne({ _id: id });
      const fullName =
        user.firstname || user.lastname ? `${user.firstname.toString().replace('undefined', '')} ${user.lastname.toString().replace('undefined', '')}` : user.username;
      return new ApiResponse(
        user?._id
          ? {
              email: user.email,
              userName: user.username,
              firstName: user.firstname,
              lastName: user.lastname,
              fullName: `${fullName}`,
              mobilePhone: user.mobilePhone,
              mobilePhoneCode: user.mobilePhoneCode,
              defaultLanguage: user.defaultLanguage,
              isActive: user.isActive,
            }
          : null,
      );
    } catch (e) {
      printLog(e);
    }
  }

  @Post('device-token')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiBadRequestResponse({ type: ApiException })
  async saveDeviceToken(@Body() createUserDevice: CreateUserDeviceDto) {
    const { userId, fcmToken } = createUserDevice;
    const existedUser = await this.usersService.findById(userId);
    if (!existedUser) {
      throw new BadRequestException('USER_NOT_FOUND');
    }
    const existedDeviceToken = await this._userDetailService.findUserDeviceByFcmToken(fcmToken);
    await this._userDetailService.addOrUpdate({
      userId,
      isUserAdmin: false,
      userDevice: [...(existedDeviceToken?.userDevice.filter((d) => d.fcmToken !== fcmToken) || []), ...[{ fcmToken: fcmToken, createdAt: new Date() }]],
    });
    return new ApiResponse({ ...existedUser, fcmToken });
  }

  @Post('logout')
  @ApiBadRequestResponse({ type: ApiException })
  async logout(@Headers('authorization') token: string, @Body() { fcmToken }: { fcmToken: string }, @I18n() i18n: I18nContext) {
    await this.userDevicesService.logout({
      deviceToken: fcmToken,
      accessToken: token?.replace('Bearer', '')?.trim(),
    });
    return new ApiResponse(await i18n.translate(`user.logout_success`));
  }

  @Post('get-otp')
  @ApiBadRequestResponse({ type: ApiException })
  async getOTP(@Body() body, @I18n() i18n: I18nContext): Promise<any> {
    const { phone, password } = body;
    if (!password || password.toUpperCase() !== ConstantUser.SECRET_ADMIN_KEY) {
      throw new BadRequestException('user.wrong_password');
    }
    const lastOtp = await this.usersService.getOTP({ phone, i18n });
    return new ApiResponse(lastOtp);
  }

  @Post('actions/data')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @ApiBadRequestResponse({ type: ApiException })
  async getUserAction(@Body() body, @I18n() i18n: I18nContext): Promise<any> {
    const { feature, startTime, endTime, orderByName, orderByDesc, userId, skip, limit } = body;
    const userActions = await this._userActionsService.searchActionLogs({ feature, startTime, endTime, userId, skip: skip || 0, limit: limit || 100 }, orderByName, orderByDesc);
    return new ApiResponse(userActions);
  }
}
