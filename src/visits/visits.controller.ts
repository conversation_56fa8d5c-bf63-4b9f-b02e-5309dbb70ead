import { <PERSON>, Get, Param, <PERSON>s } from '@nestjs/common';
import { SmsPasswordLogService } from '../users/services/sms-password-log.service';
import { ApiTags } from '@nestjs/swagger';
import { getBaseUrlData } from '../utils';

@ApiTags('Visit')
@Controller('v')
export class VisitsController {
  constructor(private readonly smsPasswordLogService: SmsPasswordLogService) {}

  @Get(':sessionId')
  async redirect(@Res() res, @Param('sessionId') sessionId: string) {
    let url = getBaseUrlData().OPEN_APP_LINK;
    if (sessionId?.length) {
      try {
        const log = await this.smsPasswordLogService.findOne({ sessionId });
        if (log) {
          url = log.link;
        }
      } catch (e) {}
    }

    return res.redirect(url);
  }
}
