// const TypeOrm = require('typeorm');
const fs = require('fs');
import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';
import { typeOrmConfig } from './typeorm.config';

config();
const configService = new ConfigService();
const isDevelop = !['production', 'development', 'staging'].includes(configService.get('NODE_ENV'));
console.log({ env: configService.get('NODE_ENV'), certPath: configService.get('POSTGRES_CERT_PATH'), isDevelop });
const { migrations, ...typeOrmConfigWithoutMigrations } = typeOrmConfig;

const AppDataSourceTransaction = new DataSource(typeOrmConfigWithoutMigrations);

export { AppDataSourceTransaction };
