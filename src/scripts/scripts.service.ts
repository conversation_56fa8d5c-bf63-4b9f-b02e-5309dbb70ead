import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Distributor, DistributorDocument, DistributorUserRelation, DistributorUserRelationDocument } from 'src/distributor/schemas';
import { SaleRepOutletRelation, SaleRepOutletRelationDocument } from 'src/outlets/schemas/sale-rep-outlet-relation.schema';
import { Outlet, OutletDocument } from '../outlets/schemas/outlet.schema';
import { mappingChannelData } from '../utils';

@Injectable()
export class ScriptsService {
  constructor(
    @InjectModel(Distributor.name)
    private readonly distributorModel: Model<DistributorDocument>,
    @InjectModel(SaleRepOutletRelation.name)
    private readonly salesRepOutletRelationModel: Model<SaleRepOutletRelationDocument>,
    @InjectModel(DistributorUserRelation.name)
    private readonly salesRepDistributorRelationModel: Model<DistributorUserRelationDocument>,
    @InjectModel(Outlet.name)
    private readonly outletModel: Model<OutletDocument>,
  ) {}

  async correctUserDistributorRelation() {
    const relations = await this.salesRepOutletRelationModel
      .aggregate()
      .match({
        disconnected: false,
        saleRep: new Types.ObjectId('643d0eef8a33c92609610b8b'),
      })
      .lookup({
        localField: 'saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'salesRepDistributorRelation',
      })
      .unwind({
        preserveNullAndEmptyArrays: false,
        path: '$salesRepDistributorRelation',
      })
      .lookup({
        localField: 'outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({
        path: '$outlet',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'outlet.distributorId': {
          $ne: null,
        },
      })
      .lookup({
        localField: 'salesRepDistributorRelation.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        $expr: {
          $ne: ['$distributor.distributorId', '$outlet.distributorId'],
        },
      });

    const filteredRelations = Object.values(
      relations.reduce(
        (initial, relation) => ({
          ...initial,
          [String(relation.saleRep)]: relation,
        }),
        {},
      ),
    ) as typeof relations;

    const distributorIds = [...new Set(filteredRelations.map((relation) => relation.outlet.distributorId as string).filter(Boolean))];
    const distributors = await this.distributorModel.find({
      distributorId: {
        $in: distributorIds,
      },
    });
    const distributorIdMap = distributors.reduce(
      (initial, distributor) => ({
        ...initial,
        [distributor.distributorId]: distributor._id,
      }),
      {},
    );

    await this.salesRepDistributorRelationModel.bulkWrite(
      filteredRelations
        .map((relation) => {
          const distributorId = distributorIdMap[relation.outlet.distributorId];
          if (!distributorId) {
            return null;
          }

          return {
            updateOne: {
              filter: {
                user: new Types.ObjectId(String(relation.saleRep)),
              },
              update: {
                distributor: distributorIdMap[relation.outlet.distributorId],
              },
            },
          };
        })
        .filter(Boolean),
    );

    return {
      updatedSalesRep: [...new Set(filteredRelations.map((relation) => String(relation.saleRep)).filter(Boolean))],
    };
  }

  async migrationChannelSubChannel() {
    try {
      // Define your mappings
      const subChannelMapping = mappingChannelData().subChannelMapping;
      const channelMapping = mappingChannelData().channelMapping;

      // Variables for batch processing
      const batchSize = 2000;
      let skip = 0;
      let documentsUpdated = 0;

      while (true) {
        const documents = await this.outletModel
          .find({
            $or: [{ channel: { $ne: null } }, { subChannel: { $ne: null } }],
          })
          .skip(skip)
          .limit(batchSize);
        if (documents.length === 0) break;
        const bulkOperations = documents
          .map((doc) => {
            const subChannelDescription = doc.subChannel;
            const channelDescription = doc.channel;

            const subChannelCode = subChannelMapping[subChannelDescription] || subChannelDescription;
            const channelCode = channelMapping[channelDescription] || channelDescription;
            if (subChannelCode || channelCode) {
              console.log({ channelDescription, subChannelDescription, subChannelCode, channelCode });
              return {
                updateOne: {
                  filter: { _id: doc._id },
                  update: {
                    $set: {
                      subChannel: subChannelCode,
                      channel: channelCode,
                      channelDescription,
                      subChannelDescription,
                    },
                  },
                },
              };
            }
          })
          .filter(Boolean); // Filter out any undefined operations

        if (bulkOperations.length > 0) {
          const result = await this.outletModel.bulkWrite(bulkOperations);
          documentsUpdated += result.modifiedCount;
        }
        skip += batchSize;
      }

      console.log(`Update completed. Total documents updated: ${documentsUpdated}`);
    } catch (error) {
      console.error('Error updating documents:', error);
    } finally {
    }
  }
}
