import { Controller, Post, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/decorator/guard/jwt-auth.guard';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { ConstantRoles } from 'src/utils/constants/role';
import { ScriptsService } from './scripts.service';
import { ApiTags } from '@nestjs/swagger';

@Controller('api/scripts')
@ApiTags('Script')
@UseGuards(JwtAuthGuard)
@Roles(ConstantRoles.SUPER_USER)
export class ScriptsController {
  constructor(private readonly services: ScriptsService) {}

  @Post('correct-sales-rep-distributor-relation')
  async correctSalesRepDistributorRelation() {
    const result = await this.services.correctUserDistributorRelation();
    return result;
  }

  @Post('migration-channel-data')
  async migrationChannelSubChannel() {
    const result = await this.services.migrationChannelSubChannel();
    return result;
  }
}
