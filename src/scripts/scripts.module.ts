import { Module } from '@nestjs/common';
import { ScriptsController } from './scripts.controller';
import { ScriptsService } from './scripts.service';
import { MongooseModule } from '@nestjs/mongoose';
import { SaleRepOutletRelation, SaleRepOutletRelationSchema } from 'src/outlets/schemas/sale-rep-outlet-relation.schema';
import { AuthModule } from 'src/auth/auth.module';
import { Distributor, DistributorSchema, DistributorUserRelation, DistributorUserRelationSchema } from 'src/distributor/schemas';
import { Outlet, OutletSchema } from '../outlets/schemas/outlet.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Distributor.name,
        schema: DistributorSchema,
      },
      {
        name: SaleRepOutletRelation.name,
        schema: SaleRepOutletRelationSchema,
      },
      {
        name: DistributorUserRelation.name,
        schema: DistributorUserRelationSchema,
      },
      {
        name: Outlet.name,
        schema: OutletSchema,
      },
    ]),
    AuthModule,
  ],
  controllers: [ScriptsController],
  providers: [ScriptsService],
})
export class ScriptsModule {}
