import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsN<PERSON>ber, IsString, Max, Min } from 'class-validator';

export class FeedbackUpdateDto {
  @ApiModelProperty()
  @IsNumber()
  @Min(1, { message: 'message.feedback.min' })
  @Max(12, { message: 'message.feedback.max' })
  star: number;

  @ApiModelProperty()
  @IsString()
  comment: string;
}
