import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsDateString, IsNumber, IsOptional, IsString } from 'class-validator';

export class FeedbackSearchDto {
  @ApiModelProperty()
  @IsString()
  @IsOptional()
  distributorId?: string;

  @ApiModelProperty()
  @IsNumber()
  @IsOptional()
  star?: number;

  @ApiModelProperty()
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiModelProperty()
  @IsString()
  @IsOptional()
  keyword: string;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  startDate: Date;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  endDate: Date;
}
