import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNumber, IsOptional, IsString } from 'class-validator';

export class FeedbackSearchDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  distributorId?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  star?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  keyword: string;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  startDate: Date;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  endDate: Date;
}
