import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsN<PERSON>ber, IsString, Max, Min } from 'class-validator';
import { Expose, Transform } from 'class-transformer';

export class FeedbackResponseDto {
  @Expose()
  @Transform(({ obj }) => obj.user?._id?.toString())
  userId: string;

  @Expose()
  star: number;

  @Expose()
  comment: string;
}
