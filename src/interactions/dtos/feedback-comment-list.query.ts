import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsDateString, IsNumberString, IsOptional, IsString } from 'class-validator';
import { PaginationDto } from 'src/shared/dtos/pagination.dto';

export class FeedbackCommentsQuery extends PaginationDto {
  @ApiModelProperty()
  @IsString()
  @IsOptional()
  distributorId?: string;

  @ApiModelProperty()
  @IsNumberString()
  @IsOptional()
  star?: number;

  @ApiModelProperty()
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiModelProperty()
  @IsString()
  @IsOptional()
  keyword?: string;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  fromDate: Date;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  toDate: Date;
}
