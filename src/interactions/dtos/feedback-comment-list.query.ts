import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNumberString, IsOptional, IsString } from 'class-validator';
import { PaginationDto } from 'src/shared/dtos/pagination.dto';

export class FeedbackCommentsQuery extends PaginationDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  distributorId?: string;

  @ApiProperty()
  @IsNumberString()
  @IsOptional()
  star?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  keyword?: string;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  fromDate: Date;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  toDate: Date;
}
