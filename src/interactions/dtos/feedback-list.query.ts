import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNumberString, IsOptional, IsString } from 'class-validator';

export class FeedbackListQuery {
  @ApiProperty()
  @IsString()
  @IsOptional()
  distributorId?: string;

  @ApiProperty()
  @IsNumberString()
  @IsOptional()
  star?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  keyword?: string;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  fromDate: Date;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  toDate: Date;
}
