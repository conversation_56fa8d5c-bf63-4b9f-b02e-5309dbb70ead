import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { I18n, I18nContext } from 'nestjs-i18n';
import { User } from 'src/users/schemas/user.schema';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { DistributorUserRelationService } from '../distributor/services';
import { OrderParams } from '../orders/dtos/order-filter.dto';
import { ApiException } from '../shared/api-exception.model';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { Roles } from '../shared/decorator/roles.decorator';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Serialize } from '../shared/interceptor/serialize.interceptor';
import { ApiResponse } from '../shared/response/api-response';
import { isEmptyObjectOrArray, toListResponse, validateFields } from '../utils';
import { ConstantRoles } from '../utils/constants/role';
import { FeedbackCommentsQuery } from './dtos/feedback-comment-list.query';
import { FeedbackListQuery } from './dtos/feedback-list.query';
import { FeedbackResponseDto } from './dtos/feedback-response.dto';
import { FeedbackUpdateDto } from './dtos/feedback-update.dto';
import { FeedbacksService } from './services/feedbacks.service';

@ApiTags('Feedback')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/feedback')
@UseGuards(JwtAuthGuard, RolesGuard)
export class FeedbacksController {
  constructor(private readonly _feedbacksService: FeedbacksService, private readonly _distributorUserRelationService: DistributorUserRelationService) {}

  @Post('vote')
  @ApiBearerAuth()
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Sales rep vote',
    description: 'Sales rep vote this app',
  })
  @Serialize(FeedbackResponseDto)
  async vote(@Body() feedbackUpdateDto: FeedbackUpdateDto, @CurrentUser() currentUser: any, @I18n() i18n: I18nContext) {
    const { star, comment } = feedbackUpdateDto;
    try {
      await validateFields(
        {
          star,
          comment,
        },
        `common.not_found`,
        i18n,
      );
      const distributorUserRelation = await this._distributorUserRelationService.findByUserId(currentUser._id);
      const currentDate = moment().tz(process.env.TZ).startOf('d').toDate();
      const endOfDate = moment().tz(process.env.TZ).endOf('d').toDate();
      const feedback = await this._feedbacksService.findOne({ user: new Types.ObjectId(currentUser._id), createdAt: { $gte: currentDate, $lte: endOfDate } });
      let result;
      if (isEmptyObjectOrArray(feedback)) {
        result = await this._feedbacksService.createFeedback(currentUser, star, comment, distributorUserRelation.distributor);
      } else {
        //result = await this._feedbacksService.updateFeedback(feedback, star, comment, distributorUserRelation.distributor);
      }
      return new ApiResponse(result);
    } catch (e) {
      return new ApiResponse(null, e);
    }
  }

  @Get('vote')
  @ApiBearerAuth()
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @Serialize(FeedbackResponseDto)
  async getVoteBySalesRep(@CurrentUser() currentUser: any) {
    const currentDate = moment().tz(process.env.TZ).startOf('d').toDate();
    const endOfDate = moment().tz(process.env.TZ).endOf('d').toDate();
    const result = await this._feedbacksService.findOne({ user: new Types.ObjectId(currentUser._id), createdAt: { $gte: currentDate, $lte: endOfDate } });
    return new ApiResponse(result);
  }

  @Get('list')
  @ApiBearerAuth()
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @ApiBadRequestResponse({ type: ApiException })
  async getFeedbacks(@Query() filters: FeedbackListQuery, @Query() sort: OrderParams, @CurrentUser() user: User & { roles: string[] }, @I18n() i18n: I18nContext) {
    const result = await this._feedbacksService.getList({ ...filters, ...sort }, user, i18n);
    return new ApiResponse(result);
  }

  @Get('comments')
  @ApiBearerAuth()
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @ApiBadRequestResponse({ type: ApiException })
  async getComments(@Query() filters: FeedbackCommentsQuery, @Query() sort: OrderParams, @CurrentUser() user: User & { roles: string[] }) {
    const result = await this._feedbacksService.getComments({ ...filters, ...sort }, user);
    return new ApiResponse(toListResponse(result));
  }
}
