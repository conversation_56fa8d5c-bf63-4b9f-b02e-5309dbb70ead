import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from '../../shared/schemas/base.schema';
import { Document, Types } from 'mongoose';
import { User } from '../../users/schemas/user.schema';
import { Distributor } from '../../distributor/schemas';

export type FeedbackDocument = Feedback & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class Feedback extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  user: User;

  @Prop({ type: Types.ObjectId, ref: Distributor.name, index: true, default: null })
  distributor: Distributor;

  @Prop({ index: true, default: 0 })
  star: number;

  @Prop({ index: true, nullable: true })
  comment: string;
}

export const FeedbackSchema = SchemaFactory.createForClass(Feedback);
