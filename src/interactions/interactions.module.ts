import { forwardRef, Module } from '@nestjs/common';
import { FeedbacksController } from './feedbacks.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from '../auth/auth.module';
import { DistributorModule } from '../distributor/distributor.module';
import { UsersModule } from '../users/users.module';
import { Feedback, FeedbackSchema } from './schemas/feedbacks.schema';
import { FeedbacksService } from './services/feedbacks.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Feedback.name,
        schema: FeedbackSchema,
      },
    ]),
    AuthModule,
    forwardRef(() => DistributorModule),
    // DistributorModule,
    forwardRef(() => UsersModule),
  ],
  providers: [FeedbacksService],
  exports: [FeedbacksService],
  controllers: [FeedbacksController],
})
export class InteractionsModule {}
