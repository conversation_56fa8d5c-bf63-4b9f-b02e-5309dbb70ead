import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { I18nContext } from 'nestjs-i18n';
import { DistributorUserRelationService } from 'src/distributor/services';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { ConstantRoles } from 'src/utils/constants/role';
import { Distributor } from '../../distributor/schemas';
import { normalizeQueryHelper } from '../../shared/helpers';
import { BaseService } from '../../shared/services/base-service';
import { User } from '../../users/schemas/user.schema';
import { ConstantCommons } from '../../utils/constants';
import { FeedbackCommentsQuery } from '../dtos/feedback-comment-list.query';
import { FeedbackListQuery } from '../dtos/feedback-list.query';
import { FeedbackStarNumber } from '../enums';
import { Feedback, FeedbackDocument } from '../schemas/feedbacks.schema';

@Injectable()
export class FeedbacksService extends BaseService<Feedback> {
  constructor(
    @InjectModel(Feedback.name)
    private readonly _feedbackDocumentModel: Model<FeedbackDocument>,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
  ) {
    super();
    this.model = _feedbackDocumentModel;
  }

  async createFeedback(user: User, star: number, comment: string, distributor: Distributor) {
    return this.create({ user, star, comment, distributor });
  }

  async updateFeedback(feedback: Feedback, star: number, comment: string, distributor: Distributor) {
    return this.update(feedback._id, { star, comment, distributor });
  }

  createFeedbackAggregation(queries: FeedbackListQuery & OrderParams) {
    const fromDate = moment(queries.fromDate || moment().tz(process.env.TZ).subtract(1, 'month'))
      .startOf('date')
      .toDate();
    const toDate = moment(queries.toDate).tz(process.env.TZ).endOf('date').toDate();
    const aggregate = this._feedbackDocumentModel.aggregate().match({
      createdAt: {
        $gte: fromDate,
        $lte: toDate,
      },
    });

    if (queries.distributorId) {
      aggregate.match({
        distributor: new Types.ObjectId(queries.distributorId),
      });
    }

    if (queries.keyword) {
      // mongo DB can not search regex for value which include \n (break line)
      // format searched field (replace \n to space)
      // after filter formatted field, remove it
      aggregate
        .addFields({
          formattedComment: '$comment',
        })
        .project({
          _id: 1,
          createdAt: 1,
          updatedAt: 1,
          user: 1,
          distributor: 1,
          star: 1,
          comment: 1,
          formattedComment: {
            $concatArrays: [
              {
                $split: ['$formattedComment', '\n'],
              },
              [' '],
            ],
          },
        });
      aggregate
        .match({
          formattedComment: {
            $regex: new RegExp(`^.*${normalizeQueryHelper(queries.keyword || '')}.*$`, 'i'),
          },
        })
        .project({
          formattedComment: 0,
        });
    }

    if (queries.star) {
      aggregate.match({
        star: +queries.star,
      });
    }

    if (queries.userId) {
      aggregate.match({
        user: new Types.ObjectId(queries.userId),
      });
    }

    return aggregate;
  }

  async getList(queries: FeedbackListQuery, user: User & { roles: string[] }, i18n: I18nContext) {
    if (user.roles.includes(ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const disUserAdmins = await this._distributorUserRelationService.findManyByUserAdminId(user._id.toString());
      if (!disUserAdmins?.map((item) => item.distributor._id.toString()).includes(queries.distributorId)) {
        throw new UnauthorizedException('plan.unauthorized');
      }
    }

    const data = await this.createFeedbackAggregation(queries)
      .group({
        _id: {
          star: '$star',
        },
        count: { $sum: 1 },
      })
      .project({
        _id: 0,
        star: '$_id.star',
        numberOfFeedbacks: '$count',
      })
      .exec();

    return Object.values(FeedbackStarNumber)
      .filter((item) => typeof item === 'number')
      .reduce(
        (pre, curr) => {
          if (!pre.map((item) => item.star).includes(curr as FeedbackStarNumber)) {
            return [
              ...pre,
              {
                star: curr,
                numberOfFeedbacks: 0,
                label: i18n.t(`feedback.star.${curr.toString()}`),
              },
            ];
          }

          return pre;
        },
        data as Array<{
          star: FeedbackStarNumber;
          numberOfFeedbacks: number;
          label: string;
        }>,
      )
      .map((item) => ({
        ...item,
        label: i18n.t(`feedback.star.${item.star.toString()}`),
      }));
  }

  async getComments(queries: FeedbackCommentsQuery & OrderParams, user: User & { roles: string[] }) {
    if (user.roles.includes(ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const disUserAdmins = await this._distributorUserRelationService.findManyByUserAdminId(user._id.toString());
      if (!disUserAdmins?.map((item) => item.distributor._id.toString()).includes(queries.distributorId)) {
        throw new UnauthorizedException('plan.unauthorized');
      }
    }

    const [{ totalRecords, data }] = await this.createFeedbackAggregation(queries)
      .lookup({
        localField: 'user',
        from: 'users',
        foreignField: '_id',
        as: 'user',
      })
      .unwind({
        path: '$user',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .project({
        _id: 1,
        createdAt: 1,
        updatedAt: 1,
        star: 1,
        comment: 1,
        salesRep: {
          _id: '$user._id',
          salesRepId: '$user.saleRepId',
          username: '$user.username',
        },
        distributor: {
          _id: '$distributor._id',
          name: '$distributor.distributorName',
          distributorId: '$distributor.distributorId',
        },
      })
      .sort({
        [queries.orderBy || 'createdAt']: queries.orderDesc && queries.orderDesc.toString().toLocaleUpperCase() === ConstantCommons.ORDER_ASC_DEFAULT ? 1 : -1,
      })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: queries.skip ? +queries.skip : 0,
          },
          {
            $limit: queries.limit ? +queries.limit : 10,
          },
        ],
      });

    const totalItem = totalRecords.length ? totalRecords[0].total : 0;

    return [data, totalItem];
  }
}
