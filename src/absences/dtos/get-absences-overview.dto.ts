import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsArray, IsDateString, IsOptional, IsString } from 'class-validator';

export class GetAbsencesOverviewDto {
  @IsString()
  @ApiModelProperty()
  distributorId: string;

  @ApiModelProperty()
  @IsDateString()
  month: Date;

  @IsArray()
  @IsOptional()
  @ApiModelProperty({
    type: String,
    isArray: true,
    required: false,
  })
  salesRepIds?: string[];
}
