import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsDateString, IsOptional, IsString } from 'class-validator';

export class GetAbsencesOverviewDto {
  @IsString()
  @ApiProperty()
  distributorId: string;

  @ApiProperty()
  @IsDateString()
  month: Date;

  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: String,
    isArray: true,
    required: false,
  })
  salesRepIds?: string[];
}
