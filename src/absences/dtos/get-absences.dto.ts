import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsArray, IsDateString, IsOptional, IsString } from 'class-validator';
import { PaginationDto } from 'src/shared/dtos/pagination.dto';

export class GetAbsencesDto extends PaginationDto {
  @IsString()
  @ApiModelProperty()
  distributorId: string;

  @IsArray()
  @IsOptional()
  @ApiModelProperty({
    type: String,
    isArray: true,
    required: false,
  })
  salesRepIds?: string[];

  @ApiModelProperty()
  @IsDateString()
  fromDate: Date;

  @ApiModelProperty()
  @IsDateString()
  toDate: Date;
}
