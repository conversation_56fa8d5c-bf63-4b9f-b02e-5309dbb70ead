import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsDateString, IsOptional, IsString } from 'class-validator';
import { PaginationDto } from 'src/shared/dtos/pagination.dto';

export class GetAbsencesDto extends PaginationDto {
  @IsString()
  @ApiProperty()
  distributorId: string;

  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: String,
    isArray: true,
    required: false,
  })
  salesRepIds?: string[];

  @ApiProperty()
  @IsDateString()
  fromDate: Date;

  @ApiProperty()
  @IsDateString()
  toDate: Date;
}
