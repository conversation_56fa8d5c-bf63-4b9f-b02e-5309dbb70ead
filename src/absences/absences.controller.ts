import { Body, Controller, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/decorator/guard/jwt-auth.guard';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { ConstantRoles } from 'src/utils/constants/role';
import { AbsencesService } from './absences.service';
import { GetAbsencesDto } from './dtos/get-absences.dto';
import { ApiResponse } from 'src/shared/response/api-response';
import { toListResponse } from 'src/utils';
import { I18n, I18nContext } from 'nestjs-i18n';
import { GetAbsencesOverviewDto } from './dtos/get-absences-overview.dto';
import { CancelAbsencesDto } from './dtos/cancel-absences.dto';
import { User } from 'src/users/schemas/user.schema';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';

@Controller('api/absences')
@ApiTags('Absences')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AbsencesController {
  constructor(private readonly service: AbsencesService) {}

  @Post()
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  async getAbsences(@Body() dto: GetAbsencesDto, @I18n() i18n: I18nContext) {
    const data = await this.service.getAbsences(dto, i18n);
    return new ApiResponse(toListResponse(data));
  }

  @Post('overview')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  async getAbsencesOverview(@Body() dto: GetAbsencesOverviewDto) {
    const data = await this.service.getAbsencesOverview(dto);
    return new ApiResponse({ data });
  }

  @Put('cancel')
  @Roles(ConstantRoles.SALE_REP)
  async cancelAbsence(@CurrentUser() currentUser: User, @Body() dto: CancelAbsencesDto, @I18n() i18n: I18nContext) {
    return this.service.cancelAbsence(currentUser._id.toString(), dto, i18n);
  }
}
