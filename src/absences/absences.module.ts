import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AbsencesController } from './absences.controller';
import { AbsencesService } from './absences.service';
import { AuthModule } from 'src/auth/auth.module';
import { OutletJourneyPlanning as OutletJourneyPlanningMongo, OutletJourneyPlanningSchema } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { DistributorUserRelation, DistributorUserRelationSchema } from 'src/distributor/schemas';
import { MissReasonsModule } from 'src/miss-reasons/miss-reasons.module';

@Module({
  imports: [
    AuthModule,
    // MongoDB
    MongooseModule.forFeature([
      { name: OutletJourneyPlanningMongo.name, schema: OutletJourneyPlanningSchema },
      { name: DistributorUserRelation.name, schema: DistributorUserRelationSchema },
    ]),
    MissReasonsModule,
  ],
  controllers: [AbsencesController],
  providers: [AbsencesService],
})
export class AbsencesModule {}
