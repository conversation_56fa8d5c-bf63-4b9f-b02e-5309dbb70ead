import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { OutletJourneyPlanning } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { GetAbsencesDto } from './dtos/get-absences.dto';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import * as moment from 'moment-timezone';
import { DistributorUserRelation } from 'src/distributor/schemas';
import { I18nContext } from 'nestjs-i18n';
import { GetAbsencesOverviewDto } from './dtos/get-absences-overview.dto';
import { CancelAbsencesDto } from './dtos/cancel-absences.dto';
import { MissReasonsService } from 'src/miss-reasons/miss-reasons.service';

@Injectable()
export class AbsencesService {
  constructor(
    @InjectModel(OutletJourneyPlanning.name)
    private readonly outletJourneyPlanningModel: Model<OutletJourneyPlanning>,
    @InjectModel(DistributorUserRelation.name)
    private readonly distributorUserRelationModel: Model<DistributorUserRelation>,
    private readonly missReasonsService: MissReasonsService,
  ) {}

  async getAbsences(dto: GetAbsencesDto, i18n: I18nContext) {
    const fromDate = moment(dto.fromDate).tz(process.env.TZ).startOf('day').toDate();
    const toDate = moment(dto.toDate).tz(process.env.TZ).endOf('day').toDate();

    let salesRepIds = dto.salesRepIds;
    if (!salesRepIds?.length) {
      const relations = await this.distributorUserRelationModel.find({
        where: {
          distributor: { id: dto.distributorId },
          user: { id: { $ne: null } },
        },
      });
      salesRepIds = relations.map((rel: any) => rel?.user?.id);
    }

    const query = this.outletJourneyPlanningModel
      .find({
        missedReason: { $ne: null },
        visitStatus: { $ne: VisitStatus.COMPLETED },
        saleRep: { $in: salesRepIds },
        $or: [
          {
            rescheduled: false,
            day: { $gte: fromDate, $lte: toDate },
          },
          {
            rescheduled: true,
            rescheduledDay: { $gte: fromDate, $lte: toDate },
          },
        ],
      })
      .populate('saleRep')
      .populate('missedReason');

    const [results, total] = await query
      .skip(dto.skip || 0)
      .limit(dto.limit || 10)
      .exec();

    // Transform data similar to MongoDB aggregation result
    const formattedData = (results as any)?.map((item) => ({
      reasonId: item.missedReason.id,
      day: moment(item.rescheduled ? item.rescheduledDay : item.day).format('DD/MM/YYYY'),
      salesRep: {
        id: item.saleRep.id,
        salesRepId: item.saleRep.saleRepId,
        username: item.saleRep.username,
      },
      reason: this.missReasonsService.getMissReasonTranslation(item.missedReason.translations, i18n.lang).reason,
      affectedOutlets: 1, // You might need to adjust this based on your requirements
    }));

    return [formattedData, total];
  }

  async getAbsencesOverview(dto: GetAbsencesOverviewDto) {
    const fromDate = moment(dto.month).tz(process.env.TZ).startOf('month').toDate();
    const toDate = moment(dto.month).tz(process.env.TZ).endOf('month').toDate();

    const query = await this.distributorUserRelationModel.find({
      where: {
        distributor: { id: dto.distributorId },
        user: { id: { $ne: null } },
      },
    });

    const salesRepIds = dto.salesRepIds?.length ? dto.salesRepIds : query.map((item: any) => item?.user?.id);

    const data = await this.outletJourneyPlanningModel
      .find({
        missedReason: { $ne: null },
        visitStatus: { $ne: VisitStatus.COMPLETED },
        saleRep: { $in: salesRepIds },
        $or: [
          {
            rescheduled: false,
            day: { $gte: fromDate, $lte: toDate },
          },
          {
            rescheduled: true,
            rescheduledDay: { $gte: fromDate, $lte: toDate },
          },
        ],
      })
      .exec();

    const res = data.reduce(
      (pre, curr) => ({
        ...pre,
        [moment(curr.rescheduled ? curr.rescheduledDay : curr.day)
          .tz(process.env.TZ)
          .format('DD/MM/YYYY')]: 1,
      }),
      {},
    );

    return res;
  }

  async cancelAbsence(userId: string, dto: CancelAbsencesDto, i18n: I18nContext) {
    const date = moment(dto.day, 'DD/MM/YYYY');
    if (date.endOf('day').isBefore(moment())) {
      throw new BadRequestException(i18n.t('absence.error.cancelAbsenceInPast'));
    }

    await this.outletJourneyPlanningModel.updateMany(
      {
        saleRep: { id: userId },
        missedReason: { id: dto.reasonId },
        $or: [
          {
            rescheduled: false,
            day: {
              $gte: date.startOf('date').toDate(),
              $lte: date.endOf('date').toDate(),
            },
          },
          {
            rescheduled: true,
            rescheduledDay: {
              $gte: date.startOf('date').toDate(),
              $lte: date.endOf('date').toDate(),
            },
          },
        ],
      },
      {
        visitStatus: VisitStatus.START_VISIT,
        missedReason: null,
        outletReport: null,
      },
    );

    return true;
  }
}
