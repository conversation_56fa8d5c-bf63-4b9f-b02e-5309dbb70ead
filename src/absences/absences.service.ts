import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model, Types } from 'mongoose';
import { OutletJourneyPlanning } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { GetAbsencesDto } from './dtos/get-absences.dto';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import * as moment from 'moment-timezone';
import { DistributorUserRelation } from 'src/distributor/schemas';
import { I18nContext } from 'nestjs-i18n';
import { GetAbsencesOverviewDto } from './dtos/get-absences-overview.dto';
import { CancelAbsencesDto } from './dtos/cancel-absences.dto';
import { MissReasonsService } from 'src/miss-reasons/miss-reasons.service';

@Injectable()
export class AbsencesService {
  constructor(
    @InjectModel(OutletJourneyPlanning.name)
    private readonly outletJourneyPlanningModel: Model<OutletJourneyPlanning>,
    @InjectModel(DistributorUserRelation.name)
    private readonly distributorUserRelationModel: Model<DistributorUserRelation>,
    private readonly missReasonsService: MissReasonsService,
  ) {}

  async getAbsences(dto: GetAbsencesDto, i18n: I18nContext) {
    const fromDate = moment(dto.fromDate).tz(process.env.TZ).startOf('day').toDate();
    const toDate = moment(dto.toDate).tz(process.env.TZ).endOf('day').toDate();

    let salesRepIds = dto.salesRepIds;
    if (!salesRepIds || !salesRepIds.length) {
      const query = await this.distributorUserRelationModel.find({
        distributor: new Types.ObjectId(dto.distributorId),
        user: { $exists: true, $ne: null },
      });

      salesRepIds = query.map((item) => item.user.toString());
    }

    const condition: Record<string, any> = {
      missedReason: { $ne: null },
      visitStatus: { $ne: VisitStatus.COMPLETED },
      saleRep: {
        $in: salesRepIds.map((item) => new Types.ObjectId(item)),
      },
      $or: [
        { day: { $gte: fromDate, $lte: toDate }, rescheduled: false },
        { rescheduledDay: { $gte: fromDate, $lte: toDate }, rescheduled: true },
      ],
    };

    const [{ totalRecords, data }] = await this.outletJourneyPlanningModel
      .aggregate()
      .match(condition)
      .lookup({
        localField: 'saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'saleRep',
      })
      .unwind({
        path: '$saleRep',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'missedReason',
      })
      .unwind({
        path: '$missedReason',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'missedReason.controllable': false,
      })
      .addFields({
        displayDay: {
          $dateToString: {
            format: '%d/%m/%Y',
            date: {
              $cond: [{ $eq: ['$rescheduled', false] }, '$day', '$rescheduledDay'],
            },
            timezone: process.env.TZ,
          },
        },
      })
      .group({
        _id: {
          reason: '$missedReason._id',
          day: '$displayDay',
          salesRep: {
            id: '$saleRep._id',
            salesRepId: '$saleRep.saleRepId',
            username: '$saleRep.username',
          },
        },
        count: { $sum: 1 },
      })
      .project({
        _id: 0,
        reasonId: '$_id.reason',
        day: '$_id.day',
        salesRep: '$_id.salesRep',
        affectedOutlets: '$count',
      })
      .sort({ 'salesRep.username': 1 })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: dto.skip ? +dto.skip : 0,
          },
          {
            $limit: dto.limit ? +dto.limit : 10,
          },
        ],
      });

    const missReasons = await this.missReasonsService.getMissReasonByIds(data.map((item) => item.reasonId));
    const missReasonsMap = missReasons.reduce((pre, curr) => ({ ...pre, [String(curr._id)]: curr }), {});

    const totalItem = totalRecords.length ? totalRecords[0].total : 0;
    const formattedData = data.map(({ reasonId, ...item }) => {
      const missReason = missReasonsMap[String(reasonId)];
      if (!missReason) {
        return {
          ...item,
          reason: '',
        };
      }
      const { reason } = this.missReasonsService.getMissReasonTranslation(missReason.translations || [], i18n.lang);

      return {
        ...item,
        reason,
      };
    });

    return [formattedData, totalItem];
  }

  async getAbsencesOverview(dto: GetAbsencesOverviewDto) {
    const fromDate = moment(dto.month).tz(process.env.TZ).startOf('month').toDate();
    const toDate = moment(dto.month).tz(process.env.TZ).endOf('month').toDate();

    const query = await this.distributorUserRelationModel.find({
      distributor: new Types.ObjectId(dto.distributorId),
      user: { $exists: true, $ne: null },
    });

    const salesRepIds = dto.salesRepIds?.length ? dto.salesRepIds : query.map((item) => item.user.toString());

    const data = await this.outletJourneyPlanningModel
      .aggregate()
      .match({
        missedReason: { $ne: null },
        visitStatus: { $ne: VisitStatus.COMPLETED },
        saleRep: {
          $in: salesRepIds.map((item) => new Types.ObjectId(item)),
        },
        $or: [
          { day: { $gte: fromDate, $lte: toDate }, rescheduled: false },
          { rescheduledDay: { $gte: fromDate, $lte: toDate }, rescheduled: true },
        ],
      })
      .lookup({
        localField: 'missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'missedReason',
      })
      .unwind({
        path: '$missedReason',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'missedReason.controllable': false,
      })
      .project({
        day: { $ifNull: ['$rescheduledDay', '$day'] },
      });

    const res = data.reduce(
      (pre, curr) => ({
        ...pre,
        [moment(curr.day).tz(process.env.TZ).format('DD/MM/YYYY')]: 1,
      }),
      {},
    );

    return res;
  }

  async cancelAbsence(userId: string, dto: CancelAbsencesDto, i18n: I18nContext) {
    const date = moment(dto.day, 'DD/MM/YYYY');
    if (date.endOf('day').isBefore(moment())) {
      throw new BadRequestException(i18n.t('absence.error.cancelAbsenceInPast'));
    }

    await this.outletJourneyPlanningModel.updateMany(
      {
        saleRep: new mongoose.Types.ObjectId(userId),
        missedReason: new mongoose.Types.ObjectId(dto.reasonId),
        $or: [
          {
            rescheduled: false,
            day: {
              $gte: date.startOf('date').toDate(),
              $lte: date.endOf('date').toDate(),
            },
          },
          {
            rescheduled: true,
            rescheduledDay: {
              $gte: date.startOf('date').toDate(),
              $lte: date.endOf('date').toDate(),
            },
          },
        ],
      },
      {
        visitStatus: VisitStatus.START_VISIT,
        $unset: {
          missedReason: '',
          outletReport: 1,
        },
      },
    );

    return true;
  }
}
