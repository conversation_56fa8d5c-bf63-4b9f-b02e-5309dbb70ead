import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class AutoCompleteDto {
  @ApiProperty({ required: true })
  @Type(() => String)
  input: string;
}

export class GeocodeAddressDto {
  @ApiProperty({ required: true })
  @Type(() => String)
  address: string;
}

export class GeocodeLatLngDto {
  @ApiProperty({ required: true })
  @Type(() => Number)
  lat?: number;

  @ApiProperty({ required: true })
  @Type(() => Number)
  lng?: number;
}
