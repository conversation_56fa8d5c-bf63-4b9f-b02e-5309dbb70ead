import { Type } from 'class-transformer';
import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';

export class AutoCompleteDto {
  @ApiModelProperty({ required: true })
  @Type(() => String)
  input: string;
}

export class GeocodeAddressDto {
  @ApiModelProperty({ required: true })
  @Type(() => String)
  address: string;
}

export class GeocodeLatLngDto {
  @ApiModelProperty({ required: true })
  @Type(() => Number)
  lat?: number;

  @ApiModelProperty({ required: true })
  @Type(() => Number)
  lng?: number;
}
