import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types, Schema as MSchema } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { SolaceQueue } from './solace-queue.schema';
import { SolaceQueueExecutionStatus } from '../enums/solace-queue-execution-status.enum';

export type SolaceQueueExecutionDocument = SolaceQueueExecution & Document;

@Schema({
  timestamps: true,
  versionKey: false,
})
export class SolaceQueueExecution extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: SolaceQueue.name })
  queue: SolaceQueue;

  @Prop({
    enum: SolaceQueueExecutionStatus,
  })
  status: SolaceQueueExecutionStatus;

  @Prop({
    type: MSchema.Types.Mixed,
  })
  error: any;
}

export const SolaceQueueExecutionSchema = SchemaFactory.createForClass(SolaceQueueExecution);
