import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MSchema } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { SolaceQueueType } from '../enums/solace-queue-type.enum';

export type SolaceQueueDocument = SolaceQueue & Document;

@Schema({
  timestamps: true,
  versionKey: false,
})
export class SolaceQueue extends BaseSchema {
  @Prop()
  rawData: string;

  @Prop({ type: MSchema.Types.Mixed, nullable: true })
  data: object;

  @Prop({ enum: SolaceQueueType })
  type: SolaceQueueType;
}

export const SolaceQueueSchema = SchemaFactory.createForClass(SolaceQueue);
