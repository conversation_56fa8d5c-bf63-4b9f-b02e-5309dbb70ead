import { CACHE_MANAGER, Inject, Injectable, Request } from '@nestjs/common';
import { Client, TrafficModel, TransitRoutingPreference, UnitSystem } from '@googlemaps/google-maps-services-js';
import { TravelMode } from '@googlemaps/google-maps-services-js/dist/common';
import { base64Encrypt, createUniqueCode, getLangCodeByOpCo, isValidCoordString, printLog, sleep, sortWaypoints } from '../utils';
import { UserActionsService } from '../users/services/user-actions.service';
import { NJRS_REQUEST } from 'nj-request-scope';
import { ConstantCommons } from '../utils/constants';
import { AutoCompleteDto, GeocodeAddressDto, GeocodeLatLngDto } from './dtos/geo.dto';
import { Cache } from 'cache-manager';

@Injectable()
export class GoogleMapsService {
  private googleMapsClient: Client;
  private promiseLocks: Map<string, { locked: boolean }> = new Map();

  constructor(
    private readonly userActionsService: UserActionsService,
    @Inject(NJRS_REQUEST) private readonly request: Request,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    this.googleMapsClient = new Client({});
  }

  getAccessToken() {
    try {
      return (this.request?.headers as any)?.authorization?.split(' ')[1];
    } catch (e) {
      return null;
    }
  }

  /**
   *
   * @param array
   * @param size
   */
  public chunkArray(array: any[], size: number): any[][] {
    if (array.length <= size) {
      return [array];
    }

    let blockSize = size;
    let remainder = array.length % blockSize;

    while (remainder > 0 && remainder < 2) {
      blockSize--;
      remainder = array.length % blockSize;
    }

    const chunkedArr = [];
    let i = 0;
    while (i < array.length) {
      const start = i === 0 ? i : i - 1;
      const end = i + blockSize > array.length ? array.length : i + blockSize;
      chunkedArr.push(array.slice(start, end));
      i = end; // Move i to the start of the next chunk
    }
    return chunkedArr;
  }

  /**
   *
   * @param leg
   * @param priority
   */
  private extractLegData = (leg: any, priority: number) => {
    return {
      startLatLong: leg.start_location,
      endLatLong: leg.end_location,
      startAddress: leg.start_address,
      endAddress: leg.end_address,
      distance: leg.distance,
      priority,
    };
  };

  /**
   *
   * @param directionsResponse
   */
  private processDirections = (directionsResponse: any) => {
    let priority = 0;
    return directionsResponse
      .map((response: any) => response.data.routes.reduce((legsData: any, route: any) => [...legsData, ...route.legs.map((leg: any) => this.extractLegData(leg, ++priority))], []))
      .flat();
  };

  /**
   * example:
   * const origin = '21.037151703533286, 105.83434648698645'; // Sale
   * const waypoints = [
   *       '21.031989831646467, 105.82911595860564', //Outlet 1
   *       '21.03533676996992, 105.81850354095964',  //Outlet 2
   *       '21.024942328024764, 105.80931379357808', //Outlet 3 ...
   *       '21.020341317611546, 105.82523172553182',
   *       '21.029688530188103, 105.83584164904389',
   *       '21.03003746095795, 105.84929227390423',
   *       '21.013785563729336, 105.83150901958602',
   *       '21.010438173948202, 105.84129715010407',
   *       '21.00081187835161, 105.82941807117601',
   *     ];
   *     const directionsResponse = await this.googleMapsService.getDirections(origin, destinations);
   * @param origin
   * @param destinations
   */
  public async getDirections(origin: string, destinations: string[] | any): Promise<any[]> {
    if (!isValidCoordString(origin)) {
      return null;
    }
    destinations = sortWaypoints(origin, destinations.filter(isValidCoordString));
    const waypoints = destinations?.length === 1 ? [origin, ...destinations, origin] : [origin, ...destinations];
    return await this.getDirectionsForBlocks(waypoints);
  }

  public async getDirection(origin: string, destination: string): Promise<any> {
    const waypoints = [origin, destination, origin];
    return await this.getDirectionsForBlocks(waypoints);
  }

  /**
   * example:
   * const waypoints = [
   *       '21.037151703533286, 105.83434648698645', // Sale
   *       '21.031989831646467, 105.82911595860564', //Outlet 1
   *       '21.03533676996992, 105.81850354095964',  //Outlet 2
   *       '21.024942328024764, 105.80931379357808', //Outlet 3 ...
   *       '21.020341317611546, 105.82523172553182',
   *       '21.029688530188103, 105.83584164904389',
   *       '21.03003746095795, 105.84929227390423',
   *       '21.013785563729336, 105.83150901958602',
   *       '21.010438173948202, 105.84129715010407',
   *       '21.00081187835161, 105.82941807117601',
   *     ];
   *     const directionsResponse = await this.googleMapsService.getDirectionsForBlocks(waypoints);
   * @param waypoints
   */
  public async getDirectionsForBlocks(waypoints: string[]): Promise<any[]> {
    try {
      if (!process.env.GOOGLE_MAPS_API_KEY) return null;

      const chunkSize = 24; // Google Rule: https://developers.google.com/maps/documentation/javascript/directions#waypoint-limits
      const waypointBlocks = this.chunkArray(waypoints, chunkSize);

      const directions = await Promise.all(
        waypointBlocks.map((block) => {
          const origin = block[0];
          const destination = block[block.length - 1];
          return this.googleMapsClient.directions({
            params: {
              origin: origin,
              destination: destination,
              waypoints: block.slice(1, -1),
              key: process.env.GOOGLE_MAPS_API_KEY,
              mode: TravelMode.driving,
              optimize: true,
              departure_time: new Date(),
              traffic_model: TrafficModel.optimistic,
              alternatives: true,
              transit_routing_preference: TransitRoutingPreference.less_walking,
              units: UnitSystem.metric,
            },
            timeout: 20000,
          });
        }),
      );

      //Action Log: Google Maps Api call
      this.userActionsService
        .saveActionLog(this.getAccessToken(), ConstantCommons.GET_DESTINATION_ROUTER, {
          request: { method: 'POST', params: null, query: null, body: waypoints },
        })
        .then();

      return this.processDirections(directions);
    } catch (e) {
      printLog(e);
      return null;
    }
  }

  public getData = async (key: string, language: string, payload: GeocodeAddressDto | GeocodeLatLngDto | AutoCompleteDto | any, currentUserId: string): Promise<any> => {
    let cacheKey = '',
      result = null;
    try {
      switch (key) {
        case ConstantCommons.GOOGLE_GET_AUTO_COMPLETE:
          cacheKey = base64Encrypt(`${payload.input}_${currentUserId}_${language}_${process.env.NODE_ENV}`);
          if (this.promiseLocks.has(cacheKey)) {
            await sleep(ConstantCommons.GOOGLE_GET_GEOCODE_TIME_DELAY);
          }
          result = (await this.cacheManager.get(cacheKey)) || (await this.getAutocomplete(payload, language, currentUserId));
          break;
        case ConstantCommons.GOOGLE_GET_GEOCODE_BY_ADDRESS:
          cacheKey = base64Encrypt(`${payload.address}_${currentUserId}_${language}_${process.env.NODE_ENV}`);
          if (this.promiseLocks.has(cacheKey)) {
            await sleep(ConstantCommons.GOOGLE_GET_GEOCODE_TIME_DELAY);
          }
          result = (await this.cacheManager.get(cacheKey)) || (await this.getGeocodeByAddress(payload, language));
          break;
        case ConstantCommons.GOOGLE_GET_GEOCODE_BY_LAT_LNG:
          cacheKey = base64Encrypt(`${payload.lat}_${payload.lng}_${currentUserId}_${language}_${process.env.NODE_ENV}`);
          if (this.promiseLocks.has(cacheKey)) {
            await sleep(ConstantCommons.GOOGLE_GET_GEOCODE_TIME_DELAY);
          }
          result = (await this.cacheManager.get(cacheKey)) || (await this.getGeocodeByLatLng(payload, language));
          break;

        default:
          result = null;
      }
      if (result) {
        this.cacheManager.set(cacheKey, result, { ttl: 60 }).catch((error) => {
          printLog('Error setting session token to cache:', error);
        });
      }
      //Action Log: Google Maps Api call
      this.userActionsService
        .saveActionLog(this.getAccessToken(), key, {
          request: { method: 'GET', params: payload, query: null, body: payload },
        })
        .then();
      this.promiseLocks.set(cacheKey, { locked: true });
      return result;
    } catch (e) {
      this.promiseLocks.delete(cacheKey);
      throw e;
    } finally {
      this.promiseLocks.delete(cacheKey);
    }
  };

  public async getAutocomplete(autoCompleteDto: AutoCompleteDto, language: string, currentUserId: string): Promise<any> {
    try {
      const response = await this.googleMapsClient.placeAutocomplete({
        params: {
          input: autoCompleteDto.input,
          key: process.env.GOOGLE_MAPS_API_KEY,
          language: getLangCodeByOpCo(process.env.OPCO.toUpperCase()),
          sessiontoken: await this.getSessionToken(`${currentUserId}_${language}`),
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(`Google Maps API Error: ${error.message}`);
    }
  }

  public async getGeocodeByAddress(geocodeAddressDto: GeocodeAddressDto, language: string): Promise<any> {
    try {
      const response = await this.googleMapsClient.geocode({
        params: {
          address: geocodeAddressDto.address,
          key: process.env.GOOGLE_MAPS_API_KEY,
          region: getLangCodeByOpCo(process.env.OPCO.toUpperCase()),
          language,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(`Google Maps API Error: ${error.message}`);
    }
  }

  public async getGeocodeByLatLng(geocodeLatLngDto: GeocodeLatLngDto, language: any): Promise<any> {
    try {
      const response = await this.googleMapsClient.reverseGeocode({
        params: {
          latlng: `${geocodeLatLngDto.lat},${geocodeLatLngDto.lng}`,
          key: process.env.GOOGLE_MAPS_API_KEY,
          language: getLangCodeByOpCo(process.env.OPCO.toUpperCase()) || language,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(`Google Maps API Error: ${error.message}`);
    }
  }

  private getSessionToken = async (key: string): Promise<string> => {
    const token = await this.cacheManager.get(key);
    if (token) {
      return token.toString();
    }
    const sToken = createUniqueCode(null, 32);
    this.cacheManager.set(`${ConstantCommons.GOOGLE_SESSION_TOKEN}_${key}`, sToken, { ttl: 150 }).catch((error) => {
      printLog('Error setting session token to cache:', error);
    });
    return sToken;
  };
}
