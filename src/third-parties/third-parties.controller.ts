import { Body, Controller, Param, Post, UseGuards } from '@nestjs/common';
import { SolaceService } from './services/solace.service';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Roles } from '../shared/decorator/roles.decorator';
import { ConstantRoles } from '../utils/constants/role';
import { GoogleMapsService } from './google-maps-services';
import { I18n, I18nContext } from 'nestjs-i18n';
import { AutoCompleteDto, GeocodeAddressDto, GeocodeLatLngDto } from './dtos/geo.dto';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiException } from '../shared/api-exception.model';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { ConstantCommons } from '../utils/constants';

@ApiTags('Third Parties')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/third-parties')
export class ThirdPartiesController {
  constructor(private readonly solaceService: SolaceService, private readonly googleMapsService: GoogleMapsService) {}

  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.SUPER_USER)
  @ApiOperation({
    summary: 'produceMessage',
  })
  @ApiBadRequestResponse({ type: ApiException })
  @Post('solace/queues/:queueName')
  produceMessage(@Param('queueName') queueName: string, @Body() data: any) {
    this.solaceService.produceMessage({ queueName, data });
    return true;
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.SUPER_USER)
  @ApiOperation({
    summary: 'getAutocomplete',
  })
  @ApiBadRequestResponse({ type: ApiException })
  @Post('place/autocomplete/json')
  getAutocomplete(@Body() autoCompleteDto: AutoCompleteDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    return this.googleMapsService.getData(ConstantCommons.GOOGLE_GET_AUTO_COMPLETE, i18n.lang, autoCompleteDto, currentUser._id.toString());
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.SUPER_USER)
  @ApiOperation({
    summary: 'getGeocodeByAddress',
  })
  @ApiBadRequestResponse({ type: ApiException })
  @Post('geocode/address/json')
  getGeocodeByAddress(@Body() geocodeAddressDto: GeocodeAddressDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    return this.googleMapsService.getData(ConstantCommons.GOOGLE_GET_GEOCODE_BY_ADDRESS, i18n.lang, geocodeAddressDto, currentUser._id.toString());
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.SUPER_USER)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'getGeocodeByLatLng',
  })
  @ApiBadRequestResponse({ type: ApiException })
  @Post('geocode/latlng/json')
  getGeocodeByLatLng(@Body() geocodeLatLngDto: GeocodeLatLngDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    return this.googleMapsService.getData(ConstantCommons.GOOGLE_GET_GEOCODE_BY_LAT_LNG, i18n.lang, geocodeLatLngDto, currentUser._id.toString());
  }
}
