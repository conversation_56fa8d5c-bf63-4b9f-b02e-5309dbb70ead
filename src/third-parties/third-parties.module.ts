import { forwardRef, Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { SettingsModule } from 'src/settings/settings.module';
import { GoogleMapsService } from './google-maps-services';
import { UsersModule } from '../users/users.module';
import { RequestScopeModule } from 'nj-request-scope';
import { SolaceQueueExecution, SolaceQueueExecutionSchema } from './schemas/solace-queue-execution.schema';
import { SolaceQueue, SolaceQueueSchema } from './schemas/solace-queue.schema';
import { SolaceService } from './services/solace.service';
import { SolaceTopicsService } from './services/solace-topics.service';
import { SolaceQueuesService } from './services/solace-queues.service';
import { SolaceDSRService } from './services/solace-dsr.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Distributor, DistributorSchema, DistributorUserRelation, DistributorUserRelationSchema } from 'src/distributor/schemas';
import { User, UserSchema } from 'src/users/schemas/user.schema';
import { SaleRepFfcStore, SaleRepFfcStoreSchema } from 'src/sale-rep/schemas';
import { Outlet, OutletSchema } from 'src/outlets/schemas/outlet.schema';
import { SaleRepOutletRelation, SaleRepOutletRelationSchema } from 'src/outlets/schemas/sale-rep-outlet-relation.schema';
import { DsrTargetsModule } from 'src/dsr-targets/dsr-targets.module';
import { ExternalModule } from 'src/external/external.module';
import { SmsService } from './services/sms.service';
import { ThirdPartiesController } from './third-parties.controller';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: SolaceQueue.name,
        schema: SolaceQueueSchema,
      },
      {
        name: SolaceQueueExecution.name,
        schema: SolaceQueueExecutionSchema,
      },
      {
        name: Distributor.name,
        schema: DistributorSchema,
      },
      {
        name: User.name,
        schema: UserSchema,
      },
      {
        name: DistributorUserRelation.name,
        schema: DistributorUserRelationSchema,
      },
      {
        name: SaleRepFfcStore.name,
        schema: SaleRepFfcStoreSchema,
      },
      {
        name: Outlet.name,
        schema: OutletSchema,
      },
      {
        name: SaleRepOutletRelation.name,
        schema: SaleRepOutletRelationSchema,
      },
    ]),
    HttpModule,
    ConfigModule,
    SettingsModule,
    DsrTargetsModule,
    forwardRef(() => AuthModule),
    forwardRef(() => UsersModule),
    RequestScopeModule,
    forwardRef(() => ExternalModule),
  ],
  controllers: [ThirdPartiesController],
  providers: [SmsService, GoogleMapsService, SolaceService, SolaceTopicsService, SolaceQueuesService, SolaceDSRService],
  exports: [SmsService, GoogleMapsService, SolaceService],
})
export class ThirdPartiesModule {}
