/* eslint-disable @typescript-eslint/no-empty-function */
import { Injectable } from '@nestjs/common';
import * as solace from 'solclientjs';
import { LogsService } from '../../settings/logs.service';

@Injectable()
export class SolaceQueuesService {
  constructor(private readonly _logsService: LogsService) {
    const factoryProps = new solace.SolclientFactoryProperties();
    factoryProps.profile = solace.SolclientFactoryProfiles.version10;
    solace.SolclientFactory.init(factoryProps);

    solace.SolclientFactory.setLogLevel(solace.LogLevel.DEBUG);
  }

  public createAndStartSession({
    sessionProperties = {},
    onUpNotice,
    retryCount = 3,
  }: {
    sessionProperties?: Omit<solace.SessionProperties, 'url' | 'vpnName' | 'userName' | 'password'>;
    onUpNotice: () => void;
    retryCount?: number;
  }) {
    try {
      const session = solace.SolclientFactory.createSession({
        url: process.env.SOLACE_URL,
        vpnName: process.env.SOLACE_VPN_NAME,
        userName: process.env.SOLACE_USERNAME,
        password: process.env.SOLACE_PASSWORD,
        ...sessionProperties,
      });

      session.on(solace.SessionEventCode.UP_NOTICE, () => {
        console.log('=== Successfully connected and ready to start the message consumer. ===');
        onUpNotice();

        retryCount = 3;
      });

      session.on(solace.SessionEventCode.CONNECT_FAILED_ERROR, (sessionEvent) => {
        console.log(`Connection failed: ${sessionEvent?.reason}`);
        this._logsService
          .saveLogByKey('solace-queue-connection', {
            userId: null,
            isUserAdmin: false,
            key: 'solace-queue-connection',
            feature: 'solace-queue-connection',
            code: 500,
            request: { retryCount },
            response: `CONNECT_FAILED_ERROR: Connection failed: ${sessionEvent?.reason}`,
          })
          .then();

        if (retryCount > 0) {
          console.log(`Retrying... (${retryCount} attempts left)`);
          setTimeout(() => {
            this.createAndStartSession({ sessionProperties, onUpNotice, retryCount: retryCount - 1 });
          }, 3000);
        } else {
          console.error('Max retries reached. Connection failed.');
        }
      });
      session.on(solace.SessionEventCode.DISCONNECTED, function () {
        console.log('Disconnected.');
        if (session !== null) {
          session.dispose();
        }
        this._logsService
          .saveLogByKey('solace-queue-connection', {
            userId: null,
            isUserAdmin: false,
            key: 'solace-queue-connection',
            feature: 'solace-queue-connection',
            code: 500,
            request: { retryCount },
            response: `SessionEventCode.DISCONNECTED`,
          })
          .then();

        if (retryCount > 0) {
          console.log(`Reconnecting... (${retryCount} attempts left)`);
          setTimeout(() => {
            this.createAndStartSession({ sessionProperties, onUpNotice, retryCount: retryCount - 1 });
          }, 3000);
        } else {
          console.error('Max retries reached. Could not reconnect.');
        }
      });

      session.connect();
      return session;
    } catch (error) {
      console.error('Session connect failed', error.toString());
      this._logsService
        .saveLogByKey('solace-queue-connection', {
          userId: null,
          isUserAdmin: false,
          key: 'solace-queue-connection',
          feature: 'solace-queue-connection',
          code: 500,
          request: { retryCount },
          response: error?.message,
        })
        .then();
      if (retryCount > 0) {
        console.log(`Retrying... (${retryCount} attempts left)`);
        setTimeout(() => {
          this.createAndStartSession({ sessionProperties, onUpNotice, retryCount: retryCount - 1 });
        }, 3000);
      }
    }
  }

  private startConsume({ queueName, session, onMessage }: { queueName: string; session: solace.Session; onMessage: (data: any) => void }) {
    if (!session) {
      console.error('Cannot start the queue consumer because not connected to Solace PubSub+ Event Broker.');
      return;
    }

    try {
      const messageConsumer = session.createMessageConsumer({
        queueDescriptor: { name: queueName, type: solace.QueueType.QUEUE },
        acknowledgeMode: solace.MessageConsumerAcknowledgeMode.CLIENT, // Enabling Client ack
        createIfMissing: true, // Create queue if not exists
      });

      messageConsumer.on(solace.MessageConsumerEventName.UP, function () {
        console.log('=== Ready to receive messages. ===');
      });
      messageConsumer.on(solace.MessageConsumerEventName.CONNECT_FAILED_ERROR, function () {
        console.log(`=== Error: the message consumer could not bind to queue "${queueName}" ===`);
        console.log('Ensure this queue exists on the message router vpn');
      });
      messageConsumer.on(solace.MessageConsumerEventName.DOWN, function () {
        console.log('=== The message consumer is now down ===');
      });
      messageConsumer.on(solace.MessageConsumerEventName.DOWN_ERROR, function () {
        console.log('=== An error happened, the message consumer is down ===');
      });

      messageConsumer.on(solace.MessageConsumerEventName.MESSAGE, function (message) {
        onMessage(message.getBinaryAttachment());
        // Need to explicitly ack otherwise it will not be deleted from the message router
        message.acknowledge();
      });

      messageConsumer.connect();
    } catch (error) {
      console.error('Start consume message failed');
      console.error(error.toString());
    }
  }

  private startProduce({ queueName, session, data }: { queueName: string; session: solace.Session; data: any }) {
    if (!session) {
      console.error('Cannot start the queue producer because not connected to Solace PubSub+ Event Broker.');
      return;
    }

    try {
      const message = solace.SolclientFactory.createMessage();

      message.setDestination(solace.SolclientFactory.createDurableQueueDestination(queueName));
      message.setBinaryAttachment(JSON.stringify(data));
      message.setDeliveryMode(solace.MessageDeliveryModeType.PERSISTENT);

      session.send(message);
    } catch (error) {
      console.error('Start produce message failed');
      console.error(error.toString());
    }
  }

  createMessageConsumer({ queueName, onMessage }: { queueName: string; onMessage: (data: any) => Promise<void> }) {
    const session = this.createAndStartSession({
      onUpNotice: () => {
        this.startConsume({ queueName, session, onMessage });
      },
    });
  }

  createMessageProducer({ queueName, data }: { queueName: string; data: any }) {
    const session = this.createAndStartSession({
      onUpNotice: () => {
        this.startProduce({ queueName, session, data });
      },
      sessionProperties: {
        publisherProperties: {
          enabled: true,
          acknowledgeMode: solace.MessagePublisherAcknowledgeMode.PER_MESSAGE,
        },
      },
    });
  }
}
