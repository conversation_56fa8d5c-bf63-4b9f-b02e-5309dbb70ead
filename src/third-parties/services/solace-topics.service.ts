/* eslint-disable @typescript-eslint/no-empty-function */
import { Injectable } from '@nestjs/common';
import * as solace from 'solclientjs';

@Injectable()
export class SolaceTopicsService {
  constructor() {
    const factoryProps = new solace.SolclientFactoryProperties();
    factoryProps.profile = solace.SolclientFactoryProfiles.version10;
    solace.SolclientFactory.init(factoryProps);

    solace.SolclientFactory.setLogLevel(solace.LogLevel.DEBUG);
  }

  createTopicSubscriber({ topic, onMessage }: { topic: string; onMessage: (data: any) => void }) {
    let session: solace.Session;
    let subscribed = false;

    try {
      session = solace.SolclientFactory.createSession({
        url: process.env.SOLACE_URL,
        vpnName: process.env.SOLACE_VPN_NAME,
        userName: process.env.SOLACE_USERNAME,
        password: process.env.SOLACE_PASSWORD,
      });
    } catch (error) {
      console.error('Create session failed', error);
    }

    session.on(solace.SessionEventCode.UP_NOTICE, function () {
      if (!session) {
        console.log('No session created');
        return;
      }

      if (subscribed) {
        console.log('Subscribed');
        return;
      }

      try {
        session.subscribe(solace.SolclientFactory.createTopicDestination(topic), true, topic, 10000);
        subscribed = true;
      } catch (error) {
        console.error('Subscribe failed', error);
      }
    });

    // define message event listener
    session.on(solace.SessionEventCode.MESSAGE, function (message) {
      onMessage(message.getBinaryAttachment());
    });

    console.log('Start subscribe topic');
    // connect the session
    try {
      session.connect();
    } catch (error) {
      console.error('Connect failed', error);
    }
  }
}
