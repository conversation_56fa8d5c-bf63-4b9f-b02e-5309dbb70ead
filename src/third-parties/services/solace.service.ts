/* eslint-disable @typescript-eslint/no-empty-function */
import { Injectable } from '@nestjs/common';
import { SolaceQueuesService } from './solace-queues.service';
import { SolaceDSRService } from './solace-dsr.service';
import { InjectModel } from '@nestjs/mongoose';
import { SolaceQueue, SolaceQueueDocument } from '../schemas/solace-queue.schema';
import { Model } from 'mongoose';
import { SolaceQueueExecution, SolaceQueueExecutionDocument } from '../schemas/solace-queue-execution.schema';
import { SolaceQueueExecutionStatus } from '../enums/solace-queue-execution-status.enum';
import { ConfigService } from '@nestjs/config';
import { SolaceQueueType } from '../enums/solace-queue-type.enum';
import { SolaceTopicsService } from './solace-topics.service';
import { printLog, safeJsonParse, sleep } from '../../utils';
import { OmsService } from 'src/external/services/oms.service';
import { Outlet, OutletDocument } from 'src/outlets/schemas/outlet.schema';
import * as moment from 'moment-timezone';

@Injectable()
export class SolaceService {
  constructor(
    private readonly queuesService: SolaceQueuesService,
    private readonly topicService: SolaceTopicsService,
    private readonly solaceDSRService: SolaceDSRService,
    private readonly configService: ConfigService,
    private readonly omsService: OmsService,
    @InjectModel(SolaceQueue.name)
    private readonly solaceQueueModel: Model<SolaceQueueDocument>,
    @InjectModel(SolaceQueueExecution.name)
    private readonly solaceQueueExecutionModel: Model<SolaceQueueExecutionDocument>,
    @InjectModel(Outlet.name)
    private readonly outletModel: Model<OutletDocument>,
  ) {}

  private getSolaceQueueType(queueName: string) {
    switch (queueName) {
      case this.configService.get('SOLACE_OUTLET_QUEUE_NAME'): {
        return SolaceQueueType.OUTLET;
      }
      case this.configService.get('SOLACE_SALES_REP_QUEUE_NAME'): {
        return SolaceQueueType.SALES_REP;
      }
    }
  }

  subscribeTopic({ topic }: { topic: string }) {
    this.topicService.createTopicSubscriber({
      topic,
      onMessage: async (data) => {
        printLog(`Received queue at: ${new Date().toISOString()}`);
        printLog('Queue data:', data);
        const jsonData = safeJsonParse(data);
        const solaceQueue = await this.solaceQueueModel.create({ rawData: data, data: jsonData, type: this.getSolaceQueueType(topic) });
        try {
          if (jsonData.businessPartner) {
            await this.solaceDSRService.handleOutletMessageQueue(jsonData);
          } else if (jsonData.businessPartnerContactPerson) {
            await this.solaceDSRService.handleSalesRepMessageQueue(jsonData);
          } else {
            return;
          }

          await this.solaceQueueExecutionModel.create({
            queue: solaceQueue._id,
            status: SolaceQueueExecutionStatus.SUCCEEDED,
          });

          //this.updateOutletChannelAndSubChannel(jsonData).then((r) => printLog(r));
        } catch (error) {
          printLog('Can not handle message queue', error);
          await this.solaceQueueExecutionModel.create({
            queue: solaceQueue._id,
            status: SolaceQueueExecutionStatus.FAILED,
            error,
          });
        }
      },
    });
  }

  consumeMessage({ queueName }: { queueName: string }) {
    this.queuesService.createMessageConsumer({
      queueName,
      onMessage: async (data) => {
        printLog(`Received queue at: ${new Date().toISOString()}`);
        printLog('Queue data:', data);
        const jsonData = safeJsonParse(data);
        const solaceQueue = await this.solaceQueueModel.create({ rawData: data, data: jsonData, type: this.getSolaceQueueType(queueName) });
        try {
          switch (queueName) {
            case this.configService.get('SOLACE_OUTLET_QUEUE_NAME'): {
              await this.solaceDSRService.handleOutletMessageQueue(jsonData);
              break;
            }
            case this.configService.get('SOLACE_SALES_REP_QUEUE_NAME'): {
              await this.solaceDSRService.handleSalesRepMessageQueue(jsonData);
              break;
            }
          }

          await this.solaceQueueExecutionModel.create({
            queue: solaceQueue._id,
            status: SolaceQueueExecutionStatus.SUCCEEDED,
          });

          //this.updateOutletChannelAndSubChannel(jsonData).then((r) => printLog(r));
        } catch (error) {
          printLog('Can not handle message queue', error);
          await this.solaceQueueExecutionModel.create({
            queue: solaceQueue._id,
            status: SolaceQueueExecutionStatus.FAILED,
            error,
          });
        }
      },
    });
  }

  produceMessage({ queueName, data }: { queueName: string; data: any }) {
    this.queuesService.createPushMessageProducer({ topicName: queueName, data });
  }

  async updateOutletChannelAndSubChannel(data: Record<'businessPartner', any[]>) {
    printLog('Start to get outlet data', data);
    try {
      const listUcc = data.businessPartner.map((item) => item.businessPartnerKey);
      const outlets = await this.omsService.getOutletChannelInfo(listUcc);
      const outletChannelAndSubChannels = outlets
        .filter((item) => item.outletChannels.length || item.outletSubChannels.length)
        .map((item) => {
          const ucc = item.external_id;
          const customerType = item?.customer_type?.trim();
          const channel = item.outletChannels.sort((first, second) => +new Date(second.updated_at) - +new Date(first.updated_at))?.[0]?.channel?.name?.trim();
          const subChannel = item.outletSubChannels.sort((first, second) => +new Date(second.updated_at) - +new Date(first.updated_at))?.[0]?.subChannel?.name?.trim();

          return { ucc, channel, customerType, subChannel };
        });

      printLog('outletChannelAndSubChannels:', outletChannelAndSubChannels);

      return await this.outletModel.bulkWrite(
        outletChannelAndSubChannels.map((item) => {
          const payload: Record<string, string> = {};
          if (item.customerType) {
            payload.customerType = item.customerType;
          }
          if (item.channel) {
            payload.channel = item.channel;
          }
          if (item.subChannel) {
            payload.subChannel = item.subChannel;
          }

          return {
            updateOne: {
              filter: { ucc: item.ucc },
              update: payload,
            },
          };
        }),
      );
    } catch (e) {
      printLog('Error when update outlet channels', e);
    }
  }

  async clearOldData(): Promise<void> {
    try {
      await this.clearOldQueueExecutions();
      await this.clearOldQueues();
      await this.cleanupOrphanedExecutions();
    } catch (e) {}
  }

  async clearOldQueues(): Promise<void> {
    try {
      const time = moment().subtract(2, 'month').toISOString();
      const batchSize = 200;
      let count = 0;
      while (true) {
        // Find a batch of IDs to delete
        const oldDocs = await this.solaceQueueModel
          .find({ createdAt: { $lte: time } })
          .select('_id')
          .limit(batchSize)
          .lean();

        if (!oldDocs?.length || ++count > batchSize * 2000) break;

        const ids = oldDocs.map((doc) => doc._id);
        await this.solaceQueueModel.deleteMany({ _id: { $in: ids } });
        await sleep(500);
      }
    } catch (e) {}
  }

  async clearOldQueueExecutions(): Promise<void> {
    try {
      const time = moment().subtract(2, 'month').toISOString();
      const batchSize = 200;
      let count = 0;
      while (true) {
        // Find a batch of IDs to delete
        const oldDocs = await this.solaceQueueExecutionModel
          .find({ createdAt: { $lte: time } })
          .select('_id')
          .limit(batchSize)
          .lean();

        if (!oldDocs?.length || ++count > batchSize * 2000) break;

        const ids = oldDocs.map((doc) => doc._id);
        await this.solaceQueueExecutionModel.deleteMany({ _id: { $in: ids } });
        await sleep(500);
      }
    } catch (e) {}
  }

  async cleanupOrphanedExecutions(): Promise<void> {
    try {
      const oldestQueue = await this.solaceQueueModel.findOne({}).sort({ createdAt: 1 }).select('createdAt').lean();

      if (!oldestQueue) {
        printLog('No queues found in database');
        return;
      }

      const oldestDate = oldestQueue.createdAt;
      printLog(`Oldest queue date: ${oldestDate}`);

      const batchSize = 200;
      let totalDeleted = 0;
      let count = 0;

      while (true) {
        const oldExecutions = await this.solaceQueueExecutionModel
          .find({ createdAt: { $lte: oldestDate } })
          .select('_id')
          .limit(batchSize)
          .lean();

        if (!oldExecutions?.length || ++count > batchSize * 2000) break;

        const executionIds = oldExecutions.map((exec) => exec._id);

        const result = await this.solaceQueueExecutionModel.deleteMany({
          _id: { $in: executionIds },
        });

        totalDeleted += result.deletedCount || 0;
        printLog(`Deleted batch of ${result.deletedCount} orphaned executions`);
        await sleep(500);
      }
      printLog(`Total deleted: ${totalDeleted} orphaned executions older than ${oldestDate}`);
    } catch (e) {
      printLog('Error cleaning up orphaned executions', e);
    }
  }
}
