import { HttpService } from '@nestjs/axios';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { LogsService } from 'src/settings/logs.service';
import { printLog } from 'src/utils';
import { firstValueFrom } from 'rxjs';
import * as https from 'https';
import { Twilio } from 'twilio';
import { OpCos } from '../../config';

@Injectable()
export class SmsService {
  constructor(
    private readonly httpService: HttpService,
    @Inject(forwardRef(() => LogsService))
    private readonly logsService: LogsService,
  ) {}

  async sendSMS({ message, from, to }: { message: string; from?: string; to: string }) {
    switch (process.env.OPCO) {
      case OpCos.Myanmar:
        return this.smsPoh({
          message,
          from,
          to,
        }).then();

      case OpCos.Cambodia:
        return this.plasGate({
          message,
          from,
          to,
        }).then();

      default:
        return this.twilio({
          message,
          from,
          to,
        }).then();
    }
  }

  async twilio({ message, from, to }: { message: string; from?: string; to: string }) {
    try {
      const accountSid = process.env.TWILIO_ACCOUNT_SID;
      const authToken = process.env.TWILIO_AUTH_TOKEN;
      from = from || process.env.TWILIO_PHONE_NUMBER;
      if (!to || !message || !from) {
        return { success: false, message: "Don't enough params." };
      }
      const client = new Twilio(accountSid, authToken);
      const resMesssage = await client.messages.create({
        body: message,
        from,
        to,
      });
      printLog('twilio:', resMesssage);
      return { success: true, message: '' };
    } catch (error) {
      this.logsService
        ?.saveLogByKey('twilio-send-sms', {
          userId: null,
          isUserAdmin: false,
          key: 'twilio-send-sms',
          feature: 'twilio-send-sms',
          code: 500,
          request: { from, to, message },
          response: error?.message,
        })
        .then()
        .catch();
      printLog('twilio ERROR:', error.message);
      return { success: false, message: error.message };
    }
  }

  async plasGate({ message, from, to }: { message: string; from?: string; to: string }) {
    try {
      const endpointUrl = process.env.PLASGATE_ENDPOINT_URL;
      const privateKey = process.env.PLASGATE_PRIVATE_KEY;
      const xSecret = process.env.PLASGATE_X_SECRET;
      const sender = process.env.PLASGATE_SENDER;
      const response = await firstValueFrom(
        this.httpService.post(
          endpointUrl,
          {
            sender: sender || 'HEINEKEN',
            to: to.replace(/\D/g, ''),
            content: message,
          },
          {
            headers: {
              'X-Secret': xSecret,
              'Content-Type': 'application/json',
            },
            httpsAgent: new https.Agent({
              rejectUnauthorized: false,
            }),
            params: {
              private_key: privateKey,
            },
          },
        ),
      );
      printLog({ message, from, to, response: JSON.stringify(response) });
      return { success: response?.status < 202, message: '' };
    } catch (error) {
      await this.logsService.saveLogByKey('plasgate-send-sms', {
        userId: null,
        isUserAdmin: false,
        key: 'plasgate-send-sms',
        feature: 'plasgate-send-sms',
        code: 500,
        request: { from, to, message },
        response: error?.message,
      });
      printLog('plasGate ERROR:', error.message);
      return { success: false, message: error.message };
    }
  }

  async smsPoh({ message, from, to }: { message: string; from?: string; to: string }) {
    try {
      const endpointUrl = process.env.SMSPOH_ENDPOINT_URL;
      const sender = process.env.SMSPOH_SENDER_NAME;
      const apiKey = process.env.SMSPOH_API_KEY;
      const apiSecret = process.env.SMSPOH_SECRET;
      const token = Buffer.from(`${apiKey}:${apiSecret}`).toString('base64');
      if (!endpointUrl || !sender || !apiKey || !apiSecret) {
        printLog('sendSMS ERROR: credentials is invalid', { endpointUrl, sender, apiKey, apiSecret, token });
        return;
      }

      const response = await firstValueFrom(
        this.httpService.post(
          `${endpointUrl}/send`,
          {
            from: sender,
            to, //: to.replace(/\D/g, ''),
            message,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`,
            },
            httpsAgent: new https.Agent({
              rejectUnauthorized: false,
            }),
          },
        ),
      );
      printLog({ message, from: sender, to, response: { status: response?.status, data: JSON.stringify(response?.data) } });
      return { success: response?.status < 202, message: '' };
    } catch (error) {
      await this.logsService.saveLogByKey('sms-poh-service', {
        userId: null,
        isUserAdmin: false,
        key: 'sms-poh-service',
        feature: 'sms-poh-service',
        code: 500,
        request: { from, to, message },
        response: error?.message,
      });
      printLog('smsPoh ERROR:', error.message);
      return { success: false, message: error.message };
    }
  }
}
