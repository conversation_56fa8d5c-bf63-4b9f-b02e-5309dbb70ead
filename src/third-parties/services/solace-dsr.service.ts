import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { I18nContext, I18nService } from 'nestjs-i18n';
import { Distributor, DistributorDocument, DistributorUserRelation, DistributorUserRelationDocument } from 'src/distributor/schemas';
import { DsrTargetService } from 'src/dsr-targets/services';
import { OutletStatus } from 'src/outlets/enums/outlet-status.enum';
import { Outlet, OutletDocument } from 'src/outlets/schemas/outlet.schema';
import { SaleRepFfcStore, SaleRepFfcStoreDocument } from 'src/sale-rep/schemas';
import { SharedEvent } from 'src/shared/events/event/shared.event';
import { SalesRepStatus } from 'src/users/enums';
import { User, UserDocument } from 'src/users/schemas/user.schema';
import { UsersService } from 'src/users/services/users.service';
import { ConstantRoles } from 'src/utils/constants/role';
import { ConstantUser } from 'src/utils/constants/user';
import { ConstantEventName } from 'src/utils/events';
import { parsePhoneNumber } from 'libphonenumber-js';
import { SaleRepOutletRelation, SaleRepOutletRelationDocument } from 'src/outlets/schemas/sale-rep-outlet-relation.schema';
import { CommunicationType, CommunicationTypeMobi } from '../enums/solace-queue-data.enum';
import { GeographicalLocationRole, RelationType, Status } from '../enums/solace-queue-type.enum';
import { ConfigService } from '@nestjs/config';
import { UserDetailService } from '../../users/services/user-detail.service';
import { getNameFromCode, mappingChannelData, printLog } from 'src/utils';

type TransformData = {
  distributor?: {
    id: string;
    name: string;
  };
  outlet?: {
    ucc: string;
    name: string;
    status: OutletStatus;
    contactNumber: string;
    contactPhoneCode: string;
    address: string;
    businessSegment: string;
    channel: string;
    subChannel: string;
    distributorDepotId: string;
    outletClassification: string;
  };
  salesRep?: {
    id: string;
    firstName: string;
    middleName: string;
    lastName: string;
    mobilePhone: string;
    mobilePhoneCode: string;
    status: SalesRepStatus;
    outletId: string;
    email?: string;
  };
};

@Injectable()
export class SolaceDSRService {
  constructor(
    @InjectModel(Distributor.name)
    private readonly distributorModel: Model<DistributorDocument>,
    @InjectModel(User.name)
    private readonly userModel: Model<UserDocument>,
    @InjectModel(DistributorUserRelation.name)
    private readonly distributorUserRelationModel: Model<DistributorUserRelationDocument>,
    @InjectModel(SaleRepFfcStore.name)
    private readonly salesRepFfcModel: Model<SaleRepFfcStoreDocument>,
    @InjectModel(Outlet.name)
    private readonly outletModel: Model<OutletDocument>,
    @InjectModel(SaleRepOutletRelation.name)
    private readonly saleRepOutletRelationModel: Model<SaleRepOutletRelationDocument>,
    private readonly usersService: UsersService,
    private readonly dsrTargetService: DsrTargetService,
    private readonly i18nService: I18nService,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
    private readonly _userDetailService: UserDetailService,
  ) {}

  async handleOutletMessageQueue(jsonData: Record<'businessPartner', any[]>) {
    await Promise.all(
      jsonData.businessPartner.map(async (item) => {
        const data = this.transformOutletData(item);
        await this.upsertOutlet(data);
      }),
    );
  }

  async handleSalesRepMessageQueue(jsonData: Record<'businessPartnerContactPerson', any[]>) {
    await Promise.all(
      jsonData.businessPartnerContactPerson.map(async (item) => {
        const data = this.transformSalesRep(item);
        await this.upsertSalesRep(data);
      }),
    );
  }

  private transformDistributorId(originalId: string) {
    return originalId;
  }

  private transformDistributorData(messageData: any): TransformData['distributor'] {
    if (!Object.keys(messageData.vender || {}).length) {
      return null;
    }

    return {
      id: messageData.businessPartnerKey,
      name: messageData.name1,
    };
  }

  private parsePhone(phone?: string) {
    let contactNumber;
    let contactPhoneCode;
    try {
      if (phone) {
        const parsedPhone = parsePhoneNumber(phone, this.configService.get('COUNTRY_CODE'));
        contactNumber = parsedPhone.number;
        contactPhoneCode = `+${parsedPhone.countryCallingCode}`;
      }
    } catch (error) {
      printLog(`Invalid phone: ${phone}`);
      contactNumber = phone;
      contactPhoneCode = this.configService.get('PHONE_COUNTRY_CODE_DEFAULT');
    }

    return {
      contactNumber,
      contactPhoneCode,
    };
  }

  private transformOutletAddress(businessPartnerGeographicalLocation: any) {
    let address = '';
    let geographicalLocation = businessPartnerGeographicalLocation?.find((item) => item.geographicalLocationRole === GeographicalLocationRole.SHIPPING)?.geographicalLocation;
    if (!geographicalLocation) {
      geographicalLocation = businessPartnerGeographicalLocation[0]?.geographicalLocation;
    }

    const { street, houseNumber, city, postalCode, latitude, longitude } = geographicalLocation;
    if (latitude && longitude) {
      address = `${latitude}, ${longitude}`;
    } else {
      if (street) {
        address += street;
      }

      if (houseNumber) {
        address += ` ${houseNumber}`;
      }

      if (city) {
        address += ` ${city}`;
      }

      if (postalCode) {
        address += `, ${postalCode}`;
      }
    }

    return address;
  }

  private transformOutletData(data: any): TransformData['outlet'] {
    if (!Object.keys(data.customer || {}).length) {
      return null;
    }

    const phone = data.communication?.find((item) => item.communicationType === CommunicationType.PHONE)?.communicationNumber;

    const { contactNumber, contactPhoneCode } = this.parsePhone(phone);

    const address = this.transformOutletAddress(data.businessPartnerGeographicalLocation);

    const originalDistributorId = data.businessPartnerRelation?.find(
      (item) => item.businessPartner1Key === data.businessPartnerKey && item.relationType === RelationType.OUTLET_DISTRIBUTOR && new Date(item.validToDate) >= new Date(),
    )?.businessPartner2Key;

    const distributorDepotId = this.transformDistributorId(originalDistributorId);

    return {
      ucc: data.businessPartnerKey,
      name: data.name1,
      status: data.status?.toLowerCase() === Status.ACTIVE ? OutletStatus.ACTIVE : OutletStatus.INACTIVE,
      contactNumber,
      contactPhoneCode,
      address,
      channel: data.customer.customerChannel?.split('|')[0].trim() || '',
      businessSegment: data.customer.businessSegment?.split('|')[0].trim() || '',
      subChannel: data.customer.customerSalesOrganization[0]?.customerSales?.customerSubChannel?.split('|')[0].trim() || '',
      distributorDepotId,
      outletClassification: data.customer.customerSalesOrganization[0]?.customerSales?.outletClassification?.split('|')[0].trim() || '',
    };
  }

  private transformSalesRep(data: any): TransformData['salesRep'] {
    let phone = data?.communication?.find((item) => item?.communicationType === CommunicationTypeMobi.PHONE)?.communicationNumber;
    if (!phone) {
      phone = data?.communication?.find((item) => item?.communicationType === CommunicationType.PHONE)?.communicationNumber;
    }

    const email = data?.communication?.find((item) => item?.communicationType === CommunicationType.EMAIL)?.communicationNumber;

    const { contactNumber, contactPhoneCode } = this.parsePhone(phone);

    return {
      id: data.naturalPersonKey.naturalPersonKey,
      firstName: data.naturalPersonKey.firstName,
      lastName: data.naturalPersonKey.lastName,
      middleName: data.naturalPersonKey.middleName,
      mobilePhone: contactNumber,
      mobilePhoneCode: contactPhoneCode,
      status: data.status?.toLowerCase() === Status.ACTIVE ? SalesRepStatus.ACTIVE : SalesRepStatus.INACTIVE,
      outletId: data.businessPartnerKey,
      email,
    };
  }

  private async upsertDistributor({ id, name }: TransformData['distributor']) {
    const distributor = await this.distributorModel.findOneAndUpdate({ distributorId: id }, { $set: { distributorName: name } }, { upsert: true, new: true });
    return distributor;
  }

  private transformUsername({ firstName, middleName, lastName }: { firstName?: string; middleName?: string; lastName?: string }) {
    let username = '';
    if (firstName) {
      username = firstName.trim();
    }
    if (middleName) {
      username += ` ${middleName.trim()}`;
    }

    if (lastName) {
      username += ` ${lastName.trim()}`;
    }

    return username.replace(/\s{2,}/gi, ' ').trim();
  }

  private async upsertSalesRep(dto: TransformData['salesRep'] & { outletId: string }) {
    const outlet = await this.outletModel.findOne({ ucc: dto.outletId });

    if (!outlet) {
      throw new Error('Outlet not found');
    }

    const distributor = await this.distributorModel.findOne({ distributorId: outlet.distributorId });

    if (!distributor) {
      throw new Error('Distributor not found');
    }

    const i18n = new I18nContext(process.env.DEFAULT_LANGUAGE, this.i18nService);

    let salesRep = await this.userModel.findOne({
      saleRepId: dto.id,
    });

    const username =
      this.transformUsername({
        firstName: dto.firstName,
        middleName: dto.middleName,
        lastName: dto.lastName,
      }) ?? dto.id;

    if (salesRep) {
      salesRep = await this.userModel.findOneAndUpdate(
        { _id: salesRep._id },
        {
          $set: {
            mobilePhone: dto.mobilePhone,
            mobilePhoneCode: dto.mobilePhoneCode,
            saleRepStatus: dto.status,
            firstname: dto.firstName.trim(),
            lastname: dto.lastName.trim(),
            username,
            status: dto.status === SalesRepStatus.ACTIVE ? ConstantUser.IS_ACTIVE : ConstantUser.BLOCKED,
            isActive: dto.status === SalesRepStatus.ACTIVE,
            roleId: [ConstantRoles.SALE_REP],
            email: dto.email,
            depotId: outlet.depotId,
          },
        },
      );

      const isSentPasswordLink = await this.usersService.isSentPasswordLink(salesRep._id.toString(), 'salesRep');
      if (!isSentPasswordLink && dto.status === SalesRepStatus.ACTIVE) {
        await this.usersService.sendCreatePasswordUser(
          {
            mobilePhone: salesRep.mobilePhone,
            user: salesRep,
          },
          i18n,
        );
      }

      await this.saleRepOutletRelationModel.updateMany(
        {
          outlet: outlet._id,
          saleRep: { $ne: salesRep._id },
        },
        {
          $set: {
            disconnected: true,
            lastActive: false,
          },
        },
      );

      await this.saleRepOutletRelationModel.updateOne(
        { saleRep: salesRep._id, outlet: outlet._id },
        {
          $set: {
            saleRep: salesRep._id,
            outlet: outlet._id,
            disconnected: false,
            lastActive: true,
          },
        },
        { upsert: true },
      );

      await this.distributorUserRelationModel.updateOne(
        {
          user: salesRep._id,
        },
        {
          $set: {
            user: salesRep._id,
            distributor: distributor._id,
          },
        },
        {
          upsert: true,
        },
      );

      const updatedSaleReps = [...(outlet?.saleRep?.map((s) => s._id.toString()) || []), salesRep._id.toString()];
      await this.outletModel
        .updateOne(
          { _id: outlet._id },
          {
            $set: {
              saleRep: [...new Set(updatedSaleReps)].map((id) => new Types.ObjectId(id)),
            },
          },
        )
        .then()
        .catch();

      return salesRep;
    }

    salesRep = await this.userModel.create({
      saleRepId: dto.id,
      mobilePhone: dto.mobilePhone,
      mobilePhoneCode: dto.mobilePhoneCode,
      firstname: dto.firstName.trim(),
      lastname: dto.lastName.trim(),
      username,
      saleRepStatus: dto.status,
      status: dto.status === SalesRepStatus.ACTIVE ? ConstantUser.IS_ACTIVE : ConstantUser.BLOCKED,
      roleId: [ConstantRoles.SALE_REP],
      email: dto.email,
      depotId: outlet.depotId,
    });

    await this._userDetailService.addOrUpdate({
      userId: salesRep._id,
      isUserAdmin: false,
      userRole: [{ roleKey: ConstantRoles.SALE_REP }],
    });

    await this.salesRepFfcModel.create({
      saleRep: salesRep._id,
      customerUserName: '',
    });

    await this.distributorUserRelationModel.updateOne(
      {
        user: salesRep._id,
        distributor: distributor._id,
      },
      {
        $set: {
          user: salesRep._id,
          distributor: distributor._id,
        },
      },
      {
        upsert: true,
      },
    );

    if (dto.status === SalesRepStatus.ACTIVE) {
      await this.usersService.sendCreatePasswordUser(
        {
          mobilePhone: dto.mobilePhone,
          user: salesRep,
        },
        i18n,
      );
    }

    this.eventEmitter.emit(
      ConstantEventName.SHARED_EVENT,
      new SharedEvent({
        callback: async () => {
          await this.dsrTargetService.createTargetForSaleRep([salesRep._id.toString()]);
        },
      }),
    );

    await this.saleRepOutletRelationModel.updateMany(
      {
        outlet: outlet._id,
        saleRep: { $ne: salesRep._id },
      },
      {
        $set: {
          disconnected: true,
          lastActive: false,
        },
      },
    );

    await this.saleRepOutletRelationModel.updateOne(
      { saleRep: salesRep._id, outlet: outlet._id },
      {
        $set: {
          saleRep: salesRep._id,
          outlet: outlet._id,
          disconnected: false,
          lastActive: true,
        },
      },
      { upsert: true },
    );

    return salesRep;
  }

  private async upsertOutlet(dto: TransformData['outlet'] & { distributorDepotId: string }) {
    const distributors = await this.distributorModel.find();

    const distributor = distributors.find((distributor) => distributor.depots.map((depot) => depot.id).includes(dto.distributorDepotId));

    if (!distributor) {
      throw new Error('Distributor not found');
    }

    const channelMapping = mappingChannelData().channelMapping;
    const subChannelMapping = mappingChannelData().subChannelMapping;

    let segmentObj: any = {};
    if (dto.channel || dto.subChannel) {
      segmentObj = {
        channel: channelMapping[dto.channel?.toString()?.toUpperCase().trim()] || dto.channel.trim(),
        subChannel: subChannelMapping[dto.subChannel?.toString()?.toUpperCase().trim()] || dto.subChannel.trim(),
        channelDescription: getNameFromCode(dto.channel?.trim(), channelMapping),
        subChannelDescription: getNameFromCode(dto.subChannel?.trim(), subChannelMapping),
      };
    }
    printLog('segmentObj', segmentObj);

    await this.outletModel.findOneAndUpdate(
      { ucc: dto.ucc },
      {
        $set: {
          ucc: dto.ucc,
          name: dto.name,
          status: dto.status,
          contactNumber: dto.contactNumber,
          contactPhoneCode: dto.contactPhoneCode,
          businessSegment: dto.businessSegment,
          address: dto.address,
          distributorId: distributor.distributorId,
          depotId: dto.distributorDepotId,
          outletClass: dto.outletClassification?.trim(),
          ...segmentObj,
        },
      },
      { upsert: true, new: true },
    );
  }
}
