import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsDateString, IsOptional, IsString } from 'class-validator';

export class ExternalCallEffectivenessQuery {
  @ApiModelProperty()
  @IsString()
  distributorId: string;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  fromDate?: Date;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  toDate?: Date;
}
