import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsArray, IsDateString, IsString } from 'class-validator';

export class _360Kpi {
  @ApiModelProperty()
  @IsDateString()
  fromDate: Date;

  @ApiModelProperty()
  @IsDateString()
  toDate: Date;

  @ApiModelProperty()
  @IsArray()
  salesRepIds: [];

  @ApiModelProperty()
  @IsString()
  depotId: string;
}
