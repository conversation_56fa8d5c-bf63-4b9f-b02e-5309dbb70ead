import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { ApiResponse } from 'src/shared/response/api-response';
import { DistributionAchievementDto } from '../dtos/distribution-achievement.dto';
import { DistributionOutletCoverageDto } from '../dtos/distribution-outlet-coverage.dto';
import { ExternalAuthGuard } from '../../auth/decorator/guard/external-auth.guard';
import * as moment from 'moment-timezone';
import { ExternalCallEffectivenessQuery } from '../queries/call-effectiveness.query';
import { ChartReportingService } from '../services/chart-reporting.service';
import { _360Kpi } from '../queries/360-kpi';

@ApiTags('Chart Reporting')
@UseGuards(ExternalAuthGuard)
@Controller('api/dsr/chart-reporting')
export class ChartReportingController {
  constructor(private readonly journeyPlanningsService: OutletJourneyPlanningService, private readonly chartReportingService: ChartReportingService) {}

  @Get('distribution-achievement')
  async getDistributionAchievementBySalesRep(@Query() queries: DistributionAchievementDto) {
    const response = await this.journeyPlanningsService.getDistributionAchievementBySalesRep(queries);
    return new ApiResponse(response);
  }

  @Get('distribution-outlet-coverage')
  async getDistributionOutletCoverage(@Query() queries: DistributionOutletCoverageDto) {
    const response = await this.journeyPlanningsService.getDistributionOutletCoverage(queries);
    return new ApiResponse({ total: response.total, visited: response.visited, missedPercent: Math.round(response.missedPercent * 100) / 100 });
  }

  @Get('call-compliance')
  async getCallCompliance(@Query() queries: DistributionOutletCoverageDto) {
    const response = await this.journeyPlanningsService.getDistributionOutletCoverage(queries);
    const diffDays = moment(queries.toDate).tz(process.env.TZ).startOf('day').diff(moment(queries.fromDate), 'days');
    const responsePrevious = await this.journeyPlanningsService.getDistributionOutletCoverage(
      queries,
      queries.compareWithNumberDayAgo > 0 ? queries.compareWithNumberDayAgo : diffDays,
    );
    return new ApiResponse({
      total: response.total,
      visited: response.visited,
      missed: response.missed,
      totalPrevious: responsePrevious.total,
      visitedPrevious: responsePrevious.visited,
      missedPrevious: responsePrevious.missed,
      compareWithNumberDayAgo: queries.compareWithNumberDayAgo > 0 ? queries.compareWithNumberDayAgo : diffDays,
    });
  }

  @Get('call-effectiveness')
  async getCallEffectiveness(@Query() queries: ExternalCallEffectivenessQuery) {
    const data = await this.chartReportingService.calculateCallEffectiveness(queries);
    return new ApiResponse(data);
  }

  @Post('360-kpi')
  async get360Kpi(@Body() body: _360Kpi) {
    const data = await this.chartReportingService.calculate360Kpi(body);
    return new ApiResponse(data);
  }
}
