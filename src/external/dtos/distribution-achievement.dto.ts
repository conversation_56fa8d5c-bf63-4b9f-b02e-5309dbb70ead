import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsDateString, IsNumberString, IsOptional, IsString } from 'class-validator';

export class DistributionAchievementDto {
  @ApiModelProperty()
  @IsString()
  @IsOptional()
  distributorId?: string;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  fromDate?: Date;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  toDate?: Date;

  @ApiModelProperty()
  @IsNumberString()
  @IsOptional()
  top?: number;
}
