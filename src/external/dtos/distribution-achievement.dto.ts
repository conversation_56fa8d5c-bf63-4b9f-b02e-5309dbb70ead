import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNumberString, IsOptional, IsString } from 'class-validator';

export class DistributionAchievementDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  distributorId?: string;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  fromDate?: Date;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  toDate?: Date;

  @ApiProperty()
  @IsNumberString()
  @IsOptional()
  top?: number;
}
