import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsString } from 'class-validator';

export class DistributionOutletCoverageDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  distributorId?: string;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  fromDate?: Date;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  toDate?: Date;

  @ApiProperty()
  @IsOptional()
  compareWithNumberDayAgo?: number;
}
