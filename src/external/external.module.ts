import { forwardRef, Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';
import { MockService } from './services/mock.service';
import { OutletsModule } from '../outlets/outlets.module';
import { DistributorModule } from '../distributor/distributor.module';
import { SaleRepModule } from '../sale-rep/sale-rep.module';
import { SettingsModule } from '../settings/settings.module';
import { RequestScopeModule } from 'nj-request-scope';
import { JourneyPlanningsModule } from 'src/journey-plannings/journey-plannings.module';
import { ChartReportingController } from './controllers/chart-reporting.controller';
import { OmsService } from './services/oms.service';
import OmsApiClientService from './services/oms.client.service';
import { MongooseModule } from '@nestjs/mongoose';
import { OmsCacheData, OmsCacheDataSchema } from './schemas/oms-cache-data.schema';
import { OmsCacheService } from './services/oms-cache.service';
import { OrdersModule } from 'src/orders/orders.module';
import { ChartReportingService } from './services/chart-reporting.service';
import { OutletJourneyPlanning, OutletJourneyPlanningSchema } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { Distributor, DistributorSchema, DistributorUserRelation, DistributorUserRelationSchema } from 'src/distributor/schemas';
import { User, UserSchema } from 'src/users/schemas/user.schema';
import { SaleRepExecutionVisibility, SaleRepExecutionVisibilitySchema } from 'src/sale-rep/schemas';
import { OrderOMS, OrderOMSSchema } from 'src/orders/schemas/order-oms.schema';
import { FilesModule } from 'src/files/files.module';

@Module({
  imports: [
    HttpModule.register({
      timeout: 20000,
      maxRedirects: 50,
    }),
    MongooseModule.forFeature([{ name: OmsCacheData.name, schema: OmsCacheDataSchema }]),
    forwardRef(() => AuthModule),
    forwardRef(() => UsersModule),
    forwardRef(() => OutletsModule),
    forwardRef(() => DistributorModule),
    forwardRef(() => SaleRepModule),
    forwardRef(() => SettingsModule),
    forwardRef(() => JourneyPlanningsModule),
    forwardRef(() => OrdersModule),
    forwardRef(() => FilesModule),
    forwardRef(() => DistributorModule),
    forwardRef(() => JourneyPlanningsModule),
    RequestScopeModule,
    MongooseModule.forFeature([
      { name: OutletJourneyPlanning.name, schema: OutletJourneyPlanningSchema },
      { name: DistributorUserRelation.name, schema: DistributorUserRelationSchema },
      { name: Distributor.name, schema: DistributorSchema },
      { name: User.name, schema: UserSchema },
      {
        name: SaleRepExecutionVisibility.name,
        schema: SaleRepExecutionVisibilitySchema,
      },
      {
        name: OrderOMS.name,
        schema: OrderOMSSchema,
      },
    ]),
  ],
  providers: [MockService, OmsApiClientService, OmsService, OmsCacheService, ChartReportingService],
  exports: [MockService, OmsService, OmsCacheService, ChartReportingService],
  controllers: [ChartReportingController],
})
export class ExternalModule {}
