import { CACHE_MANAGER, forwardRef, Inject, Injectable, UseInterceptors } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { I18nService } from 'nestjs-i18n';
import { createUniqueCode, getSTime, getTDZTime, isEmptyObjectOrArray } from '../../utils';
import { Outlet } from '../../outlets/schemas/outlet.schema';
import * as moment from 'moment-timezone';
import { UsersService } from '../../users/services/users.service';
import { products, promotions } from '../mockdatas/data';
import { OmsPlaceOrderDto } from 'src/orders/dtos/outlet-place-order.dto';
import { OrderOMSFilterDto } from 'src/orders/dtos/order-filter.dto';
import { OmsPlaceOrder } from 'src/offline-mode-v2/dtos/sync-offline-data.dto';

@Injectable()
export class MockService {
  currencyCode = 'idr';
  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly i18n: I18nService,
    @Inject(forwardRef(() => UsersService))
    private usersService: UsersService,
  ) {}

  getListProductsOms() {
    return products;
  }

  getListPromotionsOms() {
    return promotions;
  }

  async placeOrderOMS(body: OmsPlaceOrderDto, salesRepName) {
    const key = `test_account_${body.outletId}`;
    const lastOrderIdKey = `test_account_last_order_id`;

    const listOrders: Array<any> = (await this.cacheManager.get(key)) || [];
    const lastOrderId = (await this.cacheManager.get(lastOrderIdKey)) || 1;

    let subtotal = 0;
    let total = 0;
    const items = [];
    let currency_code = '';
    for (const iterator of body.items) {
      const product = products.find((e) => e.sku == iterator.sku);
      if (!product) {
        throw `Not found ${iterator.sku}.`;
      }
      subtotal += product.unit_price;
      total += product.unit_price;
      currency_code = product.currency_code;

      items.push({
        promotion_code: null,
        sku: product.sku,
        title: product.title,
        subtitle: product.subtitle,
        pack_type: product.pack_type,
        thumbnail: product.thumbnail,
        volume: product.volume,
        currency_code: product.currency_code,
        hasPromotion: iterator.promotion ? true : false,
        isFreeItem: false,
        unit_price: product.unit_price,
        quantity: iterator.quantity,
        total: Number(iterator.quantity) * Number(product.unit_price),
      });
    }
    const order = {
      id: `order_${createUniqueCode()}`,
      display_id: lastOrderId,
      status: 'pending',
      created_at: new Date().toISOString(),
      sales_rep_name: salesRepName,
      currency_code: currency_code,
      total: total,
      discount_total: 0,
      subtotal: subtotal,
      tax_total: 0,
      empties_deposit: 0,
      items: items,
    };
    listOrders.unshift(order);
    await this.cacheManager.set(key, listOrders, { ttl: 60 * 60 * 24 * 3 });
    await this.cacheManager.set(lastOrderIdKey, Number(lastOrderId) + 1, { ttl: 60 * 60 * 24 * 3 });
    return order;
  }

  async createOrderOmsOffline(body: OmsPlaceOrder[], outletId, salesRepName) {
    let success = true;
    let message = '';
    let itemsTemp = [];
    try {
      for (const iterator of body) {
        try {
          const orderInfo = await this.placeOrderOMS({ items: iterator.items, outletId, couponCode: iterator.couponCode }, salesRepName);
          itemsTemp.push({
            offlineOrderId: iterator.offlineOrderId,
            success: true,
            message: '',
            orderInfo,
          });
        } catch (error) {
          itemsTemp.push({
            offlineOrderId: iterator.offlineOrderId,
            success: false,
            message: 'Something went wrong.',
          });
          success = false;
          message = error.message;
        }
      }
    } catch (error) {
      success = false;
      message = error.message;
    }
    return { key: 'orders', success, message, items: itemsTemp };
  }

  fullTextSearchInString(text, searchText) {
    const lowerText = text.toLowerCase();
    const lowerSearchText = searchText.toLowerCase();
    const regex = new RegExp(`\\b${lowerSearchText}\\b`, 'g');
    const matches = lowerText.match(regex);
    return matches ? true : false;
  }

  async getListOrdersOms(query: OrderOMSFilterDto) {
    const key = `test_account_${query.outletId}`;
    let orders: Array<any> = (await this.cacheManager.get(key)) || [];
    if (orders.length) {
      if (query.status) {
        orders = orders.filter((e) => e.status == query.status);
      }
      if (query.search) {
        orders = orders.filter((e) => this.fullTextSearchInString(`${e.display_id} ${e.sales_rep_name} ${e.status} ${e.items.map((p) => p.title).toString()}`, query.search));
      }
      if (query.start_date && query.end_date) {
        orders = orders.filter((e) => {
          const orderDate = new Date(e.created_at).getTime();
          const startDate = moment(query.start_date).startOf('day').toDate().getTime();
          const endDate = moment(query.end_date).endOf('day').toDate().getTime();
          return orderDate >= startDate && orderDate <= endDate;
        });
      }
      if (query.offset && query.limit) {
        orders = orders.slice(Number(query.offset), Number(query.limit));
      }
    }
    return orders;
  }

  async getLatestOrderOms({ outlet }) {
    let latestOrder: any = null;
    const key = `test_account_${outlet}`;
    const orders: Array<any> = (await this.cacheManager.get(key)) || [];
    if (orders.length) {
      latestOrder = orders[0];
    }
    return latestOrder;
  }

  async getCachedDataByOutlet(outlet) {
    const key = `test_account_${outlet}`;
    const orders: Array<any> = (await this.cacheManager.get(key)) || [];
    let latestOrder: any = null;
    if (orders.length) {
      latestOrder = orders[0];
    }
    return {
      latestOrder,
      orders,
      products,
      promotions,
    };
  }
}
