import { CACHE_MANAGER, Inject, Injectable, UseInterceptors } from '@nestjs/common';
import { BaseService } from 'src/shared/services/base-service';
import { OmsCacheData, OmsCacheDataDocument, OmsCacheDataSchema } from '../schemas/oms-cache-data.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as moment from 'moment-timezone';

// @Injectable({ scope: Scope.REQUEST })
@Injectable()
export class OmsCacheService extends BaseService<OmsCacheData> {
  @InjectModel(OmsCacheData.name)
  model: Model<OmsCacheDataDocument>;
  constructor() {
    super();
  }
}
