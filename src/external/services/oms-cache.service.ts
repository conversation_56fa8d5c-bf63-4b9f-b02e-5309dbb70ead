import { Injectable } from '@nestjs/common';
import { BaseService } from 'src/shared/services/base-service';
import { OmsCacheData, OmsCacheDataDocument } from '../schemas/oms-cache-data.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

// @Injectable({ scope: Scope.REQUEST })
@Injectable()
export class OmsCacheService extends BaseService<OmsCacheData> {
  @InjectModel(OmsCacheData.name)
  model: Model<OmsCacheDataDocument>;
  constructor(@InjectModel(OmsCacheData.name) private readonly _omsCacheDataDocument: Model<OmsCacheDataDocument>) {
    super();
    this.model = this._omsCacheDataDocument;
    this.updateExternalId().then();
  }

  async updateExternalId() {
    const documents = await this.model
      .find({ outletExternalId: { $in: [null, ''] } })
      .populate('outlet')
      .exec();

    if (!documents.length) {
      console.log('Not Found');
      return;
    }

    const bulkOps = documents
      .filter((doc) => doc.outlet && doc.outlet.ucc)
      .map((doc) => ({
        updateOne: {
          filter: { _id: doc._id },
          update: { $set: { outletExternalId: doc.outlet.ucc } },
        },
      }));

    if (bulkOps.length > 0) {
      const result = await this.model.bulkWrite(bulkOps);
      console.log(`Done: ${result.modifiedCount} documents`);
    } else {
      console.log('Not Found');
    }
  }
}
