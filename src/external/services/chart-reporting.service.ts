import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { ExternalCallEffectivenessQuery } from '../queries/call-effectiveness.query';
import * as moment from 'moment-timezone';
import { Distributor, DistributorDocument, DistributorUserRelation, DistributorUserRelationDocument } from 'src/distributor/schemas';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import { _360Kpi } from '../queries/360-kpi';
import { User, UserDocument } from 'src/users/schemas/user.schema';
import { SaleRepExecutionVisibility, SaleRepExecutionVisibilityDocument } from 'src/sale-rep/schemas';
import { OutletStatus } from 'src/outlets/enums/outlet-status.enum';
import OmsApiClientService from './oms.client.service';
import { OMS_API_ASO_FOR_SALES_REP } from '../constants/oms';
import { OrderOMS, OrderOMSDocument } from 'src/orders/schemas/order-oms.schema';
import * as _ from 'lodash';
import { SettingsService } from '../../settings/settings.service';
import { OmsService } from './oms.service';
import { OmsVisibilityExecutionsService } from '../../oms/services/visibility-executions.service';
import { I18nContext } from 'nestjs-i18n';
import { FilesService } from '../../files/services';
import { OmsSalesRepStatisticsService } from '../../oms/services/sales-rep-statistics.service';
import { OmsTargetSettingsService } from 'src/oms/services/target-settings.service';

@Injectable()
export class ChartReportingService {
  constructor(
    private readonly omsApiClientService: OmsApiClientService,
    @Inject(forwardRef(() => SettingsService))
    private readonly settingsService: SettingsService,
    @InjectModel(OutletJourneyPlanning.name) private readonly planModel: Model<OutletJourneyPlanningDocument>,
    @InjectModel(DistributorUserRelation.name) private readonly distributorSalesRepRelationModel: Model<DistributorUserRelationDocument>,
    @InjectModel(Distributor.name) private readonly distributorModel: Model<DistributorDocument>,
    @InjectModel(User.name) private readonly userModel: Model<UserDocument>,
    @InjectModel(SaleRepExecutionVisibility.name) private readonly visibilityModel: Model<SaleRepExecutionVisibilityDocument>,
    @InjectModel(OrderOMS.name) private readonly orderOMSModel: Model<OrderOMSDocument>,
    @Inject(forwardRef(() => OmsService))
    private readonly omsService: OmsService,
    @Inject(forwardRef(() => OmsVisibilityExecutionsService))
    private readonly visibilityExecutionsService: OmsVisibilityExecutionsService,
    @Inject(forwardRef(() => FilesService))
    private readonly filesService: FilesService,
    @Inject(forwardRef(() => OmsSalesRepStatisticsService))
    private readonly _omsSalesRepStatisticsService: OmsSalesRepStatisticsService,
    @Inject(forwardRef(() => OmsTargetSettingsService))
    private readonly _omsTargetSettingsService: OmsTargetSettingsService,
  ) {}

  async calculateCallEffectiveness(queries: ExternalCallEffectivenessQuery) {
    const distributor = await this.distributorModel.findOne({ distributorId: queries.distributorId });

    const relations = await this.distributorSalesRepRelationModel.find({
      distributor: distributor._id,
      user: {
        $exists: true,
        $ne: null,
      },
    });

    const uniqueSalesRepIds = Array.from(new Set(relations.map((relation) => String(relation.user))));

    const { from, to } = this.transformDateRange(queries);

    const plans = await this.planModel
      .find({
        saleRep: { $in: uniqueSalesRepIds.map((id) => new Types.ObjectId(id)) },
        visitStatus: VisitStatus.COMPLETED,
        $or: [
          {
            day: {
              $gte: from.clone().toDate(),
              $lte: to.clone().toDate(),
            },
            rescheduled: false,
          },
          {
            rescheduledDay: {
              $gte: from.clone().toDate(),
              $lte: to.clone().toDate(),
            },
            rescheduled: true,
          },
        ],
      })
      .select('_id rescheduledDay day');

    const data: Array<{ date: string; numberOfVisitedOutlet: number }> = [];

    const daysDiff = to.diff(from, 'day');
    for (let i = 0; i <= daysDiff; i += 1) {
      const date = from.clone().add(i, 'days');
      const startOfDate = date.clone().startOf('date');
      const endOfDate = date.clone().endOf('date');

      const currentDatePlans = plans.filter((plan) => {
        const planDate = moment(plan.rescheduledDay || plan.day).tz(process.env.TZ);

        return planDate.isBetween(startOfDate, endOfDate, undefined, '[]');
      });

      data.push({
        date: date.format('DD/MM/YYYY'),
        numberOfVisitedOutlet: currentDatePlans.length,
      });
    }

    return {
      items: data,
      total: plans.length,
    };
  }

  private transformDateRange(query: Pick<ExternalCallEffectivenessQuery, 'fromDate' | 'toDate'>) {
    const today = moment().tz(process.env.TZ);
    const from = moment(query.fromDate || today.clone().startOf('month'))
      .tz(process.env.TZ)
      .startOf('date');

    const to = moment(query.toDate || today.clone())
      .tz(process.env.TZ)
      .endOf('date');

    return { from, to };
  }

  private async calculateCallComplianceRate(listJp: OutletJourneyPlanning[]) {
    const res = {
      callsPlanned: 0,
      callsMade: 0,
      yesterdayCallsPlanned: 0,
      yesterdayCallsMade: 0,
      error: false,
      errorMessage: '',
    };
    try {
      const yesterdayStart = moment().tz(process.env.TZ).subtract(1, 'day').startOf('day');
      const yesterdayEnd = moment().tz(process.env.TZ).subtract(1, 'day').endOf('day');
      const listJpYesterday = listJp.filter((jp) => {
        const time = jp.rescheduled ? jp.rescheduledDay : jp.day;
        const momentTime = moment(time).tz(process.env.TZ);
        if (yesterdayStart.isSameOrBefore(momentTime) && yesterdayEnd.isSameOrAfter(momentTime)) {
          return true;
        }
        return false;
      });
      res.callsPlanned = listJp.length;
      res.callsMade = listJp.filter((jp) => jp.visitStatus == VisitStatus.COMPLETED).length;
      res.yesterdayCallsPlanned = listJpYesterday.length;
      res.yesterdayCallsMade = listJpYesterday.filter((jp) => jp.visitStatus == VisitStatus.COMPLETED).length;
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }
    return res;
  }

  private async calculateStrikeRate(listJp: OutletJourneyPlanning[]) {
    const res = {
      inVisitOrder: 0,
      callsMade: 0,
      yesterdayInVisitOrder: 0,
      yesterdayCallsMade: 0,
      error: false,
      errorMessage: '',
    };
    try {
      const visitedPlans = listJp.filter((jp) => jp.visitStatus == VisitStatus.COMPLETED);
      const yesterdayStart = moment().tz(process.env.TZ).subtract(1, 'day').startOf('day');
      const yesterdayEnd = moment().tz(process.env.TZ).subtract(1, 'day').endOf('day');
      const listJpYesterday = visitedPlans.filter((jp) => {
        const time = jp.rescheduled ? jp.rescheduledDay : jp.day;
        const momentTime = moment(time).tz(process.env.TZ);
        if (yesterdayStart.isSameOrBefore(momentTime) && yesterdayEnd.isSameOrAfter(momentTime)) {
          return true;
        }
        return false;
      });

      res.inVisitOrder = visitedPlans.filter((jp) => jp.hasOrder).length;
      res.callsMade = visitedPlans.length;
      res.yesterdayInVisitOrder = listJpYesterday.filter((jp) => jp.hasOrder).length;
      res.yesterdayCallsMade = listJpYesterday.length;
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }
    return res;
  }

  private async calculateAvailability(listJp: OutletJourneyPlanning[]) {
    const res = {
      current: 0,
      target: 0,
      error: false,
      errorMessage: '',
    };
    try {
      const visitedPlans = listJp.filter((jp) => jp.visitStatus == VisitStatus.COMPLETED);
      const { current, target } = visitedPlans.reduce(
        (initial, plan) => {
          const target = initial.target + (plan.outlet.totalProductAssigned || 0);
          return {
            target,
            current: initial.current + (plan.checkStock?.listProductsChecked || []).filter((product) => product.check_stock_quantity > 0 || product?.selling_price > 0).length,
          };
        },
        {
          current: 0,
          target: 0,
        },
      );

      res.current = current || 0;
      res.target = target || 0;
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }
    return res;
  }

  private async calculateVisibility(listJp: OutletJourneyPlanning[]) {
    const res = {
      current: 0,
      target: 0,
      error: false,
      errorMessage: '',
    };
    try {
      const visitedPlans = listJp.filter((jp) => jp.visitStatus == VisitStatus.COMPLETED);
      const visitedPlanIds = visitedPlans.map((plan) => plan._id);
      const visibilities = await this.visibilityModel.find({
        journeyPlan: { $in: visitedPlanIds },
      });

      const { current, target } = visibilities.reduce(
        (initial, visibility: any) => {
          const numberOfTask = Object.values(visibility.taskProps || {}).length || Object.keys(visibility.tasks || {}).length;
          // If there's visibility, that mean the plan is visited, no need to check visit status here
          const numberOfDoneTask =
            Object.values(visibility.taskProps || {}).filter((value: any) => value?.quantity > 0 || value?.imageIds?.length > 0).length ||
            Object.values(visibility.tasks || {}).filter((value: any) => value?.length > 0).length;

          return {
            current: initial.current + numberOfDoneTask,
            target: initial.target + numberOfTask,
          };
        },
        {
          current: 0,
          target: 0,
        } as { current: number; target: number },
      );

      res.current = current || 0;
      res.target = target || 0;
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }
    return res;
  }

  /**
   *
   * @param listJp
   * @param startDate
   * @param endDate
   * @param salesRepIds : External Keys
   * @param depotId: External Key
   * @private
   */
  private async calculateASO(listJp: OutletJourneyPlanning[], startDate, endDate, salesRepIds, depotId) {
    const res = {
      current: 0,
      target: 0,
      error: false,
      errorMessage: '',
    };

    try {
      const activePlans = listJp.filter((plan) => plan.outlet.status == OutletStatus.ACTIVE);
      const uniqueOutletIds = Array.from(new Set(activePlans.map((plan) => String(plan.outlet._id))));
      let listData = [];

      while (salesRepIds.length) {
        const contact_external_ids = salesRepIds?.splice(0, 15);
        const asoSalesRepRes = await this.omsApiClientService.get(OMS_API_ASO_FOR_SALES_REP, {
          invoice_date_start: startDate,
          invoice_date_end: endDate,
          depot_external_id: depotId,
          contact_external_ids,
        });
        if (asoSalesRepRes?.data?.chart?.data?.length) {
          listData = listData.concat(asoSalesRepRes?.data?.chart?.data);
        }
      }

      if (listData.length) {
        let total = 0;
        listData?.forEach((e) => {
          total += Number(e.aso_total || 0);
        });
        res.current = total;
      }
      res.target = uniqueOutletIds.length;
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }

    return res;
  }

  public async calculateOrders(listSalesrep: User[], startDate, endDate) {
    const res = {
      totalOrder: 0,
      inVisitOrders: 0,
      adHocOrders: 0,
      error: false,
      errorMessage: '',
    };
    try {
      const listOrders = await this.orderOMSModel
        .find({
          orderDate: {
            $gte: startDate,
            $lte: endDate,
          },
          salesRep: { $in: listSalesrep },
        })
        .select('_id jp');
      res.inVisitOrders = listOrders.filter((o) => o.jp).length;
      res.adHocOrders = listOrders.filter((o) => !o.jp).length;
      res.totalOrder = res.inVisitOrders + res.adHocOrders;
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }
    return res;
  }

  private async calculateAverageVisitTime(listJp: OutletJourneyPlanning[], listSalesRep: User[]) {
    const res = {
      list: [],
      error: false,
      errorMessage: '',
    };
    try {
      const visitedPlans = listJp.filter((jp) => jp.visitStatus == VisitStatus.COMPLETED);
      const groupBySalesRep = _.mapValues(_.groupBy(visitedPlans, 'saleRep'), (clist) => clist.map((e) => _.omit(e, 'saleRep')));

      for (const salesRep of listSalesRep) {
        const temp = {
          salesRep: salesRep.saleRepId,
          averageTime: 0,
        };
        let totalTime = 0;
        const list = groupBySalesRep[String(salesRep._id)] || [];
        if (list.length > 0) {
          for (const element of list) {
            const time = new Date(element.visitedDay).getTime() - new Date(element.startVisitDate).getTime();
            totalTime += time && time > 0 ? time : 0;
          }
          temp.averageTime = totalTime / list.length;
          temp.averageTime = Math.round(Number(temp.averageTime) / 60000); // parse to minutes
        }
        res.list.push(temp);
      }
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }
    return res;
  }

  private async calculateOutletVisitsRange(listJp: OutletJourneyPlanning[]) {
    const res = {
      inRange: 0,
      outOfRange: 0,
      error: false,
      errorMessage: '',
    };
    try {
      const configMetre = (process.env.LOCATION_RANGE_CONFIG && Number(process.env.LOCATION_RANGE_CONFIG)) || 100;
      res.inRange = listJp.filter((e) => e.locationRange && Number(e.locationRange) <= configMetre).length;
      res.outOfRange = listJp.filter((e) => !e.locationRange || Number(e.locationRange) > configMetre).length;
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }
    return res;
  }

  private async calculateShareOfStock(listJp: OutletJourneyPlanning[]) {
    const res = {
      hnk: 0,
      competitor: 0,
      error: false,
      errorMessage: '',
    };
    try {
      const convertToCarton = (await this.settingsService.getConfigExcel())?.convertToCarton;
      listJp.forEach((item) => {
        if (item.checkStock) {
          for (const element of item.checkStock.listProductsChecked) {
            const pcs = convertToCarton?.find((p) => p.pcs === element.sku);
            res.hnk += pcs ? element.check_stock_quantity * pcs.pcsMpsToCarton : element.check_stock_quantity;
          }
          for (const element of item.checkStock.listCompetitorsChecked) {
            res.competitor += element.check_stock_quantity;
          }
        }
      });
    } catch (error) {
      res.error = true;
      res.errorMessage = error.message;
    }
    return { ...res, hnk: parseFloat(res.hnk.toFixed(2)) };
  }

  /**
   * getVolumeSalesRepStatisticsOMS
   * @param queries
   */
  async getVolumeSalesRepStatisticsOMS(queries: _360Kpi) {
    const { fromDate, toDate, salesRepIds, depotId } = queries;
    let fromDateFilter, toDateFilter;
    if (fromDate) {
      fromDateFilter = moment.tz(fromDate.toString().replace('Z', ''), process.env.TZ).utc().format();
    }
    if (toDate) {
      toDateFilter = moment.tz(toDate.toString().replace('Z', ''), process.env.TZ).utc().format();
    }
    const listSalesRep = await this.userModel.find({ saleRepId: { $in: salesRepIds } }).select('_id saleRepId');
    return this.omsService.getVolumeSalesRepStatisticsOMS({
      salesRepIds: listSalesRep.map((e) => e.saleRepId),
      depotId,
      startDate: fromDateFilter,
      endDate: toDateFilter,
    });
  }

  async calculate360Kpi(queries: _360Kpi) {
    const { fromDate, toDate, salesRepIds, depotId } = queries;
    let fromDateFilter, toDateFilter, toDateMoment: moment.Moment;
    const currentMoment = moment.utc();
    if (fromDate) {
      fromDateFilter = moment.tz(fromDate.toString().replace('Z', ''), process.env.TZ).utc().format();
    }
    if (toDate) {
      toDateMoment = moment.tz(toDate.toString().replace('Z', ''), process.env.TZ).utc();
      toDateFilter = toDateMoment?.format();
    }

    const dateFilterMonth = (toDateMoment?.month() || currentMoment.month()) + 1;
    const dateFilterYear = toDateMoment?.year() || currentMoment.year();
    const listSalesRep = await this.userModel.find({ saleRepId: { $in: salesRepIds } }).select('_id saleRepId');
    const saleRepIds = listSalesRep.map((e) => e._id);
    const salesRepTargetSettings = await this._omsTargetSettingsService.getAverageOmsTargetSettings(saleRepIds, dateFilterMonth, dateFilterYear);

    const allJP = await this.planModel
      .find({
        saleRep: { $in: saleRepIds },
        $or: [
          {
            day: {
              $gte: fromDateFilter,
              $lte: toDateFilter,
            },
            rescheduled: false,
          },
          {
            rescheduledDay: {
              $gte: fromDateFilter,
              $lte: toDateFilter,
            },
            rescheduled: true,
          },
        ],
      })
      .populate(['outlet'])
      .select(['_id', 'rescheduled', 'rescheduledDay', 'day', 'visitStatus', 'hasOrder', 'checkStock', 'saleRep', 'startVisitDate', 'visitedDay', 'locationRange']);
    const [callComplianceRate, strikeRate, availability, visibility, aso, order, averageVisitTime, outletVisitsRange, shareOfStock] = await Promise.all([
      this.calculateCallComplianceRate(allJP),
      this.calculateStrikeRate(allJP),
      this.calculateAvailability(allJP),
      this.calculateVisibility(allJP),
      this.calculateASO(allJP, fromDate, toDate, salesRepIds, depotId),
      this.calculateOrders(
        listSalesRep.map((e) => e._id),
        fromDate,
        toDate,
      ),
      this.calculateAverageVisitTime(allJP, listSalesRep),
      this.calculateOutletVisitsRange(allJP),
      this.calculateShareOfStock(allJP),
    ]);

    return {
      callComplianceRate,
      strikeRate,
      availability: {
        ...availability,
        targetPercent: salesRepTargetSettings.availabilityTarget,
      },
      visibility: {
        ...visibility,
        targetPercent: salesRepTargetSettings.visibilityTarget,
      },
      aso,
      order,
      averageVisitTime,
      outletVisitsRange,
      shareOfStock,
    };
  }

  async getJourneyPlans(salesRepIds, fromDate, toDate) {
    let fromDateFilter, toDateFilter;
    if (fromDate) {
      fromDateFilter = moment.tz(fromDate.toString().replace('Z', ''), process.env.TZ).utc().format();
    }
    if (toDate) {
      toDateFilter = moment.tz(toDate.toString().replace('Z', ''), process.env.TZ).utc().format();
    }
    const listSalesRep = await this.userModel.find({ saleRepId: { $in: salesRepIds } }).select('_id saleRepId');
    return this.planModel
      .find({
        saleRep: { $in: listSalesRep.map((e) => e._id) },
        $or: [
          {
            day: {
              $gte: fromDateFilter,
              $lte: toDateFilter,
            },
            rescheduled: false,
          },
          {
            rescheduledDay: {
              $gte: fromDateFilter,
              $lte: toDateFilter,
            },
            rescheduled: true,
          },
        ],
      })
      .populate(['outlet'])
      .select(['_id', 'rescheduled', 'rescheduledDay', 'day', 'visitStatus', 'hasOrder', 'checkStock', 'saleRep', 'startVisitDate', 'visitedDay', 'locationRange']);
  }

  /**
   * Get available products from completed journey plannings grouped by outlets
   * @returns Array of outlets with their available products (including full product info from cache)
   * @param salesRepIds
   * @param fromDate
   * @param toDate
   * @param i18n
   */
  async getAvailableProducts(salesRepIds, fromDate, toDate, i18n?: I18nContext) {
    const result = [];
    const listJp = await this.getJourneyPlans(salesRepIds, fromDate, toDate);
    const visitedPlans = listJp.filter((jp) => jp.visitStatus == VisitStatus.COMPLETED);
    if (!visitedPlans.length) {
      return result;
    }

    // Group plans by outlet
    const plansByOutlet = {};
    visitedPlans.forEach((plan) => {
      const outletId = String(plan.outlet._id);
      if (!plansByOutlet[outletId]) {
        plansByOutlet[outletId] = {
          outlet: plan.outlet,
          latestVisitDate: plan.visitedDay,
          availableProducts: [],
        };
      }

      const validProducts = (plan.checkStock?.listProductsChecked || []).filter((product) => product.check_stock_quantity > 0 || product?.selling_price > 0);

      plansByOutlet[outletId].availableProducts.push(
        ...validProducts.map((product) => ({
          ...product,
          complaint: product.check_stock_quantity > 0 || product?.selling_price > 0,
        })),
      );
    });

    // For each outlet, get cached data and merge with available products
    for (const outletId of Object.keys(plansByOutlet)) {
      const outletData = plansByOutlet[outletId];

      try {
        const cached = await this.omsService.getCachedDataByOutlet({
          outletId: new Types.ObjectId(outletId),
          useDb: true,
          project: {
            products: 1,
          },
          outletExternalId: null,
        });

        // Match available products with cached product data
        const enrichedProducts = outletData.availableProducts.map((availableProduct) => {
          const cachedProduct = cached?.products?.find((p) => p.sku === availableProduct.sku);
          return {
            ...availableProduct,
            ...(cachedProduct || null),
          };
        });

        result.push({
          outlet: {
            _id: outletData.outlet._id,
            name: outletData.outlet.name,
            address: outletData.outlet.address,
          },
          latestVisitDate: outletData.latestVisitDate,
          products: enrichedProducts,
        });
      } catch (error) {
        console.error(`Error getting cached data for outlet ${outletId}:`, error);
        result.push({
          outlet: {
            _id: outletData.outlet._id,
            name: outletData.outlet.name,
          },
          products: outletData.availableProducts.map((product) => ({
            ...product,
          })),
        });
      }
    }

    return result;
  }

  /**
   * Get available visibility tasks from completed journey plannings grouped by outlets
   * @returns Array of outlets with their visibility tasks and images
   * @param salesRepIds
   * @param fromDate
   * @param toDate
   * @param i18n
   */
  async getAvailableVisibilityTasks(salesRepIds, fromDate, toDate, i18n?: I18nContext) {
    const result = [];
    const listJp = await this.getJourneyPlans(salesRepIds, fromDate, toDate);
    const visitedPlans = listJp.filter((jp) => jp.visitStatus == VisitStatus.COMPLETED);
    if (!visitedPlans.length) {
      return result;
    }

    // Get visibility data for visited plans
    const visitedPlanIds = visitedPlans.map((plan) => plan._id);
    const visibilities = await this.visibilityModel.find({
      journeyPlan: { $in: visitedPlanIds },
    });

    if (!visibilities.length) {
      return result;
    }

    // Group visibilities by outlet through journey plan
    const visibilityByOutlet = {};
    const allTaskIds = new Set();

    for (const visibility of visibilities) {
      const plan = visitedPlans.find((p) => String(p._id) === String(visibility.journeyPlan));
      if (!plan) continue;

      const outletId = String(plan.outlet._id);
      if (!visibilityByOutlet[outletId]) {
        visibilityByOutlet[outletId] = {
          outlet: plan.outlet,
          latestVisitDate: plan.visitedDay,
          tasks: [],
        };
      }

      if (visibility.taskProps) {
        for (const [taskId, taskData] of Object.entries(visibility.taskProps)) {
          const imageIds = (taskData as any)?.imageIds || [];
          const quantity = (taskData as any)?.quantity || 0;

          allTaskIds.add(taskId);
          visibilityByOutlet[outletId].tasks.push({
            taskId,
            quantity,
            imageIds,
          });
        }
      }
    }

    // Get task names from VisibilityExecution collection
    let taskNamesMap = {};
    if (allTaskIds.size > 0) {
      try {
        const taskExecutions = await this.visibilityExecutionsService.findTasksByIds(Array.from(allTaskIds) as string[], '_id name subHeading');

        taskNamesMap = taskExecutions.reduce((map, task) => {
          map[String(task._id)] = {
            name: task.name,
            subHeading: task.subHeading,
          };
          return map;
        }, {});
      } catch (error) {
        console.error('Error getting task names:', error);
      }
    }

    // For each outlet, get image data and merge with tasks
    for (const outletId of Object.keys(visibilityByOutlet)) {
      const outletData = visibilityByOutlet[outletId];

      // Collect all imageIds from all tasks
      const allImageIds = [];
      outletData.tasks.forEach((task) => {
        allImageIds.push(...task.imageIds);
      });

      let imagePathMap = {};
      if (allImageIds.length > 0) {
        try {
          const imageFiles = await this.filesService.findFilesByIds(allImageIds, '_id path');

          imagePathMap = imageFiles.reduce((map, file) => {
            map[String(file._id)] = file.path;
            return map;
          }, {});
        } catch (error) {
          console.error(`Error getting image data for outlet ${outletId}:`, error);
        }
      }

      // Enrich tasks with task names and image paths
      const enrichedTasks = outletData.tasks.map((task) => {
        const taskInfo = taskNamesMap[task.taskId] || {};
        return {
          taskName: taskInfo.name || task.taskId,
          subHeading: taskInfo.subHeading || '',
          quantity: task.quantity,
          images: task.imageIds.map((imageId) => ({
            imageId,
            imagePath: imagePathMap[imageId] || null,
          })),
        };
      });

      result.push({
        outlet: {
          _id: outletData.outlet._id,
          name: outletData.outlet.name,
          address: outletData.outlet.address,
        },
        latestVisitDate: outletData.latestVisitDate,
        tasks: enrichedTasks,
      });
    }

    return result;
  }

  async getActiveAndInactiveOutlets(salesRepIds, startDate, endDate): Promise<any> {
    const listSalesRep = await this.userModel.find({ saleRepId: { $in: salesRepIds } }).select('_id saleRepId');
    const outlets = await this._omsSalesRepStatisticsService.getStatisticActiveAndInactiveOutlets(
      listSalesRep.map((e) => e._id),
      startDate,
      endDate,
    );
    return outlets;
  }
}
