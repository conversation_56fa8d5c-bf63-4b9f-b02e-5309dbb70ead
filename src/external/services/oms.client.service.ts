import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { sleep } from 'src/utils';
import { OMS_API_ADMIN_AUTH } from '../constants/oms';
import * as qs from 'qs';
import { Cache } from 'cache-manager';

@Injectable()
export class OmsApiClientService {
  private baseUrl: string = process.env.OMS_API_BASE_URL;
  private cookie: any;
  private timeout = 1000 * 60;
  private key = 'oms_token';

  constructor(private readonly _httpService: HttpService, @Inject(CACHE_MANAGER) private cacheManager: Cache) {
    console.log('🚀 ~ process.env.NODE_APP_INSTANCE:', process.env.NODE_APP_INSTANCE);
    if (!process.env.NODE_APP_INSTANCE || (process.env.NODE_APP_INSTANCE && parseInt(process.env.NODE_APP_INSTANCE) === 0)) {
      this.loginOms().then().catch();
    }
  }

  async setCookie(cookie) {
    this.cookie = cookie;
  }

  isLogged() {
    return !!this.cookie;
  }

  async loginOms() {
    const token = await this.cacheManager.get(this.key);
    console.log('🚀 ~ token cached:', token);
    if (!token) {
      try {
        const res = await this.post(OMS_API_ADMIN_AUTH, {
          email: process.env.OMS_EMAIL,
          password: process.env.OMS_PASSWORD,
        });
        if (res.data) {
          const cookieHeaders = res.headers['set-cookie'];
          const cookieValue = cookieHeaders.toString();
          this.setCookie(cookieValue);
          await this.cacheManager.set(this.key, cookieValue, { ttl: 60 * 60 * 6 }); //6 hours
        }
      } catch (error) {
        console.error('Login OMS failed:', error?.message || '');
      }
    } else {
      this.setCookie(token);
    }
  }

  async retryCall(callbackFunction, numberOfRetries: number, delay: number) {
    let res = null;
    for (let i = 0; i < numberOfRetries; i++) {
      try {
        const tempRes = await callbackFunction();
        if (tempRes) {
          res = tempRes;
          break;
        }
      } catch (error) {
        if (error?.response?.status == 401) {
          await this.cacheManager.del(this.key);
          await this.loginOms();
        }
        if ([409, 500, 401].includes(error?.response?.status)) {
          await sleep(delay);
        }
        if (i == numberOfRetries - 1) {
          throw error;
        }
      }
    }
    return res;
  }

  async get(url: string, params?: any, options?: any): Promise<any> {
    let request;
    try {
      request = async () =>
        await lastValueFrom(
          this._httpService.get(this.baseUrl + url, {
            params,
            ...(options || {}),
            timeout: this.timeout,
            headers: {
              cookie: this.cookie,
              ...options?.headers,
            },
            paramsSerializer: {
              serialize: (p) => qs.stringify(p),
            },
          }),
        );
      if (options?.retryConfig) {
        return this.retryCall(request, options?.retryConfig.numberOfRetries, options?.retryConfig.delay);
      }
      return await request();
    } catch (error) {
      if (error?.response?.status == 401) {
        await this.cacheManager.del(this.key);
        await this.loginOms();
        await sleep(500);
        return await request();
      } else {
        throw error;
      }
    }
  }

  async put(url: string, body?: any, options?: any): Promise<any> {
    let request;

    try {
      request = async () =>
        await lastValueFrom(
          this._httpService.put(this.baseUrl + url, body, {
            ...(options || {}),
            timeout: this.timeout,
            headers: {
              cookie: this.cookie,
              ...options?.headers,
            },
          }),
        );
      if (options?.retryConfig) {
        return this.retryCall(request, options?.retryConfig.numberOfRetries, options?.retryConfig.delay);
      }
      return await request();
    } catch (error) {
      if (error?.response?.status == 401) {
        await this.cacheManager.del(this.key);
        await this.loginOms();
        await sleep(500);
        return await request();
      } else {
        throw error;
      }
    }
  }

  async post(url: string, body?: any, options?: any): Promise<any> {
    let request;

    try {
      request = async () =>
        await lastValueFrom(
          this._httpService.post(this.baseUrl + url, body, {
            ...(options || {}),
            timeout: this.timeout,
            headers: {
              cookie: this.cookie,
              ...options?.headers,
            },
          }),
        );

      if (options?.retryConfig) {
        return this.retryCall(request, options?.retryConfig.numberOfRetries, options?.retryConfig.delay);
      }
      return await request();
    } catch (error) {
      if (error?.response?.status == 401) {
        await this.cacheManager.del(this.key);
        await this.loginOms();
        await sleep(500);
        return await request();
      } else {
        throw error;
      }
    }
  }

  async delete(url: string, body?: any, options?: any): Promise<any> {
    let request;
    try {
      request = async () =>
        await lastValueFrom(
          this._httpService.delete(this.baseUrl + url, {
            data: body,
            ...(options || {}),
            timeout: this.timeout,
            headers: {
              cookie: this.cookie,
              ...options?.headers,
            },
          }),
        );
      if (options?.retryConfig) {
        return this.retryCall(request, options?.retryConfig.numberOfRetries, options?.retryConfig.delay);
      }
      return await request();
    } catch (error) {
      if (error?.response?.status == 401) {
        await this.cacheManager.del(this.key);
        await this.loginOms();
        await sleep(500);
        return await request();
      } else {
        throw error;
      }
    }
  }

  throwError(error: any) {
    console.log('🚀 ~ OMS Service Error:', error);
    let message = 'OMS was down or unreachable or something went wrong!';
    if (error?.message) {
      message = error.message;
    }
    if (error?.response?.data?.message) {
      message = error?.response?.data?.message;
    }
    throw {
      code: 400,
      message: 'OMS Error: ' + message,
    };
  }
}

export default OmsApiClientService;
