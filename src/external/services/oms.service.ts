import { CACHE_MANAGER, forwardRef, Inject, Injectable } from '@nestjs/common';
import { OmsApiClientService } from './oms.client.service';
import { OmsCacheService } from './oms-cache.service';
import { Types } from 'mongoose';
import { OutletsService } from 'src/outlets/services/outlets.service';
import {
  OMS_API_ASO_FOR_SALES_REP,
  OMS_API_CREATE_REQUEST,
  OMS_API_DEPOT_ORDER_CREATE,
  OMS_API_DEPOT_ORDERS,
  OMS_API_DEPOT_PROMOTIONS,
  OMS_API_DEPOT_VARIANTS,
  OMS_API_FETCH_OUTLETS,
  OMS_API_LIST_BRANDS,
  OMS_API_LIST_ORDER_AVERAGE_DELIVERED_QUANTITY,
  OMS_API_LIST_ORDER_RECOMMENDATIONS,
  OMS_API_LIST_REQUEST,
  OMS_API_PERFORMANCE_OUTLET_SALE_VOLUME,
  OMS_API_SALES_VOLUME_BY_SALES_REP,
  OMS_API_TEMPORARY_ORDER,
} from '../constants/oms';
import { getEnvironmentString, getRandomTTL, isEmptyObjectOrArray, printLog, sleep } from 'src/utils';
import { Cache } from 'cache-manager';
import { Outlet } from 'src/outlets/schemas/outlet.schema';
import * as moment from 'moment-timezone';
import { FilesService } from 'src/files/services';
import { DistributorService } from 'src/distributor/services';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { CreateRequest, GetListRequest } from 'src/customer-flow/dtos/index.dto';
import { RequestStatus } from 'src/customer-flow/enums/index.enum';
import { ConstantCommons } from '../../utils/constants';
import { UserActionsService } from '../../users/services/user-actions.service';
import { CONVERT_ML_TO_HL, CONVERT_ORIGINAL_PRICE_TO_APAC } from '../constants';
import { OrdersService } from '../../orders/services/orders.service';
import { UsersService } from '../../users/services/users.service'; // @Injectable({ scope: Scope.REQUEST })

// @Injectable({ scope: Scope.REQUEST })
@Injectable()
export class OmsService {
  constructor(
    private readonly omsApiClientService: OmsApiClientService,
    private readonly omsCacheService: OmsCacheService,
    private readonly outletsService: OutletsService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly filesService: FilesService,
    @Inject(forwardRef(() => OutletJourneyPlanningService))
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly distributorService: DistributorService,
    @Inject(forwardRef(() => UserActionsService))
    private readonly userActionsService: UserActionsService,
    @Inject(forwardRef(() => OrdersService))
    private readonly ordersService: OrdersService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
  ) {
    if (!process.env.NODE_APP_INSTANCE || (process.env.NODE_APP_INSTANCE && parseInt(process.env.NODE_APP_INSTANCE) === 0)) {
      this.getConfigMustHaveSku(true).then().catch();
    }
  }

  getPrice(input) {
    if (!input) {
      return 0;
    }
    return Number(input) / CONVERT_ORIGINAL_PRICE_TO_APAC;
  }
  async loginOMS() {
    return this.omsApiClientService.loginOms();
  }

  async getListProductsOMS({ outletInfo, include_empties_deposit, limit, allow_empties_return, last_id, listMustHaveSku }) {
    try {
      const { depotExternalID, outletExternalID, distributorExternalID } = outletInfo;
      const regionValidate = outletInfo?.region?.toString().toLowerCase().trim();
      const channelValidate = outletInfo?.channel?.toString().toLowerCase().trim();
      const subChannelValidate = outletInfo?.subChannel?.toString().toLowerCase().trim();

      const res = await this.omsApiClientService.post(
        OMS_API_DEPOT_VARIANTS,
        {
          outletExternalID,
          depotExternalID,
        },
        {
          params: {
            include_empties_deposit,
            limit,
            allow_empties_return,
            last_id,
            include_stock_level: true,
            expand: 'product.brand',
          },
        },
      );
      if (res.data) {
        let variants = [];
        if (res.data?.variants) {
          variants = res.data?.variants.map((item) => {
            let thresholdLevel = 'low'; // default
            const stockLevel = item?.stockLevels?.length && item?.stockLevels[0];
            if (stockLevel && stockLevel.stock_level == 'high_stock') {
              thresholdLevel = 'high';
            }
            if (stockLevel && stockLevel.stock_level == 'medium_stock') {
              thresholdLevel = 'medium';
            }
            let tagging = 'Optional SKUs';
            const skuValidate = item?.sku?.toString().toLowerCase().trim();
            const titleValidate = item?.product?.title?.toString().toLowerCase().trim();
            if (listMustHaveSku?.length) {
              const found = listMustHaveSku.find((item) => {
                const distributors = item?.distributors;
                const productRegion = item?.region;
                const productChannel = item?.channel;
                const productSubChannel = item?.subChannel;
                const productTitle = item?.productTitle;
                const productSkus = item?.skus;
                if (
                  distributors?.filter((dis) => dis.Distributor === distributorExternalID)?.length &&
                  (productTitle == titleValidate || productSkus.includes(skuValidate)) &&
                  productRegion == regionValidate &&
                  productChannel == channelValidate &&
                  productSubChannel == subChannelValidate
                ) {
                  return true;
                }
                return false;
              });
              if (found) {
                tagging = ConstantCommons.MUST_HAVE_SKU_LABEL;
              }
            }
            return {
              id: item?.id,
              sku: item?.sku,
              title: item?.product?.title,
              subtitle: item?.product?.subtitle,
              pack_type: item?.product?.pack_type,
              thumbnail: item?.product?.thumbnail,
              volume: item?.product?.volume,
              brand_name: item?.product?.brand?.name,
              unit_of_measure: item?.product?.unit_of_measure,
              pack_type_filter: item?.product?.unit_of_measure,
              // item_category,
              empties_deposit: this.getPrice(item?.empties_deposit),
              unit_price: this.getPrice(item?.calculated_price),
              currency_code: item?.prices?.length ? item?.prices[0]?.currency_code : '',
              inventory_quantity: item?.inventory_quantity,
              stock_quantity: item?.stock_quantity,
              thresholdLevel,
              tagging,
              check_stock_quantity: 0,
              promotion_code: null,
            };
          });
        }
        return variants;
      }
    } catch (error) {
      this.omsApiClientService.throwError(error);
    }
    return [];
  }

  async getAllProductsWithLastIdOMS(query) {
    const limit = 20;
    let last_id = null;
    let res = [];
    let reCall = true;
    while (reCall) {
      const temp = await this.getListProductsOMS({ ...query, last_id, limit });
      const length = temp.length;
      if (length) {
        res = res.concat(temp);
      }
      if (!length || length < limit) {
        reCall = false;
      }
      last_id = temp[length - 1]?.id;
    }
    return res;
  }

  async getListPromotionsOMS({ outletExternalID, depotExternalID, start_before, end_after = null }) {
    try {
      const res = await this.omsApiClientService.get(OMS_API_DEPOT_PROMOTIONS, {
        depot_external_id: depotExternalID,
        outlet_external_id: outletExternalID,
        start_before,
        end_after,
      });
      if (res.data) {
        let promotions = [];
        if (res.data.promotionList) {
          promotions = res.data.promotionList
            .filter((promotion) => promotion?.sku != null)
            .map((promotion) => {
              return {
                promotion_scheme: promotion?.promotion_scheme,
                code: promotion?.qap_id,
                periode_start: promotion?.periode_start,
                periode_end: promotion?.periode_end,
                promo_type: promotion?.promo_type,
                sku: promotion?.sku,
                buy: promotion?.buy,
                free: promotion?.free,
                cap: promotion?.cap,
                discount: promotion?.discount,
                subtotal: promotion?.subtotal,
                banner_url: promotion?.banner_url,
              };
            });
        }
        return promotions;
      }
    } catch (error) {
      this.omsApiClientService.throwError(error);
    }
    return [];
  }

  parseOrder(order) {
    let sales_rep_name = null;
    if (order?.customer?.email) {
      sales_rep_name = order.customer.email;
    }
    if (order?.customer?.first_name) {
      sales_rep_name = `${order.customer.first_name} ${order.customer.last_name}`;
    }
    if (order?.sales_rep_name) {
      sales_rep_name = order.sales_rep_name;
    }

    return {
      id: order.id,
      display_id: order.display_id,
      status: order.extended_status || order.status,
      created_at: order.created_at,
      sales_rep_name: sales_rep_name || order.email,
      currency_code: order.currency_code,
      source_system: order?.source_system,
      item_tax_total: order?.item_tax_total,
      total: this.getPrice(order.total),
      discount_total: this.getPrice(order.discount_total),
      subtotal: this.getPrice(order.subtotal),
      tax_total: this.getPrice(order.tax_total),
      empties_deposit: this.getPrice(order.empties_deposit),
      items: order.items.map((item) => {
        const hasPromotion = item?.promotion_codes?.length > 0;
        let isFreeItem = false;
        if (item?.item_category == 'YSRG') {
          isFreeItem = true;
        }
        if (item?.variant?.metadata?.type == 'EMPTY') {
          isFreeItem = true;
        }
        return {
          promotion_code: item?.promotion_codes?.length > 0 ? item?.promotion_codes[0] : null,
          sku: item?.variant?.sku,
          title: item?.variant?.product?.title || '',
          subtitle: item?.variant?.product?.subtitle || '',
          pack_type: item?.variant?.product?.pack_type,
          thumbnail: item?.variant?.product?.thumbnail,
          //Custom fields
          hasPromotion,
          isFreeItem: isFreeItem,
          currency_code: order.currency_code,
          unit_price: this.getPrice(item?.unit_price),
          quantity: item?.quantity || 0,
          total: this.getPrice(item?.total),
        };
      }),
    };
  }

  async createOrderOMS({ outletId, outletExternalID, depotExternalID, email, items, couponCodes, deliveryInstruction }) {
    try {
      const form = {
        order_type: 'standard',
        email: email || process.env.OMS_EMAIL,
        items: items,
        depotExternalID,
        outletExternalID,
        metadata: {
          source_system: 'REP',
          delivery_instruction: deliveryInstruction,
        },
      };
      if (couponCodes.length) {
        form.metadata['coupon_codes'] = couponCodes;
      }
      const res = await this.omsApiClientService.post(OMS_API_DEPOT_ORDER_CREATE, form, {
        retryConfig: {
          numberOfRetries: 3,
          delay: 2000,
        },
      });

      try {
        this.userActionsService
          .create({
            userId: email,
            isUserAdmin: false,
            key: 'OMS_API_DEPOT_ORDER_CREATE',
            feature: 'OMS_API_DEPOT_ORDER_CREATE',
            method: 'POST',
            request: { form: JSON.stringify(form), res: JSON.stringify(res?.data) },
          })
          .then((r) => printLog('OMS_API_DEPOT_ORDER_CREATE', r));
      } catch (e) {
        printLog('OMS_API_DEPOT_ORDER_CREATE', e);
      }

      if (res.data) {
        const order = res.data.order;
        const parseOrder = this.parseOrder(order);
        const keyData = `cacheomsdata_${outletId.toString()}`;
        let cached = (await this.cacheManager.get(keyData)) as any;
        if (!cached) {
          cached = await this.omsCacheService.findOne({
            outlet: outletId,
          });
        }
        const listOrders = (cached.orders as any) || [];
        listOrders.unshift(parseOrder);
        cached.orders = listOrders;
        cached.latestOrder = parseOrder;
        await Promise.all([
          this.cacheManager.del(keyData),
          this.omsCacheService.model.updateOne(
            { outlet: new Types.ObjectId(outletId) },
            {
              latestOrder: parseOrder,
              orders: listOrders,
            },
            {
              upsert: true,
            },
          ),
        ]);

        return parseOrder;
      }
    } catch (error) {
      this.omsApiClientService.throwError(error);
    }
    return null;
  }

  async createTemporaryOrderOMS({ outletExternalID, depotExternalID, email, items, couponCodes, deliveryInstruction }) {
    try {
      const form = {
        order_type: 'standard',
        email: email || process.env.OMS_EMAIL,
        items: items,
        depotExternalID,
        outletExternalID,
        metadata: {
          source_system: 'REP',
          delivery_instruction: deliveryInstruction,
        },
      };
      if (couponCodes.length) {
        form.metadata['coupon_codes'] = couponCodes;
      }
      const res = await this.omsApiClientService.post(OMS_API_TEMPORARY_ORDER, form);
      if (res.data) {
        const order = res.data.order;
        const parseOrder = this.parseOrder(order);
        return parseOrder;
      }
    } catch (error) {
      this.omsApiClientService.throwError(error);
    }
    return null;
  }

  async getListOrdersOMS({ outletExternalID, depotExternalID, offset, limit, start_date, end_date, search, status }) {
    const optionParams = {};
    if (start_date && end_date) {
      optionParams['start_date'] = moment(start_date).startOf('day').toISOString();
      optionParams['end_date'] = moment(end_date).endOf('day').toISOString();
    }
    if (search) {
      optionParams['q'] = search;
    }
    if (status) {
      optionParams['extended_status'] = status;
    }
    try {
      const res = await this.omsApiClientService.get(OMS_API_DEPOT_ORDERS, {
        order_type: 'standard',
        outlet_external_id: outletExternalID,
        depot_external_id: depotExternalID,
        order: '-created_at',
        offset,
        limit,
        fields: 'extended_status,display_id,id,created_at,currency_code,metadata,source_system',
        expand: 'items,items.variant,items.variant.product,customer',
        ...optionParams,
      });
      if (res.data) {
        let orders = [];
        if (res.data.orders) {
          orders = res.data.orders.map((order) => {
            return this.parseOrder(order);
          });
        }
        return orders;
      }
    } catch (error) {
      this.omsApiClientService.throwError(error);
    }
    return [];
  }

  async cacheDataByOutlet(outletInfo, throwError = false) {
    try {
      const listMustHaveSku: any = (await this.getConfigMustHaveSku()) || [];
      const [listOmsProducts, listPromotions, listOrders] = await Promise.all([
        this.getAllProductsWithLastIdOMS({
          allow_empties_return: false,
          include_empties_deposit: true,
          outletInfo,
          listMustHaveSku,
        }),
        this.getListPromotionsOMS({
          depotExternalID: outletInfo.depotExternalID,
          outletExternalID: outletInfo.outletExternalID,
          start_before: new Date().toISOString(),
          end_after: new Date().toISOString(),
        }),
        this.getListOrdersOMS({
          depotExternalID: outletInfo.depotExternalID,
          outletExternalID: outletInfo.outletExternalID,
          limit: 5,
          offset: 0,
          start_date: null,
          end_date: null,
          search: null,
          status: null,
        }),
      ]);

      const listProducts = await this.getOrderAverageQuantity(outletInfo.depotExternalID, outletInfo, listOmsProducts);

      const latestOrder = listOrders.length ? listOrders[0] : {};
      // add hasPromotion to list products
      listPromotions.forEach((promotion) => {
        const item = listProducts.find((e) => e.sku === promotion?.sku);
        if (item) {
          item.hasPromotion = true;
          item.promotion_code = promotion.code;
        }
      });

      this.omsCacheService.model
        .updateOne(
          { outlet: new Types.ObjectId(outletInfo.outletId) },
          {
            latestOrder: latestOrder,
            orders: listOrders,
            products: listProducts,
            promotions: listPromotions,
          },
          {
            upsert: true,
          },
        )
        .then()
        .catch();
      this.outletsService
        .update(outletInfo.outletId, {
          totalProductAssigned: listProducts.length,
        })
        .then()
        .catch();

      // Check REP Order in REP system
      this.checkExistingRepOrder(outletInfo, listOrders).catch().then();

      return {
        latestOrder: latestOrder,
        orders: listOrders,
        products: listProducts,
        promotions: listPromotions,
      };
    } catch (error) {
      if (throwError) {
        throw error;
      }
    }
  }

  /**
   *
   * @param outletInfo
   * @param listOrders
   */
  async checkExistingRepOrder(outletInfo: any, listOrders: any) {
    try {
      const repOrders = listOrders.filter((order) => order?.source_system === 'REP');

      if (repOrders.length > 0) {
        const existingOrderIds = await this.ordersService.getOrdersByConditions(repOrders.map((order) => order.id));

        const newOrders = repOrders.filter((order) => !existingOrderIds.includes(order.id));

        if (newOrders.length > 0) {
          const userRelation = await this.outletsService.getOutletSalesRepRelationsByIds(outletInfo.outletId);

          const orderOMSRecords = newOrders.map((order) => ({
            salesRep: userRelation.saleRep._id || null,
            outlet: new Types.ObjectId(outletInfo.outletId),
            orderDate: new Date(order.created_at),
            orderId: order.id,
            displayId: order.display_id,
            depotId: outletInfo.depotExternalID,
          }));
          if (orderOMSRecords.length > 0) {
            await this.ordersService.insertMany(orderOMSRecords);
          }
        }
      }
    } catch (e) {
      printLog('Error syncing orders to OrderOMS:', e);
    }
  }

  async getCachedDataByOutlet({ outletId, useDb, project }: { outletId: Types.ObjectId; useDb?: boolean; project?: any }) {
    const key = `cacheoms_${outletId.toString()}`;
    const keyData = `cacheomsdata_${outletId.toString()}`;
    let dataCached: any = (await this.cacheManager.get(keyData)) as any;
    if (dataCached) {
      return dataCached;
    }

    let isPending = await this.cacheManager.get(key);
    if (!isPending) {
      await this.cacheManager.set(key, true, { ttl: 30 }); //  30 seconds
      if (useDb) {
        dataCached = await this.omsCacheService.findOne(
          {
            outlet: outletId,
          },
          project,
        );
      }
      // get new data from OMS
      if (!dataCached) {
        const outlet = await this.outletsService.findOne({ _id: outletId, ucc: { $ne: null }, depotId: { $nin: [null, ''] } });
        let region = outlet?.region;
        if (!region) {
          const distributor = await this.distributorService.findOne({
            distributorId: outlet.distributorId,
          });
          region = distributor.region;
        }
        if (outlet) {
          const outletInfo = {
            outletId,
            distributorExternalID: outlet.distributorId,
            depotExternalID: outlet.depotId,
            outletExternalID: outlet.ucc,
            ucc: outlet.ucc,
            region,
            channel: outlet.channel,
            subChannel: outlet.subChannel,
          };
          dataCached = (await this.cacheDataByOutlet(outletInfo)) as any;
          if (dataCached) {
            await this.cacheManager.set(keyData, dataCached, { ttl: (process.env.TIME_CACHE_OMS && Number(process.env.TIME_CACHE_OMS)) || 60 * 15 });
          }
        }
      }
      this.cacheManager.del(key);
      return dataCached;
    }

    // wait until the data is saved to the database or until 10 seconds have passed
    let count = 0;
    const timeSleep = 1000;
    const maxTimeWait = 10000;
    while (isPending) {
      await sleep(timeSleep);
      isPending = await this.cacheManager.get(key);
      if (timeSleep * count >= maxTimeWait) {
        this.cacheManager.del(key).then();
        isPending = false;
      }
      count++;
    }
    return (
      (await this.cacheManager.get(keyData)) ||
      (await this.omsCacheService.findOne(
        {
          outlet: outletId,
        },
        project,
      ))
    );
  }

  async getFullCachedDataByOutlets({ outletIds, project }: { outletIds: Types.ObjectId[]; project: any }) {
    const BATCH_SIZE = 500;
    let allResults = [];
    for (let i = 0; i < outletIds.length; i += BATCH_SIZE) {
      const batchOutletIds = outletIds.slice(i, i + BATCH_SIZE);
      const keyData = `cacheoms_batch_${i}_${batchOutletIds.map((o) => o.toString()).join('_')}`;

      let batchData = (await this.cacheManager.get(keyData)) as any;

      if (!batchData) {
        batchData = await this.omsCacheService.findAll(
          {
            outlet: { $in: batchOutletIds },
          },
          project,
          { sort: { updatedAt: -1 } },
        );

        if (batchData?.length) {
          await this.cacheManager.set(keyData, batchData, { ttl: 60 * 5 }); // Cache trong 2 phút
        }
      }

      if (batchData?.length) {
        allResults = [...allResults, ...batchData];
      }
      // printLog(i, batchOutletIds.length, batchData?.length);
    }

    return allResults;
  }

  async getCachedDataByOutlets(outletIds: Types.ObjectId[]) {
    const thread = 2;
    while (outletIds.length > 0) {
      const temp = outletIds.splice(0, thread);
      try {
        await Promise.all(
          temp.map(async (outlet) => {
            await this.getCachedDataByOutlet({
              outletId: outlet,
              useDb: false,
            });
          }),
        );
      } catch (error) {}
    }
    console.log('done');
  }

  async isOmsOutletConnected(outletId: string, outletExternalID: string, depotExternalID: string) {
    try {
      if (!depotExternalID || !outletExternalID || !outletId) {
        return false;
      }
      this.outletsService
        .update(outletId, {
          isOmsConnected: true,
        })
        .then()
        .catch();
      return true;
    } catch (error) {
      return false;
    }
  }

  async checkOutletsConnected(outlets: Outlet[]) {
    const list = [];
    while (outlets.length > 0) {
      const temp = outlets.splice(0, 3);
      try {
        if (!isEmptyObjectOrArray(temp)) {
          await Promise.all(
            temp?.map(async (outlet) => {
              const outletExternalID = outlet.ucc;
              const depotExternalID = outlet.depotId;
              const key = `oms-connection-${outlet._id}`;
              let isOmsConnectedCached = await this.cacheManager.get(key);
              if (!isOmsConnectedCached) {
                isOmsConnectedCached = await this.isOmsOutletConnected(outlet._id, outletExternalID, depotExternalID);
                await this.cacheManager.set(key, isOmsConnectedCached ? 'connected' : 'disconnected', { ttl: 60 * 10 });
              }
              if (isOmsConnectedCached == true || isOmsConnectedCached == 'connected') {
                list.push(outlet);
              }
            }),
          );
        }
      } catch (error) {}
    }
    return list;
  }

  async getSalesRepStatisticsOMS({ salesRepIds, depotId, startDate, endDate }: { salesRepIds: string[]; depotId: string; startDate: Date; endDate: Date }) {
    const res = {};
    for (const iterator of salesRepIds) {
      res[iterator] = {
        contact_external_id: iterator,
        sale_volume: 0,
        sale_value: 0,
        aso_total: 0,
      };
    }
    try {
      while (salesRepIds.length) {
        const contact_external_ids = salesRepIds.splice(0, 15);
        const [saleValueAndVolumeRes, asoSalesRepRes] = await Promise.all([
          this.omsApiClientService.get(OMS_API_SALES_VOLUME_BY_SALES_REP, {
            invoice_date_start: startDate,
            invoice_date_end: endDate,
            depot_external_id: depotId,
            order_type: 'standard',
            contact_external_ids,
          }),
          this.omsApiClientService.get(OMS_API_ASO_FOR_SALES_REP, {
            invoice_date_start: startDate,
            invoice_date_end: endDate,
            depot_external_id: depotId,
            contact_external_ids,
          }),
        ]);
        if (saleValueAndVolumeRes?.data) {
          saleValueAndVolumeRes?.data?.chart?.data?.forEach((e) => {
            res[e.contact_external_id].sale_volume += Number(e.sale_volume || 0);
            res[e.contact_external_id].sale_value += Number(e.sale_value || 0);
          });
        }
        if (asoSalesRepRes?.data) {
          asoSalesRepRes?.data?.chart?.data?.forEach((e) => {
            res[e.contact_external_id].aso_total += Number(e.aso_total || 0);
          });
        }
      }
    } catch (error) {
      console.log('🚀 ~ error:', error);
    }
    return Object.values(res).map((e: any) => {
      return {
        ...e,
        sale_value: this.getPrice(e.sale_value),
        sale_volume: e.sale_volume / CONVERT_ML_TO_HL,
      };
    });
  }

  async getConfigMustHaveSku(getNewData = false) {
    const key = `${ConstantCommons.MUST_HAVE_SKU}_${getEnvironmentString()}`;
    if (!getNewData) {
      const cached = await this.cacheManager.get(key);
      if (cached) {
        return cached;
      }
    }
    const url = process.env.MUST_HAVE_SKU_EXCEL_URL;
    const data = await this.filesService.readXlsxFromLink(url);
    if (data && data.length) {
      const regionData = data.find((e) => e.sheetName == 'Region');
      const productData = data.find((e) => e.sheetName == 'Product');
      if (regionData?.data?.length) {
        await Promise.all(
          regionData?.data?.map(async (data: any) => {
            if (data?.Distributor && data?.Region) {
              const updated = await this.distributorService.updateByCondition(
                {
                  distributorId: data.Distributor.trim(),
                },
                {
                  region: data?.Region?.trim(),
                },
              );
            }
          }),
        );
        await this.outletsService.migrateData();
        await this.clearAllCache();
      }
      if (productData?.data?.length) {
        const data = [];
        for (const element of productData?.data) {
          const temp = {
            distributors: regionData?.data?.filter((r: any) => r.Region?.toString().toLowerCase().trim() === element['Region']?.toString().toLowerCase().trim()),
            region: element['Region']?.toString().toLowerCase().trim(),
            channel: element['Channel']?.toString().toLowerCase().trim(),
            subChannel: element['Sub Channel']?.toString().toLowerCase().trim(),
            productTitle: element['Product Title']?.toString().toLowerCase().trim(),
            skus: element['SKU']
              ?.toString()
              ?.split(',')
              .map((text) => text?.toString().toLowerCase().trim()),
          };
          data.push(temp);
        }
        await this.cacheManager.set(key, data, { ttl: 60 * 60 });
        return data;
      }
      return null;
    }
  }

  /**
   *
   * @param ucc
   * @param mustHaveSku
   * @param variant
   */
  async clearAllCache(ucc?: string, mustHaveSku?: boolean, variant?: boolean) {
    if (ucc) {
      const key1 = `${ConstantCommons.OMS_ORDER_RECOMMENDATION}_${ucc}`;
      const key2 = `${ConstantCommons.OMS_ORDER_AVERAGE_QUANTITY}_${ucc}_promotion`;
      const key3 = `${ConstantCommons.OMS_ORDER_AVERAGE_QUANTITY}_${ucc}_product`;
      const key4 = `${ConstantCommons.OMS_PERFORMANCE_OUTLET_SALE_VOLUME}_${ucc}`;
      await this.cacheManager.del(key1);
      await this.cacheManager.del(key2);
      await this.cacheManager.del(key3);
      await this.cacheManager.del(key4);
    }

    if (variant) {
      const outlet = await this.outletsService.findOne({ ucc });
      if (!isEmptyObjectOrArray(outlet)) {
        const key5 = `cacheomsdata_${outlet?._id?.toString()}`;
        await this.cacheManager.del(key5);
      }
    }

    if (mustHaveSku) {
      await this.cacheManager.del(`${ConstantCommons.MUST_HAVE_SKU}_${getEnvironmentString()}`);
    }

    if (ucc || mustHaveSku || variant) {
      return;
    }

    const listJp = await this.outletJourneyPlanningService.getAllTodayPlans();
    if (!isEmptyObjectOrArray(listJp)) {
      await Promise.all(
        listJp.map(async (jp) => {
          const keyData = `cacheomsdata_${jp?.outlet?._id.toString()}`;
          await this.cacheManager.del(keyData);
        }),
      );
    }
    await this.cacheManager.del(`${ConstantCommons.MUST_HAVE_SKU}_${getEnvironmentString()}`);
  }

  async getOutletChannelInfo(listUcc: string[]) {
    try {
      const res = await this.omsApiClientService.post(
        `${OMS_API_FETCH_OUTLETS}?expand=outletBusinessSegments.businessSegment%2CoutletClassifications.classification%2CoutletChannels.channel%2CoutletSubChannels.subChannel`,
        {
          external_id: listUcc.join(','),
        },
      );
      return res?.data?.outlets || [];
    } catch (error) {
      printLog('Get outlet channel info failed', error);
      return [];
    }
  }

  async getConfigBrands(getNewData = false) {
    const key = ConstantCommons.OMS_BRAND;
    if (!getNewData) {
      const cached = await this.cacheManager.get(key);
      if (cached) {
        return cached;
      }
    }
    let brands = [];
    try {
      const res = await this.omsApiClientService.get(OMS_API_LIST_BRANDS);
      if (res?.data?.brands) {
        brands = res.data?.brands || [];
        brands = brands.map((e) => e.name);
        await this.cacheManager.set(key, brands, { ttl: 60 * 10 });
      }
    } catch (error) {}
    return brands;
  }

  /**
   *
   * @param depotExternalID
   * @param outlet
   * @param checkStockSkus
   * @param getNewData
   */
  async getOrderRecommendation(depotExternalID: string, outlet: Outlet, checkStockSkus: any = null, getNewData = false) {
    try {
      let products = [];
      const cacheData = await this.getCachedDataByOutlet({
        outletId: new Types.ObjectId(outlet._id),
        useDb: false,
      });
      const mustHaveSKUs = cacheData?.products?.filter((p) => p.tagging == ConstantCommons.MUST_HAVE_SKU_LABEL);
      const key = `${ConstantCommons.OMS_ORDER_RECOMMENDATION}_${outlet.ucc}`;
      const orderRecommendationTime = await this.cacheManager.get(key);
      if (orderRecommendationTime) {
        products = cacheData?.orderRecommendations;
      }

      if (!orderRecommendationTime) {
        try {
          const res = await this.omsApiClientService.get(OMS_API_LIST_ORDER_RECOMMENDATIONS, {
            depot_external_id: depotExternalID,
            outlet_external_id: outlet.ucc,
            sku: mustHaveSKUs?.map((mustHaveSKU) => mustHaveSKU.sku),
          });
          products = res?.data;
          this.omsCacheService.model
            .updateOne(
              { outlet: new Types.ObjectId(outlet._id) },
              {
                orderRecommendations: products,
              },
              {
                upsert: true,
              },
            )
            .then()
            .catch();
        } catch (e) {
          printLog('getOrderRecommendation', e);
        }
        this.cacheManager.set(key, new Date(), { ttl: getRandomTTL() }).then();
      }

      return mustHaveSKUs
        .map((mustHaveSKU: any) => {
          const matchedProduct = products?.find((product: any) => product.sku === mustHaveSKU.sku) || {};
          const checkStockQuantity = checkStockSkus?.find((s: any) => s.sku === mustHaveSKU.sku)?.check_stock_quantity || 0;

          const averageQuantity = Math.max(matchedProduct?.average_quantity || 0, 1);
          const stockQuantity = Math.max(Math.floor((averageQuantity - checkStockQuantity) * 1.5), 1);

          return {
            ...mustHaveSKU,
            average_quantity: matchedProduct?.average_quantity || 0,
            stock_quantity: stockQuantity,
          };
        })
        .filter(Boolean);
    } catch (e) {
      return [];
    }
  }

  /**
   *
   * @param depotExternalID
   * @param outlet
   * @param products
   * @param isPromo
   * @param checkStockSkus
   * @param getNewData
   */
  async getOrderAverageQuantity(depotExternalID: string, outlet: any, products: any, isPromo = false, getNewData = false) {
    try {
      if (isEmptyObjectOrArray(products)) {
        return [];
      }

      let omsQuantityData = [];
      const key = `${ConstantCommons.OMS_ORDER_AVERAGE_QUANTITY}_${outlet.outletExternalID || outlet.ucc}_${isPromo ? 'promotion' : 'product'}`;
      if (!getNewData) {
        omsQuantityData = await this.cacheManager.get(key);
      }

      if (!omsQuantityData || getNewData) {
        try {
          const res = await this.omsApiClientService.get(OMS_API_LIST_ORDER_AVERAGE_DELIVERED_QUANTITY, {
            depot_external_id: depotExternalID,
            outlet_external_id: outlet.outletExternalID || outlet.ucc,
            sku: products?.map((p) => p.sku),
          });
          omsQuantityData = res?.data?.filter((p) => p.average_quantity > 0) || [];
        } catch (e) {
          printLog('getOrderRecommendation', e);
        }
        this.cacheManager.set(key, omsQuantityData, { ttl: getRandomTTL() }).then();
      }

      products = await Promise.all(
        products?.map((product: any) => {
          const aqData = omsQuantityData?.find((aq: any) => aq.sku === product.sku);
          return {
            ...product,
            average_quantity: aqData?.average_quantity || 0,
          };
        }),
      );

      return products;
    } catch (e) {
      return [];
    }
  }

  /**
   *
   * @param depotExternalID
   * @param outlet
   * @param start
   * @param end
   * @param channelMonthlyAverageVolume
   * @param monthlyVolume
   * @param yearToDate
   * @param volumePerformance
   * @param averageVolume
   * @param getNewData
   */
  async getPerformanceOutletSaleVolume(
    depotExternalID: string,
    outlet: any,
    start: any,
    end: any,
    channelMonthlyAverageVolume = true,
    monthlyVolume = true,
    yearToDate = true,
    volumePerformance = true,
    averageVolume = true,
    getNewData = false,
  ) {
    let result: any = {
      start,
      end,
      average_volume: {},
      volume_performance: {},
      year_to_date: {},
      monthly_volume: [],
      channel_monthly_average_volume: [],
    };
    try {
      const key = `${ConstantCommons.OMS_PERFORMANCE_OUTLET_SALE_VOLUME}_${outlet.outletExternalID || outlet.ucc}`;
      if (!getNewData) {
        result = await this.cacheManager.get(key);
      }
      if (!result?.average_volume || getNewData) {
        const omsPerformanceData = await this.omsApiClientService.get(OMS_API_PERFORMANCE_OUTLET_SALE_VOLUME, {
          depot_external_id: depotExternalID,
          outlet_external_id: outlet.outletExternalID || outlet.ucc,
          channel_monthly_average_volume: channelMonthlyAverageVolume,
          monthly_volume: monthlyVolume,
          year_to_date: yearToDate,
          volume_performance: volumePerformance,
          average_volume: averageVolume,
          start,
          end,
        });
        result = omsPerformanceData?.data?.chart || {
          start,
          end,
          average_volume: {},
          volume_performance: {},
          year_to_date: {},
          monthly_volume: [],
          channel_monthly_average_volume: [],
        };
        this.cacheManager.set(key, result, { ttl: getRandomTTL() }).then();
      }
      return result;
    } catch (e) {
      printLog('getPerformanceOutletSaleVolume', e);
      return result;
    }
  }

  parseRequestItem(item: any) {
    if (!item) return null;
    let location: any = { current_value: null, new_value: null };
    if (item?.geographical_location) {
      const geo = item.geographical_location;
      const extractValues = (type: string) => {
        const fields = ['region', 'street', 'address_line', 'house_number', 'latitude', 'longitude'];
        /** IF:
         * "geographical_location": {
         *     "city": "Dki Jakarta",
         *     "region": "Jakarta",
         *     ...
         * } THEN: current_value
         */
        return fields.reduce(
          (result, field) => {
            result[field] = geo[field]?.[type] || (type === 'current_value' && geo[field]?.[type] === undefined ? geo[field] || null : null);
            return result;
          },
          {
            city: geo.region?.[type] || (type === 'current_value' && geo.region?.[type] === undefined ? geo.region || null : null),
          },
        );
      };

      const currentValueObj = extractValues('current_value');
      const newValueObj = extractValues('new_value');

      const currentValue = Object.values(currentValueObj).some((v) => v !== null) ? currentValueObj : null;
      const newValue = Object.values(newValueObj).some((v) => v !== null) ? newValueObj : null;
      if (newValue) {
        location = {
          current_value: currentValue,
          new_value: newValue,
        };
      } else {
        location = currentValue;
      }
    }
    let channel = { current_value: null, new_value: null };
    let sub_channel = { current_value: null, new_value: null };
    if (item?.channels?.new_value) {
      channel.current_value = item.channels?.current_value?.[item.channels?.current_value?.length - 1] || '';
      channel.new_value = item.channels?.new_value?.[item.channels?.new_value?.length - 1] || '';
    } else {
      channel = item.channels?.[item.channels?.length - 1] || '';
    }
    if (item?.sub_channels?.new_value) {
      sub_channel.current_value = item.sub_channels?.current_value?.[item.sub_channels?.current_value.length - 1] || '';
      sub_channel.new_value = item.sub_channels?.new_value?.[item.sub_channels?.new_value.length - 1] || '';
    } else {
      sub_channel = item.sub_channels?.[item.sub_channels?.length - 1] || '';
    }

    return {
      status: item.status || '',
      declined_reason: item.declined_reason || '',
      name: item.trading_name || item.legal_name || '',
      phone_number: item.phone_number || '',
      images: item.photo_url || [],
      channel,
      sub_channel,
      classifications: item.classifications?.[0] || '',
      created_at: item.created_at,
      id: item.id,
      display_id: item?.display_id,
      external_id: item?.external_id,
      outlet_id: item?.outlet_id,
      location,
    };
  }

  async getListRequest(salesrep: string, params: GetListRequest) {
    const { offset = 0, limit = 10, status, fromDate, toDate } = params;

    let statusQuery: any = status;
    if (status === RequestStatus.ALL) {
      statusQuery = [RequestStatus.APPROVED, RequestStatus.DECLINED, RequestStatus.PENDING, RequestStatus.ADMIN_APPROVED];
    }

    const query: any = {
      offset,
      limit,
      status: statusQuery,
    };

    if (fromDate && toDate) {
      query.created_at = { gt: fromDate, lt: toDate };
      query.updated_at = { gt: fromDate, lt: toDate };
    }

    try {
      const { data: resData } = await this.omsApiClientService.get(OMS_API_LIST_REQUEST, query);

      if (resData && Array.isArray(resData.data)) {
        const { data: list = [], count = 0, offset: resOffset = 0, limit: resLimit = 10 } = resData;

        const formattedList = list.map((item) => this.parseRequestItem(item));

        return {
          list: formattedList,
          count,
          offset: resOffset,
          limit: resLimit,
        };
      }
    } catch (error) {
      this.omsApiClientService.throwError(error);
    }

    // Return default data structure if data fails to load or error occurs
    return { list: [], count: 0, offset, limit };
  }

  async createRequest(salesrep: string, depotId: string, data: CreateRequest, photoUrl?: string[]) {
    try {
      const body = {
        request_type: 'new',
        trading_name: data.name,
        legal_name: data.name,
        phone_number: data.phoneNumber,
        status: true,
        geographical_location: data.location
          ? {
              location_type: 'shipping',
              address_line: data.location.addressLine,
              street: data.location.street,
              house_number: data.location.houseNumber,
              region: data.location.region,
              city: data.location.city,
              latitude: data.location.latitude,
              longitude: data.location.longitude,
            }
          : undefined,
        depots: [
          {
            depot_external_id: depotId,
          },
        ],
        channels: data.businessChannel,
        sub_channels: data.businessSubChannel,
        photo_url: photoUrl || [],
      };
      const res = await this.omsApiClientService.post(OMS_API_CREATE_REQUEST, body);
      if (res.data) {
        return this.parseRequestItem(res.data.new_request);
      }
    } catch (error) {
      this.omsApiClientService.throwError(error);
    }
    return null;
  }

  async updateRequest(salesrep: string, depotId: string, data: CreateRequest, photoUrl?: string[]) {
    try {
      const body = {
        external_id: data.externalId,
        request_type: 'update',
        trading_name: data.name,
        legal_name: data.name,
        phone_number: data.phoneNumber,
        status: true,
        geographical_location: data.location
          ? {
              location_type: 'shipping',
              address_line: data.location.addressLine,
              street: data.location.street,
              house_number: data.location.houseNumber,
              region: data.location.region,
              city: data.location.city,
              latitude: data.location.latitude,
              longitude: data.location.longitude,
            }
          : undefined,
        depots: [
          {
            depot_external_id: depotId,
          },
        ],
        channels: data.businessChannel,
        sub_channels: data.businessSubChannel,
        photo_url: photoUrl || [],
      };
      const res = await this.omsApiClientService.post(OMS_API_CREATE_REQUEST, body);
      if (res.data) {
        return this.parseRequestItem(res.data.new_request);
      }
    } catch (error) {
      this.omsApiClientService.throwError(error);
    }
    return null;
  }
}
