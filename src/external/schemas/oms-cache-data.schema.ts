import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Types } from 'mongoose';

import { Outlet } from '../../outlets/schemas/outlet.schema';
import { BaseSchema } from '../../shared/schemas/base.schema';

export type OmsCacheDataDocument = OmsCacheData & Document;
@Schema({
  timestamps: true,
})
export class OmsCacheData extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({
    default: null,
    index: true,
  })
  outletExternalId: string;

  @Prop({ type: mongoose.Schema.Types.Array })
  products: [];

  @Prop({ type: mongoose.Schema.Types.Array })
  promotions: [];

  @Prop({ type: mongoose.Schema.Types.Array })
  orders: [];

  @Prop({ type: mongoose.Schema.Types.Mixed })
  latestOrder: any;

  @Prop({ type: mongoose.Schema.Types.Array })
  orderRecommendations: [];

  @Prop({ type: mongoose.Schema.Types.Array })
  getOrderAverageQuantities: [];

  @Prop({ type: mongoose.Schema.Types.Array })
  orderPerformanceData: any;
}
export const OmsCacheDataSchema = SchemaFactory.createForClass(OmsCacheData);
