import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartnerRelation } from '../entities/business-partner-relation/business-partner-relation.entity';
import { BusinessPartnerRelationDto } from '../dtos/business-partner-relation.dto';
import { BusinessPartnerContactRole, BusinessPartnerRelationCommunication, BusinessPartnerRelationType } from '../constants/business-partner.enum';
import { ContactDistributorRelations } from 'src/call-center/constants/call-plan.type';

@Injectable()
export class BusinessPartnerRelationService extends BaseSQLService<BusinessPartnerRelation> {
  constructor(
    @InjectRepository(BusinessPartnerRelation)
    private readonly _businessPartnersRelationRepository: Repository<BusinessPartnerRelation>,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {
    super();
    this._repository = this._businessPartnersRelationRepository;
  }

  async update(id: string, updateData: BusinessPartnerRelation) {
    const existedBusinessPartnerDetail = await this._repository.findOne({ where: { id } });
    if (!existedBusinessPartnerDetail) {
      throw new BadRequestException('partner_detail.not_found');
    }
    return this._repository.update(id, { ...existedBusinessPartnerDetail, ...updateData });
  }

  async updateRelation(updateData: BusinessPartnerRelationDto) {
    let updateDataObj: any = { ...updateData };
    const checkExisted = await this._repository.findOne({
      where: { businessPartner1: updateData.businessPartner1, businessPartner2: updateData.businessPartner2, businessPartnerRelationType: updateData.businessPartnerRelationType },
    });
    if (checkExisted) {
      updateDataObj = { ...updateDataObj, id: checkExisted.id };
    }
    return this._repository.save(updateDataObj);
  }

  async createRelation(data: any, entityManager?: EntityManager) {
    const newRelationEntity = this._repository.create(data);

    if (entityManager) {
      return entityManager.save(BusinessPartnerRelation, newRelationEntity);
    }
    return this._repository.save(newRelationEntity);
  }

  async createRelations(data: any[], entityManager?: EntityManager) {
    if (data.length < 1) {
      return [];
    }
    const newRelationEntities = this._repository.create(data);
    if (entityManager) {
      return entityManager.save(BusinessPartnerRelation, newRelationEntities);
    }
    return this._repository.save(newRelationEntities);
  }

  async updateRelations(data: BusinessPartnerRelation[], entityManager?: EntityManager) {
    if (data.length < 1) {
      return [];
    }
    if (entityManager) {
      return Promise.all(data.filter((data) => !!data.id).map((r) => entityManager.save(BusinessPartnerRelation, r)));
    }
    return this._repository.save(data);
  }

  async updateRelationsByCondition(condition: any, data: any, entityManager?: EntityManager) {
    if (entityManager) {
      const relation = await entityManager.findOne(BusinessPartnerRelation, { where: condition });
      if (relation?.id) {
        await entityManager.update(BusinessPartnerRelation, { id: relation.id }, { ...relation, ...data });
        return entityManager.findOne(BusinessPartnerRelation, { where: { id: relation.id } });
      }
    }
    const relation = await this._repository.findOne({ where: condition });
    if (relation) {
      return this._repository.save({ ...relation, ...data });
    }
  }

  async findRelationsWithEntityManager(conditions: any, entityManager: EntityManager) {
    return entityManager.find(BusinessPartnerRelation, { where: conditions });
  }

  async removeBusinessPartnerRelations(targetId: string) {
    if (!targetId) {
      return [];
    }

    return this._repository
      .createQueryBuilder()
      .update(BusinessPartnerRelation)
      .set({ isDeleted: true, isActive: false })
      .where(`businessPartner1 = :targetId and isDeleted = false`, { targetId })
      .orWhere(`businessPartner2 = :targetId and isDeleted = false`, { targetId })
      .execute();
  }

  async findRelatedBusinessPartnersForContact(contactId: string) {
    // Step 1: Get the entityId from business_partner_contacts
    // const contact = await this._businessPartnerContactService.findOne({ where: { businessPartnerContactKey } });
    // if (!contact) {
    //     return { outletIds: [], depotIds: [], distributorIds: [] };
    // }

    // Step 2: Custom raw query to get outletIds, depotIds, and distributorIds
    const query = `
        SELECT 
            contactOutlet."businessPartner2" AS "outletId",
            outletDepot."businessPartner2" AS "depotId",
            depotDistributor."businessPartner2" AS "distributorId"
        FROM 
            business_partner_relations AS contactOutlet
        LEFT JOIN 
            business_partner_relations AS outletDepot ON outletDepot."businessPartner1" = contactOutlet."businessPartner2"
        LEFT JOIN 
            business_partner_relations AS depotDistributor ON depotDistributor."businessPartner1" = outletDepot."businessPartner2"
        WHERE 
            contactOutlet."businessPartner1" = $1
            AND contactOutlet."businessPartnerRelationType" = '${BusinessPartnerRelationType.CONTACT_OUTLET}'
            AND outletDepot."businessPartnerRelationType" = '${BusinessPartnerRelationType.OUTLET_DEPOT}'
            AND depotDistributor."businessPartnerRelationType" = '${BusinessPartnerRelationType.DEPOT_DISTRIBUTOR}'
    `
      .replace(/\n/g, ' ')
      .trim();

    const results = await this._repository.query(query, [contactId]);

    // Extracting unique IDs from results
    const outletIds = [...new Set(results.map((result) => result.outletId))];
    const depotIds = [...new Set(results.map((result) => result.depotId))];
    const distributorIds = [...new Set(results.map((result) => result.distributorId))];

    return {
      outletIds,
      depotIds,
      distributorIds,
    };
  }

  async findRelatedBusinessPartnersFromDistributors(distributorIds: string[]) {
    if (!distributorIds || distributorIds.length === 0) {
      return { depotIds: [], outletIds: [], contactIds: [] };
    }

    // Build "$1, $2 ..." placeholders so the IN clause is safely parameterised
    const placeholders = distributorIds.map((_, idx) => `$${idx + 1}`).join(', ');

    const query = `
      SELECT
        outletDepot."businessPartner2"         AS "outletId",
        outletDepot."businessPartner1"         AS "depotId",
        distributorContact."businessPartner2"  AS "contactId"
      FROM public."business_partner_relations" AS distributorOutlet
      LEFT JOIN public."business_partner_relations" AS outletDepot
             ON outletDepot."businessPartner1" = distributorOutlet."businessPartner2"
      LEFT JOIN public."business_partner_relations" AS distributorContact
             ON distributorContact."businessPartner1" = distributorOutlet."businessPartner2"
      WHERE distributorOutlet."businessPartner1" IN (${placeholders})
        AND distributorOutlet."businessPartnerRelationType" = '${BusinessPartnerRelationType.DEPOT_DISTRIBUTOR}'
        AND outletDepot."businessPartnerRelationType"        = '${BusinessPartnerRelationType.OUTLET_DEPOT}'
        AND distributorContact."businessPartnerRelationType" = '${BusinessPartnerRelationType.CONTACT_DISTRIBUTOR}'
    `
      .replace(/\n/g, ' ')
      .trim();

    const results = await this._repository.query(query, distributorIds);

    // Extracting unique IDs from results
    const outletIds = [...new Set(results.map((result) => result.outletId))];
    const depotIds = [...new Set(results.map((result) => result.depotId))];
    const contactIds = [...new Set(results.map((result) => result.contactId))];

    return {
      outletIds,
      depotIds,
      contactIds,
    };
  }

  async findRelatedBusinessPartnerIdsFromContact(contactId: string): Promise<ContactDistributorRelations> {
    if (!contactId) {
      return { originalContactId: contactId, depotIds: [], outletIds: [], contactIds: [], distributorIds: [] };
    }

    const query = `
      SELECT
        outletDepot."businessPartner1"        AS "outletId",
        depotDistributor."businessPartner1"   AS "depotId",
        contactDistributor."businessPartner1" AS "sourceContactId",
        contactDistributor."businessPartner2" AS "distributorId",
        contactOutlet."businessPartner1"      AS "outletContactId"
      FROM  public."business_partner_relations" AS contactDistributor
      JOIN  public."business_partner_relations" AS depotDistributor
            ON depotDistributor."businessPartner2" = contactDistributor."businessPartner2"
           AND depotDistributor."isDeleted" = false
           AND depotDistributor."businessPartnerRelationType" = '${BusinessPartnerRelationType.DEPOT_DISTRIBUTOR}'
      JOIN  public."business_partner_relations" AS outletDepot
            ON outletDepot."businessPartner2" = depotDistributor."businessPartner1"
           AND outletDepot."isDeleted" = false
           AND outletDepot."businessPartnerRelationType" = '${BusinessPartnerRelationType.OUTLET_DEPOT}'
      JOIN  public."business_partner_relations" AS contactOutlet
            ON contactOutlet."businessPartner2" = outletDepot."businessPartner1"
           AND contactOutlet."isDeleted" = false
           AND contactOutlet."businessPartnerRelationType" = '${BusinessPartnerRelationType.CONTACT_OUTLET}'
      WHERE contactDistributor."businessPartner1" = $1
        AND contactDistributor."businessPartnerRelationType" = '${BusinessPartnerRelationType.CONTACT_DISTRIBUTOR}'
        AND contactDistributor."isDeleted" = false
    `
      .replace(/\n/g, ' ')
      .trim();

    const results = await this._repository.query(query, [contactId]);

    const { outletIds, depotIds, distributorIds, outletContactIds } = (results || []).reduce(
      (acc, crr) => {
        acc.outletIds.push(crr?.outletId);
        acc.depotIds.push(crr?.depotId);
        acc.distributorIds.push(crr?.distributorId);
        acc.outletContactIds.push(crr?.outletContactId);
        return acc;
      },
      { outletIds: [], depotIds: [], distributorIds: [], outletContactIds: [] },
    );

    return {
      originalContactId: contactId,
      outletIds: [...new Set(outletIds)] as string[],
      depotIds: [...new Set(depotIds)] as string[],
      contactIds: [...new Set([...outletContactIds, contactId])],
      distributorIds: [...new Set(distributorIds)] as string[],
    };
  }

  async findContactAndRelationsKey(contactId: string) {
    // Step 2: Custom raw query to get contact and it's relation id and key
    const query = `SELECT 
      contactRelation.*,
      businessPartner."businessPartnerKey" AS "businessPartnerKey2"
    FROM 
      public."business_partner_relations" AS contactRelation
      LEFT JOIN public."business_partners" AS businessPartner ON contactRelation."businessPartner2" = businessPartner."id"
    WHERE
      contactRelation."businessPartner1" = $1
      AND contactRelation."isDeleted" = false AND contactRelation."businessPartnerRelationType" in ('${BusinessPartnerRelationType.CONTACT_DISTRIBUTOR}', '${BusinessPartnerRelationType.CONTACT_OUTLET}');
    `
      .replace(/\n/g, ' ')
      .trim();

    return this._repository.query(query, [contactId]);
  }

  async findBusinessPartnerRelationsDataAndRelationPartnerKeys(
    businessPartnerId: string,
    relationType: BusinessPartnerRelationType,
  ) {
    if (!relationType) return null;

    const query = `
      SELECT
        businessPartnerRelation.*,
        businessPartnerData1."businessPartnerKey" AS "businessPartnerKey1",
        businessPartnerData2."businessPartnerKey" AS "businessPartnerKey2"
      FROM  public."business_partner_relations" AS businessPartnerRelation
      LEFT JOIN public."business_partners" AS businessPartnerData1
             ON businessPartnerRelation."businessPartner1" = businessPartnerData1."id"
      LEFT JOIN public."business_partners" AS businessPartnerData2
             ON businessPartnerRelation."businessPartner2" = businessPartnerData2."id"
      WHERE businessPartnerRelation."isDeleted" = false
        AND businessPartnerRelation."businessPartnerRelationType" = $2
        AND businessPartnerRelation."businessPartner1" = $1
    `
      .replace(/\n/g, ' ')
      .trim();

    return this._repository.query(query, [businessPartnerId, relationType]);
  }

  async findBusinessPartnerContactRelationsPartnerKeys(
    businessPartnerId: string,
    relationType: BusinessPartnerRelationType,
  ) {
    if (!relationType) return null;

    const query = `
      SELECT
        businessPartnerRelation.*,
        businessPartnerData1."businessPartnerContactKey" AS "businessPartnerKey1",
        businessPartnerData2."businessPartnerKey"        AS "businessPartnerKey2"
      FROM  public."business_partner_relations" AS businessPartnerRelation
      LEFT JOIN public."business_partner_contacts" AS businessPartnerData1
             ON businessPartnerRelation."businessPartner1" = businessPartnerData1."id"
      LEFT JOIN public."business_partners" AS businessPartnerData2
             ON businessPartnerRelation."businessPartner2" = businessPartnerData2."id"
      WHERE businessPartnerRelation."isDeleted" = false
        AND businessPartnerRelation."businessPartnerRelationType" = $2
        AND businessPartnerRelation."businessPartner2" = $1
    `
      .replace(/\n/g, ' ')
      .trim();

    return this._repository.query(query, [businessPartnerId, relationType]);
  }

  async findAboveRelationIdsFromOutlet(outletId: string) {
    if (!outletId) {
      return { depotIds: [], distributorIds: [] };
    }

    // Step 2: Custom raw query to get depotIds and distributorIds
    const query = `SELECT 
      outletDepot."businessPartner1" AS "outletId", 
      depotDistributor."businessPartner1" AS "depotId",
      depotDistributor."businessPartner2" AS "distributorId"
    FROM 
      public."business_partner_relations" AS outletDepot 
      LEFT JOIN public."business_partner_relations" AS depotDistributor ON depotDistributor."businessPartner1" = outletDepot."businessPartner2" 
      AND depotDistributor."isDeleted" = false AND depotDistributor."businessPartnerRelationType" = '${BusinessPartnerRelationType.DEPOT_DISTRIBUTOR}'
    WHERE
      outletDepot."businessPartner1" = $1
      AND outletDepot."businessPartnerRelationType" = '${BusinessPartnerRelationType.OUTLET_DEPOT}'
      AND outletDepot."isDeleted" = false;
    `
      .replace(/\n/g, ' ')
      .trim();

    const results = await this._repository.query(query, [outletId]);

    const { depotIds, distributorIds } = (results || []).reduce(
      (acc, crr) => {
        acc.depotIds.push(crr?.depotId);
        acc.distributorIds.push(crr?.distributorId);
        return acc;
      },
      { depotIds: [], distributorIds: [] },
    );

    return {
      depotIds: [...new Set(depotIds)],
      distributorIds: [...new Set(distributorIds)],
    };
  }

  async countRelations(businessPartnerId: string, relationType: BusinessPartnerRelationType) {
    if (!relationType) {
      return 0;
    }
    let filterConditions: any = { businessPartnerRelationType: relationType };
    if (relationType === BusinessPartnerRelationType.CONTACT_OUTLET) {
      filterConditions = { ...filterConditions, businessPartner1: businessPartnerId };
    }
    return await this._repository.count({
      where: filterConditions,
    });
  }

  async findOutletIdsInRelationWithDepot(depotExternalKey: string) {
    if (!depotExternalKey) {
      return;
    }
    const query = `SELECT 
      businessPartnerRelation."businessPartner1"
    FROM 
      public."business_partner_relations" AS businessPartnerRelation
      LEFT JOIN public."business_partners" AS depot ON businessPartnerRelation."businessPartner2" = depot."id"
    WHERE
      businessPartnerRelation."isDeleted" = false AND businessPartnerRelation."businessPartnerRelationType" ='${BusinessPartnerRelationType.OUTLET_DEPOT}' AND depot."businessPartnerKey" = $1;
    `
      .replace(/\n/g, ' ')
      .trim();

    const result = await this._repository.query(query, [depotExternalKey]);
    return [...new Set((result || []).map((r) => r.businessPartner1))];
  }

  async findOutletRelationDataByCallCenterId(contactKey: string) {
    if (!contactKey) {
      return [];
    }
    const query = `SELECT 
      outlet."id",
      outlet."businessPartnerName1",
      outlet."businessPartnerName2",
      outlet."businessPartnerKey",
      outlet."businessPartnerStatus",
      outlet_communication."communicationName" as "outletOwnerName",
      outlet_communication."communicationValue" as "outletOwnerPhone"
    FROM 
      public."business_partner_relations" AS businessPartnerRelation
      INNER JOIN public."business_partners" AS outlet
        ON businessPartnerRelation."businessPartner2" = outlet."id"
          AND businessPartnerRelation."isDeleted" = false
          AND businessPartnerRelation."businessPartnerRelationType" ='${BusinessPartnerRelationType.CONTACT_OUTLET}'
          AND outlet."isDeleted"=false
          AND businessPartnerRelation."businessPartner1" = (
            SELECT id FROM public."business_partner_contacts" WHERE "businessPartnerContactKey" = $1 LIMIT 1
          )
      INNER JOIN public."business_partner_communications" AS outlet_communication
        ON businessPartnerRelation."businessPartner2" = outlet_communication."businessPartner"
          AND outlet_communication."isDeleted" = false
          AND outlet_communication."communicationType" ='${BusinessPartnerRelationCommunication.TEL}'
    ORDER BY "businessPartnerName1" ASC,"businessPartnerName2" ASC, "businessPartnerKey" ASC;
    `
      .replace(/\n/g, ' ')
      .trim();

    const outlets = await this._repository.query(query, [contactKey]);
    return outlets.map((outlet) => ({
      ...outlet,
      outletOwnerName: outlet.outletOwnerName || outlet.businessPartnerName2 || '',
    }));
  }

  async findDepotsAndRelatedSaleRepsDataByASMKey(asmKey: string) {
    if (!asmKey) {
      return [];
    }
    const query = `
      SELECT
        depot."id"                       AS "depotId",
        depot."businessPartnerKey"       AS "depotKey",
        COALESCE(depot."businessPartnerName1", depot."businessPartnerName2")     AS "depotName",
        JSON_AGG (
          DISTINCT JSONB_BUILD_OBJECT(
            'saleRepId',          sr."id",
            'saleRepContactKey',  sr."businessPartnerContactKey",
            'saleRepName',        COALESCE(sr."businessPartnerContactName1", sr."businessPartnerContactName2")
          )
        ) FILTER (WHERE sr."id" IS NOT NULL)    AS "saleReps"
      FROM
        public."business_partner_contacts"  asm
        JOIN public."business_partner_relations"  asm_dist
          ON asm_dist."businessPartner1"           = asm."id"
          AND asm_dist."businessPartnerRelationType" = '${BusinessPartnerRelationType.CONTACT_DISTRIBUTOR}'
          AND asm_dist."isDeleted"                  = FALSE
          AND asm."businessPartnerContactPersonRole"       = '${BusinessPartnerContactRole.AREA_SALES_REP_MANAGER}'
          AND asm."businessPartnerContactKey"       = '${asmKey}'
            
        JOIN public."business_partner_relations"  dep_dist
          ON dep_dist."businessPartner2"           = asm_dist."businessPartner2"
          AND dep_dist."businessPartnerRelationType" = '${BusinessPartnerRelationType.DEPOT_DISTRIBUTOR}'
          AND dep_dist."isDeleted"                  = FALSE

        JOIN public."business_partners"           depot
          ON depot."id"        = dep_dist."businessPartner1"
          AND depot."isDeleted" = FALSE

        JOIN public."business_partner_relations"  out_dep
          ON out_dep."businessPartner2"              = depot."id"
          AND out_dep."businessPartnerRelationType"    = '${BusinessPartnerRelationType.OUTLET_DEPOT}'
          AND out_dep."isDeleted"                     = FALSE

        LEFT JOIN public."business_partner_relations"  sr_out
          ON sr_out."businessPartner2"                = out_dep."businessPartner1"
          AND sr_out."businessPartnerRelationType"      = '${BusinessPartnerRelationType.CONTACT_OUTLET}'
          AND sr_out."isDeleted"                       = FALSE

        LEFT JOIN public."business_partner_contacts"  sr
          ON sr."id"        = sr_out."businessPartner1"
          AND sr."isDeleted" = FALSE
          AND sr."businessPartnerContactPersonRole" = '${BusinessPartnerContactRole.SALE_REP}'
      GROUP BY
        depot."id", depot."businessPartnerKey";
    `
      .replace(/\n/g, ' ')
      .trim();

    return this._repository.query(query);
  }

  async findRelationKeyForContact(contactId: string, contactKey: string, contactRole: BusinessPartnerContactRole.SALE_REP | BusinessPartnerContactRole.CALL_CENTER) {
    if (!contactId && !contactKey) {
      return;
    }
    const query = `
      SELECT
        contact."id"                          AS "contactId",
        contact."businessPartnerContactKey"   AS "contactKey",
        outlet."id"                           AS "outletId",
        outlet."businessPartnerKey"           AS "outletKey",
        depot."id"                            AS "depotId",
        depot."businessPartnerKey"            AS "depotKey",
        distributor."id"                      AS "distributorId",
        distributor."businessPartnerKey"      AS "distributorKey"
      FROM
        public."business_partner_contacts" contact
        JOIN public."business_partner_relations" contact_outlet
          ON contact_outlet."businessPartner1" = contact."id"
          AND contact_outlet."businessPartnerRelationType" = '${BusinessPartnerRelationType.CONTACT_OUTLET}'
          AND contact_outlet."isDeleted" = FALSE
          AND contact."businessPartnerContactPersonRole" = '${contactRole}'
          ${(contactKey && `AND contact."businessPartnerContactKey" = '${contactKey}'`) || ''}
          ${(contactId && `AND contact."id" = '${contactId}'`) || ''}

        JOIN public."business_partners"           outlet
          ON outlet."id" = contact_outlet."businessPartner2"
          AND outlet."isDeleted" = FALSE
            
        JOIN public."business_partner_relations"  outlet_depot
          ON outlet_depot."businessPartner1" = contact_outlet."businessPartner2"
          AND outlet_depot."businessPartnerRelationType" = '${BusinessPartnerRelationType.OUTLET_DEPOT}'
          AND outlet_depot."isDeleted" = FALSE

        JOIN public."business_partners"           depot
          ON depot."id" = outlet_depot."businessPartner2"
          AND depot."isDeleted" = FALSE

        JOIN public."business_partner_relations"  depot_distributor
          ON depot_distributor."businessPartner1" = depot."id"
          AND depot_distributor."businessPartnerRelationType" = '${BusinessPartnerRelationType.DEPOT_DISTRIBUTOR}'
          AND depot_distributor."isDeleted" = FALSE

        JOIN public."business_partners"  distributor
          ON depot_distributor."businessPartner2" = distributor."id"
          AND distributor."isDeleted" = FALSE;
    `
      .replace(/\n/g, ' ')
      .trim();

    const contactRelation = await this._repository.query(query);
    return (contactRelation && contactRelation[0]) || {};
  }

  async findOutletsAndDepotsByContact(contactId: string, contactKey: string) {
    if (!contactId && !contactKey) {
      return;
    }
    const query = `
      SELECT
        contact."id"                          AS "contactId",
        contact."businessPartnerContactKey"   AS "contactKey",
        outlet."id"                           AS "outletId",
        outlet."businessPartnerKey"           AS "outletKey",
        depot."id"                            AS "depotId",
        depot."businessPartnerKey"            AS "depotKey"
      FROM
        public."business_partner_contacts" contact
        JOIN public."business_partner_relations" contact_outlet
          ON contact_outlet."businessPartner1" = contact."id"
          AND contact_outlet."businessPartnerRelationType" = '${BusinessPartnerRelationType.CONTACT_OUTLET}'
          AND contact_outlet."isDeleted" = FALSE
          ${(contactKey && `AND contact."businessPartnerContactKey" = '${contactKey}'`) || ''}
          ${(contactId && `AND contact."id" = '${contactId}'`) || ''}

        JOIN public."business_partners"           outlet
          ON outlet."id" = contact_outlet."businessPartner2"
          AND outlet."isDeleted" = FALSE
            
        JOIN public."business_partner_relations"  outlet_depot
          ON outlet_depot."businessPartner1" = contact_outlet."businessPartner2"
          AND outlet_depot."businessPartnerRelationType" = '${BusinessPartnerRelationType.OUTLET_DEPOT}'
          AND outlet_depot."isDeleted" = FALSE

        JOIN public."business_partners"           depot
          ON depot."id" = outlet_depot."businessPartner2"
          AND depot."isDeleted" = FALSE
    `
      .replace(/\n/g, ' ')
      .trim();

    return this._repository.query(query);
  }

  async findSaleRepsByDepot(depotKey: string) {
    if (!depotKey) {
      return [];
    }
    const query = `
      SELECT DISTINCT
        sr."id" AS "saleRepId",
        sr."businessPartnerContactKey" AS "saleRepContactKey"
      FROM
        public."business_partners" depot
        JOIN public."business_partner_relations" out_dep
          ON out_dep."businessPartner2" = depot."id"
          AND out_dep."businessPartnerRelationType" = '${BusinessPartnerRelationType.OUTLET_DEPOT}'
          AND out_dep."isDeleted" = FALSE
          AND depot."businessPartnerKey" = '${depotKey}'
          AND depot."isDeleted" = FALSE
        JOIN public."business_partner_relations" sr_out
          ON sr_out."businessPartner2" = out_dep."businessPartner1"
          AND sr_out."businessPartnerRelationType" = '${BusinessPartnerRelationType.CONTACT_OUTLET}'
          AND sr_out."isDeleted" = FALSE
        JOIN public."business_partner_contacts" sr
          ON sr."id" = sr_out."businessPartner1"
          AND sr."isDeleted" = FALSE
          AND sr."businessPartnerContactPersonRole" = '${BusinessPartnerContactRole.SALE_REP}'
    `
      .replace(/\n/g, ' ')
      .trim();

    return this._repository.query(query);
  }
}
