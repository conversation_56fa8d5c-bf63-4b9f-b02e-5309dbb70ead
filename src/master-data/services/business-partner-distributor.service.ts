import { BadRequestException, forwardRef, HttpException, HttpStatus, Inject, Injectable, NotFoundException } from '@nestjs/common';
import * as _ from 'lodash';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartnerDto } from '../dtos/business-partner.dto';
import { I18nContext } from 'nestjs-i18n';
import { checkImportDataRequiredFields, excelNumberToString, isEmptyObjectOrArray, isValidEmail } from 'src/utils';
import { BusinessPartnersService } from './business-partners.service';
import { BusinessPartnersGeoLocationService } from './business-partners-geo-location.service';
import { BusinessPartnerDetailService } from './business-partners-detail.service';
import { BusinessPartnersImageService } from './business-partners-image.service';
import { BusinessPartnerCommunicationService } from './business-partners-communication.service';
import { runTransaction } from '../../utils/helpers/database';
import { EntityManager, ILike, In } from 'typeorm';
import { BusinessPartnerOperatingHourService } from './business-partners-operating-hour.service';
import { BusinessPartnersContactService } from './business-partners-contact.service';
import { DistributorDepotExportMapping, DistributorDepotMapping } from '../constants/distributor.mapping';
import { BusinessPartnerRelationService } from './business-partners-relation.service';
import { FilesService } from 'src/files/services';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ContactType, DepotsType, DistributorDepotMappingsType, DistributorType } from '../constants/distributor.type';
import {
  BusinessPartnerContactRole,
  BusinessPartnerRelationCommunication,
  BusinessPartnerRelationType,
  BusinessPartnerStatus,
  BusinessPartnerType,
  DayOfWeekShort,
} from '../constants/business-partner.enum';
import { BusinessPartnerContactDto } from '../dtos/business-partner-contact.dto';
import { BusinessPartnerDepotDto } from '../dtos/business-partner-depot.dto';
import { SearchDto } from '../dtos/search.dto';
import { BusinessPartnersCustomerService } from './business-partners-customer.service';

@Injectable()
export class BusinessPartnerDistributorService {
  constructor(
    @Inject()
    readonly _businessPartnerService: BusinessPartnersService,
    @Inject()
    readonly _businessPartnersGeoLocationService: BusinessPartnersGeoLocationService,
    @Inject()
    readonly _businessPartnerDetailService: BusinessPartnerDetailService,
    @Inject()
    readonly _businessPartnersImageService: BusinessPartnersImageService,
    @Inject()
    readonly _businessPartnerCommunicationService: BusinessPartnerCommunicationService,
    @Inject()
    readonly _businessPartnerOperatingHourService: BusinessPartnerOperatingHourService,
    @Inject()
    readonly _businessPartnerContactService: BusinessPartnersContactService,
    @Inject()
    readonly _businessPartnerRelationService: BusinessPartnerRelationService,
    @Inject()
    readonly fileService: FilesService,
    @Inject()
    private readonly eventEmitter: EventEmitter2,

    @Inject(forwardRef(() => BusinessPartnersCustomerService))
    readonly _businessPartnerCustomerService: BusinessPartnersCustomerService,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {}

  async createDistributor(dto: BusinessPartnerDto, i18n?: I18nContext) {
    // check if an business partner entity with businessPartnerKey already existed
    if (dto?.businessPartnerKey) {
      const existedEntity = await this._businessPartnerService.findOne({ where: { businessPartnerKey: dto?.businessPartnerKey } });
      if (existedEntity && !existedEntity?.isDeleted) {
        throw new BadRequestException('distributor.existed_id');
      }
      return await this.updateDistributor(existedEntity, { ...dto, isDeleted: false, isActive: true });
    }

    const newBusinessPartnerEntity = this._businessPartnerService.mapBusinessPartnerDataFromDtoToEntity(null, dto);

    // TRANSACTIONAL
    const [newBusinessPartner] = await runTransaction([
      async (entityManage: EntityManager, context) => {
        const newBusinessPartner = await entityManage.save(BusinessPartner, newBusinessPartnerEntity);
        context.businessPartner = newBusinessPartner;
        return newBusinessPartner;
      },

      async (entityManage: EntityManager, context) => {
        const businessPartner = context.businessPartner;
        return this._businessPartnerService.createBusinessPartnerRelatedData(entityManage, businessPartner, dto);
      },
    ]);

    const result = await this._businessPartnerService.attachBusinessPartnerRelationData(newBusinessPartner);
    this.eventEmitter.emit('distributor.synced', { distributor: result });
    return result;
  }

  async createDepot(dto: BusinessPartnerDepotDto, i18n?: I18nContext, needDistributorRelation?: boolean) {
    let distributor;
    if (needDistributorRelation) {
      if (!dto?.distributorPartnerKey) {
        throw new BadRequestException('distributor.not_existed');
      }
    }

    if (dto?.distributorPartnerKey) {
      distributor = await this._businessPartnerService.findByExternalId(dto.distributorPartnerKey);
      if (isEmptyObjectOrArray(distributor)) {
        throw new BadRequestException('distributor.not_existed');
      }
    }

    if (dto?.businessPartnerKey) {
      const existedEntity = await this._businessPartnerService.findOne({ where: { businessPartnerKey: dto?.businessPartnerKey } });
      if (existedEntity) {
        if (!existedEntity?.isDeleted) {
          throw new BadRequestException('depot.existed_id');
        }

        return await this.updateDepot(existedEntity, { ...dto, isDeleted: false, isActive: true });
      }
    }

    const newBusinessPartnerEntity = this._businessPartnerService.mapBusinessPartnerDataFromDtoToEntity(null, dto);

    // TRANSACTIONAL
    const [newBusinessPartner] = await runTransaction([
      async (entityManage: EntityManager, context) => {
        const newBusinessPartner = await entityManage.save(BusinessPartner, newBusinessPartnerEntity);
        context.businessPartner = newBusinessPartner;
        return newBusinessPartner;
      },

      async (entityManage: EntityManager, context) => {
        const businessPartner = context.businessPartner;
        return this._businessPartnerService.createBusinessPartnerRelatedData(entityManage, businessPartner, dto);
      },
      async (entityManage: EntityManager, context) => {
        if (isEmptyObjectOrArray(distributor)) {
          return null;
        }
        const businessPartner = context.businessPartner;
        return this._businessPartnerRelationService.createRelation(
          {
            businessPartner1: businessPartner.id,
            businessPartner2: distributor.id,
            businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR,
            businessPartnerRelationStatus: BusinessPartnerStatus.ACTIVE,
            businessPartnerRelationValidFromDate: new Date(),
          },
          entityManage,
        );
      },
    ]);

    const result = await this._businessPartnerService.attachBusinessPartnerRelationData(newBusinessPartner);
    this.eventEmitter.emit('depot.synced', { depot: result, distributorPartnerKey: distributor?.businessPartnerKey });
    return result;
  }

  async findById(id: string, i18n: I18nContext) {
    return await this._businessPartnerService.find({ where: { id, isDeleted: false, businessPartnerType: BusinessPartnerType.DISTRIBUTOR } });
  }

  async findByExternalId(businessPartnerKey: string) {
    return await this._businessPartnerService.findOne({ where: { businessPartnerKey, isDeleted: false, businessPartnerType: BusinessPartnerType.DISTRIBUTOR } });
  }

  async findByIdWithAllRelations(id: string, i18n: I18nContext) {
    return this._businessPartnerService.findByIdWithAllRelations(id);
  }

  async findDistributorByIdWithAllRelations(id: string, i18n: I18nContext) {
    const distributor = await this._businessPartnerService.findByIdWithAllRelations(id);
    const distributorRelations = await this._businessPartnerRelationService.find({
      where: {
        businessPartner2: id,
        businessPartnerRelationType: In([BusinessPartnerRelationType.DEPOT_DISTRIBUTOR, BusinessPartnerRelationType.CONTACT_DISTRIBUTOR]),
        isDeleted: false,
      },
    });
    const { depotIds, contactIds } = (distributorRelations || []).reduce(
      (acc, relation) => {
        if (relation.businessPartnerRelationType === BusinessPartnerRelationType.DEPOT_DISTRIBUTOR) {
          acc.depotIds.push(relation.businessPartner1);
        }

        if (relation.businessPartnerRelationType === BusinessPartnerRelationType.CONTACT_DISTRIBUTOR) {
          acc.contactIds.push(relation.businessPartner1);
        }
        return acc;
      },
      { depotIds: [], contactIds: [] },
    );

    const depots = await Promise.all(depotIds.map((depotId) => this._businessPartnerService.findByIdWithAllRelations(depotId)));
    const contacts = await Promise.all(contactIds.map((contactId) => this._businessPartnerContactService.findByIdWithAllRelations(contactId)));

    return {
      ...distributor,
      depots: depots || [],
      contacts: contacts || [],
    };
  }

  async updateDistributor(distributor: BusinessPartner, updateData: any) {
    const { id, ...distributorUpdateData } = updateData;
    const updateDistributorData = this._businessPartnerService.mapBusinessPartnerDataFromDtoToEntity(distributor, distributorUpdateData);

    // TRANSACTIONAL
    const [_, response] = await runTransaction([
      async (entityManager: EntityManager, context) => {
        // If update existed record, dont pass relation entity into update value
        const { details, customers, communications, geoGraphicalLocations, images, operatingHours, ...updateDistributor } = updateDistributorData;
        const updatedBusinessPartner = await entityManager.save(BusinessPartner, updateDistributor);
        context.businessPartner = updatedBusinessPartner;
        return updatedBusinessPartner;
      },
      async (entityManager: EntityManager, context) => {
        const businessPartner = context.businessPartner;
        return this._businessPartnerService.updateBusinessPartnerRelatedData(entityManager, businessPartner, updateDistributorData);
      },
    ]);

    this.eventEmitter.emit('distributor.synced', { distributor: response });
    return response;
  }

  async updateDepot(depot: BusinessPartner, updateData: any) {
    const { id, ...depotUpdateData } = updateData;
    const updateDepotData = this._businessPartnerService.mapBusinessPartnerDataFromDtoToEntity(depot, depotUpdateData);

    // TRANSACTIONAL
    const [_, response, relation] = await runTransaction([
      async (entityManager: EntityManager, context) => {
        // If update existed record, dont pass relation entity into update value
        const { details, customers, communications, geoGraphicalLocations, images, operatingHours, ...updateDistributor } = updateDepotData;
        const updatedBusinessPartner = await entityManager.save(BusinessPartner, updateDistributor);
        context.businessPartner = updatedBusinessPartner;
        return updatedBusinessPartner;
      },

      async (entityManager: EntityManager, context) => {
        const businessPartner = context.businessPartner;
        const depotData = await this._businessPartnerService.updateBusinessPartnerRelatedData(entityManager, businessPartner, updateDepotData);
        context.depot = depotData;
        return depotData;
      },

      async (entityManager: EntityManager, context) => {
        return this.updateDepotDistributorOldRelation(depot, updateData, entityManager);
      },
    ]);

    // Get distributor info for sync
    const distributorRelation = await this._businessPartnerRelationService.findOne({
      where: {
        businessPartner1: depot.id,
        businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR,
        isDeleted: false,
      },
    });

    let distributor = null;
    if (distributorRelation) {
      distributor = await this._businessPartnerService.findOne({
        where: {
          id: distributorRelation.businessPartner2,
          isDeleted: false,
        },
      });
    }

    this.eventEmitter.emit('depot.synced', { depot: response, distributorPartnerKey: distributor?.businessPartnerKey });
    return response;
  }

  async updateDepotDistributorOldRelation(depotData: BusinessPartner, updatedData: BusinessPartnerDepotDto, entityManager: EntityManager) {
    if (!updatedData?.distributorPartnerKey) {
      return null;
    }

    const distributor = await this._businessPartnerService.findByExternalId(updatedData?.distributorPartnerKey);
    if (isEmptyObjectOrArray(distributor)) {
      return null;
    }

    const depotDistributorRelations = await this._businessPartnerRelationService.findRelationsWithEntityManager(
      {
        businessPartner1: depotData.id,
        businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR,
        isDeleted: false,
      },
      entityManager,
    );

    if (isEmptyObjectOrArray(depotDistributorRelations)) {
      return this._businessPartnerRelationService.createRelation(
        {
          businessPartner1: depotData.id,
          businessPartner2: distributor.id,
          businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR,
          businessPartnerRelationStatus: BusinessPartnerStatus.ACTIVE,
          businessPartnerRelationValidFromDate: new Date(),
        },
        entityManager,
      );
    }

    const depotDistributorRelationId = depotDistributorRelations[0]?.businessPartner2;

    if (distributor?.id === depotDistributorRelationId) {
      return depotDistributorRelations[0];
    }

    return this._businessPartnerRelationService.updateRelationsByCondition(
      { id: depotDistributorRelations[0]?.id },
      {
        businessPartner1: depotData.id,
        businessPartner2: distributor?.id,
        businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR,
        businessPartnerRelationStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerRelationValidFromDate: new Date(),
      },
      entityManager,
    );
  }

  async checkExistAndUpdateDistributor(id: string, updateData: any, i18n: I18nContext) {
    const distributor = await this._businessPartnerService.findOne({ where: { id } });
    if (!distributor) {
      throw new NotFoundException(i18n.t('distributor.not_found'));
    }

    return this.updateDistributor(distributor, updateData);
  }

  async checkExistAndUpdateDepot(id: string, updateData: any, i18n: I18nContext) {
    const depot = await this._businessPartnerService.findOne({ where: { id } });
    if (!depot) {
      throw new NotFoundException(i18n.t('depot.not_found'));
    }

    return this.updateDepot(depot, updateData);
  }

  async findDistributors(filter: any) {
    return await this._businessPartnerService.find({ where: filter });
  }

  async findDepot(filter: any) {
    return await this._businessPartnerService.find({ where: filter });
  }

  async checkExistAndDeleteDistributor(id: string, i18n: I18nContext) {
    const deactivatedDistributor = await this.checkExistAndUpdateDistributor(id, { isDeleted: true, isActive: false }, i18n);

    // Deactivate related data
    await this._businessPartnerService.deactivateRelatedBusinessPartnerData(deactivatedDistributor);

    // Emit event for sync
    this.eventEmitter.emit('distributor.synced', { distributor: deactivatedDistributor });

    return deactivatedDistributor;
  }

  async checkExistAndDeleteDepot(id: string, i18n: I18nContext) {
    const deactivatedDepot = await this.checkExistAndUpdateDepot(id, { isDeleted: true, isActive: false }, i18n);

    // Deactivate related data
    await this._businessPartnerService.deactivateRelatedBusinessPartnerData(deactivatedDepot);

    // Get distributor info for sync
    const distributorRelation = await this._businessPartnerRelationService.findOne({
      where: {
        businessPartner1: id,
        businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR,
        isDeleted: false,
      },
    });

    let distributor = null;
    if (distributorRelation) {
      distributor = await this._businessPartnerService.findOne({
        where: {
          id: distributorRelation.businessPartner2,
          isDeleted: false,
        },
      });
    }

    // Emit event for sync
    this.eventEmitter.emit('depot.synced', { depot: deactivatedDepot, distributorPartnerKey: distributor?.businessPartnerKey });

    return deactivatedDepot;
  }

  mappingEntityDataToExportData(dataMappingFields: NonNullable<unknown>, data: NonNullable<unknown>) {
    return Object.keys(dataMappingFields).reduce((exportData, key) => {
      const fieldValueExtractor = dataMappingFields[key];
      if (!fieldValueExtractor) {
        return exportData;
      }
      exportData[key] = typeof fieldValueExtractor === 'function' ? fieldValueExtractor(data) : _.get(data, fieldValueExtractor);
      return exportData;
    }, {});
  }

  async queryExportDistributorData() {
    const queryBusinessPartnerTypes = [BusinessPartnerType.DISTRIBUTOR, BusinessPartnerType.DEPOT, BusinessPartnerType.CONTACT];
    const queryRelationTypes = [BusinessPartnerRelationType.CONTACT_DISTRIBUTOR, BusinessPartnerRelationType.DEPOT_DISTRIBUTOR];
    const [businessPartners, contacts, businessPartnerRelations, communications, details, operatingHours, geoLocations, customers] = await Promise.all([
      this._businessPartnerService.findWithOrder(
        {
          businessPartnerType: In([BusinessPartnerType.DISTRIBUTOR, BusinessPartnerType.DEPOT]),
          isDeleted: false,
        },
        'createdAt',
        'DESC',
      ),
      this._businessPartnerContactService.findWithOrder(
        {
          businessPartnerContactPersonRole: In([
            BusinessPartnerContactRole.CALL_CENTER_MANAGEMENT,
            BusinessPartnerContactRole.DISTRIBUTOR_ADMIN,
            BusinessPartnerContactRole.AREA_SALES_REP_MANAGER,
          ]),
          isDeleted: false,
        },
        'createdAt',
        'DESC',
      ),
      this._businessPartnerRelationService.find({
        where: {
          businessPartnerRelationType: In(queryRelationTypes),
        },
      }),
      this._businessPartnerCommunicationService.find({
        where: {
          businessPartnerType: In(queryBusinessPartnerTypes),
          isDeleted: false,
        },
      }),
      this._businessPartnerDetailService.find({
        where: {
          businessPartnerType: In(queryBusinessPartnerTypes),
          isDeleted: false,
        },
      }),
      this._businessPartnerOperatingHourService.find({
        where: {
          businessPartnerType: In(queryBusinessPartnerTypes),
          isDeleted: false,
        },
      }),
      this._businessPartnersGeoLocationService.find({
        where: {
          businessPartnerType: In(queryBusinessPartnerTypes),
          isDeleted: false,
        },
      }),
      this._businessPartnerCustomerService.find({
        where: {
          businessPartnerType: BusinessPartnerType.DEPOT,
          isDeleted: false,
        },
        relations: ['customerSalesOrganizations'],
      }),
    ]);

    const distributorDataMappingFields = DistributorDepotExportMapping.distributors;
    const depotDataMappingFields = DistributorDepotExportMapping.depots;
    const contactMappingFields = DistributorDepotExportMapping.contacts;

    const { distributors, depots } = businessPartners.reduce(
      (acc, bp) => {
        const businessPartnerType = bp.businessPartnerType;
        const businessPartnerId = bp.id;
        const filterCondition = (data: any) => data && data?.businessPartner === businessPartnerId && businessPartnerType === data?.businessPartnerType;
        if (bp.businessPartnerType === BusinessPartnerType.DISTRIBUTOR) {
          acc.distributors.push({
            ...bp,
            details: details.filter(filterCondition),
            communications: communications.filter(filterCondition),
            // operatingHours: operatingHours.filter(filterCondition),
            // geoGraphicalLocations: geoLocations.filter(filterCondition),
          });
          return acc;
        }

        if (bp.businessPartnerType === BusinessPartnerType.DEPOT) {
          const distributorRelation = businessPartnerRelations.find(
            (bpr) => bpr.businessPartnerRelationType === BusinessPartnerRelationType.DEPOT_DISTRIBUTOR && bpr.businessPartner1 === bp.id,
          );
          acc.depots.push({
            ...bp,
            distributor: isEmptyObjectOrArray(distributorRelation) ? {} : businessPartners.find((businessP) => businessP.id === distributorRelation?.businessPartner2),
            details: details.filter(filterCondition),
            communications: communications.filter(filterCondition),
            operatingHours: operatingHours.filter(filterCondition),
            geoGraphicalLocations: geoLocations.filter(filterCondition),
            customers: customers.filter(filterCondition),
          });
          return acc;
        }

        return acc;
      },
      { distributors: [], depots: [] },
    );

    const contactData = contacts
      .map((contact) => {
        const businessPartnerContactType = contact.businessPartnerContactType;
        const businessPartnerContactId = contact.id;
        const contactDistributorRelations = (businessPartnerRelations || []).filter(
          (bpr) => bpr.businessPartnerRelationType === BusinessPartnerRelationType.CONTACT_DISTRIBUTOR && bpr.businessPartner1 === contact.id,
        );
        const distributorPartnerKeys = [];
        for (let i = 0; i < contactDistributorRelations.length; i++) {
          const relationDistributor = distributors.find((distributor) => distributor.id === contactDistributorRelations[i]?.businessPartner2);
          if (relationDistributor) {
            distributorPartnerKeys.push(relationDistributor?.businessPartnerKey);
          }
        }
        const filterCondition = (data: any) => data && data?.businessPartner === businessPartnerContactId && businessPartnerContactType === data?.businessPartnerType;
        const contactExportData = {
          ...contact,
          distributorPartnerKeys: [...new Set(distributorPartnerKeys)].join(', '),
          details: details.filter(filterCondition),
          communications: communications.filter(filterCondition),
          operatingHours: operatingHours.filter(filterCondition),
          geoGraphicalLocations: geoLocations.filter(filterCondition),
        };

        return this.mappingEntityDataToExportData(contactMappingFields, contactExportData);
      })
      .filter((contact) => !!contact);

    return {
      distributors: distributors.map((distributor) => this.mappingEntityDataToExportData(distributorDataMappingFields, distributor)),
      depots: depots.map((depot) => this.mappingEntityDataToExportData(depotDataMappingFields, depot)),
      contacts: contactData,
    };
  }

  async exportDistributorsData(i18n: I18nContext) {
    const { distributors, depots, contacts } = await this.queryExportDistributorData();

    return this.fileService.exportXLSXFileWithMultipleSheet({
      fileName: `Distributor_Export_New`,
      sheets: [
        {
          name: 'distributors',
          data: distributors,
        },
        {
          name: 'depots',
          data: depots,
        },
        {
          name: 'contacts',
          data: contacts,
        },
        // {
        //   name: 'contact-depots',
        //   data: contactDepot,
        // },
      ],
    });
  }

  async importDistributor(distributor: DistributorType, i18n: I18nContext) {
    /**
     *   businessPartnerKey: string;
     *   businessPartnerName1: string;
     *   businessPartnerName2: string;
     *   businessPartnerType: string;
     *   geoLocation: string;
     *   businessPartnerStatus: string;
     *   taxNumber: string;
     *   timezone: string;
     *   communicationNumber: string;
     */
    const communications =
      (distributor.communicationNumber && [
        {
          communicationName: '',
          communicationValue: distributor.communicationNumber,
          businessPartnerKey: distributor.businessPartnerKey,
          communicationType: BusinessPartnerRelationCommunication.TEL,
          communicationDescription: '',
          communicationStatus: BusinessPartnerStatus.ACTIVE,
          businessPartnerType: BusinessPartnerType.DISTRIBUTOR,
        },
      ]) ||
      [];
    const row: BusinessPartnerDto = {
      businessPartnerName1: distributor.businessPartnerName1,
      businessPartnerName2: distributor.businessPartnerName2,
      businessPartnerKey: distributor.businessPartnerKey,
      businessPartnerDescription: '',
      businessPartnerType: BusinessPartnerType.DISTRIBUTOR,
      businessPartnerStatus: BusinessPartnerStatus.ACTIVE,
      operatingHours: [],
      geoGraphicalLocations: [],
      details: [
        {
          source: '',
          taxNumber: distributor.taxNumber,
          businessPartnerType: BusinessPartnerType.DISTRIBUTOR,
          timezone: distributor.timezone,
          rawAddress: distributor.geoLocation,
          customerCommercialHierarchy: {
            territory: '',
          },
        },
      ],
      communications,
      images: [],
    };

    const checkExisted = await this._businessPartnerService.findOne({
      where: { businessPartnerKey: distributor.businessPartnerKey, isDeleted: false, businessPartnerType: BusinessPartnerType.DISTRIBUTOR },
    });

    if (checkExisted) {
      return this.updateDistributor(checkExisted, row);
    } else {
      return this.createDistributor(row, i18n);
    }
  }

  async importDepot(depot: DepotsType, distributor: BusinessPartner, i18n: I18nContext) {
    /**
     *   businessPartnerKey: string;
     *   businessPartnerDistributorKey: string;
     *   businessPartnerName1: string;
     *   businessPartnerName2: string;
     *   defaultCurrencyCode: string;
     *   defaultSalesChannelId: string;
     *   deliveringSiteKey: string;
     *   businessPartnerStatus: string;
     *   businessUnit: string;
     *   geoLocation: string;
     *   communicationNumber: string;
     *   holidays: string;
     *   operatingHours: string;
     */
    const operatingHours: any = depot.operatingHours
      ?.split('|')
      .map((entry) => {
        const [day, isOpen, openTime, closeTime] = entry.split('-');
        const validDay = Object.values(DayOfWeekShort).find((d) => d === day?.trim());
        if (!validDay) {
          return null;
        }
        return {
          businessPartnerType: BusinessPartnerType.DEPOT,
          day: validDay,
          openTime: isOpen === 'true' ? openTime : '00:01',
          closeTime: isOpen === 'true' ? closeTime : '23:59',
          isClosed: isOpen === 'false',
        };
      })
      .filter((operatingHour) => !!operatingHour);
    const communications =
      (depot.communicationNumber && [
        {
          communicationName: '',
          communicationValue: depot.communicationNumber,
          businessPartnerKey: depot.businessPartnerKey,
          communicationType: BusinessPartnerRelationCommunication.TEL,
          communicationDescription: '',
          communicationStatus: BusinessPartnerStatus.ACTIVE,
          businessPartnerType: BusinessPartnerType.DEPOT,
        },
      ]) ||
      [];
    const rowDepot: BusinessPartnerDepotDto = {
      distributorPartnerKey: null,
      businessPartnerName1: depot.businessPartnerName1,
      businessPartnerName2: depot.businessPartnerName2,
      businessPartnerKey: depot.businessPartnerKey,
      businessPartnerDescription: '',
      businessPartnerType: BusinessPartnerType.DEPOT,
      businessPartnerStatus: BusinessPartnerStatus.ACTIVE,
      operatingHours: operatingHours,
      geoGraphicalLocations: [],
      details: [
        {
          businessPartnerType: BusinessPartnerType.DEPOT,
          businessUnit: depot.businessUnit,
          timezone: '',
          // holidays: depot.holidays,
          customerCommercialHierarchy: {
            territory: '',
          },
          rawAddress: depot.geoLocation,
        },
      ],
      communications,
      customers: [
        {
          businessPartnerKey: depot.businessPartnerKey,
          businessPartnerType: BusinessPartnerType.DEPOT,
          customerSalesOrganizations: [
            {
              deliveringSiteKey: depot.deliveringSiteKey,
              currencyCode: depot?.defaultCurrencyCode,
            },
          ],
        },
      ],
      images: [],
    };

    if (distributor?.businessPartnerKey) {
      rowDepot.distributorPartnerKey = distributor?.businessPartnerKey;
    }

    let checkExisted = await this._businessPartnerService.findOne({
      where: { businessPartnerKey: depot.businessPartnerKey, isDeleted: false, businessPartnerType: BusinessPartnerType.DEPOT },
    });

    if (checkExisted) {
      checkExisted = await this.updateDepot(checkExisted, rowDepot);
    } else {
      checkExisted = await this.createDepot(rowDepot, i18n);
    }

    return checkExisted;
  }

  async importContact(contact: ContactType, distributorKeys: string[], i18n: I18nContext) {
    /**
     *   businessPartnerContactKey: string;
     *   businessPartnerContactName1: string;
     *   businessPartnerContactName2: string;
     *   middleName: string;
     *   businessPartnerKey: string;
     *   businessPartnerContactPersonRole: string;
     *   businessPartnerContactStatus: string;
     *   communicationNumberTel: string;
     *   communicationNumberTelHome: string;
     *   communicationEmail: string;
     *   addressLine: string;
     *   street: string;
     *   houseNumber: string;
     *   postalCode: string;
     *   city: string;
     *   countryIsoCode: string;
     *   region: string;
     *   regionIsoCode: string;
     *   businessPartnerContactPersonJobTitle: string;
     *   birthday: string;
     */
    const communications = [];
    if (contact.communicationNumberTel) {
      communications.push({
        communicationName: '',
        communicationValue: contact.communicationNumberTel,
        businessPartnerKey: contact.businessPartnerKey,
        communicationType: BusinessPartnerRelationCommunication.TEL,
        communicationDescription: '',
        communicationStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerType: BusinessPartnerType.CONTACT,
      });
    }
    if (contact.communicationNumberTelHome) {
      communications.push({
        communicationName: '',
        communicationValue: contact.communicationNumberTelHome,
        businessPartnerKey: contact.businessPartnerKey,
        communicationType: BusinessPartnerRelationCommunication.PHONE,
        communicationDescription: '',
        communicationStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerType: BusinessPartnerType.CONTACT,
      });
    }
    if (contact.communicationEmail) {
      communications.push({
        communicationName: '',
        communicationValue: contact.communicationEmail,
        businessPartnerKey: contact.businessPartnerKey,
        communicationType: BusinessPartnerRelationCommunication.EMAIL,
        communicationDescription: '',
        communicationStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerType: BusinessPartnerType.CONTACT,
      });
    }

    const rowContact: BusinessPartnerContactDto = {
      businessPartnerContactName1: contact.businessPartnerContactName1,
      businessPartnerContactName2: contact.businessPartnerContactName2,
      businessPartnerContactKey: contact.businessPartnerContactKey,
      businessPartnerContactType: BusinessPartnerType.CONTACT,
      businessPartnerContactStatus: BusinessPartnerStatus.ACTIVE,
      businessPartnerContactPersonJobTitle: contact.businessPartnerContactPersonJobTitle,
      businessPartnerContactPersonRole: contact.businessPartnerContactPersonRole as BusinessPartnerContactRole,
      businessPartnerContactDescription: '',
      operatingHours: [],
      geoGraphicalLocations: [
        {
          businessPartnerType: BusinessPartnerType.CONTACT,
          locationStatus: BusinessPartnerStatus.ACTIVE,
          city: contact.city,
          street: contact.street,
          houseNumber: contact.houseNumber,
          postalCode: contact.postalCode,
          countryKey: contact.countryIsoCode,
          regionKey: contact.regionIsoCode,
          region: contact.region,
        },
      ],
      details: [
        {
          businessPartnerType: BusinessPartnerType.DEPOT,
          birthday: excelNumberToString(contact.birthday),
          customerCommercialHierarchy: {
            territory: '',
          },
          rawAddress: contact.addressLine,
        },
      ],
      communications,
      images: [],
    };

    if (distributorKeys.length) {
      rowContact.distributorPartnerKeys = distributorKeys;
    }

    let checkExisted = await this._businessPartnerContactService.findOne({
      where: { businessPartnerContactKey: rowContact.businessPartnerContactKey, isDeleted: false },
    });

    if (checkExisted) {
      checkExisted = await this._businessPartnerContactService.updateContact(checkExisted, rowContact);
    } else {
      checkExisted = await this._businessPartnerContactService.createContact(rowContact, i18n);
    }

    return checkExisted;
  }

  async checkImportDistributorData(distributor: DistributorType, index: number, i18n: I18nContext) {
    const invalidField = checkImportDataRequiredFields<DistributorType>(distributor, ['businessPartnerKey', 'businessPartnerName1'], DistributorDepotMapping.distributors);
    if (invalidField) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataRequiredError`, {
          args: { fileType: 'Distributor', dataType: 'distributor', row: index + 2, field: invalidField },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async checkImportDepotData(depot: DepotsType, index: number, i18n: I18nContext) {
    const invalidField = checkImportDataRequiredFields<DepotsType>(
      depot,
      ['businessPartnerKey', 'businessPartnerName1', 'businessPartnerDistributorKey'],
      DistributorDepotMapping.depots,
    );
    if (invalidField) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataRequiredError`, {
          args: { fileType: 'Distributor', dataType: 'depot', row: index + 2, field: invalidField },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async checkImportContactDistributorData(contact: ContactType, index: number, i18n: I18nContext) {
    const invalidField = checkImportDataRequiredFields<ContactType>(
      contact,
      ['businessPartnerKey', 'businessPartnerContactName1', 'businessPartnerContactKey', 'communicationEmail', 'businessPartnerContactPersonRole'],
      DistributorDepotMapping.contacts,
    );
    if (invalidField) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataRequiredError`, {
          args: { fileType: 'Distributor', dataType: 'contact', row: index + 2, field: invalidField },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
    const contactGeoLocationFields: (keyof ContactType)[] = ['city', 'countryIsoCode', 'houseNumber', 'postalCode', 'region', 'regionIsoCode', 'street'];
    if (contactGeoLocationFields.filter((field) => !!contact[field]).length < 1) {
      if (invalidField) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importDataInvalidError`, {
            args: { fileType: 'Distributor', dataType: 'contact', row: index + 2, field: 'geoLocations' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const contactRole = contact.businessPartnerContactPersonRole;

    if (
      ![
        BusinessPartnerContactRole.DISTRIBUTOR_ADMIN,
        BusinessPartnerContactRole.CALL_CENTER_MANAGEMENT,
        BusinessPartnerContactRole.MARKET_DEVELOPMENT_SUPERVISOR,
        BusinessPartnerContactRole.AREA_SALES_REP_MANAGER,
      ].includes(contactRole as BusinessPartnerContactRole)
    ) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataInvalidError`, {
          args: { fileType: 'Distributor', dataType: 'contact', row: index + 2, field: 'role' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async validateImportDistributorData(mappingResult: DistributorDepotMappingsType, i18n: I18nContext) {
    const { distributors, depots, contacts } = mappingResult;
    const distributorExternalKeys = [],
      depotExternalKeys = [],
      contactExternalKeys = [];

    for (let i = 0; i < distributors.length; i++) {
      if (distributorExternalKeys.includes(distributors[i].businessPartnerKey)) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importExternalKeyDuplicated`, {
            args: { fileType: 'Distributor', dataType: 'distributor', row: i + 2 },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      distributorExternalKeys.push(distributors[i].businessPartnerKey);
      await this.checkImportDistributorData(distributors[i], i, i18n);
    }

    for (let i = 0; i < depots.length; i++) {
      await this.checkImportDepotData(depots[i], i, i18n);
      if (depotExternalKeys.includes(depots[i].businessPartnerKey)) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importExternalKeyDuplicated`, {
            args: { fileType: 'Distributor', dataType: 'depot', row: i + 2 },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      depotExternalKeys.push(depots[i].businessPartnerKey);
    }

    const emailByRoles: string[] = [];

    for (let i = 0; i < contacts.length; i++) {
      const contactData = contacts[i];
      await this.checkImportContactDistributorData(contactData, i, i18n);
      const contactRole = contactData.businessPartnerContactPersonRole;
      const contactEmail = contactData?.communicationEmail?.trim();
      const emailAndRole = `${contactRole}-${contactEmail}`;
      if (
        (BusinessPartnerContactRole.DISTRIBUTOR_ADMIN === contactRole ||
          BusinessPartnerContactRole.CALL_CENTER_MANAGEMENT === contactRole ||
          BusinessPartnerContactRole.AREA_SALES_REP_MANAGER === contactRole) &&
        (!isValidEmail(contactEmail) || emailByRoles.includes(emailAndRole))
      ) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importDataInvalidError`, {
            args: { fileType: 'Distributor', dataType: 'contact', row: i + 2, field: 'email' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      emailByRoles.push(emailAndRole);

      if (contactExternalKeys.includes(String(contactData.businessPartnerContactKey))) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importExternalKeyDuplicated`, {
            args: { fileType: 'Distributor', dataType: 'contact', row: i + 2 },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }

      if (contactExternalKeys.includes(String(contacts[i].businessPartnerContactKey))) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importExternalKeyDuplicated`, {
            args: { fileType: 'Distributor', dataType: 'contact', row: i + 2 },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      contactExternalKeys.push(String(contacts[i].businessPartnerContactKey));
    }
  }

  async importBusinessPartnerDistributorDepot(mappingResult: DistributorDepotMappingsType, i18n: I18nContext) {
    if (isEmptyObjectOrArray(mappingResult)) {
      return null;
    }

    await this.validateImportDistributorData(mappingResult, i18n);

    let depots: any[] = [],
      contacts: any[] = [];

    // Process distributors in batches
    const distributors: BusinessPartner[] = await this._businessPartnerService.processBatch(mappingResult.distributors, async (distributor) =>
      this.importDistributor(distributor, i18n),
    );

    // Process depots in batches
    depots = await this._businessPartnerService.processBatch(mappingResult.depots, async (depot) => {
      let distributor = distributors.find((d) => String(d.businessPartnerKey) === String(depot.businessPartnerDistributorKey));
      if (isEmptyObjectOrArray(distributor)) {
        distributor = await this._businessPartnerService.findByExternalId(depot.businessPartnerDistributorKey);
      }
      return this.importDepot(depot, distributor, i18n);
    });

    // Process contacts in batches
    contacts = await this._businessPartnerService.processBatch(mappingResult.contacts, async (contact) => {
      const distributorPartnerKeys = (contact?.businessPartnerKey?.split && contact.businessPartnerKey.split(',').map((value) => value.trim())) || [contact?.businessPartnerKey];
      return this.importContact(contact, distributorPartnerKeys, i18n);
    });

    // Filter out null values from results
    return {
      distributors: distributors.filter(Boolean),
      depots: depots.filter(Boolean),
      contacts: contacts.filter(Boolean),
    };
  }

  async searchDistributors(searchDto: SearchDto) {
    const { offset, limit, orderBy, orderDesc, searchText } = searchDto;

    const baseConditions: any = { isDeleted: false, businessPartnerType: BusinessPartnerType.DISTRIBUTOR };
    let filterConditions: any = [];

    const businessPartnerIdsByCommunicationSearch = !!searchText
      ? await this._businessPartnerCommunicationService.findWithOptions({
          where: {
            isDeleted: false,
            businessPartnerType: BusinessPartnerType.DISTRIBUTOR,
            communicationValue: searchText,
          },
          select: ['businessPartner'],
        })
      : [];

    if (businessPartnerIdsByCommunicationSearch && businessPartnerIdsByCommunicationSearch.length) {
      filterConditions.push({
        ...baseConditions,
        id: In(businessPartnerIdsByCommunicationSearch.map((bp) => bp.businessPartner)),
      });
    }

    if (searchText) {
      filterConditions.push({
        ...baseConditions,
        businessPartnerName1: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerName2: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerKey: ILike(`%${searchText}%`),
      });
    }

    if (!filterConditions.length) {
      filterConditions = baseConditions;
    }

    const [distributors, count] = await this._businessPartnerService.findAndCount(filterConditions, offset, limit, orderBy, orderDesc);
    const distributorWithRelatedData = await this._businessPartnerService.attachBusinessPartnersRelationData(distributors);
    return {
      distributors: distributorWithRelatedData,
      count,
    };
  }

  async attachDepotDistributorData(depot: BusinessPartner) {
    const distributorData = {};
    const depotDistributorRelation = await this._businessPartnerRelationService.findOne({
      where: {
        businessPartner1: depot?.id,
        businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR,
        isDeleted: false,
      },
    });
    if (isEmptyObjectOrArray(depotDistributorRelation) || !depotDistributorRelation?.businessPartner2) {
      return distributorData;
    }

    const distributor = await this._businessPartnerService.findOne({
      where: {
        id: depotDistributorRelation?.businessPartner2,
        isDeleted: false,
      },
      select: ['id', 'businessPartnerKey'],
    });

    if (isEmptyObjectOrArray(distributor)) {
      return distributorData;
    }

    return {
      ...distributorData,
      distributorPartnerKey: distributor?.businessPartnerKey,
    };
  }

  async attachDepotSearchData(depot: BusinessPartner) {
    const response = await Promise.all([this.attachDepotDistributorData(depot), this._businessPartnerService.attachBusinessPartnerRelationData(depot)]).catch((error) => {
      console.log(`Error attachDepotSearchData for depot ${depot?.id}`, error);
      return depot;
    });

    if (isEmptyObjectOrArray(response)) {
      return depot;
    }

    return {
      ...response[1],
      ...response[0],
    };
  }

  async findDepotByIdWithAllRelations(id: string, i18n: I18nContext) {
    const depot = await this._businessPartnerService.findOne({
      where: {
        businessPartnerType: BusinessPartnerType.DEPOT,
        id,
        isDeleted: false,
      },
    });
    return this.attachDepotSearchData(depot);
  }

  async searchDepot(searchDto: SearchDto | any) {
    const { offset, limit, orderBy, orderDesc, searchText, depotIds } = searchDto;

    const baseConditions: any = { isDeleted: false, businessPartnerType: BusinessPartnerType.DEPOT };
    let filterConditions: any = [];

    if (Array.isArray(depotIds)) {
      baseConditions.id = In(depotIds);
    }

    const businessPartnerIdsByCommunicationSearch = !!searchText
      ? await this._businessPartnerCommunicationService.findWithOptions({
          where: {
            isDeleted: false,
            businessPartnerType: BusinessPartnerType.DEPOT,
            communicationValue: searchText,
          },
          select: ['businessPartner'],
        })
      : [];

    if (businessPartnerIdsByCommunicationSearch && businessPartnerIdsByCommunicationSearch.length) {
      filterConditions.push({
        ...baseConditions,
        id: In(businessPartnerIdsByCommunicationSearch.map((bp) => bp.businessPartner)),
      });
    }

    if (searchText) {
      filterConditions.push({
        ...baseConditions,
        businessPartnerName1: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerName2: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerKey: ILike(`%${searchText}%`),
      });
    }

    if (!filterConditions.length) {
      filterConditions = baseConditions;
    }

    const [depots, count] = await this._businessPartnerService.findAndCount(filterConditions, offset, limit, orderBy, orderDesc);
    // const depotWithRelatedData = await Promise.all(depots.map((d) => this.attachDepotSearchData(d)));
    let depotWithRelatedData = await this._businessPartnerService.attachBusinessPartnersRelationData(depots);
    depotWithRelatedData = await this.attachDepotDistributorRelationData(depotWithRelatedData);
    return {
      depots: depotWithRelatedData,
      count,
    };
  }

  async getDistributorAndDepotDataFromDepotExternalKey(depotExternalKey: string) {
    const depot = await this._businessPartnerService.findByExternalId(depotExternalKey);
    const depotDistributorRelation = await this._businessPartnerRelationService.findOne({
      where: {
        businessPartner1: depot?.id,
        businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR,
        isDeleted: false,
      },
    });
    if (isEmptyObjectOrArray(depotDistributorRelation) || !depotDistributorRelation?.businessPartner2) {
      return {};
    }

    const distributor = await this._businessPartnerService.findOne({
      where: {
        id: depotDistributorRelation?.businessPartner2,
        isDeleted: false,
      },
    });

    return { depot, distributor };
  }

  async attachDepotDistributorRelationData(depots: BusinessPartner[] | any) {
    const depotIds = depots.map((depot) => depot.id).filter((id) => !!id);
    if (depotIds.length === 0) {
      return depots;
    }

    // Get all depot-distributor relations in one query
    const depotRelations = await this._businessPartnerRelationService.find({
      where: {
        businessPartner1: In(depotIds),
        businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR,
        isDeleted: false,
      },
    });

    if (isEmptyObjectOrArray(depotRelations)) {
      return depots;
    }

    // Get all related distributors in one query
    const relatedDistributorIds = depotRelations.map((relation) => relation.businessPartner2);
    const relatedDistributors = await this._businessPartnerService.find({
      where: {
        id: In(relatedDistributorIds),
        isDeleted: false,
      },
      select: ['id', 'businessPartnerKey'],
    });

    // Map results back to depots
    return depots.map((depot) => {
      const relation = depotRelations.find((r) => r.businessPartner1 === depot.id);
      if (!relation) return depot;

      const relatedDistributor = relatedDistributors.find((d) => d.id === relation.businessPartner2);
      if (!relatedDistributor) return depot;

      return {
        ...depot,
        distributorPartnerKey: relatedDistributor.businessPartnerKey,
      };
    });
  }

  /**
   * Get all active depots and their related call centers with pagination for outlets and relations
   * @param pageSize Number of outlets to process in each batch
   * @returns Map of depot IDs to their related contact IDs
   */
  async getAllActiveDepotsWithCallCentersBatch(pageSize = 100): Promise<
    Array<{
      depotId: string;
      depotKey: string;
      contacts: Array<{
        contactId: string;
        contactKey: string;
      }>;
    }>
  > {
    // Step 1: Get all active depots
    const depots = await this._businessPartnerService.find({
      where: {
        businessPartnerType: BusinessPartnerType.DEPOT,
        isActive: true,
        isDeleted: false,
      },
      select: ['id', 'businessPartnerKey'],
    });

    if (depots.length === 0) {
      return [];
    }

    const depotIds = depots.map((depot) => depot.id);
    const depotKeyMap = new Map(depots.map((depot) => [depot.id, depot.businessPartnerKey]));

    // Step 2: Process outlet relations in batches
    let offset = 0;
    let hasMore = true;
    const outletToDepotsMap = new Map<string, string[]>();
    const finalDepotToContactsMap = new Map<
      string,
      {
        depotId: string;
        depotKey: string;
        contacts: Array<{
          contactId: string;
          contactKey: string;
        }>;
      }
    >();

    while (hasMore) {
      // Get outlet relations for this batch
      const [outletRelations, total] = await this._businessPartnerRelationService.findAndCount({
        where: {
          businessPartner2: In(depotIds),
          businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT,
          isDeleted: false,
        },
        select: ['businessPartner1', 'businessPartner2'],
        skip: offset,
        take: pageSize,
      });

      if (outletRelations.length === 0) {
        break;
      }

      // Add to outlet to depots map
      for (const rel of outletRelations) {
        const depots = outletToDepotsMap.get(rel.businessPartner1) || [];
        depots.push(rel.businessPartner2);
        outletToDepotsMap.set(rel.businessPartner1, depots);
      }

      // Process outlets in this batch
      const batchOutletIds = outletRelations.map((rel) => rel.businessPartner1);

      // Get contact relations for these outlets
      const contactRelations = await this._businessPartnerRelationService.find({
        where: {
          businessPartner2: In(batchOutletIds),
          businessPartnerRelationType: BusinessPartnerRelationType.CONTACT_OUTLET,
          isDeleted: false,
        },
        select: ['businessPartner1', 'businessPartner2'],
      });

      // Get contact keys
      const contactIds = contactRelations.map((rel) => rel.businessPartner1);
      const contacts = await this._businessPartnerContactService.find({
        where: {
          id: In(contactIds),
          isDeleted: false,
          businessPartnerContactPersonRole: BusinessPartnerContactRole.CALL_CENTER,
        },
        select: ['id', 'businessPartnerContactKey'],
      });
      const contactKeyMap = new Map(contacts.map((contact) => [contact.id, contact.businessPartnerContactKey]));

      // Map contacts to depots through outlets
      for (const rel of contactRelations) {
        const outletId = rel.businessPartner2;
        const contactId = rel.businessPartner1;
        const contactKey = contactKeyMap.get(contactId);
        if (!contactKey) continue;

        const depots = outletToDepotsMap.get(outletId) || [];
        for (const depotId of depots) {
          const depotKey = depotKeyMap.get(depotId);
          if (!depotKey) continue;

          const existing = finalDepotToContactsMap.get(depotId) || {
            depotId,
            depotKey,
            contacts: [],
          };

          // Check if contact already exists
          const contactExists = existing.contacts.some((c) => c.contactId === contactId);
          if (!contactExists) {
            existing.contacts.push({
              contactId,
              contactKey,
            });
          }
          finalDepotToContactsMap.set(depotId, existing);
        }
      }

      // Check if we've processed all relations
      offset += pageSize;
      hasMore = offset < total;
    }

    // Convert map to array
    return Array.from(finalDepotToContactsMap.values());
  }

  /**
   * Get all unique active call centers in the system with pagination
   * @param pageSize Number of outlets to process in each batch
   * @returns Array of unique contact IDs
   */
  async getAllActiveCallCentersBatch(pageSize = 100): Promise<any> {
    return await this.getAllActiveDepotsWithCallCentersBatch(pageSize);
  }
}
