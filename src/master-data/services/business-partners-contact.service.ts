import { BadRequestException, forwardRef, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, EntityManager, ILike, In, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartnerCommunicationService } from './business-partners-communication.service';
import { BusinessPartnerDetailService } from './business-partners-detail.service';
import { BusinessPartnersImageService } from './business-partners-image.service';
import { BusinessPartnersGeoLocationService } from './business-partners-geo-location.service';
import { BusinessPartnerOperatingHourService } from './business-partners-operating-hour.service';
import { isEmptyObjectOrArray, mapDataFromDtoToEntity } from 'src/utils';
import { BusinessPartnerContact } from '../entities/business-partner-contact/business-partner-contact.entity';
import { I18nContext } from 'nestjs-i18n';
import { runTransaction } from '../../utils/helpers/database';
import { BusinessPartnerCommunication } from '../entities/business-partner-communication/business-partner-communication.entity';
import { BusinessPartnerCommunicationDto } from '../dtos/business-partner-communication.dto';
import { BusinessPartnerImage } from '../entities/business-partner-image/business-partner-image.entity';
import { BusinessPartnerImageDto } from '../dtos/business-partner-image.dto';
import { BusinessPartnerDetail } from '../entities/business-partner-detail/business-partner-detail.entity';
import { BusinessPartnerDetailDto } from '../dtos/business-partner-detail.dto';
import { BusinessPartnerGeoLocation } from '../entities/business-partner-geo-location/business-partner-geo-location.entity';
import { BusinessPartnerGeoLocationDto } from '../dtos/business-partner-geo-location.dto';
import { BusinessPartnerOperatingHour } from '../entities/business-partner-operating-hour/business-partner-operating-hour.entity';
import { BusinessPartnerOpeningHourDto } from '../dtos/business-partner-opening-hour.dto';
import { BusinessPartnerContactDto } from '../dtos/business-partner-contact.dto';
import { BusinessPartnerRelationService } from './business-partners-relation.service';
import { SearchDto } from '../dtos/search.dto';
import {
  BusinessPartnerContactRole,
  BusinessPartnerRelationCommunication,
  BusinessPartnerRelationType,
  BusinessPartnerStatus,
  BusinessPartnerType,
} from '../constants/business-partner.enum';
import { BusinessPartnersService } from './business-partners.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AgentsMetricsArgs } from 'src/call-center/dtos/agents-metrics.args';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { AgentStatus, CallCenterStatus, CallPlanCreatorType } from 'src/call-center/enums/call-center.enum';
import * as moment from 'moment-timezone';
import { CONVERT_ML_TO_HL } from '../../external/constants';

@Injectable()
export class BusinessPartnersContactService extends BaseSQLService<BusinessPartnerContact> {
  constructor(
    @InjectRepository(BusinessPartnerContact)
    private readonly _businessPartnersContactRepository: Repository<BusinessPartnerContact>,

    @Inject(forwardRef(() => BusinessPartnerCommunicationService))
    readonly _businessPartnerCommunicationService: BusinessPartnerCommunicationService,

    @Inject(forwardRef(() => BusinessPartnerDetailService))
    readonly _businessPartnerDetailService: BusinessPartnerDetailService,

    @Inject(forwardRef(() => BusinessPartnersImageService))
    readonly _businessPartnerImageService: BusinessPartnersImageService,

    @Inject(forwardRef(() => BusinessPartnersGeoLocationService))
    readonly _businessPartnerGeoLocationService: BusinessPartnersGeoLocationService,

    @Inject(forwardRef(() => BusinessPartnerOperatingHourService))
    readonly _businessPartnerOperatingHourService: BusinessPartnerOperatingHourService,

    @Inject()
    readonly _businessPartnersGeoLocationService: BusinessPartnersGeoLocationService,

    @Inject()
    readonly _businessPartnersImageService: BusinessPartnersImageService,

    @Inject()
    readonly _businessPartnerRelationsService: BusinessPartnerRelationService,

    @Inject(forwardRef(() => BusinessPartnersService))
    readonly _businessPartnerService: BusinessPartnersService,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,

    private eventEmitter: EventEmitter2,
  ) {
    super();
    this._repository = this._businessPartnersContactRepository;
  }

  async update(id: string, updateData: any) {
    return this._repository.update(id, updateData);
  }

  async findById(id: string) {
    return this._repository.findOne({ where: { id } });
  }

  async rawQuery(sql: string, params: any[]) {
    return this._repository.query(sql, params);
  }

  async findByIdWithRelations(id: string, relations: string[]) {
    return this._repository.findOne({
      where: { id },
      relations,
    });
  }

  async loadContactRelation(dto: BusinessPartnerContactDto) {
    const distributorPartnerKeys = [];
    const outletPartnerKeys = [];
    if (dto?.distributorPartnerKey) {
      distributorPartnerKeys.push(dto.distributorPartnerKey);
    }

    if (dto?.distributorPartnerKeys?.length > 0) {
      distributorPartnerKeys.push(...dto.distributorPartnerKeys);
    }

    if (dto?.outletPartnerKey) {
      outletPartnerKeys.push(dto.outletPartnerKey);
    }

    if (dto?.outletPartnerKeys?.length > 0) {
      outletPartnerKeys.push(...dto.outletPartnerKeys);
    }

    if (distributorPartnerKeys.length < 1 && outletPartnerKeys.length < 1) {
      return [];
    }

    if (outletPartnerKeys.length > 0) {
      const outletRelationData = await Promise.all(
        [...new Set(outletPartnerKeys)].map(async (key) => {
          return {
            businessPartnerRelationType: BusinessPartnerRelationType.CONTACT_OUTLET,
            businessPartnerRelationData: await this._businessPartnerService.findByExternalId(key, { businessPartnerType: BusinessPartnerType.OUTLET }),
          };
        }),
      );
      return outletRelationData.filter((oR) => !!oR?.businessPartnerRelationData?.id);
    }

    const distributorRelations = await Promise.all(
      [...new Set(distributorPartnerKeys)].map(async (key) => {
        return {
          businessPartnerRelationType: BusinessPartnerRelationType.CONTACT_DISTRIBUTOR,
          businessPartnerRelationData: await this._businessPartnerService.findByExternalId(key, { businessPartnerType: BusinessPartnerType.DISTRIBUTOR }),
        };
      }),
    );

    return distributorRelations.filter((dR) => !!dR?.businessPartnerRelationData?.id);
  }

  async checkContactRelation(dto: BusinessPartnerContactDto) {
    if ((dto?.distributorPartnerKey || dto?.distributorPartnerKeys?.length > 0) && (dto?.outletPartnerKey || dto?.outletPartnerKeys?.length > 0)) {
      throw new BadRequestException('contact.conflict_reference_key');
    }

    if (!dto?.distributorPartnerKey && dto?.distributorPartnerKeys.length < 1 && !dto?.outletPartnerKey && dto?.outletPartnerKeys.length < 1) {
      throw new BadRequestException('contact.key_reference_not_found');
    }
  }

  async createContact(dto: BusinessPartnerContactDto, i18n: I18nContext, needContactRelation?: boolean, eventSync = true) {
    if (needContactRelation) {
      this.checkContactRelation(dto);
    }

    const contactRelations = await this.loadContactRelation(dto);

    // check if an business partner entity with businessPartnerKey already existed
    if (dto?.businessPartnerContactKey) {
      const existedEntity = await this.findOne({ where: { businessPartnerContactKey: dto?.businessPartnerContactKey } });
      if (existedEntity && !existedEntity?.isDeleted) {
        throw new BadRequestException('contact.existed_id');
      }
      return await this.updateContact(existedEntity, { ...dto, isDeleted: false, isActive: true });
    }

    const newBusinessPartnerEntity = this.mapBusinessPartnerContactDataFromDtoToEntity(null, dto);

    // TRANSACTIONAL
    const [newBusinessPartner, _, relations] = await runTransaction([
      async (entityManage: EntityManager, context) => {
        const newBusinessPartnerContact = await entityManage.save(BusinessPartnerContact, newBusinessPartnerEntity);
        context.businessPartnerContact = newBusinessPartnerContact;
        return newBusinessPartner;
      },

      async (entityManage: EntityManager, context) => {
        const businessPartnerContact = context.businessPartnerContact;
        return this.createBusinessPartnerRelatedData(entityManage, businessPartnerContact, dto);
      },

      async (entityManage: EntityManager, context) => {
        if (!isEmptyObjectOrArray(contactRelations)) {
          return [];
        }
        const businessPartner = context.businessPartner;
        const validContactRelations = contactRelations.filter(
          (contactRelation) => !isEmptyObjectOrArray(contactRelation?.businessPartnerRelationData) && contactRelation?.businessPartnerRelationType,
        );
        return this.updateContactRelationsData(businessPartner, validContactRelations, entityManage);
      },
    ]);
    const result = await this.attachBusinessPartnerContactRelationData(newBusinessPartner);

    if (eventSync) {
      this.eventEmitter.emit('contact.synced', {
        contact: result,
        relations,
      });
    }
    return result;
  }

  async updateOldContactDistributorRelations(contact: BusinessPartnerContact, contactRelations: any[], entityManager: EntityManager) {
    const contactOldRelations = await this._businessPartnerRelationsService.findRelationsWithEntityManager(
      {
        businessPartner1: contact.id,
        businessPartnerRelationType: contactRelations[0].businessPartnerRelationType,
      },
      entityManager,
    );

    const existedRelations = [],
      newRelations = [],
      existedRelationIds = [];

    for (let i = 0; i < contactRelations.length; i++) {
      const contactRelation = contactRelations[i];
      const oldRelation = contactOldRelations.find((cOR) => cOR.businessPartner2 === contactRelation?.businessPartnerRelationData?.id);
      if (!oldRelation) {
        newRelations.push(contactRelation);
        continue;
      }
      existedRelations.push(oldRelation);
      existedRelationIds.push(oldRelation.id);
    }

    const needDeleteRelations = contactOldRelations.filter((contactOldRelation) => !contactOldRelation?.isDeleted && !existedRelationIds.includes(contactOldRelation?.id));

    const restoreRelationPromises = this._businessPartnerRelationsService.updateRelations(
      existedRelations.map((eR) => {
        eR.isDeleted = false;
        eR.isActive = true;
        eR.businessPartnerRelationStatus = BusinessPartnerStatus.ACTIVE;
        eR.businessPartnerRelationValidFromDate = new Date();
        return eR;
      }),
      entityManager,
    );

    const createdRelationPromises = this._businessPartnerRelationsService.createRelations(
      newRelations.map((newRelation) => ({
        businessPartner1: contact.id,
        businessPartner2: newRelation?.businessPartnerRelationData?.id,
        businessPartnerRelationType: newRelation.businessPartnerRelationType,
        businessPartnerRelationValidFromDate: new Date(),
      })),
      entityManager,
    );

    const deleteRelationPromises = this._businessPartnerRelationsService.updateRelations(
      needDeleteRelations.map((nDR) => {
        nDR.isDeleted = true;
        nDR.isActive = false;
        return nDR;
      }),
      entityManager,
    );

    const [restoredRelations, deletedRelations, createdRelations] = await Promise.all([restoreRelationPromises, deleteRelationPromises, createdRelationPromises]);

    return [...createdRelations, ...restoredRelations, ...deletedRelations];
  }

  async updateOldContactOutletRelations(contact: BusinessPartnerContact, contactRelations: any[], entityManager: EntityManager) {
    const contactOldRelations = await this._businessPartnerRelationsService.findRelationsWithEntityManager(
      [
        {
          businessPartner1: contact.id,
          businessPartnerRelationType: BusinessPartnerRelationType.CONTACT_OUTLET,
        },
        {
          businessPartner2: In(contactRelations.map((cR) => cR?.businessPartnerRelationData?.id).filter((id) => !!id)),
          businessPartnerRelationType: BusinessPartnerRelationType.CONTACT_OUTLET,
        },
      ],
      entityManager,
    );

    const existedRelations = [],
      newRelations = [],
      existedRelationIds = [],
      needDeleteRelations = [];

    for (let i = 0; i < contactRelations.length; i++) {
      const contactRelation = contactRelations[i];
      const oldContactOutletRelation = contactOldRelations.find(
        (cOR) => cOR.businessPartner2 === contactRelation?.businessPartnerRelationData?.id && cOR.businessPartner1 === contact.id,
      );
      const oldOutletRelations = contactOldRelations.filter((cOR) => cOR.businessPartner2 === contactRelation?.businessPartnerRelationData?.id);
      if (oldOutletRelations.length < 1) {
        newRelations.push(contactRelation);
        continue;
      }

      const otherContactOutletRelations = oldOutletRelations.filter((r) => r?.businessPartner1 !== contact.id);
      const oldContactRelationIds = otherContactOutletRelations.map((relation) => relation?.businessPartner1);
      const oldContactRelationData = await this._repository.find({ where: { id: In(oldContactRelationIds) } });
      const relationContactDataByRole = oldContactRelationData.filter((data) => data?.businessPartnerContactPersonRole === contact.businessPartnerContactPersonRole);
      if (relationContactDataByRole.length < 1 && !oldContactOutletRelation) {
        newRelations.push(contactRelation);
        continue;
      }

      const relationContactDataByRoleIds = relationContactDataByRole.map((data) => data?.id);
      const oldContactRelationsByRoles = otherContactOutletRelations.filter((r) => relationContactDataByRoleIds.includes(r.businessPartner1));

      if (oldContactOutletRelation) {
        existedRelations.push(oldContactOutletRelation);
        existedRelationIds.push(oldContactOutletRelation.id);
        needDeleteRelations.push(...oldContactRelationsByRoles.filter((r) => !r.isDeleted));
        continue;
      }
      // update first and soft delete remain
      if (oldContactRelationsByRoles.length > 0) {
        const oldOutletRelationUpdate = oldContactRelationsByRoles[0];
        const oldOutletRelationsDelete = oldContactRelationsByRoles.slice(1);
        existedRelations.push(oldOutletRelationUpdate);
        existedRelationIds.push(oldOutletRelationUpdate.id);
        needDeleteRelations.push(...oldOutletRelationsDelete);
      }
    }

    needDeleteRelations.push(
      ...contactOldRelations.filter(
        (contactOldRelation) => contactOldRelation.businessPartner1 === contact.id && !contactOldRelation?.isDeleted && !existedRelationIds.includes(contactOldRelation?.id),
      ),
    );

    const restoreRelationPromises = this._businessPartnerRelationsService.updateRelations(
      existedRelations.map((eR) => {
        eR.isDeleted = false;
        eR.isActive = true;
        eR.businessPartnerRelationStatus = BusinessPartnerStatus.ACTIVE;
        eR.businessPartnerRelationValidFromDate = new Date();
        eR.businessPartner1 = contact.id;
        return eR;
      }),
      entityManager,
    );

    const createdRelationPromises = this._businessPartnerRelationsService.createRelations(
      newRelations.map((newRelation) => ({
        businessPartner1: contact.id,
        businessPartner2: newRelation?.businessPartnerRelationData?.id,
        businessPartnerRelationType: newRelation.businessPartnerRelationType,
        businessPartnerRelationValidFromDate: new Date(),
      })),
      entityManager,
    );

    const deleteRelationPromises = this._businessPartnerRelationsService.updateRelations(
      needDeleteRelations.map((nDR) => {
        nDR.isDeleted = true;
        nDR.isActive = false;
        return nDR;
      }),
      entityManager,
    );

    const [restoredRelations, deletedRelations, createdRelations] = await Promise.all([restoreRelationPromises, deleteRelationPromises, createdRelationPromises]);

    return [...createdRelations, ...restoredRelations, ...deletedRelations];
  }

  async updateContactRelationsData(contactData: BusinessPartnerContact, contactRelations: any[], entityManager: EntityManager) {
    const contactRelationType = contactRelations[0]?.businessPartnerRelationType;
    if (!contactRelationType) {
      return [];
    }
    if (contactRelationType === BusinessPartnerRelationType.CONTACT_DISTRIBUTOR) {
      return this.updateOldContactDistributorRelations(contactData, contactRelations, entityManager);
    }

    if (contactRelationType === BusinessPartnerRelationType.CONTACT_OUTLET) {
      return this.updateOldContactOutletRelations(contactData, contactRelations, entityManager);
    }

    return [];
  }

  async checkAndUpdateContactRelations(contactData: BusinessPartnerContact, updatedData: BusinessPartnerContactDto, entityManager: EntityManager) {
    if (!updatedData.distributorPartnerKey && !updatedData.distributorPartnerKeys && !updatedData.outletPartnerKey && !updatedData.outletPartnerKeys) {
      return null;
    }
    const contactRelations = await this.loadContactRelation(updatedData);
    return this.updateContactRelationsData(contactData, contactRelations, entityManager);
  }

  async updateContact(contact: BusinessPartnerContact, updateData: any, eventSync = true) {
    const { id, ...contactUpdateData } = updateData;
    const updateContactData = this.mapBusinessPartnerContactDataFromDtoToEntity(contact, contactUpdateData);

    // TRANSACTIONAL
    // Note
    const [contactData, response, relations] = await runTransaction([
      async (entityManager: EntityManager, context) => {
        // If update existed record, dont pass relation entity into update value
        const { details, communications, geoGraphicalLocations, images, operatingHours, ...updateContact } = updateContactData;
        const updatedBusinessPartner = await entityManager.save(BusinessPartnerContact, updateContact);
        context.businessPartner = updatedBusinessPartner;
        return updatedBusinessPartner;
      },
      async (entityManager: EntityManager, context) => {
        const businessPartner = context.businessPartner;
        return this.updateBusinessPartnerRelatedData(entityManager, businessPartner, updateContactData);
      },

      async (entityManager: EntityManager, context) => {
        const businessPartner = context.businessPartner;
        return this.checkAndUpdateContactRelations(businessPartner, updateData, entityManager);
      },
    ]);

    if (eventSync) {
      this.eventEmitter.emit('contact.synced', {
        contact: response,
        relations,
      });
    }
    return response;
  }

  async checkUniqueContactCommunicationData(businessPartnerContact: BusinessPartnerContact, communications: BusinessPartnerCommunicationDto[], entityManager?: EntityManager) {
    const businessPartnerContactRole = businessPartnerContact?.businessPartnerContactPersonRole;
    const businessPartnerContactId = businessPartnerContact?.id;

    if (businessPartnerContactRole === BusinessPartnerContactRole.SUPER_USER) {
      return;
    }

    if (businessPartnerContact.isDeleted || communications.length < 1) {
      return;
    }

    const distributorContacts = await (entityManager
      ? entityManager.find(BusinessPartnerContact, {
          where: {
            businessPartnerContactPersonRole: businessPartnerContactRole,
            isDeleted: false,
          },
        })
      : this._repository.find({
          where: {
            businessPartnerContactPersonRole: businessPartnerContactRole,
            isDeleted: false,
          },
        }));
    if (isEmptyObjectOrArray(distributorContacts)) {
      return;
    }

    if (
      businessPartnerContactRole === BusinessPartnerContactRole.CALL_CENTER_MANAGEMENT ||
      businessPartnerContactRole === BusinessPartnerContactRole.DISTRIBUTOR_ADMIN ||
      businessPartnerContactRole === BusinessPartnerContactRole.AREA_SALES_REP_MANAGER
    ) {
      const distributorContactEmails = await this._businessPartnerCommunicationService.findByBusinessPartners(
        distributorContacts.map((contact) => contact.id),
        entityManager,
        {
          communicationType: BusinessPartnerRelationCommunication.EMAIL,
        },
      );

      const emailCommunications = communications
        .filter((c) => c.businessPartnerType === BusinessPartnerType.CONTACT && c.communicationType === BusinessPartnerRelationCommunication.EMAIL)
        .map((eC) => eC.communicationValue);

      const duplicatedEmailCommunications = distributorContactEmails.filter(
        (dcE) => emailCommunications.includes(dcE.communicationValue) && businessPartnerContactId !== dcE.businessPartner,
      );
      if (duplicatedEmailCommunications.length) {
        throw new BadRequestException(`contact_communication.${businessPartnerContactRole.toLowerCase()}_duplicate_email_${duplicatedEmailCommunications[0].communicationValue}`);
      }
    }

    if (businessPartnerContactRole === BusinessPartnerContactRole.SALE_REP || businessPartnerContactRole === BusinessPartnerContactRole.CALL_CENTER) {
      const distributorContactPhones = await this._businessPartnerCommunicationService.findByBusinessPartners(
        distributorContacts.map((contact) => contact.id),
        entityManager,
        {
          communicationType: In([BusinessPartnerRelationCommunication.PHONE, BusinessPartnerRelationCommunication.TEL]),
        },
      );

      const mobilePhoneCommunications = communications
        .filter((c) => c.businessPartnerType === BusinessPartnerType.CONTACT && c.communicationType === BusinessPartnerRelationCommunication.PHONE)
        .map((eC) => eC.communicationValue);

      const phoneNumberCommunications = communications
        .filter((c) => c.businessPartnerType === BusinessPartnerType.CONTACT && c.communicationType === BusinessPartnerRelationCommunication.TEL)
        .map((eC) => eC.communicationValue);

      const duplicatedMobilePhoneCommunications = distributorContactPhones.filter(
        (dcP) =>
          mobilePhoneCommunications.includes(dcP.communicationValue) &&
          businessPartnerContactId !== dcP.businessPartner &&
          dcP.communicationType === BusinessPartnerRelationCommunication.PHONE,
      );

      const duplicatedPhoneNumberCommunications = distributorContactPhones.filter(
        (dcP) =>
          phoneNumberCommunications.includes(dcP.communicationValue) &&
          businessPartnerContactId !== dcP.businessPartner &&
          dcP.communicationType === BusinessPartnerRelationCommunication.TEL,
      );
      if (duplicatedMobilePhoneCommunications.length) {
        throw new BadRequestException(
          `contact_communication.${businessPartnerContactRole.toLowerCase()}_duplicate_mobile_phone_${duplicatedMobilePhoneCommunications[0].communicationValue}`,
        );
      }

      if (duplicatedPhoneNumberCommunications.length) {
        throw new BadRequestException(
          `contact_communication.${businessPartnerContactRole.toLowerCase()}_duplicate_phone_number_${duplicatedPhoneNumberCommunications[0].communicationValue}`,
        );
      }
    }

    return;
  }

  async updateBusinessPartnerRelatedData(entityManager: EntityManager, businessPartner: BusinessPartnerContact, dto: any) {
    const { images, communications, details, geoGraphicalLocations, operatingHours } = dto;
    await this.checkUniqueContactCommunicationData(businessPartner, communications);
    const partnerCommunications = (communications || []).map((communication) =>
      mapDataFromDtoToEntity<BusinessPartnerCommunication, BusinessPartnerCommunicationDto>(null, communication, BusinessPartnerCommunication),
    );
    const partnerImages = (images || []).map((image) => this._businessPartnersImageService.mapImageDataFromDtoToEntity(image));
    const partnerDetails = (details || []).map((detail) => mapDataFromDtoToEntity<BusinessPartnerDetail, BusinessPartnerDetailDto>(null, detail, BusinessPartnerDetail));
    const partnerGeoGraphicalLocations = (geoGraphicalLocations || []).map((geoLocation) =>
      mapDataFromDtoToEntity<BusinessPartnerGeoLocation, BusinessPartnerGeoLocationDto>(null, geoLocation, BusinessPartnerGeoLocation),
    );
    const partnerOperatingHours = (operatingHours || []).map((oh) =>
      mapDataFromDtoToEntity<BusinessPartnerOperatingHour, BusinessPartnerOpeningHourDto>(null, oh, BusinessPartnerOperatingHour),
    );

    const updatedRelatedData = await Promise.all([
      this._businessPartnerCommunicationService.replaceBusinessPartnerContactCommunications(businessPartner, partnerCommunications, entityManager),
      this._businessPartnerDetailService.replaceBusinessPartnerContactDetails(businessPartner, partnerDetails, entityManager),
      this._businessPartnersImageService.replaceBusinessPartnerContactImages(businessPartner, partnerImages, entityManager),
      this._businessPartnersGeoLocationService.replaceBusinessPartnerContactGeoLocations(businessPartner, partnerGeoGraphicalLocations, entityManager),
      this._businessPartnerOperatingHourService.replaceBusinessPartnerContactOperatingHours(businessPartner, partnerOperatingHours, entityManager),
    ]);

    return {
      ...businessPartner,
      communications: updatedRelatedData[0] || [],
      details: updatedRelatedData[1] || [],
      images: updatedRelatedData[2] || [],
      geoGraphicalLocations: updatedRelatedData[3] || [],
      operatingHours: updatedRelatedData[4] || [],
    };
  }

  async attachBusinessPartnerContactRelationData(businessPartner: BusinessPartnerContact) {
    const errorLogging = (fieldName, error) => {
      console.log(`Error loading ${fieldName} for business partner contact ${businessPartner.id} relation data`, error?.message);
      return [];
    };
    const [details, communications, geoGraphicalLocations, images, operatingHours] = await Promise.all([
      this._businessPartnerDetailService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('details', error)),
      this._businessPartnerCommunicationService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('communications', error)),
      this._businessPartnerGeoLocationService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('geoLocations', error)),
      this._businessPartnerImageService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('images', error)),
      this._businessPartnerOperatingHourService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('operating hours', error)),
    ]);
    return {
      ...businessPartner,
      details,
      communications,
      geoGraphicalLocations,
      images,
      operatingHours,
    };
  }

  async attachBusinessPartnerContactsRelationData(businessPartnerContacts: BusinessPartnerContact[]) {
    const errorLogging = (fieldName, error) => {
      console.log(`Error loading ${fieldName} for business partner contacts`, error?.message);
      return [];
    };
    const businessPartnerContactIds = businessPartnerContacts.map((bpc) => bpc.id);
    const [businessPartnerDetails, businessPartnerCommunications, businessPartnerGeoGraphicalLocations, businessPartnerImages, businessPartnerOperatingHours] = await Promise.all([
      this._businessPartnerDetailService.findByBusinessPartnerIds(businessPartnerContactIds).catch((error) => errorLogging('details', error)),
      this._businessPartnerCommunicationService.findByBusinessPartnerIds(businessPartnerContactIds).catch((error) => errorLogging('communications', error)),
      this._businessPartnerGeoLocationService.findByBusinessPartnerIds(businessPartnerContactIds).catch((error) => errorLogging('geoLocations', error)),
      this._businessPartnerImageService.findByBusinessPartnerIds(businessPartnerContactIds).catch((error) => errorLogging('images', error)),
      this._businessPartnerOperatingHourService.findByBusinessPartnerIds(businessPartnerContactIds).catch((error) => errorLogging('operatingHours', error)),
    ]);
    return businessPartnerContacts.map((bpc) => ({
      ...bpc,
      details: businessPartnerDetails.filter((bpD) => bpD?.businessPartner === bpc.id),
      communications: businessPartnerCommunications.filter((bpC) => bpC?.businessPartner === bpc.id),
      geoGraphicalLocations: businessPartnerGeoGraphicalLocations.filter((bpG) => bpG?.businessPartner === bpc.id),
      images: businessPartnerImages.filter((bpI) => bpI?.businessPartner === bpc.id),
      operatingHours: businessPartnerOperatingHours.filter((bpO) => bpO?.businessPartner === bpc.id),
    }));
  }

  async createBusinessPartnerRelatedData(entityManager: EntityManager, businessPartnerContact: BusinessPartnerContact, dto: BusinessPartnerContactDto) {
    const { images, communications, details, geoGraphicalLocations, operatingHours } = dto;
    await this.checkUniqueContactCommunicationData(businessPartnerContact, communications);
    const partnerCommunications = (communications || []).map((communication) =>
      mapDataFromDtoToEntity<BusinessPartnerCommunication, BusinessPartnerCommunicationDto>(null, communication, BusinessPartnerCommunication),
    );
    const partnerImages = (images || []).map((image) => mapDataFromDtoToEntity<BusinessPartnerImage, BusinessPartnerImageDto>(null, image, BusinessPartnerImage));
    const partnerDetails = (details || []).map((detail) => mapDataFromDtoToEntity<BusinessPartnerDetail, BusinessPartnerDetailDto>(null, detail, BusinessPartnerDetail));
    const partnerGeoGraphicalLocations = (geoGraphicalLocations || []).map((geoLocation) =>
      mapDataFromDtoToEntity<BusinessPartnerGeoLocation, BusinessPartnerGeoLocationDto>(null, geoLocation, BusinessPartnerGeoLocation),
    );
    const partnerOperatingHours = (operatingHours || []).map((operatingHour) =>
      mapDataFromDtoToEntity<BusinessPartnerOperatingHour, BusinessPartnerOpeningHourDto>(null, operatingHour, BusinessPartnerOperatingHour),
    );
    return Promise.all([
      this._businessPartnerCommunicationService.createCommunicationsForBusinessPartnerContact(businessPartnerContact, partnerCommunications, entityManager),
      this._businessPartnerDetailService.createDetailsForBusinessPartnerContact(businessPartnerContact, partnerDetails, entityManager),
      this._businessPartnersImageService.createImagesForBusinessPartnerContact(businessPartnerContact, partnerImages, entityManager),
      this._businessPartnersGeoLocationService.createGeoLocationsForBusinessPartnerContact(businessPartnerContact, partnerGeoGraphicalLocations, entityManager),
      this._businessPartnerOperatingHourService.createOperatingHoursForBusinessPartnerContact(businessPartnerContact, partnerOperatingHours, entityManager),
    ]);
  }

  async checkExistAndUpdateContact(id: string, updateData: any, i18n: I18nContext) {
    const contact = await this.findOne({ where: { id } });
    if (!contact) {
      throw new NotFoundException(i18n.t('depot.not_found'));
    }

    return this.updateContact(contact, updateData);
  }

  async checkExistAndDeleteContact(id: string, i18n: I18nContext) {
    const deactivatedContact = await this.checkExistAndUpdateContact(id, { isDeleted: true, isActive: false }, i18n);

    // Deactivate related data
    await this.deactivateRelatedBusinessPartnerData(deactivatedContact);
    this.eventEmitter.emit('contact.synced', { contact: deactivatedContact, relations: [] });
    return deactivatedContact;
  }

  async deactivateRelatedBusinessPartnerData(businessPartner: any) {
    return Promise.all([
      this._businessPartnerCommunicationService.removeBusinessPartnerCommunications(businessPartner.id),
      this._businessPartnerDetailService.removeBusinessPartnerDetails(businessPartner.id),
      this._businessPartnersImageService.removeBusinessPartnerImages(businessPartner.id),
      this._businessPartnersGeoLocationService.removeBusinessPartnerGeoLocations(businessPartner.id),
      this._businessPartnerRelationsService.removeBusinessPartnerRelations(businessPartner.id),
    ]);
  }

  async findByIdWithAllRelations(id: string) {
    const businessPartnerContact = await this.findById(id);
    if (isEmptyObjectOrArray(businessPartnerContact)) {
      return businessPartnerContact;
    }
    return this.attachContactSearchData(businessPartnerContact);
  }

  async deleteBusinessPartner(businessPartner: BusinessPartnerContact) {
    if (!businessPartner || !businessPartner.id) {
      return null;
    }

    return this.update(businessPartner.id, { isDeleted: true, isActive: false });
  }

  mapBusinessPartnerContactDataFromDtoToEntity(originalData: BusinessPartnerContact, data: BusinessPartnerContactDto): BusinessPartnerContact {
    const newBusinessPartnerContactEntity = mapDataFromDtoToEntity<BusinessPartnerContact, BusinessPartnerContactDto>(originalData, data, BusinessPartnerContact);
    newBusinessPartnerContactEntity.images = data?.images?.map((image) => this._businessPartnersImageService.mapImageDataFromDtoToEntity(image));
    return newBusinessPartnerContactEntity;
  }

  async searchContacts(searchDto: SearchDto | any) {
    const { offset, limit, orderBy, orderDesc, searchText, contactIds } = searchDto;

    const baseConditions: any = { isDeleted: false };
    let filterConditions: any = [];

    if (Array.isArray(contactIds)) {
      baseConditions.id = In(contactIds);
    }

    const businessPartnerContactIdsByCommunicationSearch = !!searchText
      ? await this._businessPartnerCommunicationService.findWithOptions({
          where: {
            isDeleted: false,
            businessPartnerType: BusinessPartnerType.CONTACT,
            communicationValue: searchText,
          },
          select: ['businessPartner'],
        })
      : [];

    if (businessPartnerContactIdsByCommunicationSearch && businessPartnerContactIdsByCommunicationSearch.length) {
      filterConditions.push({
        ...baseConditions,
        id: In(businessPartnerContactIdsByCommunicationSearch.map((bpC) => bpC.businessPartner)),
      });
    }

    if (searchText) {
      filterConditions.push({
        ...baseConditions,
        businessPartnerContactName1: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerContactName2: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerContactKey: ILike(`%${searchText}%`),
      });
    }

    if (!filterConditions.length) {
      filterConditions = baseConditions;
    }

    const [contacts, count] = await this.findAndCount(filterConditions, offset, limit, orderBy, orderDesc);
    const distributorWithRelatedData = await Promise.all(contacts.map((c) => this.attachBusinessPartnerContactRelationData(c)));
    return {
      contacts: distributorWithRelatedData,
      count,
    };
  }

  async findAndCount(filters: any = {}, offset = 0, limit = 1000, orderBy = 'id', orderDesc = 'ASC') {
    return this._repository.findAndCount({
      where: filters,
      order: {
        [orderBy]: orderDesc,
      },
      skip: offset,
      take: limit,
    });
  }

  async attachContactRelationData(contact: BusinessPartnerContact) {
    const contactRelationData = await this._businessPartnerRelationsService.find({
      where: {
        businessPartner1: contact.id,
        businessPartnerRelationType: In([BusinessPartnerRelationType.CONTACT_DISTRIBUTOR, BusinessPartnerRelationType.CONTACT_OUTLET]),
        isDeleted: false,
      },
    });
    const businessPartnerRelationIds = (contactRelationData || []).map((data) => data?.businessPartner2);
    if (isEmptyObjectOrArray(businessPartnerRelationIds)) {
      return {};
    }

    const businessPartnerRelationData = await this._businessPartnerService.find({
      where: {
        id: In(businessPartnerRelationIds),
        isDeleted: false,
      },
      select: ['id', 'businessPartnerKey', 'businessPartnerType'],
    });

    if (isEmptyObjectOrArray(businessPartnerRelationData)) {
      return {};
    }

    return {
      distributorPartnerKeys: businessPartnerRelationData.filter((data) => data?.businessPartnerType === BusinessPartnerType.DISTRIBUTOR).map((data) => data.businessPartnerKey),
      outletPartnerKeys: businessPartnerRelationData.filter((data) => data?.businessPartnerType === BusinessPartnerType.OUTLET).map((data) => data.businessPartnerKey),
    };
  }

  async attachContactSearchData(contact: BusinessPartnerContact) {
    const response = await Promise.all([this.attachContactRelationData(contact), this.attachBusinessPartnerContactRelationData(contact)]).catch((error) => {
      console.log(`Error attachDepotSearchData for depot ${contact?.id}`, error);
      return contact;
    });

    if (isEmptyObjectOrArray(response)) {
      return contact;
    }

    return {
      ...response[1],
      ...response[0],
    };
  }

  async attachContactsRelationData(contacts: BusinessPartnerContact[]) {
    const contactIds = contacts.map((contact) => contact.id).filter((id) => !!id);
    if (contactIds.length === 0) {
      return contacts;
    }

    // Get all contact-outlet and contact-distributor relations in one query
    const contactRelations = await this._businessPartnerRelationsService.find({
      where: {
        businessPartner1: In(contactIds),
        businessPartnerRelationType: In([BusinessPartnerRelationType.CONTACT_OUTLET, BusinessPartnerRelationType.CONTACT_DISTRIBUTOR]),
        isDeleted: false,
      },
    });

    if (isEmptyObjectOrArray(contactRelations)) {
      return contacts;
    }

    // Get all related outlets and distributors in one query
    const relatedPartnerIds = contactRelations.map((relation) => relation.businessPartner2);
    const relatedPartners = await this._businessPartnerService.find({
      where: {
        id: In(relatedPartnerIds),
        isDeleted: false,
      },
      select: ['id', 'businessPartnerKey', 'businessPartnerType'],
    });

    // Map results back to contacts
    return contacts.map((contact) => {
      const relation = contactRelations.find((r) => r.businessPartner1 === contact.id);
      if (!relation) return contact;

      const relatedPartner = relatedPartners.find((p) => p.id === relation.businessPartner2);
      if (!relatedPartner) return contact;

      if (relatedPartner.businessPartnerType === BusinessPartnerType.OUTLET) {
        return {
          ...contact,
          outletPartnerKey: relatedPartner.businessPartnerKey,
        };
      }
      if (relatedPartner.businessPartnerType === BusinessPartnerType.DISTRIBUTOR) {
        return {
          ...contact,
          distributorPartnerKey: relatedPartner.businessPartnerKey,
        };
      }

      return contact;
    });
  }

  async queryContactRelationIds(businessPartnerContactKey: string = null) {
    if (!businessPartnerContactKey) {
      return;
    }
    const businessPartnerContact = await this._repository.findOne({
      where: {
        isDeleted: false,
        businessPartnerContactKey,
      },
      select: ['id', 'businessPartnerContactKey'],
    });

    return this._businessPartnerRelationsService.findRelatedBusinessPartnerIdsFromContact(businessPartnerContact?.id);
  }

  /**
   * Mongo User to Contact Person data
   * @param currentUser
   */
  async getCurrentContact(currentUser: any): Promise<any> {
    const contact = await this.findOne({ where: { businessPartnerContactKey: currentUser.saleRepId || currentUser?.contactId } });
    return { contact, currentUser };
  }

  async getDistributorId(contact: BusinessPartnerContact): Promise<any> {
    const contactOutlet = await this._businessPartnerRelationsService.findOne({
      where: { businessPartner1: contact.id, businessPartnerRelationType: BusinessPartnerRelationType.CONTACT_OUTLET },
    });
    if (isEmptyObjectOrArray(contactOutlet)) {
      return null;
    }
    const outletDepot = await this._businessPartnerRelationsService.findOne({
      where: { businessPartner1: contactOutlet.businessPartner2, businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT },
    });
    if (isEmptyObjectOrArray(outletDepot)) {
      return null;
    }
    const depotDistributor = await this._businessPartnerRelationsService.findOne({
      where: { businessPartner1: outletDepot.businessPartner2, businessPartnerRelationType: BusinessPartnerRelationType.DEPOT_DISTRIBUTOR },
    });
    return { distributorId: depotDistributor?.businessPartner2 };
  }

  async getAgentsPerformance(params: AgentsMetricsArgs & PaginationParams & OrderParams, callCenterIds: string[]) {
    let { startDate, endDate } = params;
    // If no date range provided, default to current month
    const today = moment().tz(process.env.TZ);
    if (!startDate && !endDate) {
      startDate = today.clone().startOf('month').toDate();
      endDate = today.clone().endOf('month').endOf('day').toDate();
    } else {
      // Convert ISO strings to Date objects if they are strings
      if (typeof startDate === 'string') {
        startDate = moment(startDate).tz(process.env.TZ).startOf('day').toDate();
      }
      if (typeof endDate === 'string') {
        endDate = moment(endDate).tz(process.env.TZ).endOf('day').toDate();
      }
    }

    const endToday = today.clone().endOf('day');
    if (moment(endDate).isAfter(endToday)) {
      endDate = endToday.toDate();
    }
    // Define reusable expressions
    const statusExpression = `CASE 
      WHEN COUNT(CASE WHEN "callPlannings"."callStatus" = '${CallCenterStatus.IN_PROGRESS}' THEN 1 END) > 0 THEN '${AgentStatus.IN_A_CALL}'
      ELSE '${AgentStatus.IDLE}'
    END`;

    const customerInCallExpression = `(
      SELECT JSON_BUILD_OBJECT(
        'name', bp."businessPartnerName2",
        'id', bp."businessPartnerKey"
      )
      FROM call_plannings cp
      LEFT JOIN business_partners bp ON bp.id = cp."outletId"
      WHERE cp."callCenterId" = "callCenter"."id"
      AND cp."callStatus" = '${CallCenterStatus.IN_PROGRESS}'
      AND cp."displayDay" >= :startDate
      AND cp."displayDay" <= :endDate
      LIMIT 1
    )`;

    const callCoverageExpression = `(
      SELECT 
        COALESCE(
          JSON_BUILD_OBJECT(
            'numerator', CAST(COUNT(CASE WHEN cp."callStatus" = '${CallCenterStatus.COMPLETED}' THEN 1 END) AS NUMERIC),
            'denominator', CAST(COUNT(cp.id) AS NUMERIC),
            'value', ROUND(CAST(COUNT(CASE WHEN cp."callStatus" = '${CallCenterStatus.COMPLETED}' THEN 1 END) AS NUMERIC) / NULLIF(COUNT(cp.id), 0) * 100, 2),
            'target', CAST(ts."callCoverage" AS NUMERIC)
          ),
          JSON_BUILD_OBJECT(
            'numerator', 0,
            'denominator', 0,
            'value', 0,
            'target', CAST(ts."callCoverage" AS NUMERIC)
          )
        )
      FROM target_settings ts
      LEFT JOIN call_plannings cp ON cp."callCenterId" = "callCenter"."id"
      WHERE ts."agentId" = "callCenter"."businessPartnerContactKey"
      AND ts."month" = EXTRACT(MONTH FROM CURRENT_DATE)
      AND ts."year" = EXTRACT(YEAR FROM CURRENT_DATE)
      AND cp."displayDay" >= :startDate
      AND cp."displayDay" <= :endDate
      AND cp."isDeleted" = false
      AND cp."creator" = :creator
      AND ts."isDeleted" = false
      GROUP BY ts."callCoverage"
    )`;

    const strikeRateExpression = `(
      SELECT 
        COALESCE(
          JSON_BUILD_OBJECT(
            'numerator', CAST(COUNT(DISTINCT CASE WHEN cpo.id IS NOT NULL AND cp."callStatus" = '${CallCenterStatus.COMPLETED}' THEN cp.id END) AS NUMERIC),
            'denominator', CAST(COUNT(CASE WHEN cp."callStatus" = '${CallCenterStatus.COMPLETED}' THEN cp.id END) AS NUMERIC),
            'value', ROUND(CAST(COUNT(DISTINCT CASE WHEN cpo.id IS NOT NULL AND cp."callStatus" = '${CallCenterStatus.COMPLETED}' THEN cp.id END) AS NUMERIC) / NULLIF(COUNT(CASE WHEN cp."callStatus" = '${CallCenterStatus.COMPLETED}' THEN cp.id END), 0) * 100, 2),
            'target', CAST(ts."strikeRate" AS NUMERIC)
          ),
          JSON_BUILD_OBJECT(
            'numerator', 0,
            'denominator', 0,
            'value', 0,
            'target', CAST(ts."strikeRate" AS NUMERIC)
          )
        )
      FROM target_settings ts
      LEFT JOIN call_plannings cp ON cp."callCenterId" = "callCenter"."id"
      LEFT JOIN call_planning_orders cpo ON cpo."callPlanningId" = cp.id AND DATE(cpo."orderDate") = DATE(cp."displayDay")
      WHERE ts."agentId" = "callCenter"."businessPartnerContactKey"
      AND ts."month" = EXTRACT(MONTH FROM CURRENT_DATE)
      AND ts."year" = EXTRACT(YEAR FROM CURRENT_DATE)
      AND cp."displayDay" >= :startDate
      AND cp."displayDay" <= :endDate
      AND cp."isDeleted" = false
      AND cp."creator" = :creator
      AND ts."isDeleted" = false
      GROUP BY ts."strikeRate"
    )`;

    const volumeTargetExpression = `(
      SELECT 
        CASE 
          WHEN ts."volumeTarget" IS NOT NULL
          THEN JSON_BUILD_OBJECT(
            'numerator', ROUND(
              CAST(
                COALESCE(
                  (
                    SELECT SUM(cpo."orderVolume")
                    FROM call_plannings cp
                    LEFT JOIN call_planning_orders cpo ON cpo."callPlanningId" = cp.id
                    WHERE cp."callCenterId" = "callCenter"."id"
                    AND cp."callStatus" = '${CallCenterStatus.COMPLETED}'
                    AND cp."displayDay" >= :startDate
                    AND cp."displayDay" <= :endDate
                  ),
                  0
                ) AS NUMERIC
              ) / ${CONVERT_ML_TO_HL},
            5),
            'denominator', CAST(ts."volumeTarget" AS NUMERIC),
            'target', CAST(ts."volumeTarget" AS NUMERIC),
            'value', ROUND(
              CAST(
                COALESCE(
                  (
                    SELECT SUM(cpo."orderVolume")
                    FROM call_plannings cp
                    LEFT JOIN call_planning_orders cpo ON cpo."callPlanningId" = cp.id
                    WHERE cp."callCenterId" = "callCenter"."id"
                    AND cp."callStatus" = '${CallCenterStatus.COMPLETED}'
                    AND cp."displayDay" >= :startDate
                    AND cp."displayDay" <= :endDate
                  ),
                  0
                ) AS NUMERIC
              ) / (NULLIF(CAST(ts."volumeTarget" AS NUMERIC), 0) * ${CONVERT_ML_TO_HL} / 100),
              2
            )
          )
          ELSE NULL
        END
      FROM target_settings ts
      WHERE ts."agentId" = "callCenter"."businessPartnerContactKey"
      AND ts."month" = EXTRACT(MONTH FROM CURRENT_DATE)
      AND ts."year" = EXTRACT(YEAR FROM CURRENT_DATE)
      AND ts."isDeleted" = false
      LIMIT 1
    )`;

    const callPlanningExpression = `(
      SELECT COALESCE(
        JSON_AGG(
          JSON_BUILD_OBJECT(
            'id', cp.id,
            'displayDay', cp."displayDay",
            'callStatus', cp."callStatus"
          )
        ),
        '[]'::json
      )
      FROM call_plannings cp
      WHERE "cp"."callCenterId" = "callCenter"."id"
      AND cp."displayDay" >= :startDate
      AND cp."displayDay" <= :endDate
      AND cp."isDeleted" = false
    )`;

    const communicationsExpression = `jsonb_object_agg(communications.communicationType, communications.communicationValue) FILTER (WHERE communications.id IS NOT NULL AND communications.communicationType IS NOT NULL AND communications.communicationType != '' AND communications.communicationValue IS NOT NULL AND communications.communicationValue != '')`;

    const query = this._repository
      .createQueryBuilder('callCenter')
      .leftJoinAndSelect('call_plannings', 'callPlannings', 'callPlannings.callCenterId = callCenter.id')
      .innerJoinAndSelect('business_partner_communications', 'communications', 'communications.businessPartner = callCenter.id AND communications.isDeleted = false')
      .setParameter('startDate', startDate)
      .setParameter('endDate', endDate)
      .setParameter('creator', CallPlanCreatorType.MANAGER)
      .select([
        'callCenter.id as "id"',
        'callCenter.businessPartnerContactKey as "contactId"',
        'callCenter.businessPartnerContactName1 as "name"',
        'callCenter.businessPartnerContactName2 as "tradingName"',
        'callCenter.businessPartnerContactType as "type"',
        `${callPlanningExpression} as "callPlannings"`,
        `${statusExpression} as "status"`,
        `${customerInCallExpression} as "customerInCall"`,
        `${volumeTargetExpression} as "volumeTarget"`,
        `${callCoverageExpression} as "callCoverage"`,
        `${strikeRateExpression} as "strikeRate"`,
        `${communicationsExpression} as "communications"`,
      ])
      .where('callCenter.id IN (:...callCenterIds)', { callCenterIds })
      .andWhere('callCenter.businessPartnerContactPersonRole = :role', { role: BusinessPartnerContactRole.CALL_CENTER })
      .andWhere('"callPlannings"."displayDay" >= :startDate', { startDate })
      .andWhere('"callPlannings"."displayDay" <= :endDate', { endDate })
      .andWhere('"callPlannings"."isDeleted" = false')
      .andWhere('"callCenter"."isDeleted" = false')
      .groupBy('"callCenter"."id"');

    if (params.search) {
      query.andWhere(
        new Brackets((qb) => {
          qb.where('callCenter.businessPartnerContactName2 ILIKE :search', { search: `%${params.search}%` })
            .orWhere('callCenter.businessPartnerContactName1 ILIKE :search', { search: `%${params.search}%` })
            .orWhere('callCenter.businessPartnerContactKey ILIKE :search', { search: `%${params.search}%` });
        }),
      );
    }

    // Handle ordering
    if (params.orderBy) {
      const orderDirection = params.orderDesc === 'DESC' ? 'DESC' : 'ASC';
      const orderNullDirection = params.orderDesc === 'DESC' ? 'NULLS LAST' : 'NULLS FIRST';
      switch (params.orderBy) {
        case 'name':
          query.orderBy('callCenter.businessPartnerContactName2', orderDirection);
          break;
        case 'status':
          query.orderBy(statusExpression, orderDirection, orderNullDirection);
          break;
        case 'customerInCall':
          query.orderBy(`(${customerInCallExpression}->>'name')`, orderDirection, orderNullDirection);
          break;
        case 'callCoverage':
          query.orderBy(`(${callCoverageExpression}->>'value')::numeric`, orderDirection, orderNullDirection);
          break;
        case 'strikeRate':
          query.orderBy(`(${strikeRateExpression}->>'value')::numeric`, orderDirection, orderNullDirection);
          break;
        case 'volumeTarget':
          query.orderBy(`(${volumeTargetExpression}->>'value')::numeric`, orderDirection, orderNullDirection);
          break;
        default:
          query.orderBy('callCenter.businessPartnerContactName2', orderDirection);
      }
    } else {
      query.orderBy('callCenter.businessPartnerContactName2', 'ASC');
    }

    const [agents, total] = await Promise.all([query.getRawMany(), query.getCount()]);
    return {
      agents,
      total,
    };
  }
}
