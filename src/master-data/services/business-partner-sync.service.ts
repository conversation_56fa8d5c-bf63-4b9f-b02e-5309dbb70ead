import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { BusinessPartnerContact } from '../entities/business-partner-contact/business-partner-contact.entity';
import { BusinessPartnerRelationCommunication, BusinessPartnerRelationType, BusinessPartnerStatus, BusinessPartnerType, QueueTypes } from '../constants/business-partner.enum';
import * as _ from 'lodash';
import { isEmptyObjectOrArray, printLog } from '../../utils';
import { BusinessPartnersService } from './business-partners.service';
import { BusinessPartnersContactService } from './business-partners-contact.service';
import { BusinessPartnerOutletService } from './business-partner-outlet.service';
import { BusinessPartnerDistributorService } from './business-partner-distributor.service';
import { DistributorService, DistributorUserRelationService } from '../../distributor/services';
import { OutletsService } from '../../outlets/services/outlets.service';
import { UsersService } from '../../users/services/users.service';
import { UserAdminsService } from '../../users/services/user-admins.service';
import { BusinessPartnerRelationService } from './business-partners-relation.service';
import { ConstantRoles } from '../../utils/constants/role';
import { SaleRepOutletRelationService } from '../../outlets/services/sale-rep-outlet-relation.service';
import { QueueLog } from '../entities/queue/queue-log.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { SolaceService } from '../../third-parties/services/solace.service';
import { ConfigService } from '@nestjs/config';
import { BusinessPartnerContactEDMData, BusinessPartnerEDMData } from '../interfaces/edm-data.interface';
import { UserDetailService } from '../../users/services/user-detail.service';
import { BusinessPartnerSearchService } from './business-partner-search.service';
import { BusinessPartnerOutletDto } from '../dtos/business-partner-outlet.dto';
import { BusinessPartnerContactDto } from '../dtos/business-partner-contact.dto';
import { I18nContext } from 'nestjs-i18n';

@Injectable()
export class BusinessPartnerSyncService {
  constructor(
    private eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => DistributorService))
    private distributorModel: DistributorService,
    @Inject(forwardRef(() => OutletsService))
    private outletModel: OutletsService,
    @Inject(forwardRef(() => SaleRepOutletRelationService))
    private saleRepOutletRelationModel: SaleRepOutletRelationService,
    @Inject(forwardRef(() => UsersService))
    private userModel: UsersService,
    @Inject(forwardRef(() => UserAdminsService))
    private userAdminModel: UserAdminsService,
    @Inject(forwardRef(() => UserDetailService))
    private userDetailService: UserDetailService,
    @Inject(forwardRef(() => DistributorUserRelationService))
    private readonly distributorUserRelationModel: DistributorUserRelationService,
    private readonly businessPartnersService: BusinessPartnersService,
    private readonly businessPartnersOutletService: BusinessPartnerOutletService,
    private readonly businessPartnersDistributorService: BusinessPartnerDistributorService,
    private readonly businessPartnersContactService: BusinessPartnersContactService,
    private readonly businessPartnerRelationService: BusinessPartnerRelationService,
    @Inject(forwardRef(() => SolaceService))
    private readonly solaceService: SolaceService,
    @Inject(forwardRef(() => ConfigService))
    private readonly configService: ConfigService,
    private readonly businessPartnerSearchService: BusinessPartnerSearchService,
    @InjectRepository(QueueLog)
    private readonly queueLogRepository: Repository<QueueLog>,
  ) {}

  @OnEvent('distributor.synced')
  async handleDistributorSync(payload: { distributor: BusinessPartner }) {
    try {
      await this.syncDistributor(payload.distributor);
      printLog(`Successfully synced distributor: ${payload.distributor.businessPartnerKey}`);
    } catch (error) {
      printLog(`Failed to sync distributor: ${payload.distributor.businessPartnerKey} - ${error.message}`);
    }
  }

  @OnEvent('depot.synced')
  async handleDepotSync(payload: { depot: BusinessPartner; distributorPartnerKey: string }) {
    try {
      await this.syncDepot(payload.depot, payload.distributorPartnerKey);
      printLog(`Successfully synced depot: ${payload.depot.businessPartnerKey}`);
    } catch (error) {
      printLog(`Failed to sync depot: ${payload.depot.businessPartnerKey} - ${error.message}`);
    }
  }

  @OnEvent('outlet.synced')
  async handleOutletSync(payload: { outlet: BusinessPartner; depot: BusinessPartner }) {
    try {
      await this.syncOutlet(payload.outlet, payload.depot);
      printLog(`Successfully synced outlet: ${payload.outlet.businessPartnerKey}`);
    } catch (error) {
      printLog(`Failed to sync outlet: ${payload.outlet.businessPartnerKey} - ${error.message}`);
    }
  }

  @OnEvent('contact.synced')
  async handleContactSync(payload: { contact: BusinessPartnerContact; relations?: any[] }) {
    try {
      await this.syncContact(payload.contact, payload.relations);
      printLog(`Successfully synced contact: ${payload.contact.businessPartnerContactKey}`);
    } catch (error) {
      printLog(`Failed to sync contact: ${payload.contact.businessPartnerContactKey} - ${error.message}`);
    }
  }

  @OnEvent('depot.removed')
  async handleDepotRemove(payload: { depotId: string; distributorId: string }) {
    try {
      await this.removeDepot(payload.depotId, payload.distributorId);
      printLog(`Successfully removed depot: ${payload.depotId}`);
    } catch (error) {
      printLog(`Failed to remove depot: ${payload.depotId} - ${error.message}`);
    }
  }

  @OnEvent('distributor.deleted')
  async handleDistributorDelete(payload: { distributorId: string }) {
    try {
      await this.softDeleteDistributor(payload.distributorId);
      printLog(`Successfully soft deleted distributor: ${payload.distributorId}`);
    } catch (error) {
      printLog(`Failed to soft delete distributor: ${payload.distributorId} - ${error.message}`);
    }
  }

  @OnEvent('outlet.deleted')
  async handleOutletDelete(payload: { ucc: string }) {
    try {
      await this.softDeleteOutlet(payload.ucc);
      printLog(`Successfully soft deleted outlet: ${payload.ucc}`);
    } catch (error) {
      printLog(`Failed to soft delete outlet: ${payload.ucc} - ${error.message}`);
    }
  }

  async syncDistributor(businessPartner: BusinessPartner): Promise<void> {
    const distributorData = {
      distributorId: businessPartner.businessPartnerKey.toString()?.trim(),
      distributorName: `${businessPartner.businessPartnerName1} ${businessPartner.businessPartnerName2}`,
      isActive: businessPartner.isActive,
      isDeleted: businessPartner.isDeleted,
      updatedAt: new Date(),
    };

    try {
      await this.distributorModel.findOneAndUpdateData(businessPartner.businessPartnerKey, distributorData);
    } catch (error) {
      printLog(`Failed to sync distributor: ${businessPartner.businessPartnerKey}`, error.stack);
    }

    await this.pushBusinessPartnerToQueue(businessPartner, QueueTypes.PUBLISH_BUSINESS_PARTNER);
  }

  async syncDepot(businessPartner: BusinessPartner, distributorId: string): Promise<void> {
    if (!distributorId) {
      printLog(`Stop sync depot: ${businessPartner.businessPartnerKey} - no distributor partner key provided`);
      return;
    }
    const depotData = {
      id: businessPartner.businessPartnerKey.toString()?.trim(),
      name: `${businessPartner.businessPartnerName1} ${businessPartner.businessPartnerName2}`,
      status: businessPartner.businessPartnerStatus,
    };

    try {
      await this.distributorModel.findOneAndUpdateDepotData(distributorId, businessPartner.businessPartnerKey, depotData);
    } catch (error) {
      printLog(`Failed to sync depot: ${businessPartner.businessPartnerKey}`, error.stack);
    }

    await this.pushBusinessPartnerToQueue(businessPartner, QueueTypes.PUBLISH_BUSINESS_PARTNER);
  }

  async syncOutlet(businessPartner: BusinessPartner, depot: BusinessPartner): Promise<void> {
    const outletData = {
      ucc: businessPartner.businessPartnerKey.toString()?.trim(),
      name: `${businessPartner.businessPartnerName1} ${businessPartner.businessPartnerName2}`,
      address: _.get(businessPartner, 'details[0].rawAddress', ''),
      contactName: _.get(businessPartner, 'details[0].contactName', ''),
      contactNumber: _.get(
        businessPartner.communications?.find((c) => c.communicationType === BusinessPartnerRelationCommunication.TEL),
        'communicationValue',
        '',
      ),
      customerType: _.get(businessPartner, 'customers[0].customerChannel', ''),
      channel: _.get(businessPartner, 'customers[0].customerChannelCode', ''),
      channelDescription: _.get(businessPartner, 'customers[0].customerChannel', ''),
      subChannel: _.get(businessPartner, 'customers[0].customerSalesOrganizations[0].customerSubChannelCode', ''),
      subChannelDescription: _.get(businessPartner, 'customers[0].customerSalesOrganizations[0].customerSubChannel', ''),
      section: _.get(businessPartner, 'customers[0].customerSalesOrganizations[0].salesGroup', ''),
      businessSegment: _.get(businessPartner, 'customers[0].businessSegment', ''),
      outletClass: _.get(businessPartner, 'customers[0].customerSalesOrganizations[0].outletClassification', ''),
      status: businessPartner.businessPartnerStatus,
      isActive: businessPartner.isActive,
      isDeleted: businessPartner.isDeleted,
      updatedAt: new Date(),
    };

    try {
      const depotDistributor = await this.businessPartnersDistributorService.getDistributorAndDepotDataFromDepotExternalKey(depot?.businessPartnerKey);
      await this.outletModel.findOneAndUpdateData(businessPartner.businessPartnerKey, outletData, depot.businessPartnerKey, depotDistributor?.distributor?.businessPartnerKey);
    } catch (error) {
      printLog(`Failed to sync outlet: ${businessPartner.businessPartnerKey}`, error.stack);
    }

    await this.pushBusinessPartnerToQueue(businessPartner, QueueTypes.PUBLISH_BUSINESS_PARTNER);
  }

  async removeDepot(depotId: string, distributorId: string): Promise<void> {
    try {
      await this.distributorModel.findOneAndRemoveDepotData(distributorId, depotId);
    } catch (error) {
      printLog(`Failed to remove depot: ${depotId}`, error.stack);
    }
  }

  async softDeleteDistributor(distributorId: string): Promise<void> {
    try {
      await this.distributorModel.findOneAndRemoveDistributorData(distributorId);
    } catch (error) {
      printLog(`Failed to soft delete distributor: ${distributorId}`, error.stack);
    }
  }

  async softDeleteOutlet(ucc: string): Promise<void> {
    try {
      await this.outletModel.findOneAndRemoveDistributorData(ucc);
    } catch (error) {
      printLog(`Failed to soft delete outlet: ${ucc}`, error.stack);
    }
  }

  async syncContactToUserAdmin(contact: BusinessPartnerContact, relations?: any[], i18n?: I18nContext): Promise<void> {
    try {
      const phone = _.get(
        contact.communications?.find((c) => c.communicationType === BusinessPartnerRelationCommunication.TEL),
        'communicationValue',
        '',
      );
      const email = _.get(
        contact.communications?.find((c) => c.communicationType === BusinessPartnerRelationCommunication.EMAIL),
        'communicationValue',
        '',
      );

      const userAdminData = {
        mobilePhone: phone,
        mobilePhoneCode: process.env.PHONE_COUNTRY_CODE_DEFAULT || '+60',
        email: email,
        username: contact.businessPartnerContactKey,
        firstname: contact.businessPartnerContactName1,
        lastname: contact.businessPartnerContactName2,
        contactId: contact.businessPartnerContactKey.toString()?.trim(),
        isActive: contact.isActive,
        userType: contact.businessPartnerContactPersonRole,
        updatedAt: new Date(),
        status: contact.businessPartnerContactStatus === BusinessPartnerStatus.ACTIVE ? 6 : 0,
        roleId: [contact.businessPartnerContactPersonRole],
      };

      const userAdmin = await this.userAdminModel.findOneAndUpdateData(contact.businessPartnerContactKey, userAdminData);
      const userAdminRole = (contact?.businessPartnerContactPersonRole && [contact.businessPartnerContactPersonRole?.toUpperCase()]?.map((r) => ({ roleKey: r }))) || [];
      await this.userDetailService.addOrUpdate({ userId: userAdmin._id, isUserAdmin: true, userRole: userAdminRole });

      if (
        !isEmptyObjectOrArray(relations) &&
        [ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER].includes(contact.businessPartnerContactPersonRole)
      ) {
        const businessPartnerDistributors = await this.businessPartnersService.find({
          select: ['businessPartnerKey'],
          where: {
            id: In(relations.map((r) => r.businessPartner2)),
          },
        });

        if (!isEmptyObjectOrArray(businessPartnerDistributors)) {
          const distributors = await this.distributorModel.findAll({ distributorId: { $in: businessPartnerDistributors.map((dis) => dis.businessPartnerKey) } });
          const newRelations = distributors.map((distributor) => {
            return {
              distributor: distributor._id,
              userAdmin: userAdmin._id,
              isActive: true,
              isDeleted: false,
              updatedAt: new Date(),
            };
          });
          await this.distributorUserRelationModel.findOneUserAdminRelationAndUpdateData(userAdmin, newRelations);
        }
      }

      //Check for call center should send to web link
      const isSentPasswordLink = await this.userModel.isSentPasswordLink(userAdmin._id.toString(), 'admin');
      if (!isSentPasswordLink && userAdmin.isActive) {
        await this.userAdminModel.sendCreatePasswordAdmin(
          {
            mobilePhone: userAdmin.mobilePhone,
            userAdmin: userAdmin,
          },
          i18n,
        );
      }
    } catch (error) {
      printLog(`Failed to sync contact to UserAdmin: ${contact.businessPartnerContactKey}`, error.stack);
    }
  }

  async syncContactToUser(contact: BusinessPartnerContact, relations: any[], i18n?: I18nContext): Promise<void> {
    try {
      if (isEmptyObjectOrArray(relations)) return;
      const businessPartnerOutlets = await this.businessPartnersService.find({
        select: ['businessPartnerKey'],
        where: {
          id: In(relations.map((r) => r.businessPartner2)),
        },
      });

      if (isEmptyObjectOrArray(businessPartnerOutlets)) return;

      const phone = _.get(
        contact.communications?.find((c) => c.communicationType === BusinessPartnerRelationCommunication.TEL),
        'communicationValue',
        '',
      );
      const email = _.get(
        contact.communications?.find((c) => c.communicationType === BusinessPartnerRelationCommunication.EMAIL),
        'communicationValue',
        '',
      );

      const userData = {
        mobilePhone: phone,
        mobilePhoneCode: process.env.PHONE_COUNTRY_CODE_DEFAULT || '+60',
        email: email,
        username: contact.businessPartnerContactKey,
        firstname: contact.businessPartnerContactName1,
        lastname: contact.businessPartnerContactName2,
        contactId: contact.businessPartnerContactKey.toString()?.trim(),
        isActive: contact.isActive,
        userType: contact.businessPartnerContactPersonRole,
        roleId: [contact.businessPartnerContactPersonRole],
        saleRepId: contact.businessPartnerContactKey.toString()?.trim(),
        saleRepStatus: contact.isActive ? 'ACTIVE' : 'INACTIVE',
        updatedAt: new Date(),
        status: contact.businessPartnerContactStatus === BusinessPartnerStatus.ACTIVE ? 6 : 0,
      };
      const outlets = await this.outletModel.findAll({ ucc: { $in: businessPartnerOutlets.map((dis) => dis.businessPartnerKey) } });
      if (isEmptyObjectOrArray(outlets)) {
        return;
      }
      const distributor = await this.distributorModel.findOne({ distributorId: outlets[0].distributorId });
      const user = await this.userModel.findOneAndUpdateData(contact.businessPartnerContactKey, userData, distributor);
      const userRoles = (contact?.businessPartnerContactPersonRole && [contact.businessPartnerContactPersonRole?.toUpperCase()]?.map((r) => ({ roleKey: r }))) || [];
      await this.userDetailService.addOrUpdate({ userId: user._id, isUserAdmin: false, userRole: userRoles });
      /**
       * No need to add a relation in Mongodb with call-center
       */
      if (contact.businessPartnerContactPersonRole === ConstantRoles.SALE_REP) {
        await this.saleRepOutletRelationModel.findOneUserRelationAndUpdateData(user, outlets);
      }

      //Check for call center should send to web link
      const isSentPasswordLink = await this.userModel.isSentPasswordLink(user._id.toString(), user.roleId?.indexOf(ConstantRoles.CALL_CENTER) > -1 ? 'callCenter' : 'salesRep');
      if (!isSentPasswordLink && user.isActive) {
        await this.userModel.sendCreatePasswordUser(
          {
            mobilePhone: user.mobilePhone,
            user: user,
          },
          i18n,
        );
      }
    } catch (error) {
      printLog(`Failed to sync contact to User: ${contact.businessPartnerContactKey}`, error.stack);
    }
  }

  async syncContact(contact: BusinessPartnerContact, relations: any[]): Promise<void> {
    try {
      const role = contact.businessPartnerContactPersonRole?.toUpperCase();

      if (
        role === ConstantRoles.SUPER_USER ||
        role === ConstantRoles.DISTRIBUTOR_ADMIN ||
        role === ConstantRoles.CALL_CENTER_MANAGEMENT ||
        role === ConstantRoles.AREA_SALES_REP_MANAGER
      ) {
        await this.syncContactToUserAdmin(contact, relations);
      }

      if (role === ConstantRoles.SALE_REP || role === ConstantRoles.CALL_CENTER) {
        await this.syncContactToUser(contact, relations);
      }
    } catch (error) {
      printLog(`Failed to sync contact: ${contact.businessPartnerContactKey}`, error.stack);
    }

    await this.pushBusinessPartnerContactToQueue(contact, QueueTypes.PUBLISH_BUSINESS_PARTNER_CONTACT);
  }

  private async pushBusinessPartnerToQueue(businessPartnerData: BusinessPartner, queueType: QueueTypes): Promise<void> {
    try {
      const businessPartnerType = businessPartnerData.businessPartnerType;
      const businessPartnerRelationType =
        (businessPartnerType === BusinessPartnerType.DEPOT && BusinessPartnerRelationType.DEPOT_DISTRIBUTOR) ||
        (businessPartnerType === BusinessPartnerType.OUTLET && BusinessPartnerRelationType.OUTLET_DEPOT) ||
        null;
      const [filteredRelations, businessPartner] = await Promise.all([
        this.businessPartnerRelationService.findBusinessPartnerRelationsDataAndRelationPartnerKeys(businessPartnerData.id, businessPartnerRelationType),
        this.businessPartnersService.attachBusinessPartnerRelationData(businessPartnerData),
      ]);
      // const filteredRelations = relations.filter((rel) => rel.businessPartner1 === businessPartner.id || rel.businessPartner2 === businessPartner.id);

      const edmData: BusinessPartnerEDMData = {
        businessPartner: [
          {
            businessPartnerKey: businessPartner.businessPartnerKey,
            status: businessPartner.businessPartnerStatus,
            name1: businessPartner.businessPartnerName1,
            name2: businessPartner.businessPartnerName2,
            communication: businessPartner.communications?.map((comm) => ({
              communicationType: comm.communicationType.toLowerCase(),
              communicationNumber: comm.communicationValue,
            })),
            businessPartnerGeographicalLocation: businessPartner.geoGraphicalLocations?.map((loc) => ({
              geographicalLocationRole: loc.geographicalLocationRole,
              validFromDate: loc.validFromDate,
              validToDate: loc.validToDate,
              geographicalLocation: {
                street: loc.street,
                houseNumber: loc.houseNumber,
                postalCode: loc.postalCode,
                city: loc.city,
                regionKey: loc.regionKey,
                countryKey: loc.countryKey,
                longitude: loc.longitude,
                latitude: loc.latitude,
              },
            })),
            businessPartnerRelation: (filteredRelations || []).map((rel) => ({
              businessPartner1Key: rel.businessPartner1 === businessPartner.id ? businessPartner.businessPartnerKey : rel.businessPartner1,
              businessPartner2Key: rel.businessPartnerKey2,
              relationType: rel.businessPartnerRelationType,
              validFromDate: rel.businessPartnerRelationValidFromDate?.toISOString(),
              validToDate: rel.businessPartnerRelationValidToDate?.toISOString(),
            })),
            customer: businessPartner.customers?.[0]
              ? {
                  status: businessPartner.customers[0].customerStatus,
                  customerChannel: businessPartner.customers[0].customerChannel,
                  businessSegment: businessPartner.customers[0].businessSegment,
                  businessOrganizationalSegment: businessPartner.customers[0].businessOrganizationalSegment,
                  customerSalesOrganization: businessPartner.customers[0].customerSalesOrganizations?.map((org) => ({
                    customerSales: {
                      customerSubChannel: org.customerSubChannel,
                      outletClassification: org.outletClassification,
                      priceGroup: org.priceGroup,
                      tradingEndDate: org.tradingEndDate,
                      currency: org.currencyCode,
                      deliveringSiteKey: org.deliveringSiteKey,
                      paymentTermsKey: org.customerBilling?.customerBilling,
                      salesGroup: org.salesGroup,
                    },
                    customerBilling: {
                      paymentTermsKey: org.customerBilling?.paymentTermsKey,
                    },
                  })),
                  customerCommercialHierarchy: businessPartner.details[0]
                    ? {
                        territory: businessPartner.details[0].customerCommercialHierarchy?.territory,
                      }
                    : undefined,
                }
              : undefined,
          },
        ],
      };

      // Push to Solace queue
      const solaceQueueName = this.configService.get<string>('SOLACE_BUSINESS_PARTNER_TOPIC_NAME');
      if (solaceQueueName) {
        this.solaceService.produceMessage({
          queueName: solaceQueueName,
          data: JSON.stringify(edmData),
        });
      }
      printLog(`Successfully pushed message to queue for EDM: ${businessPartner.businessPartnerKey}`);

      //Indexing DB cache
      this.businessPartnerSearchService
        .upsertSearchIndexForBusinessPartner(
          businessPartnerData,
          (filteredRelations || [])?.map((relation) => ({
            businessPartner1Key: businessPartner.businessPartnerKey,
            businessPartner2Key: relation.businessPartnerKey2,
            relationType: relation.businessPartnerRelationType,
          })),
          businessPartner.communications,
        )
        .then();
    } catch (error) {
      printLog(`Failed to push message to queue for EDM: ${businessPartnerData.businessPartnerKey} - ${error.message}`);
    }
  }

  private async pushBusinessPartnerContactToQueue(contactData: BusinessPartnerContact, queueType: QueueTypes): Promise<void> {
    try {
      const [contactRelations, contact] = await Promise.all([
        this.businessPartnerRelationService.findContactAndRelationsKey(contactData.id),
        this.businessPartnersContactService.attachBusinessPartnerContactRelationData(contactData),
      ]);

      const edmData: BusinessPartnerContactEDMData = {
        businessPartnerContactPerson: contactRelations.map((cR) => {
          return {
            businessPartnerKey: cR?.businessPartnerKey2 || '',
            status: contact.businessPartnerContactStatus,
            communication: contact.communications?.map((comm) => ({
              communicationType: comm.communicationType.toLowerCase(),
              communicationNumber: comm.communicationValue,
            })),
            contactPersonRole: contact.businessPartnerContactPersonRole,
            jobTitle: contact.businessPartnerContactPersonJobTitle,
            naturalPersonKey: {
              naturalPersonKey: contact.businessPartnerContactKey,
              firstName: contact.businessPartnerContactName1,
              middleName: '',
              lastName: contact.businessPartnerContactName2,
            },
          };
        }),
      };

      // Push to Solace queue
      const solaceQueueName = this.configService.get<string>('SOLACE_BUSINESS_PARTNER_CONTACT_TOPIC_NAME');
      if (solaceQueueName) {
        this.solaceService.produceMessage({
          queueName: solaceQueueName,
          data: JSON.stringify(edmData),
        });
      }
      printLog(`Successfully pushed message to queue for EDM: ${contact.businessPartnerContactKey}`);

      //Indexing DB cache
      this.businessPartnerSearchService
        .upsertSearchIndexForBusinessPartnerContact(
          contactData,
          contactRelations?.map((relation) => ({
            businessPartner1Key: contact.businessPartnerContactKey,
            businessPartner2Key: relation.businessPartnerKey2,
            relationType: relation.businessPartnerRelationType,
          })),
          contact.communications,
        )
        .then();
    } catch (error) {
      printLog(`Failed to push message to queue for EDM: ${contactData.businessPartnerContactKey} - ${error.message}`);
    }
  }

  /**
   * Sync a single outlet from queue data
   * @param outletData - The outlet data object from the queue
   */
  async syncSingleOutletFromQueue(outletData: any): Promise<any> {
    try {
      // 1. Find depot if depotId is provided
      let depot = null;
      if (outletData.depotId) {
        depot = await this.businessPartnersService.findOne({
          where: { businessPartnerKey: outletData.depotId, businessPartnerType: BusinessPartnerType.DEPOT, isDeleted: false },
        });
      }

      // 3. Prepare communications array
      const communications = [];
      if (outletData.contactNumber) {
        communications.push({
          communicationName: outletData.name,
          communicationValue: outletData.contactNumber,
          businessPartnerKey: outletData.ucc,
          communicationType: BusinessPartnerRelationCommunication.TEL,
          // communicationDescription: '',
          communicationStatus: BusinessPartnerStatus.ACTIVE,
          businessPartnerType: BusinessPartnerType.OUTLET,
        });
      }
      if (outletData.contactEmail) {
        communications.push({
          communicationName: outletData.name,
          communicationValue: outletData.contactEmail,
          businessPartnerKey: outletData.ucc,
          communicationType: BusinessPartnerRelationCommunication.EMAIL,
          // communicationDescription: '',
          communicationStatus: BusinessPartnerStatus.ACTIVE,
          businessPartnerType: BusinessPartnerType.OUTLET,
        });
      }

      // 4. Prepare rowOutlet DTO
      const rowOutlet: BusinessPartnerOutletDto = {
        businessPartnerDepotKey: depot?.businessPartnerKey,
        businessPartnerName1: outletData.name,
        businessPartnerName2: outletData.name,
        businessPartnerKey: outletData.ucc,
        businessPartnerDescription: outletData.channelDescription || '',
        businessPartnerType: BusinessPartnerType.OUTLET,
        businessPartnerStatus: BusinessPartnerStatus.ACTIVE,
        // operatingHours: [],
        geoGraphicalLocations: [
          {
            street: outletData.street,
            houseNumber: outletData.houseNumber,
            region: outletData.region,
            postalCode: outletData.postalCode,
            city: outletData.city,
            longitude: outletData.longitude,
            latitude: outletData.latitude,
            countryKey: outletData.countryIsoCode,
            businessPartnerType: BusinessPartnerType.OUTLET,
          },
        ],
        customers: [
          {
            businessPartnerKey: outletData.ucc,
            businessPartnerType: BusinessPartnerType.OUTLET,
            customerSalesOrganizations: [
              {
                deliveringSiteKey: outletData.deliveringSiteKey,
                customerSubChannel: outletData?.subChannel,
                customerSubChannelCode: outletData?.subChannel,
                tradingEndDate: outletData.tradingEndDate,
                currencyCode: outletData.currencyKey,
                customerBilling: {
                  paymentTermsKey: outletData.paymentTerm,
                },
                salesGroup: outletData.saleArea,
                outletClassification: outletData?.outletClass,
              },
            ],
            customerChannel: outletData?.channel,
            customerChannelCode: outletData?.channel,
            businessSegment: outletData?.businessSegment,
            businessSegmentCode: outletData?.businessSegment,
            businessOrganizationalSegment: outletData?.businessOrganizationalSegment,
            customerType: BusinessPartnerType.OUTLET,
          },
        ],
        details: [
          {
            // source: '',
            // taxNumber: '',
            businessPartnerType: BusinessPartnerType.OUTLET,
            // timezone: '',
            rawAddress: outletData.address,
            customerCommercialHierarchy: {
              territory: outletData.userGroups,
            },
          },
        ],
        communications,
        images: [],
      };
      rowOutlet.businessPartnerDepotKey = outletData?.depotId;
      rowOutlet.depotRelationStartDate = new Date('2025-01-10');
      rowOutlet.depotRelationEndDate = new Date('9999-12-31');

      // 6. Check if outlet exists
      let checkExisted: any = await this.businessPartnersService.findOne({
        where: { businessPartnerKey: outletData.ucc, isDeleted: false, businessPartnerType: BusinessPartnerType.OUTLET },
      });

      if (checkExisted) {
        checkExisted = await this.businessPartnersOutletService.updateOutlet(checkExisted, rowOutlet, false);
      } else {
        checkExisted = await this.businessPartnersOutletService.createOutlet(rowOutlet, true, false);
      }
      return checkExisted;
    } catch (error) {
      // Log error and rethrow or handle as needed
      printLog('syncSingleOutletFromQueue error:', error);
    }
  }

  /**
   *
   saleRepId: string; //ExternalID
   firstName: string;
   middleName: string;
   lastName: string;
   username: string;
   mobilePhone: string;
   mobilePhoneCode: string;
   status: SalesRepStatus;
   outletId: string;//ExternalID
   email: string;
   roleId: string;
   depotId: string;//ExternalID
   * @returns
   * @param contactData
   */
  async syncSingleContactFromQueue(contactData: any): Promise<void> {
    try {
      // 1. Find outlet if outletId is provided
      let outlet = null;
      if (contactData.outletId) {
        outlet = await this.businessPartnersService.findOne({
          where: { businessPartnerKey: contactData.outletId, businessPartnerType: BusinessPartnerType.OUTLET, isDeleted: false },
        });
        printLog(`Outlet not found: ${contactData.outletId}`);
        if (!outlet) return;
      }
      // 2. Prepare communications array
      const communications = [];
      if (contactData.mobilePhone) {
        communications.push({
          communicationName: null,
          communicationValue: contactData.mobilePhone,
          businessPartnerKey: contactData.saleRepId,
          communicationType: BusinessPartnerRelationCommunication.TEL,
          // communicationDescription: '',
          communicationStatus: BusinessPartnerStatus.ACTIVE,
          businessPartnerType: BusinessPartnerType.CONTACT,
        });
      }
      if (contactData.email) {
        communications.push({
          communicationName: null,
          communicationValue: contactData.email,
          businessPartnerKey: contactData.saleRepId,
          communicationType: BusinessPartnerRelationCommunication.EMAIL,
          // communicationDescription: '',
          communicationStatus: BusinessPartnerStatus.ACTIVE,
          businessPartnerType: BusinessPartnerType.CONTACT,
        });
      }

      // 3. Prepare rowContact DTO
      const rowContact: BusinessPartnerContactDto = {
        businessPartnerContactName1: contactData.firstName,
        businessPartnerContactName2: contactData.lastName,
        businessPartnerContactKey: contactData.saleRepId,
        businessPartnerContactType: BusinessPartnerType.CONTACT,
        businessPartnerContactStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerContactPersonJobTitle: contactData.roleId,
        businessPartnerContactPersonRole: !isEmptyObjectOrArray(contactData.roleId) ? contactData.roleId[0] : null,
        // businessPartnerContactDescription: '',
        // operatingHours: [],
        geoGraphicalLocations: [
          {
            businessPartnerType: BusinessPartnerType.CONTACT,
            locationStatus: BusinessPartnerStatus.ACTIVE,
            city: contactData.city,
            street: contactData.street,
            houseNumber: contactData.houseNumber,
            postalCode: contactData.postalCode,
            countryKey: contactData.countryIsoCode,
            regionKey: contactData.regionIsoCode,
            region: contactData.region,
          },
        ],
        details: [
          {
            businessPartnerType: BusinessPartnerType.DEPOT,
            birthday: contactData.birthday ? String(contactData.birthday) : '',
            customerCommercialHierarchy: {
              territory: '',
            },
            rawAddress: contactData.addressLine,
          },
        ],
        communications,
        images: [],
      };

      rowContact.outletPartnerKeys = [contactData.outletId];
      // 4. Check if contact exists
      let checkExisted: any = await this.businessPartnersContactService.findOne({
        where: { businessPartnerContactKey: contactData.id, isDeleted: false },
      });
      // 5. Create or update contact
      if (checkExisted) {
        checkExisted = await this.businessPartnersContactService.updateContact(checkExisted, rowContact, false);
      } else {
        checkExisted = await this.businessPartnersContactService.createContact(rowContact, null, false, false);
      }
      return checkExisted;
    } catch (error) {
      printLog('syncSingleContactFromQueue error:', error);
    }
  }

  /**
   *
   */
  async syncDistributorsAndDepotsToPostgres(): Promise<any> {
    try {
      printLog('Starting Distributors and Depots synchronization from MongoDB to PostgreSQL');
      const result = {
        distributors: { processed: 0, total: 0, errors: 0 },
        depots: { processed: 0, total: 0, errors: 0 },
        relations: { processed: 0, total: 0, errors: 0 },
      };

      const BATCH_SIZE = 100;
      let skip = 0;
      let hasMoreDistributors = true;
      let totalDistributors = 0;

      totalDistributors = await this.distributorModel._model.countDocuments({});
      result.distributors.total = totalDistributors;
      printLog(`Found ${totalDistributors} distributors to sync`);

      const distributorMapping = new Map<string, string>(); // MongoDB ID -> PostgreSQL ID
      const depotMapping = new Map<string, string>(); // MongoDB ID -> PostgreSQL ID

      while (hasMoreDistributors) {
        const distributors = await this.distributorModel._model.find({}).sort({ _id: 1 }).skip(skip).limit(BATCH_SIZE);

        if (distributors.length === 0) {
          hasMoreDistributors = false;
          break;
        }

        printLog(`Processing batch of ${distributors.length} distributors (${skip} - ${skip + distributors.length} of ${totalDistributors})`);

        for (const distributor of distributors) {
          try {
            if (!distributor.distributorId || !distributor.distributorName) continue;

            const distributorData = {
              businessPartnerKey: distributor.distributorId.toString().trim(),
              businessPartnerName1: distributor.distributorName,
              businessPartnerName2: '',
              businessPartnerType: BusinessPartnerType.DISTRIBUTOR,
              businessPartnerStatus: BusinessPartnerStatus.ACTIVE,
              isActive: true,
              isDeleted: false,
            };

            const existingDistributor = await this.businessPartnersService.findByExternalId(distributor.distributorId);
            let pgDistributor;

            if (existingDistributor) {
              pgDistributor = await this.businessPartnersDistributorService.updateDistributor(existingDistributor, distributorData);
            } else {
              pgDistributor = await this.businessPartnersDistributorService.createDistributor(distributorData);
            }

            if (pgDistributor && pgDistributor.id) {
              distributorMapping.set(distributor._id.toString(), pgDistributor.id);
            }

            result.distributors.processed++;
            if (result.distributors.processed % 10 === 0) {
              printLog(`Processed ${result.distributors.processed}/${result.distributors.total} distributors`);
            }

            const depots = distributor.depots || [];
            result.depots.total += depots.length;

            for (const depot of depots) {
              try {
                if (!depot.id || !depot.name) continue;

                const depotData = {
                  businessPartnerKey: depot.id.toString().trim(),
                  businessPartnerName1: depot.name,
                  businessPartnerName2: '',
                  businessPartnerType: BusinessPartnerType.DEPOT,
                  businessPartnerStatus: BusinessPartnerStatus.ACTIVE,
                  isActive: true,
                  isDeleted: false,
                  distributorPartnerKey: distributor.distributorId,
                };

                const existingDepot = await this.businessPartnersService.findByExternalId(depot.id);
                let pgDepot;

                if (existingDepot) {
                  pgDepot = await this.businessPartnersDistributorService.updateDepot(existingDepot, depotData);
                } else {
                  pgDepot = await this.businessPartnersDistributorService.createDepot(depotData, null, true);
                }

                if (pgDepot && pgDepot.id) {
                  depotMapping.set(depot.id.toString(), pgDepot.id);
                }

                result.depots.processed++;
                if (result.depots.processed % 50 === 0) {
                  printLog(`Processed ${result.depots.processed}/${result.depots.total} depots`);
                }
              } catch (error) {
                result.depots.errors++;
                printLog(`Error syncing depot ${depot.id}: ${error.message}`);
              }
            }
          } catch (error) {
            result.distributors.errors++;
            printLog(`Error syncing distributor ${distributor.distributorId}: ${error.message}`);
          }
        }

        skip += BATCH_SIZE;
      }

      printLog('Synchronizing depot-to-depot relations...');

      return result;
    } catch (error) {
      printLog(`Error in syncDistributorsAndDepotsToPostgres: ${error.message}`);
      throw error;
    }
  }

  async syncMongoToPostgres(): Promise<any> {
    try {
      printLog('Starting MongoDB to PostgreSQL synchronization');
      const result = {
        outlets: { processed: 0, total: 0, errors: 0 },
        salesReps: { processed: 0, total: 0, errors: 0 },
        relations: { processed: 0, total: 0, errors: 0 },
      };

      const BATCH_SIZE = 200;
      let skip = 0;
      let hasMoreOutlets = true;
      let totalOutlets = 0;

      totalOutlets = await this.outletModel._outletDocumentModel.count({});
      result.outlets.total = totalOutlets;
      printLog(`Found ${totalOutlets} outlets to sync`);

      while (hasMoreOutlets) {
        const outlets: any = await this.outletModel.findAll(
          {},
          {},
          {
            skip,
            limit: BATCH_SIZE,
            sort: { _id: 1 },
          },
        );

        if (outlets.length === 0) {
          hasMoreOutlets = false;
          break;
        }

        printLog(`Processing batch of ${outlets.length} outlets (${skip} - ${skip + outlets.length} of ${totalOutlets})`);

        for (const outlet of outlets) {
          try {
            if (!outlet.ucc || !outlet.depotId) continue;

            const outletData = {
              ucc: outlet.ucc,
              name: outlet.name,
              status: outlet.status,
              contactNumber: outlet.contactNumber,
              contactPhoneCode: outlet.contactPhoneCode,
              address: outlet.address,
              channel: outlet.channel,
              businessSegment: outlet.businessSegment,
              subChannel: outlet.subChannel,
              depotId: outlet.depotId,
              outletClassification: outlet.outletClass,
              city: outlet.city,
              postalCode: outlet.postalCode,
              region: outlet.region,
              longitude: outlet.longitude,
              latitude: outlet.latitude,
              countryIsoCode: process.env.COUNTRY_CODE || 'MM',
            };

            await this.syncSingleOutletFromQueue(outletData);

            result.outlets.processed++;
            if (result.outlets.processed % 100 === 0) {
              printLog(`Processed ${result.outlets.processed}/${result.outlets.total} outlets`);
            }
          } catch (error) {
            result.outlets.errors++;
            printLog(`Error syncing outlet ${outlet.ucc}: ${error.message}`);
          }
        }

        skip += BATCH_SIZE;
      }

      skip = 0;
      let hasMoreRelations = true;
      let totalRelations = 0;

      totalRelations = await this.saleRepOutletRelationModel._saleRepOutletRelationDocumentModel.countDocuments({ disconnected: false });
      result.relations.total = totalRelations;
      printLog(`Found ${totalRelations} relations to sync`);

      while (hasMoreRelations) {
        const relations = await this.saleRepOutletRelationModel._saleRepOutletRelationDocumentModel
          .find({
            disconnected: false,
            outlet: { $ne: null },
            saleRep: { $ne: null },
          })
          .sort({ _id: 1 })
          .populate({
            path: 'outlet',
            match: { ucc: { $exists: true, $ne: null } },
          })
          .populate({
            path: 'saleRep',
            match: { saleRepId: { $exists: true, $ne: null } },
          })
          .skip(skip)
          .limit(BATCH_SIZE);
        const filteredRelations = relations.filter((relation) => relation.outlet && relation.saleRep);

        if (filteredRelations.length === 0) {
          hasMoreRelations = false;
          break;
        }

        printLog(`Processing batch of ${relations.length} relations (${skip} - ${skip + relations.length} of ${totalRelations})`);

        for (const relation of relations) {
          try {
            const outlet = relation.outlet as any;
            const salesRep = relation.saleRep as any;

            if (!outlet?.ucc || !salesRep?.saleRepId) continue;

            const contactData = {
              id: salesRep.saleRepId,
              saleRepId: salesRep.saleRepId,
              firstName: salesRep.firstname,
              lastName: salesRep.lastname,
              username: salesRep.username,
              mobilePhone: salesRep.mobilePhone,
              mobilePhoneCode: salesRep.mobilePhoneCode,
              email: salesRep.email,
              roleId: salesRep.roleId || [ConstantRoles.SALE_REP],
              status: salesRep.status === 6 ? 'ACTIVE' : 'INACTIVE',
              outletId: outlet.ucc,
            };

            await this.syncSingleContactFromQueue(contactData);

            result.relations.processed++;
            if (result.relations.processed % 100 === 0) {
              printLog(`Processed ${result.relations.processed}/${result.relations.total} relations`);
            }
          } catch (error) {
            result.relations.errors++;
            printLog(`Error syncing relation: ${error.message}`);
          }
        }
        skip += BATCH_SIZE;
      }

      printLog('MongoDB to PostgreSQL synchronization completed');
      printLog(`Outlets: ${result.outlets.processed}/${result.outlets.total} (${result.outlets.errors} errors)`);
      printLog(`Sales Reps: ${result.salesReps.processed}/${result.salesReps.total} (${result.salesReps.errors} errors)`);
      printLog(`Relations: ${result.relations.processed}/${result.relations.total} (${result.relations.errors} errors)`);

      return result;
    } catch (error) {
      printLog(`Error in syncMongoToPostgres: ${error.message}`);
      throw error;
    }
  }
}
