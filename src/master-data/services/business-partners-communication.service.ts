import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON>tityManager, In, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartnerCommunication } from '../entities/business-partner-communication/business-partner-communication.entity';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { BusinessPartnerContact } from '../entities/business-partner-contact/business-partner-contact.entity';
import { BusinessPartnerContactRole, BusinessPartnerRelationCommunication } from '../constants/business-partner.enum';
import { isEmptyObjectOrArray } from 'src/utils';

@Injectable()
export class BusinessPartnerCommunicationService extends BaseSQLService<BusinessPartnerCommunication> {
  constructor(
    @InjectRepository(BusinessPartnerCommunication)
    private readonly _businessPartnersCommunicationRepository: Repository<BusinessPartnerCommunication>,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {
    super();
    this._repository = this._businessPartnersCommunicationRepository;
  }

  async update(id: string, updateData: BusinessPartnerCommunication) {
    const existedBusinessPartnerCommunication = await this._repository.findOne({ where: { id } });
    if (!existedBusinessPartnerCommunication) {
      throw new BadRequestException('partner_communication.not_found');
    }
    return this._repository.update(id, { ...existedBusinessPartnerCommunication, ...updateData });
  }

  async createCommunicationsForBusinessPartner(businessPartner: BusinessPartner, communications: BusinessPartnerCommunication[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const communicationRequests = communications
      .filter((c) => !!c?.communicationValue)
      .map(async (communication) => {
        communication.businessPartnerType = businessPartner.businessPartnerType;
        communication.businessPartner = businessPartner.id;
        communication.businessPartnerKey = businessPartner.businessPartnerKey;

        const newCommunicationEntity = this._repository.create(communication);
        const promise = () => (entityManager ? entityManager.save(BusinessPartnerCommunication, newCommunicationEntity) : this._repository.save(newCommunicationEntity));
        return promise().catch((error) => {
          console.log(`Error creating communication for business partner ${businessPartner.businessPartnerType} - ${businessPartner.id}`, error?.message);
          throw error;
        });
      });

    return Promise.all(communicationRequests);
  }

  async createCommunicationsForBusinessPartnerContact(businessPartner: BusinessPartnerContact, communications: BusinessPartnerCommunication[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerContactType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const communicationRequests = communications
      .filter((c) => !!c?.communicationValue)
      .map(async (communication) => {
        communication.businessPartnerType = businessPartner.businessPartnerContactType;
        communication.businessPartner = businessPartner.id;
        communication.businessPartnerKey = businessPartner.businessPartnerContactKey;
        const newCommunicationEntity = this._repository.create(communication);
        const promise = () => (entityManager ? entityManager.save(BusinessPartnerCommunication, newCommunicationEntity) : this._repository.save(newCommunicationEntity));
        return promise().catch((error) => {
          console.log(`Error creating communication for business partner ${businessPartner.businessPartnerContactType} - ${businessPartner.id}`, error?.message);
          throw error;
        });
      });

    return Promise.all(communicationRequests);
  }

  async removeBusinessPartnerCommunications(businessPartnerId: string, ignoreTypes?: BusinessPartnerRelationCommunication[]) {
    if (!businessPartnerId) {
      return [];
    }

    const communications = await this._repository.find({
      where: {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
    });

    if (isEmptyObjectOrArray(ignoreTypes)) {
      return this._repository.save(communications.map((communication) => ({ ...communication, isDeleted: true, isActive: false })));
    }

    const { notDeleleCommunications, deleteCommunications } = communications.reduce(
      (acc, communication) => {
        if (ignoreTypes.includes(communication?.communicationType as BusinessPartnerRelationCommunication)) {
          acc.notDeleleCommunications.push(communication);
        } else {
          acc.deleteCommunications.push(communication);
        }
        return acc;
      },
      { notDeleleCommunications: [], deleteCommunications: [] },
    );

    const updatedCommunications = await this._repository.save(deleteCommunications.map((communication) => ({ ...communication, isDeleted: true, isActive: false })));
    return [...notDeleleCommunications, ...updatedCommunications];
  }

  async replaceBusinessPartnerCommunications(businessPartner: BusinessPartner, newCommunications: BusinessPartnerCommunication[], entityManager: EntityManager) {
    await this.removeBusinessPartnerCommunications(businessPartner.id);
    return this.createCommunicationsForBusinessPartner(businessPartner, newCommunications, entityManager);
  }

  getUniqueCommunicationContactType(type: BusinessPartnerContactRole): BusinessPartnerRelationCommunication[] {
    if (type === BusinessPartnerContactRole.CALL_CENTER || type === BusinessPartnerContactRole.SALE_REP) {
      return [BusinessPartnerRelationCommunication.TEL];
    }

    if (
      type === BusinessPartnerContactRole.CALL_CENTER_MANAGEMENT ||
      type === BusinessPartnerContactRole.DISTRIBUTOR_ADMIN ||
      type === BusinessPartnerContactRole.AREA_SALES_REP_MANAGER
    ) {
      return [BusinessPartnerRelationCommunication.EMAIL];
    }

    return [];
  }

  async replaceBusinessPartnerContactCommunications(businessPartner: BusinessPartnerContact, newCommunications: BusinessPartnerCommunication[], entityManager: EntityManager) {
    // Not allow update email if contact role is [CALL_CENTER_MANAGEMENT , DISTRIBUTOR_ADMIN]
    // Not allow update phone-tel if contact role is [CALL_CENTER , SALE_REP]
    const unaffectedCommunicationTypes = this.getUniqueCommunicationContactType(businessPartner?.businessPartnerContactPersonRole);
    const oldBusinessPartnerCommunications = await this.removeBusinessPartnerCommunications(businessPartner.id, unaffectedCommunicationTypes);
    const notDeletedBusinessPartnerCommunications = oldBusinessPartnerCommunications.filter((oBPC) => !oBPC?.isDeleted);
    const notDeletedBusinessPartnerCommunicationsTypes = notDeletedBusinessPartnerCommunications.map((c) => c.communicationType);
    const allowedNewCommunications = newCommunications.filter(
      (newCommunication) => !notDeletedBusinessPartnerCommunicationsTypes.includes(newCommunication?.communicationType as BusinessPartnerRelationCommunication),
    );
    const newContactCommunications = await this.createCommunicationsForBusinessPartnerContact(businessPartner, allowedNewCommunications, entityManager);
    return [...notDeletedBusinessPartnerCommunications, ...newContactCommunications];
  }

  async findByBusinessPartner(businessPartnerId: string) {
    return this._repository.find({
      where: {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
    });
  }

  async findByBusinessPartners(businessPartnerIds: string[], entityManager?: EntityManager, additionalConditions?: Object) {
    const searchCondition = {
      ...additionalConditions,
      businessPartner: In(businessPartnerIds),
      isDeleted: false,
    };
    if (entityManager) {
      return entityManager.find(BusinessPartnerCommunication, { where: searchCondition });
    }
    return this._repository.find({
      where: searchCondition,
    });
  }

  async findByBusinessPartnerIds(businessPartnerIds: string[]) {
    return this._repository.find({
      where: {
        businessPartner: In(businessPartnerIds),
        isDeleted: false,
      },
    });
  }
}
