import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { Currencies } from '../entities/config/currencies.entity';


@Injectable()
export class CurrenciesService extends BaseSQLService<Currencies> {
  constructor(
    @InjectRepository(Currencies)
    private readonly _countryRepository: Repository<Currencies>,
  ) {
    super();
    this._repository = this._countryRepository;
  }
}
