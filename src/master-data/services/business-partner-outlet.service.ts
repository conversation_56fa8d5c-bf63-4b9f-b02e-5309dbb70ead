import { BadRequestException, forwardRef, HttpException, HttpStatus, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { AuthService } from '../../auth/auth.service';
import { I18nContext } from 'nestjs-i18n';
import { checkImportDataRequiredFields, excelNumberToDate, excelNumberToString, formatDate, isEmptyObjectOrArray, isValidDate } from 'src/utils';
import * as _ from 'lodash';

import { BusinessPartnersService } from './business-partners.service';
import { BusinessPartnersGeoLocationService } from './business-partners-geo-location.service';
import { BusinessPartnerDetailService } from './business-partners-detail.service';
import { BusinessPartnersImageService } from './business-partners-image.service';
import { BusinessPartnerCommunicationService } from './business-partners-communication.service';
import { runTransaction } from '../../utils/helpers/database';
import { EntityManager, ILike, In } from 'typeorm';
import { BusinessPartnerOperatingHourService } from './business-partners-operating-hour.service';
import { ContactType, DepotType, OutletMappingsType, OutletType, SegmentType } from '../constants/outlet.type';
import {
  BusinessPartnerContactRole,
  BusinessPartnerRelationCommunication,
  BusinessPartnerRelationDataTypes,
  BusinessPartnerRelationType,
  BusinessPartnerStatus,
  BusinessPartnerType,
  DayOfWeekShort,
} from '../constants/business-partner.enum';
import { BusinessPartnerContactDto } from '../dtos/business-partner-contact.dto';
import { BusinessPartnerRelationService } from './business-partners-relation.service';
import { BusinessPartnersContactService } from './business-partners-contact.service';
import { BusinessPartnerOutletDto } from '../dtos/business-partner-outlet.dto';
import { FilesService } from 'src/files/services';
import { OutletExportMapping, OutletMapping } from '../constants/outlet.mapping';
import { BusinessPartnersCustomerService } from './business-partners-customer.service';
import { OutletSearchDto } from '../dtos/outlet-search.dto';

@Injectable()
export class BusinessPartnerOutletService {
  constructor(
    @Inject()
    readonly _businessPartnerService: BusinessPartnersService,
    @Inject()
    readonly _businessPartnersGeoLocationService: BusinessPartnersGeoLocationService,
    @Inject()
    readonly _businessPartnerDetailService: BusinessPartnerDetailService,
    @Inject()
    readonly _businessPartnersImageService: BusinessPartnersImageService,
    @Inject()
    readonly _businessPartnerCommunicationService: BusinessPartnerCommunicationService,
    @Inject()
    readonly _businessPartnerOperatingHourService: BusinessPartnerOperatingHourService,
    @Inject()
    readonly _businessPartnerContactService: BusinessPartnersContactService,
    @Inject()
    readonly _businessPartnerRelationService: BusinessPartnerRelationService,
    @Inject()
    readonly _businessPartnerCustomerService: BusinessPartnersCustomerService,
    @Inject()
    private readonly eventEmitter: EventEmitter2,

    @Inject(forwardRef(() => FilesService))
    readonly fileService: FilesService,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {}

  async createOutlet(dto: BusinessPartnerOutletDto, needDepotRelation?: boolean, eventSync = true) {
    let depot: BusinessPartner;
    if (needDepotRelation) {
      if (!dto?.businessPartnerDepotKey) {
        throw new BadRequestException('depot.not_existed');
      }
    }

    if (dto?.businessPartnerDepotKey) {
      depot = await this._businessPartnerService.findByExternalId(dto?.businessPartnerDepotKey, { businessPartnerType: BusinessPartnerType.DEPOT });
      if (isEmptyObjectOrArray(depot)) {
        throw new BadRequestException('depot.not_existed');
      }
    }

    if (dto?.businessPartnerKey) {
      const existedEntity = await this._businessPartnerService.findOne({ where: { businessPartnerKey: dto?.businessPartnerKey } });
      if (existedEntity) {
        if (!existedEntity?.isDeleted) {
          throw new BadRequestException('outlet.existed_id');
        }
        return await this.updateOutlet(existedEntity, { ...dto, isDeleted: false, isActive: true });
      }
    }

    const newBusinessPartnerEntity = this._businessPartnerService.mapBusinessPartnerDataFromDtoToEntity(null, dto);

    // TRANSACTIONAL
    const [newBusinessPartner] = await runTransaction([
      async (entityManage: EntityManager, context) => {
        const newBusinessPartner = await entityManage.save(BusinessPartner, newBusinessPartnerEntity);
        context.businessPartner = newBusinessPartner;
        return newBusinessPartner;
      },

      async (entityManage: EntityManager, context) => {
        const businessPartner = context.businessPartner;
        return this._businessPartnerService.createBusinessPartnerRelatedData(entityManage, businessPartner, dto);
      },

      async (entityManage: EntityManager, context) => {
        if (isEmptyObjectOrArray(depot)) {
          return null;
        }
        const businessPartner = context.businessPartner;
        return this._businessPartnerRelationService.createRelation(
          {
            businessPartner1: businessPartner.id,
            businessPartner2: depot.id,
            businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT,
            businessPartnerRelationStatus: BusinessPartnerStatus.ACTIVE,
            businessPartnerRelationValidFromDate: dto?.depotRelationStartDate,
            businessPartnerRelationValidToDate: dto?.depotRelationEndDate,
          },
          entityManage,
        );
      },
    ]);

    const result = await this._businessPartnerService.attachBusinessPartnerRelationData(newBusinessPartner);
    if (eventSync) {
      this.eventEmitter.emit('outlet.synced', { outlet: result, depot });
    }
    return result;
  }

  async findById(id: string, i18n: I18nContext) {
    return await this._businessPartnerService.findById(id);
  }

  async findByIdWithAllRelations(id: string, i18n: I18nContext) {
    return await this._businessPartnerService.findByIdWithAllRelations(id);
  }

  async updateOutlet(outlet: BusinessPartner, updateData: any, eventSync = true) {
    const { id, ...outletUpdateData } = updateData;
    const updateOutletData = this._businessPartnerService.mapBusinessPartnerDataFromDtoToEntity(outlet, outletUpdateData);

    // TRANSACTIONAL
    const [_, response, relation] = await runTransaction([
      async (entityManager: EntityManager, context) => {
        // If update existed record, dont pass relation entity into update value
        const { details, customers, communications, geoGraphicalLocations, images, operatingHours, ...updateOutlet } = updateOutletData;
        const updatedBusinessPartner = await entityManager.save(BusinessPartner, updateOutlet);
        context.businessPartner = updatedBusinessPartner;
        return updatedBusinessPartner;
      },
      async (entityManager: EntityManager, context) => {
        const businessPartner = context.businessPartner;
        return this._businessPartnerService.updateBusinessPartnerRelatedData(entityManager, businessPartner, updateOutletData);
      },

      async (entityManager: EntityManager, context) => {
        return this.updateOutletDepotOldRelation(outlet, updateData, entityManager);
      },
    ]);

    // Get depot info for sync
    const depotRelation = await this._businessPartnerRelationService.findOne({
      where: {
        businessPartner1: outlet.id,
        businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT,
        isDeleted: false,
      },
    });

    let depot = null;
    if (depotRelation) {
      depot = await this._businessPartnerService.findOne({
        where: {
          id: depotRelation.businessPartner2,
          isDeleted: false,
        },
      });
    }

    if (eventSync) {
      this.eventEmitter.emit('outlet.synced', { outlet: response, depot });
    }
    return response;
  }

  async updateOutletDepotOldRelation(outletData: BusinessPartner, updatedData: BusinessPartnerOutletDto, entityManager: EntityManager) {
    if (!updatedData?.businessPartnerDepotKey) {
      return null;
    }

    const depot = await this._businessPartnerService.findByExternalId(updatedData?.businessPartnerDepotKey, { businessPartnerType: BusinessPartnerType.DEPOT });
    if (isEmptyObjectOrArray(depot)) {
      return null;
    }

    const outletDepotRelations = await this._businessPartnerRelationService.findRelationsWithEntityManager(
      {
        businessPartner1: outletData.id,
        businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT,
        isDeleted: false,
      },
      entityManager,
    );

    if (isEmptyObjectOrArray(outletDepotRelations)) {
      return this._businessPartnerRelationService.createRelation(
        {
          businessPartner1: outletData.id,
          businessPartner2: depot.id,
          businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT,
          businessPartnerRelationStatus: BusinessPartnerStatus.ACTIVE,
          businessPartnerRelationValidFromDate: updatedData?.depotRelationStartDate,
          businessPartnerRelationValidToDate: updatedData?.depotRelationEndDate,
        },
        entityManager,
      );
    }

    // const outletDepotRelationId = outletDepotRelations[0]?.businessPartner2;

    // if (depot?.id === outletDepotRelationId) {
    //   return outletDepotRelations[0];
    // }

    return this._businessPartnerRelationService.updateRelationsByCondition(
      { id: outletDepotRelations[0]?.id },
      {
        businessPartner1: outletData.id,
        businessPartner2: depot?.id,
        businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT,
        businessPartnerRelationStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerRelationValidFromDate: updatedData?.depotRelationStartDate,
        businessPartnerRelationValidToDate: updatedData?.depotRelationEndDate,
      },
      entityManager,
    );
  }

  async checkExistAndUpdateOutlet(id: string, updateData: any, i18n: I18nContext) {
    const outlet = await this._businessPartnerService.findOne({ where: { id } });
    if (!outlet) {
      throw new NotFoundException(await i18n.t('outlet.not_found'));
    }

    return this.updateOutlet(outlet, updateData);
  }

  async findOutlets(filter: any) {
    return await this._businessPartnerService.find({ where: filter });
  }

  async checkExistAndDeleteOutlet(id: string, i18n: I18nContext) {
    const deactivatedOutlet = await this.checkExistAndUpdateOutlet(id, { isDeleted: true, isActive: false }, i18n);

    // Get depot info before deleting
    const depotRelation = await this._businessPartnerRelationService.findOne({
      where: {
        businessPartner1: id,
        businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT,
        isDeleted: false,
      },
    });

    let depot = null;
    if (depotRelation) {
      depot = await this._businessPartnerService.findOne({
        where: {
          id: depotRelation.businessPartner2,
          isDeleted: false,
        },
      });
    }

    // Deactivate related data
    await this._businessPartnerService.deactivateRelatedBusinessPartnerData(deactivatedOutlet);

    // Emit event for sync
    this.eventEmitter.emit('outlet.synced', { deactivatedOutlet, depot });

    return deactivatedOutlet;
  }

  async importOutlet(outlet: OutletType | any, segment: SegmentType, depot: BusinessPartner, depotRelationData: DepotType, i18n: I18nContext) {
    /**
     *   businessPartnerKey: string;
     *   businessPartnerName1: string;
     *   businessPartnerName2: string;
     *   thirdPartyCustomerKey: string;
     *   communicationName: string;
     *   communicationNumber: string;
     *   communicationEmail: string;
     *   businessPartnerType: string;
     *   businessPartnerDescription: string;
     *   currencyKey: string;
     *   saleArea: string;
     *   saleSection: string;
     *   saleSector: string;
     *   operatingHours: string;
     *   paymentTerm: string;
     *   tradingEndDate: string;
     *   locationType: string;
     *   deliveringSiteKey: string;
     *   addressLine: string;
     *   street: string;
     *   houseNumber: string;
     *   region: string;
     *   userGroups: string;
     *   postalCode: string;
     *   city: string;
     *   longitude: string;
     *   latitude: string;
     *   countryIsoCode: string;
     *   status: string;
     */

    const communications = [];
    if (outlet.communicationNumber) {
      communications.push({
        communicationName: outlet.communicationName,
        communicationValue: outlet.communicationNumber,
        businessPartnerKey: outlet.businessPartnerKey,
        communicationType: BusinessPartnerRelationCommunication.TEL,
        communicationDescription: '',
        communicationStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerType: BusinessPartnerType.OUTLET,
      });
    }

    if (outlet.communicationEmail) {
      communications.push({
        communicationName: outlet.communicationName,
        communicationValue: outlet.communicationEmail,
        businessPartnerKey: outlet.businessPartnerKey,
        communicationType: BusinessPartnerRelationCommunication.EMAIL,
        communicationDescription: '',
        communicationStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerType: BusinessPartnerType.OUTLET,
      });
    }

    const rowOutlet: BusinessPartnerOutletDto = {
      businessPartnerDepotKey: depot?.businessPartnerKey,
      businessPartnerName1: outlet.businessPartnerName1,
      businessPartnerName2: outlet.businessPartnerName2,
      businessPartnerKey: outlet.businessPartnerKey,
      businessPartnerDescription: outlet?.businessPartnerDescription,
      businessPartnerType: BusinessPartnerType.OUTLET,
      businessPartnerStatus: BusinessPartnerStatus.ACTIVE,
      operatingHours: (outlet?.operatingHours || '')
        .split('|')
        .map((entry) => {
          const [day, isOpen, openTime, closeTime] = entry.split('-');
          const validDay = Object.values(DayOfWeekShort).find((d) => d === day?.trim());
          if (!validDay) {
            return null;
          }
          return {
            businessPartnerType: BusinessPartnerType.DEPOT,
            day: validDay,
            openTime: isOpen === 'true' ? openTime : '00:01',
            closeTime: isOpen === 'true' ? closeTime : '23:59',
            isClosed: isOpen === 'false',
          };
        })
        ?.filter((operatingHour) => !!operatingHour),
      geoGraphicalLocations: [
        {
          street: outlet.street,
          houseNumber: outlet.houseNumber,
          region: outlet.region,
          postalCode: outlet.postalCode,
          city: outlet.city,
          longitude: outlet.longitude,
          latitude: outlet.latitude,
          countryKey: outlet.countryIsoCode,
          businessPartnerType: BusinessPartnerType.OUTLET,
        },
      ],
      customers: [
        {
          businessPartnerKey: outlet.businessPartnerKey,
          businessPartnerType: BusinessPartnerType.OUTLET,
          customerSalesOrganizations: [
            {
              deliveringSiteKey: outlet?.deliveringSiteKey,
              customerSubChannel: segment?.customerSubChannel,
              customerSubChannelCode: segment?.customerSubChannelCode,
              tradingEndDate: outlet?.tradingEndDate,
              currencyCode: outlet?.currencyKey,
              customerBilling: {
                paymentTermsKey: outlet?.paymentTerm,
              },
              salesGroup: outlet?.saleArea,
              outletClassification: segment?.outletClassification,
            },
          ],
          customerChannel: segment?.customerChannel,
          customerChannelCode: segment?.customerChannelCode,
          businessSegment: segment?.businessSegment,
          businessSegmentCode: segment?.businessSegmentCode,
          businessOrganizationalSegment: segment?.businessOrganizationalSegment,
          customerType: outlet?.businessPartnerType,
        },
      ],
      details: [
        {
          source: '',
          taxNumber: '',
          businessPartnerType: BusinessPartnerType.OUTLET,
          timezone: '',
          rawAddress: outlet.addressLine,
          customerCommercialHierarchy: {
            territory: outlet.userGroups,
          },
        },
      ],
      communications,
      images: [],
    };

    if (depot) {
      rowOutlet.businessPartnerDepotKey = depot?.businessPartnerKey;
    }

    if (depotRelationData?.startDate) {
      rowOutlet.depotRelationStartDate = excelNumberToDate(depotRelationData.startDate);
    }

    if (depotRelationData?.endDate) {
      rowOutlet.depotRelationEndDate = excelNumberToDate(depotRelationData.endDate);
    }

    let checkExisted: any = await this._businessPartnerService.findOne({
      where: { businessPartnerKey: outlet.businessPartnerKey, isDeleted: false, businessPartnerType: BusinessPartnerType.OUTLET },
    });

    if (checkExisted) {
      checkExisted = await this.updateOutlet(checkExisted, rowOutlet);
    } else {
      checkExisted = await this.createOutlet(rowOutlet);
    }
    return checkExisted;
  }

  async importContact(contact: ContactType, outletKeys: string[], i18n: I18nContext) {
    /**
     *   businessPartnerContactKey: string;
     *   businessPartnerContactName1: string;
     *   businessPartnerContactName2: string;
     *   middleName: string;
     *   businessPartnerKey: string;
     *   businessPartnerContactPersonRole: string;
     *   businessPartnerContactStatus: string;
     *   communicationNumberTel: string;
     *   communicationNumberTelHome: string;
     *   communicationEmail: string;
     *   addressLine: string;
     *   street: string;
     *   houseNumber: string;
     *   postalCode: string;
     *   city: string;
     *   countryIsoCode: string;
     *   region: string;
     *   regionIsoCode: string;
     *   businessPartnerContactPersonJobTitle: string;
     *   birthday: string;
     */

    const communications = [];
    if (contact.communicationNumberTel) {
      communications.push({
        communicationName: contact.communicationName || contact.businessPartnerContactName2,
        communicationValue: contact.communicationNumberTel,
        businessPartnerKey: contact.businessPartnerKey,
        communicationType: BusinessPartnerRelationCommunication.TEL,
        communicationDescription: '',
        communicationStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerType: BusinessPartnerType.CONTACT,
      });
    }
    if (contact.communicationNumberTelHome) {
      communications.push({
        communicationName: contact.communicationName || contact.businessPartnerContactName2,
        communicationValue: contact.communicationNumberTelHome,
        businessPartnerKey: contact.businessPartnerKey,
        communicationType: BusinessPartnerRelationCommunication.PHONE,
        communicationDescription: '',
        communicationStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerType: BusinessPartnerType.CONTACT,
      });
    }
    if (contact.communicationEmail) {
      communications.push({
        communicationName: '',
        communicationValue: contact.communicationEmail,
        businessPartnerKey: contact.businessPartnerKey,
        communicationType: BusinessPartnerRelationCommunication.EMAIL,
        communicationDescription: '',
        communicationStatus: BusinessPartnerStatus.ACTIVE,
        businessPartnerType: BusinessPartnerType.CONTACT,
      });
    }

    const rowContact: BusinessPartnerContactDto = {
      businessPartnerContactName1: contact.businessPartnerContactName1,
      businessPartnerContactName2: contact.businessPartnerContactName2,
      businessPartnerContactKey: contact.businessPartnerContactKey,
      businessPartnerContactType: BusinessPartnerType.CONTACT,
      businessPartnerContactStatus: BusinessPartnerStatus.ACTIVE,
      businessPartnerContactPersonJobTitle: contact.businessPartnerContactPersonJobTitle,
      businessPartnerContactPersonRole: contact.businessPartnerContactPersonRole as BusinessPartnerContactRole,
      businessPartnerContactDescription: '',
      operatingHours: [],
      geoGraphicalLocations: [
        {
          businessPartnerType: BusinessPartnerType.CONTACT,
          locationStatus: BusinessPartnerStatus.ACTIVE,
          city: contact.city,
          street: contact.street,
          houseNumber: contact.houseNumber,
          postalCode: contact.postalCode,
          countryKey: contact.countryIsoCode,
          regionKey: contact.regionIsoCode,
          region: contact.region,
        },
      ],
      details: [
        {
          businessPartnerType: BusinessPartnerType.DEPOT,
          birthday: excelNumberToString(contact.birthday),
          customerCommercialHierarchy: {
            territory: '',
          },
          rawAddress: contact.addressLine,
        },
      ],
      communications,
      images: [],
    };

    if (outletKeys) {
      rowContact.outletPartnerKeys = outletKeys;
    }

    let checkExisted = await this._businessPartnerContactService.findOne({
      where: { businessPartnerContactKey: rowContact.businessPartnerContactKey, isDeleted: false },
    });

    if (checkExisted) {
      checkExisted = await this._businessPartnerContactService.updateContact(checkExisted, rowContact);
    } else {
      checkExisted = await this._businessPartnerContactService.createContact(rowContact, i18n);
    }

    return checkExisted;
  }

  async checkImportOutletData(outlet: OutletType, index: number, i18n: I18nContext) {
    const invalidField = checkImportDataRequiredFields<OutletType>(outlet, ['businessPartnerKey', 'businessPartnerName1', 'communicationNumber'], OutletMapping.outlets);
    if (invalidField) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataRequiredError`, {
          args: { dataType: 'outlet', row: index + 2, field: invalidField },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    const outletGeoLocationFields: (keyof OutletType)[] = ['city', 'countryIsoCode', 'houseNumber', 'postalCode', 'region', 'street', 'latitude', 'longitude'];
    if (outletGeoLocationFields.filter((field) => !!outlet[field]).length < 1) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataInvalidError`, {
          args: { dataType: 'outlet', row: index + 2, field: 'geoLocations' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    // if (outlet.communicationNumber && !isValidPhoneNumber(outlet.communicationNumber)) {
    //   throw new HttpException(
    //     await i18n.t(`importExport.masterData.importDataInvalidError`, {
    //       args: { dataType: 'outlet', row: index + 2, field: 'phone_number' },
    //     }),
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }
  }

  async checkImportDepotData(depot: DepotType, index: number, i18n: I18nContext) {
    const invalidField = checkImportDataRequiredFields<DepotType>(depot, ['businessPartnerKey', 'businessPartnerDepotKey'], OutletMapping.depots);
    if (invalidField) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataRequiredError`, {
          args: { dataType: 'depot', row: index + 2, field: invalidField },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async checkImportContactOutletData(contact: ContactType, index: number, i18n: I18nContext) {
    const invalidField = checkImportDataRequiredFields<ContactType>(
      contact,
      [
        'businessPartnerKey',
        'businessPartnerContactName1',
        'businessPartnerContactKey',
        'communicationNumberTel',
        'businessPartnerContactPersonRole',
        'communicationNumberTelHome',
      ],
      OutletMapping.contacts,
    );

    const contactRole = contact.businessPartnerContactPersonRole;

    if (
      ![BusinessPartnerContactRole.CALL_CENTER, BusinessPartnerContactRole.SALE_REP, BusinessPartnerContactRole.MARKET_DEVELOPMENT_SUPERVISOR].includes(
        contactRole as BusinessPartnerContactRole,
      )
    ) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataInvalidError`, {
          args: { fileType: 'Distributor', dataType: 'contact', row: index + 2, field: 'role' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (invalidField) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataRequiredError`, {
          args: { dataType: 'contact', row: index + 2, field: invalidField },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    const contactGeoLocationFields: (keyof ContactType)[] = ['city', 'countryIsoCode', 'houseNumber', 'postalCode', 'region', 'regionIsoCode', 'street'];
    if (contactGeoLocationFields.filter((field) => !!contact[field]).length < 1) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataInvalidError`, {
          args: { dataType: 'contact', row: index + 2, field: 'geoLocations' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (contact.birthday && !isValidDate(contact.birthday)) {
      throw new HttpException(
        await i18n.t(`importExport.masterData.importDataInvalidError`, {
          args: { dataType: 'contact', row: index + 2, field: 'date_of_birth' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    // if (contact.communicationNumberTel && !isValidPhoneNumber(contact.communicationNumberTel)) {
    //   throw new HttpException(
    //     await i18n.t(`importExport.masterData.importDataInvalidError`, {
    //       args: { dataType: 'contact', row: index + 2, field: 'phone_number' },
    //     }),
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    // if (contact.communicationNumberTelHome && !isValidPhoneNumber(contact.communicationNumberTelHome)) {
    //   throw new HttpException(
    //     await i18n.t(`importExport.masterData.importDataInvalidError`, {
    //       args: { dataType: 'contact', row: index + 2, field: 'mobile_phone' },
    //     }),
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }
  }

  async validateImportOutletData(mappingResult: OutletMappingsType, i18n: I18nContext) {
    const { outlets, depots, contacts } = mappingResult;

    const outletExternalKeys = [],
      contactExternalKeys = [];
    for (let i = 0; i < outlets.length; i++) {
      const outletData = outlets[i];
      if (outletExternalKeys.includes(String(outletData.businessPartnerKey))) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importExternalKeyDuplicated`, {
            args: { fileType: 'Outlet', dataType: 'outlet', row: i + 2 },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      outletExternalKeys.push(String(outletData.businessPartnerKey));
      await this.checkImportOutletData(outlets[i], i, i18n);
    }

    for (let i = 0; i < depots.length; i++) {
      const depotData = depots[i];
      await this.checkImportDepotData(depotData, i, i18n);
      if (!outletExternalKeys.includes(String(depotData.businessPartnerKey))) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importDataInvalidError`, {
            args: { dataType: 'depot', row: i + 2, field: 'outlet_external_id' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const contactMobilePhones = [],
      contactPhones = [],
      outletKeysSaleRep = [],
      outletKeysCallCenter = [];
    for (let i = 0; i < contacts.length; i++) {
      const contactData = contacts[i];
      if (contactExternalKeys.includes(String(contactData.businessPartnerContactKey))) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importExternalKeyDuplicated`, {
            args: { fileType: 'Outlet', dataType: 'contact', row: i + 2 },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      const outletExternalKeys = (contactData?.businessPartnerKey?.split && contactData.businessPartnerKey.split(',').map((value) => value?.trim())) || [
        contactData?.businessPartnerKey,
      ];
      const importOutletKeys = contactData.businessPartnerContactPersonRole === BusinessPartnerContactRole.SALE_REP ? outletKeysSaleRep : outletKeysCallCenter;
      const duplicateKeys = outletExternalKeys.filter((key) => importOutletKeys.includes(key));
      if (duplicateKeys.length) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importDataInvalidError`, {
            args: { fileType: 'Outlet', dataType: 'contact', row: i + 2, field: 'outlet_external_id' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      importOutletKeys.push(...outletExternalKeys);
      contactExternalKeys.push(String(contactData.businessPartnerContactKey));
      await this.checkImportContactOutletData(contactData, i, i18n);
      const contactRole = contactData.businessPartnerContactPersonRole;
      const contactMobilePhone = contactData?.communicationNumberTelHome;
      const contactPhoneNumber = contactData?.communicationNumberTel;
      const contactRoleMobilePhone = `${contactRole}-mobilePhone-${contactMobilePhone}`;
      if ((BusinessPartnerContactRole.SALE_REP === contactRole || BusinessPartnerContactRole.CALL_CENTER === contactRole) && contactMobilePhones.includes(contactMobilePhone)) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importDataInvalidError`, {
            args: { fileType: 'Outlet', dataType: 'contact', row: i + 2, field: 'mobile_phone' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      contactMobilePhones.push(contactRoleMobilePhone);
      const contactRolePhoneNumber = `${contactRole}-phoneNumber-${contactPhoneNumber}`;
      if ((BusinessPartnerContactRole.SALE_REP === contactRole || BusinessPartnerContactRole.CALL_CENTER === contactRole) && contactPhones.includes(contactRolePhoneNumber)) {
        throw new HttpException(
          await i18n.t(`importExport.masterData.importDataInvalidError`, {
            args: { fileType: 'Outlet', dataType: 'contact', row: i + 2, field: 'phone_number' },
          }),
          HttpStatus.BAD_REQUEST,
        );
      }
      contactPhones.push(contactRolePhoneNumber);
    }
  }

  async importBusinessPartnerOutletContact(mappingResult: OutletMappingsType, i18n: I18nContext) {
    if (isEmptyObjectOrArray(mappingResult)) {
      return null;
    }

    await this.validateImportOutletData(mappingResult, i18n);

    let contacts: any[] = [];
    const depots = await this._businessPartnerService.find({
      where: { businessPartnerKey: In(mappingResult.depots.map((d) => d.businessPartnerDepotKey)), isDeleted: false },
    });

    // Process outlets in batches
    const outlets: BusinessPartner[] = await this._businessPartnerService.processBatch(mappingResult.outlets, async (outlet: any) => {
      const outletDepotKey = mappingResult.depots.find((d) => d.businessPartnerKey.toString().trim() === outlet.businessPartnerKey.toString().trim());
      const segment = mappingResult.segments.find((s) => s.businessPartnerKey === outlet.businessPartnerKey);
      if (outletDepotKey) {
        const depot = depots.find((d) => d?.businessPartnerKey === outletDepotKey?.businessPartnerDepotKey);
        const depotImportData: DepotType = mappingResult.depots.find((d) => d?.businessPartnerDepotKey === depot?.businessPartnerKey);
        return this.importOutlet(outlet, segment, depot, depotImportData, i18n);
      }
      return this.importOutlet(outlet, segment, null, null, i18n);
    });

    if (!isEmptyObjectOrArray(outlets)) {
      // Process contacts in batches
      contacts = await this._businessPartnerService.processBatch(mappingResult.contacts, async (contact) => {
        const outletPartnerKeys = (contact?.businessPartnerKey?.split && contact.businessPartnerKey.split(',').map((value) => value?.trim())) || [contact?.businessPartnerKey];
        return this.importContact(contact, outletPartnerKeys, i18n);
      });
    }

    // Filter out null values from results
    return {
      depots: depots.filter(Boolean),
      outlets: outlets.filter(Boolean),
      contacts: contacts.filter(Boolean),
    };
  }

  async attachOutletsRelationData(outlets: BusinessPartner[] | any) {
    const outletIds = outlets.map((outlet) => outlet.id).filter((id) => !!id);
    if (outletIds.length === 0) {
      return outlets;
    }

    // Get all outlet-depot relations in one query
    const outletDepotRelations = await this._businessPartnerRelationService.find({
      where: {
        businessPartner1: In(outletIds),
        businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT,
        isDeleted: false,
      },
    });

    if (isEmptyObjectOrArray(outletDepotRelations)) {
      return outlets;
    }

    // Get all depots in one query
    const depotIds = outletDepotRelations.map((relation) => relation.businessPartner2);
    const depots = await this._businessPartnerService.find({
      where: {
        id: In(depotIds),
        isDeleted: false,
      },
      select: ['id', 'businessPartnerKey'],
    });

    // Map results back to outlets
    return outlets.map((outlet) => {
      const relation = outletDepotRelations.find((r) => r.businessPartner1 === outlet.id);
      if (!relation) return outlet;

      const depot = depots.find((d) => d.id === relation.businessPartner2);
      if (!depot) return outlet;

      return {
        ...outlet,
        businessPartnerDepotKey: depot.businessPartnerKey,
      };
    });
  }

  async attachOutletRelationData(outlet: BusinessPartner) {
    const outletData = {};
    if (!outlet?.id) {
      return outletData;
    }
    const outletDepotRelation = await this._businessPartnerRelationService.findOne({
      where: {
        businessPartner1: outlet.id,
        businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT,
        isDeleted: false,
      },
    });
    if (isEmptyObjectOrArray(outletDepotRelation) || !outletDepotRelation?.businessPartner2) {
      return outletData;
    }

    const depot = await this._businessPartnerService.findOne({
      where: {
        id: outletDepotRelation?.businessPartner2,
        isDeleted: false,
      },
      select: ['id', 'businessPartnerKey'],
    });

    if (isEmptyObjectOrArray(depot)) {
      return outletData;
    }

    return {
      ...outletData,
      businessPartnerDepotKey: depot?.businessPartnerKey,
    };
  }

  async attachOutletSearchData(outlet: BusinessPartner) {
    const response = await Promise.all([this.attachOutletRelationData(outlet), this._businessPartnerService.attachBusinessPartnerRelationData(outlet)]).catch((error) => {
      console.log(`Error attachOutletSearchData for outlet ${outlet?.id}`, error);
      return outlet;
    });

    if (isEmptyObjectOrArray(response)) {
      return outlet;
    }

    return {
      ...response[1],
      ...response[0],
    };
  }

  async findOutletByIdWithAllRelations(id: string, i18n: I18nContext) {
    const depot = await this._businessPartnerService.findOne({
      where: {
        businessPartnerType: BusinessPartnerType.OUTLET,
        id,
        isDeleted: false,
      },
    });
    return this.attachOutletSearchData(depot);
  }

  async searchOutlet(searchDto: OutletSearchDto | any, i18n: I18nContext) {
    const { offset, limit, orderBy, orderDesc, searchText, outletIds, depotKey } = searchDto;

    const baseConditions: any = { isDeleted: false, businessPartnerType: BusinessPartnerType.OUTLET };
    let filterConditions: any = [];

    let filterOutletIdsByDepot;
    if (depotKey) {
      filterOutletIdsByDepot = await this._businessPartnerRelationService.findOutletIdsInRelationWithDepot(depotKey);
    }

    const isSearchByDepot = Array.isArray(filterOutletIdsByDepot);
    let filteredOutletIds;
    if (Array.isArray(outletIds)) {
      filteredOutletIds = isSearchByDepot ? filterOutletIdsByDepot.filter((id) => outletIds.includes(id)) : outletIds;
    } else {
      filteredOutletIds = isSearchByDepot ? filterOutletIdsByDepot : undefined;
    }

    if (Array.isArray(filteredOutletIds)) {
      baseConditions.id = In(filteredOutletIds);
    }

    const businessPartnerIdsByCommunicationSearch = !!searchText
      ? await this._businessPartnerCommunicationService.findWithOptions({
          where: {
            isDeleted: false,
            businessPartnerType: BusinessPartnerType.OUTLET,
            communicationValue: searchText,
          },
          select: ['businessPartner'],
        })
      : [];

    if (businessPartnerIdsByCommunicationSearch && businessPartnerIdsByCommunicationSearch.length) {
      filterConditions.push({
        ...baseConditions,
        id: In(businessPartnerIdsByCommunicationSearch.map((bp) => bp.businessPartner)),
      });
    }

    if (searchText) {
      filterConditions.push({
        ...baseConditions,
        businessPartnerName1: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerName2: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerKey: ILike(`%${searchText}%`),
      });
    }

    if (!filterConditions.length) {
      filterConditions = baseConditions;
    }

    const [outlets, count] = await this._businessPartnerService.findAndCount(filterConditions, offset, limit, orderBy, orderDesc);
    // const outletWithRelatedData = await Promise.all(outlets.map((outlet) => this.attachOutletSearchData(outlet)));
    const outletWithRelatedData = await this._businessPartnerService.attachBusinessPartnersRelationData(outlets);
    const outletWithRelationsData = await this.attachOutletsRelationData(outletWithRelatedData);
    return {
      outlets: outletWithRelationsData,
      count,
    };
  }

  mappingEntityDataToExportData(dataMappingFields: NonNullable<unknown>, data: NonNullable<unknown>) {
    return Object.keys(dataMappingFields).reduce((exportData, key) => {
      const fieldValueExtractor = dataMappingFields[key];
      if (!fieldValueExtractor) {
        return exportData;
      }
      const value = typeof fieldValueExtractor === 'function' ? fieldValueExtractor(data) : _.get(data, fieldValueExtractor);
      exportData[key] = value instanceof Date ? formatDate(value, 'M/D/YYYY H:mm') : value;
      return exportData;
    }, {});
  }

  async queryExportOutletData() {
    const queryRelationTypes = [BusinessPartnerRelationType.OUTLET_DEPOT, BusinessPartnerRelationType.CONTACT_OUTLET];
    const queryBusinessPartnerTypes = [BusinessPartnerType.OUTLET, BusinessPartnerType.CONTACT, BusinessPartnerType.DEPOT];
    const [businessPartners, contacts, businessPartnerRelations, communications, details, operatingHours, geoLocations, customers] = await Promise.all([
      this._businessPartnerService.findWithOrder(
        {
          businessPartnerType: In(queryBusinessPartnerTypes),
          isDeleted: false,
        },
        'createdAt',
        'DESC',
      ),
      this._businessPartnerContactService.findWithOrder(
        {
          businessPartnerContactPersonRole: In([BusinessPartnerContactRole.CALL_CENTER, BusinessPartnerContactRole.SALE_REP]),
          isDeleted: false,
        },
        'createdAt',
        'DESC',
      ),
      this._businessPartnerRelationService.find({
        where: {
          businessPartnerRelationType: In(queryRelationTypes),
        },
      }),
      this._businessPartnerCommunicationService.find({
        where: {
          businessPartnerType: In(queryBusinessPartnerTypes),
          isDeleted: false,
        },
      }),
      this._businessPartnerDetailService.find({
        where: {
          businessPartnerType: In(queryBusinessPartnerTypes),
          isDeleted: false,
        },
      }),
      this._businessPartnerOperatingHourService.find({
        where: {
          businessPartnerType: In(queryBusinessPartnerTypes),
          isDeleted: false,
        },
      }),
      this._businessPartnersGeoLocationService.find({
        where: {
          businessPartnerType: In(queryBusinessPartnerTypes),
          isDeleted: false,
        },
      }),
      this._businessPartnerCustomerService.find({
        where: {
          businessPartnerType: In(queryBusinessPartnerTypes),
          isDeleted: false,
        },
        relations: ['customerSalesOrganizations'],
      }),
    ]);

    const outletsMappingFields = OutletExportMapping.outlets;
    const depotsMappingFields = OutletExportMapping.depots;
    const contactsMappingFields = OutletExportMapping.contacts;
    const segmentsDataMappingFields = OutletExportMapping.segments;

    const { depots, outlets } = businessPartners.reduce(
      (acc, bp) => {
        const businessPartnerType = bp.businessPartnerType;
        const businessPartnerId = bp.id;
        const filterCondition = (data: any) => data && data?.businessPartner === businessPartnerId && businessPartnerType === data?.businessPartnerType;
        if (bp.businessPartnerType !== BusinessPartnerType.OUTLET) {
          return acc;
        }

        acc.outlets.push({
          ...bp,
          details: details.filter(filterCondition),
          communications: communications.filter(filterCondition),
          customers: customers.filter(filterCondition),
          operatingHours: operatingHours.filter(filterCondition),
          geoGraphicalLocations: geoLocations.filter(filterCondition),
        });

        const outletDepotRelation = businessPartnerRelations.find(
          (bpr) => bpr.businessPartnerRelationType === BusinessPartnerRelationType.OUTLET_DEPOT && bpr.businessPartner1 === bp.id,
        );

        const depotData = !isEmptyObjectOrArray(outletDepotRelation) && businessPartners.find((bP) => bP.id === outletDepotRelation.businessPartner2);

        if (!isEmptyObjectOrArray(depotData)) {
          acc.depots.push({
            ...outletDepotRelation,
            depotBusinessPartnerKey: depotData?.businessPartnerKey,
            outletBusinessPartnerKey: bp.businessPartnerKey,
          });
        }

        return acc;
      },
      { outlets: [], depots: [] },
    );

    const contactData = contacts.map((contact) => {
      const businessPartnerContactType = contact.businessPartnerContactType;
      const businessPartnerContactId = contact.id;
      const contactOutletRelations = (businessPartnerRelations || []).filter(
        (bpr) => bpr.businessPartnerRelationType === BusinessPartnerRelationType.CONTACT_OUTLET && bpr.businessPartner1 === contact.id,
      );
      const filterCondition = (data: any) => data && data?.businessPartner === businessPartnerContactId && businessPartnerContactType === data?.businessPartnerType;
      const contactExportData = {
        ...contact,
        details: details.filter(filterCondition),
        communications: communications.filter(filterCondition),
        // operatingHours: operatingHours.filter(filterCondition),
        geoGraphicalLocations: geoLocations.filter(filterCondition),
      };
      const outletBusinessPartnerKeys = [];

      for (let i = 0; i < contactOutletRelations.length; i++) {
        const relationOutletId = contactOutletRelations[i].businessPartner2;
        const outletData = outlets.find((outlet) => outlet.id === relationOutletId);
        if (outletData) {
          outletBusinessPartnerKeys.push(outletData?.businessPartnerKey);
        }
      }

      return this.mappingEntityDataToExportData(contactsMappingFields, {
        ...contactExportData,
        outletBusinessPartnerKeys: [...new Set(outletBusinessPartnerKeys)].join(', '),
      });
    });

    return {
      outlets: outlets.map((outlet) => this.mappingEntityDataToExportData(outletsMappingFields, outlet)),
      depots: depots.map((depot) => this.mappingEntityDataToExportData(depotsMappingFields, depot)),
      contacts: contactData,
      segments: outlets.map((outlet) => this.mappingEntityDataToExportData(segmentsDataMappingFields, outlet)),
    };
  }

  async exportOutletData(i18n: I18nContext) {
    const { outlets, depots, contacts, segments } = await this.queryExportOutletData();

    return this.fileService.exportXLSXFileWithMultipleSheet({
      fileName: `Outlet_Export_New`,
      sheets: [
        {
          name: 'outlets',
          data: outlets,
        },
        {
          name: 'depots',
          data: depots,
        },
        {
          name: 'contacts',
          data: contacts,
        },
        {
          name: 'segments',
          data: segments,
        },
      ],
    });
  }

  async getOutletCustomerData(outletExternalKeys: string[]) {
    const outlets = await this._businessPartnerService.find({
      where: {
        businessPartnerKey: In(outletExternalKeys),
        isDeleted: false,
      },
    });

    const outletsWithRelationData = await this._businessPartnerService.attachSelectedBusinessPartnersRelationsData(outlets, [
      BusinessPartnerRelationDataTypes.COMMUNICATIONS,
      BusinessPartnerRelationDataTypes.CUSTOMERS,
      BusinessPartnerRelationDataTypes.GEO_LOCATIONS,
    ]);

    return outletsWithRelationData.map((outlet) => {
      const outletCommunications = outlet?.communications;
      const outletCustomers = outlet?.customers;
      const outletGeoGraphicalLocations = outlet?.geoGraphicalLocations;

      let contact = outletCommunications?.find((comm) => comm.communicationType === BusinessPartnerRelationCommunication.TEL);
      if (!contact) {
        contact = outletCommunications?.find((comm) => comm.communicationType === BusinessPartnerRelationCommunication.PHONE);
      }
      const contactEmail = outletCommunications?.find((comm) => comm.communicationType === BusinessPartnerRelationCommunication.EMAIL);
      const customer = outletCustomers?.length ? outletCustomers[0] : null;
      const customerSalesOrganization = customer?.customerSalesOrganizations?.length ? customer?.customerSalesOrganizations[0] : null;
      const geoGraphicalLocations = outletGeoGraphicalLocations?.length ? outletGeoGraphicalLocations[0] : null;
      return {
        id: outlet.id,
        name: outlet.businessPartnerName1 || outlet.businessPartnerName2 || '',
        outletUCC: outlet.businessPartnerKey,
        address:
          geoGraphicalLocations?.city || geoGraphicalLocations?.houseNumber || geoGraphicalLocations?.region
            ? `${geoGraphicalLocations?.street} ${geoGraphicalLocations?.houseNumber} ${geoGraphicalLocations?.postalCode} ${geoGraphicalLocations?.city} ${geoGraphicalLocations?.region}`
            : outlet.details?.length > 0
            ? outlet.details[0].rawAddress
            : '',
        status: outlet.businessPartnerStatus,
        contact: {
          name: contact?.communicationName || outlet?.businessPartnerName2,
          phoneNumber: contact?.communicationValue || '',
          email: contactEmail?.communicationValue || '',
          role: 'Outlet Owner',
        },
        segments: {
          channel: customer?.customerChannel || '',
          subChannel: customerSalesOrganization?.customerSubChannel || '',
          segment: customer?.businessSegment || '',
          outletClassification: customerSalesOrganization?.outletClassification || '',
          organizationalSegment: customer?.businessOrganizationalSegment || '',
        },
      };
    });
  }
}
