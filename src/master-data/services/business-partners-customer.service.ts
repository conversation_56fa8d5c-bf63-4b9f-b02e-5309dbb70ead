import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, EntityManager, In, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartnerContact } from '../entities/business-partner-contact/business-partner-contact.entity';
import { BusinessPartnerCustomer } from '../entities/business-partner-customer/business-partner-customer.entity';

@Injectable()
export class BusinessPartnersCustomerService extends BaseSQLService<BusinessPartnerCustomer> {
  constructor(
    @InjectRepository(BusinessPartnerCustomer)
    private readonly _businessPartnersRepository: Repository<BusinessPartnerCustomer>,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {
    super();
    this._repository = this._businessPartnersRepository;
  }

  async update(id: string, updateData: BusinessPartnerCustomer) {
    const existedBusinessPartnerCustomer = await this._repository.findOne({ where: { id } });
    if (!existedBusinessPartnerCustomer) {
      throw new BadRequestException('partner_geo_location.not_found');
    }
    return this._repository.update(id, { ...existedBusinessPartnerCustomer, ...updateData });
  }

  async createCustomerForBusinessPartner(businessPartner: BusinessPartner, customers: BusinessPartnerCustomer[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const customersRequests = customers.map(async (customerData: DeepPartial<BusinessPartnerCustomer>) => {
      const { id, ...customer } = customerData;
      customer.businessPartnerType = businessPartner.businessPartnerType;
      customer.businessPartner = businessPartner.id;
      if (customer.customerSalesOrganizations.length > 0) {
        const newCSOs = customer.customerSalesOrganizations.map((cSO) => {
          const { id, businessPartnerCustomer, ...newCSO } = cSO;
          return newCSO;
        });
        customer.customerSalesOrganizations = newCSOs;
      }
      const newCustomerEntity = this._repository.create(customer);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerCustomer, newCustomerEntity) : this._repository.save(newCustomerEntity));
      return promise().catch((error) => {
        console.log(`Error creating customer for business partner ${businessPartner.businessPartnerType} - ${businessPartner.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(customersRequests);
  }

  async createCustomerForBusinessPartnerContact(businessPartner: BusinessPartnerContact, customers: BusinessPartnerCustomer[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerContactType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const customersRequests = customers.map(async (customerData: DeepPartial<BusinessPartnerCustomer>) => {
      const { id, ...customer } = customerData;
      customer.businessPartnerType = businessPartner.businessPartnerContactType;
      customer.businessPartner = businessPartner.id;
      if (customer.customerSalesOrganizations.length > 0) {
        const newCSOs = customer.customerSalesOrganizations.map((cSO) => {
          const { id, businessPartnerCustomer, ...newCSO } = cSO;
          return newCSO;
        });
        customer.customerSalesOrganizations = newCSOs;
      }
      const newCustomerEntity = this._repository.create(customer);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerCustomer, newCustomerEntity) : this._repository.save(newCustomerEntity));
      return promise().catch((error) => {
        console.log(`Error creating customer for business partner ${businessPartner.businessPartnerContactType} - ${businessPartner.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(customersRequests);
  }

  async removeBusinessPartnerCustomers(businessPartnerId: string) {
    if (!businessPartnerId) {
      return [];
    }

    return this._repository.update(
      {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
      { isDeleted: true, isActive: false },
    );
  }

  async replaceBusinessPartnerCustomers(businessPartner: BusinessPartner, newCustomers: BusinessPartnerCustomer[], entityManager: EntityManager) {
    await this.removeBusinessPartnerCustomers(businessPartner.id);
    return this.createCustomerForBusinessPartner(businessPartner, newCustomers, entityManager);
  }

  async replaceBusinessPartnerContactCustomers(businessPartner: BusinessPartnerContact, newOperatingHours: BusinessPartnerCustomer[], entityManager: EntityManager) {
    await this.removeBusinessPartnerCustomers(businessPartner.id);
    return this.createCustomerForBusinessPartnerContact(businessPartner, newOperatingHours, entityManager);
  }

  async findByBusinessPartner(businessPartnerId: string) {
    const customers = await this._repository.find({
      where: {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
      relations: ['customerSalesOrganizations'],
    });

    return customers.map((customer) => ({
      ...customer,
      customerSalesOrganizations: (customer?.customerSalesOrganizations || []).filter((cSO) => !cSO?.isDeleted),
    }));
  }

  async findByBusinessPartnerIds(businessPartnerIds: string[]) {
    return this._repository.find({
      where: {
        businessPartner: In(businessPartnerIds),
        isDeleted: false,
      },
      relations: ['customerSalesOrganizations'],
    });
  }
}
