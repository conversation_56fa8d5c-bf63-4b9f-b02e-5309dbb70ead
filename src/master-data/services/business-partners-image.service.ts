import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, In, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartnerImage } from '../entities/business-partner-image/business-partner-image.entity';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { BusinessPartnerContact } from '../entities/business-partner-contact/business-partner-contact.entity';
import { BusinessPartnerImageDto } from '../dtos/business-partner-image.dto';
import { extractImageInformation, mapDataFromDtoToEntity } from '../../utils';

@Injectable()
export class BusinessPartnersImageService extends BaseSQLService<BusinessPartnerImage> {
  constructor(
    @InjectRepository(BusinessPartnerImage)
    private readonly _businessPartnersImagesRepository: Repository<BusinessPartnerImage>,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {
    super();
    this._repository = this._businessPartnersImagesRepository;
  }

  async update(id: string, updateData: BusinessPartnerImage) {
    const existedBusinessPartnerimage = await this._repository.findOne({ where: { id } });
    if (!existedBusinessPartnerimage) {
      throw new BadRequestException('partner_geo_location.not_found');
    }
    return this._repository.update(id, { ...existedBusinessPartnerimage, ...updateData });
  }

  async createImagesForBusinessPartner(businessPartner: BusinessPartner, images: BusinessPartnerImage[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const imageRequests = images.map(async (image) => {
      image.businessPartnerType = businessPartner.businessPartnerType;
      image.businessPartner = businessPartner.id;
      const newimageEntity = this._repository.create(image);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerImage, newimageEntity) : this._repository.save(newimageEntity));
      return promise().catch((error) => {
        console.log(`Error creating image for business partner ${businessPartner.businessPartnerType} - ${businessPartner.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(imageRequests);
  }

  async createImagesForBusinessPartnerContact(businessPartner: BusinessPartnerContact, images: BusinessPartnerImage[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerContactType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const imageRequests = images.map(async (image) => {
      image.businessPartnerType = businessPartner.businessPartnerContactType;
      image.businessPartner = businessPartner.id;
      const newimageEntity = this._repository.create(image);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerImage, newimageEntity) : this._repository.save(newimageEntity));
      return promise().catch((error) => {
        console.log(`Error creating image for business partner ${businessPartner.businessPartnerContactType} - ${businessPartner.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(imageRequests);
  }

  async removeBusinessPartnerImages(businessPartnerId: string) {
    if (!businessPartnerId) {
      return [];
    }

    return this._repository.update(
      {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
      { isDeleted: true, isActive: false },
    );
  }

  async replaceBusinessPartnerImages(businessPartner: BusinessPartner, newImages: BusinessPartnerImage[], entityManager: EntityManager) {
    await this.removeBusinessPartnerImages(businessPartner.id);
    return this.createImagesForBusinessPartner(businessPartner, newImages, entityManager);
  }

  async replaceBusinessPartnerContactImages(businessPartner: BusinessPartnerContact, newImages: BusinessPartnerImage[], entityManager: EntityManager) {
    await this.removeBusinessPartnerImages(businessPartner.id);
    return this.createImagesForBusinessPartnerContact(businessPartner, newImages, entityManager);
  }

  mapImageDataFromDtoToEntity(imageData: BusinessPartnerImageDto): BusinessPartnerImage {
    if (imageData.server && imageData.imagePath) {
      return mapDataFromDtoToEntity<BusinessPartnerImage, BusinessPartnerImageDto>(null, imageData, BusinessPartnerImage);
    }
    const businessPartnerImage: BusinessPartnerImage = new BusinessPartnerImage();
    businessPartnerImage.businessPartnerType = imageData.businessPartnerType;
    const { server, path, imageName } = extractImageInformation(imageData?.imagePath);
    businessPartnerImage.imagePath = path;
    businessPartnerImage.server = server;
    businessPartnerImage.imageName = imageName;
    return businessPartnerImage;
  }

  async findByBusinessPartner(businessPartnerId: string) {
    return this._repository.find({
      where: {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
    });
  }

  async findByBusinessPartnerIds(businessPartnerIds: string[]) {
    return this._repository.find({
      where: {
        businessPartner: In(businessPartnerIds),
        isDeleted: false,
      },
    });
  }
}
