import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Countries } from '../entities/config/countries.entity';
import { BaseSQLService } from '../../shared/services/basesql.service';


@Injectable()
export class CountriesService extends BaseSQLService<Countries> {
  constructor(
    @InjectRepository(Countries)
    private readonly _countryRepository: Repository<Countries>,
  ) {
    super();
    this._repository = this._countryRepository;
  }
}
