import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON>tityManager, In, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartnerDetail } from '../entities/business-partner-detail/business-partner-detail.entity';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { BusinessPartnerContact } from '../entities/business-partner-contact/business-partner-contact.entity';

@Injectable()
export class BusinessPartnerDetailService extends BaseSQLService<BusinessPartnerDetail> {
  constructor(
    @InjectRepository(BusinessPartnerDetail)
    private readonly _businessPartnersDetailRepository: Repository<BusinessPartnerDetail>,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {
    super();
    this._repository = this._businessPartnersDetailRepository;
  }

  async update(id: string, updateData: BusinessPartnerDetail) {
    const existedBusinessPartnerDetail = await this._repository.findOne({ where: { id } });
    if (!existedBusinessPartnerDetail) {
      throw new BadRequestException('partner_detail.not_found');
    }
    return this._repository.update(id, { ...existedBusinessPartnerDetail, ...updateData });
  }

  async createDetailsForBusinessPartner(businessPartner: BusinessPartner, details: BusinessPartnerDetail[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const detailRequests = details.map(async (detail) => {
      detail.businessPartnerType = businessPartner.businessPartnerType;
      detail.businessPartner = businessPartner.id;
      const newDetailEntity = this._repository.create(detail);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerDetail, newDetailEntity) : this._repository.save(newDetailEntity));
      return promise().catch((error) => {
        console.log(`Error creating detail for business partner ${businessPartner.businessPartnerType} - ${businessPartner.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(detailRequests);
  }

  async createDetailsForBusinessPartnerContact(businessPartner: BusinessPartnerContact, details: BusinessPartnerDetail[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerContactType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const detailRequests = details.map(async (detail) => {
      detail.businessPartnerType = businessPartner.businessPartnerContactType;
      detail.businessPartner = businessPartner.id;
      const newDetailEntity = this._repository.create(detail);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerDetail, newDetailEntity) : this._repository.save(newDetailEntity));
      return promise().catch((error) => {
        console.log(`Error creating detail for business partner ${businessPartner.businessPartnerContactType} - ${businessPartner.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(detailRequests);
  }

  async removeBusinessPartnerDetails(businessPartnerId: string) {
    if (!businessPartnerId) {
      return [];
    }

    return this._repository.update(
      {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
      { isDeleted: true, isActive: false },
    );
  }

  async replaceBusinessPartnerDetails(businessPartner: BusinessPartner, newDetails: BusinessPartnerDetail[], entityManager: EntityManager) {
    await this.removeBusinessPartnerDetails(businessPartner.id);
    return this.createDetailsForBusinessPartner(businessPartner, newDetails, entityManager);
  }

  async replaceBusinessPartnerContactDetails(businessPartner: BusinessPartnerContact, newDetails: BusinessPartnerDetail[], entityManager: EntityManager) {
    await this.removeBusinessPartnerDetails(businessPartner.id);
    return this.createDetailsForBusinessPartnerContact(businessPartner, newDetails, entityManager);
  }

  async findByBusinessPartner(businessPartnerId: string) {
    return this._repository.find({
      where: {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
    });
  }

  async findByBusinessPartnerIds(businessPartnerIds: string[]) {
    return this._repository.find({
      where: {
        businessPartner: In(businessPartnerIds),
        isDeleted: false,
      },
    });
  }
}
