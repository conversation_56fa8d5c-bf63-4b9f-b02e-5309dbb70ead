import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import * as _ from 'lodash';

import { AuthService } from '../../auth/auth.service';
import { BusinessPartnersService } from './business-partners.service';
import { BusinessPartnersGeoLocationService } from './business-partners-geo-location.service';
import { BusinessPartnerDetailService } from './business-partners-detail.service';
import { BusinessPartnersImageService } from './business-partners-image.service';
import { BusinessPartnerCommunicationService } from './business-partners-communication.service';
import { DeepPartial, EntityManager, ILike, In, Repository } from 'typeorm';
import { BusinessPartnerOperatingHourService } from './business-partners-operating-hour.service';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { BusinessPartnerRequest } from '../entities/business-partner/business-partner-request.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { I18nContext } from 'nestjs-i18n';
import { getBusinessPartnerRequestTargetEmails, isEmptyObjectOrArray, mapDataFromDtoToEntity, printLog } from '../../utils';
import { runTransaction } from '../../utils/helpers/database';
import { BusinessPartnerRequestDto } from '../dtos/business-partner-request.dto';
import { BusinessPartnersCustomerService } from './business-partners-customer.service';
import { SendGridService } from '../../shared/mail/sendgrid';
import { BusinessPartnerRequestReviewDto } from '../dtos/business-partner-request-review.dto';
import { BusinessPartnerRelationCommunication, BusinessPartnerStatus, BusinessPartnerType } from '../constants/business-partner.enum';
import { SearchDto } from '../dtos/search.dto';
import { BusinessPartnerOutletDto } from '../dtos/business-partner-outlet.dto';
import { BusinessPartnerOutletService } from './business-partner-outlet.service';
import { CountDto } from '../dtos/count.dto';
import { BusinessPartnerDistributorService } from './business-partner-distributor.service';
import { ReviewStatus, ReviewType } from '../constants/outlet.enum';
import { BusinessPartnersContactService } from './business-partners-contact.service';
import { RequestStatus } from 'src/customer-flow/enums/index.enum';

@Injectable()
export class BusinessPartnerRequestService extends BaseSQLService<BusinessPartnerRequest> {
  constructor(
    @InjectRepository(BusinessPartnerRequest)
    private readonly _businessPartnerRequestsRepository: Repository<BusinessPartnerRequest>,

    @Inject()
    readonly _businessPartnerService: BusinessPartnersService,
    @Inject(forwardRef(() => BusinessPartnerCommunicationService))
    readonly _businessPartnerCommunicationService: BusinessPartnerCommunicationService,

    @Inject(forwardRef(() => BusinessPartnerDetailService))
    readonly _businessPartnerDetailService: BusinessPartnerDetailService,

    @Inject(forwardRef(() => BusinessPartnersImageService))
    readonly _businessPartnerImageService: BusinessPartnersImageService,

    @Inject(forwardRef(() => BusinessPartnersGeoLocationService))
    readonly _businessPartnerGeoLocationService: BusinessPartnersGeoLocationService,

    @Inject(forwardRef(() => BusinessPartnerOperatingHourService))
    readonly _businessPartnerOperatingHourService: BusinessPartnerOperatingHourService,

    @Inject(forwardRef(() => BusinessPartnersCustomerService))
    readonly _businessPartnersCustomerService: BusinessPartnersCustomerService,

    @Inject()
    readonly _businessPartnersGeoLocationService: BusinessPartnersGeoLocationService,

    @Inject()
    readonly _businessPartnersImageService: BusinessPartnersImageService,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,

    @Inject(forwardRef(() => SendGridService))
    readonly sendGridService: SendGridService,

    @Inject(forwardRef(() => BusinessPartnerOutletService))
    readonly _businessPartnerOutletService: BusinessPartnerOutletService,

    @Inject(forwardRef(() => BusinessPartnerDistributorService))
    readonly _businessPartnerDistributorService: BusinessPartnerDistributorService,

    @Inject(forwardRef(() => BusinessPartnersContactService))
    readonly _businessPartnersContactService: BusinessPartnersContactService,
  ) {
    super();
    this._repository = this._businessPartnerRequestsRepository;
  }

  async createOutlet(dto: BusinessPartnerRequestDto, i18n: I18nContext) {
    // check if an business partner entity with businessPartnerKey already existed
    if (dto?.businessPartnerKey) {
      const existedEntity = await this.findOne({ where: { businessPartnerKey: dto?.businessPartnerKey } });
      if (existedEntity && !existedEntity?.isDeleted) {
        throw new BadRequestException(i18n.t('outlet.existed_id'));
      }
      return this.updateOutlet(existedEntity, { ...dto, isDeleted: false, isActive: true }, i18n);
    }

    const newBusinessPartnerEntity = this.mapBusinessPartnerDataFromDtoToEntity(null, dto);

    // TRANSACTIONAL
    const [newBusinessPartner] = await runTransaction([
      async (entityManage: EntityManager, context) => {
        const newBusinessPartner = await entityManage.save(BusinessPartnerRequest, newBusinessPartnerEntity);
        context.businessPartner = newBusinessPartner;
        return newBusinessPartner;
      },
    ]);
    return newBusinessPartner;
  }

  async updateOutlet(outlet: BusinessPartnerRequest, updateData: any, i18n: I18nContext) {
    const updateOutletData = this.mapBusinessPartnerDataFromDtoToEntity(outlet, updateData);
    // TRANSACTIONAL
    const [_, response] = await runTransaction([
      async (entityManager: EntityManager, context) => {
        return await entityManager.save(BusinessPartnerRequest, updateOutletData);
      },
    ]);

    return response;
  }

  mapBusinessPartnerDataFromDtoToEntity(originalData: BusinessPartnerRequest, data: BusinessPartnerRequestDto): BusinessPartnerRequest {
    const newBusinessPartnerEntity = mapDataFromDtoToEntity<BusinessPartnerRequest, BusinessPartnerRequestDto>(originalData, data, BusinessPartnerRequest);
    newBusinessPartnerEntity.images = data?.images?.map((image) => this._businessPartnersImageService.mapImageDataFromDtoToEntity(image));
    return newBusinessPartnerEntity;
  }

  async findAndCount(filters: any = {}, offset = 0, limit = 20, orderBy = 'updatedAt', orderDesc = 'DESC'): Promise<BusinessPartnerRequest[] | any> {
    return this._repository.findAndCount({
      where: filters,
      order: {
        [orderBy]: orderDesc,
      },
      skip: offset,
      take: limit,
    });
  }

  async generateBusinessPartnerRequestEmailTemplateData(businessPartnerRequest: BusinessPartnerRequest, requestLabel: string) {
    const token = this._authService.jwtEncrypt({ type: ReviewType.type, createdAt: new Date() }, { expiredIn: '2y', secretKey: process.env.JWT_SECRET });
    const depotExternalId = businessPartnerRequest.businessPartnerDepotKey;
    const phoneNumber: any = (businessPartnerRequest?.communications || []).find(
      (communication: any) => _.toLower(communication?.communicationType) === BusinessPartnerRelationCommunication.TEL,
    );
    const firstImage: any = isEmptyObjectOrArray(businessPartnerRequest?.images) ? {} : businessPartnerRequest.images[0];
    const photoUrl = firstImage?.server && firstImage?.imagePath ? `${firstImage?.server}/${firstImage?.imagePath}` : '';
    const customer: any = isEmptyObjectOrArray(businessPartnerRequest?.customers) ? {} : businessPartnerRequest.customers[0];
    const { distributor } = await this._businessPartnerDistributorService.getDistributorAndDepotDataFromDepotExternalKey(depotExternalId);
    return {
      logo_img: `${process.env.BASE_URL}/templates/assets/img/oms-logo.svg`,
      request_label: requestLabel,
      customer_name: businessPartnerRequest.businessPartnerName1 || businessPartnerRequest.businessPartnerName2 || '',
      distributor_external_id: distributor?.businessPartnerKey,
      depot_external_id: depotExternalId,
      phone_number: phoneNumber?.communicationValue || '',
      business_channel: customer?.customerChannel || customer?.customerChannelCode || '',
      business_channel_cde: customer?.customerChannelCode || '',
      business_sub_channel: customer?.customerSubChannel || customer?.customerSubChannelCode || '',
      business_sub_channel_code: customer?.customerSubChannelCode || '',
      business_segment: customer?.businessSegment || customer?.businessSegmentCode || '',
      business_segment_code: customer?.businessSegmentCode || '',
      address_line: _.get(businessPartnerRequest, 'details[0].rawAddress', ''),
      photo_url: photoUrl,
      decline_url: `${process.env.BASE_URL}/business-partners/request/review-form/${businessPartnerRequest.id}?token=${token}&status=${ReviewStatus.DECLINED}`,
      approve_url: `${process.env.BASE_URL}/business-partners/request/review-form/${businessPartnerRequest.id}?token=${token}&status=${ReviewStatus.APPROVED}`,
    };
  }

  async sendEmailForBusinessPartnerRequest(businessPartnerRequest: BusinessPartnerRequest) {
    const existed = await this._businessPartnerService.findOne({
      where: {
        businessPartnerKey: businessPartnerRequest.businessPartnerKey,
      },
    });
    const requestType = existed && !existed.isDeleted ? 'Update Customer Request' : 'Create Customer Request';
    const templateData = await this.generateBusinessPartnerRequestEmailTemplateData(businessPartnerRequest, requestType);
    const emailAddresses = getBusinessPartnerRequestTargetEmails();
    return Promise.all(emailAddresses.map((address) => this.sendGridService.sendTemplateMail(address, requestType, templateData, 'approve-customer-email')));
  }

  async sendBusinessPartnerRequestEmail(businessPartnerKey: string) {
    const businessPartnerRequest = await this.findBusinessPartnerRequest(businessPartnerKey);
    return this.sendEmailForBusinessPartnerRequest(businessPartnerRequest);
  }

  validateBusinessPartnerRequestStatusChange(currentStatus: BusinessPartnerStatus, updateStatus: BusinessPartnerStatus) {
    if (currentStatus === BusinessPartnerStatus.ACTIVE && updateStatus !== BusinessPartnerStatus.ACTIVE) {
      throw new BadRequestException('business_partner_request.invalid_status_update');
    }
  }

  async updateBusinessPartnerRequest(requestId: string, dto: BusinessPartnerRequestDto) {
    const businessPartnerRequest = await this.findBusinessPartnerRequest(requestId);

    if (isEmptyObjectOrArray(businessPartnerRequest)) {
      throw new BadRequestException('business_partner_request.not_found');
    }

    const updatedBusinessPartnerRequest = await this._repository.save({ ...businessPartnerRequest, ...dto });

    if (businessPartnerRequest.status !== BusinessPartnerStatus.SEND_TO_HNK && updatedBusinessPartnerRequest?.status === BusinessPartnerStatus.SEND_TO_HNK) {
      this.sendEmailForBusinessPartnerRequest(updatedBusinessPartnerRequest).then((r) => printLog(r));
    }

    return updatedBusinessPartnerRequest;
  }

  async saleRepReviewRequest(review: BusinessPartnerRequestReviewDto) {
    const businessPartnerRequest = await this.findBusinessPartnerRequest(review?.businessPartnerKey);

    this.validateBusinessPartnerRequestStatusChange(businessPartnerRequest.status, review.status);

    const updateData: DeepPartial<BusinessPartnerRequest> = {
      status: review.status,
    };

    if (businessPartnerRequest.status !== BusinessPartnerStatus.ACTIVE && review.status === BusinessPartnerStatus.ACTIVE) {
      await this.processBusinessPartnerRequest(businessPartnerRequest);
    }

    return this._repository.save({ ...businessPartnerRequest, ...updateData });
  }

  async processBusinessPartnerRequest(businessPartnerRequest: BusinessPartnerRequest) {
    const existed = await this._businessPartnerService.findByExternalId(businessPartnerRequest.businessPartnerKey);
    const businessPartnerPayload: BusinessPartnerOutletDto = mapDataFromDtoToEntity<BusinessPartnerOutletDto, BusinessPartnerRequest>(
      null,
      businessPartnerRequest,
      BusinessPartnerOutletDto,
    );

    businessPartnerPayload.businessPartnerDepotKey = businessPartnerRequest.businessPartnerDepotKey;

    if (existed) {
      return this._businessPartnerOutletService.updateOutlet(existed, businessPartnerPayload);
    }

    return this._businessPartnerOutletService.createOutlet(businessPartnerPayload, true);
  }

  async getBusinessPartnerRequestDetail(id: string) {
    const request = await this.findOne({ where: id });
    const contact = request?.businessPartnerContact ? await this._businessPartnersContactService.findOne({ where: { id: request.businessPartnerContact } }) : {};
    return {
      ...request,
      submitRequestContact: contact,
    };
  }

  async findBusinessPartnerRequest(id: string): Promise<any> {
    const businessPartnerRequest = await this.getBusinessPartnerRequestDetail(id);

    if (isEmptyObjectOrArray(businessPartnerRequest) || businessPartnerRequest.isDeleted) {
      throw new BadRequestException('business_partner_request.not_found');
    }

    return businessPartnerRequest;
  }

  async attachBusinessPartnerRequestContactData(businessPartnerRequests: BusinessPartnerRequest[]) {
    try {
      const businessPartnerRequestContactIds = businessPartnerRequests.map((request) => request?.businessPartnerContact);
      const businessPartnerContacts = await this._businessPartnersContactService.find({
        where: {
          id: In(businessPartnerRequestContactIds),
        },
      });
      return businessPartnerRequests.map((request) => ({
        ...request,
        submitRequestContact: businessPartnerContacts.find((contact) => contact.id === request.businessPartnerContact) || {},
      }));
    } catch (err) {
      console.log(`Error query request contact data`, err);
      return businessPartnerRequests;
    }
  }

  async searchBusinessPartnerRequest(searchDto: SearchDto, i18n: I18nContext) {
    const { offset, limit, orderBy, orderDesc, searchText, status } = searchDto;

    const baseConditions: any = { isDeleted: false };
    let filterConditions: any = [];

    if (status) {
      filterConditions.push({
        ...baseConditions,
        status,
      });
    }

    if (searchText) {
      filterConditions.push({
        ...baseConditions,
        businessPartnerName1: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerName2: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerKey: ILike(`%${searchText}%`),
      });
    }

    if (!filterConditions.length) {
      filterConditions = baseConditions;
    }

    const [requests, count] = await this.findAndCount(filterConditions, offset, limit, orderBy, orderDesc);
    const businessPartnerRequestWithContactData = await this.attachBusinessPartnerRequestContactData(requests);
    return {
      requests: businessPartnerRequestWithContactData,
      count,
    };
  }

  async countBusinessPartnerRequest(countDto: CountDto, i18n: I18nContext) {
    const { orderBy, orderDesc, searchText, status } = countDto;

    const baseConditions: any = { isDeleted: false };
    let filterConditions: any = [];

    if (status) {
      filterConditions.push({
        ...baseConditions,
        status,
      });
    }

    if (searchText) {
      filterConditions.push({
        ...baseConditions,
        businessPartnerName1: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerName2: ILike(`%${searchText}%`),
      });

      filterConditions.push({
        ...baseConditions,
        businessPartnerKey: ILike(`%${searchText}%`),
      });
    }

    if (!filterConditions.length) {
      filterConditions = baseConditions;
    }

    const count = await this._repository.count({
      where: filterConditions,
      order: {
        [orderBy]: orderDesc,
      },
    });

    return { numberOfRequests: count };
  }

  async approveBusinessPartnerRequest(businessPartnerRequest: any | BusinessPartnerRequest) {
    const businessPartnerRequestData = await this.findOne({ where: { id: businessPartnerRequest?.id } });
    if (isEmptyObjectOrArray(businessPartnerRequestData)) {
      return null;
    }
    const businessPartner = await this._businessPartnerService.findOne({ where: { businessPartnerKey: businessPartnerRequestData.businessPartnerKey, isDeleted: false } });
    const updatedData = { ...businessPartnerRequestData, ...businessPartnerRequest };
    const { images, customers, communications, operatingHours, geoGraphicalLocations, details } = updatedData;
    const rowOutlet: BusinessPartnerOutletDto = {
      businessPartnerDepotKey: updatedData?.businessPartnerDepotKey,
      businessPartnerName1: updatedData.businessPartnerName1,
      businessPartnerName2: updatedData.businessPartnerName2,
      businessPartnerKey: updatedData.businessPartnerKey,
      businessPartnerDescription: updatedData?.businessPartnerDescription,
      businessPartnerType: BusinessPartnerType.OUTLET,
      businessPartnerStatus: BusinessPartnerStatus.ACTIVE,
      operatingHours: operatingHours ?? [],
      geoGraphicalLocations: geoGraphicalLocations ?? [],
      customers: customers ?? [],
      details: details ?? [],
      communications: communications ?? [],
      images: images ?? [],
    };
    if (!businessPartner) {
      return await this._businessPartnerOutletService.createOutlet(rowOutlet, true);
    }
    return await this._businessPartnerOutletService.updateOutlet(businessPartner, rowOutlet);
  }

  mappingBusinessPartnerRequestStatus(status: BusinessPartnerStatus): string {
    if (status === BusinessPartnerStatus.PENDING) {
      return RequestStatus.PENDING;
    }

    if (status === BusinessPartnerStatus.SEND_TO_HNK) {
      return RequestStatus.ADMIN_APPROVED;
    }

    if (status === BusinessPartnerStatus.ACTIVE) {
      return RequestStatus.APPROVED;
    }

    return RequestStatus.DECLINED;
  }

  async transformBusinessPartnerRequests(requests: BusinessPartnerRequest[]) {
    const requestOutletIds = requests.map((request) => request?.businessPartnerCurrentKey).filter((data) => !!data);
    const outlets = await this._businessPartnerService.find({
      where: {
        id: In([...new Set(requestOutletIds)]),
      },
    });
    const outletWithRelationData = await this._businessPartnerService.attachBusinessPartnersRelationData(outlets);
    return requests.map((request) => {
      const currentKey = request?.businessPartnerCurrentKey;
      const existedOutlet = (outletWithRelationData || []).find((outlet) => outlet?.id === currentKey);
      let currentData,
        result: any = {};
      if (existedOutlet) {
        currentData = this.transformBusinessPartnerRequestData(existedOutlet);
        result = {
          ...result,
          id: existedOutlet?.id,
          display_id: existedOutlet?.id,
          outlet_id: existedOutlet?.id,
        };
      }
      const requestData = this.transformBusinessPartnerRequestData(request);
      result.status = requestData?.status;
      result.declined_reason = requestData?.declined_reason;
      result.classifications = requestData?.classifications;
      result.created_at = requestData?.created_at;
      result.external_id = request?.businessPartnerKey;

      if (isEmptyObjectOrArray(currentData)) {
        result.name = requestData?.name;
        result.phone_number = requestData?.phone_number;
        result.images = requestData?.images;
        result.channel = requestData?.channel;
        result.sub_channel = requestData?.sub_channel;
        result.location = requestData?.location;
        return result;
      }

      result.name = {
        current_value: currentData?.name,
        new_value: requestData?.name,
      };
      result.phone_number = {
        current_value: currentData?.phone_number,
        new_value: requestData?.phone_number,
      };
      result.images = {
        current_value: currentData?.images,
        new_value: requestData?.images,
      };
      result.channel = {
        current_value: currentData?.channel,
        new_value: requestData?.channel,
      };
      result.sub_channel = {
        current_value: currentData?.sub_channel,
        new_value: requestData?.sub_channel,
      };
      result.location = {
        current_value: currentData?.location,
        new_value: requestData?.location,
      };
      return result;
    });
  }

  transformBusinessPartnerRequestData(item: BusinessPartnerRequest | any) {
    const { communications, details, geoGraphicalLocations, customers, images } = item;

    const channel = !isEmptyObjectOrArray(customers) ? (customers as any)[0]?.customerChannelCode : '';
    const customerSalesOrganizations = !isEmptyObjectOrArray(customers) ? (customers[0] as any)?.customerSalesOrganizations : '';
    const subChannel = !isEmptyObjectOrArray(customerSalesOrganizations) ? (customerSalesOrganizations[0] as any)?.customerSubChannel : '';
    const classifications = !isEmptyObjectOrArray(customerSalesOrganizations) ? (customerSalesOrganizations[0] as any)?.outletClassification : '';
    const phoneNumberCommunication = (communications || []).find((c: any) => c?.communicationType === BusinessPartnerRelationCommunication.TEL);
    const phoneNumber = (phoneNumberCommunication as any)?.communicationValue || '';
    const requestImages = (images || []).map((image: any) => (image?.server && image?.imagePath && `${image?.server}/${image?.imagePath}`) || '');
    const baseLocation = (isEmptyObjectOrArray(details) && {}) || {
      address_line: details[0]?.rawAddress,
    };
    const location = (isEmptyObjectOrArray(geoGraphicalLocations) && baseLocation) || {
      ...baseLocation,
      city: geoGraphicalLocations[0]?.city,
      region: geoGraphicalLocations[0]?.regionKey,
      street: geoGraphicalLocations[0]?.street,
      house_number: geoGraphicalLocations[0]?.houseNumber,
      latitude: geoGraphicalLocations[0]?.latitude,
      longitude: geoGraphicalLocations[0]?.longitude,
    };

    return {
      status: this.mappingBusinessPartnerRequestStatus(item?.status),
      declined_reason: item?.businessPartnerNote || '',
      name: item?.businessPartnerName1 || item?.businessPartnerName2 || '',
      phone_number: phoneNumber,
      images: requestImages,
      channel,
      sub_channel: subChannel,
      classifications,
      created_at: item?.createdAt,
      // "id": outlet?.id,
      // "display_id": outlet?.id,
      external_id: item?.businessPartnerKey,
      // "outlet_id": outlet?.id,
      location: isEmptyObjectOrArray(location) ? null : location,
    };
  }
}
