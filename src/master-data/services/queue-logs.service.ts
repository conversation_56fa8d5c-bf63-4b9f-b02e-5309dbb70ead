import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { QueueLog } from '../entities/queue/queue-log.entity';
import { QueueStatus, QueueTypes } from '../constants/business-partner.enum';
import { BusinessPartnerContactEDMData, BusinessPartnerEDMData } from '../interfaces/edm-data.interface';

@Injectable()
export class QueueLogsService extends BaseSQLService<QueueLog> {
  constructor(
    @InjectRepository(QueueLog)
    private readonly _queueLogRepository: Repository<QueueLog>,
  ) {
    super();
    this._repository = this._queueLogRepository;
  }

  async createLog({
    queueType,
    queueName,
    content,
    errorMessage,
    status,
    isTopic,
    isReTry,
  }: {
    queueType?: QueueTypes;
    queueName: string;
    content: BusinessPartnerEDMData | BusinessPartnerContactEDMData;
    errorMessage?: string;
    status: QueueStatus;
    isTopic?: boolean;
    isReTry?: boolean;
  }) {
    return this._queueLogRepository.save({
      queueType: this.getQueueType(content) || queueType,
      queueName,
      content: JSON.stringify(content, null, 2),
      status,
      isTopic,
      isReTry: isReTry,
      errorMessage,
    });
  }

  async updateLog(id: string, status: QueueStatus, error?: string) {
    return this._queueLogRepository.update(id, {
      status,
      errorMessage: error,
    });
  }

  private getQueueType(content: BusinessPartnerEDMData | BusinessPartnerContactEDMData): QueueTypes {
    const parsedContent = typeof content === 'string' ? JSON.parse(content) : content;

    if ('businessPartner' in parsedContent) {
      return QueueTypes.PUBLISH_BUSINESS_PARTNER;
    }

    if ('businessPartnerContactPerson' in parsedContent) {
      return QueueTypes.PUBLISH_BUSINESS_PARTNER_CONTACT;
    }

    return QueueTypes.PUBLISH_BUSINESS_PARTNER;
  }
}
