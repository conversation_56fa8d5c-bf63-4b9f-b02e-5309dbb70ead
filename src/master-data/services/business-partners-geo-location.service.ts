import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON>tityManager, In, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartnerGeoLocation } from '../entities/business-partner-geo-location/business-partner-geo-location.entity';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { BusinessPartnerContact } from '../entities/business-partner-contact/business-partner-contact.entity';

@Injectable()
export class BusinessPartnersGeoLocationService extends BaseSQLService<BusinessPartnerGeoLocation> {
  constructor(
    @InjectRepository(BusinessPartnerGeoLocation)
    private readonly _businessPartnersGeoLocationRepository: Repository<BusinessPartnerGeoLocation>,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {
    super();
    this._repository = this._businessPartnersGeoLocationRepository;
  }

  async update(id: string, updateData: BusinessPartnerGeoLocation) {
    const existedBusinessPartnergeoLocation = await this._repository.findOne({ where: { id } });
    if (!existedBusinessPartnergeoLocation) {
      throw new BadRequestException('partner_geo_location.not_found');
    }
    return this._repository.update(id, { ...existedBusinessPartnergeoLocation, ...updateData });
  }

  async createGeoLocationsForBusinessPartner(businessPartner: BusinessPartner, geoLocations: BusinessPartnerGeoLocation[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const geoLocationRequests = geoLocations.map(async (geoLocation) => {
      geoLocation.businessPartnerType = businessPartner.businessPartnerType;
      geoLocation.businessPartner = businessPartner.id;
      const newgeoLocationEntity = this._repository.create(geoLocation);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerGeoLocation, newgeoLocationEntity) : this._repository.save(newgeoLocationEntity));
      return promise().catch((error) => {
        console.log(`Error creating geoLocation for business partner ${businessPartner.businessPartnerType} - ${businessPartner.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(geoLocationRequests);
  }

  async createGeoLocationsForBusinessPartnerContact(businessPartner: BusinessPartnerContact, geoLocations: BusinessPartnerGeoLocation[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerContactType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const geoLocationRequests = geoLocations.map(async (geoLocation) => {
      geoLocation.businessPartnerType = businessPartner.businessPartnerContactType;
      geoLocation.businessPartner = businessPartner.id;
      const newgeoLocationEntity = this._repository.create(geoLocation);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerGeoLocation, newgeoLocationEntity) : this._repository.save(newgeoLocationEntity));
      return promise().catch((error) => {
        console.log(`Error creating geoLocation for business partner ${businessPartner.businessPartnerContactType} - ${businessPartner.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(geoLocationRequests);
  }

  async removeBusinessPartnerGeoLocations(businessPartnerId: string) {
    if (!businessPartnerId) {
      return [];
    }

    return this._repository.update(
      {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
      { isDeleted: true, isActive: false },
    );
  }

  async replaceBusinessPartnerGeoLocations(businessPartner: BusinessPartner, newGeoLocations: BusinessPartnerGeoLocation[], entityManager: EntityManager) {
    await this.removeBusinessPartnerGeoLocations(businessPartner.id);
    return this.createGeoLocationsForBusinessPartner(businessPartner, newGeoLocations, entityManager);
  }

  async replaceBusinessPartnerContactGeoLocations(businessPartner: BusinessPartnerContact, newGeoLocations: BusinessPartnerGeoLocation[], entityManager: EntityManager) {
    await this.removeBusinessPartnerGeoLocations(businessPartner.id);
    return this.createGeoLocationsForBusinessPartnerContact(businessPartner, newGeoLocations, entityManager);
  }

  async findByBusinessPartner(businessPartnerId: string) {
    return this._repository.find({
      where: {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
    });
  }

  async findByBusinessPartnerIds(businessPartnerIds: string[]) {
    return this._repository.find({
      where: {
        businessPartner: In(businessPartnerIds),
        isDeleted: false,
      },
    });
  }
}
