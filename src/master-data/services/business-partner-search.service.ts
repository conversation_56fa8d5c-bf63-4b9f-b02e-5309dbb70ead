import { Injectable } from '@nestjs/common';
import { BusinessPartnerRelationData, BusinessPartnerSearch } from '../entities/business-partner-search/business-partner-search.entity';
import { BusinessPartnersService } from './business-partners.service';
import { BusinessPartnersContactService } from './business-partners-contact.service';
import { BusinessPartnerCommunicationService } from './business-partners-communication.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { BusinessPartnerRelationCommunication, BusinessPartnerRelationType, BusinessPartnerStatus } from '../constants/business-partner.enum';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { BusinessPartnerCommunication } from '../entities/business-partner-communication/business-partner-communication.entity';
import { BusinessPartnerContact } from '../entities/business-partner-contact/business-partner-contact.entity';
import { printLog } from '../../utils';

interface BusinessPartnerRelation {
  businessPartner1Key: string;
  businessPartner2Key: string;
  relationType: BusinessPartnerRelationType;
  validFromDate?: string;
  validToDate?: string;
}

@Injectable()
export class BusinessPartnerSearchService extends BaseSQLService<BusinessPartnerSearch> {
  constructor(
    @InjectRepository(BusinessPartnerSearch)
    private readonly searchRepository: Repository<BusinessPartnerSearch>,
    private readonly businessPartnerService: BusinessPartnersService,
    private readonly contactService: BusinessPartnersContactService,
    private readonly communicationService: BusinessPartnerCommunicationService,
  ) {
    super();
    this._repository = this.searchRepository;
  }

  convertRelationsToData(relations: BusinessPartnerRelation[] = null): BusinessPartnerRelationData[] {
    if (!relations || relations.length === 0) {
      return null;
    }
    const map = new Map<BusinessPartnerRelationData['type'], Set<string>>();

    for (const rel of relations) {
      const type = rel.relationType;
      const id = rel.businessPartner2Key;

      if (!map.has(type)) {
        map.set(type, new Set());
      }
      map.get(type)!.add(id);
    }

    return Array.from(map.entries()).map(([type, ids]) => ({
      type,
      ids: Array.from(ids),
    }));
  }

  /**
   *
   * @param businessPartner
   * @param relations
   * @param communications
   */
  async upsertSearchIndexForBusinessPartner(
    businessPartner: BusinessPartner,
    relations: BusinessPartnerRelation[] = null,
    communications: BusinessPartnerCommunication[] = null,
  ): Promise<void> {
    try {
      if (!businessPartner) {
        return;
      }

      // If business partner is not active, delete search index
      if (businessPartner.businessPartnerStatus !== BusinessPartnerStatus.ACTIVE) {
        await this.searchRepository.delete({ businessPartnerId: businessPartner.id });
        return;
      }
      // Update or create search index
      await this.searchRepository.upsert(
        {
          businessPartnerId: businessPartner.id,
          businessPartnerType: businessPartner.businessPartnerType,
          name1: businessPartner.businessPartnerName1,
          name2: businessPartner.businessPartnerName2,
          key: businessPartner.businessPartnerKey,
          phone: communications
            ? communications
                .filter((c) => c.communicationType === BusinessPartnerRelationCommunication.TEL || c.communicationType === BusinessPartnerRelationCommunication.PHONE)
                .map((c) => c.communicationValue)
                .join(', ')
            : null,
          email: communications
            ? communications
                .filter((c) => c.communicationType === BusinessPartnerRelationCommunication.EMAIL)
                .map((c) => c.communicationValue)
                .join(', ')
            : null,
          relations: this.convertRelationsToData(relations),
        },
        ['businessPartnerId'],
      );
    } catch (e) {
      printLog('upsertSearchIndexForBusinessPartner', e);
    }
  }

  /**
   *
   * @param contact
   * @param relations
   * @param communications
   */
  async upsertSearchIndexForBusinessPartnerContact(
    contact: BusinessPartnerContact,
    relations: BusinessPartnerRelation[] = null,
    communications: BusinessPartnerCommunication[] = null,
  ): Promise<void> {
    try {
      if (!contact) {
        return;
      }

      // If contact is inactive, delete search index
      if (contact.businessPartnerContactStatus !== BusinessPartnerStatus.ACTIVE) {
        await this.searchRepository.delete({ businessPartnerId: contact.id });
        return;
      }
      // Update or create search index
      await this.searchRepository.upsert(
        {
          businessPartnerId: contact.id,
          businessPartnerType: contact.businessPartnerContactType,
          name1: contact.businessPartnerContactName1,
          name2: contact.businessPartnerContactName2,
          key: contact.businessPartnerContactKey,
          phone: communications
            ? communications
                .filter((c) => c.communicationType === BusinessPartnerRelationCommunication.TEL || c.communicationType === BusinessPartnerRelationCommunication.PHONE)
                .map((c) => c.communicationValue)
                .join(', ')
            : null,
          email: communications
            ? communications
                .filter((c) => c.communicationType === BusinessPartnerRelationCommunication.EMAIL)
                .map((c) => c.communicationValue)
                .join(', ')
            : null,
          relations: this.convertRelationsToData(relations),
        },
        ['businessPartnerId'],
      );
    } catch (e) {
      printLog('upsertSearchIndexForBusinessPartnerContact', e);
    }
  }

  async updateSearchIndexForBusinessPartner(businessPartnerId: string): Promise<void> {
    try {
      // Get business partner
      const businessPartner = await this.businessPartnerService.findOne({
        where: { id: businessPartnerId },
      });

      if (!businessPartner) {
        return;
      }

      // If business partner is not active, delete search index
      if (businessPartner.businessPartnerStatus !== BusinessPartnerStatus.ACTIVE) {
        await this.searchRepository.delete({ businessPartnerId });
        return;
      }

      // Get communications
      const communications = await this.communicationService.findByBusinessPartner(businessPartnerId);

      // Update or create search index
      await this.searchRepository.upsert(
        {
          businessPartnerId,
          businessPartnerType: businessPartner.businessPartnerType,
          name1: businessPartner.businessPartnerName1,
          name2: businessPartner.businessPartnerName2,
          key: businessPartner.businessPartnerKey,
          phone: communications
            ? communications
                .filter((c) => c.communicationType === BusinessPartnerRelationCommunication.TEL || c.communicationType === BusinessPartnerRelationCommunication.PHONE)
                .map((c) => c.communicationValue)
                .join(', ')
            : null,
          email: communications
            ? communications
                .filter((c) => c.communicationType === BusinessPartnerRelationCommunication.EMAIL)
                .map((c) => c.communicationValue)
                .join(', ')
            : null,
          relations: [],
        },
        ['businessPartnerId'],
      );
    } catch (e) {}
  }

  async updateSearchIndexForBusinessPartnerContact(contactId: string): Promise<void> {
    try {
      // Get contact
      const contact = await this.contactService.findOne({
        where: { id: contactId },
      });

      if (!contact) {
        return;
      }

      // If contact is inactive, delete search index
      if (contact.businessPartnerContactStatus !== BusinessPartnerStatus.ACTIVE) {
        await this.searchRepository.delete({ businessPartnerId: contactId });
        return;
      }

      // Get communications
      const communications = await this.communicationService.findByBusinessPartner(contactId);

      // Update or create search index
      await this.searchRepository.upsert(
        {
          businessPartnerId: contactId,
          businessPartnerType: contact.businessPartnerContactType,
          name1: contact.businessPartnerContactName1,
          name2: contact.businessPartnerContactName2,
          key: contact.businessPartnerContactKey,
          phone: communications
            ? communications
                .filter((c) => c.communicationType === BusinessPartnerRelationCommunication.TEL || c.communicationType === BusinessPartnerRelationCommunication.PHONE)
                .map((c) => c.communicationValue)
                .join(', ')
            : null,
          email: communications
            ? communications
                .filter((c) => c.communicationType === BusinessPartnerRelationCommunication.EMAIL)
                .map((c) => c.communicationValue)
                .join(', ')
            : null,
        },
        ['businessPartnerId'],
      );
    } catch (e) {}
  }

  async updateAllSearchIndexes(): Promise<void> {
    // Get all business partners
    const businessPartners = await this.businessPartnerService.findAll();
    for (const businessPartner of businessPartners) {
      await this.updateSearchIndexForBusinessPartner(businessPartner.id);
    }

    // Get all contacts
    const contacts = await this.contactService.findAll();
    for (const contact of contacts) {
      await this.updateSearchIndexForBusinessPartnerContact(contact.id);
    }
  }

  async search(keyword: string, businessPartnerType?: string): Promise<string[]> {
    const query = this.searchRepository
      .createQueryBuilder('search')
      .select('search.businessPartnerId')
      .where('(search.name1 ILIKE :keyword OR search.name2 ILIKE :keyword OR search.phone ILIKE :keyword OR search.email ILIKE :keyword OR search.key ILIKE :keyword)', {
        keyword: `%${keyword}%`,
      });

    if (businessPartnerType) {
      query.andWhere('search.businessPartnerType = :type', {
        type: businessPartnerType,
      });
    }

    const results = await query.getMany();
    return results.map((r) => r.businessPartnerId);
  }
}
