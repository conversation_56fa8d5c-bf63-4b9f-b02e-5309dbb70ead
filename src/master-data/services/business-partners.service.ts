import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartnerCommunicationService } from './business-partners-communication.service';
import { BusinessPartnerDetailService } from './business-partners-detail.service';
import { BusinessPartnersImageService } from './business-partners-image.service';
import { BusinessPartnersGeoLocationService } from './business-partners-geo-location.service';
import { BusinessPartnerOperatingHourService } from './business-partners-operating-hour.service';
import { isEmptyObjectOrArray, mapDataFromDtoToEntity } from 'src/utils';
import { BusinessPartnerDto } from '../dtos/business-partner.dto';
import { BusinessPartnerCommunication } from '../entities/business-partner-communication/business-partner-communication.entity';
import { BusinessPartnerCommunicationDto } from '../dtos/business-partner-communication.dto';
import { BusinessPartnerDetailDto } from '../dtos/business-partner-detail.dto';
import { BusinessPartnerImageDto } from '../dtos/business-partner-image.dto';
import { BusinessPartnerImage } from '../entities/business-partner-image/business-partner-image.entity';
import { BusinessPartnerDetail } from '../entities/business-partner-detail/business-partner-detail.entity';
import { BusinessPartnerGeoLocation } from '../entities/business-partner-geo-location/business-partner-geo-location.entity';
import { BusinessPartnerGeoLocationDto } from '../dtos/business-partner-geo-location.dto';
import { BusinessPartnerOperatingHour } from '../entities/business-partner-operating-hour/business-partner-operating-hour.entity';
import { BusinessPartnerOpeningHourDto } from '../dtos/business-partner-opening-hour.dto';
import { BusinessPartnersCustomerService } from './business-partners-customer.service';
import { BusinessPartnerCustomer } from '../entities/business-partner-customer/business-partner-customer.entity';
import { BusinessPartnerCustomerDto } from '../dtos/business-partner-customer.dto';
import { BusinessPartnerRelationService } from './business-partners-relation.service';
import { SYNC_BATCH_SIZE } from '../constants/business-partner.const';
import { BusinessPartnerRelationCommunication, BusinessPartnerRelationDataTypes, BusinessPartnerRelationType } from '../constants/business-partner.enum';

export interface RawContact extends BusinessPartner {
  id: string;
  businessPartnerName1: string;
  businessPartnerName2: string;
  communicationName: string;
  communicationType: string;
}

@Injectable()
export class BusinessPartnersService extends BaseSQLService<BusinessPartner> {
  constructor(
    @InjectRepository(BusinessPartner)
    private readonly _businessPartnersRepository: Repository<BusinessPartner>,

    @Inject(forwardRef(() => BusinessPartnerCommunicationService))
    readonly _businessPartnerCommunicationService: BusinessPartnerCommunicationService,

    @Inject(forwardRef(() => BusinessPartnerDetailService))
    readonly _businessPartnerDetailService: BusinessPartnerDetailService,

    @Inject(forwardRef(() => BusinessPartnersImageService))
    readonly _businessPartnerImageService: BusinessPartnersImageService,

    @Inject(forwardRef(() => BusinessPartnersGeoLocationService))
    readonly _businessPartnerGeoLocationService: BusinessPartnersGeoLocationService,

    @Inject(forwardRef(() => BusinessPartnerOperatingHourService))
    readonly _businessPartnerOperatingHourService: BusinessPartnerOperatingHourService,

    @Inject(forwardRef(() => BusinessPartnersCustomerService))
    readonly _businessPartnersCustomerService: BusinessPartnersCustomerService,

    @Inject()
    readonly _businessPartnersGeoLocationService: BusinessPartnersGeoLocationService,

    @Inject()
    readonly _businessPartnersImageService: BusinessPartnersImageService,

    @Inject()
    readonly _businessPartnersRelationService: BusinessPartnerRelationService,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {
    super();
    this._repository = this._businessPartnersRepository;
  }

  async update(id: string, updateData: any) {
    return this._repository.update(id, updateData);
  }

  async findById(id: string) {
    return this._repository.findOne({ where: { id } });
  }

  async findByIdWithRelations(id: string, relations: string[]) {
    return this._repository.findOne({
      where: { id },
      relations,
    });
  }

  async attachBusinessPartnerRelationData(businessPartner: BusinessPartner) {
    const errorLogging = (fieldName, error) => {
      console.log(`Error loading ${fieldName} for business partner ${businessPartner.id} relation data`, error?.message);
      return [];
    };
    const [details, communications, geoGraphicalLocations, images, operatingHours, customers] = await Promise.all([
      this._businessPartnerDetailService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('details', error)),
      this._businessPartnerCommunicationService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('communications', error)),
      this._businessPartnerGeoLocationService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('geoLocations', error)),
      this._businessPartnerImageService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('images', error)),
      this._businessPartnerOperatingHourService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('operatingHours', error)),
      this._businessPartnersCustomerService.findByBusinessPartner(businessPartner.id).catch((error) => errorLogging('customers', error)),
    ]);
    return {
      ...businessPartner,
      details,
      communications,
      geoGraphicalLocations,
      images,
      operatingHours,
      customers,
    };
  }

  async attachBusinessPartnersRelationData(businessPartners: BusinessPartner[]) {
    const errorLogging = (fieldName, error) => {
      console.log(`Error loading ${fieldName} for business partners`, error?.message);
      return [];
    };
    const businessPartnerIds = businessPartners.map((bp) => bp.id);
    const [
      businessPartnerDetails,
      businessPartnerCommunications,
      businessPartnerGeoGraphicalLocations,
      businessPartnerImages,
      businessPartnerOperatingHours,
      businessPartnerCustomers,
    ] = await Promise.all([
      this._businessPartnerDetailService.findByBusinessPartnerIds(businessPartnerIds).catch((error) => errorLogging('details', error)),
      this._businessPartnerCommunicationService.findByBusinessPartnerIds(businessPartnerIds).catch((error) => errorLogging('communications', error)),
      this._businessPartnerGeoLocationService.findByBusinessPartnerIds(businessPartnerIds).catch((error) => errorLogging('geoLocations', error)),
      this._businessPartnerImageService.findByBusinessPartnerIds(businessPartnerIds).catch((error) => errorLogging('images', error)),
      this._businessPartnerOperatingHourService.findByBusinessPartnerIds(businessPartnerIds).catch((error) => errorLogging('operatingHours', error)),
      this._businessPartnersCustomerService.findByBusinessPartnerIds(businessPartnerIds).catch((error) => errorLogging('customers', error)),
    ]);
    return businessPartners.map((bp) => ({
      ...bp,
      details: businessPartnerDetails.filter((bpD) => bpD?.businessPartner === bp.id),
      communications: businessPartnerCommunications.filter((bpC) => bpC?.businessPartner === bp.id),
      geoGraphicalLocations: businessPartnerGeoGraphicalLocations.filter((bpG) => bpG?.businessPartner === bp.id),
      images: businessPartnerImages.filter((bpI) => bpI?.businessPartner === bp.id),
      operatingHours: businessPartnerOperatingHours.filter((bpO) => bpO?.businessPartner === bp.id),
      customers: businessPartnerCustomers.filter((bpCu) => bpCu?.businessPartner === bp.id),
    }));
  }

  async attachSelectedBusinessPartnersRelationsData(businessPartners: BusinessPartner[], relationFieldNames: BusinessPartnerRelationDataTypes[]) {
    const errorLogging = (fieldName: string, error: any) => {
      console.log(`Error loading ${fieldName} for business partners`, error?.message);
      return [];
    };

    const businessPartnerIds = businessPartners.map((bp) => bp.id);

    // Prepare one promise per relation type (they are executed lazily below)
    const dataFetchers: Record<BusinessPartnerRelationDataTypes, Promise<any>> = {
      [BusinessPartnerRelationDataTypes.DETAILS]: this._businessPartnerDetailService
        .findByBusinessPartnerIds(businessPartnerIds)
        .catch((error) => errorLogging(BusinessPartnerRelationDataTypes.DETAILS, error)),

      [BusinessPartnerRelationDataTypes.COMMUNICATIONS]: this._businessPartnerCommunicationService
        .findByBusinessPartnerIds(businessPartnerIds)
        .catch((error) => errorLogging(BusinessPartnerRelationDataTypes.COMMUNICATIONS, error)),

      [BusinessPartnerRelationDataTypes.GEO_LOCATIONS]: this._businessPartnerGeoLocationService
        .findByBusinessPartnerIds(businessPartnerIds)
        .catch((error) => errorLogging(BusinessPartnerRelationDataTypes.GEO_LOCATIONS, error)),
      [BusinessPartnerRelationDataTypes.OPERATING_HOURS]: this._businessPartnerOperatingHourService
        .findByBusinessPartnerIds(businessPartnerIds)
        .catch((error) => errorLogging(BusinessPartnerRelationDataTypes.OPERATING_HOURS, error)),

      [BusinessPartnerRelationDataTypes.CUSTOMERS]: this._businessPartnersCustomerService
        .findByBusinessPartnerIds(businessPartnerIds)
        .catch((error) => errorLogging(BusinessPartnerRelationDataTypes.CUSTOMERS, error)),

      [BusinessPartnerRelationDataTypes.IMAGES]: this._businessPartnerImageService
        .findByBusinessPartnerIds(businessPartnerIds)
        .catch((error) => errorLogging(BusinessPartnerRelationDataTypes.IMAGES, error)),
    };

    // Execute only the promises requested in `relationFieldNames`
    const requestedResults = await Promise.all(relationFieldNames.map((field) => dataFetchers[field] ?? Promise.resolve([])));

    // Map <RelationType, DataArray>
    const relationResultMap = new Map<BusinessPartnerRelationDataTypes, any[]>();
    relationFieldNames.forEach((field, idx) => relationResultMap.set(field, requestedResults[idx] || []));

    const getPropName = (type: BusinessPartnerRelationDataTypes): string => {
      switch (type) {
        case BusinessPartnerRelationDataTypes.DETAILS:
          return 'details';
        case BusinessPartnerRelationDataTypes.COMMUNICATIONS:
          return 'communications';
        case BusinessPartnerRelationDataTypes.GEO_LOCATIONS:
          return 'geoGraphicalLocations';
        case BusinessPartnerRelationDataTypes.OPERATING_HOURS:
          return 'operatingHours';
        case BusinessPartnerRelationDataTypes.CUSTOMERS:
          return 'customers';
        case BusinessPartnerRelationDataTypes.IMAGES:
          return 'images';
        default:
          return '';
      }
    };

    return businessPartners.map((bp) => {
      const enrichedPartner: any = { ...bp };

      relationResultMap.forEach((items, type) => {
        const prop = getPropName(type);
        if (prop) {
          enrichedPartner[prop] = (items || []).filter((i) => i?.businessPartner === bp.id);
        }
      });
      return enrichedPartner;
    });
  }

  async createBusinessPartnerRelatedData(entityManager: EntityManager, businessPartner: BusinessPartner, dto: BusinessPartnerDto) {
    const { images, communications, details, geoGraphicalLocations, operatingHours, customers } = dto;
    const partnerCommunications = (communications || []).map((communication) =>
      mapDataFromDtoToEntity<BusinessPartnerCommunication, BusinessPartnerCommunicationDto>(null, communication, BusinessPartnerCommunication),
    );
    const partnerImages = (images || []).map((image) => this._businessPartnerImageService.mapImageDataFromDtoToEntity(image));
    const partnerDetails = (details || []).map((detail) => mapDataFromDtoToEntity<BusinessPartnerDetail, BusinessPartnerDetailDto>(null, detail, BusinessPartnerDetail));
    const partnerGeoGraphicalLocations = (geoGraphicalLocations || []).map((geoLocation) =>
      mapDataFromDtoToEntity<BusinessPartnerGeoLocation, BusinessPartnerGeoLocationDto>(null, geoLocation, BusinessPartnerGeoLocation),
    );
    const partnerOperatingHours = (operatingHours || []).map((operatingHour) =>
      mapDataFromDtoToEntity<BusinessPartnerOperatingHour, BusinessPartnerOpeningHourDto>(null, operatingHour, BusinessPartnerOperatingHour),
    );
    const partnerCustomers = (customers || []).map((customer) =>
      mapDataFromDtoToEntity<BusinessPartnerCustomer, BusinessPartnerCustomerDto>(null, customer, BusinessPartnerCustomer),
    );
    return Promise.all([
      this._businessPartnerCommunicationService.createCommunicationsForBusinessPartner(businessPartner, partnerCommunications, entityManager),
      this._businessPartnerDetailService.createDetailsForBusinessPartner(businessPartner, partnerDetails, entityManager),
      this._businessPartnersImageService.createImagesForBusinessPartner(businessPartner, partnerImages, entityManager),
      this._businessPartnersGeoLocationService.createGeoLocationsForBusinessPartner(businessPartner, partnerGeoGraphicalLocations, entityManager),
      this._businessPartnerOperatingHourService.createOperatingHoursForBusinessPartner(businessPartner, partnerOperatingHours, entityManager),
      this._businessPartnersCustomerService.createCustomerForBusinessPartner(businessPartner, partnerCustomers, entityManager),
    ]);
  }

  async deactivateRelatedBusinessPartnerData(businessPartner: any) {
    return Promise.all([
      this._businessPartnerCommunicationService.removeBusinessPartnerCommunications(businessPartner.id),
      this._businessPartnerDetailService.removeBusinessPartnerDetails(businessPartner.id),
      this._businessPartnersImageService.removeBusinessPartnerImages(businessPartner.id),
      this._businessPartnersGeoLocationService.removeBusinessPartnerGeoLocations(businessPartner.id),
      this._businessPartnerOperatingHourService.removeBusinessPartnerOperatingHours(businessPartner.id),
      this._businessPartnersCustomerService.removeBusinessPartnerCustomers(businessPartner.id),
      this._businessPartnersRelationService.removeBusinessPartnerRelations(businessPartner.id),
      this._businessPartnersCustomerService.removeBusinessPartnerCustomers(businessPartner.id),
    ]);
  }

  async updateBusinessPartnerRelatedData(entityManager: EntityManager, businessPartner: BusinessPartner, dto: any) {
    const { images, communications, details, geoGraphicalLocations, operatingHours, customers } = dto;
    const partnerCommunications = (communications || []).map((communication) =>
      mapDataFromDtoToEntity<BusinessPartnerCommunication, BusinessPartnerCommunicationDto>(null, communication, BusinessPartnerCommunication),
    );
    const partnerImages = (images || []).map((image) => mapDataFromDtoToEntity<BusinessPartnerImage, BusinessPartnerImageDto>(null, image, BusinessPartnerImage));
    const partnerDetails = (details || []).map((detail) => mapDataFromDtoToEntity<BusinessPartnerDetail, BusinessPartnerDetailDto>(null, detail, BusinessPartnerDetail));
    const partnerGeoGraphicalLocations = (geoGraphicalLocations || []).map((geoLocation) =>
      mapDataFromDtoToEntity<BusinessPartnerGeoLocation, BusinessPartnerGeoLocationDto>(null, geoLocation, BusinessPartnerGeoLocation),
    );
    const partnerOperatingHours = (operatingHours || []).map((oh) =>
      mapDataFromDtoToEntity<BusinessPartnerOperatingHour, BusinessPartnerOpeningHourDto>(null, oh, BusinessPartnerOperatingHour),
    );

    const partnerCustomers = (customers || []).map((oh) => mapDataFromDtoToEntity<BusinessPartnerCustomer, BusinessPartnerCustomerDto>(null, oh, BusinessPartnerCustomer));

    const updatedRelatedData = await Promise.all([
      this._businessPartnerCommunicationService.replaceBusinessPartnerCommunications(businessPartner, partnerCommunications, entityManager),
      this._businessPartnerDetailService.replaceBusinessPartnerDetails(businessPartner, partnerDetails, entityManager),
      this._businessPartnersImageService.replaceBusinessPartnerImages(businessPartner, partnerImages, entityManager),
      this._businessPartnersGeoLocationService.replaceBusinessPartnerGeoLocations(businessPartner, partnerGeoGraphicalLocations, entityManager),
      this._businessPartnerOperatingHourService.replaceBusinessPartnerOperatingHours(businessPartner, partnerOperatingHours, entityManager),
      this._businessPartnersCustomerService.replaceBusinessPartnerCustomers(businessPartner, partnerCustomers, entityManager),
    ]);

    return {
      ...businessPartner,
      communications: updatedRelatedData[0] || [],
      details: updatedRelatedData[1] || [],
      images: updatedRelatedData[2] || [],
      geoGraphicalLocations: updatedRelatedData[3] || [],
      operatingHours: updatedRelatedData[4] || [],
      customers: updatedRelatedData[5] || [],
    };
  }

  async findByIdWithAllRelations(id: string) {
    const businessPartner = await this.findById(id);
    if (isEmptyObjectOrArray(businessPartner)) {
      return businessPartner;
    }
    return this.attachBusinessPartnerRelationData(businessPartner);
  }

  async deleteBusinessPartner(businessPartner: BusinessPartner) {
    if (!businessPartner || !businessPartner.id) {
      return null;
    }

    return this.update(businessPartner.id, { isDeleted: true, isActive: false });
  }

  mapBusinessPartnerDataFromDtoToEntity(originalData: BusinessPartner, data: BusinessPartnerDto): BusinessPartner {
    const newBusinessPartnerEntity = mapDataFromDtoToEntity<BusinessPartner, BusinessPartnerDto>(originalData, data, BusinessPartner);
    newBusinessPartnerEntity.images = data?.images?.map((image) => this._businessPartnersImageService.mapImageDataFromDtoToEntity(image));
    return newBusinessPartnerEntity;
  }

  async findAndCount(filters: any = {}, offset = 0, limit = 1000, orderBy = 'id', orderDesc = 'ASC'): Promise<BusinessPartner[] | any> {
    return this._repository.findAndCount({
      where: filters,
      order: {
        [orderBy]: orderDesc,
      },
      skip: offset,
      take: limit,
    });
  }

  async findByExternalId(businessPartnerKey: string, additionalConditions?: any) {
    const searchConditions = (isEmptyObjectOrArray(additionalConditions) && { businessPartnerKey, isDeleted: false }) || {
      ...additionalConditions,
      businessPartnerKey,
      isDeleted: false,
    };

    return await this.findOne({ where: searchConditions });
  }

  public async processBatch<T, R>(items: T[], processor: (item: T) => Promise<R>): Promise<R[]> {
    const results: R[] = [];
    for (let i = 0; i < items.length; i += SYNC_BATCH_SIZE.DISTRIBUTOR) {
      const chunk = items.slice(i, i + SYNC_BATCH_SIZE.DISTRIBUTOR);
      const batchResults = await Promise.all(chunk.map(processor));
      results.push(...batchResults);
    }
    return results;
  }

  async findOneDepotByOutletId(outletId: string) {
    try {
      const outlet = await this.findOne({ where: { id: outletId, isDeleted: false } });
      if (isEmptyObjectOrArray(outlet)) {
        return null;
      }
      const outletDepotRelationId = await this._businessPartnersRelationService.findOne({
        where: {
          businessPartner1: outlet.id,
          businessPartnerRelationType: BusinessPartnerRelationType.OUTLET_DEPOT,
        },
      });
      return {
        outlet,
        depot: await this.findOne({ where: { id: outletDepotRelationId.businessPartner2, isDeleted: false } }),
      };
    } catch (e) {
      return null;
    }
  }

  async findContactByOutletId(outletId: string): Promise<RawContact> {
    const query = this._repository
      .createQueryBuilder('businessPartner')
      .leftJoinAndSelect('business_partner_communications', 'communications', 'communications.businessPartnerKey = businessPartner.businessPartnerKey')
      .select([
        'businessPartner.id as "id"',
        'businessPartner.businessPartnerName1 as "businessPartnerName1"',
        'businessPartner.businessPartnerName2 as "businessPartnerName2"',
        'communications.communicationName as "communicationName"',
        'communications.communicationType as "communicationType"',
      ])
      .where('businessPartner.id = :outletId', { outletId })
      .andWhere('businessPartner.isDeleted = false')
      .andWhere('businessPartner.isActive = true')
      .andWhere('communications.isDeleted = false')
      .andWhere('communications.isActive = true')
      .andWhere('communications.communicationType IN (:...communicationTypes)', {
        communicationTypes: [BusinessPartnerRelationCommunication.TEL, BusinessPartnerRelationCommunication.PHONE],
      })
      .orderBy('businessPartner.updatedAt', 'DESC');
    return await query.getRawOne();
  }
}
