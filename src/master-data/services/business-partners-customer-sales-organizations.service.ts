import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { BusinessPartnerContact } from '../entities/business-partner-contact/business-partner-contact.entity';
import { BusinessPartnerCustomerSalesOrganization } from '../entities/business-partner-customer/business-partner-customer-sales-organization.entity';
import { BusinessPartnerCustomerSalesOrganizationDto } from '../dtos/business-partner-customer-sales-organization.dto';
import { BusinessPartnerCustomer } from '../entities/business-partner-customer/business-partner-customer.entity';
import { mapDataFromDtoToEntity } from 'src/utils';

@Injectable()
export class BusinessPartnerCustomerSalesOrganizationsService extends BaseSQLService<BusinessPartnerCustomerSalesOrganization> {
  constructor(
    @InjectRepository(BusinessPartnerCustomerSalesOrganization)
    private readonly _businessPartnerCustomerSalesOrganizartionsRepository: Repository<BusinessPartnerCustomerSalesOrganization>,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {
    super();
    this._repository = this._businessPartnerCustomerSalesOrganizartionsRepository;
  }

  async update(id: string, updateData: BusinessPartnerCustomerSalesOrganizationDto) {
    const existedBusinessPartnerCustomerSalesOrganization = await this._repository.findOne({ where: { id } });
    if (!existedBusinessPartnerCustomerSalesOrganization) {
      throw new BadRequestException('partner_customer_sale_organization.not_found');
    }
    return this._repository.update(id, { ...existedBusinessPartnerCustomerSalesOrganization, ...updateData });
  }

  async createSalesOrganizationsForBusinessPartnerCustomer(businessPartnerCustomer: BusinessPartnerCustomer, salesOrganizations: BusinessPartnerCustomerSalesOrganizationDto[], entityManager?: EntityManager) {
    if (!businessPartnerCustomer.id) {
      throw new BadRequestException('partner_customer.not_found');
    }
    const customerSalesOrganizationRequests = salesOrganizations.map(async (sOR) => {
      const newBusinessPartnerCustomerSalesOrganizationsEntity =
        mapDataFromDtoToEntity<BusinessPartnerCustomerSalesOrganization, BusinessPartnerCustomerSalesOrganizationDto>(
          null, sOR, BusinessPartnerCustomerSalesOrganization
        );
      newBusinessPartnerCustomerSalesOrganizationsEntity.businessPartnerCustomer = businessPartnerCustomer;
      const newCommunicationEntity = this._repository.create(newBusinessPartnerCustomerSalesOrganizationsEntity);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerCustomerSalesOrganizationDto, newCommunicationEntity) : this._repository.save(newCommunicationEntity));
      return promise().catch((error) => {
        console.log(`Error creating communication for business partner customer ${businessPartnerCustomer.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(customerSalesOrganizationRequests);
  }

  async removeBusinessPartnerCustomerSalesOrganizations(businessPartnerCustomerId: string) {
    if (!businessPartnerCustomerId) {
      return [];
    }
    this._repository.createQueryBuilder()
      .update()
      .set({isDeleted: true, isActive: false})
      .where(`businessPartnerCustomerId=${businessPartnerCustomerId}`)
      .execute();
  }

  async replaceBusinessPartnerCommunications(businessPartnerCustomer: BusinessPartnerCustomer, newCommunications: BusinessPartnerCustomerSalesOrganizationDto[], entityManager: EntityManager) {
    await this.removeBusinessPartnerCustomerSalesOrganizations(businessPartnerCustomer.id);
    return this.createSalesOrganizationsForBusinessPartnerCustomer(businessPartnerCustomer, newCommunications, entityManager);
  }
}
