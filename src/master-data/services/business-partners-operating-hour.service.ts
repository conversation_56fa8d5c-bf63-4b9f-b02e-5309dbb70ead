import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, In, Repository } from 'typeorm';
import { BaseSQLService } from '../../shared/services/basesql.service';
import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { AuthService } from '../../auth/auth.service';
import { BusinessPartnerOperatingHour } from '../entities/business-partner-operating-hour/business-partner-operating-hour.entity';
import { BusinessPartnerContact } from '../entities/business-partner-contact/business-partner-contact.entity';

@Injectable()
export class BusinessPartnerOperatingHourService extends BaseSQLService<BusinessPartnerOperatingHour> {
  constructor(
    @InjectRepository(BusinessPartnerOperatingHour)
    private readonly _businessPartnersRepository: Repository<BusinessPartnerOperatingHour>,

    @Inject(forwardRef(() => AuthService))
    readonly _authService: AuthService,
  ) {
    super();
    this._repository = this._businessPartnersRepository;
  }

  async update(id: string, updateData: BusinessPartnerOperatingHour) {
    const existedBusinessPartnerOperatingHour = await this._repository.findOne({ where: { id } });
    if (!existedBusinessPartnerOperatingHour) {
      throw new BadRequestException('partner_geo_location.not_found');
    }
    return this._repository.update(id, { ...existedBusinessPartnerOperatingHour, ...updateData });
  }

  async createOperatingHoursForBusinessPartner(businessPartner: BusinessPartner, operatingHours: BusinessPartnerOperatingHour[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const operatingHourRequests = operatingHours.map(async (operatingHour) => {
      operatingHour.businessPartnerType = businessPartner.businessPartnerType;
      operatingHour.businessPartner = businessPartner.id;
      const newoperatingHourEntity = this._repository.create(operatingHour);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerOperatingHour, newoperatingHourEntity) : this._repository.save(newoperatingHourEntity));
      return promise().catch((error) => {
        console.log(`Error creating operating hours for business partner ${businessPartner.businessPartnerType} - ${businessPartner.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(operatingHourRequests);
  }

  async createOperatingHoursForBusinessPartnerContact(businessPartner: BusinessPartnerContact, operatingHours: BusinessPartnerOperatingHour[], entityManager?: EntityManager) {
    if (!businessPartner.businessPartnerContactType || !businessPartner.id) {
      throw new BadRequestException('distributor.not_found_user');
    }
    const operatingHourRequests = operatingHours.map(async (operatingHour) => {
      operatingHour.businessPartnerType = businessPartner.businessPartnerContactType;
      operatingHour.businessPartner = businessPartner.id;
      const newoperatingHourEntity = this._repository.create(operatingHour);
      const promise = () => (entityManager ? entityManager.save(BusinessPartnerOperatingHour, newoperatingHourEntity) : this._repository.save(newoperatingHourEntity));
      return promise().catch((error) => {
        console.log(`Error creating operating hours for business partner ${businessPartner.businessPartnerContactType} - ${businessPartner.id}`, error?.message);
        throw error;
      });
    });

    return Promise.all(operatingHourRequests);
  }

  async removeBusinessPartnerOperatingHours(businessPartnerId: string) {
    if (!businessPartnerId) {
      return [];
    }

    return this._repository.update(
      {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
      { isDeleted: true, isActive: false },
    );
  }

  async replaceBusinessPartnerOperatingHours(businessPartner: BusinessPartner, newOperatingHours: BusinessPartnerOperatingHour[], entityManager: EntityManager) {
    await this.removeBusinessPartnerOperatingHours(businessPartner.id);
    return this.createOperatingHoursForBusinessPartner(businessPartner, newOperatingHours, entityManager);
  }

  async replaceBusinessPartnerContactOperatingHours(businessPartner: BusinessPartnerContact, newOperatingHours: BusinessPartnerOperatingHour[], entityManager: EntityManager) {
    await this.removeBusinessPartnerOperatingHours(businessPartner.id);
    return this.createOperatingHoursForBusinessPartnerContact(businessPartner, newOperatingHours, entityManager);
  }

  async findByBusinessPartner(businessPartnerId: string) {
    return this._repository.find({
      where: {
        businessPartner: businessPartnerId,
        isDeleted: false,
      },
    });
  }

  async findByBusinessPartnerIds(businessPartnerIds: string[]) {
    return this._repository.find({
      where: {
        businessPartner: In(businessPartnerIds),
        isDeleted: false,
      },
    });
  }
}
