interface BusinessPartnerGeographicalLocation {
  geographicalLocationRole: string;
  validFromDate?: string;
  validToDate?: string;
  geographicalLocation: {
    street?: string;
    houseNumber?: string;
    postalCode?: string;
    city?: string;
    regionKey?: string;
    countryKey?: string;
    longitude?: string;
    latitude?: string;
  };
}

interface BusinessPartnerRelation {
  businessPartner1Key: string;
  businessPartner2Key: string;
  relationType: string;
  validFromDate?: string;
  validToDate?: string;
}

interface CustomerSalesOrganization {
  customerSales: {
    customerSubChannel?: string;
    outletClassification?: string;
    priceGroup?: string;
    tradingEndDate?: string;
    currency?: string;
    paymentTermsKey?: string;
    salesGroup?: string;
    deliveringSiteKey?: string;
  };
  customerBilling?: {
    paymentTermsKey?: string;
  };
}

interface Customer {
  status: string;
  customerChannel?: string;
  businessSegment?: string;
  businessOrganizationalSegment?: string;
  customerSalesOrganization?: CustomerSalesOrganization[];
  customerCommercialHierarchy: {
    territory: string;
  };
}

interface BusinessPartnerData {
  businessPartnerKey: string;
  status: string;
  name1: string;
  name2?: string;
  communication?: {
    communicationType: string;
    communicationNumber: string;
  }[];
  businessPartnerGeographicalLocation?: BusinessPartnerGeographicalLocation[];
  businessPartnerRelation?: BusinessPartnerRelation[];
  customer?: Customer;
}

interface BusinessPartnerContactData {
  businessPartnerKey: string;
  status: string;
  communication?: {
    communicationType: string;
    communicationNumber: string;
  }[];
  contactPersonRole: string;
  jobTitle: string;
  naturalPersonKey: {
    naturalPersonKey: string;
    firstName: string;
    middleName: string;
    lastName: string;
  };
}

export interface BusinessPartnerEDMData {
  businessPartner: BusinessPartnerData[];
}

export interface BusinessPartnerContactEDMData {
  businessPartnerContactPerson: BusinessPartnerContactData[];
}
