import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { BusinessPartnerStatus, BusinessPartnerType } from '../constants/business-partner.enum';

export class BusinessPartnerGeoLocationDto {
  @ApiPropertyOptional()
  @IsString()
  street?: string;

  @ApiPropertyOptional()
  @IsString()
  addressLine?: string;

  @ApiPropertyOptional()
  @IsString()
  houseNumber?: string;

  @ApiPropertyOptional()
  @IsString()
  postalCode?: string;

  @ApiPropertyOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional()
  @IsString()
  region?: string;

  @ApiPropertyOptional()
  @IsString()
  regionKey?: string;

  @ApiPropertyOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional()
  @IsString()
  countryKey?: string;

  @ApiPropertyOptional()
  @IsString()
  longitude?: string;

  @ApiPropertyOptional()
  @IsString()
  latitude?: string;

  @ApiPropertyOptional()
  @IsString()
  validToDate?: string;

  @ApiPropertyOptional()
  @IsString()
  validFromDate?: string;

  @ApiPropertyOptional()
  @IsString()
  geographicalLocationRole?: string;

  @ApiPropertyOptional()
  @IsString()
  businessPartnerType?: BusinessPartnerType;

  @ApiPropertyOptional()
  @IsString()
  locationStatus?: BusinessPartnerStatus;
}
