import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { BusinessPartnerContactRole, BusinessPartnerStatus } from '../constants/business-partner.enum';
import { BusinessPartnerGeoLocationDto } from './business-partner-geo-location.dto';
import { BusinessPartnerDetailDto } from './business-partner-detail.dto';
import { BaseDto } from './base.dto';
import { BusinessPartnerCommunicationDto } from './business-partner-communication.dto';
import { BusinessPartnerImageDto } from './business-partner-image.dto';
import { BusinessPartnerOpeningHourDto } from './business-partner-opening-hour.dto';

export class BusinessPartnerContactDto extends BaseDto {
  @ApiPropertyOptional()
  distributorPartnerKey?: string;

  @ApiPropertyOptional()
  distributorPartnerKeys?: string[];

  @ApiPropertyOptional()
  outletPartnerKey?: string;

  @ApiPropertyOptional()
  outletPartnerKeys?: string[];

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  businessPartnerContactName1: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  businessPartnerContactName2: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  businessPartnerContactKey: string;

  @ApiProperty()
  businessPartnerContactType: string;

  @ApiPropertyOptional()
  businessPartnerContactDescription?: string;

  @ApiPropertyOptional()
  businessPartnerContactPersonRole: BusinessPartnerContactRole;

  @ApiPropertyOptional()
  businessPartnerContactPersonJobTitle: string;

  @ApiPropertyOptional()
  @IsString()
  businessPartnerContactStatus: BusinessPartnerStatus;

  @ApiPropertyOptional()
  geoGraphicalLocations?: BusinessPartnerGeoLocationDto[];

  @ApiPropertyOptional()
  details?: BusinessPartnerDetailDto[];

  @ApiPropertyOptional()
  communications?: BusinessPartnerCommunicationDto[];

  @ApiPropertyOptional()
  images?: BusinessPartnerImageDto[];

  @ApiPropertyOptional()
  operatingHours?: BusinessPartnerOpeningHourDto[];
}
