import { ApiPropertyOptional } from "@nestjs/swagger";
import { BusinessPartnerStatus } from '../constants/business-partner.enum';

export class SearchDto {
  @ApiPropertyOptional()
  offset?: number;

  @ApiPropertyOptional()
  limit?: number;

  @ApiPropertyOptional()
  orderBy?: string;

  @ApiPropertyOptional()
  orderDesc?: 'ASC' | 'DESC';

  @ApiPropertyOptional()
  searchText?: string;

  @ApiPropertyOptional()
  status?: BusinessPartnerStatus;
}