import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {  IsString } from 'class-validator';
import { BusinessPartnerRelationCommunication, BusinessPartnerStatus, BusinessPartnerType } from '../constants/business-partner.enum';

export class BusinessPartnerCommunicationDto {
  @ApiProperty()
  @IsString()
  communicationName: string;

  @ApiProperty()
  @IsString()
  communicationValue: string;

  @ApiPropertyOptional()
  @IsString()
  businessPartnerKey?: string;

  @ApiProperty()
  communicationType: BusinessPartnerRelationCommunication;

  @ApiPropertyOptional()
  communicationDescription?: string;

  @ApiPropertyOptional()
  communicationStatus?: BusinessPartnerStatus;

  @ApiPropertyOptional()
  businessPartnerType?: BusinessPartnerType;
}
