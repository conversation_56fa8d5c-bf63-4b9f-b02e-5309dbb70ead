import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { Column } from 'typeorm';

export class BusinessPartnerCustomerSalesOrganizationDto {
  @ApiProperty()
  @IsString()
  customerName?: string;

  @ApiProperty()
  @IsString()
  customerSales?: string;

  @ApiProperty()
  @IsString()
  customerSubChannel?: string;

  @ApiProperty()
  @IsString()
  customerSubChannelCode?: string;

  @ApiProperty()
  @IsString()
  outletClassification?: string;

  @ApiProperty()
  @IsString()
  priceGroup?: string;

  @ApiProperty()
  @IsString()
  tradingEndDate?: string;

  @ApiProperty()
  @IsString()
  deliveringSiteKey?: string;

  @ApiProperty()
  @IsString()
  currencyCode?: string;

  @ApiProperty()
  @IsString()
  salesGroup?: string;

  /**
   * "customerBilling": {
   *     "paymentTermsKey": "01"
   *  }
   *  const result = await repo.findOne({
   *   where: { "customerBilling->>paymentTermsKey": "01" },
   * });
   */
  @Column({ type: 'jsonb', nullable: true })
  customerBilling?: Record<string, any>;

  @ApiProperty()
  @IsString()
  status?: string;
}
