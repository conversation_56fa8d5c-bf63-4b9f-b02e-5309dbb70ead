import { BusinessPartnerDto } from './business-partner.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class BusinessPartnerOutletDto extends BusinessPartnerDto {
  @ApiProperty()
  businessPartnerDepotKey: string;

  @ApiPropertyOptional()
  depotRelationStartDate?: Date;

  @ApiPropertyOptional()
  depotRelationEndDate?: Date;
}
