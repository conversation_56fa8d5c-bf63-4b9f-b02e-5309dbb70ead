import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { BusinessPartnerStatus, BusinessPartnerType } from '../constants/business-partner.enum';
import { BusinessPartnerCustomerSalesOrganizationDto } from './business-partner-customer-sales-organization.dto';

export class BusinessPartnerCustomerDto {
  @ApiPropertyOptional()
  @IsString()
  businessPartnerKey: string;

  @ApiPropertyOptional()
  businessPartnerType?: BusinessPartnerType;

  @ApiProperty()
  @IsString()
  customerName?: string;

  @ApiProperty()
  @IsString()
  customerKey?: string;

  @ApiProperty()
  @IsString()
  customerType?: string;

  @ApiProperty()
  @IsString()
  customerChannel?: string;

  @ApiProperty()
  @IsString()
  customerChannelCode?: string;

  @ApiProperty()
  @IsString()
  businessSegment?: string;

  @ApiProperty()
  @IsString()
  businessSegmentCode?: string;

  @ApiProperty()
  @IsString()
  businessOrganizationalSegment?: string;

  @ApiProperty()
  customerStatus?: BusinessPartnerStatus;

  @ApiProperty()
  customerSalesOrganizations?: BusinessPartnerCustomerSalesOrganizationDto[];
}
