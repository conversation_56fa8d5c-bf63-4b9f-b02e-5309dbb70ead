import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { BusinessPartnerStatus, BusinessPartnerType } from '../constants/business-partner.enum';
import { BusinessPartnerGeoLocationDto } from './business-partner-geo-location.dto';
import { BusinessPartnerDetailDto } from './business-partner-detail.dto';
import { BaseDto } from './base.dto';
import { BusinessPartnerCommunicationDto } from './business-partner-communication.dto';
import { BusinessPartnerImageDto } from './business-partner-image.dto';
import { BusinessPartnerOpeningHourDto } from './business-partner-opening-hour.dto';
import { BusinessPartnerCustomerDto } from './business-partner-customer.dto';

export class BusinessPartnerDto extends BaseDto {
  @ApiProperty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  businessPartnerName1: string;

  @ApiProperty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  businessPartnerName2: string;

  @ApiProperty()
  businessPartnerKey: string;

  @ApiPropertyOptional()
  businessPartnerDescription?: string;

  @ApiProperty()
  businessPartnerType: BusinessPartnerType;

  @ApiPropertyOptional()
  businessPartnerStatus: BusinessPartnerStatus;

  @ApiPropertyOptional()
  geoGraphicalLocations?: BusinessPartnerGeoLocationDto[];

  @ApiPropertyOptional()
  details?: BusinessPartnerDetailDto[];

  @ApiPropertyOptional()
  communications?: BusinessPartnerCommunicationDto[];

  @ApiPropertyOptional()
  images?: BusinessPartnerImageDto[];

  @ApiPropertyOptional()
  operatingHours?: BusinessPartnerOpeningHourDto[];

  @ApiPropertyOptional()
  customers?: BusinessPartnerCustomerDto[];
}
