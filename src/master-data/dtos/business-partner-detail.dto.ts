import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { BusinessPartnerType } from '../constants/business-partner.enum';

export class BusinessPartnerDetailDto {
  @ApiPropertyOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional()
  birthday?: string;

  @ApiPropertyOptional()
  taxNumber?: string;

  @ApiPropertyOptional()
  @IsString()
  businessPartnerType?: BusinessPartnerType;

  @ApiPropertyOptional()
  @IsString()
  businessUnit?: string;

  @ApiPropertyOptional()
  @IsString()
  timezone?: string;

  @ApiPropertyOptional()
  @IsString()
  rawAddress?: string;

  @ApiPropertyOptional()
  @IsString()
  holidays?: string;

  @ApiPropertyOptional()
  customerCommercialHierarchy?: Record<string, any>;
}
