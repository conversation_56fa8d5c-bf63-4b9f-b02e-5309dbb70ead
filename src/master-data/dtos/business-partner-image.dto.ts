import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { BusinessPartnerImageType, BusinessPartnerStatus, BusinessPartnerType } from '../constants/business-partner.enum';

export class BusinessPartnerImageDto {
  @ApiProperty()
  @IsString()
  imageName?: string;

  @ApiProperty()
  @IsString()
  server?: string;

  @ApiProperty()
  @IsString()
  imagePath: string;

  @ApiProperty()
  imageType?: BusinessPartnerImageType;

  @ApiPropertyOptional()
  businessPartnerType?: BusinessPartnerType;

  @ApiPropertyOptional()
  imageStatus?: BusinessPartnerStatus;
}
