import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsDateString } from 'class-validator';
import { IsValidDateRange } from 'src/shared/decorator/valid-date-range.decorator';

export class DateRangeDto {
  @ApiProperty({
    description: 'Start date of the range (YYYY-MM-DD)',
    required: false,
    example: '2024-04-01',
  })
  @IsOptional()
  @IsDateString()
  startDate?: Date;

  @ApiProperty({
    description: 'End date of the range (YYYY-MM-DD)',
    required: false,
    example: '2024-04-10',
  })
  @IsOptional()
  @IsDateString()
  @IsValidDateRange()
  endDate?: Date;
}
