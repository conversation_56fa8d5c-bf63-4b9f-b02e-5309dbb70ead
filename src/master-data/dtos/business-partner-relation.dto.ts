import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID } from 'class-validator';
import { BusinessPartnerRelationType, BusinessPartnerStatus } from '../constants/business-partner.enum';

export class BusinessPartnerRelationDto {
  @ApiProperty()
  @IsString()
  @IsUUID()
  businessPartner1: string;

  @ApiProperty()
  @IsString()
  @IsUUID()
  businessPartner2: string;

  @ApiProperty()
  businessPartnerRelationType: BusinessPartnerRelationType;

  @ApiProperty()
  businessPartnerRelationValidFromDate: Date;

  @ApiProperty()
  businessPartnerRelationValidToDate: Date;

  @ApiProperty()
  businessPartnerRelationStatus: BusinessPartnerStatus;
}
