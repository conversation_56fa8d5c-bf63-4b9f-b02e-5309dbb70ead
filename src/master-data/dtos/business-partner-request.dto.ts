import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { BusinessPartnerStatus, BusinessPartnerType } from '../constants/business-partner.enum';
import { BaseDto } from './base.dto';

export class BusinessPartnerRequestDto extends BaseDto {
  @ApiProperty({ nullable: true })
  businessPartnerNote: string;

  @ApiProperty({ nullable: true })
  businessPartnerContactAdmin: string;

  @ApiProperty()
  businessPartnerContact: string;

  @ApiProperty({ nullable: true })
  businessPartnerCurrentKey: string;

  @ApiProperty({ nullable: true })
  businessPartnerDepotKey: string;

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  businessPartnerName1: string;

  @ApiProperty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  businessPartnerName2: string;

  @ApiProperty()
  businessPartnerKey: string;

  @ApiPropertyOptional()
  businessPartnerDescription?: string;

  @ApiProperty()
  businessPartnerType: BusinessPartnerType;

  @ApiPropertyOptional()
  geoGraphicalLocations?: any[];

  @ApiPropertyOptional()
  details?: any[];

  @ApiPropertyOptional()
  communications?: any[];

  @ApiPropertyOptional()
  images?: any[];

  @ApiPropertyOptional()
  operatingHours?: any[];

  @ApiPropertyOptional()
  customers?: any[];

  @ApiPropertyOptional()
  status?: BusinessPartnerStatus;
}
