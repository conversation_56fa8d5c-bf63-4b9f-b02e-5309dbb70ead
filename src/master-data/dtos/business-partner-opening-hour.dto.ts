import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { BusinessPartnerType, DayOfWeekShort } from '../constants/business-partner.enum';

export class BusinessPartnerOpeningHourDto {
  @ApiPropertyOptional()
  @IsString()
  businessPartnerType: BusinessPartnerType;

  @ApiPropertyOptional()
  day: DayOfWeekShort;

  @ApiPropertyOptional()
  @IsString()
  openTime: string;

  @ApiPropertyOptional()
  @IsString()
  closeTime: string;

  @ApiPropertyOptional()
  isClosed: boolean;
}
