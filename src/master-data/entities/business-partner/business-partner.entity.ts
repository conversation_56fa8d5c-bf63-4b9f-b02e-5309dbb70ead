import { Column, Entity, Index, OneToMany } from 'typeorm';
import { BusinessPartnerDetail } from '../business-partner-detail/business-partner-detail.entity';
import { BusinessPartnerCommunication } from '../business-partner-communication/business-partner-communication.entity';
import { BusinessPartnerCustomer } from '../business-partner-customer/business-partner-customer.entity';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerStatus, BusinessPartnerType } from '../../constants/business-partner.enum';
import { BusinessPartnerGeoLocation } from '../business-partner-geo-location/business-partner-geo-location.entity';
import { BusinessPartnerImage } from '../business-partner-image/business-partner-image.entity';
import { BusinessPartnerOperatingHour } from '../business-partner-operating-hour/business-partner-operating-hour.entity';
import { Vendor } from '../vendor/vendor.entity';

@Entity('business_partners')
export class BusinessPartner extends BaseSQLEntity {
  @Column({ comment: 'Legal Name', nullable: false })
  businessPartnerName1: string;

  @Column({ comment: 'Trading Name', nullable: true })
  businessPartnerName2: string;

  @Index('business_partners_key_idx')
  @Column({ comment: 'External ID', unique: true })
  businessPartnerKey: string;

  @Column({ nullable: true })
  businessPartnerDescription: string;

  @Column({
    comment: 'distributor, depot, outlet',
    type: 'enum',
    enumName: 'business_partner_type',
    enum: BusinessPartnerType,
    default: BusinessPartnerType.OUTLET,
    nullable: false,
  })
  businessPartnerType: string;

  @Column({ type: 'enum', enum: BusinessPartnerStatus, enumName: 'business_partner_status', default: BusinessPartnerStatus.ACTIVE })
  businessPartnerStatus: BusinessPartnerStatus;

  @OneToMany(() => BusinessPartnerCommunication, (partner) => partner.businessPartner)
  communications: BusinessPartnerCommunication[];

  @OneToMany(() => BusinessPartnerGeoLocation, (partner) => partner.businessPartner)
  geoGraphicalLocations: BusinessPartnerGeoLocation[];

  @OneToMany(() => BusinessPartnerCustomer, (partner) => partner.businessPartner)
  customers: BusinessPartnerCustomer[];

  @OneToMany(() => BusinessPartnerImage, (partner) => partner.businessPartner)
  images: BusinessPartnerImage[];

  @OneToMany(() => BusinessPartnerOperatingHour, (partner) => partner.businessPartner)
  operatingHours: BusinessPartnerOperatingHour[];

  @OneToMany(() => BusinessPartnerDetail, (detail) => detail.businessPartner)
  details: BusinessPartnerDetail[];

  @OneToMany(() => Vendor, (detail) => detail.businessPartner)
  vendors: Vendor;
}
