import { Column, Entity, Index } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerStatus, BusinessPartnerType } from '../../constants/business-partner.enum';

@Entity('business_partner_requests')
export class BusinessPartnerRequest extends BaseSQLEntity {
  @Column({ nullable: true, comment: 'Note for Rejected' })
  businessPartnerNote: string;

  @Column({ nullable: true, type: 'uuid', comment: 'Who is approved or rejected?' })
  businessPartnerContactAdmin: string;

  @Column({ nullable: false, type: 'uuid', comment: 'Who is requested?' })
  businessPartnerContact: string;

  @Column({ nullable: true, comment: 'Which is depot?' })
  businessPartnerDepotKey: string;

  @Column({ comment: 'Legal Name', nullable: false })
  businessPartnerName1: string;

  @Column({ comment: 'Trading Name', nullable: true })
  businessPartnerName2: string;

  @Column({ comment: 'External Current ID', nullable: true })
  businessPartnerCurrentKey: string;

  @Index('business_partner_requests_key_idx', { unique: true })
  @Column({ comment: 'External ID', nullable: true })
  businessPartnerKey: string;

  @Column({ nullable: true })
  businessPartnerDescription: string;

  @Column({
    comment: 'distributor, depot, outlet',
    type: 'enum',
    enumName: 'business_partner_request_type',
    enum: BusinessPartnerType,
    default: BusinessPartnerType.OUTLET,
    nullable: false,
  })
  businessPartnerType: string;

  @Column({ type: 'enum', enum: BusinessPartnerStatus, default: BusinessPartnerStatus.PENDING })
  status: BusinessPartnerStatus;

  @Column({ type: 'jsonb', nullable: true })
  communications: object[];

  @Column({ type: 'jsonb', nullable: true })
  geoGraphicalLocations: object[];

  @Column({ type: 'jsonb', nullable: true })
  customers: object[];

  @Column({ type: 'jsonb', nullable: true })
  images: object[];

  @Column({ type: 'jsonb', nullable: true })
  operatingHours: object[];

  @Column({ type: 'jsonb', nullable: true })
  details: object[];
}
