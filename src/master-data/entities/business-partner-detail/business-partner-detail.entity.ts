import { Column, Entity, Index, PrimaryColumn } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerType } from '../../constants/business-partner.enum';

@Entity('business_partner_details')
@Index(['businessPartner', 'businessPartnerType'])
export class BusinessPartnerDetail extends BaseSQLEntity {
  @Column({ nullable: true })
  source: string;

  @Column({ nullable: true })
  birthday: string;

  @Column({ nullable: true })
  taxNumber: string;

  @Column({ comment: 'example: BT03', nullable: true })
  businessUnit: string;

  @Column({ comment: 'Asia/Yangon', nullable: true })
  timezone: string;

  @Column({ comment: 'example: 30-04-2023|01-05-2023|01-09-2023', nullable: true })
  holidays: string;

  @Column({ comment: 'example: 45, Pyay Road, Kamayut Township, Yangon, Yangon Region, Myanmar', nullable: true })
  rawAddress: string;

  /**
   * "customerCommercialHierarchy": {
   *     "territory": "string"
   *  }
   *  const result = await repo.findOne({
   *   where: { "customerCommercialHierarchy->>territory": "string" },
   *  });
   */
  @Column({ type: 'jsonb', nullable: true })
  customerCommercialHierarchy: Record<string, any>;

  @PrimaryColumn('uuid', { nullable: false })
  businessPartner: string;

  @Column({ nullable: false, type: 'enum', enumName: 'details_business_partner_type', enum: BusinessPartnerType })
  businessPartnerType: string;
}
