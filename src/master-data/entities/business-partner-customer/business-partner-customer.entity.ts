import { Column, <PERSON><PERSON>ty, OneToMany, PrimaryColumn } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerStatus, BusinessPartnerType } from '../../constants/business-partner.enum';
import { BusinessPartnerCustomerSalesOrganization } from './business-partner-customer-sales-organization.entity';

@Entity('business_partner_customers')
export class BusinessPartnerCustomer extends BaseSQLEntity {
  @Column({ nullable: true })
  customerName: string;

  @Column({ nullable: true })
  customerKey: string;

  @Column({ nullable: true })
  customerType: string;

  @Column({ nullable: true })
  customerChannel: string;

  @Column({ nullable: true })
  customerChannelCode: string;

  @Column({ nullable: true })
  businessSegment: string;

  @Column({ nullable: true })
  businessSegmentCode: string;

  @Column({ nullable: true })
  businessOrganizationalSegment: string;

  @Column({ type: 'enum', enum: BusinessPartnerStatus, enumName: 'partner_customer_status', default: BusinessPartnerStatus.ACTIVE })
  customerStatus: BusinessPartnerStatus;

  @OneToMany(() => BusinessPartnerCustomerSalesOrganization, (saleOrgination) => saleOrgination.businessPartnerCustomer, { cascade: ['insert'] })
  customerSalesOrganizations: BusinessPartnerCustomerSalesOrganization[];

  @PrimaryColumn('uuid', { nullable: false })
  businessPartner: string;

  @Column({ nullable: false, type: 'enum', enum: BusinessPartnerType })
  businessPartnerType: string;
}
