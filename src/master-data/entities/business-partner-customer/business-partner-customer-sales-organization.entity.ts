import { Column, Entity, ManyToOne } from 'typeorm';
import { BusinessPartnerCustomer } from './business-partner-customer.entity';
import { BaseSQLEntity } from '../../../shared/basesql.entity';

@Entity('business_partner_customer_sales_organizations')
export class BusinessPartnerCustomerSalesOrganization extends BaseSQLEntity {
  @Column({ nullable: true })
  customerSales: string;

  @Column({ nullable: true })
  customerSubChannel: string;

  @Column({ nullable: true })
  customerSubChannelCode: string;

  @Column({ nullable: true })
  outletClassification: string;

  @Column({ nullable: true })
  priceGroup: string;

  @Column({ nullable: true })
  tradingEndDate: string;

  @Column({ nullable: true })
  deliveringSiteKey: string;

  @Column({ nullable: true })
  salesGroup: string;

  @Column({ nullable: true })
  currencyCode: string;

  /**
   * "customerBilling": {
   *     "paymentTermsKey": "01"
   *  }
   *  const result = await repo.findOne({
   *   where: { "customerBilling->>paymentTermsKey": "01" },
   * });
   */
  @Column({ type: 'jsonb', nullable: true })
  customerBilling: Record<string, any>;

  @Column({ nullable: true })
  status: string;

  @ManyToOne(() => BusinessPartnerCustomer, (partnerCustomer) => partnerCustomer.customerSalesOrganizations, { nullable: false })
  businessPartnerCustomer: BusinessPartnerCustomer;
}
