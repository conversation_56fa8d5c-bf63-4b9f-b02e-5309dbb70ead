import { Column, Entity, Index, PrimaryColumn } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerRelationCommunication, BusinessPartnerStatus, BusinessPartnerType } from '../../constants/business-partner.enum';

@Entity('business_partner_communications')
@Index(['businessPartner', 'businessPartnerType'])
export class BusinessPartnerCommunication extends BaseSQLEntity {
  @Column({ nullable: true })
  communicationName: string;

  /**
   * "communication": [
   * {
   *     "communicationName": "Thien",
   *     "communicationType": "phone",
   *     "communicationNumber": "+8312345678"
   * },
   * {
   *     "communicationName": "Thien",
   *     "communicationType": "email",
   *     "communicationNumber": "<EMAIL>"
   * }
   * ],
   */
  @Column({ nullable: true })
  communicationValue: string;

  @Column()
  businessPartnerKey: string;

  @Column({ comment: 'TEL, INT, EMAIL, ...', enumName: 'communication_type', enum: BusinessPartnerRelationCommunication })
  communicationType: string;

  @Column({ nullable: true })
  communicationDescription: string;

  @Column({ type: 'enum', enum: BusinessPartnerStatus, enumName: 'communication_status', default: BusinessPartnerStatus.ACTIVE })
  communicationStatus: BusinessPartnerStatus;

  @PrimaryColumn('uuid', { nullable: false })
  businessPartner: string;

  @Column({ nullable: false, type: 'enum', enumName: 'communication_business_partner_type', enum: BusinessPartnerType })
  businessPartnerType: string;
}
