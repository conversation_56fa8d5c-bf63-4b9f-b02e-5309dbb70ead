import { Column, Entity } from 'typeorm';

import { BaseSQLEntity } from '../../../shared/basesql.entity';

@Entity()
export class Currencies extends BaseSQLEntity {
  @Column({ unique: true })
  name: string;

  @Column({ unique: true })
  code: string;

  @Column()
  majorName: string;

  @Column({nullable: true})
  majorSymbol: string;

  @Column({nullable: true})
  minorName: string;

  @Column({nullable: true})
  minorSymbol: string;

  @Column()
  minorValue: string;

}
