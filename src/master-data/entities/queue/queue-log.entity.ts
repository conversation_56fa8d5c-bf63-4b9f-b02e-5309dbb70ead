import { Column, Entity } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { QueueStatus, QueueTypes } from '../../constants/business-partner.enum';

@Entity('queue_logs')
export class QueueLog extends BaseSQLEntity {
  @Column({ type: 'enum', enum: QueueTypes, nullable: false })
  queueType: QueueTypes;

  @Column({ type: 'varchar', nullable: false })
  queueName: string;

  @Column({ type: 'enum', enum: QueueStatus, nullable: false })
  status: QueueStatus;

  @Column({ type: 'boolean', default: false })
  isTopic: boolean;

  @Column({ type: 'boolean', default: false })
  isReTry: boolean;

  @Column({ type: 'text', nullable: true })
  content: string;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;
}
