import { Column, Entity, Index, PrimaryColumn } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerImageType, BusinessPartnerStatus, BusinessPartnerType } from '../../constants/business-partner.enum';

@Entity('business_partner_images')
@Index(['businessPartner', 'businessPartnerType'])
export class BusinessPartnerImage extends BaseSQLEntity {
  @Column()
  imageName: string;

  @Column({ nullable: false, type: 'enum', enum: BusinessPartnerImageType, enumName: 'image_type', default: BusinessPartnerImageType.ORIGIN })
  imageType: string;

  @Column({ nullable: false })
  server: string;

  @Column({ nullable: false })
  imagePath: string;

  @PrimaryColumn('uuid', { nullable: false })
  businessPartner: string;

  @Column({ nullable: false, type: 'enum', enumName: 'images_business_partner_type', enum: BusinessPartnerType })
  businessPartnerType: string;

  @Column({ type: 'enum', enum: BusinessPartnerStatus, enumName: 'image_status', default: BusinessPartnerStatus.ACTIVE })
  imageStatus: BusinessPartnerStatus;
}
