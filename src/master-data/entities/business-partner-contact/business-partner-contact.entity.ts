import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>ToMany } from 'typeorm';
import { BusinessPartnerCommunication } from '../business-partner-communication/business-partner-communication.entity';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerContactRole, BusinessPartnerStatus, BusinessPartnerType } from '../../constants/business-partner.enum';
import { BusinessPartnerImage } from '../business-partner-image/business-partner-image.entity';
import { BusinessPartnerOperatingHour } from '../business-partner-operating-hour/business-partner-operating-hour.entity';
import { BusinessPartnerGeoLocation } from '../business-partner-geo-location/business-partner-geo-location.entity';
import { BusinessPartnerDetail } from '../business-partner-detail/business-partner-detail.entity';

@Entity('business_partner_contacts')
export class BusinessPartnerContact extends BaseSQLEntity {
  @Column({ comment: 'Legal Name or First Name', nullable: false })
  businessPartnerContactName1: string;

  @Column({ comment: 'Trading Name  or Last Name', nullable: true })
  businessPartnerContactName2: string;

  @Column({ nullable: false, unique: true })
  businessPartnerContactKey: string;

  @Column({
    comment: 'contact, sales_rep, call_center',
    type: 'enum',
    enumName: 'business_partner_contact_type',
    enum: BusinessPartnerType,
    nullable: false,
    default: BusinessPartnerType.CONTACT,
  })
  businessPartnerContactType: string;

  @Column({ nullable: true })
  businessPartnerContactDescription: string;

  @Column({ comment: 'following user role', type: 'enum', enum: BusinessPartnerContactRole, enumName: 'business_partner_contact_roles', nullable: false })
  businessPartnerContactPersonRole: BusinessPartnerContactRole;

  @Column({ comment: 'example: DISTRIBUTOR, ADMIN, DSR, REP, MEMBER, ...', nullable: true })
  businessPartnerContactPersonJobTitle: string;

  @Column({ type: 'enum', enum: BusinessPartnerStatus, enumName: 'business_partner_contact_status', default: BusinessPartnerStatus.ACTIVE })
  businessPartnerContactStatus: BusinessPartnerStatus;

  @OneToMany(() => BusinessPartnerCommunication, (partner) => partner.businessPartner)
  communications: BusinessPartnerCommunication[];

  @OneToMany(() => BusinessPartnerImage, (partner) => partner.businessPartner)
  images: BusinessPartnerImage[];

  @OneToMany(() => BusinessPartnerOperatingHour, (partner) => partner.businessPartner)
  operatingHours: BusinessPartnerOperatingHour[];

  @OneToMany(() => BusinessPartnerGeoLocation, (partner) => partner.businessPartner)
  geoGraphicalLocations: BusinessPartnerGeoLocation[];

  @OneToMany(() => BusinessPartnerDetail, (detail) => detail.businessPartner)
  details: BusinessPartnerDetail[];
}
