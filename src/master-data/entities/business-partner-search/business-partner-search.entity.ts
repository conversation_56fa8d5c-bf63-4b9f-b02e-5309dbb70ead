import { Column, Entity, Index } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerRelationType } from '../../constants/business-partner.enum';

export interface BusinessPartnerRelationData {
  type: BusinessPartnerRelationType;
  ids: string[]; //Currently: save Partner Key - not UUID
}

@Entity('business_partner_searches')
export class BusinessPartnerSearch extends BaseSQLEntity {
  @Column({ type: 'uuid', unique: true, nullable: false })
  @Index()
  businessPartnerId: string;

  @Column()
  @Index()
  businessPartnerType: string;

  @Column({ nullable: true })
  @Index()
  name1: string;

  @Column({ nullable: true })
  @Index()
  name2: string;

  @Column({ nullable: true })
  @Index()
  phone: string;

  @Column({ nullable: true })
  @Index()
  email: string;

  @Column({ nullable: true })
  @Index()
  key: string;

  @Column('jsonb', { nullable: true })
  relations: BusinessPartnerRelationData[];
}
