import { Column, Entity, Index, PrimaryColumn } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerStatus, BusinessPartnerType } from '../../constants/business-partner.enum';

@Entity('business_partner_geo_locations')
@Index(['businessPartner', 'businessPartnerType'])
export class BusinessPartnerGeoLocation extends BaseSQLEntity {
  @Column({ nullable: true })
  street: string;

  @Column({ nullable: true })
  houseNumber: string;

  @Column({ nullable: true })
  postalCode: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  region: string;

  @Column({ nullable: true })
  regionKey: string;

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  countryKey: string;

  @Column({ nullable: true })
  longitude: string;

  @Column({ nullable: true })
  latitude: string;

  @Column({ nullable: true })
  validToDate: string;

  @Column({ nullable: true })
  validFromDate: string;

  @Column({ nullable: true })
  geographicalLocationRole: string;

  @PrimaryColumn('uuid', { nullable: false })
  businessPartner: string;

  @Column({ nullable: false, type: 'enum', enumName: 'geo_location_business_partner_type', enum: BusinessPartnerType })
  businessPartnerType: string;

  @Column({ type: 'enum', enumName: 'location_status', enum: BusinessPartnerStatus, default: BusinessPartnerStatus.ACTIVE })
  locationStatus: BusinessPartnerStatus;

  @Column({ nullable: true })
  timezone: string;
}
