import { Column, Entity, Index, PrimaryColumn } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerType, DayOfWeekShort } from '../../constants/business-partner.enum';

@Entity('business_partner_operating_hours')
@Index(['businessPartner', 'businessPartnerType'])
export class BusinessPartnerOperatingHour extends BaseSQLEntity {
  @PrimaryColumn('uuid', { nullable: false })
  businessPartner: string;

  @Column({ nullable: false, type: 'enum', enumName: 'operating_hours_business_partner_type', enum: BusinessPartnerType })
  businessPartnerType: string;

  @Column({ type: 'enum', enum: DayOfWeekShort, enumName: 'operating_hour_day' })
  day: DayOfWeekShort;

  @Column({ type: 'time' })
  openTime: string;

  @Column({ type: 'time' })
  closeTime: string;

  @Column({ default: false })
  isClosed: boolean;
}
