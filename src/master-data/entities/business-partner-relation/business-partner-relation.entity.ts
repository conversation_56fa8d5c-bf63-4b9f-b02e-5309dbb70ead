import { BeforeInsert, BeforeUpdate, Column, Entity, Index, PrimaryColumn, Unique } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerRelationType, BusinessPartnerStatus } from '../../constants/business-partner.enum';

@Entity('business_partner_relations')
@Index(['businessPartner1', 'businessPartner2', 'businessPartnerRelationType'], { unique: true, where: '"isDeleted"=false' })
@Index(['businessPartnerRelationValidFromDate', 'businessPartnerRelationValidToDate'])
@Unique(['id'])
export class BusinessPartnerRelation extends BaseSQLEntity {
  @PrimaryColumn('uuid', { nullable: false })
  businessPartner1: string;

  @PrimaryColumn('uuid', { nullable: false })
  businessPartner2: string;

  @Column({ type: 'enum', enum: BusinessPartnerRelationType, enumName: 'business_partner_relation_type' })
  businessPartnerRelationType: BusinessPartnerRelationType;

  @Column({ type: 'timestamptz', default: '1900-01-01 07:00:00+00' })
  businessPartnerRelationValidFromDate: Date;

  @Column({ type: 'timestamptz', default: '1900-01-01 07:00:00+00' })
  businessPartnerRelationValidToDate: Date;

  @Column({ type: 'enum', enum: BusinessPartnerStatus, enumName: 'business_partner_relation_status', default: BusinessPartnerStatus.ACTIVE })
  businessPartnerRelationStatus: BusinessPartnerStatus;

  @BeforeUpdate()
  @BeforeInsert()
  updateRelationExpiredDate() {
    if (!this.businessPartnerRelationValidFromDate) {
      this.businessPartnerRelationValidFromDate = new Date('1900-01-01 07:00:00+00');
    }

    if (!this.businessPartnerRelationValidToDate) {
      this.businessPartnerRelationValidToDate = new Date('9999-01-01 07:00:00+00');
    }
  }
}
