import { Column, Entity, Index, PrimaryColumn } from 'typeorm';
import { BaseSQLEntity } from '../../../shared/basesql.entity';
import { BusinessPartnerStatus, BusinessPartnerType } from '../../constants/business-partner.enum';

@Entity('vendors')
@Index(['businessPartner', 'businessPartnerType'])
export class Vendor extends BaseSQLEntity {
  @Column()
  vendorName: string;

  @Column()
  vendorDescription: string;

  @Column({ type: 'enum', enum: BusinessPartnerStatus, default: BusinessPartnerStatus.ACTIVE })
  vendorStatus: string;

  @PrimaryColumn('uuid', { nullable: false })
  businessPartner: string;

  @Column({ nullable: true, type: 'enum', enumName: 'vendor_business_partner_type', enum: BusinessPartnerType })
  businessPartnerType: string;
}