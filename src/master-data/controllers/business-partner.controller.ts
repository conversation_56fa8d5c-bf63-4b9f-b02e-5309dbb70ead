import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  forwardRef,
  Get,
  Inject,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Post,
  Put,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { BusinessPartnerDistributorService } from '../services/business-partner-distributor.service';
import { ApiBadRequestResponse, ApiBearerAuth, ApiConsumes, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiResponse } from 'src/shared/response/api-response';
import { ConstantRoles } from 'src/utils/constants/role';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { BusinessPartnerDto } from '../dtos/business-partner.dto';
import { I18n, I18nContext } from 'nestjs-i18n';
import { ApiException } from '../../shared/api-exception.model';
import { FileInterceptor } from '@nestjs/platform-express';
import { CurrentUser } from '../../shared/decorator/current-user.decorator';
import { User } from '../../users/schemas/user.schema';
import * as xlsx from 'xlsx';
import { WorkBook } from 'xlsx';
import { DistributorDepotMapping } from '../constants/distributor.mapping';
import { DistributorDepotMappingsType } from '../constants/distributor.type';
import { BusinessPartnerDepotDto } from '../dtos/business-partner-depot.dto';
import { SearchDto } from '../dtos/search.dto';
import { FilesService } from '../../files/services';
import { isEmptyObjectOrArray } from '../../utils';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from '../../shared/guards/roles.guard';

@ApiTags('Business Partner')
@Controller('api/business-partners')
@ApiHeader({ name: 'locale', description: 'en' })
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BusinessPartnerController {
  constructor(
    private readonly service: BusinessPartnerDistributorService,

    @Inject(forwardRef(() => FilesService))
    private readonly _fileService: FilesService,
  ) {}

  @Post('distributors')
  @Roles(ConstantRoles.SUPER_USER)
  async create(@Body() dto: BusinessPartnerDto, @I18n() i18n: I18nContext) {
    const newDistributor = await this.service.createDistributor(dto, i18n);
    const imagePaths = (newDistributor?.images || []).map((image) => `${image?.server}/${image?.imagePath}`);
    if (!isEmptyObjectOrArray(imagePaths)) {
      this._fileService.removeImagesExpiredDate(imagePaths);
    }
    return new ApiResponse(newDistributor);
  }

  @Get('distributors/:id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async findById(@Param('id') id: string, @I18n() i18n: I18nContext) {
    const distributor = await this.service.findDistributorByIdWithAllRelations(id, i18n);
    return new ApiResponse(distributor);
  }

  @Put('distributors/:id')
  @Roles(ConstantRoles.SUPER_USER)
  async update(@Param('id') id: string, @Body() dto: BusinessPartnerDto, @I18n() i18n: I18nContext) {
    const distributor = await this.service.checkExistAndUpdateDistributor(id, dto, i18n);
    const imagePaths = (distributor?.images || []).map((image) => `${image?.server}/${image?.imagePath}`);
    if (!isEmptyObjectOrArray(imagePaths)) {
      this._fileService.removeImagesExpiredDate(imagePaths);
    }
    return new ApiResponse(distributor);
  }

  @Delete('distributors/:id')
  @Roles(ConstantRoles.SUPER_USER)
  async deactivateDistributor(@Param('id') id: string, @I18n() i18n: I18nContext) {
    const deactivatedDistributor = await this.service.checkExistAndDeleteDistributor(id, i18n);
    return new ApiResponse(deactivatedDistributor);
  }

  @Post('distributors/search')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  @ApiBadRequestResponse({ type: ApiException })
  async searchDistributors(@Body() searchProductDto: SearchDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    return this.service.searchDistributors(searchProductDto);
  }

  @Post('import')
  @ApiBadRequestResponse({ type: ApiException })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiOperation({
    summary: 'Upload distributors and depots',
  })
  async importBusinessPartnerDistributor(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 20 * 1024 * 1024 })],
      }),
    )
    file: Express.Multer.File,
    @CurrentUser() currentUser: User,
    @I18n() i18n: I18nContext,
  ) {
    const workbook: WorkBook = xlsx.read(file.buffer, { type: 'buffer' });
    const mappingResult: DistributorDepotMappingsType = {
      distributors: [],
      depots: [],
      contacts: [],
      depotLocations: [],
      contactDepots: [],
    };

    Object.keys(DistributorDepotMapping).forEach((sheetName) => {
      let formattedSheetName = sheetName?.trim();
      if (sheetName === 'depot-locations') {
        formattedSheetName = 'depotLocations';
      } else if (sheetName === 'contact-depot') {
        formattedSheetName = 'contactDepots';
      }
      if (workbook.SheetNames.includes(sheetName)) {
        const sheet = workbook.Sheets[sheetName];
        const rawData = xlsx.utils.sheet_to_json(sheet);
        if (rawData.length > 0) {
          const trimmedHeaders = Object.keys(rawData[0]).reduce((acc, key) => {
            acc[key.trim()] = key;
            return acc;
          }, {} as Record<string, string>);

          rawData.forEach((row: any) => {
            Object.keys(row).forEach((key) => {
              if (trimmedHeaders[key.trim()] && key.trim() !== key) {
                row[key.trim()] = row[key];
                delete row[key];
              }
            });
          });
        }
        mappingResult[formattedSheetName] = rawData.map((row) => {
          const mappedRow: Record<string, any> = {};
          const trimmedHeaders = Object.keys(DistributorDepotMapping).reduce((acc, key) => {
            acc[key.trim()] = DistributorDepotMapping[key?.trim()];
            return acc;
          }, {} as Record<string, any>);

          const mapping = trimmedHeaders[sheetName.trim()];

          Object.entries(mapping).forEach(([key, excelField]) => {
            let value = row[excelField.toString()?.trim()];

            // Format phone numbers if field contains 'phone' or 'tel' in its name
            if (value && ['phone_number', 'mobile_phone'].includes(excelField.toString().toLowerCase()) && !value.toString().startsWith('+')) {
              value = `+${value}`;
            }

            mappedRow[key?.trim()] = value;
          });

          return mappedRow;
        });
      }
    });

    //Start Update
    const result = await this.service.importBusinessPartnerDistributorDepot(mappingResult, i18n);
    return new ApiResponse(result);
  }

  @Post('distributors/export')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  async exportOutletMenuTemplate(@I18n() i18n: I18nContext) {
    const file = await this.service.exportDistributorsData(i18n);
    return new ApiResponse(file);
  }

  @Post('depots/search')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  @ApiBadRequestResponse({ type: ApiException })
  async searchDepot(@Body() searchProductDto: SearchDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    if (this.isSuperUser(currentUser)) {
      return this.service.searchDepot(searchProductDto);
    }
    const depotIds = currentUser?.businessPartnerRelations?.depotIds || [];
    return this.service.searchDepot({
      ...searchProductDto,
      depotIds,
    });
  }

  @Post('depots')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async createDepot(@Body() dto: BusinessPartnerDepotDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    if (!this.isSuperUser(currentUser)) {
      const distributorIds: string[] = currentUser?.businessPartnerRelations?.distributorIds;
      const distributorPartnerKey = dto.distributorPartnerKey;
      const distributor = await this.service.findByExternalId(distributorPartnerKey);
      if (isEmptyObjectOrArray(distributorIds) || !distributorIds.includes(distributor?.id)) {
        throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
      }
    }
    const newDepot = await this.service.createDepot(dto, i18n, true);
    const imagePaths = (newDepot?.images || []).map((image) => `${image?.server}/${image?.imagePath}`);
    if (!isEmptyObjectOrArray(imagePaths)) {
      this._fileService.removeImagesExpiredDate(imagePaths);
    }
    return new ApiResponse(newDepot);
  }

  @Get('depots/:id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async findDepotById(@Param('id') id: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    const depotIds: string[] = currentUser?.businessPartnerRelations?.depotIds || [];
    if (!this.isSuperUser(currentUser) && (isEmptyObjectOrArray(depotIds) || !depotIds.includes(id))) {
      throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
    }
    const depot = await this.service.findDepotByIdWithAllRelations(id, i18n);
    return new ApiResponse(depot);
  }

  @Put('depots/:id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async updateDepot(@Param('id') id: string, @Body() dto: BusinessPartnerDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    const depotIds: string[] = currentUser?.businessPartnerRelations?.depotIds || [];
    if (!this.isSuperUser(currentUser) && (isEmptyObjectOrArray(depotIds) || !depotIds.includes(id))) {
      throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
    }
    const depot = await this.service.checkExistAndUpdateDepot(id, dto, i18n);
    const imagePaths = (depot?.images || []).map((image) => `${image?.server}/${image?.imagePath}`);
    if (!isEmptyObjectOrArray(imagePaths)) {
      this._fileService.removeImagesExpiredDate(imagePaths);
    }
    return new ApiResponse(depot);
  }

  @Delete('depots/:id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async deactivateDepot(@Param('id') id: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User | any) {
    const depotIds: string[] = currentUser?.businessPartnerRelations?.depotIds || [];
    if (!this.isSuperUser(currentUser) && (isEmptyObjectOrArray(depotIds) || !depotIds.includes(id))) {
      throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
    }
    const deactivatedDepot = await this.service.checkExistAndDeleteDepot(id, i18n);
    return new ApiResponse(deactivatedDepot);
  }

  isSuperUser(currentUser: any) {
    return (currentUser?.roles || []).includes(ConstantRoles.SUPER_USER);
  }
}
