import { BadRequestException, Body, Controller, Get, HttpException, HttpStatus, Param, Post, Res, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { BusinessPartnerRequestService } from '../services/business-partner-request.service';
import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';
import { TokenVerifyGuard } from '../../auth/decorator/guard/token-verify.guard';
import * as _ from 'lodash';
import { BusinessPartnerRelationCommunication, BusinessPartnerStatus } from '../constants/business-partner.enum';
import { BusinessPartnerCommunication } from '../entities/business-partner-communication/business-partner-communication.entity';
import { BusinessPartnerRequestReviewSubmitDto } from '../dtos/business-partner-request-review-submit.dto';
import { I18nContext } from 'nestjs-i18n';
import { ReviewStatus } from '../constants/outlet.enum';
import { ApiResponse } from '../../shared/response/api-response';

@ApiTags('BusinessPartnerRequestForm')
@Controller('business-partners/request')
export class BusinessPartnerRequestFormController {
  constructor(private readonly service: BusinessPartnerRequestService) {}

  private generateOperatingHoursHtml(operatingHours: any[]): string {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    let html = '';

    days.forEach((day) => {
      const dayData = operatingHours?.find((oh) => oh.day === day);
      const isClosed = dayData?.isClosed ?? true;
      const openTime = dayData?.openTime?.substring(0, 5) ?? '08:00';
      const closeTime = dayData?.closeTime?.substring(0, 5) ?? '17:00';
      const opacity = isClosed ? 'opacity-30' : '';

      html += `
        <tr class="border-b">
          <td class="py-3">${day}</td>
          <td class="py-3">
            <div class="flex items-center gap-2 ${opacity}">
              <button class="relative items-center inline-flex h-[22px] w-10 flex-shrink-0 rounded-full border-[1.5px] transition-colors duration-200 ease-in-out focus:outline-none" 
                      style="background-color:${isClosed ? '#cdcdcd' : '#22c55e'};border-color:${isClosed ? '#cdcdcd' : '#22c55e'}"
                      role="switch" type="button" tabindex="0" aria-checked="${!isClosed}">
                <span class="sr-only">Use setting</span>
                <span aria-hidden="true" style="background-color:#ffffff"
                      class="${
                        isClosed ? 'translate-x-1' : 'translate-x-5'
                      } pointer-events-none inline-block h-3.5 w-3.5 transform rounded-full transition duration-200 ease-in-out">
                </span>
              </button> 
              <span class="hidden xl:block">${isClosed ? 'Closed' : 'Open'}</span>
            </div>
          </td>
          <td class="py-3 text-right text-[13px]">
            <div class="flex items-center gap-1">
              <span class="hidden xl:flex items-center justify-center shrink-0 w-4 h-4 bg-oms-green rounded-full ${opacity}">
                <span class="w-1.5 h-1.5 rounded-full bg-white"></span>
              </span>
              <input type="time" readonly="" class="min-w-[80px]" value="${openTime}">
              <span class="px-2">-</span>
              <input type="time" readonly="" class="min-w-[80px]" value="${closeTime}">
            </div>
          </td>
        </tr>`;
    });

    return `
      <script src="/js/approve-customer.js"></script>
      <table class="xl:table-fixed w-full text-[#6B7280]">
        <tbody>
          ${html}
        </tbody>
      </table>`;
  }

  private replaceAllPlaceholders(html: string): string {
    const placeholderRegex = /{{([^}]+)}}/g;
    let match;

    while ((match = placeholderRegex.exec(html)) !== null) {
      const placeholder = match[0];
      if (html.includes(placeholder)) {
        html = html.replace(new RegExp(placeholder, 'g'), 'null');
      }
    }

    return html;
  }

  @Get('review-form/:id')
  @UseGuards(TokenVerifyGuard)
  async getApprovalForm(@Param('id') requestId: string, @Param('status') status: string, @Res() res: Response) {
    try {
      // Get request detail
      const requestDetail = await this.service.findOne({ where: { id: requestId } });

      // Read HTML template
      const templatePath = path.join(process.cwd(), 'public/templates/approve-customer-form.html');
      let htmlContent = fs.readFileSync(templatePath, 'utf8');

      // Get first image for photo
      const firstImage: any = _.get(requestDetail, 'images[0]', {});
      const photoUrl = firstImage?.server && firstImage?.imagePath ? `${firstImage?.server}/${firstImage?.imagePath}` : '/images/no-image.png';

      // Get communication details
      const communications = (requestDetail?.communications || []) as BusinessPartnerCommunication[];
      const email = _.get(
        communications.find((c) => c.communicationType === BusinessPartnerRelationCommunication.EMAIL),
        'communicationValue',
        '-',
      );
      const phone = _.get(
        communications.find((c) => c.communicationType === BusinessPartnerRelationCommunication.TEL),
        'communicationValue',
        '-',
      );

      // Get geographical location details
      const geoLocation: any = _.get(requestDetail, 'geoGraphicalLocations[0]', {});

      // Get customer details
      const customer: any = _.get(requestDetail, 'customers[0]', {});

      // Generate operating hours HTML
      const operatingHoursHtml = this.generateOperatingHoursHtml(requestDetail?.operatingHours || []);

      // Replace values in template
      htmlContent = htmlContent
        .replace(/{{BASE_URL}}/g, process.env.BASE_URL || '-')
        .replace(/{{businessPartnerKey}}/g, requestDetail?.businessPartnerKey || '-')
        .replace(/{{businessPartnerContact}}/g, requestDetail?.businessPartnerName1 || '-')
        .replace(/{{businessPartnerDepotKey}}/g, requestDetail?.businessPartnerDepotKey || '-')
        .replace(/{{businessPartnerContactAdmin}}/g, requestDetail?.businessPartnerContactAdmin || '-')
        .replace(/{{businessPartnerPhoto}}/g, photoUrl)
        .replace(/{{businessPartnerName1}}/g, requestDetail?.businessPartnerName1 || '-')
        .replace(/{{businessPartnerName2}}/g, requestDetail?.businessPartnerName2 || '-')
        .replace(/{{status}}/g, requestDetail?.status || '-')
        .replace(/{{businessPartnerType}}/g, requestDetail?.businessPartnerType || '-')
        .replace(/{{businessPartnerEmail}}/g, email)
        .replace(/{{businessPartnerPhone}}/g, phone)
        .replace(/{{businessPartnerDescription}}/g, requestDetail?.businessPartnerDescription || '-')
        .replace(/{{businessPartnerContactExternalId}}/g, requestDetail?.businessPartnerKey || '-')
        .replace(/{{LocationType}}/g, geoLocation?.locationType || '-')
        .replace(/{{Street}}/g, geoLocation?.street || '-')
        .replace(/{{HouseNumber}}/g, geoLocation?.houseNumber || '-')
        .replace(/{{PostalCode}}/g, geoLocation?.postalCode || '-')
        .replace(/{{City}}/g, geoLocation?.city || '-')
        .replace(/{{CountryCode}}/g, geoLocation?.countryIsoCode || '-')
        .replace(/{{Region}}/g, geoLocation?.region || '-')
        .replace(/{{Latitude}}/g, geoLocation?.latitude || '-')
        .replace(/{{Longitude}}/g, geoLocation?.longitude || '-')
        .replace(/{{Channel}}/g, customer?.customerChannel || '-')
        .replace(/{{SubChannel}}/g, customer?.subChannel || '-')
        .replace(/{{Classification}}/g, customer?.classification || '-')
        .replace(/{{BusinessOrganizationalSegment}}/g, customer?.businessOrganizationalSegment || '-')
        .replace(/{{SaleArea}}/g, customer?.saleArea || '-')
        .replace(/{{SaleSection}}/g, customer?.saleSection || '-')
        .replace(/{{SaleSector}}/g, customer?.saleSector || '-')
        .replace(/{{TradingEndDate}}/g, customer?.tradingEndDate || '-')
        .replace(/{{businessPartnerPaymentTermOld}}/g, customer?.tradingEndDate || '-')
        .replace(/{{businessPartnerPaymentTerm}}/g, customer?.tradingEndDate || '-')
        .replace(/{{businessPartnerCustomerGroup}}/g, customer?.tradingEndDate || '-')
        .replace(/{{businessPartnerValidRegion}}/g, customer?.tradingEndDate || '-')
        .replace(/{{businessPartnerStockLocationKey}}/g, customer?.tradingEndDate || '-')
        .replace(/{{businessPartnerCurrencyKey}}/g, customer?.tradingEndDate || '-')
        .replace(/{{CreditBlock}}/g, customer?.creditBlock || '-');

      // Replace operating hours table
      const operatingHoursTableRegex = /<table class="xl:table-fixed w-full text-\[#6B7280\]">[^]*?<\/table>/;
      htmlContent = htmlContent.replace(operatingHoursTableRegex, operatingHoursHtml);

      // Send HTML response
      res.header('Content-Type', 'text/html');
      return res.send(htmlContent);
    } catch (error) {
      throw new BadRequestException('Failed to generate approval form');
    }
  }

  @Post('/review-submit')
  @UseGuards(TokenVerifyGuard)
  async reviewBusinessPartnerRequest(@Body() dto: BusinessPartnerRequestReviewSubmitDto, i18n: I18nContext) {
    const { id, note, status } = dto;

    if (![ReviewStatus.DECLINED, ReviewStatus.APPROVED].includes(status)) {
      throw new HttpException(await i18n.t('Invalid status. Only APPROVED or REJECTED are allowed.'), HttpStatus.BAD_REQUEST);
    }

    if (status.toUpperCase() === ReviewStatus.DECLINED && !note) {
      throw new HttpException(await i18n.t('Invalid note. Please enter a note.'), HttpStatus.BAD_REQUEST);
    }

    const request = await this.service.findOne({ where: { id } });
    if (!request) {
      throw new HttpException(await i18n.t('Invalid status. Only APPROVED or REJECTED are allowed.'), HttpStatus.BAD_REQUEST);
    }

    const requestStatus = (status === ReviewStatus.APPROVED && BusinessPartnerStatus.ACTIVE) || BusinessPartnerStatus.REJECTED;

    const response = await this.service.save({ id, businessPartnerNote: note, status: requestStatus, isDeleted: false });
    if (response?.status.toUpperCase() === BusinessPartnerStatus.ACTIVE) {
      await this.service.approveBusinessPartnerRequest(response);
    }

    return new ApiResponse(response);
  }
}
