import { Body, Controller, Delete, ForbiddenException, forwardRef, Get, Inject, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from '../../shared/guards/roles.guard';
import { I18n, I18nContext } from 'nestjs-i18n';
import { SearchDto } from '../dtos/search.dto';
import { ApiException } from 'src/shared/api-exception.model';
import { BusinessPartnersContactService } from '../services/business-partners-contact.service';
import { BusinessPartnerContactDto } from '../dtos/business-partner-contact.dto';
import { ApiResponse } from '../../shared/response/api-response';
import { ConstantRoles } from 'src/utils/constants/role';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { BusinessPartnersService } from '../services/business-partners.service';
import { In } from 'typeorm';
import { isSuperUser } from '../../shared/helpers';
import { BusinessPartnerContactRole } from '../constants/business-partner.enum';

@ApiTags('BusinessPartner')
@Controller('api/business-partners/contact')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BusinessPartnerContactController {
  constructor(
    private readonly service: BusinessPartnersContactService,
    // @Inject(forwardRef(() => FilesService))
    // private readonly _fileService: FilesService,
    @Inject(forwardRef(() => BusinessPartnersService))
    private readonly _businessPartnerService: BusinessPartnersService,
  ) {}

  @Post('search')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async searchContacts(@Body() searchContactDto: SearchDto, @I18n() i18n: I18nContext, @CurrentUser() user) {
    if (isSuperUser(user)) {
      return this.service.searchContacts(searchContactDto);
    }
    const contactIds = user?.businessPartnerRelations?.contactIds || [];
    return this.service.searchContacts({
      ...searchContactDto,
      contactIds,
    });
  }

  @Post('')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async createContact(@Body() dto: BusinessPartnerContactDto, @I18n() i18n: I18nContext, @CurrentUser() user) {
    if (isSuperUser(user)) {
      const newContact = await this.service.createContact(dto, i18n);
      return new ApiResponse(newContact);
    }
    const distributorExternalKeys = dto?.distributorPartnerKeys,
      outletExternalKeys = dto?.outletPartnerKeys;

    if (distributorExternalKeys.length > 0) {
      const distributors = await this._businessPartnerService.find({
        where: {
          businessPartnerKey: In(distributorExternalKeys),
        },
      });
      const distributorIds = user?.businessPartnerRelations?.distributorIds;

      const notAllowedDistributor = distributors.find((d) => !distributorIds.includes(d.id));

      if (notAllowedDistributor) {
        throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
      }
    }

    if (outletExternalKeys.length > 0) {
      const outlets = await this._businessPartnerService.find({
        where: {
          businessPartnerKey: In(outletExternalKeys),
        },
      });
      const outletIds = user?.businessPartnerRelations?.outletIds;

      const notAllowedOutlet = outlets.find((d) => !outletIds.includes(d.id));

      if (notAllowedOutlet) {
        throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
      }
    }

    const newContact = await this.service.createContact(dto, i18n);
    return new ApiResponse(newContact);
  }

  @Get(':id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async findContactById(@Param('id') id: string, @I18n() i18n: I18nContext, @CurrentUser() user) {
    const contactIds: string[] = user?.businessPartnerRelations?.contactIds || [];
    if (!isSuperUser(user) && (contactIds.length < 1 || !contactIds.includes(id))) {
      throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
    }
    const depot = await this.service.findByIdWithAllRelations(id);
    return new ApiResponse(depot);
  }

  @Put(':id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async updateContact(@Param('id') id: string, @Body() dto: BusinessPartnerContactDto, @I18n() i18n: I18nContext, @CurrentUser() user) {
    const contactIds: string[] = user?.businessPartnerRelations?.contactIds || [];
    if (!isSuperUser(user) && (contactIds.length < 1 || !contactIds.includes(id))) {
      throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
    }
    const depot = await this.service.checkExistAndUpdateContact(id, dto, i18n);
    return new ApiResponse(depot);
  }

  @Delete(':id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async deactivateContact(@Param('id') id: string, @I18n() i18n: I18nContext, @CurrentUser() user) {
    const contactIds: string[] = user?.businessPartnerRelations?.contactIds || [];
    if (!isSuperUser(user) && (contactIds.length < 1 || !contactIds.includes(id))) {
      throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
    }
    const deactivatedDepot = await this.service.checkExistAndDeleteContact(id, i18n);
    return new ApiResponse(deactivatedDepot);
  }
}
