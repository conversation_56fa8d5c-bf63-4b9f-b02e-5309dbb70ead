import { CurrenciesService } from '../services/currencies.service';
import { CountriesService } from '../services/countries.service';
import { ApiBearerAuth, ApiHeader, ApiTags } from '@nestjs/swagger';
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiResponse } from '../../shared/response/api-response';
import { JwtAuthGuard } from 'src/auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { ConstantRoles } from 'src/utils/constants/role';
import { Roles } from 'src/shared/decorator/roles.decorator';

@ApiTags('Business Partner Configs')
@Controller('api/business-partners/configs')
@ApiHeader({ name: 'locale', description: 'en' })
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BusinessPartnerConfigController {
  constructor(private readonly currenciesService: CurrenciesService, private readonly countriesService: CountriesService) {}

  @Get('all')
  @Roles(
    ConstantRoles.SUPER_USER,
    ConstantRoles.CALL_CENTER_MANAGEMENT,
    ConstantRoles.DISTRIBUTOR_ADMIN,
    ConstantRoles.CALL_CENTER,
    ConstantRoles.SALE_REP,
    ConstantRoles.AREA_SALES_REP_MANAGER,
  )
  async getAllConfigs() {
    const [currencies, countries] = await Promise.all([this.currenciesService.findAll(), this.countriesService.findAll()]);
    return new ApiResponse({ currencies, countries });
  }
}
