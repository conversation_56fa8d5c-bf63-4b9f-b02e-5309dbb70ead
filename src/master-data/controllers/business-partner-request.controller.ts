import { BadRequestException, Body, Controller, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from '../../shared/guards/roles.guard';
import { ApiResponse } from '../../shared/response/api-response';
import { BusinessPartnerRequestService } from '../services/business-partner-request.service';
import { I18n, I18nContext } from 'nestjs-i18n';
import { SearchDto } from '../dtos/search.dto';
import { CountDto } from '../dtos/count.dto';
import { ConstantRoles } from '../../utils/constants/role';
import { Roles } from '../../shared/decorator/roles.decorator';
import { BusinessPartnerStatus } from '../constants/business-partner.enum';
import { BusinessPartnerRequestDto } from '../dtos/business-partner-request.dto';

@ApiTags('BusinessPartnerRequest')
@Controller('api/business-partners/request')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BusinessPartnerRequestController {
  constructor(private readonly service: BusinessPartnerRequestService) {}

  @Post('/search')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async getBusinessPartnerRequests(@Body() dto: SearchDto, @I18n() i18n: I18nContext) {
    const response = await this.service.searchBusinessPartnerRequest(dto, i18n);
    return new ApiResponse(response);
  }

  @Post('/count')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async getBusinessPartnerRequestsCount(@Body() dto: CountDto, @I18n() i18n: I18nContext) {
    const response = await this.service.countBusinessPartnerRequest(dto, i18n);
    return new ApiResponse(response);
  }

  @Get(':id')
  async getBusinessPartnerRequestDetail(@Param() requestId: string) {
    const businessPartnerRequest = await this.service.getBusinessPartnerRequestDetail(requestId);
    return new ApiResponse(businessPartnerRequest);
  }

  @Put(':id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async updateBusinessPartnerRequest(@Param() requestId: string, @Body() dto: BusinessPartnerRequestDto, @I18n() i18n: I18nContext) {
    if (BusinessPartnerStatus.ACTIVE === dto?.status) {
      throw new BadRequestException('business_partner_request.invalid_request_status');
    }
    const response = await this.service.updateBusinessPartnerRequest(requestId, dto);
    return new ApiResponse(response);
  }
}
