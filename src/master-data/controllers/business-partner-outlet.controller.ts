import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  forwardRef,
  Get,
  Inject,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Post,
  Put,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from '../../shared/guards/roles.guard';
import { ApiException } from '../../shared/api-exception.model';
import { FileInterceptor } from '@nestjs/platform-express';
import { Roles } from '../../shared/decorator/roles.decorator';
import { ConstantRoles } from '../../utils/constants/role';
import { CurrentUser } from '../../shared/decorator/current-user.decorator';
import { User } from '../../users/schemas/user.schema';
import { I18n, I18nContext } from 'nestjs-i18n';
import * as xlsx from 'xlsx';
import { WorkBook } from 'xlsx';
import { OutletMapping } from '../constants/outlet.mapping';
import { OutletMappingsType } from '../constants/outlet.type';
import { BusinessPartnerOutletService } from '../services/business-partner-outlet.service';
import { ApiResponse } from '../../shared/response/api-response';
import { BusinessPartnerOutletDto } from '../dtos/business-partner-outlet.dto';
import { FilesService } from '../../files/services/files.service';
import { isEmptyObjectOrArray } from '../../utils';
import { isSuperUser } from 'src/shared/helpers/role.helper';
import { OutletSearchDto } from '../dtos/outlet-search.dto';

@ApiTags('BusinessPartner')
@Controller('api/business-partners/outlet')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BusinessPartnerOutletController {
  constructor(
    private readonly service: BusinessPartnerOutletService,

    @Inject(forwardRef(() => FilesService))
    private readonly _fileService: FilesService,
  ) {}

  @Post('import')
  @ApiBadRequestResponse({ type: ApiException })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  @ApiOperation({
    summary: 'Upload outlet and contacts',
  })
  async importBusinessPartnerOutletContact(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 20 * 1024 * 1024 })],
      }),
    )
    file: Express.Multer.File,
    @CurrentUser() currentUser: User,
    @I18n() i18n: I18nContext,
  ) {
    const workbook: WorkBook = xlsx.read(file.buffer, { type: 'buffer' });
    const mappingResult: OutletMappingsType = {
      outlets: [],
      depots: [],
      contacts: [],
      segments: [],
    };

    Object.keys(OutletMapping).forEach((sheetName) => {
      const formattedSheetName = sheetName?.trim();
      if (workbook.SheetNames.includes(sheetName)) {
        const sheet = workbook.Sheets[sheetName];
        const rawData = xlsx.utils.sheet_to_json(sheet);
        if (rawData.length > 0) {
          const trimmedHeaders = Object.keys(rawData[0]).reduce((acc, key) => {
            acc[key.trim()] = key;
            return acc;
          }, {} as Record<string, string>);

          rawData.forEach((row: any) => {
            Object.keys(row).forEach((key) => {
              if (trimmedHeaders[key.trim()] && key.trim() !== key) {
                row[key.trim()] = row[key];
                delete row[key];
              }
            });
          });
        }
        mappingResult[formattedSheetName] = rawData.map((row) => {
          const mappedRow: Record<string, any> = {};
          const trimmedHeaders = Object.keys(OutletMapping).reduce((acc, key) => {
            acc[key.trim()] = OutletMapping[key?.trim()];
            return acc;
          }, {} as Record<string, any>);

          const mapping = trimmedHeaders[sheetName.trim()];

          Object.entries(mapping).forEach(([key, excelField]) => {
            let value = row[excelField.toString()?.trim()];

            // Format phone numbers if field contains 'phone' or 'tel' in its name
            if (value && ['phone_number', 'mobile_phone'].includes(excelField.toString().toLowerCase()) && !value.toString().startsWith('+')) {
              value = `+${value}`;
            }

            mappedRow[key?.trim()] = value;
          });

          return mappedRow;
        });
      }
    });

    // Start Update
    const result = await this.service.importBusinessPartnerOutletContact(mappingResult, i18n);
    return new ApiResponse(result);
  }
  @Post('search')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async searchOutlet(@Body() dto: OutletSearchDto, @I18n() i18n: I18nContext, @CurrentUser() user) {
    if (isSuperUser(user)) {
      const newOutlet = await this.service.searchOutlet(dto, i18n);
      return new ApiResponse(newOutlet);
    }
    const outletIds = user?.businessPartnerRelations?.outletIds || [];
    const newOutlet = await this.service.searchOutlet(
      {
        ...dto,
        outletIds,
      },
      i18n,
    );
    return new ApiResponse(newOutlet);
  }

  @Post('')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async create(@Body() dto: BusinessPartnerOutletDto, @I18n() i18n: I18nContext, @CurrentUser() user) {
    const newOutlet = await this.service.createOutlet(dto, true);
    const imagePaths = (newOutlet?.images || []).map((image) => `${image?.server}/${image?.imagePath}`);
    if (!isEmptyObjectOrArray(imagePaths)) {
      this._fileService.removeImagesExpiredDate(imagePaths);
    }
    return new ApiResponse(newOutlet);
  }

  @Get(':id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async findById(@Param('id') id: string, @I18n() i18n: I18nContext, @CurrentUser() user) {
    const outletIds: string[] = user?.businessPartnerRelations?.outletIds || [];
    if (!isSuperUser(user) && (outletIds.length < 1 || !outletIds.includes(id))) {
      throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
    }
    const outlet = await this.service.findOutletByIdWithAllRelations(id, i18n);
    return new ApiResponse(outlet);
  }

  @Put(':id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async update(@Param('id') id: string, @Body() dto: BusinessPartnerOutletDto, @I18n() i18n: I18nContext, @CurrentUser() user) {
    const outletIds: string[] = user?.businessPartnerRelations?.outletIds || [];
    if (!isSuperUser(user) && (outletIds.length < 1 || !outletIds.includes(id))) {
      throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
    }
    const outlet = await this.service.checkExistAndUpdateOutlet(id, dto, i18n);
    const imagePaths = (outlet?.images || []).map((image) => `${image?.server}/${image?.imagePath}`);
    if (!isEmptyObjectOrArray(imagePaths)) {
      this._fileService.removeImagesExpiredDate(imagePaths);
    }
    return new ApiResponse(outlet);
  }

  @Delete(':id')
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async deactivateOutlet(@Param('id') id: string, @I18n() i18n: I18nContext, @CurrentUser() user) {
    const outletIds: string[] = user?.businessPartnerRelations?.outletIds || [];
    if (!isSuperUser(user) && (outletIds.length < 1 || !outletIds.includes(id))) {
      throw new ForbiddenException(await i18n.translate('user.unauthorized_resource'));
    }
    const deactivatedOutlet = await this.service.checkExistAndDeleteOutlet(id, i18n);
    return new ApiResponse(deactivatedOutlet);
  }

  @Post('export')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.CALL_CENTER_MANAGEMENT, ConstantRoles.AREA_SALES_REP_MANAGER)
  async exportOutletMenuTemplate(@I18n() i18n: I18nContext, @CurrentUser() user) {
    const file = await this.service.exportOutletData(i18n);
    return new ApiResponse(file);
  }
}
