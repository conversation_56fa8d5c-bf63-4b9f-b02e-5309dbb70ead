import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessPartner } from './entities/business-partner/business-partner.entity';
import { BusinessPartnerDetail } from './entities/business-partner-detail/business-partner-detail.entity';
import { BusinessPartnerContact } from './entities/business-partner-contact/business-partner-contact.entity';
import { BusinessPartnerRelation } from './entities/business-partner-relation/business-partner-relation.entity';
import { Vendor } from './entities/vendor/vendor.entity';
import { BusinessPartnerCustomerSalesOrganization } from './entities/business-partner-customer/business-partner-customer-sales-organization.entity';
import { BusinessPartnerOperatingHour } from './entities/business-partner-operating-hour/business-partner-operating-hour.entity';
import { BusinessPartnerImage } from './entities/business-partner-image/business-partner-image.entity';
import { BusinessPartnerGeoLocation } from './entities/business-partner-geo-location/business-partner-geo-location.entity';
import { BusinessPartnerCustomer } from './entities/business-partner-customer/business-partner-customer.entity';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';
import { BusinessPartnerController } from './controllers/business-partner.controller';
import { BusinessPartnerDistributorService } from './services/business-partner-distributor.service';
import { BusinessPartnersService } from './services/business-partners.service';
import { BusinessPartnerCommunicationService } from './services/business-partners-communication.service';
import { BusinessPartnersImageService } from './services/business-partners-image.service';
import { BusinessPartnerDetailService } from './services/business-partners-detail.service';
import { BusinessPartnersGeoLocationService } from './services/business-partners-geo-location.service';
import { BusinessPartnerOperatingHourService } from './services/business-partners-operating-hour.service';
import { BusinessPartnerCommunication } from './entities/business-partner-communication/business-partner-communication.entity';
import { BusinessPartnerOutletController } from './controllers/business-partner-outlet.controller';
import { BusinessPartnerRelationService } from './services/business-partners-relation.service';
import { BusinessPartnersContactService } from './services/business-partners-contact.service';
import { FilesModule } from '../files/files.module';
import { BusinessPartnerContactController } from './controllers/business-partner-contact.controller';
import { BusinessPartnerOutletService } from './services/business-partner-outlet.service';
import { BusinessPartnersCustomerService } from './services/business-partners-customer.service';
import { BusinessPartnerRequest } from './entities/business-partner/business-partner-request.entity';
import { BusinessPartnerRequestService } from './services/business-partner-request.service';
import { BusinessPartnerRequestController } from './controllers/business-partner-request.controller';
import { CountriesService } from './services/countries.service';
import { CurrenciesService } from './services/currencies.service';
import { Currencies } from './entities/config/currencies.entity';
import { Countries } from './entities/config/countries.entity';
import { SendGridService } from 'src/shared/mail/sendgrid';
import { BusinessPartnerSyncService } from './services/business-partner-sync.service';
import { DistributorModule } from '../distributor/distributor.module';
import { OutletsModule } from '../outlets/outlets.module';
import { BusinessPartnerConfigController } from './controllers/business-partner-config.controller';
import { BusinessPartnerRequestFormController } from './controllers/business-partner-request-form.controller';
import { QueueLog } from './entities/queue/queue-log.entity';
import { QueueLogsService } from './services/queue-logs.service';
import { ThirdPartiesModule } from '../third-parties/third-parties.module';
import { BusinessPartnerSearch } from './entities/business-partner-search/business-partner-search.entity';
import { BusinessPartnerSearchService } from './services/business-partner-search.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BusinessPartner,
      BusinessPartnerDetail,
      BusinessPartnerContact,
      BusinessPartnerRelation,
      Vendor,
      BusinessPartnerContact,
      BusinessPartnerCustomerSalesOrganization,
      BusinessPartnerOperatingHour,
      BusinessPartnerImage,
      BusinessPartnerGeoLocation,
      BusinessPartnerCustomer,
      BusinessPartnerCommunication,
      BusinessPartnerRequest,
      BusinessPartnerSearch,
      Countries,
      Currencies,
      QueueLog,
    ]),
    forwardRef(() => UsersModule),
    forwardRef(() => FilesModule),
    forwardRef(() => DistributorModule),
    forwardRef(() => OutletsModule),
    forwardRef(() => UsersModule),
    forwardRef(() => ThirdPartiesModule),
    AuthModule,
  ],
  exports: [
    BusinessPartnerDistributorService,
    BusinessPartnersService,
    BusinessPartnerCommunicationService,
    BusinessPartnersImageService,
    BusinessPartnerDetailService,
    BusinessPartnersGeoLocationService,
    BusinessPartnerOperatingHourService,
    BusinessPartnerRelationService,
    BusinessPartnersContactService,
    BusinessPartnerOutletService,
    BusinessPartnersCustomerService,
    BusinessPartnerRequestService,
    CountriesService,
    CurrenciesService,
    BusinessPartnerSyncService,
    BusinessPartnerSearchService,
    QueueLogsService,
  ],
  controllers: [
    BusinessPartnerController,
    BusinessPartnerOutletController,
    BusinessPartnerContactController,
    BusinessPartnerRequestController,
    BusinessPartnerConfigController,
    BusinessPartnerRequestFormController,
  ],
  providers: [
    BusinessPartnerDistributorService,
    BusinessPartnersService,
    BusinessPartnerCommunicationService,
    BusinessPartnersImageService,
    BusinessPartnerDetailService,
    BusinessPartnersGeoLocationService,
    BusinessPartnerOperatingHourService,
    BusinessPartnerRelationService,
    BusinessPartnersContactService,
    BusinessPartnerOutletService,
    BusinessPartnersCustomerService,
    BusinessPartnerRequestService,
    CountriesService,
    CurrenciesService,
    SendGridService,
    BusinessPartnerSyncService,
    BusinessPartnerSearchService,
    QueueLogsService,
  ],
})
export class MasterDataModule {}
