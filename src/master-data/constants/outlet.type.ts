export type OutletType = {
  businessPartnerKey: string;
  businessPartnerName1: string;
  businessPartnerName2: string;
  thirdPartyCustomerKey: string;
  communicationName: string;
  communicationNumber: string;
  communicationEmail: string;
  businessPartnerType: string;
  businessPartnerDescription: string;
  currencyKey: string;
  saleArea: string;
  saleSection: string;
  saleSector: string;
  operatingHours: string;
  paymentTerm: string;
  tradingEndDate: string;
  locationType: string;
  deliveringSiteKey: string;
  addressLine: string;
  street: string;
  houseNumber: string;
  region: string;
  userGroups: string;
  postalCode: string;
  city: string;
  longitude: string;
  latitude: string;
  countryIsoCode: string;
  status: string;
};

export type DepotType = {
  businessPartnerDepotKey: string;
  businessPartnerKey: string;
  startDate: string;
  endDate: string;
  isDeleted: string;
};

export type ContactType = {
  businessPartnerContactKey: string;
  businessPartnerContactName1: string;
  businessPartnerContactName2: string;
  middleName: string;
  businessPartnerKey: string;
  businessPartnerContactPersonRole: string;
  businessPartnerContactStatus: string;
  communicationNumberTel: string;
  communicationNumberTelHome: string;
  communicationEmail: string;
  communicationName: string;
  addressLine: string;
  street: string;
  houseNumber: string;
  postalCode: string;
  city: string;
  countryIsoCode: string;
  region: string;
  regionIsoCode: string;
  businessPartnerContactPersonJobTitle: string;
  birthday: string;
  isDeleted: string;
};

export type SegmentType = {
  businessPartnerKey: string;
  customerChannel: string;
  customerChannelCode: string;
  customerSubChannel: string;
  customerSubChannelCode: string;
  businessSegment: string;
  businessSegmentCode: string;
  outletClassification: string;
  businessOrganizationalSegment: string;
};

export type OutletMappingType = {
  outlets: OutletType;
  depots: DepotType;
  contacts: ContactType;
  segments: SegmentType;
};

export type OutletMappingsType = {
  outlets: OutletType[];
  depots: DepotType[];
  contacts: ContactType[];
  segments: SegmentType[];
};
