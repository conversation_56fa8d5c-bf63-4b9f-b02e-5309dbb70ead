export type DepotsType = {
  businessPartnerKey: string;
  businessPartnerDistributorKey: string;
  businessPartnerName1: string;
  businessPartnerName2: string;
  defaultCurrencyCode: string;
  // defaultSalesChannelId: string;
  deliveringSiteKey: string;
  businessPartnerStatus: string;
  businessUnit: string;
  geoLocation: string;
  communicationNumber: string;
  // holidays: string;
  operatingHours: string;
};

export type DistributorType = {
  businessPartnerKey: string;
  businessPartnerName1: string;
  businessPartnerName2: string;
  businessPartnerType: string;
  geoLocation: string;
  businessPartnerStatus: string;
  taxNumber: string;
  timezone: string;
  communicationNumber: string;
};
export type ContactType = {
  businessPartnerContactKey: string;
  businessPartnerContactName1: string;
  businessPartnerContactName2: string;
  middleName: string;
  businessPartnerKey: string;
  businessPartnerContactPersonRole: string;
  businessPartnerContactStatus: string;
  communicationNumberTel: string;
  communicationNumberTelHome: string;
  communicationEmail: string;
  addressLine: string;
  street: string;
  houseNumber: string;
  postalCode: string;
  city: string;
  countryIsoCode: string;
  region: string;
  regionIsoCode: string;
  businessPartnerContactPersonJobTitle: string;
  birthday: string;
};
export type DepotLocationType = {
  businessPartnerDepotKey: string;
  locationExternalId: string;
};
export type ContactDepotType = {
  businessPartnerDepotKey: string;
  businessPartnerContactKey: string;
  isDeleted: string;
};
export type DistributorDepotMappingType = {
  distributors: DistributorType;
  depots: DepotsType;
  contacts: ContactType;
  'depot-locations': DepotLocationType;
  'contact-depot': ContactDepotType;
};

export type DistributorDepotMappingsType = {
  distributors: DistributorType[];
  depots: DepotsType[];
  contacts: ContactType[];
  depotLocations: DepotLocationType[];
  contactDepots: ContactDepotType[];
};
