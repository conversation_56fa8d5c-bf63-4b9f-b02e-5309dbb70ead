import { BusinessPartner } from '../entities/business-partner/business-partner.entity';
import { DistributorDepotMappingType } from './distributor.type';
import { extractBusinessPartnerOperatingHoursData, extractBusinessPartnerCommunicationData, extractBusinessPartnerRelatedData } from '../../utils/helpers/businessPartner';
import { BusinessPartnerRelationCommunication } from './business-partner.enum';

export const DistributorDepotMapping: DistributorDepotMappingType = {
  distributors: {
    businessPartnerKey: 'external_id',
    businessPartnerName1: 'trading_name',
    businessPartnerName2: 'legal_name',
    businessPartnerType: 'type',
    geoLocation: 'address',
    businessPartnerStatus: 'status',
    taxNumber: 'tax_identification_number',
    timezone: 'timezone',
    communicationNumber: 'phone_number',
  },
  depots: {
    businessPartnerKey: 'external_id',
    businessPartnerDistributorKey: 'distributor_external_id',
    businessPartnerName1: 'trading_name',
    businessPartnerName2: 'legal_name',
    defaultCurrencyCode: 'default_currency_code',
    // defaultSalesChannelId: 'default_sales_channel_id',
    deliveringSiteKey: 'default_location_id',
    businessPartnerStatus: 'status',
    businessUnit: 'business_unit',
    geoLocation: 'address',
    communicationNumber: 'phone_number',
    // holidays: 'holidays',
    operatingHours: 'operating_hours',
  },
  contacts: {
    businessPartnerContactKey: 'external_id',
    businessPartnerContactName1: 'first_name',
    businessPartnerContactName2: 'last_name',
    middleName: 'middle_name',
    businessPartnerKey: 'distributor_external_id',
    businessPartnerContactPersonRole: 'role',
    businessPartnerContactStatus: 'status',
    communicationNumberTel: 'phone_number',
    communicationNumberTelHome: 'mobile_phone',
    communicationEmail: 'email_address',
    addressLine: 'address_line',
    street: 'street',
    houseNumber: 'house_number',
    postalCode: 'postal_code',
    city: 'city',
    countryIsoCode: 'country_ISO_Code',
    region: 'region',
    regionIsoCode: 'region_ISO_code',
    businessPartnerContactPersonJobTitle: 'job_title',
    birthday: 'date_of_birth',
  },
  'depot-locations': {
    businessPartnerDepotKey: 'depot_external_id',
    locationExternalId: 'location_external_id',
  },
  'contact-depot': {
    businessPartnerDepotKey: 'depot_external_id',
    businessPartnerContactKey: 'contact_external_id',
    isDeleted: 'is_deleted',
  },
};

export const DistributorDepotExportMapping = {
  distributors: {
    external_id: 'businessPartnerKey',
    trading_name: 'businessPartnerName1',
    legal_name: 'businessPartnerName2',
    type: 'businessPartnerType',
    address: (data) => extractBusinessPartnerRelatedData(data, 'details', 'rawAddress'),
    status: 'businessPartnerStatus',
    tax_identification_number: (data) => extractBusinessPartnerRelatedData(data, 'details', 'taxNumber'),
    timezone: (data) => extractBusinessPartnerRelatedData(data, 'details', 'timezone'),
    phone_number: extractBusinessPartnerCommunicationData,
  },
  depots: {
    external_id: 'businessPartnerKey',
    distributor_external_id: 'distributor.businessPartnerKey',
    trading_name: 'businessPartnerName1',
    legal_name: 'businessPartnerName2',
    default_currency_code: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerSalesOrganizations[0].currencyCode'),
    // default_sales_channel_id: 'defaultSalesChannelId',
    default_location_id: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerSalesOrganizations[0].deliveringSiteKey'),
    status: 'businessPartnerStatus',
    business_unit: (data) => extractBusinessPartnerRelatedData(data, 'details', 'businessUnit'),
    address: (data) => extractBusinessPartnerRelatedData(data, 'details', 'rawAddress'),
    phone_number: extractBusinessPartnerCommunicationData,
    // holidays: (data) => extractBusinessPartnerRelatedData(data, 'details', 'holidays'),
    operating_hours: extractBusinessPartnerOperatingHoursData,
  },
  contacts: {
    external_id: 'businessPartnerContactKey',
    first_name: 'businessPartnerContactName1',
    last_name: 'businessPartnerContactName2',
    middle_name: 'middleName',
    distributor_external_id: 'distributorPartnerKeys',
    role: 'businessPartnerContactPersonRole',
    status: 'businessPartnerContactStatus',
    phone_number: extractBusinessPartnerCommunicationData,
    mobile_phone: (data) => extractBusinessPartnerCommunicationData(data, BusinessPartnerRelationCommunication.PHONE),
    email_address: (data) => extractBusinessPartnerCommunicationData(data, BusinessPartnerRelationCommunication.EMAIL),
    address_line: (data) => extractBusinessPartnerRelatedData(data, 'details', 'rawAddress'),
    street: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'street'),
    house_number: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'houseNumber'),
    postal_code: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'postalCode'),
    city: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'city'),
    country_ISO_Code: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'countryKey'),
    region: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'region'),
    region_ISO_code: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'regionKey'),
    job_title: 'businessPartnerContactPersonJobTitle',
    date_of_birth: (data) => extractBusinessPartnerRelatedData(data, 'details', 'birthday'),
  },
  'depot-locations': {
    depot_external_id: 'businessPartnerDepotKey',
    location_external_id: 'locationExternalId',
  },
  'contact-depot': {
    depot_external_id: 'businessPartner2',
    contact_external_id: 'businessPartner1',
    is_deleted: 'isDeleted',
  },
};
