import { extractBusinessPartnerCommunicationData, extractBusinessPartnerOperatingHoursData, extractBusinessPartnerRelatedData } from 'src/utils/helpers/businessPartner';
import { OutletMappingType } from './outlet.type';
import { BusinessPartnerRelationCommunication } from './business-partner.enum';

export const OutletMapping: OutletMappingType = {
  outlets: {
    businessPartnerKey: 'external_id',
    businessPartnerName1: 'trading_name',
    businessPartnerName2: 'legal_name',
    thirdPartyCustomerKey: 'third_party_customer_key',
    communicationName: 'contact_name',
    communicationNumber: 'phone_number',
    communicationEmail: 'email_address',
    businessPartnerType: 'customer_type',
    businessPartnerDescription: 'description',
    currencyKey: 'currency_key',
    saleArea: 'sale_area',
    saleSection: 'sale_section',
    saleSector: 'sale_sector',
    operatingHours: 'operating_hours',
    paymentTerm: 'payment_term',
    tradingEndDate: 'trading_end_date',
    locationType: 'location_type',
    deliveringSiteKey: 'default_location_id',
    addressLine: 'address_line',
    street: 'street',
    houseNumber: 'house_number',
    region: 'region',
    userGroups: 'user_groups',
    postalCode: 'postal_code',
    city: 'city',
    longitude: 'longitude',
    latitude: 'latitude',
    countryIsoCode: 'country_iso_code',
    status: 'status',
  },
  depots: {
    businessPartnerDepotKey: 'depot_external_id',
    businessPartnerKey: 'outlet_external_id',
    startDate: 'start_date',
    endDate: 'end_date',
    isDeleted: 'default_sales_channel_id',
  },
  contacts: {
    businessPartnerContactKey: 'external_id',
    businessPartnerContactName1: 'first_name',
    businessPartnerContactName2: 'last_name',
    middleName: 'middle_name',
    businessPartnerKey: 'outlet_external_id',
    businessPartnerContactPersonRole: 'role',
    businessPartnerContactStatus: 'status',
    communicationNumberTel: 'phone_number',
    communicationNumberTelHome: 'mobile_phone',
    communicationEmail: 'email_address',
    communicationName: 'contact_name',
    addressLine: 'address_line',
    street: 'street',
    houseNumber: 'house_number',
    postalCode: 'postal_code',
    city: 'city',
    countryIsoCode: 'country_ISO_Code',
    region: 'region',
    regionIsoCode: 'region_ISO_code',
    businessPartnerContactPersonJobTitle: 'job_title',
    birthday: 'date_of_birth',
    isDeleted: 'is_deleted',
  },
  segments: {
    businessPartnerKey: 'outlet_external_id',
    customerChannel: 'channel_name',
    customerChannelCode: 'channel_code',
    customerSubChannel: 'sub_channel_name',
    customerSubChannelCode: 'sub_channel_code',
    businessSegment: 'business_segment',
    businessSegmentCode: 'business_segment_code',
    outletClassification: 'classification_name',
    businessOrganizationalSegment: 'business_organizational_segment',
  },
};

export const OutletExportMapping = {
  outlets: {
    external_id: 'businessPartnerKey',
    trading_name: 'businessPartnerName1',
    legal_name: 'businessPartnerName2',
    third_party_customer_key: 'thirdPartyCustomerKey',
    contact_name: (data) => extractBusinessPartnerRelatedData(data, 'communications', 'communicationName'),
    phone_number: extractBusinessPartnerCommunicationData,
    email_address: (data) => extractBusinessPartnerCommunicationData(data, BusinessPartnerRelationCommunication.EMAIL),
    customer_type: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerType'),
    description: 'businessPartnerDescription',
    currency_key: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerSalesOrganizations[0].currencyCode'),
    sale_area: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerSalesOrganizations[0].salesGroup'),
    sale_section: 'saleSection',
    sale_sector: 'saleSector',
    operating_hours: extractBusinessPartnerOperatingHoursData,
    payment_term: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerSalesOrganizations[0].customerBilling.paymentTermsKey'),
    trading_end_date: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerSalesOrganizations[0].tradingEndDate'),
    location_type: 'locationType',
    default_location_id: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerSalesOrganizations[0].deliveringSiteKey'),
    address_line: (data) => extractBusinessPartnerRelatedData(data, 'details', 'rawAddress'),
    street: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'street'),
    house_number: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'houseNumber'),
    region: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'region'),
    user_groups: 'userGroups',
    postal_code: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'postalCode'),
    city: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'city'),
    longitude: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'longitude'),
    latitude: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'latitude'),
    country_iso_code: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'countryKey'),
    status: 'isActive',
  },
  depots: {
    depot_external_id: 'depotBusinessPartnerKey',
    outlet_external_id: 'outletBusinessPartnerKey',
    start_date: 'businessPartnerRelationValidFromDate',
    end_date: 'businessPartnerRelationValidToDate',
  },
  contacts: {
    external_id: 'businessPartnerContactKey',
    first_name: 'businessPartnerContactName1',
    last_name: 'businessPartnerContactName2',
    middle_name: 'middleName',
    outlet_external_id: 'outletBusinessPartnerKeys',
    role: 'businessPartnerContactPersonRole',
    status: 'businessPartnerContactStatus',
    phone_number: extractBusinessPartnerCommunicationData,
    mobile_phone: (data) => extractBusinessPartnerCommunicationData(data, BusinessPartnerRelationCommunication.PHONE),
    email_address: (data) => extractBusinessPartnerCommunicationData(data, BusinessPartnerRelationCommunication.EMAIL),
    address_line: (data) => extractBusinessPartnerRelatedData(data, 'details', 'rawAddress'),
    street: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'street'),
    house_number: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'houseNumber'),
    postal_code: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'postalCode'),
    city: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'city'),
    country_ISO_Code: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'countryKey'),
    region: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'region'),
    region_ISO_code: (data) => extractBusinessPartnerRelatedData(data, 'geoGraphicalLocations', 'regionKey'),
    job_title: 'businessPartnerContactPersonJobTitle',
    date_of_birth: (data) => extractBusinessPartnerRelatedData(data, 'details', 'birthday'),
  },
  segments: {
    outlet_external_id: 'businessPartnerKey',
    channel_name: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerChannel'),
    channel_code: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerChannelCode'),
    sub_channel_name: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerSalesOrganizations[0].customerSubChannel'),
    sub_channel_code: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerSalesOrganizations[0].customerSubChannelCode'),
    business_segment: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'businessSegment'),
    business_segment_code: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'businessSegmentCode'),
    classification_name: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'customerSalesOrganizations[0].outletClassification'),
    business_organizational_segment: (data) => extractBusinessPartnerRelatedData(data, 'customers', 'businessOrganizationalSegment'),
  },
};
