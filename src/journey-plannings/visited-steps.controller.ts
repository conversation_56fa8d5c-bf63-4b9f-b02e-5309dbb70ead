import { Body, Controller, ForbiddenException, Get, NotFoundException, Param, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiHeader, ApiTags } from '@nestjs/swagger';
import { I18n, I18nContext } from 'nestjs-i18n';
import { JwtAuthGuard } from 'src/auth/decorator/guard/jwt-auth.guard';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { ApiResponse } from 'src/shared/response/api-response';
import { User } from 'src/users/schemas/user.schema';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { ConstantRoles } from 'src/utils/constants/role';
import { InjectModel } from '@nestjs/mongoose';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from './schemas/outlet-journey-planning.schema';
import { Model } from 'mongoose';
import { UpdateVisitedStepDto } from './dtos/update-visited-step.dto';
import { JourneyPlanVisitedStepsService } from './services/visited-steps.service';

@ApiTags('Visit step')
@ApiHeader({ name: 'locale', description: 'en' })
@Controller('api/journey-plans')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class JourneyPlanVisitedStepsController {
  constructor(private readonly visitedStepsService: JourneyPlanVisitedStepsService) {}

  @Get(':planId/visited-steps')
  @Roles(ConstantRoles.SALE_REP)
  async getJourneyPlanVisitedSteps(@Param('planId') planId: string, @CurrentUser() salesRep: User, @I18n() i18n: I18nContext) {
    const salesRepId = String(salesRep._id);
    const data = await this.visitedStepsService.getVisitedStep({
      planId,
      salesRepId,
      i18n,
    });

    return new ApiResponse(data);
  }

  @Put(':planId/visited-steps')
  @Roles(ConstantRoles.SALE_REP)
  async updateJourneyPlanVisitedSteps(@Param('planId') planId: string, @Body() dto: UpdateVisitedStepDto, @CurrentUser() salesRep: User, @I18n() i18n: I18nContext) {
    const salesRepId = String(salesRep._id);
    const data = await this.visitedStepsService.updateVisitedStep({
      planId,
      dto,
      salesRepId,
      i18n,
    });

    return new ApiResponse(data);
  }
}
