import { OpCos } from 'src/config';

export const transformMissedReason = ({ obj }) => {
  if (!obj?.missedReason) {
    return;
  }
  if (process.env.OPCO == OpCos.Cambodia) {
    const { _id, translations, controllable, reschedulable } = obj.missedReason;
    return { _id, controllable, reschedulable, translations };
  } else {
    const { _id, reasonKey, controllable, reschedulable } = obj.missedReason;
    return { _id, controllable, reschedulable, reasonKey };
  }
};
