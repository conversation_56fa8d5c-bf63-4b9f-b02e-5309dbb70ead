import { Body, Controller, Get, Param, Put, UseGuards } from '@nestjs/common';
import { ApiBearerA<PERSON>, ApiHeader, ApiTags } from '@nestjs/swagger';
import { I18n, I18nContext } from 'nestjs-i18n';
import { JwtAuthGuard } from 'src/auth/decorator/guard/jwt-auth.guard';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { ApiResponse } from 'src/shared/response/api-response';
import { User } from 'src/users/schemas/user.schema';
import { UpdateJourneyPlanVisibilityDto } from './dtos/update-visibility.dto';
import { JourneyPlanVisibilityService } from './services/visibility.service';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { ConstantRoles } from 'src/utils/constants/role';

@ApiTags('Visibility')
@ApiHeader({ name: 'locale', description: 'en' })
@Controller('api/journey-plans')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class JourneyPlanVisibilityController {
  constructor(private readonly visibilityService: JourneyPlanVisibilityService) {}

  @Get(':planId/visibility')
  @Roles(ConstantRoles.SALE_REP)
  async getJourneyPlanVisibility(@Param('planId') planId: string, @CurrentUser() salesRep: User, @I18n() i18n: I18nContext) {
    const visibility = await this.visibilityService.getJourneyPlanVisibility({ planId, salesRepId: String(salesRep._id), i18n });
    return new ApiResponse(visibility);
  }

  @Put(':planId/visibility')
  @Roles(ConstantRoles.SALE_REP)
  async updateJourneyPlanVisibility(@Param('planId') planId: string, @Body() dto: UpdateJourneyPlanVisibilityDto, @CurrentUser() salesRep: User, @I18n() i18n: I18nContext) {
    const visibility = await this.visibilityService.updateJourneyPlanVisibility({ planId, dto, salesRepId: String(salesRep._id), i18n });
    return new ApiResponse(visibility);
  }
}
