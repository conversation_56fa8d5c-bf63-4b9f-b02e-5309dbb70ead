import { ApiModelProperty, ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';

export class UpdateColdStockDto {
  @ApiModelProperty()
  @IsBoolean()
  availableFridge: boolean;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isDedicatedFridge?: boolean;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isProductsInDedicatedFridge?: boolean;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsString({
    each: true,
  })
  imageIds?: string[];
}
