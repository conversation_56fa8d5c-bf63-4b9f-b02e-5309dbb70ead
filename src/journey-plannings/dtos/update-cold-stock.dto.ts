import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';

export class UpdateColdStockDto {
  @ApiProperty()
  @IsBoolean()
  availableFridge: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isDedicatedFridge?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isProductsInDedicatedFridge?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsString({
    each: true,
  })
  imageIds?: string[];
}
