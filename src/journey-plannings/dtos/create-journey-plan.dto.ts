import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

import { Days, Weeks } from '../../distributor/enums';

export class CreateJourneyPlanDto {
  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value?.trim())
  outletId: string;

  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value?.trim())
  saleRepId: string;

  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value?.trim())
  distributorId: string;

  @ApiModelProperty({ default: Weeks.WEEK_1 })
  @IsNumber()
  week: number;

  @ApiModelProperty({ default: Days.DAY_1 })
  @IsNumber()
  day: number;
}
