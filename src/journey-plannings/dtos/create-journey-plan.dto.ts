import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

import { Days, Weeks } from '../../distributor/enums';

export class CreateJourneyPlanDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value?.trim())
  outletId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value?.trim())
  saleRepId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value?.trim())
  distributorId: string;

  @ApiProperty({ default: Weeks.WEEK_1 })
  @IsNumber()
  week: number;

  @ApiProperty({ default: Days.DAY_1 })
  @IsNumber()
  day: number;
}
