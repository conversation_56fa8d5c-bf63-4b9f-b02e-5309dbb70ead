import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsArray, IsEnum, IsString, Matches } from 'class-validator';
import { ConstantCommons } from 'src/utils/constants';
import { VisibilityExecutionStatus } from '../schemas/visibility-execution.schema';

export class CreateVisibilityExecutionDto {
  @ApiModelProperty()
  @IsString()
  readonly name: string;

  @ApiModelProperty()
  @IsString()
  readonly subHeading: string;

  @ApiModelProperty()
  @IsEnum(VisibilityExecutionStatus)
  readonly status: VisibilityExecutionStatus;

  @ApiModelProperty()
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  startDate: string;

  @ApiModelProperty()
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  endDate: string;

  @ApiModelProperty()
  @IsArray()
  @IsString({
    each: true,
  })
  outletIds: string[];

  @ApiModelProperty()
  @IsString()
  depotId: string;
}
