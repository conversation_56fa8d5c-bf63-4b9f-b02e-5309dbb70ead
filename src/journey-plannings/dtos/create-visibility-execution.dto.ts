import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsString, Matches } from 'class-validator';
import { ConstantCommons } from 'src/utils/constants';
import { VisibilityExecutionStatus } from '../schemas/visibility-execution.schema';

export class CreateVisibilityExecutionDto {
  @ApiProperty()
  @IsString()
  readonly name: string;

  @ApiProperty()
  @IsString()
  readonly subHeading: string;

  @ApiProperty()
  @IsEnum(VisibilityExecutionStatus)
  readonly status: VisibilityExecutionStatus;

  @ApiProperty()
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  startDate: string;

  @ApiProperty()
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  endDate: string;

  @ApiProperty()
  @IsArray()
  @IsString({
    each: true,
  })
  outletIds: string[];

  @ApiProperty()
  @IsString()
  depotId: string;
}
