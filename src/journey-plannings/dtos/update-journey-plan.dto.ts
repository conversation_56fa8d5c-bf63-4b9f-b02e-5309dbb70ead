import { printLog } from './../../utils/index';
import { IsNumber } from 'class-validator';
import { IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';
import { IsString } from 'class-validator';
import { Weeks } from './../../distributor/enums/weeks.enum';
import { Days } from './../../distributor/enums/days.enum';
import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';

import { BaseDto } from '../../shared/dtos/base.dto';

export class UpdateJourneyPlanning extends BaseDto {
  @ApiModelProperty()
  @IsString()
  @Transform(({ value }) => value.toString()?.trim())
  cycleId: string;

  @ApiModelProperty()
  @IsString()
  @Transform(({ value }) => value.toString()?.trim())
  journeyPlanId: string;

  @ApiModelProperty()
  @IsString()
  @Transform(({ value }) => value?.trim())
  outletId: string;

  @ApiModelProperty()
  @IsString()
  @Transform(({ value }) => {
    return value.toString()?.trim();
  })
  saleRepId: string;

  @ApiModelProperty()
  @IsString()
  @Transform(({ value }) => {
    return value.toString()?.trim();
  })
  distributorId: string;

  @ApiModelProperty({ default: Weeks.WEEK_1 })
  // @IsNumber()
  week: number;

  @ApiModelProperty({ default: Days.DAY_1 })
  // @IsNumber()
  day: number;
}
