import { printLog } from './../../utils/index';
import { IsNumber } from 'class-validator';
import { IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';
import { IsString } from 'class-validator';
import { Weeks } from './../../distributor/enums/weeks.enum';
import { Days } from './../../distributor/enums/days.enum';
import { ApiProperty } from '@nestjs/swagger';

import { BaseDto } from '../../shared/dtos/base.dto';

export class UpdateJourneyPlanning extends BaseDto {
  @ApiProperty()
  @IsString()
  @Transform(({ value }) => value.toString()?.trim())
  cycleId: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => value.toString()?.trim())
  journeyPlanId: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => value?.trim())
  outletId: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => {
    return value.toString()?.trim();
  })
  saleRepId: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => {
    return value.toString()?.trim();
  })
  distributorId: string;

  @ApiProperty({ default: Weeks.WEEK_1 })
  // @IsNumber()
  week: number;

  @ApiProperty({ default: Days.DAY_1 })
  // @IsNumber()
  day: number;
}
