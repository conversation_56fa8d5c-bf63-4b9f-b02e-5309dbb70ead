import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsString, Matches } from 'class-validator';
import { ConstantCommons } from '../../utils/constants';

export class FetchAdminJourneyPlanDto {
  @ApiModelProperty()
  @IsString()
  query: string;

  @ApiModelProperty()
  @ApiModelProperty({ default: new Date().toISOString() })
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  startTime: string;

  @ApiModelProperty()
  @ApiModelProperty({ default: new Date().toISOString() })
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  endTime: string;
}
