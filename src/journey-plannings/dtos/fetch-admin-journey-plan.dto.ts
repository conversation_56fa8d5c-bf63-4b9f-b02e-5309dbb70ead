import { ApiProperty } from '@nestjs/swagger';
import { IsString, Matches } from 'class-validator';
import { ConstantCommons } from '../../utils/constants';

export class FetchAdminJourneyPlanDto {
  @ApiProperty()
  @IsString()
  query: string;

  @ApiProperty()
  @ApiProperty({ default: new Date().toISOString() })
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  startTime: string;

  @ApiProperty()
  @ApiProperty({ default: new Date().toISOString() })
  @IsString()
  @Matches(new RegExp(ConstantCommons.ISO_DATE_PATTERN), { message: 'Date must be valid and has ISO Format' })
  endTime: string;
}
