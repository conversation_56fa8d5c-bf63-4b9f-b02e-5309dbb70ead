import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsDateString, IsOptional, IsString } from 'class-validator';
import { CancellationReasonType } from '../enums/cancellation-reason-type.enum';

export class MissedVisitReasonDto {
  @ApiProperty({ default: CancellationReasonType.PUBLIC_HOLIDAY })
  readonly reasonKey: string;
}

export class MissedReasonForVisitsDto {
  @ApiProperty({
    type: String,
    isArray: true,
  })
  journeyPlanIds: string[];

  @ApiProperty({ default: CancellationReasonType.PUBLIC_HOLIDAY })
  readonly reasonKey: string;
}

export class MissedReasonForOutletDto {
  @ApiProperty()
  @IsString()
  planId: string;

  @ApiProperty()
  @IsString()
  reasonId: string;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  evidenceImages: Array<string>;
}

export class MissedReasonForOutletsDto {
  @ApiProperty()
  @IsDateString()
  fromDate: Date;

  @ApiProperty()
  @IsDateString()
  toDate: Date;

  @ApiProperty()
  @IsString()
  reasonId: string;
}

export class CountAffectedOutletsDto {
  @ApiProperty()
  @IsDateString()
  fromDate: Date;

  @ApiProperty()
  @IsDateString()
  toDate: Date;
}
