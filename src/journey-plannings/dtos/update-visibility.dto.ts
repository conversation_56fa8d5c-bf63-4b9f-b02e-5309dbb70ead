import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsDate, IsOptional, IsString } from 'class-validator';

export class TaskDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsArray()
  @IsString({
    each: true,
  })
  @ArrayMinSize(1, { message: 'execution.visibility.min_photo' })
  @ArrayMaxSize(5, { message: 'execution.visibility.max_photo' })
  imageIds: string[];

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  quantity?: number;
}

export class UpdateJourneyPlanVisibilityDto {
  @ApiProperty()
  @IsOptional()
  @IsArray()
  @Type(() => TaskDto)
  tasks: TaskDto[];

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  visibilityTime: Date;
}
