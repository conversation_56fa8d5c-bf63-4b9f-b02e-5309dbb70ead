import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { Type } from 'class-transformer';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsDate, IsOptional, IsString } from 'class-validator';

export class TaskDto {
  @ApiModelProperty()
  @IsString()
  id: string;

  @ApiModelProperty()
  @IsArray()
  @IsString({
    each: true,
  })
  @ArrayMinSize(1, { message: 'execution.visibility.min_photo' })
  @ArrayMaxSize(5, { message: 'execution.visibility.max_photo' })
  imageIds: string[];

  @ApiModelProperty()
  @IsOptional()
  @Type(() => Number)
  quantity?: number;
}

export class UpdateJourneyPlanVisibilityDto {
  @ApiModelProperty()
  @IsOptional()
  @IsArray()
  @Type(() => TaskDto)
  tasks: TaskDto[];

  @ApiModelProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  visibilityTime: Date;
}
