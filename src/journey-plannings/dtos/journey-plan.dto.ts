import { Expose, Transform } from 'class-transformer';

import { Outlet } from '../../outlets/schemas/outlet.schema';
import { Distance, GeoLocation } from '../schemas/outlet-journey-planning.schema';
import { transformMissedReason } from '../transforms/missed-reason.transform';
import { transformOutlet } from '../transforms/outlet.transform';
import { transformDistance } from '../transforms/distance-data.transform';
import { MissReason } from 'src/miss-reasons/schemas/miss-reason.schema';

export class JourneyPlanDto {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  @Transform(({ obj }) => (!obj.rescheduled ? obj.displayDay : obj.day))
  day: Date;

  @Expose()
  rescheduled: boolean;

  @Expose()
  @Transform(({ obj }) => (obj.rescheduled ? obj.displayDay : obj.day))
  rescheduledDay: Date;

  @Expose()
  cancel: boolean;

  @Expose()
  cancelDay: Date;

  @Expose()
  cancellationReason: string;

  @Expose()
  startOfWeek: Date;

  @Expose()
  endOfWeek: Date;

  @Expose()
  startOfCycleTime: Date;

  @Expose()
  endOfCycleTime: Date;

  @Expose()
  @Transform(transformOutlet)
  outlet: Partial<Outlet>;

  @Expose()
  outletDataKey?: string;

  // @Expose()
  // hasStartVisitLabel: boolean;

  @Expose()
  @Transform(({ obj }) => {
    if (obj.location) {
      return {
        coordinates: [obj.location.latitude, obj.location.longitude],
      };
    }
    return null;
  })
  geoLocation: GeoLocation;

  @Expose()
  location: any;

  @Expose()
  @Transform(({ obj }) => obj.visitStatus)
  visitStatus: string;

  @Expose()
  canSetReason: boolean;

  @Expose()
  assignedTimes: Date[];

  @Expose()
  startTimeFrame: string;

  @Expose()
  timeInterval: number;

  @Expose()
  isWillMiss?: boolean;

  @Expose()
  @Transform(transformMissedReason)
  missedReason?: Partial<MissReason>;

  @Expose()
  @Transform(({ obj }) => obj.priority)
  priority: number;

  @Expose()
  @Transform(transformDistance)
  distance?: Partial<Distance>;
}
