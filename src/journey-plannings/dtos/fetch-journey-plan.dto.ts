import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { SortOrder } from 'mongoose';

import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class JourneyPlanSortOrder extends SortOrderDto {
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  ucc: SortOrder;
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletName: SortOrder;
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletClass: SortOrder;
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  area: SortOrder;
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  cycleWeek: SortOrder;
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  weekDay: SortOrder;
}

export class FetchJourneyPlanDto extends PaginationDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  distributorId: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'plan.cycle_id_required' })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  cycleId: string;

  @ApiPropertyOptional({ type: () => JourneyPlanSortOrder })
  sort: JourneyPlanSortOrder;
}

export class ExportJourneyPlan {
  @ApiProperty()
  distributorId: string;

  @ApiProperty()
  cycleId: string;

  @ApiPropertyOptional({ type: () => JourneyPlanSortOrder })
  sort: JourneyPlanSortOrder;
}
