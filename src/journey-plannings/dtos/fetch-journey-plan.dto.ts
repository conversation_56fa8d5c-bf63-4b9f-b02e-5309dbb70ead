import { ApiModelProperty, ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { SortOrder } from 'mongoose';

import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { PaginationDto } from '../../shared/dtos/pagination.dto';

export class JourneyPlanSortOrder extends SortOrderDto {
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  ucc: SortOrder;
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletName: SortOrder;
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  outletClass: SortOrder;
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  area: SortOrder;
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  cycleWeek: SortOrder;
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  weekDay: SortOrder;
}

export class FetchJourneyPlanDto extends PaginationDto {
  @ApiModelPropertyOptional()
  @IsString()
  @IsOptional()
  distributorId: string;

  @ApiModelProperty()
  @IsNotEmpty({ message: 'plan.cycle_id_required' })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  cycleId: string;

  @ApiModelPropertyOptional({ type: () => JourneyPlanSortOrder })
  sort: JourneyPlanSortOrder;
}

export class ExportJourneyPlan {
  @ApiModelProperty()
  distributorId: string;

  @ApiModelProperty()
  cycleId: string;

  @ApiModelPropertyOptional({ type: () => JourneyPlanSortOrder })
  sort: JourneyPlanSortOrder;
}
