import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';

import { BaseDto } from '../../shared/dtos/base.dto';

export class CreateCycleDto extends BaseDto {
  @ApiModelProperty()
  readonly cycleName: string;
  @ApiModelProperty()
  readonly year: number;
}

export class ListJourneyPlanningDto extends BaseDto {
  @ApiModelProperty()
  readonly distributorId: string;
  @ApiModelProperty()
  readonly cycleId: string;
}
