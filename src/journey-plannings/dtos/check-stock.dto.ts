import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class GetCheckStock {
  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  outletId: string;

  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  journeyPlanId: string;
}

export class CheckStockData {
  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  sku: string;

  @ApiModelProperty()
  @IsNumber()
  @IsNotEmpty()
  check_stock_quantity: number;

  @ApiModelProperty()
  @IsNumber()
  @IsOptional()
  selling_price?: number;
}

export class SyncCheckStock {
  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  outletId: string;

  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  journeyPlanId: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CheckStockData)
  listProductsChecked: CheckStockData[];

  @ApiModelProperty()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CheckStockData)
  listCompetitorsChecked: CheckStockData[];
}
