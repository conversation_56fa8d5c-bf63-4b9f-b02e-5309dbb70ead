import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsNotEmpty, isN<PERSON>ber, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class GetCheckStock {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  outletId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  journeyPlanId: string;
}

export class CheckStockData {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  sku: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  check_stock_quantity: number;
}

export class SyncCheckStock {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  outletId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  journeyPlanId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CheckStockData)
  listProductsChecked: CheckStockData[];

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CheckStockData)
  listCompetitorsChecked: CheckStockData[];
}
