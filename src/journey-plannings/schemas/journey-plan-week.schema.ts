import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types, Document } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { JourneyPlanCycle } from './journey-plan-cycle.schema';
import { WeekNameType } from '../enums/week-type.enum';

export type JourneyPlanWeekDocument = JourneyPlanWeek & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class JourneyPlanWeek extends BaseSchema {
  @Prop({ enum: WeekNameType, default: WeekNameType.WEEK_1, index: true })
  weekName: string;

  @Prop({ index: true })
  startTime: Date;

  @Prop({ type: Types.ObjectId, ref: JourneyPlanCycle.name, index: true })
  cycle: JourneyPlanCycle;
}

export const JourneyPlanWeekSchema = SchemaFactory.createForClass(JourneyPlanWeek);
