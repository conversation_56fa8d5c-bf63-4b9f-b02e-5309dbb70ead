import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, ObjectId, Types } from 'mongoose';

import { Outlet } from '../../outlets/schemas/outlet.schema';
import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';
import { VisitStatus } from '../enums/visit-status.enum';
import { JourneyPlanWeek } from './journey-plan-week.schema';
import { MissReason } from 'src/miss-reasons/schemas/miss-reason.schema';

export class GeoLocation {
  type: string;
  coordinates: number[];
}

export class Location {
  latitude: number;
  longitude: number;
}

export class Distance {
  text: string;
  value: number;
}

export enum JourneyPlanVisitStep {
  CHECK_STOCK = 'CHECK_STOCK',
  COLD_STOCK = 'COLD_STOCK',
  PLACE_ORDER = 'PLACE_ORDER',
  DO_VISIBILITY = 'DO_VISIBILITY',
}

export class CheckStock {
  sku: string;
  check_stock_quantity: number;
  selling_price?: number;
}

export type OutletJourneyPlanningDocument = OutletJourneyPlanning & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class OutletJourneyPlanning extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: JourneyPlanWeek.name, index: true })
  week: JourneyPlanWeek;

  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  @Prop({ type: Types.ObjectId, ref: MissReason.name, index: true })
  missedReason?: MissReason;

  @Prop({ type: Types.ObjectId })
  outletReport?: ObjectId;

  @Prop({ index: true })
  day: Date;

  @Prop({ default: false, index: true })
  rescheduled: boolean;

  @Prop({ index: true })
  rescheduledDay: Date;

  @Prop()
  startVisitDate: Date;

  @Prop()
  visitedDay: Date;

  @Prop({ default: false, index: true })
  cancel: boolean;

  @Prop()
  cancelDay: Date;

  @Prop()
  cancellationReason: string;

  @Prop({ enum: VisitStatus, default: VisitStatus.START_VISIT, index: true })
  visitStatus: string;

  // Don't use this property, only support 1.0
  @Prop({ type: GeoLocation, index: true })
  geoLocation: GeoLocation;

  @Prop({ type: Location })
  location: Location;

  @Prop()
  locationRange: number; //metre

  @Prop({ unique: false, index: true, default: 0 })
  priority: number;

  @Prop({ type: Distance, index: true, default: null })
  distance: Distance;

  displayDay?: string;

  @Prop({ default: [] })
  visitedSteps?: JourneyPlanVisitStep[];

  @Prop({ type: mongoose.Schema.Types.Mixed })
  checkStock?: {
    lastUpdate: Date;
    listProductsChecked: CheckStock[];
    listCompetitorsChecked: CheckStock[];
  };

  @Prop({ type: Boolean, default: false })
  hasOrder: boolean;

  @Prop({ index: true, default: null })
  depotId: string;
}

const OutletJourneyPlanningSchema = SchemaFactory.createForClass(OutletJourneyPlanning);

OutletJourneyPlanningSchema.virtual('displayDay').get(function (this: OutletJourneyPlanningDocument) {
  return this.rescheduled ? this.rescheduledDay : this.day;
});

export { OutletJourneyPlanningSchema };

OutletJourneyPlanningSchema.index({ saleRep: 1, visitStatus: 1, rescheduled: 1, day: 1 });
OutletJourneyPlanningSchema.index({ saleRep: 1, visitStatus: 1, rescheduled: 1, rescheduledDay: 1 });
