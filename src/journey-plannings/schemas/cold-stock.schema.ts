import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from 'src/shared/schemas/base.schema';
import { OutletJourneyPlanning } from './outlet-journey-planning.schema';
import { Files } from 'src/files/schemas';
import { User } from 'src/users/schemas/user.schema';
import { Outlet } from 'src/outlets/schemas/outlet.schema';

export type ColdStockDocument = ColdStock & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class ColdStock extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: OutletJourneyPlanning.name, index: true })
  journeyPlan: OutletJourneyPlanning;

  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({
    type: Boolean,
  })
  availableFridge: boolean;

  @Prop({
    type: Boolean,
  })
  isDedicatedFridge: boolean;

  @Prop({
    type: Boolean,
  })
  isProductsInDedicatedFridge: boolean;

  @Prop({
    type: [Types.ObjectId],
    ref: Files.name,
    index: true,
    default: [],
  })
  images: Files[];
}

export const ColdStockSchema = SchemaFactory.createForClass(ColdStock);
