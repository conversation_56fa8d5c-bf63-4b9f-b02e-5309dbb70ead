import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { BaseSchema } from 'src/shared/schemas/base.schema';
import { CancellationReasonType, OutletAbsenceReason, OutletAbsenceReasonWarningMessage } from '../enums/cancellation-reason-type.enum';

export type JourneyPlanMissedReasonDocument = JourneyPlanMissedReason & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class JourneyPlanMissedReason extends BaseSchema {
  @Prop({ unique: true, index: true })
  reasonKey: CancellationReasonType | OutletAbsenceReason;

  @Prop({
    default: true,
  })
  controllable: boolean;

  @Prop({
    default: false,
  })
  reschedulable: boolean;

  @Prop({
    default: false,
  })
  isForMultipleOutlets: boolean;

  @Prop()
  warningMessageKey?: OutletAbsenceReasonWarningMessage;

  @Prop({ unique: true, index: true })
  priority: number;
}

export const JourneyPlanMissedReasonSchema = SchemaFactory.createForClass(JourneyPlanMissedReason);
