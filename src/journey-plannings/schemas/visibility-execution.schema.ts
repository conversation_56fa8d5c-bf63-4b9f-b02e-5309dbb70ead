import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from 'src/shared/schemas/base.schema';
import { Outlet } from 'src/outlets/schemas/outlet.schema';

export type VisibilityExecutionDocument = VisibilityExecution & Document;

export enum VisibilityExecutionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum OutletChannel {
  MODERN_ON_TRADE = 'MODERN_ON_TRADE',
  MODERN_OFF_TRADE = 'MODERN_OFF_TRADE',
  TRADITIONAL_ON_TRADE = 'TRADITIONAL_ON_TRADE',
  TRADITIONAL_OFF_TRADE = 'TRADITIONAL_OFF_TRADE',
  MODERN_ON_TRADE_N = 'Modern On',
  MODERN_OFF_TRADE_N = 'Modern Off',
  TRADITIONAL_ON_TRADE_N = 'Traditional On',
  TRADITIONAL_OFF_TRADE_N = 'Traditional Off',
  MODERN_ON_TRADE_N_T = 'Modern On Trade',
  MODERN_OFF_TRADE_N_T = 'Modern Off Trade',
  TRADITIONAL_ON_TRADE_N_T = 'Traditional On Trade',
  TRADITIONAL_OFF_TRADE_N_T = 'Traditional Off Trade',
}

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class VisibilityExecution extends BaseSchema {
  @Prop({
    type: String,
    required: true,
    index: true,
  })
  name: string;

  @Prop({
    type: String,
    required: true,
  })
  subHeading: string;

  @Prop({
    enum: VisibilityExecutionStatus,
    default: VisibilityExecutionStatus.ACTIVE,
  })
  status: VisibilityExecutionStatus;

  @Prop({
    required: true,
  })
  startDate: Date;

  @Prop({
    required: true,
  })
  endDate: Date;

  @Prop({ type: [Types.ObjectId], ref: Outlet.name })
  outlets: Outlet[];

  @Prop({
    required: true,
  })
  depotId: string;
}

export const VisibilityExecutionSchema = SchemaFactory.createForClass(VisibilityExecution);

VisibilityExecutionSchema.index({ name: 1, subHeading: 1, startDate: 1, endDate: 1 });

VisibilityExecutionSchema.index({ updatedAt: -1, createdAt: -1 });
