import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { CycleNameType } from '../enums/cycle-type.enum';

export type JourneyPlanCycleDocument = JourneyPlanCycle & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class JourneyPlanCycle extends BaseSchema {
  @Prop({ enum: CycleNameType, default: CycleNameType.CYCLE_1, index: true })
  cycleName: string;

  @Prop({ index: true })
  year: number;
}

export const JourneyPlanCycleSchema = SchemaFactory.createForClass(JourneyPlanCycle);
