import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from 'src/shared/schemas/base.schema';
import { OutletJourneyPlanning } from './outlet-journey-planning.schema';
import { Files } from 'src/files/schemas';
import { MissReason } from 'src/miss-reasons/schemas/miss-reason.schema';

export type JourneyPlanMissedReasonHistoryDocument = JourneyPlanMissedReasonHistory & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class JourneyPlanMissedReasonHistory extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: OutletJourneyPlanning.name, index: true })
  journeyPlan: OutletJourneyPlanning;

  @Prop({
    type: Types.ObjectId,
    ref: MissReason.name,
    index: true,
  })
  missedReason: MissReason;

  @Prop({
    type: [Types.ObjectId],
    ref: Files.name,
    index: true,
    default: [],
  })
  evidenceImages: Files[];
}

export const JourneyPlanMissedReasonHistorySchema = SchemaFactory.createForClass(JourneyPlanMissedReasonHistory);
