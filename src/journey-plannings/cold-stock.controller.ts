import { Body, Controller, Get, Param, Put, UseGuards } from '@nestjs/common';
import { ApiB<PERSON>er<PERSON><PERSON>, ApiHeader, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/decorator/guard/jwt-auth.guard';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { User } from 'src/users/schemas/user.schema';
import { ColdStocksService } from './services/cold-stocks.service';
import { ApiResponse } from 'src/shared/response/api-response';
import { I18n, I18nContext } from 'nestjs-i18n';
import { UpdateColdStockDto } from './dtos/update-cold-stock.dto';

@ApiTags('Cold Stock')
@ApiHeader({ name: 'locale', description: 'en' })
@Controller('api/cold-stocks')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ColdStockController {
  constructor(private readonly coldStocksService: ColdStocksService) {}

  @Get('journey-plans/:planId')
  async getColdStock(@Param('planId') planId: string, @CurrentUser() salesRep: User, @I18n() i18n: I18nContext) {
    const data = await this.coldStocksService.getColdStockByJourneyPlan({ planId, salesRepId: String(salesRep._id), i18n });
    return new ApiResponse(data);
  }

  @Put('journey-plans/:planId')
  async updateColdStock(@Param('planId') planId: string, @Body() dto: UpdateColdStockDto, @CurrentUser() salesRep: User, @I18n() i18n: I18nContext) {
    const data = await this.coldStocksService.updateColdStockByJourneyPlan({ planId, dto, salesRepId: String(salesRep._id), i18n });
    return new ApiResponse(data);
  }
}
