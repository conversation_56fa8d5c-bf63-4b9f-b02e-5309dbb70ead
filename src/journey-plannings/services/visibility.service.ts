import { ForbiddenException, forwardRef, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';
import { JourneyPlanVisitStep, OutletJourneyPlanning, OutletJourneyPlanningDocument } from '../schemas/outlet-journey-planning.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { SaleRepExecutionVisibility, SaleRepExecutionVisibilityDocument } from 'src/sale-rep/schemas';
import { UpdateJourneyPlanVisibilityDto } from '../dtos/update-visibility.dto';
import { VisitStatus } from '../enums/visit-status.enum';
import { VisibilityExecution, VisibilityExecutionDocument, VisibilityExecutionStatus } from '../schemas/visibility-execution.schema';
import * as moment from 'moment-timezone';
import { Files, FilesDocument } from 'src/files/schemas';
import { OmsRepReportsCalculatorsService } from 'src/oms/services/rep-reports-calculator.service';
import { User, UserDocument } from 'src/users/schemas/user.schema';
import { OutletJourneyPlanningService } from './outlet-journey-planning.service';
import { FilesService } from 'src/files/services';
import { isEmptyObjectOrArray } from '../../utils';
import { UserActionsService } from '../../users/services/user-actions.service';

@Injectable()
export class JourneyPlanVisibilityService {
  constructor(
    @InjectModel(OutletJourneyPlanning.name)
    private readonly planModel: Model<OutletJourneyPlanningDocument>,
    @InjectModel(SaleRepExecutionVisibility.name)
    private readonly visibilityModel: Model<SaleRepExecutionVisibilityDocument>,
    @InjectModel(VisibilityExecution.name)
    private readonly visibilityExecutionModel: Model<VisibilityExecutionDocument>,
    @InjectModel(User.name)
    private readonly salesRepModel: Model<UserDocument>,
    @InjectModel(Files.name)
    private readonly fileModel: Model<FilesDocument>,
    @Inject(forwardRef(() => OmsRepReportsCalculatorsService))
    private readonly omsRepReportsCalculatorsService: OmsRepReportsCalculatorsService,
    @Inject(forwardRef(() => OutletJourneyPlanningService))
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    @Inject(forwardRef(() => FilesService))
    private readonly filesService: FilesService,
    private readonly _userActionsService: UserActionsService,
  ) {}

  async getJourneyPlanVisibility({ planId, salesRepId, i18n }: { planId: string; salesRepId: string; i18n: I18nContext }) {
    const plan = await this.checkPermission({ planId, salesRepId, i18n });

    const visibility = await this.visibilityModel.findOne({ journeyPlan: plan._id }, {}, { sort: { createdAt: -1 } });
    const today = moment().tz(process.env.TZ).toDate();
    const activeTasks = await this.visibilityExecutionModel.find({
      outlets: plan.outlet,
      startDate: { $lte: today },
      endDate: { $gte: today },
      status: VisibilityExecutionStatus.ACTIVE,
    });

    const fileIds = activeTasks.reduce((pre, curr) => {
      return Array.from(new Set([...pre, ...(visibility?.tasks?.[String(curr._id)] || [])]));
    }, []);
    const files = await this.fileModel.find({
      _id: {
        $in: fileIds.map((id) => new Types.ObjectId(id)),
      },
    });

    const visibilityTasks = activeTasks.map((task) => {
      const taskId = String(task._id);
      const taskFileIds: string[] = (visibility?.tasks?.[taskId] || []).map(String);
      const quantity: number = visibility?.taskProps?.[taskId]?.quantity || 0;
      const taskFiles = files.filter((file) => taskFileIds.includes(String(file._id)));

      return {
        id: taskId,
        name: task.name,
        subHeading: task.subHeading,
        images: taskFiles.map((file) => ({
          id: file._id,
          path: file.path,
        })),
        quantity,
      };
    });

    return this.transformVisibility({ plan, visibility, tasks: visibilityTasks });
  }

  async getJourneyPlanVisibilityOffline({ plan }: { plan: OutletJourneyPlanning }) {
    const visibility = await this.visibilityModel.findOne({ journeyPlan: plan._id }, {}, { sort: { createdAt: -1 } });
    const today = moment().tz(process.env.TZ).toDate();
    const activeTasks = await this.visibilityExecutionModel.find({
      outlets: plan.outlet,
      startDate: { $lte: today },
      endDate: { $gte: today },
      status: VisibilityExecutionStatus.ACTIVE,
    });
    const fileIds = activeTasks.reduce((pre, curr) => {
      return Array.from(new Set([...pre, ...(visibility?.tasks?.[String(curr._id)] || [])]));
    }, []);
    const files = await this.fileModel.find({
      _id: {
        $in: fileIds.map((id) => new Types.ObjectId(id)),
      },
    });

    const visibilityTasks = activeTasks.map((task) => {
      const taskId = String(task._id);
      const taskFileIds: string[] = (visibility?.tasks?.[taskId] || []).map(String);
      const quantity: number = visibility?.taskProps?.[taskId]?.quantity || 0;
      const taskFiles = files.filter((file) => taskFileIds.includes(String(file._id)));

      return {
        id: taskId,
        name: task.name,
        subHeading: task.subHeading,
        images: taskFiles.map((file) => ({
          id: file._id,
          path: file.path,
        })),
        quantity,
      };
    });

    return this.transformVisibility({ plan, visibility, tasks: visibilityTasks });
  }

  async updateJourneyPlanVisibility({ planId, dto, salesRepId, i18n }: { planId: string; dto: UpdateJourneyPlanVisibilityDto; salesRepId: string; i18n: I18nContext }) {
    let plan: OutletJourneyPlanning = await this.checkPermission({ planId, salesRepId, i18n });
    const salesRep = await this.salesRepModel.findById(salesRepId);

    //Check Today Plan
    if (!isEmptyObjectOrArray(plan)) {
      const startOfDay = moment().tz(process.env.TZ).startOf('day').toDate();
      const endOfDay = moment().tz(process.env.TZ).endOf('day').toDate();
      const isTodayPlan =
        (!plan.rescheduled && plan.day >= startOfDay && plan.day <= endOfDay) || (plan.rescheduled && plan.rescheduledDay >= startOfDay && plan.rescheduledDay <= endOfDay);

      if (!isTodayPlan) {
        plan = await this.outletJourneyPlanningService.getTodayPlan(plan.saleRep, plan.outlet);
      }

      if (!plan) {
        return null;
      }
    }

    const oldVisibility = await this.visibilityModel.findOne({ journeyPlan: plan._id });
    let listNewImages = [];
    for (const key in dto.tasks) {
      if (Object.prototype.hasOwnProperty.call(dto.tasks, key)) {
        const element = dto.tasks[key];
        listNewImages = listNewImages.concat(element.imageIds);
      }
    }
    if (oldVisibility) {
      let listImageIds = oldVisibility.images || [];
      if (oldVisibility.tasks) {
        for (const key in oldVisibility.tasks) {
          if (Object.prototype.hasOwnProperty.call(oldVisibility.tasks, key)) {
            const element = oldVisibility.tasks[key];
            listImageIds = listImageIds.concat(element);
          }
        }
      }
      const listImages = await this.filesService.findAll({
        _id: { $in: listImageIds },
      });
      const listImagesDelete = listImages.filter((image) => !listNewImages.includes(String(image._id)));
      // Remove old images
      this.filesService.deleteFiles(listImagesDelete).then().catch();
    }
    let startVisitDate = plan.startVisitDate;
    const visitedDay = moment().toDate();
    if (!startVisitDate || moment(startVisitDate).isAfter(moment(visitedDay))) {
      const correctLog = await this._userActionsService.getPlanFirstAction(
        [`/api/sale-rep/outlets/${plan.outlet._id.toString()}/execute-visit`, `/api/journey-plans/${plan._id.toString()}/visited-steps`],
        ['post', 'put'],
        moment().format('YYYY-MM-DD'),
      );

      if (!correctLog?.createdAt || moment(correctLog?.createdAt).isAfter(moment(visitedDay))) {
        startVisitDate = moment(visitedDay).subtract(5, 'minutes').toDate();
      } else {
        startVisitDate = correctLog?.createdAt;
      }
    }
    await Promise.all([
      this.visibilityModel.findOneAndUpdate(
        { journeyPlan: plan._id },
        {
          $set: {
            outlet: plan.outlet,
            saleRep: plan.saleRep,
            // avoid breaking change for version 1
            forwardStock: 0,
            images: [],
            // version 2.0
            tasks: (dto.tasks || []).reduce(
              (pre, curr) => ({
                ...pre,
                [curr.id]: curr.imageIds,
              }),
              {},
            ),
            taskProps: (dto.tasks || []).reduce(
              (pre, curr) => ({
                ...pre,
                [curr.id]: {
                  imageIds: curr.imageIds,
                  quantity: curr?.quantity || 0,
                },
              }),
              {},
            ),
          },
        },
        { upsert: true, new: true },
      ),
      this.planModel.findOneAndUpdate(
        { _id: plan._id },
        {
          $set: {
            visitedSteps: [...new Set([...(plan.visitedSteps || []), JourneyPlanVisitStep.DO_VISIBILITY])],
            visitStatus: VisitStatus.COMPLETED,
            updatedAt: Date.now(),
            visitedDay: visitedDay,
            startVisitDate: startVisitDate,
          },
        },
      ),
    ]);
    if (dto.tasks) {
      this.filesService.removeExpiredDate(listNewImages).then().catch();
    }

    this.outletJourneyPlanningService
      .checkHasOrder(String(plan.saleRep._id), String(plan.outlet._id))
      .then(() => {
        this.omsRepReportsCalculatorsService.executeDSRStatistic({ salesRepId: salesRep.saleRepId }).then().catch();
      })
      .catch();

    return this.getJourneyPlanVisibility({ planId, salesRepId, i18n });
  }

  async updateJourneyPlanVisibilityOffline({ plan, dto }: { plan: OutletJourneyPlanning; dto: UpdateJourneyPlanVisibilityDto }) {
    let success = false;
    let message = '';
    try {
      const oldVisibility = await this.visibilityModel.findOne({ journeyPlan: plan._id });
      let listNewImages = [];
      for (const key in dto.tasks) {
        if (Object.prototype.hasOwnProperty.call(dto.tasks, key)) {
          const element = dto.tasks[key];
          listNewImages = listNewImages.concat(element.imageIds);
        }
      }
      if (oldVisibility) {
        let listImageIds = oldVisibility.images || [];
        if (oldVisibility.tasks) {
          for (const key in oldVisibility.tasks) {
            if (Object.prototype.hasOwnProperty.call(oldVisibility.tasks, key)) {
              const element = oldVisibility.tasks[key];
              listImageIds = listImageIds.concat(element);
            }
          }
        }
        const listImages = await this.filesService.findAll({
          _id: { $in: listImageIds },
        });
        const listImagesDelete = listImages.filter((image) => !listNewImages.includes(String(image._id)));
        // Remove old images
        this.filesService.deleteFiles(listImagesDelete).then().catch();
      }

      let visitedDay = dto.visibilityTime ? new Date(dto.visibilityTime) : new Date();
      if (plan.startVisitDate && moment(visitedDay).isBefore(moment(plan.startVisitDate))) {
        visitedDay = moment(plan.startVisitDate).add(5, 'minutes').toDate();
      }

      await Promise.all([
        this.visibilityModel.findOneAndUpdate(
          { journeyPlan: plan._id },
          {
            $set: {
              outlet: plan.outlet,
              saleRep: plan.saleRep,
              // avoid breaking change for version 1
              forwardStock: 0,
              images: [],
              // version 2.0
              tasks: (dto.tasks || []).reduce(
                (pre, curr) => ({
                  ...pre,
                  [curr.id]: curr.imageIds,
                }),
                {},
              ),
              taskProps: (dto.tasks || []).reduce(
                (pre, curr) => ({
                  ...pre,
                  [curr.id]: {
                    imageIds: curr.imageIds,
                    quantity: curr?.quantity || 0,
                  },
                }),
                {},
              ),
            },
          },
          { upsert: true, new: true },
        ),
        this.planModel.findOneAndUpdate(
          { _id: plan._id },
          {
            $set: {
              visitStatus: VisitStatus.COMPLETED,
              updatedAt: Date.now(),
              visitedDay: visitedDay,
            },
          },
        ),
      ]);
      if (dto.tasks) {
        this.filesService.removeExpiredDate(listNewImages).then().catch();
      }
      this.outletJourneyPlanningService.checkHasOrder(String(plan.saleRep._id), String(plan.outlet._id)).then().catch();
      success = true;
    } catch (error) {
      message = error.message;
    }
    return { key: 'visibility', success, message };
  }

  private async checkPermission({ planId, salesRepId, i18n }: { planId: string; salesRepId: string; i18n: I18nContext }) {
    const plan = await this.planModel.findById(planId);
    if (!plan) {
      throw new NotFoundException(i18n.translate('plan.not_found'));
    }

    if (String(plan.saleRep) !== salesRepId) {
      throw new ForbiddenException(i18n.translate('plan.unauthorized'));
    }

    return plan;
  }

  private transformVisibility({ visibility, plan, tasks }: { visibility?: SaleRepExecutionVisibility; plan: OutletJourneyPlanning; tasks: any }) {
    return {
      planId: String(visibility?.journeyPlan || ''),
      lastUpdatedAt: visibility?.updatedAt,
      visitStatus: plan.visitStatus,
      startedAt: plan.startVisitDate,
      endedAt: plan.visitStatus === VisitStatus.COMPLETED ? plan.updatedAt : null,
      tasks,
      hasData: tasks.findIndex((t) => t.quantity > 0 || t.images?.length > 0) > -1,
    };
  }

  //=============================================================================================
  async getListVisibilityByJpList(jpList: any) {
    if (isEmptyObjectOrArray(jpList)) {
      return jpList;
    }
    const today = moment().tz(process.env.TZ).toDate();
    const visibilityData = await this.visibilityModel.find({ journeyPlan: { $in: jpList.map((jp) => jp._id) } }, {}, { sort: { createdAt: -1 } });
    const activeTaskData = await this.visibilityExecutionModel.find({
      outlets: { $in: jpList.map((jp) => jp.outlet) },
      startDate: { $lte: today },
      endDate: { $gte: today },
      status: VisibilityExecutionStatus.ACTIVE,
    });

    return await Promise.all(
      jpList.map(async (jp) => {
        const visibility: any = visibilityData?.find((v) => v.journeyPlan.toString() === jp._id.toString()) || {};
        const activeTasks = activeTaskData?.filter((task) => task.outlets.map((outlet) => outlet.toString()).includes(jp.outlet.toString())) || [];
        const fileIds = activeTasks.reduce((pre, curr) => {
          return Array.from(new Set([...pre, ...(visibility?.tasks?.[String(curr._id)] || [])]));
        }, []);
        const files = fileIds.length
          ? await this.fileModel.find({
              _id: {
                $in: fileIds.map((id) => new Types.ObjectId(id)),
              },
            })
          : [];

        const visibilityTasks = activeTasks.map((task) => {
          const taskId = String(task._id);
          const taskFileIds: string[] = (visibility?.tasks?.[taskId] || []).map(String);
          const quantity: number = visibility?.taskProps?.[taskId]?.quantity || 0;
          const taskFiles = files.filter((file) => taskFileIds.includes(String(file._id)));

          return {
            id: taskId,
            name: task.name,
            subHeading: task.subHeading,
            images: taskFiles?.map((file) => ({
              id: file._id,
              path: file.path,
            })),
            quantity,
          };
        });

        return {
          ...jp,
          visibility: this.transformVisibility({ plan: jp, visibility, tasks: visibilityTasks }),
        };
      }),
    );
  }
}
