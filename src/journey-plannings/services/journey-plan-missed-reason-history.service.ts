import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BaseService } from 'src/shared/services/base-service';
import { JourneyPlanMissedReasonHistory, JourneyPlanMissedReasonHistoryDocument } from '../schemas/journey-plan-absence.schema';

@Injectable()
export class JourneyPlanMissedReasonHistoriesService extends BaseService<JourneyPlanMissedReasonHistory> {
  constructor(@InjectModel(JourneyPlanMissedReasonHistory.name) private readonly journeyPlanMissedReasonHistoryModel: Model<JourneyPlanMissedReasonHistoryDocument>) {
    super();
    this.model = journeyPlanMissedReasonHistoryModel;
  }

  async getOutletReportDetail(id: string) {
    const res = await this.model
      .aggregate()
      .match({
        _id: new Types.ObjectId(id),
      })
      .lookup({
        localField: 'journeyPlan',
        from: 'outletjourneyplannings',
        foreignField: '_id',
        as: 'journeyPlan',
      })
      .unwind({
        path: '$journeyPlan',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'journeyPlan.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({
        path: '$outlet',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'missReason',
      })
      .unwind({
        path: '$missReason',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'evidenceImages',
        from: 'files',
        foreignField: '_id',
        as: 'evidenceImages',
      });
    return res[0] || null;
  }

  async getLatestOutletReportDetail(planId: string) {
    const res = await this.model
      .aggregate()
      .match({
        journeyPlan: new Types.ObjectId(planId),
      })
      .sort({
        createdAt: -1,
      })
      .limit(1)
      .lookup({
        localField: 'journeyPlan',
        from: 'outletjourneyplannings',
        foreignField: '_id',
        as: 'journeyPlan',
      })
      .unwind({
        path: '$journeyPlan',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'journeyPlan.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({
        path: '$outlet',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'journeyPlan.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'saleRep',
      })
      .unwind({
        path: '$saleRep',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'evidenceImages',
        from: 'files',
        foreignField: '_id',
        as: 'evidenceImages',
      })
      .project({
        visitedDate: { $cond: [{ $eq: ['$journeyPlan.rescheduled', false] }, '$journeyPlan.day', '$journeyPlan.rescheduledDay'] },
        evidenceImages: 1,
        missReason: 1,
        missedReason: 1,
        journeyPlan: 1,
        outlet: {
          name: 1,
          ucc: 1,
        },
        saleRep: {
          username: 1,
          saleRepId: 1,
        },
      });
    return res[0] || null;
  }
}
