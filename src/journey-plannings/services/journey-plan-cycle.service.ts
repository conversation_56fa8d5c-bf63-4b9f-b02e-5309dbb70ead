import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { BaseService } from '../../shared/services/base-service';
import { JourneyPlanCycle, JourneyPlanCycleDocument } from '../schemas/journey-plan-cycle.schema';
import { JourneyPlanWeek } from '../schemas/journey-plan-week.schema';

@Injectable()
export class JourneyPlanCycleService extends BaseService<JourneyPlanCycle> {
  constructor(
    @InjectModel(JourneyPlanCycle.name)
    private readonly _journeyPlanCycleDocumentModel: Model<JourneyPlanCycleDocument>,
  ) {
    super();
    this.model = _journeyPlanCycleDocumentModel;
  }

  async findByIdAndGetWeeks(id: string): Promise<JourneyPlanCycle & { weeks: JourneyPlanWeek[] }> {
    const queryResult = await this._journeyPlanCycleDocumentModel
      .aggregate()
      .project({
        _id: 0,
        c: '$$ROOT',
      })
      .match({
        'c._id': new Types.ObjectId(id),
      })
      .lookup({
        localField: 'c._id',
        from: 'journeyplanweeks',
        foreignField: 'cycle',
        as: 'weeks',
      })
      .facet({
        data: [
          {
            $skip: 0,
          },
          {
            $limit: 1,
          },
        ],
      })
      .exec();

    const cycles = queryResult[0].data;
    if (!cycles.length) return null;

    return {
      ...cycles[0].c,
      weeks: cycles[0].weeks,
    };
  }

  async findOrCreate(cycleName: string, year: number) {
    let cycle = await this.findOne({
      cycleName,
      year,
    });
    if (!cycle) {
      cycle = await this.create({
        cycleName,
        year,
      });
    }
    return cycle;
  }
}
