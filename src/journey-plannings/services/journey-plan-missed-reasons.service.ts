import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseService } from 'src/shared/services/base-service';
import { JourneyPlanMissedReason, JourneyPlanMissedReasonDocument } from '../schemas/journey-plan-missed-reason.schema';

@Injectable()
export class JourneyPlanMissedReasonsService extends BaseService<JourneyPlanMissedReason> {
  constructor(@InjectModel(JourneyPlanMissedReason.name) private readonly journeyPlanMissedReasonsModel: Model<JourneyPlanMissedReasonDocument>) {
    super();
    this.model = journeyPlanMissedReasonsModel;
  }

  async getAllReasons() {
    return this.model.find().sort({ priority: 1 }).exec();
  }
}
