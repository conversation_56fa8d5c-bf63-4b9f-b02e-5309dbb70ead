import { BadRequestException, ForbiddenException, forwardRef, Inject, Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { OutletStatus } from 'src/outlets/enums/outlet-status.enum';
import { nanoid } from 'nanoid';
import { I18nContext } from 'nestjs-i18n';
import { ISor } from 'src/outlets/interfaces';
import { IOutletSearching } from 'src/sale-rep/interfaces';
import { PaginationDto } from 'src/shared/dtos/pagination.dto';
import { ConstantRoles } from 'src/utils/constants/role';
import { BaseJourneyPlanSettingService, DistributorService, DistributorUserRelationService } from '../../distributor/services';
import { Outlet } from '../../outlets/schemas/outlet.schema';
import { GetMissedOutletReportDto, GetSuccessOutletReportDto } from '../../report/dtos';
import { normalizeQueryHelper } from '../../shared/helpers';
import { BaseService } from '../../shared/services/base-service';
import { User } from '../../users/schemas/user.schema';
import { UsersService } from '../../users/services/users.service';
import { canEdit, findClosetCoordinates, formatCurrentDay, generateOutletKey, isEmptyObjectOrArray, printLog } from '../../utils';
import { JourneyPlanSortOrder } from '../dtos/fetch-journey-plan.dto';
import { CountAffectedOutletsDto, MissedReasonForOutletDto, MissedReasonForOutletsDto, MissedReasonForVisitsDto } from '../dtos/missed-visit-reason.dto';
import { CancellationReasonType } from '../enums/cancellation-reason-type.enum';
import { JourneyPlanType } from '../enums/journey-plan-type.enum';
import { VisitStatus } from '../enums/visit-status.enum';
import { WeekNameType } from '../enums/week-type.enum';
import { JourneyPlanWeek } from '../schemas/journey-plan-week.schema';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from '../schemas/outlet-journey-planning.schema';
import { JourneyPlanMissedReasonHistoriesService } from './journey-plan-missed-reason-history.service';
import { JourneyPlanMissedReasonsService } from './journey-plan-missed-reasons.service';
import { JourneyPlanWeekService } from './journey-plan-week.service';
import { DistributionAchievementDto } from 'src/external/dtos/distribution-achievement.dto';
import { DistributionOutletCoverageDto } from 'src/external/dtos/distribution-outlet-coverage.dto';
import { GoogleMapsService } from '../../third-parties/google-maps-services';
import { FilesService } from 'src/files/services';
import { MissReason, MissReasonDocument } from 'src/miss-reasons/schemas/miss-reason.schema';
import { MissReasonLocation } from 'src/miss-reasons/enums/miss-reason-location.enum';
import { MissReasonsService } from 'src/miss-reasons/miss-reasons.service';
import { OmsService } from 'src/external/services/oms.service';
import { ExecuteVisitDto } from 'src/sale-rep/dtos';
import { SaleRepExecutionAvailabilityService } from 'src/sale-rep/services';
import { OrdersService } from 'src/orders/services/orders.service';
import { SettingsService } from '../../settings/settings.service';
import { ConstantCommons } from '../../utils/constants';
import { CheckStocksService } from './check-stock.service';
import { ColdStocksService } from './cold-stocks.service';
import { JourneyPlanVisibilityService } from './visibility.service';
import { OutletsService } from '../../outlets/services/outlets.service';

@Injectable()
export class OutletJourneyPlanningService extends BaseService<OutletJourneyPlanning> {
  constructor(
    @InjectModel(OutletJourneyPlanning.name)
    private readonly _outletJourneyPlanningDocumentModel: Model<OutletJourneyPlanningDocument>,
    @InjectModel(MissReason.name)
    private readonly missReasonModel: Model<MissReasonDocument>,
    private readonly _journeyPlanMissedReasonHistoriesService: JourneyPlanMissedReasonHistoriesService,
    private readonly _journeyPlanWeekService: JourneyPlanWeekService,
    private readonly _journeyPlanMissedReasonsService: JourneyPlanMissedReasonsService,
    @Inject(forwardRef(() => BaseJourneyPlanSettingService))
    private readonly _baseJourneyPlanSettingService: BaseJourneyPlanSettingService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _distributorService: DistributorService,
    @Inject(forwardRef(() => UsersService))
    private readonly _userService: UsersService,
    @Inject(forwardRef(() => CheckStocksService))
    private readonly _checkStocksService: CheckStocksService,
    @Inject(forwardRef(() => ColdStocksService))
    private readonly _coldStocksService: ColdStocksService,
    @Inject(forwardRef(() => JourneyPlanVisibilityService))
    private readonly _visibilityService: JourneyPlanVisibilityService,
    private readonly _googleMapsService: GoogleMapsService,
    private readonly _fileService: FilesService,
    private readonly missReasonsService: MissReasonsService,
    @Inject(forwardRef(() => OmsService))
    private readonly omsService: OmsService,
    @Inject(forwardRef(() => OrdersService))
    private readonly ordersService: OrdersService,
    @Inject(forwardRef(() => SettingsService))
    private readonly settingsService: SettingsService,
    private readonly _saleRepExecutionAvailabilityService: SaleRepExecutionAvailabilityService,
    @Inject(forwardRef(() => OutletsService))
    private readonly _outletsService: OutletsService,
  ) {
    super();
    this.model = _outletJourneyPlanningDocumentModel;
    if (!process.env.NODE_APP_INSTANCE || (process.env.NODE_APP_INSTANCE && parseInt(process.env.NODE_APP_INSTANCE) === 0)) {
      this.migrateData().then().catch();
    }
  }

  async migrateData() {
    await this.clearWrongDataJp().then().catch();
    await this.updateAllPendingJPToNewStatus().then().catch();
    await this.updateStartVisitDate().then().catch();
    await this.updateLocation().then().catch();
  }

  async updateStartVisitDate() {
    const time = moment().startOf('day').subtract(2, 'month').toDate();
    const plans = await this._outletJourneyPlanningDocumentModel.find({
      startVisitDate: null,
      createdAt: { $gte: time },
      visitStatus: VisitStatus.COMPLETED,
    });
    for (const plan of plans) {
      try {
        plan.startVisitDate = plan.rescheduled ? plan.rescheduledDay : plan.day;
        plan.save();
      } catch (error) {}
    }
  }

  async updateLocation() {
    const plans = await this._outletJourneyPlanningDocumentModel.find({ geoLocation: { $ne: null }, location: null });
    for (const plan of plans) {
      try {
        if (plan.geoLocation) {
          plan.location = { latitude: plan.geoLocation.coordinates[1], longitude: plan.geoLocation.coordinates[0] };
          await plan.save();
        }
      } catch (error) {}
    }
  }

  async checkPermission({ planId, salesRepId, i18n }: { planId: string; salesRepId: string; i18n: I18nContext }) {
    const plan = await this._outletJourneyPlanningDocumentModel.findById(planId);
    if (!plan) {
      throw new NotFoundException(i18n.translate('plan.not_found'));
    }
    if (String(plan.saleRep) !== salesRepId) {
      throw new ForbiddenException(i18n.translate('plan.unauthorized'));
    }
    return plan;
  }

  async getJourneyPlanDetails({ planId, salesRepId, i18n }: { planId: string; salesRepId: string; i18n: I18nContext }) {
    const plan = await this.checkPermission({ planId, salesRepId, i18n });

    return {
      id: plan._id,
      visitStatus: plan.visitStatus,
      startedAt: plan.visitedDay,
      endedAt: plan.visitStatus === VisitStatus.COMPLETED ? plan.visitedDay : null,
    };
  }

  async addOutletToCycle(saleRep: User, outlet: Outlet, week: JourneyPlanWeek, day: Date) {
    return this.create({ saleRep, outlet, week, day });
  }

  async addUnplanVisited(saleRep: User, outlet: Outlet) {
    const currentDay = new Date();
    const currentWeek = await this._journeyPlanWeekService.getCurrentWeekOfGivenDay(currentDay.toISOString());
    return this.create({ saleRep, outlet, week: currentWeek, day: null, visitedDay: currentDay });
  }

  async getJourneyPlans(saleRepId: string, type = JourneyPlanType.TODAY, outletId = '') {
    let dateCondition;
    const startOfDay = moment().tz(process.env.TZ).startOf('day').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();

    switch (type) {
      case JourneyPlanType.UPCOMING:
        const nextTwoWeekDay = moment().add(2, 'weeks').endOf('day').toISOString();
        dateCondition = {
          $or: [
            { day: { $gt: endOfDay, $lte: nextTwoWeekDay }, rescheduled: false },
            { rescheduledDay: { $gt: endOfDay, $lte: nextTwoWeekDay }, rescheduled: true },
          ],
        };
        break;
      case JourneyPlanType.UPCOMINGCURRENTWEEK:
        const currentWeek = await this._journeyPlanWeekService.getCurrentWeekOfGivenDay(new Date().toISOString());
        const endOfCurrentWeek = moment(currentWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toISOString();
        dateCondition = {
          $or: [
            {
              day: { $gt: startOfDay, $lte: endOfCurrentWeek },
              rescheduled: false,
              visitStatus: { $ne: VisitStatus.COMPLETED },
            },
            {
              rescheduledDay: { $gt: startOfDay, $lte: endOfCurrentWeek },
              rescheduled: true,
              visitStatus: { $ne: VisitStatus.COMPLETED },
            },
          ],
        };
        break;
      case JourneyPlanType.TODAY:
      default:
        dateCondition = {
          $or: [
            { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
            { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
          ],
        };
        break;
    }

    const listPlans = await this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        ...dateCondition,
        ...(outletId ? { outlet: new Types.ObjectId(outletId) } : {}),
      })
      .populate([
        {
          path: 'outlet',
          match: { status: OutletStatus.ACTIVE },
        },
        {
          path: 'missedReason',
        },
      ])
      .populate({
        path: 'week',
        populate: {
          path: 'cycle',
        },
      })
      .exec();
    // Add end of cycle in the response
    const result = [];

    let assignedTimes = [];
    if (listPlans.length) {
      assignedTimes = await this.getAssignedTimes(saleRepId);
    }

    const { startTimeFrame, timeInterval } = await this.getJourneyPlanSettingBySalesRep(saleRepId);

    for (const p of listPlans) {
      if (p.outlet.status === OutletStatus.ACTIVE || (type == JourneyPlanType.TODAY && p.visitStatus === VisitStatus.COMPLETED)) {
        const planCycleId = p.week.cycle._id;
        const allWeeks = await this._journeyPlanWeekService.getWeeksOfGivenCycle(planCycleId);
        const firstWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_1);
        const lastWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_4);
        const startOfWeek = p.week.startTime;
        const endOfWeek = moment(startOfWeek).add(6, 'days').tz(process.env.TZ).endOf('day').toISOString();
        const startOfCycleTime = firstWeek.startTime;
        const endOfCycleTime = moment(lastWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toISOString();
        const displayDay = p.rescheduledDay ? p.rescheduledDay : p.day;
        const outletDataKey = generateOutletKey(p?.outlet);
        const isWillMiss = p.visitStatus !== VisitStatus.COMPLETED && !!p.missedReason;
        result.push({
          ...p.toObject(),
          startOfWeek,
          endOfWeek,
          startOfCycleTime,
          endOfCycleTime,
          displayDay,
          outletDataKey,
          assignedTimes,
          startTimeFrame,
          timeInterval,
          isWillMiss,
          missedReason: p.missedReason,
          visitStatus: isWillMiss ? undefined : p.visitStatus,
        });
      }
    }
    return result.sort((p1, p2) => +new Date(p1.displayDay) - +new Date(p2.displayDay));
  }

  async getJourneyPlansKH(saleRepId: string, type = JourneyPlanType.TODAY, outletId = '', currentUser: User = null) {
    let dateCondition;
    const startOfDay = moment().tz(process.env.TZ).startOf('day').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();

    switch (type) {
      case JourneyPlanType.UPCOMING:
        const nextTwoWeekDay = moment().add(2, 'weeks').endOf('day').toISOString();
        dateCondition = {
          $or: [
            { day: { $gt: endOfDay, $lte: nextTwoWeekDay }, rescheduled: false },
            { rescheduledDay: { $gt: endOfDay, $lte: nextTwoWeekDay }, rescheduled: true },
          ],
        };
        break;
      case JourneyPlanType.UPCOMINGCURRENTWEEK:
        const currentWeek = await this._journeyPlanWeekService.getCurrentWeekOfGivenDay(new Date().toISOString());
        const endOfCurrentWeek = moment(currentWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toISOString();
        dateCondition = {
          $or: [
            {
              day: { $gt: startOfDay, $lte: endOfCurrentWeek },
              rescheduled: false,
              visitStatus: { $ne: VisitStatus.COMPLETED },
            },
            {
              rescheduledDay: { $gt: startOfDay, $lte: endOfCurrentWeek },
              rescheduled: true,
              visitStatus: { $ne: VisitStatus.COMPLETED },
            },
          ],
        };
        break;
      case JourneyPlanType.TODAY:
      default:
        dateCondition = {
          $or: [
            { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
            { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
          ],
        };
        break;
    }

    const listPlans = await this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        ...dateCondition,
        ...(outletId ? { outlet: new Types.ObjectId(outletId) } : {}),
      })
      .populate([
        {
          path: 'outlet',
          match: { status: OutletStatus.ACTIVE },
        },
        {
          path: 'missedReason',
        },
      ])
      .populate({
        path: 'week',
        populate: {
          path: 'cycle',
        },
      })
      .sort({ priority: 1 })
      .exec();

    // Add end of cycle in the response
    const result = [];

    let assignedTimes = [];
    if (listPlans.length) {
      assignedTimes = await this.getAssignedTimes(saleRepId);
    }

    const { startTimeFrame, timeInterval } = await this.getJourneyPlanSettingBySalesRep(saleRepId);

    for (const p of listPlans) {
      if (p.outlet.status === OutletStatus.ACTIVE || (type == JourneyPlanType.TODAY && p.visitStatus === VisitStatus.COMPLETED)) {
        const planCycleId = p.week.cycle._id;
        const allWeeks = await this._journeyPlanWeekService.getWeeksOfGivenCycle(planCycleId);
        const firstWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_1);
        const lastWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_4);
        const startOfWeek = p.week.startTime;
        const endOfWeek = moment(startOfWeek).add(6, 'days').tz(process.env.TZ).endOf('day').toISOString();
        const startOfCycleTime = firstWeek.startTime;
        const endOfCycleTime = moment(lastWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toISOString();
        const displayDay = p.rescheduledDay ? p.rescheduledDay : p.day;
        const outletDataKey = generateOutletKey(p?.outlet);
        const isWillMiss = p.visitStatus !== VisitStatus.COMPLETED && !!p.missedReason;
        result.push({
          ...p.toObject(),
          startOfWeek,
          endOfWeek,
          startOfCycleTime,
          endOfCycleTime,
          displayDay,
          outletDataKey,
          assignedTimes,
          startTimeFrame,
          timeInterval,
          isWillMiss,
          missedReason: p.missedReason,
          visitStatus: isWillMiss ? undefined : p.visitStatus,
        });
      }
    }
    return this.priorityJPs(currentUser, result);
  }

  private async priorityJPs(salesRep: User = null, listPlans: any) {
    if (salesRep?.geoAddress && (salesRep?.isAddressChanged || !isEmptyObjectOrArray(listPlans.find((p) => p.priority === 0)))) {
      //Get lat,lng of sale
      const originAddress = salesRep?.geoAddress;

      //get lat,lng of outlets
      const outletAddress = listPlans.map((p) => p.outlet.address);

      //get Destination
      const destinations = await this._googleMapsService.getDirections(originAddress, outletAddress);

      //Sort data
      let result = [];
      if (!isEmptyObjectOrArray(destinations)) {
        const coordinates = outletAddress.map((item: any) => {
          const [lat, lng] = item.replace(/\s/g, '').split(',');
          return { lat: parseFloat(lat), lng: parseFloat(lng) };
        });
        const destinationsCoordinates = destinations.map((item: any) => {
          const [lat, lng, priority, distance] = [item.endLatLong.lat, item.endLatLong.lng, item.priority, item.distance];
          return { lat: parseFloat(lat), lng: parseFloat(lng), priority, distance };
        });
        const sortedCoors = findClosetCoordinates(coordinates, destinationsCoordinates);

        let priority = 0;
        const currentDay = new Date().toISOString().split('T')[0];
        const startTime = moment(`${currentDay}T${listPlans[0].startTimeFrame}`);
        for (const sortItem of sortedCoors) {
          const objPlan = listPlans.find((p) => p.outlet.address.replace(/\s/g, '') === `${sortItem.lat},${sortItem.lng}` && !result.find((r) => r.outlet.ucc === p.outlet.ucc));
          if (!isEmptyObjectOrArray(objPlan)) {
            ++priority;
            const displayDay = priority === 1 ? startTime : startTime.add(objPlan.timeInterval, 'minutes');
            result.push({ ...objPlan, priority: sortItem.priority ? sortItem.priority : priority, distance: sortItem.distance, displayDay: displayDay.toDate() });
          }
        }

        //Check plans are missing
        const missedPlans = listPlans.filter((plan1: any) => !result.some((plan2: any) => plan1._id === plan2._id));
        if (!isEmptyObjectOrArray(missedPlans)) {
          result = result.concat(
            missedPlans
              .sort((p1, p2) => +new Date(p1.displayDay) - +new Date(p2.displayDay))
              .map((m) => {
                return { ...m, priority: ++priority, distance: undefined };
              }),
          );
        }

        //Update isAddressChanged and Priority
        this._userService.addressChangedUpdate(salesRep, false).then();
        this.updateOutletJourneyPlanPriorities(result).then();
        return result.sort((p1, p2) => p1.priority - p2.priority);
      } else {
        const listObjPlans = listPlans.map((obj) => {
          return {
            ...obj,
            distance: null,
            priority: 0,
          };
        });
        this.updateOutletJourneyPlanPriorities(listObjPlans).then();
        return listObjPlans.sort((p1, p2) => +new Date(p1.displayDay) - +new Date(p2.displayDay));
      }
    }

    if (!isEmptyObjectOrArray(listPlans.find((p) => p.priority === 0))) {
      return listPlans.sort((p1, p2) => +new Date(p1.displayDay) - +new Date(p2.displayDay));
    }
    return listPlans;
  }

  async updateOutletJourneyPlanPriorities(journeyPlans: any = []) {
    return await Promise.all(
      journeyPlans.map(async (journey: { _id: any; priority: number; distance: any }) => {
        try {
          return await this.update(journey._id, { priority: journey?.priority || 0, distance: journey?.distance });
        } catch (error) {}
      }),
    );
  }

  async getJourneyPlanSettingBySalesRep(salesRepId: string) {
    const distributorUserRelation = await this._distributorUserRelationService.findOne({
      user: new Types.ObjectId(salesRepId),
    });

    if (distributorUserRelation) {
      const baseJourneyPlanSetting = await this._baseJourneyPlanSettingService.findOne({
        distributor: distributorUserRelation.distributor,
      });
      if (baseJourneyPlanSetting) {
        return {
          startTimeFrame: baseJourneyPlanSetting.startingTimeframe,
          timeInterval: baseJourneyPlanSetting.timeInterval,
        };
      }
    }

    return {
      startTimeFrame: '00:00',
      timeInterval: 15,
    };
  }

  /**
   *
   * @param saleRep
   * @param outlet
   */
  async getTodayPlan(saleRep: User, outlet: Outlet) {
    try {
      const startOfDay = moment().tz(process.env.TZ).startOf('day').toDate();
      const endOfDay = moment().tz(process.env.TZ).endOf('day').toDate();
      const todayPlan = await this.findOne({
        saleRep: saleRep,
        outlet: outlet,
        $or: [
          {
            day: { $gte: startOfDay, $lte: endOfDay },
            rescheduled: false,
          },
          {
            rescheduledDay: { $gte: startOfDay, $lte: endOfDay },
            rescheduled: true,
          },
        ],
      } as any);

      if (isEmptyObjectOrArray(todayPlan)) {
        return todayPlan;
      }

      //Check Today Plan
      const isTodayPlan =
        (!todayPlan?.rescheduled && todayPlan?.day >= startOfDay && todayPlan?.day <= endOfDay) ||
        (todayPlan?.rescheduled && todayPlan?.rescheduledDay >= startOfDay && todayPlan?.rescheduledDay <= endOfDay);

      return isTodayPlan ? todayPlan : null;
    } catch (e) {
      return null;
    }
  }

  async getTodayPlans(saleRepId: string) {
    const startOfDay = moment().tz(process.env.TZ).startOf('day').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        $or: [
          { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
          { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
        ],
      })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
  }

  async getTodayPlanByOutlet(outletId: string) {
    const startOfDay = moment().tz(process.env.TZ).startOf('day').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .findOne({
        outlet: new Types.ObjectId(outletId),
        $or: [
          { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
          { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
        ],
      })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
  }

  async getAllTodayPlans() {
    const startOfDay = moment().tz(process.env.TZ).startOf('day').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .find({
        $or: [
          { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
          { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
        ],
      })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
  }

  async findOneAndPopulateOutlet(filter: any) {
    return this._outletJourneyPlanningDocumentModel
      .findOne(filter)
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
  }

  async findMissedVisitOutletToday(saleRepId = null) {
    const startOfDay = moment().tz(process.env.TZ).startOf('day').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();
    let condition = {
      $or: [
        {
          day: { $gte: startOfDay, $lte: endOfDay },
          rescheduled: false,
        },
        {
          rescheduledDay: { $gte: startOfDay, $lte: endOfDay },
          rescheduled: true,
        },
      ],
      visitStatus: { $ne: VisitStatus.COMPLETED },
    } as any;
    if (saleRepId) {
      condition = { ...condition, saleRep: new Types.ObjectId(saleRepId) };
    }
    return this._outletJourneyPlanningDocumentModel.find(condition).populate('outlet saleRep missedReason');
  }

  async getAllMissedOutletOfSaleRep(saleRepId: string, skip: number, limit: number, i18n: I18nContext) {
    const endOfYesterday = moment().tz(process.env.TZ).subtract(1, 'days').endOf('day').toDate();
    const allWeeksOfCurrentCycle = await this._journeyPlanWeekService.getWeeksOfCurrentCycle();
    const firstWeek = allWeeksOfCurrentCycle.find((w) => w.weekName === WeekNameType.WEEK_1);
    const lastWeek = allWeeksOfCurrentCycle.find((w) => w.weekName === WeekNameType.WEEK_4);
    const endOfCycleTime = moment(lastWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toDate();
    const startOfCycleTime = firstWeek.startTime;

    const aggregate = this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        saleRep: new Types.ObjectId(saleRepId),
        visitStatus: { $ne: VisitStatus.COMPLETED },
        $or: [
          {
            day: { $lte: endOfYesterday, $gte: firstWeek.startTime },
            rescheduled: false,
          },
          {
            rescheduledDay: { $lte: endOfYesterday, $gte: firstWeek.startTime },
            rescheduled: true,
          },
        ],
      })
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .addFields({
        'ojp.displayDay': {
          $cond: [{ $eq: ['$ojp.rescheduled', false] }, '$ojp.day', '$ojp.rescheduledDay'],
        },
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp.week',
        from: 'journeyplanweeks',
        foreignField: '_id',
        as: 'jpw',
      })
      .unwind({
        path: '$jpw',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp.missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'mr',
      })
      .sort({
        'ojp.displayDay': -1,
      });
    if (skip >= 0 && limit > 0) {
      aggregate.skip(skip).limit(limit);
    }
    const executedAggregate = await aggregate.exec();

    let assignedTimes = [];
    if (executedAggregate.length) {
      assignedTimes = await this.getAssignedTimes(saleRepId);
    }

    const { startTimeFrame, timeInterval } = await this.getJourneyPlanSettingBySalesRep(saleRepId);

    const missReasonIds = executedAggregate.map((item) => (item.mr && item.mr.length ? String(item.mr[0]._id) : null)).filter(Boolean);
    const missReasons = await this.missReasonsService.getMissReasonByIds(missReasonIds);
    const missReasonsMap = this.missReasonsService.convertMissReasonsToMap(missReasons);

    const data = executedAggregate.map((item) => {
      const canSetReason = this.isAbleToSetMissedVisitReason(item.ojp.displayDay);
      const missReasonId = item.mr && item.mr.length ? String(item.mr[0]._id) : null;
      let missedReason;
      if (missReasonId) {
        const missReason = missReasonsMap[missReasonId];
        const { reason } = this.missReasonsService.getMissReasonTranslation(missReason.translations, i18n.lang);
        missedReason = {
          _id: missReason._id,
          reason,
          reschedulable: missReason.reschedulable,
        };
      }
      return {
        ...item.ojp,
        outlet: item.o,
        missedReason,
        startOfWeek: moment(item.jpw.startTime).toISOString(),
        endOfWeek: moment(item.jpw.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toISOString(),
        assignedTimes,
        startOfCycleTime,
        endOfCycleTime,
        canSetReason,
        startTimeFrame,
        timeInterval,
      };
    });

    return data;
  }

  async getListAbsences({ limit, skip }: PaginationDto, currentUser: User, i18n: I18nContext) {
    const cycle = await this._journeyPlanWeekService.getCycleByGivenDay();
    const { startOfCycle, endOfCycle } = await this._journeyPlanWeekService.getTimeRangeOfGivenCycle(cycle._id.toString());
    const yesterday = moment().tz(process.env.TZ).subtract(1, 'day').endOf('day').toDate();

    const queryResult = await this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        missedReason: { $ne: null },
        visitStatus: { $ne: VisitStatus.COMPLETED },
        saleRep: currentUser._id,
        $or: [
          { day: { $gt: startOfCycle, $lte: yesterday }, rescheduled: false },
          { rescheduledDay: { $gt: startOfCycle, $lte: yesterday }, rescheduled: true },
        ],
      })
      .lookup({
        localField: 'missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'mr',
      })
      .unwind({
        path: '$mr',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'mr.controllable': false,
      })
      .addFields({
        displayDay: {
          $dateToString: {
            format: '%d/%m/%Y',
            date: {
              $cond: [{ $eq: ['$rescheduled', false] }, '$day', '$rescheduledDay'],
            },
            timezone: process.env.TZ,
          },
        },
        rawDay: {
          $cond: [{ $eq: ['$rescheduled', false] }, '$day', '$rescheduledDay'],
        },
      })
      .group({
        _id: {
          id: '$missedReason',
          day: '$displayDay',
        },
        rawDay: { $first: '$rawDay' },
        count: { $sum: 1 },
      })
      .project({
        _id: 0,
        reasonId: '$_id.id',
        day: '$_id.day',
        rawDay: '$rawDay',
        numberOfAffectedOutlets: '$count',
      })
      .addFields({
        createdAt: {
          $dateFromString: {
            dateString: '$day',
            format: '%d/%m/%Y',
            timezone: process.env.TZ,
          },
        },
      })
      .sort({
        createdAt: -1,
      })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? +skip : 0,
          },
          {
            $limit: limit >= 1 ? +limit : 10,
          },
        ],
      })
      .exec();

    const [{ totalRecords, data }] = queryResult;

    const missReasonIds = data.map((item) => item.reasonId);
    const missReasons = await this.missReasonsService.getMissReasonByIds(missReasonIds);
    const missReasonsMap = this.missReasonsService.convertMissReasonsToMap(missReasons);

    return {
      data: data.map((item) => {
        const today = moment().tz(process.env.TZ).startOf('day');
        const date = moment(item.rawDay).tz(process.env.TZ);
        const isFuture = date.isSameOrAfter(today);
        const hasCancel = isFuture;

        const missReason = missReasonsMap[String(item.reasonId)];
        let reasonName = '';
        if (missReason) {
          const { reason } = this.missReasonsService.getMissReasonTranslation(missReason.translations, i18n.lang);
          reasonName = reason;
        }

        return {
          ...item,
          id: `${item.reasonId}_${item.day.replace(/\D/g, '')}_${nanoid()}`,
          reasonName,
          isFuture,
          hasCancel,
        };
      }),
      totalItem: totalRecords.length ? totalRecords[0].total : 0,
      cycle: {
        ...(cycle as any).toObject(),
        startOfCycle,
        endOfCycle,
      },
    };
  }

  async getListAbsencesKH({ limit, skip }: PaginationDto, currentUser: User, i18n: I18nContext) {
    const cycle = await this._journeyPlanWeekService.getCycleByGivenDay();
    const { startOfCycle, endOfCycle } = await this._journeyPlanWeekService.getTimeRangeOfGivenCycle(cycle._id.toString());

    const queryResult = await this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        missedReason: { $ne: null },
        visitStatus: { $ne: VisitStatus.COMPLETED },
        saleRep: currentUser._id,
      })
      .lookup({
        localField: 'missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'mr',
      })
      .unwind({
        path: '$mr',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'mr.locations': MissReasonLocation.REPORT_ABSENCES,
      })
      .addFields({
        displayDay: {
          $dateToString: {
            format: '%d/%m/%Y',
            date: {
              $cond: [{ $eq: ['$rescheduled', false] }, '$day', '$rescheduledDay'],
            },
            timezone: process.env.TZ,
          },
        },
        rawDay: {
          $cond: [{ $eq: ['$rescheduled', false] }, '$day', '$rescheduledDay'],
        },
      })
      .group({
        _id: {
          id: '$missedReason',
          day: '$displayDay',
        },
        rawDay: { $first: '$rawDay' },
        count: { $sum: 1 },
      })
      .project({
        _id: 0,
        reasonId: '$_id.id',
        day: '$_id.day',
        rawDay: '$rawDay',
        numberOfAffectedOutlets: '$count',
      })
      .addFields({
        createdAt: {
          $dateFromString: {
            dateString: '$day',
            format: '%d/%m/%Y',
            timezone: process.env.TZ,
          },
        },
      })
      .sort({
        createdAt: -1,
      })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? +skip : 0,
          },
          {
            $limit: limit >= 1 ? +limit : 10,
          },
        ],
      })
      .exec();

    const [{ totalRecords, data }] = queryResult;

    const missReasonIds = data.map((item) => item.reasonId);
    const missReasons = await this.missReasonsService.getMissReasonByIds(missReasonIds);
    const missReasonsMap = this.missReasonsService.convertMissReasonsToMap(missReasons);

    return {
      data: data.map((item) => {
        const today = moment().tz(process.env.TZ).startOf('day');
        const date = moment(item.rawDay).tz(process.env.TZ);
        const isFuture = date.isSameOrAfter(today);
        const hasCancel = isFuture;

        const missReason = missReasonsMap[String(item.reasonId)];
        let reasonName = '';
        if (missReason) {
          const { reason } = this.missReasonsService.getMissReasonTranslation(missReason.translations, i18n.lang);
          reasonName = reason;
        }

        return {
          ...item,
          id: `${item.reasonId}_${item.day.replace(/\D/g, '')}_${nanoid()}`,
          reasonName,
          isFuture,
          hasCancel,
        };
      }),
      totalItem: totalRecords.length ? totalRecords[0].total : 0,
      cycle: {
        ...(cycle as any).toObject(),
        startOfCycle,
        endOfCycle,
      },
    };
  }

  async getAssignedTimes(saleRepId: string) {
    const currentWeek = await this._journeyPlanWeekService.getCurrentWeekOfGivenDay(moment().tz(process.env.TZ).toDate().toISOString());
    const endOfCurrentWeek = moment(currentWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toDate();
    const assignedTimes = await this._outletJourneyPlanningDocumentModel
      .aggregate()
      .project({
        _id: 0,
      })
      .match({
        saleRep: new Types.ObjectId(saleRepId),
        $or: [
          {
            day: { $lte: endOfCurrentWeek, $gte: currentWeek.startTime },
            rescheduled: false,
          },
          {
            rescheduledDay: { $lte: endOfCurrentWeek, $gte: currentWeek.startTime },
            rescheduled: true,
          },
        ],
      })
      .addFields({
        displayDay: {
          $cond: [{ $eq: ['$rescheduled', false] }, '$day', '$rescheduledDay'],
        },
      })
      .project({
        displayDay: 1,
      })
      .exec();

    return assignedTimes.map((item) => item.displayDay);
  }

  isAbleToSetMissedVisitReason(displayDay: string) {
    if (moment(displayDay).tz(process.env.TZ).weekday() === 0) {
      return moment(displayDay).tz(process.env.TZ).endOf('d').diff(moment().tz(process.env.TZ).endOf('d'), 'd') === -1;
    } else {
      return moment(displayDay).tz(process.env.TZ).week() === moment().tz(process.env.TZ).week();
    }
  }

  isSameCurrentMonth(displayDay: string): boolean {
    return moment(displayDay).tz(process.env.TZ).get('months') === moment().tz(process.env.TZ).get('months');
  }

  isSameDay(day1: Date, day2: Date) {
    return moment(day1).tz(process.env.TZ).isSame(moment(day2).tz(process.env.TZ), 'day');
  }

  isCurrentDay(day: Date) {
    return moment().tz(process.env.TZ).isSame(moment(day).tz(process.env.TZ), 'day');
  }

  isFutureDay(day: Date) {
    return moment().tz(process.env.TZ).endOf('day').isBefore(moment(day));
  }

  isPastDay(day: Date) {
    return moment().tz(process.env.TZ).startOf('day').isAfter(moment(day));
  }

  async findByOutletAndSalRepAndCycle(outlet, saleRep, cycle) {
    const allWeeks = await this._journeyPlanWeekService.findAll({ cycle });
    return this._outletJourneyPlanningDocumentModel.findOne({
      outlet,
      saleRep: saleRep,
      $or: [{ week: { $in: allWeeks.map((w) => w._id) } }, { day: null }],
    });
  }

  async findByOutletAndSalRepAndWeek(outlet: string, saleRep: string, week: string) {
    return this._outletJourneyPlanningDocumentModel.findOne({
      outlet: new Types.ObjectId(outlet),
      saleRep: new Types.ObjectId(saleRep),
      $or: [{ week: new Types.ObjectId(week) }, { day: null }],
    });
  }

  /**
   *
   * @param saleRepId
   * @param distributorId
   * @param filterVisitedStatus
   * @param firstDay
   * @param lastDay
   * @param skip
   * @param limit
   * @param sort
   * @param i18n
   * @param noStatistic
   */
  async findPlannedVisitedOutletBySaleRep(
    { saleRepId, distributorId, filterVisitedStatus, firstDay, lastDay, skip = 0, limit = 1, sort = { day: 'desc' } }: any,
    i18n: I18nContext = null,
    noStatistic = false,
  ) {
    const startOfToday = moment().tz(process.env.TZ).startOf('day').toDate();
    const today = moment().tz(process.env.TZ).endOf('day').toDate();
    if (lastDay > today) {
      lastDay = today;
    }
    let filters: any = {
      saleRep: new Types.ObjectId(saleRepId),
    };
    switch (filterVisitedStatus) {
      case ConstantCommons.ALL:
        filters = { ...filters };
        break;
      case ConstantCommons.COMPLETED:
        filters = { ...filters, visitStatus: VisitStatus.COMPLETED, visitedDay: { $gte: firstDay, $lte: lastDay } };
        break;
      case ConstantCommons.MISSED:
        if (moment(lastDay).tz(process.env.TZ).isSame(moment().tz(process.env.TZ), 'months')) {
          lastDay = moment().tz(process.env.TZ).endOf('day').subtract(1, 'day').toDate();
        }
        filters = {
          ...filters,
          visitStatus: { $ne: VisitStatus.COMPLETED },
        };
        break;
    }
    filters = {
      ...filters,
      $or: [
        { day: { $gte: firstDay, $lte: lastDay }, rescheduled: false },
        {
          rescheduledDay: { $gte: firstDay, $lte: lastDay },
          rescheduled: true,
        },
      ],
      /*$nor: [
        {
          $expr: {
            $and: [
              { $eq: [{ $dateToString: { format: '%Y-%m-%d', date: '$day', timezone: 'Asia/Jakarta' } }, today.toISOString().split('T')[0]] },
              { $ne: ['$visitStatus', VisitStatus.COMPLETED] },
              { $eq: [{ $ifNull: ['$missedReason', null] }, null] },
              { $eq: ['$rescheduled', false] },
            ],
          },
        },
        {
          $expr: {
            $and: [
              { $eq: [{ $dateToString: { format: '%Y-%m-%d', date: '$rescheduledDay', timezone: 'Asia/Jakarta' } }, today.toISOString().split('T')[0]] },
              { $ne: ['$visitStatus', VisitStatus.COMPLETED] },
              { $eq: [{ $ifNull: ['$missedReason', null] }, null] },
              { $eq: ['$rescheduled', true] },
            ],
          },
        },
      ],*/
    };
    let aggregation = this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match(filters)
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      });
    const distributor = await this._distributorService.findById(distributorId);
    //Relation other collections
    aggregation = aggregation
      .lookup({
        localField: 'ojp.missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'mr',
      })
      .unwind({
        path: '$mr',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: false,
      });

    const queryResponse = await aggregation
      .project({
        outletReport: '$ojp.outletReport',
        plannedDate: '$ojp.day',
        visitStatus: '$ojp.visitStatus',
        rescheduled: '$ojp.rescheduled',
        rescheduledDay: '$ojp.rescheduledDay',
        visitedDate: '$ojp.visitedDay',
        plannedTime: { $cond: [{ $eq: ['$ojp.rescheduled', false] }, '$ojp.day', '$ojp.rescheduledDay'] },
        cancellationReason: { $ifNull: ['$mr._id', { $ifNull: ['$ojp.cancellationReason', ''] }] },
        missedReason: '$ojp.missedReason',
        controllable: '$mr.controllable',
        checkStock: '$ojp.checkStock',
        outlet: '$ojp.outlet',
        outletId: '$o._id',
        outletUcc: '$o.ucc',
        outletName: '$o.name',
        outletAddress: '$o.address',
        saleRepId: '$u.saleRepId',
        journeyPlanId: '$ojp._id',
        _id: '$ojp._id',
        saleRep: { _id: '$u._id', saleRepId: '$u.saleRepId', username: '$u.username' },
        // distributorId: '$distributor.distributorId',
        // distributorName: '$distributor.distributorName',
      })
      .collation({ locale: 'en' })
      .sort({ plannedTime: 'desc' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
          {
            $lookup: {
              localField: 'outletReport',
              from: 'journeyplanmissedreasonhistories',
              foreignField: '_id',
              as: 'jpReasonHistory',
            },
          },
          {
            $unwind: {
              path: '$jpReasonHistory',
              preserveNullAndEmptyArrays: true,
            },
          },
        ],
      })
      .exec();
    const [{ totalRecords, data: jpData }] = queryResponse;
    if (isEmptyObjectOrArray(jpData)) {
      return { data: jpData, totalRecords };
    }
    let data = jpData;
    if (!noStatistic) {
      data = await this.getLatestVisitedTime(data, firstDay, lastDay);
      data = await this._checkStocksService.getListAvailabilityDataByJpList(data);
      data = await this._coldStocksService.getListColdStockByJpList(data);
      data = await this._visibilityService.getListVisibilityByJpList(data);
    }
    const missReasonIds = data.map((item) => item.cancellationReason).filter(Types.ObjectId.isValid);

    const missReasons = await this.missReasonsService.getMissReasonByIds(missReasonIds);
    const missReasonsMap = this.missReasonsService.convertMissReasonsToMap(missReasons);

    return {
      data: data?.map((item) => {
        const plannedTime = item.rescheduled ? item.rescheduledDay : item.plannedDate;
        const isMissed = item.visitStatus !== VisitStatus.COMPLETED && (moment(plannedTime).tz(process.env.TZ).isBefore(startOfToday, 'day') || !!item.missedReason);
        const missReason = missReasonsMap[String(item.cancellationReason)];

        if (!missReason) {
          return {
            ...item,
            cancellationReason: item.cancellationReason ? i18n.t(item.cancellationReason) : '',
            isMissed,
            distributorId: distributor?.distributorId,
            distributorName: distributor?.distributorName,
          };
        }

        const { reason } = this.missReasonsService.getMissReasonTranslation(missReason.translations, i18n.lang);

        return {
          ...item,
          cancellationReason: reason,
          isMissed,
          distributorId: distributor?.distributorId,
          distributorName: distributor?.distributorName,
        };
      }),
      totalRecords: totalRecords.length ? totalRecords[0].total : 0,
    };
  }

  async findVisitedByOutletAndDistributorAndMonth({ sort = { name: 'asc' }, search, dayInMonth, distributorId, skip = 0, limit = 1 }: GetSuccessOutletReportDto) {
    let matchCondition: Record<string, any> = {};
    const firstDay = moment(dayInMonth).tz(process.env.TZ).startOf('month').toDate();
    const lastDay = moment(dayInMonth).tz(process.env.TZ).endOf('month').toDate();

    let aggregation = this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        visitStatus: VisitStatus.COMPLETED,
        visitedDay: { $gte: firstDay, $lte: dayInMonth ? lastDay : moment().toDate() },
      })
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'dis',
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'ojp.visitStatus': VisitStatus.COMPLETED,
        'dis.distributor': new Types.ObjectId(distributorId),
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      });

    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      matchCondition = {
        $or: [{ 'o.ucc': { $regex: new RegExp(`${normalizedQuery}`, 'i') } }, { 'o.name': { $regex: new RegExp(`${normalizedQuery}`, 'i') } }],
      };

      //Relation to outlets collection for search
      aggregation = aggregation.match({
        $and: [matchCondition],
      });
    }

    //Relation other collections
    aggregation = aggregation
      .lookup({
        localField: 'dis.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp._id',
        from: 'salerepexecutionavailabilities',
        foreignField: 'journeyPlan',
        as: 'av',
      })
      .unwind({
        path: '$av',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp._id',
        from: 'salerepexecutionvisibilities',
        foreignField: 'journeyPlan',
        as: 'vi',
      })
      .unwind({
        path: '$vi',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: false,
      });

    return aggregation
      .project({
        ojp: '$ojp',
        plannedDate: '$ojp.day',
        visitedDate: '$ojp.visitedDay',
        endDate: {
          $cond: {
            if: {
              $eq: ['$ojp.visitStatus', VisitStatus.COMPLETED],
            },
            then: '$ojp.updatedAt',
            else: new Date(),
          },
        },
        outletUcc: '$o.ucc',
        outletName: '$o.name',
        availability: '$av.brands',
        visibility: '$vi.images',
        availabilitiesId: '$av._id',
        visibilitiesId: '$vi._id',
        saleRepId: '$u.saleRepId',
        journeyPlanId: '$ojp._id',
        saleRep: { _id: '$u._id', saleRepId: '$u.saleRepId', username: '$u.username' },
        distributorId: '$distributor.distributorId',
        distributorName: '$distributor.distributorName',
      })
      .addFields({
        endTime: { $dateToString: { format: '%H %M', date: '$endDate' } },
      })
      .sort(sort)
      .collation({ locale: 'en' })
      .match({ visitedDate: { $gte: firstDay, $lte: dayInMonth ? lastDay : moment().toDate() } })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      });
  }

  async findVisitedByOutletAndDistributorAndMonthMY({ sort = { name: 'asc' }, search, dayInMonth, distributorIds, skip = 0, limit = 1 }: GetSuccessOutletReportDto) {
    let matchCondition: Record<string, any> = {};
    const firstDay = moment(dayInMonth).tz(process.env.TZ).startOf('month').toDate();
    const lastDay = moment(dayInMonth).tz(process.env.TZ).endOf('month').toDate();

    let aggregation = this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        visitStatus: VisitStatus.COMPLETED,
        visitedDay: { $gte: firstDay, $lte: dayInMonth ? lastDay : moment().toDate() },
      })
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'dis',
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'ojp.visitStatus': VisitStatus.COMPLETED,
        'dis.distributor': { $in: distributorIds },
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      });

    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      matchCondition = {
        $or: [{ 'o.ucc': { $regex: new RegExp(`${normalizedQuery}`, 'i') } }, { 'o.name': { $regex: new RegExp(`${normalizedQuery}`, 'i') } }],
      };

      //Relation to outlets collection for search
      aggregation = aggregation.match({
        $and: [matchCondition],
      });
    }

    //Relation other collections
    aggregation = aggregation
      .lookup({
        localField: 'dis.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp._id',
        from: 'salerepexecutionavailabilities',
        foreignField: 'journeyPlan',
        as: 'av',
      })
      .unwind({
        path: '$av',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp._id',
        from: 'salerepexecutionvisibilities',
        foreignField: 'journeyPlan',
        as: 'vi',
      })
      .unwind({
        path: '$vi',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: false,
      });

    return aggregation
      .project({
        ojp: '$ojp',
        plannedDate: '$ojp.day',
        visitedDate: '$ojp.visitedDay',
        endDate: {
          $cond: {
            if: {
              $eq: ['$ojp.visitStatus', VisitStatus.COMPLETED],
            },
            then: '$ojp.updatedAt',
            else: new Date(),
          },
        },
        outletUcc: '$o.ucc',
        outletName: '$o.name',
        availability: '$av.brands',
        visibility: '$vi.images',
        availabilitiesId: '$av._id',
        visibilitiesId: '$vi._id',
        saleRepId: '$u.saleRepId',
        journeyPlanId: '$ojp._id',
        saleRep: { _id: '$u._id', saleRepId: '$u.saleRepId', username: '$u.username' },
        distributorId: '$distributor.distributorId',
        distributorName: '$distributor.distributorName',
      })
      .addFields({
        endTime: { $dateToString: { format: '%H %M', date: '$endDate' } },
      })
      .sort(sort)
      .collation({ locale: 'en' })
      .match({ visitedDate: { $gte: firstDay, $lte: dayInMonth ? lastDay : moment().toDate() } })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      });
  }

  async findMissedVisitByOutletAndDistributorAndMonthMY({ distributorIds, skip = 0, limit = 1, sort = {}, search, dayInMonth }: GetMissedOutletReportDto, i18n: I18nContext) {
    let outletMatchCondition: Record<string, any> = {};
    const firstDay = moment(dayInMonth).tz(process.env.TZ).startOf('month').toDate();
    let lastDay = moment(dayInMonth).tz(process.env.TZ).endOf('month').toDate();
    if (!dayInMonth || moment(dayInMonth).tz(process.env.TZ).isSame(moment().tz(process.env.TZ), 'months')) {
      lastDay = moment().tz(process.env.TZ).endOf('day').subtract(1, 'day').toDate();
    }

    let aggregation = this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        visitStatus: { $ne: VisitStatus.COMPLETED },
        $or: [
          { day: { $gte: firstDay, $lte: lastDay }, rescheduled: false },
          {
            rescheduledDay: { $gte: firstDay, $lte: lastDay },
            rescheduled: true,
          },
        ],
      })
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'dis',
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'dis.distributor': { $in: distributorIds },
      })
      .lookup({
        localField: 'dis.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      });

    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      outletMatchCondition = {
        $or: [
          {
            'o.ucc': { $regex: new RegExp(`${normalizedQuery}`, 'i') },
          },
          {
            'o.name': { $regex: new RegExp(`${normalizedQuery}`, 'i') },
          },
        ],
      };

      //Relation to outlets collection for search
      aggregation = aggregation.match(outletMatchCondition);
    }

    //Relation other collections
    aggregation = aggregation
      .lookup({
        localField: 'ojp.missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'mr',
      })
      .unwind({
        path: '$mr',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: false,
      });

    const queryResponse = await aggregation
      .project({
        outletReport: '$ojp.outletReport',
        plannedDate: '$ojp.day',
        visitStatus: '$ojp.visitStatus',
        rescheduled: '$ojp.rescheduled',
        rescheduledDay: '$ojp.rescheduledDay',
        visitedDate: '$ojp.visitedDay',
        plannedTime: { $cond: [{ $eq: ['$ojp.rescheduled', false] }, '$ojp.day', '$ojp.rescheduledDay'] },
        cancellationReason: { $ifNull: ['$mr._id', { $ifNull: ['$ojp.cancellationReason', ''] }] },
        missedReason: '$ojp.missedReason',
        controllable: '$mr.controllable',
        outletUcc: '$o.ucc',
        outletName: '$o.name',
        saleRepId: '$u.saleRepId',
        journeyPlanId: '$ojp._id',
        saleRep: { _id: '$u._id', saleRepId: '$u.saleRepId', username: '$u.username' },
        distributorId: '$distributor.distributorId',
        distributorName: '$distributor.distributorName',
      })
      .sort(sort)
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
          {
            $lookup: {
              localField: 'outletReport',
              from: 'journeyplanmissedreasonhistories',
              foreignField: '_id',
              as: 'jpReasonHistory',
            },
          },
          {
            $unwind: {
              path: '$jpReasonHistory',
              preserveNullAndEmptyArrays: true,
            },
          },
        ],
      })
      .exec();

    const [{ totalRecords, data }] = queryResponse;

    const missReasonIds = data.map((item) => item.cancellationReason).filter(Types.ObjectId.isValid);

    const missReasons = await this.missReasonsService.getMissReasonByIds(missReasonIds);
    const missReasonsMap = this.missReasonsService.convertMissReasonsToMap(missReasons);

    return {
      data: data.map((item) => {
        const missReason = missReasonsMap[String(item.cancellationReason)];

        if (!missReason) {
          return { ...item, cancellationReason: item.cancellationReason ? i18n.t(item.cancellationReason) : '' };
        }

        const { reason } = this.missReasonsService.getMissReasonTranslation(missReason.translations, i18n.lang);

        return {
          ...item,
          cancellationReason: reason,
        };
      }),
      totalRecords: totalRecords.length ? totalRecords[0].total : 0,
    };
  }

  async findMissedVisitByOutletAndDistributorAndMonth({ distributorId, skip = 0, limit = 1, sort = {}, search, dayInMonth }: GetMissedOutletReportDto, i18n: I18nContext) {
    let outletMatchCondition: Record<string, any> = {};
    const firstDay = moment(dayInMonth).tz(process.env.TZ).startOf('month').toDate();
    let lastDay = moment(dayInMonth).tz(process.env.TZ).endOf('month').toDate();
    if (!dayInMonth || moment(dayInMonth).tz(process.env.TZ).isSame(moment().tz(process.env.TZ), 'months')) {
      lastDay = moment().tz(process.env.TZ).endOf('day').subtract(1, 'day').toDate();
    }

    let aggregation = this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        visitStatus: { $ne: VisitStatus.COMPLETED },
        $or: [
          { day: { $gte: firstDay, $lte: lastDay }, 'ojp.rescheduled': false },
          {
            rescheduledDay: { $gte: firstDay, $lte: lastDay },
            rescheduled: true,
          },
        ],
      })
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'dis',
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'dis.distributor': distributorId,
      })
      .lookup({
        localField: 'dis.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'dis.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      });

    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      outletMatchCondition = {
        $or: [
          {
            'o.ucc': { $regex: new RegExp(`${normalizedQuery}`, 'i') },
          },
          {
            'o.name': { $regex: new RegExp(`${normalizedQuery}`, 'i') },
          },
        ],
      };

      //Relation to outlets collection for search
      aggregation = aggregation.match(outletMatchCondition);
    }

    //Relation other collections
    aggregation = aggregation
      .lookup({
        localField: 'ojp.missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'mr',
      })
      .unwind({
        path: '$mr',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: false,
      });

    const queryResponse = await aggregation
      .project({
        outletReport: '$ojp.outletReport',
        plannedDate: '$ojp.day',
        visitStatus: '$ojp.visitStatus',
        rescheduled: '$ojp.rescheduled',
        rescheduledDay: '$ojp.rescheduledDay',
        visitedDate: '$ojp.visitedDay',
        cancellationReason: { $ifNull: ['$mr._id', { $ifNull: ['$ojp.cancellationReason', ''] }] },
        missedReason: '$ojp.missedReason',
        controllable: '$mr.controllable',
        outletUcc: '$o.ucc',
        outletName: '$o.name',
        saleRepId: '$u.saleRepId',
        journeyPlanId: '$ojp._id',
        saleRep: { _id: '$u._id', saleRepId: '$u.saleRepId', username: '$u.username' },
        distributorId: '$distributor.distributorId',
        distributorName: '$distributor.distributorName',
      })
      .sort(sort)
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
          {
            $lookup: {
              localField: 'outletReport',
              from: 'journeyplanmissedreasonhistories',
              foreignField: '_id',
              as: 'jpReasonHistory',
            },
          },
          {
            $unwind: {
              path: '$jpReasonHistory',
              preserveNullAndEmptyArrays: true,
            },
          },
        ],
      })
      .exec();

    const [{ totalRecords, data }] = queryResponse;

    const missReasonIds = data.map((item) => item.cancellationReason).filter(Types.ObjectId.isValid);

    const missReasons = await this.missReasonsService.getMissReasonByIds(missReasonIds);
    const missReasonsMap = this.missReasonsService.convertMissReasonsToMap(missReasons);

    return {
      data: data.map((item) => {
        const missReason = missReasonsMap[String(item.cancellationReason)];

        if (!missReason) {
          return { ...item, cancellationReason: item.cancellationReason ? i18n.t(item.cancellationReason) : '' };
        }

        const { reason } = this.missReasonsService.getMissReasonTranslation(missReason.translations, i18n.lang);

        return {
          ...item,
          cancellationReason: reason,
        };
      }),
      totalRecords: totalRecords.length ? totalRecords[0].total : 0,
    };
  }

  async findMissedVisitOutletBySaleRep({ saleRepId, distributorId, dayInMonth, skip = 0, limit = 1, sort = { name: 'asc' } }: any, i18n: I18nContext = null) {
    const firstDay = moment(dayInMonth).tz(process.env.TZ).startOf('month').toDate();
    let lastDay = moment(dayInMonth).tz(process.env.TZ).endOf('month').toDate();
    if (!dayInMonth || moment(dayInMonth).tz(process.env.TZ).isSame(moment().tz(process.env.TZ), 'months')) {
      lastDay = moment().tz(process.env.TZ).endOf('day').subtract(1, 'day').toDate();
    }

    let aggregation = this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        visitStatus: { $ne: VisitStatus.COMPLETED },
        saleRep: new Types.ObjectId(saleRepId),
        $or: [
          { day: { $gte: firstDay, $lte: lastDay }, rescheduled: false },
          {
            rescheduledDay: { $gte: firstDay, $lte: lastDay },
            rescheduled: true,
          },
        ],
      })
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'dis',
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'dis.distributor': distributorId,
      })
      .lookup({
        localField: 'dis.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'dis.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      });

    //Relation other collections
    aggregation = aggregation
      .lookup({
        localField: 'ojp.missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'mr',
      })
      .unwind({
        path: '$mr',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: false,
      });

    const queryResponse = await aggregation
      .project({
        outletReport: '$ojp.outletReport',
        plannedDate: '$ojp.day',
        visitStatus: '$ojp.visitStatus',
        rescheduled: '$ojp.rescheduled',
        rescheduledDay: '$ojp.rescheduledDay',
        visitedDate: '$ojp.visitedDay',
        cancellationReason: { $ifNull: ['$mr._id', { $ifNull: ['$ojp.cancellationReason', ''] }] },
        missedReason: '$ojp.missedReason',
        controllable: '$mr.controllable',
        outletUcc: '$o.ucc',
        outletName: '$o.name',
        saleRepId: '$u.saleRepId',
        journeyPlanId: '$ojp._id',
        saleRep: { _id: '$u._id', saleRepId: '$u.saleRepId', username: '$u.username' },
        distributorId: '$distributor.distributorId',
        distributorName: '$distributor.distributorName',
      })
      .collation({ locale: 'en' })
      .sort(sort)
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
          {
            $lookup: {
              localField: 'outletReport',
              from: 'journeyplanmissedreasonhistories',
              foreignField: '_id',
              as: 'jpReasonHistory',
            },
          },
          {
            $unwind: {
              path: '$jpReasonHistory',
              preserveNullAndEmptyArrays: true,
            },
          },
        ],
      })
      .exec();

    const [{ totalRecords, data }] = queryResponse;

    const missReasonIds = data.map((item) => item.cancellationReason).filter(Types.ObjectId.isValid);

    const missReasons = await this.missReasonsService.getMissReasonByIds(missReasonIds);
    const missReasonsMap = this.missReasonsService.convertMissReasonsToMap(missReasons);

    return {
      data: data.map((item) => {
        const missReason = missReasonsMap[String(item.cancellationReason)];

        if (!missReason) {
          return { ...item, cancellationReason: item.cancellationReason ? i18n.t(item.cancellationReason) : '' };
        }

        const { reason } = this.missReasonsService.getMissReasonTranslation(missReason.translations, i18n.lang);

        return {
          ...item,
          cancellationReason: reason,
        };
      }),
      totalRecords: totalRecords.length ? totalRecords[0].total : 0,
    };
  }

  async searchOutletByNameOrUCC(saleRepId: string, isInCurrentCycle = true, includeOutlets = []): Promise<IOutletSearching[]> {
    const allWeeks = await this._journeyPlanWeekService.getWeeksOfCurrentCycle();
    const firstWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_1);
    const lastWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_4);
    const endOfCycleTime = moment(lastWeek.startTime).add(6, 'days').toDate();
    const aggregate = this._outletJourneyPlanningDocumentModel
      .aggregate()
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .match({
        'ojp.saleRep': new Types.ObjectId(saleRepId),
        'ojp.outlet': {
          $in: includeOutlets,
        },
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp.missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'missedReason',
      })
      .addFields({
        'ojp.displayDay': {
          $cond: [{ $eq: ['$ojp.rescheduled', false] }, '$ojp.day', '$ojp.rescheduledDay'],
        },
      });
    if (isInCurrentCycle) {
      aggregate
        .match({
          'ojp.displayDay': {
            $gte: new Date(firstWeek.startTime),
            $lte: endOfCycleTime,
          },
        })
        .sort({
          'ojp.displayDay': 1,
        });
    } else {
      aggregate
        .match({
          $or: [
            {
              'ojp.displayDay': {
                $lt: new Date(firstWeek.startTime),
              },
            },
            {
              'ojp.day': null,
            },
          ],
        })
        .sort({
          'ojp.displayDay': 1,
        });
    }
    const queryResult = await aggregate.exec();

    return queryResult.map((item) => ({
      ...item,
      ojp: {
        ...item.ojp,
        missedReason: item.missedReason && item.missedReason.length ? item.missedReason[0] : null,
      },
    }));
  }

  async searchOutletByNameOrUCCV2(saleRepId: string, isInCurrentCycle = true, includeOutlets = []): Promise<IOutletSearching[]> {
    const allWeeks = await this._journeyPlanWeekService.getWeeksOfCurrentCycle();
    const firstWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_1);
    const lastWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_4);
    const endOfCycleTime = moment(lastWeek.startTime).add(6, 'days').toDate();

    const queryResult = await this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: includeOutlets,
      })
      .populate(['missedReason', 'outlet']);

    return queryResult
      .filter((ojp) => {
        const displayDay = moment(!ojp.rescheduled ? ojp.day : ojp.rescheduledDay).tz(process.env.TZ);
        if (isInCurrentCycle) {
          return displayDay.isBetween(moment(firstWeek.startTime).tz(process.env.TZ), moment(endOfCycleTime).tz(process.env.TZ));
        }
        return displayDay.isBefore(moment(firstWeek.startTime).tz(process.env.TZ)) || !ojp.day;
      })
      .sort((first: any, second: any) => {
        const displayDayFirst = moment(!first.rescheduled ? first.day : first.rescheduledDay).tz(process.env.TZ);
        const displayDaySecond = moment(!second.rescheduled ? second.day : second.rescheduledDay).tz(process.env.TZ);
        return displayDayFirst.isBefore(displayDaySecond) ? -1 : 1;
      })
      .map((item) => ({
        o: item.outlet,
        ojp: {
          ...item.toObject(),
          missedReason: !isEmptyObjectOrArray(item?.missedReason) ? item.missedReason : null,
          displayDay: (item.rescheduledDay || item.day) as unknown as string,
        },
      }));
  }

  async findClosestPlan(saleRepId: string, outletId: string) {
    this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
      })
      .project({
        day: 1,
        diff: { $abs: [{ $subtract: ['$day', new Date()] }] },
      })
      .sort({ diff: 1 });
  }

  /**
   * Find closest visited previous plan (include planned visit and unplanned visit) of current cycle
   * @param saleRepId
   * @param outletId
   */
  async findClosestVisitedPreviousPlanOfCurrentCycle(saleRepId: string, outletId: string) {
    const allWeeks = await this._journeyPlanWeekService.getWeeksOfCurrentCycle();
    const firstWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_1);
    return this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
        visitStatus: VisitStatus.COMPLETED,
        visitedDay: {
          $gte: new Date(firstWeek.startTime),
          $lte: new Date(),
        },
      })
      .sort({ visitedDay: -1 })
      .limit(1)
      .exec();
  }

  async findClosestVisitedPreviousPlanOfCurrentCycleOfOutlets(salesRepId: string, outletIds: string[]) {
    const allWeeks = await this._journeyPlanWeekService.getWeeksOfCurrentCycle();
    const firstWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_1);
    const plans = await this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(salesRepId),
        outlet: {
          $in: outletIds.map((id) => new Types.ObjectId(id)),
        },
        visitStatus: VisitStatus.COMPLETED,
        visitedDay: {
          $gte: new Date(firstWeek.startTime),
          $lte: new Date(),
        },
      })
      .exec();

    return outletIds.reduce((pre, outletId) => {
      const plansOfOutlet = plans.filter((item) => (item.outlet as unknown as typeof Types.ObjectId).toString() === outletId).sort((a, b) => +b.visitedDay - +a.visitedDay);

      if (plansOfOutlet.length) {
        return {
          ...pre,
          [outletId]: plansOfOutlet[0],
        };
      }

      return pre;
    }, {});
  }

  async checkHasOrder(saleRepId: string, outletId: string) {
    const startTime = moment().tz(process.env.TZ).startOf('day').toISOString();
    const endTime = moment().tz(process.env.TZ).endOf('day').toISOString();
    const order = await this.ordersService.findOne({
      salesRep: new Types.ObjectId(saleRepId),
      outlet: new Types.ObjectId(outletId),
      orderDate: { $gte: startTime, $lt: endTime },
    });
    if (order) {
      const jp = await this._outletJourneyPlanningDocumentModel.findOne({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
        visitStatus: VisitStatus.COMPLETED,
        $or: [
          { day: { $gte: startTime, $lt: endTime }, rescheduled: false },
          { rescheduledDay: { $gte: startTime, $lt: endTime }, rescheduled: true },
        ],
      });
      if (jp) {
        jp.hasOrder = true;
        jp.save().then().catch();
        this.ordersService.update(String(order._id), { jp: jp._id }).then().catch();
      }
    }
  }

  async findFuturePlannedVisitInCurrentWeek(saleRepId: string, outletId: string) {
    const startDayOfCurrentTime = moment().tz(process.env.TZ).startOf('day').toDate();
    const currentWeek = await this._journeyPlanWeekService.getCurrentWeekOfGivenDay(new Date().toISOString());
    const endOfCurrentWeek = moment(currentWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
        visitStatus: { $ne: VisitStatus.COMPLETED },
        $or: [
          { day: { $gt: startDayOfCurrentTime, $lte: endOfCurrentWeek }, rescheduled: false },
          { rescheduledDay: { $gt: startDayOfCurrentTime, $lte: endOfCurrentWeek }, rescheduled: true },
        ],
      })
      .limit(1)
      .exec();
  }

  async findFuturePlannedVisitInCurrentWeekOfOutlets(salesRepId: string, outletIds: string[]) {
    const startDayOfCurrentTime = moment().tz(process.env.TZ).startOf('day').toDate();
    const currentWeek = await this._journeyPlanWeekService.getCurrentWeekOfGivenDay(new Date().toISOString());
    const endOfCurrentWeek = moment(currentWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toISOString();
    const plans = await this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(salesRepId),
        outlet: {
          $in: outletIds.map((id) => new Types.ObjectId(id)),
        },
        visitStatus: { $ne: VisitStatus.COMPLETED },
        $or: [
          { day: { $gt: startDayOfCurrentTime, $lte: endOfCurrentWeek }, rescheduled: false },
          { rescheduledDay: { $gt: startDayOfCurrentTime, $lte: endOfCurrentWeek }, rescheduled: true },
        ],
      })
      .exec();

    return outletIds.reduce((pre, outletId) => {
      const plansOfOutlet = plans.filter((item) => (item.outlet as unknown as typeof Types.ObjectId).toString() === outletId);

      if (plansOfOutlet.length) {
        return {
          ...pre,
          [outletId]: plansOfOutlet[0],
        };
      }

      return pre;
    }, {});
  }

  /**
   * Check whether an outlet have "Start Visit" Label or not
   * @param saleRepId
   * @param outletId
   */
  async hasStartVisitLabel(saleRepId: string, outletId: string): Promise<boolean> {
    const closestVisitedPreviousPlanOfCurrentCycle = await this.findClosestVisitedPreviousPlanOfCurrentCycle(saleRepId, outletId);
    let hasStartVisitLabel = closestVisitedPreviousPlanOfCurrentCycle?.length === 0;
    if (!hasStartVisitLabel) {
      const futurePlannedVisitInCurrentWeek = await this.findFuturePlannedVisitInCurrentWeek(saleRepId, outletId);
      hasStartVisitLabel = futurePlannedVisitInCurrentWeek?.length !== 0;
    }
    return hasStartVisitLabel;
  }

  async hasStartVisitLabelOfOutlets(salesRepId: string, outletIds: string[]) {
    const closestVisitedPreviousPlanOfCurrentCycleOfOutletsMap = await this.findClosestVisitedPreviousPlanOfCurrentCycleOfOutlets(salesRepId, outletIds);
    let futurePlannedVisitInCurrentWeekOfOutletsMap = {};
    if (!Object.keys(closestVisitedPreviousPlanOfCurrentCycleOfOutletsMap).length) {
      futurePlannedVisitInCurrentWeekOfOutletsMap = await this.findFuturePlannedVisitInCurrentWeekOfOutlets(salesRepId, outletIds);
    }

    return outletIds.reduce((pre, outletId) => {
      return {
        ...pre,
        [outletId]: !closestVisitedPreviousPlanOfCurrentCycleOfOutletsMap[outletId] || !!futurePlannedVisitInCurrentWeekOfOutletsMap[outletId],
      };
    }, {});
  }

  async findUnVisitedPlan(journeyPlanId: string): Promise<OutletJourneyPlanning> {
    return this._outletJourneyPlanningDocumentModel
      .findOne({
        _id: new Types.ObjectId(journeyPlanId),
        visitStatus: { $ne: VisitStatus.COMPLETED },
      })
      .populate('week missedReason')
      .exec();
  }

  async findUnVisitedPlans(journeyPlanIds: string[]): Promise<OutletJourneyPlanning[]> {
    return this._outletJourneyPlanningDocumentModel
      .find({
        _id: {
          $in: journeyPlanIds.map((id) => new Types.ObjectId(id)),
        },
        visitStatus: { $ne: VisitStatus.COMPLETED },
      })
      .populate({
        path: 'week outlet',
      })
      .exec();
  }

  async findUnVisitedPlanInCurrentWeek(journeyPlanId: string): Promise<OutletJourneyPlanning> {
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();
    const currentWeek = await this._journeyPlanWeekService.getCurrentWeekOfGivenDay(new Date().toISOString());
    const endOfCurrentWeek = moment(currentWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .findOne({
        _id: new Types.ObjectId(journeyPlanId),
        $or: [
          {
            day: { $gt: endOfDay, $lte: endOfCurrentWeek },
            rescheduled: false,
            visitStatus: { $ne: VisitStatus.COMPLETED },
          },
          {
            rescheduledDay: { $gt: endOfDay, $lte: endOfCurrentWeek },
            rescheduled: true,
            visitStatus: { $ne: VisitStatus.COMPLETED },
          },
        ],
      })
      .populate({
        path: 'week',
      })
      .exec();
  }

  async getPlanBySaleRepAndDay(saleRep: User, day: Date) {
    return this._outletJourneyPlanningDocumentModel
      .findOne({
        saleRep: saleRep,
        $or: [
          { rescheduledDay: day, rescheduled: true },
          { day, rescheduled: false },
        ],
      })
      .exec();
  }

  async roundCurentMinutes() {
    const currentDay = new Date();
    const roundMinutes = Math.round(currentDay.getMinutes() / 60);
    currentDay.setHours(currentDay.getHours() + roundMinutes);
    currentDay.setMinutes(roundMinutes === 1 ? 0 : 30, 0, 0);
    return currentDay;
  }

  async calculateCPSR(saleRepId: string) {
    const startOfMonth = moment().tz(process.env.TZ).startOf('month').toISOString();
    const endOfMonth = moment().tz(process.env.TZ).endOf('month').toISOString();
    const allPlansOfMonth = await this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        $or: [
          { day: { $gte: startOfMonth, $lte: endOfMonth }, rescheduled: false },
          { rescheduledDay: { $gt: startOfMonth, $lte: endOfMonth }, rescheduled: true },
        ],
      })
      // .select(['_id', 'cancel', 'visitStatus'])
      .populate('missedReason')
      .exec();
    const plannedCall = allPlansOfMonth?.length ?? 0;

    const completedCalls = allPlansOfMonth?.filter((plan) => plan.visitStatus === VisitStatus.COMPLETED)?.length ?? 0;
    const uncontrollableMissCalls = (allPlansOfMonth || []).filter((plan) => plan.cancel || (plan.missedReason && !plan.missedReason.controllable)).length;

    return {
      current: completedCalls,
      target: plannedCall - uncontrollableMissCalls,
    };
  }

  async calculateAvaibility(saleRepId: string) {
    const startTime = moment().startOf('month').toISOString();
    const endTime = moment().endOf('month').toISOString();

    const listJp = await this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        checkStock: { $exists: true },
        $or: [
          {
            rescheduled: false,
            day: {
              $gte: startTime,
              $lte: endTime,
            },
          },
          {
            rescheduled: true,
            rescheduledDay: {
              $gte: startTime,
              $lte: endTime,
            },
          },
        ],
      })
      .sort({
        updatedAt: -1,
      });
    const listValid = {};
    for (const element of listJp) {
      if (!listValid[element.outlet._id]) {
        const checkedStock = element?.checkStock?.listProductsChecked?.filter((e) => e.check_stock_quantity > 0 || e?.selling_price > 0)?.length || 0;
        const dataCached = await this.omsService.getCachedDataByOutlet({
          outletId: new Types.ObjectId(element.outlet._id),
          useDb: true,
          project: {
            products: 1,
          },
        });
        const totalProducts = dataCached?.products?.length || 0;
        listValid[element.outlet._id] = {
          checkedStock,
          totalProducts,
        };
      }
    }
    let current = 0;
    let target = 0;
    for (const iterator of Object.values(listValid) as any) {
      current += iterator?.checkedStock;
      target += iterator?.totalProducts;
    }
    return {
      current,
      target,
    };
  }

  async getTodayPlanBySaleRepIdAndOutletId(saleRepId: string, outletId: string) {
    const startOfDay = moment().tz(process.env.TZ).startOf('d').toDate();
    const endOfDay = moment().tz(process.env.TZ).endOf('d').toDate();
    const todayPlan = await this._outletJourneyPlanningDocumentModel
      .findOne({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
        $or: [
          { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
          { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
        ],
      })
      .populate([
        {
          path: 'outlet',
          match: { status: OutletStatus.ACTIVE },
        },
        {
          path: 'missedReason',
        },
      ])
      .exec();

    //Check Today Plan
    const isTodayPlan =
      (!isEmptyObjectOrArray(todayPlan) && !todayPlan.rescheduled && todayPlan.day >= startOfDay && todayPlan.day <= endOfDay) ||
      (!isEmptyObjectOrArray(todayPlan) && todayPlan.rescheduled && todayPlan.rescheduledDay >= startOfDay && todayPlan.rescheduledDay <= endOfDay);

    return isTodayPlan ? todayPlan : null;
  }

  async getNextTwoWeeksPlanBySaleRepIdAndOutletId(saleRepId: string, outletId: string) {
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();
    const nextTwoWeekDay = moment().add(2, 'weeks').endOf('day').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .findOne({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
        $or: [
          { day: { $gt: endOfDay, $lte: nextTwoWeekDay }, rescheduled: false },
          { rescheduledDay: { $gt: endOfDay, $lte: nextTwoWeekDay }, rescheduled: true },
        ],
      })
      .populate('outlet missedReason')
      .exec();
  }

  async getInProgressTodayVisitBySaleRepIdAndExcludeOutletId(saleRepId: string, excludeOutletId: string) {
    const startOfDay = moment().tz(process.env.TZ).startOf('d').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('d').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .findOne({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: { $ne: new Types.ObjectId(excludeOutletId) },
        visitStatus: VisitStatus.IN_PROGRESS,
        $or: [
          { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
          { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
        ],
      })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
  }

  async getInProgressTodayVisitBySaleRepIdAndExcludeOutletIdOfOutlets(saleRepId: string, outletIds: string[]) {
    const startOfDay = moment().tz(process.env.TZ).startOf('d').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('d').toISOString();
    const plans = await this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: { $in: outletIds.map((id) => new Types.ObjectId(id)) },
        visitStatus: VisitStatus.IN_PROGRESS,
        $or: [
          { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
          { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
        ],
      })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();

    return outletIds.reduce((pre, id) => {
      const plansOfOtherOutlets = plans.filter((item) => item.outlet._id.toString() !== id);

      if (plansOfOtherOutlets.length) {
        return {
          ...pre,
          [id]: plansOfOtherOutlets[0],
        };
      }
      return pre;
    }, {});
  }

  async getInProgressTodayVisitBySaleRepIdAndOutletId(saleRepId: string, outletId: string) {
    const startOfDay = moment().tz(process.env.TZ).startOf('d').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('d').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .findOne({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
        visitStatus: VisitStatus.IN_PROGRESS,
        $or: [
          { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
          { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
        ],
      })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
  }

  async getPlan(query: string, startTime: string, endTime: string) {
    const normalizedQuery = normalizeQueryHelper(query);
    const aggregate = this._outletJourneyPlanningDocumentModel
      .aggregate()
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        $and: [
          {
            $or: [
              {
                'o.name': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
              },
              {
                'o.ucc': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
              },
            ],
          },
          {
            $or: [
              {
                'ojp.day': { $lte: new Date(endTime), $gte: new Date(startTime) },
                'ojp.rescheduled': false,
              },
              {
                'ojp.rescheduledDay': { $lte: new Date(endTime), $gte: new Date(startTime) },
                'ojp.rescheduled': true,
              },
            ],
          },
        ],
      });

    return aggregate.exec();
  }

  async getOfflinePlanByOutlet(salesRepId: Types.ObjectId, startTime: string, endTime: string) {
    const startOfCurrencyDate = moment(startTime).tz(process.env.TZ).startOf('day').toDate();
    const endOfCurrencyDate = moment(endTime).tz(process.env.TZ).endOf('day').toDate();
    const listJp = await this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        saleRep: salesRepId,
        $or: [
          {
            day: { $gte: startOfCurrencyDate, $lte: endOfCurrencyDate },
            rescheduled: false,
          },
          {
            rescheduledDay: { $gte: startOfCurrencyDate, $lte: endOfCurrencyDate },
            rescheduled: true,
          },
        ],
      })
      .lookup({
        localField: 'outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({
        path: '$outlet',
        preserveNullAndEmptyArrays: false,
      })
      .exec();

    return listJp.filter((o) => o.outlet?.status === OutletStatus.ACTIVE);
  }

  async getOutletJourneyPlanningSaleRep(saleRep: string[], listIdWeek: string[], limit = 1, skip = 0, orderBy, orderDesc) {
    const sort = { [`${orderBy}`]: orderDesc };
    const result = await this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        saleRep: { $in: saleRep },
      })
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'ol',
      })
      .unwind({
        path: '$ol',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'ojp.week',
        from: 'journeyplanweeks',
        foreignField: '_id',
        as: 'we',
      })
      .unwind({
        path: '$we',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'us',
      })
      .unwind({
        path: '$us',
        preserveNullAndEmptyArrays: true,
      })
      .addFields({
        'ojp.displayDay': {
          $cond: [{ $eq: ['$ojp.rescheduled', false] }, '$ojp.day', '$ojp.rescheduledDay'],
        },
        'ojp.isEdited': true,
      })
      .project({
        ucc: '$ol.ucc',
        outletName: { $ifNull: ['$ol.name', ''] },
        outletClass: { $ifNull: ['$ol.outletClass', ''] },
        outletArea: { $ifNull: ['$ol.area', ''] },
        saleRepId: { $ifNull: ['$us.saleRepId', ''] },
        weekName: { $ifNull: ['$we.weekName', ''] },
        weekId: { $ifNull: ['$we._id', ''] },
        day: { $ifNull: ['$ojp.day', ''] },

        userId: { $ifNull: ['$us._id', ''] },
        outletStatus: { $ifNull: ['$ol.status', ''] },
        isActiveSaleRep: { $ifNull: ['$us.isActive', false] },
        displayDay: '$ojp.displayDay',
        startTimeWeek: '$we.startTime',
        isEdited: '$ojp.isEdited',
        visitStatus: '$ojp.visitStatus',
        journeyPlaningId: '$ojp._id',
      })
      .match({
        userId: { $in: saleRep },
        weekId: { $in: listIdWeek },
        isActiveSaleRep: true,
        outletStatus: { $in: [OutletStatus.ACTIVE] },
      })
      // .sort({ [`${orderBy}`]: orderDesc })
      .sort(sort)
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      })
      .exec();
    return result;
  }

  getWeekAndDayByGivenDay(date = new Date(), allWeeksOfCycle: JourneyPlanWeek[]) {
    const firstWeek = allWeeksOfCycle.find((w) => w.weekName === WeekNameType.WEEK_1);
    const diffDays = moment(date).tz(process.env.TZ).startOf('day').diff(moment(firstWeek.startTime), 'days');
    const day = (diffDays % 7) + 1;
    const week = Math.floor(diffDays / 7) + 1;
    return { day, week };
  }

  async getJourneyPlansByConnectedSORAndCycleId({
    soRelations,
    weeks,
    sort = {} as JourneyPlanSortOrder,
    skip = 0,
    limit = 10,
  }: {
    soRelations: ISor[];
    weeks: JourneyPlanWeek[];
    sort: JourneyPlanSortOrder;
    skip?: number;
    limit?: number;
  }) {
    const firstWeek = weeks.find((w) => w.weekName === WeekNameType.WEEK_1);
    const lastWeek = weeks.find((w) => w.weekName === WeekNameType.WEEK_4);
    const startOfCycleTime = firstWeek.startTime;
    const endOfCycleTime = moment(lastWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toDate();
    const weekFilters = await Promise.all(weeks.map((w) => w._id));
    const outletFilters = await Promise.all(soRelations.map((item) => item.outlet));
    const saleRepFilters = await Promise.all(soRelations.map((item) => item.saleRep));

    const listJourneyPlansQuery = this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        week: { $in: weekFilters },
        outlet: { $in: outletFilters },
        saleRep: { $in: saleRepFilters },
      })
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .lookup({
        localField: 'ojp.outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'o',
      })
      .unwind({
        path: '$o',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 's',
      })
      .unwind({
        path: '$s',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'ojp.week',
        from: 'journeyplanweeks',
        foreignField: '_id',
        as: 'w',
      })
      .unwind({
        path: '$w',
        preserveNullAndEmptyArrays: false,
      })
      .project({
        _id: '$ojp._id',
        w: '$w',
        ucc: '$o.ucc',
        outletName: '$o.name',
        outletClass: '$o.outletClass',
        area: '$o.address',
        saleRepId: '$s.saleRepId',
        visitStatus: '$ojp.visitStatus',
        cancel: '$ojp.cancel',
        displayDay: {
          $cond: [{ $eq: ['$ojp.rescheduled', false] }, '$ojp.day', '$ojp.rescheduledDay'],
        },
        updatedAt: '$ojp.updatedAt',
        sor: { $concat: [{ $toString: '$ojp.outlet' }, '_', { $toString: '$ojp.saleRep' }] },
        cycleWeek: '$w.startTime',
      });

    if (Object.keys(sort).length) {
      listJourneyPlansQuery.sort(sort);
    }

    listJourneyPlansQuery.facet({
      totalRecords: [
        {
          $count: 'totalItem',
        },
      ],
      data: [
        {
          $skip: skip >= 0 ? skip : 0,
        },
        {
          $limit: limit >= 1 ? limit : 1,
        },
      ],
    });

    const listJourneyPlansQueryResult: any = await listJourneyPlansQuery.exec();
    const totalRecords = listJourneyPlansQueryResult[0].totalRecords;
    return {
      totalItem: totalRecords.length ? totalRecords[0].totalItem : 0,
      data: listJourneyPlansQueryResult[0].data.map((jp) => {
        const { _id, ucc, outletName, outletClass, area, saleRepId, visitStatus, cancel, displayDay, sor, w } = jp;
        const weekDay = formatCurrentDay(displayDay, w?.startTime);
        const disconnected = soRelations.find((item) => item.sor === sor)?.disconnected;
        // const { week: cycleWeek } = this._baseJourneyPlanService.getWeekAndDayByGivenDay(new Date(displayDay), weeks);
        const cycleWeek = Object.values(WeekNameType).findIndex((item) => item === w.weekName) + 1;
        return {
          _id,
          ucc,
          outletName,
          outletClass,
          area,
          saleRepId,
          cycleWeek,
          weekDay,
          displayDay,
          editable: disconnected ? !disconnected : canEdit(displayDay, visitStatus, startOfCycleTime, endOfCycleTime, cancel),
        };
      }),
    };
  }

  async getDetailInformationJourneyPlanning(journeyPlanId: string): Promise<OutletJourneyPlanning> {
    return this._outletJourneyPlanningDocumentModel
      .findOne({
        _id: new Types.ObjectId(journeyPlanId),
      })
      .populate({
        path: 'outlet',
      })
      .populate({
        path: 'saleRep',
      })
      .populate({
        path: 'week',
        populate: {
          path: 'cycle',
        },
      })
      .exec();
  }

  async deleteJourneyPlansBySalesRepIdsAndWeeks(salesRepIds: string[], weeks: JourneyPlanWeek[]) {
    return this._outletJourneyPlanningDocumentModel.deleteMany({
      saleRep: { $in: salesRepIds.map((sid) => new Types.ObjectId(sid)) },
      week: { $in: weeks.map((w) => w._id) },
      visitStatus: VisitStatus.START_VISIT,
    });
  }

  async findJourneyPlansBySalesRepIdsAndWeeks(salesRepIds: string[], weeks: JourneyPlanWeek[]) {
    return this._outletJourneyPlanningDocumentModel.find({
      saleRep: { $in: salesRepIds.map((sid) => new Types.ObjectId(sid)) },
      week: { $in: weeks.map((w) => w._id) },
    });
  }

  findJourneyPlansBySalesRepIdsAndTimeRange(salesRepIds: string[], startOfCycle: Date, endOfCycle: Date, outlets: string[] = null) {
    return this._outletJourneyPlanningDocumentModel.find({
      saleRep: { $in: salesRepIds.map((sid) => new Types.ObjectId(sid)) },
      outlet: { $in: outlets.map((sid) => new Types.ObjectId(sid)) },
      $or: [
        { day: { $gte: startOfCycle.toISOString(), $lte: endOfCycle.toISOString() }, rescheduled: false },
        { rescheduledDay: { $gte: startOfCycle.toISOString(), $lte: endOfCycle.toISOString() }, rescheduled: true },
      ],
    });
  }

  async getLastDayJourneyPlanBySalesRepId(saleRepId: string, week: string, date: Date) {
    const endOfDay = moment(date).tz(process.env.TZ).endOf('d').toISOString();
    const startOfDay = moment(date).tz(process.env.TZ).startOf('day').toDate();

    return this._outletJourneyPlanningDocumentModel
      .findOne({
        saleRep: new Types.ObjectId(saleRepId),
        week: week,
        $or: [
          { day: { $lte: endOfDay, $gte: startOfDay }, rescheduled: false },
          { rescheduledDay: { $lte: endOfDay }, rescheduled: false },
        ],
      })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
  }

  async getUnExecutedPlannedVisitsToday(saleRepId: string) {
    const startOfDay = moment().tz(process.env.TZ).startOf('d').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('d').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .findOne({
        saleRep: new Types.ObjectId(saleRepId),
        visitStatus: VisitStatus.IN_PROGRESS,
        $or: [
          { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
          { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
        ],
      })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
  }

  async findJourneyPlansBySalesRepIdsAndCycleWeeks(salesRepIds: string[], cycleWeeks: JourneyPlanWeek[]) {
    return this._outletJourneyPlanningDocumentModel.find({
      saleRep: { $in: salesRepIds.map((sid) => new Types.ObjectId(sid)) },
      week: { $in: cycleWeeks.map((w) => w._id) },
    });
  }

  async getUnExecutedPlannedVisits(saleRepId: string, outletId: string): Promise<OutletJourneyPlanning[]> {
    const startOfDay = moment().tz(process.env.TZ).startOf('d').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
        visitStatus: VisitStatus.START_VISIT,
        $or: [
          { day: { $gte: new Date(startOfDay) }, rescheduled: false },
          { rescheduledDay: { $gte: new Date(startOfDay) }, rescheduled: true },
        ],
      })
      .sort({ day: -1 })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
  }

  async getLastPlannedVisitsGroupByDate(saleRepId: string) {
    const startOfDay = moment().tz(process.env.TZ).startOf('d').toISOString();
    return this._outletJourneyPlanningDocumentModel
      .aggregate()
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .match({
        'ojp.saleRep': new Types.ObjectId(saleRepId),
        $or: [
          { 'ojp.day': { $gte: new Date(startOfDay) }, 'ojp.rescheduled': false },
          { 'ojp.rescheduledDay': { $gte: new Date(startOfDay) }, 'ojp.rescheduled': true },
        ],
      })
      .sort({ day: 1 })
      .addFields({
        'ojp.groupDate': {
          $cond: [{ $eq: ['$ojp.rescheduled', false] }, '$ojp.day', '$ojp.rescheduledDay'],
        },
      })
      .group({
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$ojp.groupDate' },
        },
        day: { $last: '$ojp.groupDate' },
        week: { $last: '$ojp.week' },
        saleRep: { $last: '$ojp.saleRep' },
        outlet: { $last: '$ojp.outlet' },
        rescheduled: { $last: '$ojp.rescheduled' },
        _idOjp: { $last: '$ojp._id' },
      })
      .sort({ _id: -1 })
      .exec();
  }

  async getLastPlannedVisitsByDate(saleRepId: string, startOfDay: string) {
    return this._outletJourneyPlanningDocumentModel
      .aggregate()
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .match({
        'ojp.saleRep': new Types.ObjectId(saleRepId),
        $or: [
          { 'ojp.day': { $gte: new Date(startOfDay) }, 'ojp.rescheduled': false },
          { 'ojp.rescheduledDay': { $gte: new Date(startOfDay) }, 'ojp.rescheduled': true },
        ],
      })
      .sort({ day: 1 })
      .addFields({
        'ojp.groupDate': {
          $cond: [{ $eq: ['$ojp.rescheduled', false] }, '$ojp.day', '$ojp.rescheduledDay'],
        },
      })
      .group({
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$ojp.groupDate' },
        },
        day: { $last: '$ojp.groupDate' },
        week: { $last: '$ojp.week' },
        saleRep: { $last: '$ojp.saleRep' },
        outlet: { $last: '$ojp.outlet' },
        rescheduled: { $last: '$ojp.rescheduled' },
        _idOjp: { $last: '$ojp._id' },
      })
      .sort({ _id: -1 })
      .exec();
  }

  async assignUnExecutedPlannedVisits(saleRepOldId: string, saleRepId: string, outlet: Outlet) {
    const unExecutePlannedVisitsGroupByDate: OutletJourneyPlanning[] = await this.getUnExecutedPlannedVisits(saleRepOldId, outlet._id);
    const distributorUser = await this._distributorUserRelationService.findByUserId(saleRepId);
    const { startingTimeframe, timeInterval } = await this._baseJourneyPlanSettingService.getBaseJourneyPlanSettingByDistributor(distributorUser.distributor._id.toString());
    const saleRep = await this._userService.findById(saleRepId);
    const startingTimeframeArray = startingTimeframe.split(':');
    const createData = [];
    let canBeAssigned = true;

    if (!isEmptyObjectOrArray(unExecutePlannedVisitsGroupByDate)) {
      let newDate = null;
      for (const ojp of unExecutePlannedVisitsGroupByDate) {
        const day = ojp.rescheduled ? ojp.rescheduledDay : ojp.day;
        const lastPlannedVisitsGroupByDate = await this.getLastPlannedVisitsByDate(saleRepId, day.toISOString());
        const checkLastDates = lastPlannedVisitsGroupByDate?.filter((last) => {
          const lDay = moment(last.day).format('YYYY-MM-DD');
          const mDay = moment(day).format('YYYY-MM-DD');

          if (lDay == mDay) return ojp;
        });

        if (!isEmptyObjectOrArray(checkLastDates)) {
          const countSlots = this._baseJourneyPlanSettingService.calculateTimeSlotByTimeIntervalAndStartingTime(moment(day).tz(process.env.TZ).format('YYYY-MM-DD'), timeInterval);
          if (countSlots < checkLastDates.length) {
            canBeAssigned = false;
            return false;
          }
          newDate = moment(checkLastDates[0].day).tz(process.env.TZ).add(Number(timeInterval), 'minutes');
          //check end time of day (23:30)
          if (!newDate.isSame(moment(checkLastDates[0].day).tz(process.env.TZ), 'date')) {
            return false;
          }
        } else {
          newDate = moment(day).tz(process.env.TZ).set({
            hour: startingTimeframeArray[0],
            minute: startingTimeframeArray[1],
          });
        }
        createData.push({
          saleRep,
          day: newDate,
          week: ojp.week,
          outlet: ojp.outlet._id,
        });
      }

      if (!canBeAssigned) {
        return false;
      }
      for (const row of createData) {
        await this.create(row);
      }

      for (const row of unExecutePlannedVisitsGroupByDate) {
        await this.delete(row._id);
      }
    }
    return true;
  }

  async getLatestPlanOfSalesRepByGivenDay(saleRepId: string, givenDay: Date) {
    const startOfDay = moment(givenDay).tz(process.env.TZ).startOf('day').toDate();
    const endOfDay = moment(givenDay).tz(process.env.TZ).endOf('day').toDate();
    const journeyPlannings = await this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        saleRep: new Types.ObjectId(saleRepId),
      })
      .addFields({
        displayDay: {
          $cond: [{ $eq: ['$rescheduled', false] }, '$day', '$rescheduledDay'],
        },
      })
      .match({
        displayDay: {
          $gte: startOfDay,
          $lte: endOfDay,
        },
      })
      .sort({
        displayDay: -1,
      })
      .limit(1)
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .exec();
    return !isEmptyObjectOrArray(journeyPlannings) ? [journeyPlannings[0]] : [];
  }

  async deleteUnExecutedPlannedVisits(saleRepId: string): Promise<any> {
    const startOfDay = moment().tz(process.env.TZ).startOf('d').toISOString();
    return this._outletJourneyPlanningDocumentModel.deleteMany({
      saleRep: new Types.ObjectId(saleRepId),
      visitStatus: VisitStatus.START_VISIT,
      $or: [
        { day: { $gte: new Date(startOfDay) }, rescheduled: false },
        { rescheduledDay: { $gte: new Date(startOfDay) }, rescheduled: true },
      ],
    });
  }

  async deleteUnExecutedPlannedVisitsByOutlet(outletId: string): Promise<any> {
    const startOfDay = moment().tz(process.env.TZ).startOf('d').toISOString();
    return this._outletJourneyPlanningDocumentModel.deleteMany({
      outlet: new Types.ObjectId(outletId),
      visitStatus: VisitStatus.START_VISIT,
      $or: [
        { day: { $gte: new Date(startOfDay) }, rescheduled: false },
        { rescheduledDay: { $gte: new Date(startOfDay) }, rescheduled: true },
      ],
    });
  }

  async deleteByCondition(condition: Record<string, any>) {
    return this._outletJourneyPlanningDocumentModel.deleteMany(condition);
  }

  async setMissedReasonForVisits({ dto, currentUser, i18n }: { dto: MissedReasonForVisitsDto; currentUser: User & { roles: string[] }; i18n: I18nContext }) {
    const journeyPlans = await this.validateMissedReasonForVisitsDto({ dto, currentUser, i18n });
    const cancellationReason = dto.reasonKey?.trim();
    const reason = !cancellationReason || cancellationReason === '-' ? null : cancellationReason;
    await this.model.updateMany(
      {
        _id: {
          $in: dto.journeyPlanIds,
        },
      },
      {
        updatedAt: new Date(),
        cancellationReason: reason,
        cancel: !!reason,
      },
      {
        populate: 'outlet',
      },
    );

    return journeyPlans.map((item) => {
      item.cancel = !!reason;
      item.cancellationReason = i18n.t(reason);
      return item;
    });
  }

  private async validateMissedReasonForVisitsDto({ dto, currentUser, i18n }: { dto: MissedReasonForVisitsDto; currentUser: User & { roles: string[] }; i18n: I18nContext }) {
    const { journeyPlanIds, reasonKey } = dto;
    const cancellationReason = reasonKey?.trim();

    // check reason key
    if (currentUser.roles.some((r) => r === ConstantRoles.SALE_REP)) {
      if (!Object.values(CancellationReasonType).some((reason) => reason === cancellationReason)) {
        throw new BadRequestException('plan.not_found_reason');
      }
    }

    const journeyPlans = await this.findUnVisitedPlans(journeyPlanIds);
    if (journeyPlans.length !== journeyPlanIds.length) {
      throw new NotFoundException(i18n.t('plan.not_found'));
    }

    let disUserAdmin = null;
    let disSalesRepObj = {};
    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      journeyPlans.forEach((journeyPlan) => {
        if (!this.isSameCurrentMonth(journeyPlan.displayDay)) {
          throw new BadRequestException('plan.cannot_set_reason');
        }
      });

      disUserAdmin = await this._distributorUserRelationService.findByUserAdminId(currentUser._id.toString());
      if (!disUserAdmin) {
        throw new UnauthorizedException('plan.unauthorized');
      }

      const disSalesReps = await this._distributorUserRelationService.findByUserIds(journeyPlans.map((item) => item.saleRep._id.toString()));
      disSalesRepObj = disSalesReps.reduce(
        (pre, curr) => ({
          ...pre,
          [curr.user._id.toString()]: curr,
        }),
        {},
      );
    }

    journeyPlans.forEach((journeyPlan) => {
      if (currentUser.roles.some((r) => r === ConstantRoles.SALE_REP)) {
        if (journeyPlan.saleRep.toString() !== currentUser._id.toString()) {
          throw new UnauthorizedException('plan.unauthorized');
        }
        if (journeyPlan.cancel) {
          throw new BadRequestException('plan.cannot_set_reason_cancel_plan');
        }
        const canSetReason = this.isAbleToSetMissedVisitReason(journeyPlan.displayDay);
        if (!canSetReason) {
          throw new BadRequestException('plan.cannot_set_reason');
        }
      }
      if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
        const disSalesRep = disSalesRepObj[journeyPlan.saleRep._id.toString()];
        if (disSalesRep?.distributor._id.toString() !== disUserAdmin?.distributor._id.toString()) {
          throw new UnauthorizedException('plan.unauthorized');
        }
      }
    });

    return journeyPlans;
  }

  async setMissedReasonForOutlet({ planId, reasonId }: MissedReasonForOutletDto, i18n: I18nContext) {
    const reason = await this._journeyPlanMissedReasonsService.findById(reasonId);

    if (!reason) {
      throw new BadRequestException(i18n.t('plan.not_found_reason'));
    }

    const plan = await this.model
      .findById(planId)
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
    if (!plan) {
      throw new BadRequestException(i18n.t('plan.not_found'));
    }

    if (plan.visitStatus === VisitStatus.COMPLETED) {
      throw new BadRequestException(i18n.t('plan.cannot_edit_data'));
    }

    const absence = await this._journeyPlanMissedReasonHistoriesService.create({
      journeyPlan: new Types.ObjectId(plan._id.toString()),
      missedReason: new Types.ObjectId(reasonId),
    });

    await this._outletJourneyPlanningDocumentModel.updateOne(
      {
        _id: plan._id,
      },
      {
        visitStatus: VisitStatus.START_VISIT,
        missedReason: new Types.ObjectId(reasonId),
        $unset: {
          cancel: '',
          cancellationReason: '',
        },
      },
    );

    return absence;
  }

  async setMissedReasonForOutletKH({ planId, reasonId, evidenceImages }: MissedReasonForOutletDto, i18n: I18nContext, currentUser: User & { roles: string[] }) {
    const reason = await this.missReasonModel.findById(reasonId);

    if (!reason) {
      throw new BadRequestException(i18n.t('plan.not_found_reason'));
    }

    const plan = await this.model
      .findById(planId)
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
    if (!plan) {
      throw new BadRequestException(i18n.t('plan.not_found'));
    }

    const listEvidenceImages = [];
    const isAdmin = currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN);
    if (!isAdmin) {
      const isRequireEvidence = reason.requireEvidenceSettings?.REPORT_OUTLET;
      if (isRequireEvidence && (!evidenceImages || !evidenceImages.length)) {
        throw new BadRequestException(i18n.t('plan.evidence_required'));
      }
    }

    if (evidenceImages?.length) {
      for (const iterator of evidenceImages) {
        const file = await this._fileService.findOne({ _id: new Types.ObjectId(iterator) });
        if (!file) {
          throw new BadRequestException(i18n.t('plan.not_found_evidence'));
        }
        listEvidenceImages.push(file._id);
      }
    }

    if (plan.visitStatus === VisitStatus.COMPLETED) {
      throw new BadRequestException(i18n.t('plan.cannot_edit_data'));
    }

    const oldHistory = await this._journeyPlanMissedReasonHistoriesService.findOne({ journeyPlan: new Types.ObjectId(plan._id.toString()) });
    if (oldHistory) {
      try {
        for (const iterator of oldHistory.evidenceImages) {
          const image = await this._fileService.findOne({ _id: new Types.ObjectId(iterator._id) });
          await Promise.all([
            this._fileService.deleteBlobIfItExists(image?.folder, image?.name),
            this._fileService.deleteMultipleBlobs(image?.compressImages),
            this._fileService.delete(iterator._id),
          ]);
        }
      } catch (e) {
        printLog('oldHistory error:', e);
      }

      await this._journeyPlanMissedReasonHistoriesService.delete(oldHistory._id);
    }
    const absence = await this._journeyPlanMissedReasonHistoriesService.create({
      journeyPlan: new Types.ObjectId(plan._id.toString()),
      missedReason: new Types.ObjectId(reasonId),
      evidenceImages: listEvidenceImages,
    });

    await this._outletJourneyPlanningDocumentModel.updateOne(
      {
        _id: plan._id,
      },
      {
        visitStatus: VisitStatus.START_VISIT,
        missedReason: new Types.ObjectId(reasonId),
        outletReport: absence._id,
        $unset: {
          cancel: '',
          cancellationReason: '',
        },
      },
    );
    await this._fileService.removeExpiredDate(listEvidenceImages);
    return absence;
  }

  async setMissedReasonForOutletOffline({ planId, reasonId, evidenceImages }: MissedReasonForOutletDto, i18n: I18nContext) {
    let success = false;
    let message = '';
    try {
      const reason = await this.missReasonModel.findById(reasonId);
      if (!reason) {
        throw new BadRequestException(i18n.t('plan.not_found_reason'));
      }
      const plan = await this.model.findById(planId).exec();
      if (!plan) {
        throw new BadRequestException(i18n.t('plan.not_found'));
      }
      if (plan.visitStatus === VisitStatus.COMPLETED) {
        throw new BadRequestException(i18n.t('plan.cannot_edit_data'));
      }

      const listEvidenceImages = [];
      const isRequireEvidence = reason.requireEvidenceSettings?.REPORT_OUTLET;
      if (isRequireEvidence && (!evidenceImages || !evidenceImages.length)) {
        throw new BadRequestException(i18n.t('plan.evidence_required'));
      }

      if (evidenceImages?.length) {
        for (const iterator of evidenceImages) {
          const file = await this._fileService.findOne({ _id: new Types.ObjectId(iterator) });
          if (!file) {
            throw new BadRequestException(i18n.t('plan.not_found_evidence'));
          }
          listEvidenceImages.push(file._id);
        }
      }
      const oldHistory = await this._journeyPlanMissedReasonHistoriesService.findOne({ journeyPlan: new Types.ObjectId(plan._id.toString()) });
      if (oldHistory) {
        try {
          for (const iterator of oldHistory.evidenceImages) {
            const image = await this._fileService.findOne({ _id: new Types.ObjectId(iterator._id) });
            await Promise.all([
              this._fileService.deleteBlobIfItExists(image?.folder, image?.name),
              this._fileService.deleteMultipleBlobs(image?.compressImages),
              this._fileService.delete(iterator._id),
            ]);
          }
        } catch (e) {}
        await this._journeyPlanMissedReasonHistoriesService.delete(oldHistory._id);
      }
      const absence = await this._journeyPlanMissedReasonHistoriesService.create({
        journeyPlan: new Types.ObjectId(plan._id.toString()),
        missedReason: new Types.ObjectId(reasonId),
        evidenceImages: listEvidenceImages,
      });

      await this._outletJourneyPlanningDocumentModel.updateOne(
        {
          _id: plan._id,
        },
        {
          visitStatus: VisitStatus.START_VISIT,
          missedReason: new Types.ObjectId(reasonId),
          outletReport: absence._id,
          $unset: {
            cancel: '',
            cancellationReason: '',
          },
        },
      );
      await this._fileService.removeExpiredDate(listEvidenceImages);
      success = true;
    } catch (error) {
      message = error.message;
    }
    return { key: 'skipOutlet', success, message };
  }

  async unsetMissedReasonForOutlet({ planId }: MissedReasonForOutletDto, i18n: I18nContext) {
    const plan = await this.model
      .findById(planId)
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();
    if (!plan) {
      throw new BadRequestException(i18n.t('plan.not_found'));
    }

    if (plan.visitStatus === VisitStatus.COMPLETED) {
      throw new BadRequestException(i18n.t('plan.cannot_edit_data'));
    }

    await this._outletJourneyPlanningDocumentModel.updateOne(
      {
        _id: plan._id,
      },
      {
        visitStatus: VisitStatus.START_VISIT,
        $unset: {
          missedReason: 1,
          outletReport: 1,
        },
      },
    );

    return true;
  }

  async setMissedReasonForOutlets(dto: MissedReasonForOutletsDto, currentUser: User) {
    const currentDate = moment().tz(process.env.TZ).startOf('date');
    const fromDate = moment(dto.fromDate).tz(process.env.TZ).startOf('date');
    const toDate = moment(dto.toDate).tz(process.env.TZ).endOf('date');

    if (fromDate.isBefore(currentDate)) {
      throw new BadRequestException('plan.missed_visit_reason.invalid_from_date');
    }
    const cycle = await this._journeyPlanWeekService.getCycleByGivenDay();
    const { endOfCycle } = await this._journeyPlanWeekService.getTimeRangeOfGivenCycle(cycle._id.toString());
    if (toDate.isBefore(fromDate) || toDate.isAfter(endOfCycle)) {
      throw new BadRequestException('plan.missed_visit_reason.invalid_to_date');
    }

    const reason = await this.missReasonModel.findById(dto.reasonId);
    if (!reason || !reason.locations.includes(MissReasonLocation.REPORT_ABSENCES)) {
      throw new BadRequestException('plan.not_found_reason');
    }

    const journeyPlans = await this.model
      .find({
        saleRep: currentUser._id,
        visitStatus: { $ne: VisitStatus.COMPLETED },
        $or: [
          { day: { $gte: fromDate.toISOString(), $lte: toDate.toISOString() }, rescheduled: false },
          { rescheduledDay: { $gte: fromDate.toISOString(), $lte: toDate.toISOString() }, rescheduled: true },
        ],
      })
      .exec();

    await this.model.updateMany(
      {
        _id: {
          $in: journeyPlans.map((item) => item._id),
        },
      },
      {
        visitStatus: VisitStatus.START_VISIT,
        missedReason: new Types.ObjectId(dto.reasonId),
        $unset: {
          cancel: 1,
          cancellationReason: 1,
          outletReport: 1,
        },
      },
    );

    return Promise.all(
      journeyPlans.map(async (item) => {
        return this._journeyPlanMissedReasonHistoriesService.create({
          journeyPlan: item._id,
          missedReason: new Types.ObjectId(dto.reasonId),
        });
      }),
    );
  }

  async countAffectedOutlets(dto: CountAffectedOutletsDto, currentUser: User) {
    const fromDate = moment(dto.fromDate).tz(process.env.TZ).startOf('date');
    const toDate = moment(dto.toDate).tz(process.env.TZ).endOf('date');
    const journeyPlans = await this.model
      .find({
        saleRep: currentUser._id,
        visitStatus: { $ne: VisitStatus.COMPLETED },
        $or: [
          { day: { $gte: fromDate.toISOString(), $lte: toDate.toISOString() }, rescheduled: false },
          { rescheduledDay: { $gte: fromDate.toISOString(), $lte: toDate.toISOString() }, rescheduled: true },
        ],
      })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      })
      .exec();

    return journeyPlans.reduce((pre, curr) => (pre.includes(curr.outlet._id.toString()) ? pre : [...pre, curr.outlet._id.toString()]), []).length;
  }

  private async getTodayPlanOfOutletBySalesRep(outletId: string, salesRepId: string) {
    const startOfDay = moment().tz(process.env.TZ).startOf('day').toISOString();
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toISOString();

    return this._outletJourneyPlanningDocumentModel
      .findOne({
        outlet: new Types.ObjectId(outletId),
        saleRep: new Types.ObjectId(salesRepId),
        $or: [
          { day: { $gte: startOfDay, $lte: endOfDay }, rescheduled: false },
          { rescheduledDay: { $gte: startOfDay, $lte: endOfDay }, rescheduled: true },
        ],
      })
      .exec();
  }

  async getDistributionAchievementBySalesRep({ distributorId, fromDate, toDate, top }: DistributionAchievementDto) {
    const dateCondition = {};
    if (fromDate) {
      const from = moment(fromDate).tz(process.env.TZ).startOf('day').toDate();
      dateCondition['$gte'] = from;
    }
    if (toDate) {
      const to = moment(toDate).tz(process.env.TZ).endOf('day').toDate();
      dateCondition['$lte'] = to;
    }

    const aggregation = this._outletJourneyPlanningDocumentModel.aggregate().allowDiskUse(true);

    const saleRepTest = await this._userService.findAll({ isTestAccount: true });
    if (!isEmptyObjectOrArray(saleRepTest)) {
      aggregation.match({
        saleRep: { $nin: saleRepTest.map((sid: any) => new Types.ObjectId(sid)) },
      });
    }

    if (Object.keys(dateCondition).length) {
      aggregation.match({
        $or: [
          { day: dateCondition, rescheduled: false },
          { rescheduledDay: dateCondition, rescheduled: true },
        ],
      });
    }

    if (distributorId) {
      const distributor = await this._distributorService.findOne({ distributorId: distributorId });
      if (!isEmptyObjectOrArray(distributor)) {
        // aggregation
        // .lookup({
        //   localField: 'saleRep',
        //   from: 'distributoruserrelations',
        //   foreignField: 'user',
        //   as: 'dis',
        // })
        // .unwind({
        //   path: '$dis',
        //   preserveNullAndEmptyArrays: false,
        // })
        // .match({
        //   'dis.distributor': distributor?._id,
        // });

        const depotIds = distributor?.depots?.map((d) => d.id)?.filter(Boolean);

        if (depotIds && depotIds.length > 0) {
          const matchingOutletIds = await this._outletsService.findByDepotIds(depotIds);

          if (matchingOutletIds && matchingOutletIds.length > 0) {
            const outletIdList = matchingOutletIds.map((o) => o._id);

            aggregation.match({
              outlet: { $in: outletIdList },
            });
          }
        }
      }
    }

    aggregation
      .group({
        _id: '$saleRep',
        visited: {
          $sum: {
            $cond: [{ $eq: ['$visitStatus', VisitStatus.COMPLETED] }, 1, 0],
          },
        },
        total: {
          $sum: 1,
        },
      })
      .addFields({
        percent: {
          $multiply: [{ $divide: ['$visited', `$total`] }, 100],
        },
      })
      .sort({ percent: -1 });

    if (top) {
      aggregation.limit(+top);
    }

    aggregation
      .lookup({
        from: 'users',
        as: 'salesRep',
        foreignField: '_id',
        localField: '_id',
      })
      .unwind('$salesRep')
      .project({
        _id: 0,
        visited: 1,
        total: 1,
        percent: 1,
        salesRep: {
          id: '$_id',
          username: '$salesRep.username',
          saleRepId: '$salesRep.saleRepId',
        },
      });

    return aggregation.exec();
  }

  async getDistributionOutletCoverage({ distributorId, fromDate, toDate }: DistributionOutletCoverageDto, backToDate = 0) {
    const dateCondition = {};
    let from, to;
    if (fromDate) {
      from = moment(fromDate).tz(process.env.TZ).startOf('day').toDate();
      dateCondition['$gte'] = from;
    }
    if (toDate) {
      to = moment(toDate).tz(process.env.TZ).endOf('day').toDate();
      dateCondition['$lte'] = to;
    }

    if (backToDate > 0) {
      if (fromDate) {
        from = moment(fromDate).tz(process.env.TZ).startOf('day').subtract(backToDate, 'd').toDate();
        dateCondition['$gte'] = from;

        to = moment(fromDate).tz(process.env.TZ).endOf('day').toDate();
        dateCondition['$lte'] = to;
      }
    }

    const aggregation = this._outletJourneyPlanningDocumentModel.aggregate().allowDiskUse(true);
    const saleRepTest = await this._userService.findAll({ isTestAccount: true });
    if (!isEmptyObjectOrArray(saleRepTest)) {
      aggregation.match({
        saleRep: { $nin: saleRepTest.map((sid: any) => new Types.ObjectId(sid)) },
      });
    }

    if (Object.keys(dateCondition).length) {
      aggregation.match({
        $or: [
          { day: dateCondition, rescheduled: false },
          { rescheduledDay: dateCondition, rescheduled: true },
        ],
      });
    }

    if (distributorId) {
      const distributor = await this._distributorService.findOne({ distributorId });

      const depotIds = distributor?.depots?.map((d) => d.id)?.filter(Boolean);

      if (depotIds && depotIds.length > 0) {
        const matchingOutletIds = await this._outletsService.findByDepotIds(depotIds);

        if (matchingOutletIds && matchingOutletIds.length > 0) {
          const outletIdList = matchingOutletIds.map((o) => o._id);

          aggregation.match({
            outlet: { $in: outletIdList },
          });
        }
      }
    }
    aggregation
      .addFields({
        status: { $cond: [{ $eq: ['$visitStatus', VisitStatus.COMPLETED] }, 'visited', 'missed'] },
      })
      .group({
        _id: '$status',
        count: {
          $sum: 1,
        },
      });
    const data = await aggregation.exec();
    const total = data.reduce((pre, curr) => pre + curr.count, 0);
    const visited = data.filter((item) => item._id === 'visited').reduce((pre, curr) => pre + curr.count, 0);
    const missed = data.filter((item) => item._id === 'missed').reduce((pre, curr) => pre + curr.count, 0);

    return { total, visited, missed, missedPercent: (missed * 100) / total };
  }
  async clearWrongDataJp() {
    await this.deleteByCondition({
      day: null,
      week: null,
    });
  }

  async updateAllPendingJPToNewStatus() {
    await this.model.updateMany(
      {
        visitStatus: 'PENDING',
      },
      {
        visitStatus: VisitStatus.START_VISIT,
      },
    );
  }

  async executeVisitOffline(outletId: string, executeVisitDto: ExecuteVisitDto, currentUser: User, jpId: string) {
    let success = false;
    let message = '';
    try {
      const anotherInProgressVisit = await this.getInProgressTodayVisitBySaleRepIdAndExcludeOutletId(currentUser._id, outletId);
      if (anotherInProgressVisit) {
        await this.update(anotherInProgressVisit._id, {
          visitStatus: VisitStatus.START_VISIT,
          updatedAt: new Date(),
          $unset: {
            missedReason: 1,
            outletReport: 1,
          },
        });
        await this._saleRepExecutionAvailabilityService.deleteAvailabilityBySaleRepIdAndJourneyPlanId(currentUser._id, anotherInProgressVisit._id.toString());
      }
      const jp = await this._outletJourneyPlanningDocumentModel.findOne({ _id: new Types.ObjectId(jpId) }).populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      });
      if (jp && jp.visitStatus !== VisitStatus.COMPLETED) {
        const { longitude, latitude, time } = executeVisitDto;
        let locationRange = null;
        if (!jp.locationRange && latitude && longitude && jp.outlet.address) {
          const address1 = `${latitude}, ${longitude}`;
          const outletAddress = jp.outlet.address;
          //get Destination
          const destinations = await this._googleMapsService.getDirection(address1, outletAddress);
          if (destinations?.length) {
            locationRange = destinations[0].distance.value;
          }
        }
        const visitStatus = VisitStatus.IN_PROGRESS;
        const result = await this.update(jpId, {
          location: {
            latitude: latitude,
            longitude: longitude,
          },
          locationRange,
          visitStatus,
          startVisitDate: time ? new Date(time) : new Date(),
          updatedAt: new Date(),
          $unset: {
            missedReason: 1,
            outletReport: 1,
          },
        });
        success = true;
      } else {
        message = 'JP not found!';
      }
    } catch (error) {
      message = error.message;
    }
    return { key: 'executeVisit', success, message };
  }

  async getAllPlansBySaleAndDate(salesRepId: string, startDate: Date, endDate: Date) {
    return this._outletJourneyPlanningDocumentModel
      .find({
        saleRep: new Types.ObjectId(salesRepId),
        $or: [
          {
            day: {
              $gte: startDate,
              $lte: endDate,
            },
            rescheduled: false,
          },
          {
            rescheduledDay: {
              $gte: startDate,
              $lte: endDate,
            },
            rescheduled: true,
          },
        ],
      })
      .populate(['outlet'])
      .select(['_id', 'rescheduled', 'rescheduledDay', 'day', 'visitStatus', 'hasOrder', 'checkStock', 'saleRep', 'startVisitDate', 'visitedDay', 'locationRange']);
  }

  async getPlansBySaleRep({ outletIds, saleRep }: { outletIds: any; saleRep: any }) {
    const weeks = await this._journeyPlanWeekService.getWeeksOfCurrentCycle();
    const weekIds = weeks.map((week) => week._id);
    const [existedDistributorUserRelation] = await this._distributorUserRelationService.getDistributorUserRelation({ 'u._id': saleRep });
    const distributor = existedDistributorUserRelation?.dis;
    const plans = await this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        week: { $in: weekIds },
        outlet: { $in: outletIds },
        saleRep,
      })
      // .lookup({
      //   localField: 'saleRep',
      //   from: 'distributoruserrelations',
      //   foreignField: 'user',
      //   as: 'salesRepDistributorRelation',
      // })
      // .unwind({
      //   path: '$salesRepDistributorRelation',
      //   preserveNullAndEmptyArrays: false,
      // })
      // .lookup({
      //   localField: 'salesRepDistributorRelation.distributor',
      //   from: 'distributors',
      //   foreignField: '_id',
      //   as: 'distributor',
      // })
      // .unwind({
      //   path: '$distributor',
      //   preserveNullAndEmptyArrays: false,
      // })
      .lookup({
        localField: 'week',
        from: 'journeyplanweeks',
        foreignField: '_id',
        as: 'week',
      })
      .unwind({
        path: '$week',
        preserveNullAndEmptyArrays: false,
      })
      .project({
        id: '$_id',
        createdAt: '$createdAt',
        outlet: '$outlet',
        week: {
          id: '$week._id',
          name: '$week.weekName',
          startTime: '$week.startTime',
        },
        date: {
          $ifNull: ['$rescheduledDay', '$day'],
        },
        // distributor: {
        //   distributorId: '$distributor.distributorId',
        //   name: '$distributor.distributorName',
        // },
        visitStatus: '$visitStatus',
      })
      .exec();
    return plans.map((plan) => {
      return {
        ...plan,
        distributor: {
          distributorId: distributor?.distributorId,
          name: distributor?.distributorName,
        },
      };
    });
  }

  private async getLatestVisitedTime(jpList: any, firstDay: Date, lastDay: Date) {
    try {
      if (isEmptyObjectOrArray(jpList)) {
        return jpList;
      }
      const plans = await this._outletJourneyPlanningDocumentModel.aggregate([
        {
          $match: {
            outlet: { $in: jpList.map((jp) => new Types.ObjectId(jp.outlet)) },
            visitedDay: { $nin: [null, ''] },
            $or: [
              { day: { $gte: firstDay, $lte: lastDay }, rescheduled: false },
              {
                rescheduledDay: { $gte: firstDay, $lte: lastDay },
                rescheduled: true,
              },
            ],
          },
        },
        {
          $sort: { visitedDay: -1 },
        },
        {
          $group: {
            _id: '$outlet',
            latestPlan: { $first: '$$ROOT' },
          },
        },
        {
          $replaceRoot: { newRoot: '$latestPlan' },
        },
        {
          $project: {
            _id: 1,
            outlet: 1,
            visitedDay: 1,
          },
        },
      ]);

      return jpList?.map((jp) => {
        const latestVisitedTime = plans?.find((p) => p.outlet.toString() == jp.outlet.toString())?.visitedDay;
        return { ...jp, visitedDate: jp.visitedDay || latestVisitedTime, latestVisitedTime };
      });
    } catch (e) {
      printLog(e);
    }
  }

  public async calculateShareOfStock(outletId: string, fromDateFilter: Date, toDateFilter: Date) {
    const res = {
      hnk: 0,
      competitor: 0,
    };
    try {
      const convertToCarton = (await this.settingsService.getConfigExcel())?.convertToCarton;
      const listJp = await this._outletJourneyPlanningDocumentModel
        .find({
          outlet: new Types.ObjectId(outletId),
          $or: [
            {
              day: {
                $gte: fromDateFilter,
                $lte: toDateFilter,
              },
              rescheduled: false,
            },
            {
              rescheduledDay: {
                $gte: fromDateFilter,
                $lte: toDateFilter,
              },
              rescheduled: true,
            },
          ],
        })
        .populate(['outlet'])
        .select(['_id', 'rescheduled', 'rescheduledDay', 'day', 'visitStatus', 'hasOrder', 'checkStock', 'saleRep', 'startVisitDate', 'visitedDay', 'locationRange']);
      listJp?.forEach((item) => {
        if (item.checkStock) {
          for (const element of item.checkStock.listProductsChecked) {
            const pcs = convertToCarton?.find((p) => p.pcs === element.sku);
            res.hnk += pcs ? pcs.pcsMpsToCarton * element.check_stock_quantity : element.check_stock_quantity;
          }
          for (const element of item.checkStock.listCompetitorsChecked) {
            res.competitor += element.check_stock_quantity;
          }
        }
      });
    } catch (error) {
      printLog('calculateShareOfStock', error);
    }
    return { ...res, hnk: parseFloat(res.hnk?.toFixed(2)) };
  }

  async getLatestPlanVisitedOfOutlet(outletId: any, startOfDay: Date, endOfDay: Date) {
    const journeyPlannings = await this._outletJourneyPlanningDocumentModel
      .aggregate()
      .match({
        outlet: new Types.ObjectId(outletId),
        visitedDay: { $ne: null },
      })
      .addFields({
        displayDay: {
          $cond: [{ $eq: ['$rescheduled', false] }, '$day', '$rescheduledDay'],
        },
      })
      .match({
        displayDay: {
          $gte: startOfDay,
          $lte: endOfDay,
        },
      })
      .sort({
        displayDay: -1,
      })
      .project({
        _id: 0,
        ojp: '$$ROOT',
      })
      .exec();
    const now = new Date();
    if (!isEmptyObjectOrArray(journeyPlannings)) {
      return {
        latestVisitedDay: journeyPlannings[0].ojp.visitedDay,
        freq: (new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate() / journeyPlannings?.length).toFixed(2),
      };
    }

    return {
      latestVisited: null,
      freq: 0,
    };
  }
}
