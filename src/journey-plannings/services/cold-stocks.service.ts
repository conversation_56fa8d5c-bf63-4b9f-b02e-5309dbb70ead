import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ColdStock, ColdStockDocument } from '../schemas/cold-stock.schema';
import { AnyKeys, Model, Types } from 'mongoose';
import { I18nContext } from 'nestjs-i18n';
import { JourneyPlanVisitStep, OutletJourneyPlanning, OutletJourneyPlanningDocument } from '../schemas/outlet-journey-planning.schema';
import { UpdateColdStockDto } from '../dtos/update-cold-stock.dto';
import { FilesService } from 'src/files/services';
import { isEmptyObjectOrArray } from '../../utils';

@Injectable()
export class ColdStocksService {
  constructor(
    @InjectModel(ColdStock.name)
    private readonly coldStockModel: Model<ColdStockDocument>,
    @InjectModel(OutletJourneyPlanning.name)
    private readonly planModel: Model<OutletJourneyPlanningDocument>,
    private readonly filesService: FilesService,
  ) {}

  async getColdStockByJourneyPlan({ planId, salesRepId, i18n }: { planId: string; salesRepId: string; i18n: I18nContext }) {
    const plan = await this.checkPermission({ planId, salesRepId, i18n });

    // in logic, one journey plan should have only 1 cold stock
    // sort by updatedAt here just make sure we always get latest cold stock of journey plan in case there's something wrong with data (have two or more cold stocks...)
    let coldStock = await this.coldStockModel.findOne({ journeyPlan: plan._id }, {}, { sort: { updatedAt: -1 } }).populate('images');

    // get latest cold stock
    if (!coldStock) {
      coldStock = await this.coldStockModel.findOne({ outlet: plan.outlet }, {}, { sort: { updatedAt: -1 } }).populate('images');
    }

    return this.transformColdStock(coldStock);
  }

  async getColdStockByJpAndOulet({ planId, outlet }: { planId: Types.ObjectId; outlet: Types.ObjectId }) {
    let coldStock = await this.coldStockModel.findOne({ journeyPlan: planId }, {}, { sort: { updatedAt: -1 } }).populate('images');
    if (!coldStock) {
      coldStock = await this.coldStockModel.findOne({ outlet: outlet }, {}, { sort: { updatedAt: -1 } }).populate('images');
    }
    return this.transformColdStock(coldStock);
  }

  async updateColdStockByJourneyPlan({ planId, dto, salesRepId, i18n }: { planId: string; dto: UpdateColdStockDto; salesRepId: string; i18n: I18nContext }) {
    const plan = await this.checkPermission({ planId, salesRepId, i18n });

    if (dto.availableFridge && !dto.imageIds?.length) {
      throw new BadRequestException(i18n.translate('execution.cold_stock.min_photo'));
    }

    if (dto.imageIds?.length && dto.imageIds.length > 5) {
      throw new BadRequestException(i18n.translate('execution.cold_stock.max_photo'));
    }

    const payload: AnyKeys<ColdStockDocument> = {
      outlet: plan.outlet,
      saleRep: plan.saleRep,
      availableFridge: dto.availableFridge || false,
      isDedicatedFridge: dto.isDedicatedFridge,
      isProductsInDedicatedFridge: dto.isProductsInDedicatedFridge,
    };
    if (dto.imageIds) {
      payload.images = dto.imageIds;
    }
    const oldStock = await this.coldStockModel.findOne({ journeyPlan: plan._id }).populate('images');
    if (oldStock) {
      const listImages = oldStock.images.filter((image) => !dto.imageIds.includes(String(image._id)));
      // Remove old images
      this.filesService.deleteFiles(listImages).then().catch();
    }
    const coldStock = await this.coldStockModel
      .findOneAndUpdate(
        { journeyPlan: plan._id },
        {
          $set: payload,
        },
        { upsert: true, new: true },
      )
      .populate('images');

    if (dto.imageIds) {
      this.filesService.removeExpiredDate(dto.imageIds).then().catch();
    }

    this.planModel
      .updateOne(
        { _id: plan._id },
        {
          $set: {
            visitedDay: new Date(),
            visitedSteps: [...new Set([...(plan.visitedSteps || []), JourneyPlanVisitStep.COLD_STOCK])],
          },
        },
      )
      .then()
      .catch();

    return this.transformColdStock(coldStock);
  }

  async updateColdStockByJourneyPlanOffline({ plan, dto, i18n }: { plan: OutletJourneyPlanning; dto: UpdateColdStockDto; i18n: I18nContext }) {
    let success = false;
    let message = '';
    try {
      if (dto.availableFridge && !dto.imageIds?.length) {
        throw new BadRequestException(i18n.translate('execution.cold_stock.min_photo'));
      }
      if (dto.imageIds?.length && dto.imageIds.length > 5) {
        throw new BadRequestException(i18n.translate('execution.cold_stock.max_photo'));
      }
      const payload: AnyKeys<ColdStockDocument> = {
        outlet: plan.outlet,
        saleRep: plan.saleRep,
        availableFridge: dto.availableFridge || false,
        isDedicatedFridge: dto.isDedicatedFridge,
        isProductsInDedicatedFridge: dto.isProductsInDedicatedFridge,
      };
      if (dto.imageIds) {
        payload.images = dto.imageIds;
      }
      const oldStock = await this.coldStockModel.findOne({ journeyPlan: plan._id }).populate('images');
      if (oldStock) {
        const listImages = oldStock.images.filter((image) => !dto.imageIds.includes(String(image._id)));
        // Remove old images
        this.filesService.deleteFiles(listImages).then().catch();
      }
      await this.coldStockModel.findOneAndUpdate(
        { journeyPlan: plan._id },
        {
          $set: payload,
        },
        { upsert: true, new: true },
      );
      if (dto.imageIds) {
        this.filesService.removeExpiredDate(dto.imageIds).then().catch();
      }
      success = true;
    } catch (error) {
      message = error.message;
    }
    return { key: 'coldStock', success, message };
  }

  private async checkPermission({ planId, salesRepId, i18n }: { planId: string; salesRepId: string; i18n: I18nContext }) {
    const plan = await this.planModel.findById(planId);

    if (!plan) {
      throw new NotFoundException(i18n.translate('plan.not_found'));
    }
    if (String(plan.saleRep) !== salesRepId) {
      throw new ForbiddenException(i18n.translate('plan.unauthorized'));
    }

    return plan;
  }

  private transformColdStock(coldStock?: ColdStock) {
    return {
      planId: String(coldStock?.journeyPlan || ''),
      availableFridge: coldStock?.availableFridge ?? null,
      isDedicatedFridge: coldStock?.isDedicatedFridge ?? null,
      isProductsInDedicatedFridge: coldStock?.isProductsInDedicatedFridge ?? null,
      images: (coldStock?.images || []).map((image) => ({
        id: image._id,
        path: image.path,
      })),
      lastUpdatedAt: coldStock?.updatedAt,
    };
  }

  //=========================
  async getListColdStockByJpList(jpList: any) {
    if (isEmptyObjectOrArray(jpList)) {
      return jpList;
    }
    const planIds = jpList.map((jp) => jp._id);
    const coldStocks = await this.coldStockModel.find({ journeyPlan: { $in: planIds } }, {}, { sort: { updatedAt: -1 } }).populate('images');
    if (!coldStocks) {
      return null;
    }

    return jpList?.map((jp) => {
      const coldStock = coldStocks.find((c) => c.journeyPlan.toString() === jp._id.toString());
      return {
        ...jp,
        coldStock: this.transformColdStock(coldStock),
      };
    });
  }
}
