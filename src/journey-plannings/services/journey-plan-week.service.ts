import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';

import { BaseService } from '../../shared/services/base-service';
import { CycleNameType } from '../enums/cycle-type.enum';
import { WeekNameType } from '../enums/week-type.enum';
import { JourneyPlanWeek, JourneyPlanWeekDocument } from '../schemas/journey-plan-week.schema';
import { JourneyPlanCycleService } from './journey-plan-cycle.service';

@Injectable()
export class JourneyPlanWeekService extends BaseService<JourneyPlanWeek> {
  constructor(
    @InjectModel(JourneyPlanWeek.name)
    private readonly _journeyPlanWeekDocumentModel: Model<JourneyPlanWeekDocument>,
    private readonly _journeyPlanCycleService: JourneyPlanCycleService,
  ) {
    super();
    this.model = _journeyPlanWeekDocumentModel;
  }

  async getAllWeeks() {
    return this._journeyPlanWeekDocumentModel.find().populate('cycle').sort({ startTime: 1 });
  }

  async getWeekOfCycle(cycleId: string) {
    return this._journeyPlanWeekDocumentModel
      .find({ cycle: new Types.ObjectId(cycleId) })
      .populate('cycle')
      .sort({ startTime: 1 });
  }

  async getCurrentWeekOfGivenDay(day: string) {
    const isoDay = moment(day).toISOString();
    const currentCycleWeek = await this._journeyPlanWeekDocumentModel
      .find({ startTime: { $lte: isoDay } })
      .sort({ startTime: -1 })
      .limit(1)
      .exec();
    return currentCycleWeek[0];
  }

  async getWeeksOfCurrentCycle() {
    const currentTime = moment().toISOString();
    const currentCycleWeek = await this._journeyPlanWeekDocumentModel
      .find({ startTime: { $lte: currentTime } })
      .sort({ startTime: -1 })
      .limit(1);
    return this._journeyPlanWeekDocumentModel.find({ cycle: currentCycleWeek[0].cycle });
  }

  async getLastWeek(): Promise<JourneyPlanWeek> {
    return this._journeyPlanWeekDocumentModel.findOne().sort({ startTime: -1 }).populate('cycle').exec();
  }

  async getLastWeekGivenCycle(cycleId: string): Promise<JourneyPlanWeek> {
    return this._journeyPlanWeekDocumentModel
      .findOne({
        cycle: new Types.ObjectId(cycleId),
        weekName: WeekNameType.WEEK_4,
      })
      .exec();
  }

  async getWeeksOfGivenCycle(cycleId: string): Promise<JourneyPlanWeek[]> {
    return this._journeyPlanWeekDocumentModel
      .find({
        cycle: new Types.ObjectId(cycleId),
      })
      .exec();
  }

  async isLastDayOfCurrentCycle(day = moment().toISOString()): Promise<boolean> {
    const allWeeksOfCurrentCycle = await this.getWeeksOfCurrentCycle();
    const lastWeek = allWeeksOfCurrentCycle.find((w) => w.weekName === WeekNameType.WEEK_4);
    return moment(day).isSame(moment(lastWeek.startTime).add(6, 'days'), 'day');
  }

  async getTimeRangeOfGivenCycle(cycleId: string) {
    const allWeeks = await this.getWeeksOfGivenCycle(cycleId);
    if (allWeeks?.length > 0) {
      const firstWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_1);
      const lastWeek = allWeeks.find((w) => w.weekName === WeekNameType.WEEK_4);
      const startOfCycle = moment(firstWeek.startTime).toDate();
      const endOfCycle = moment(lastWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toDate();
      return { startOfCycle, endOfCycle, allWeeks };
    }
    return null;
  }

  async getLastWeekByGivenTime(startTime: string, endTime: string) {
    return this._journeyPlanWeekDocumentModel
      .findOne({
        weekName: WeekNameType.WEEK_4,
        startTime: { $gte: startTime, $lte: endTime },
      })
      .exec();
  }

  async getFirstWeekByGivenTime(startTime: string, endTime: string) {
    return this._journeyPlanWeekDocumentModel
      .findOne({
        weekName: WeekNameType.WEEK_1,
        startTime: { $gte: startTime, $lte: endTime },
      })
      .exec();
  }

  async getWeekByGivenTime(startTime: string, endTime: string) {
    return this._journeyPlanWeekDocumentModel
      .find({
        startTime: { $gte: startTime, $lte: endTime },
      })
      .populate('cycle')
      .sort({ startTime: 1 });
  }

  async getCycleByGivenDay(givenDay = new Date()) {
    const [currentCycleWeek] = await this._journeyPlanWeekDocumentModel
      .find({ startTime: { $lte: givenDay.toISOString() } })
      .sort({ startTime: -1 })
      .limit(1)
      .populate('cycle');
    return currentCycleWeek.cycle;
  }

  async getNextCycleByGivenDay(givenDay = new Date()) {
    const { cycleName, year } = await this.getCycleByGivenDay(givenDay);
    if (cycleName === CycleNameType.CYCLE_13) {
      return this._journeyPlanCycleService.findOne({
        cycleName: CycleNameType.CYCLE_1,
        year: year + 1,
      });
    }
    const matchedIndex = Object.values(CycleNameType).findIndex((value) => value === cycleName);
    return this._journeyPlanCycleService.findOne({
      cycleName: Object.values(CycleNameType)[matchedIndex + 1],
      year,
    });
  }

  async getPreviousCycleOfGivenCycle(cycleId: string) {
    const existedGivenCycle = await this._journeyPlanCycleService.findOne({ _id: new Types.ObjectId(cycleId) });
    if (existedGivenCycle) {
      const { cycleName, year } = existedGivenCycle;
      if (cycleName === CycleNameType.CYCLE_1) {
        return this._journeyPlanCycleService.findOne({
          cycleName: CycleNameType.CYCLE_13,
          year: year - 1,
        });
      }
      const matchedIndex = Object.values(CycleNameType).findIndex((value) => value === cycleName);
      return this._journeyPlanCycleService.findOne({
        cycleName: Object.values(CycleNameType)[matchedIndex - 1],
        year,
      });
    }
    return null;
  }

  async migrateData() {
    const data = await this.model.find({});
    for (const iterator of data) {
      const momentDate = moment(iterator.startTime).tz(process.env.TZ);
      const momentDate2 = moment(iterator.startTime).tz(process.env.TZ).set('day', 1).startOf('day');
      if (momentDate !== momentDate2) {
        iterator.startTime = momentDate2.toDate();
        await iterator.save();
      }
    }
  }
}
