import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from '../schemas/outlet-journey-planning.schema';
import { Model } from 'mongoose';
import { I18nContext } from 'nestjs-i18n';
import { UpdateVisitedStepDto } from '../dtos/update-visited-step.dto';
import * as moment from 'moment-timezone';
import { isEmptyObjectOrArray } from '../../utils';
import { OutletJourneyPlanningService } from './outlet-journey-planning.service';

@Injectable()
export class JourneyPlanVisitedStepsService {
  constructor(
    @InjectModel(OutletJourneyPlanning.name)
    private readonly planModel: Model<OutletJourneyPlanningDocument>,
    private readonly _outletJourneyPlanningService: OutletJourneyPlanningService,
  ) {}

  async getVisitedStep({ planId, salesRepId, i18n }: { planId: string; salesRepId: string; i18n: I18nContext }) {
    const plan = await this.checkPermission({ planId, salesRepId, i18n });

    return this.transformVisitedStep(plan);
  }

  async updateVisitedStep({ planId, dto, salesRepId, i18n }: { planId: string; dto: UpdateVisitedStepDto; salesRepId: string; i18n: I18nContext }) {
    let plan: OutletJourneyPlanning = await this.checkPermission({ planId, salesRepId, i18n });

    //Check Today Plan
    const startOfDay = moment().tz(process.env.TZ).startOf('day').toDate();
    const endOfDay = moment().tz(process.env.TZ).endOf('day').toDate();
    if (!isEmptyObjectOrArray(plan)) {
      const isTodayPlan =
        (!plan.rescheduled && plan.day >= startOfDay && plan.day <= endOfDay) || (plan.rescheduled && plan.rescheduledDay >= startOfDay && plan.rescheduledDay <= endOfDay);

      if (!isTodayPlan) {
        plan = await this._outletJourneyPlanningService.getTodayPlan(plan.saleRep, plan.outlet);
      }

      if (!plan) {
        return null;
      }
    }
    const newVisitedStep = [...new Set([...(plan.visitedSteps || []), ...(dto.steps || [])])];

    const updatedPlan = await this.planModel.findOneAndUpdate(
      {
        _id: plan._id,
      },
      {
        $set: {
          visitedSteps: newVisitedStep,
        },
      },
    );

    return this.transformVisitedStep(updatedPlan);
  }

  async updateVisitedStepOffline({ plan, dto }: { plan: OutletJourneyPlanning; dto: UpdateVisitedStepDto }) {
    let success = false;
    let message = '';
    try {
      const newVisitedStep = [...new Set([...(plan.visitedSteps || []), ...(dto.steps || [])])];
      const updatedPlan = await this.planModel.findOneAndUpdate(
        {
          _id: plan._id,
        },
        {
          $set: {
            visitedSteps: newVisitedStep,
          },
        },
      );
      success = true;
    } catch (error) {
      message = error.message;
    }
    return { key: 'visitedSteps', success, message };
  }

  private async checkPermission({ planId, salesRepId, i18n }: { planId: string; salesRepId: string; i18n: I18nContext }) {
    const plan = await this.planModel.findById(planId);
    if (!plan) {
      throw new NotFoundException(i18n.translate('plan.not_found'));
    }
    if (String(plan.saleRep) !== salesRepId) {
      throw new ForbiddenException(i18n.translate('plan.unauthorized'));
    }
    return plan;
  }

  private transformVisitedStep(plan: OutletJourneyPlanning) {
    return {
      planId: plan._id,
      visitedSteps: plan.visitedSteps || [],
    };
  }
}
