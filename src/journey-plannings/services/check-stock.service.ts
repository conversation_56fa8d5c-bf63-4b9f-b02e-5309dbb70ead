import { OmsService } from 'src/external/services/oms.service';
import { OutletJourneyPlanningService } from './outlet-journey-planning.service';
import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import { isEmptyObjectOrArray } from 'src/utils';
import { I18nContext } from 'nestjs-i18n';
import { GetCheckStock, SyncCheckStock } from '../dtos/check-stock.dto';
import { User } from 'src/users/schemas/user.schema';
import { getListCompetitors } from '../constants/competitor';
import { OutletJourneyPlanning } from '../schemas/outlet-journey-planning.schema';
import { OmsRepReportsCalculatorsService } from 'src/oms/services/rep-reports-calculator.service';
import { ConstantCommons } from '../../utils/constants';

@Injectable()
export class CheckStocksService {
  constructor(
    @Inject(forwardRef(() => OutletJourneyPlanningService))
    private outletJourneyPlanningService: OutletJourneyPlanningService,
    @Inject(forwardRef(() => OmsService))
    private omsService: OmsService,
    @Inject(forwardRef(() => OmsRepReportsCalculatorsService))
    private readonly omsRepReportsCalculatorsService: OmsRepReportsCalculatorsService,
  ) {}

  async getCheckStockData(query: GetCheckStock, i18n: I18nContext, currentUser: User) {
    const jp = await this.outletJourneyPlanningService.findOne({
      _id: new Types.ObjectId(query.journeyPlanId),
      outlet: new Types.ObjectId(query.outletId),
      saleRep: currentUser._id,
    });
    if (isEmptyObjectOrArray(jp)) {
      throw new HttpException(
        await i18n.translate(`common.not_found`, {
          args: { fieldName: 'outlet journeyPlanning' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
    const cached = await this.omsService.getCachedDataByOutlet({
      outletId: new Types.ObjectId(query.outletId),
      useDb: true,
      project: {
        products: 1,
      },
      outletExternalId: null,
    });
    let productCached = cached?.products || [];
    productCached = productCached.map((product: any) => {
      const hasCheckStock = jp.checkStock?.listProductsChecked?.find((checkStock) => checkStock.sku == product.sku);
      return { ...product, check_stock_quantity: hasCheckStock?.check_stock_quantity || 0 };
    }) as any;
    const mustHaveSKUs = productCached.filter((p) => p.tagging == ConstantCommons.MUST_HAVE_SKU_LABEL);
    const listProducts = productCached.filter((p) => p.tagging != ConstantCommons.MUST_HAVE_SKU_LABEL);
    //fixed list Competitors
    const listCompetitors = getListCompetitors().map((product: any) => {
      const hasCheckStock = jp.checkStock?.listCompetitorsChecked?.find((checkStock) => checkStock.sku == product.sku);
      return { ...product, check_stock_quantity: hasCheckStock?.check_stock_quantity || 0 };
    });

    return { mustHaveSKUs, listProducts, listCompetitors, lastUpdate: jp.checkStock?.lastUpdate || jp.createdAt };
  }

  async getCheckStockDataV3(query: GetCheckStock, i18n: I18nContext, currentUser: User) {
    const jp = await this.outletJourneyPlanningService.findOne({
      _id: new Types.ObjectId(query.journeyPlanId),
      outlet: new Types.ObjectId(query.outletId),
      saleRep: currentUser._id,
    });
    if (isEmptyObjectOrArray(jp)) {
      throw new HttpException(
        await i18n.translate(`common.not_found`, {
          args: { fieldName: 'outlet journeyPlanning' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
    const cached = await this.omsService.getCachedDataByOutlet({
      outletId: new Types.ObjectId(query.outletId),
      useDb: true,
      project: {
        products: 1,
      },
    });
    let productCached = cached?.products || [];
    productCached = productCached.map((product: any) => {
      const hasCheckStock = jp.checkStock?.listProductsChecked?.find((checkStock) => checkStock.sku == product.sku);
      return { ...product, check_stock_quantity: hasCheckStock?.check_stock_quantity || 0, selling_price: hasCheckStock?.selling_price || 0 };
    }) as any;

    //fixed list Competitors
    const listCompetitors = getListCompetitors().map((product: any) => {
      const hasCheckStock = jp.checkStock?.listCompetitorsChecked?.find((checkStock) => checkStock.sku == product.sku);
      return { ...product, check_stock_quantity: hasCheckStock?.check_stock_quantity || 0, selling_price: hasCheckStock?.selling_price || 0 };
    });

    return { listProducts: productCached, listCompetitors, lastUpdate: jp.checkStock?.lastUpdate || jp.createdAt };
  }

  async syncCheckStockData(body: SyncCheckStock, i18n: I18nContext, currentUser: User) {
    const jp = await this.outletJourneyPlanningService.findOne({
      _id: new Types.ObjectId(body.journeyPlanId),
      outlet: new Types.ObjectId(body.outletId),
      saleRep: currentUser._id,
    });
    if (isEmptyObjectOrArray(jp)) {
      throw new HttpException(
        await i18n.translate(`common.not_found`, {
          args: { fieldName: 'outlet journeyPlanning' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
    await this.outletJourneyPlanningService.update(jp._id, {
      checkStock: {
        lastUpdate: new Date(),
        listProductsChecked: body.listProductsChecked,
        listCompetitorsChecked: body.listCompetitorsChecked,
      },
      visitedDay: new Date(),
    });
    this.omsRepReportsCalculatorsService.executeDSRStatistic({ salesRepId: currentUser.saleRepId }).then().catch();
  }

  async syncCheckStockDataOffline(jp: OutletJourneyPlanning, body: SyncCheckStock, i18n: I18nContext, currentUser: User) {
    let success = false;
    let message = '';
    try {
      await this.outletJourneyPlanningService.update(jp._id, {
        checkStock: {
          lastUpdate: new Date(),
          listProductsChecked: body.listProductsChecked,
          listCompetitorsChecked: body.listCompetitorsChecked,
        },
        visitedDay: new Date(),
      });
      success = true;
    } catch (error) {
      message = error.message;
    }
    return { key: 'checkStock', success, message };
  }

  //=================================
  async getListAvailabilityDataByJpList(jpList: any) {
    if (isEmptyObjectOrArray(jpList)) {
      return jpList;
    }
    const cachedData = await this.omsService.getFullCachedDataByOutlets({
      outletIds: jpList.map((jp) => new Types.ObjectId(jp.outlet)),
      project: {
        products: 1,
        outlet: 1,
      },
    });

    return jpList?.map((jp) => {
      const { checkStock, ...jpWithoutCheckStock } = jp;
      const cached = cachedData.find((c) => c.outlet.toString() === jp.outlet.toString());
      let hasDataCount = 0;
      let productCached = cached?.products || [];
      productCached = productCached
        .map((product: any) => {
          const hasCheckStock = jp.checkStock?.listProductsChecked?.find((checkStock) => checkStock.sku == product.sku);
          let hasDataThisProduct = false;
          if (hasCheckStock?.check_stock_quantity > 0 || hasCheckStock?.selling_price > 0) {
            hasDataCount++;
            hasDataThisProduct = true;
          }
          return { ...product, check_stock_quantity: hasCheckStock?.check_stock_quantity || 0, selling_price: hasCheckStock?.selling_price || 0, hasData: hasDataThisProduct };
        })
        .filter(Boolean) as any;

      //fixed list Competitors
      const listCompetitors = getListCompetitors()
        .map((product: any) => {
          let hasDataThisProduct = false;
          const hasCheckStock = jp.checkStock?.listCompetitorsChecked?.find((checkStock) => checkStock.sku == product.sku);
          if (hasCheckStock?.check_stock_quantity > 0 || hasCheckStock?.selling_price > 0) {
            hasDataThisProduct = true;
          }
          return { ...product, check_stock_quantity: hasCheckStock?.check_stock_quantity || 0, selling_price: hasCheckStock?.selling_price || 0, hasData: hasDataThisProduct };
        })
        .filter(Boolean);

      return {
        ...jpWithoutCheckStock,
        availability: {
          listProducts: productCached,
          listCompetitors,
          lastUpdate: jp.checkStock?.lastUpdate || jp.createdAt,
          ratio: `${hasDataCount}/${productCached?.length}`,
          hasData: hasDataCount > 0,
        },
      };
    });
  }
}
