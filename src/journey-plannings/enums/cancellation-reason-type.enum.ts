export enum CancellationReasonType {
  PUBLIC_HOLIDAY = 'plan.missed_visit_reason.public_holiday',
  ANNUAL_MEDICAL_LEAVE = 'plan.missed_visit_reason.annual_medical_leave',
  MEETING_TRAINING_SPECIAL_EVENT = 'plan.missed_visit_reason.meeting_training_special_event',
  TASKS_ASSIGNED_BY_HMB = 'plan.missed_visit_reason.tasks_assigned_by_hmb',
  VEHICLE_BREAKDOWN = 'plan.missed_visit_reason.vehicle_breakdown',
  NATURAL_DISASTER = 'plan.missed_visit_reason.natural_disaster',
  OUTLET_SHUTDOWN = 'outlet.missed_visit_reason.temporarily_closed_or_renovation',
}

export enum OutletAbsenceReason {
  TEMPORARILY_CLOSED_OR_RENOVATION = 'outlet.missed_visit_reason.temporarily_closed_or_renovation',
  PERMANENTLY_CLOSED = 'outlet.missed_visit_reason.permanently_closed',
  NOT_OPEN_TODAY = 'outlet.missed_visit_reason.not_open_today',
  OTHERS = 'outlet.missed_visit_reason.others',
}

export enum OutletAbsenceReasonWarningMessage {
  TEMPORARILY_CLOSED_OR_RENOVATION = 'outlet.missed_visit_reason.temporarily_closed_or_renovation_warning',
  PERMANENTLY_CLOSED = 'outlet.missed_visit_reason.permanently_closed_warning',
  NOT_OPEN_TODAY = 'outlet.missed_visit_reason.not_open_today_warning',
  OTHERS = 'outlet.missed_visit_reason.others_warning',
}
