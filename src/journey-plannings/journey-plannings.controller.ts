import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  UnauthorizedException,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiConsumes, ApiExcludeEndpoint, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import * as _ from 'lodash';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { I18n, I18nContext } from 'nestjs-i18n';

import { Distributor } from 'src/distributor/schemas';
import { PaginationDto } from 'src/shared/dtos/pagination.dto';
import { isEmptyObjectOrArray } from 'src/utils';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { Days, Weeks } from '../distributor/enums';
import { BaseJourneyPlanService, BaseJourneyPlanSettingService, DistributorService, DistributorUserRelationService } from '../distributor/services';
import { FilesService } from '../files/services';
import { OrderParams } from '../orders/dtos/order-filter.dto';
import { OutletStatus } from '../outlets/enums/outlet-status.enum';
import { OutletsService } from '../outlets/services/outlets.service';
import { SaleRepOutletRelationService } from '../outlets/services/sale-rep-outlet-relation.service';
import { SaleRepExecutionAvailabilityService, SaleRepStatisticService } from '../sale-rep/services';
import { ApiException } from '../shared/api-exception.model';
import { PaginationParams } from '../shared/common-params/pagination.params';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { Roles } from '../shared/decorator/roles.decorator';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Serialize } from '../shared/interceptor/serialize.interceptor';
import { ApiResponse } from '../shared/response/api-response';
import { SalesRepStatus } from '../users/enums';
import { User } from '../users/schemas/user.schema';
import { UsersService } from '../users/services/users.service';
import { formatCurrentDay, isEditJourneyPlanning, printLog, toListResponse, validateFields } from '../utils';
import { ConstantRoles } from '../utils/constants/role';
import { CreateCycleDto, ListJourneyPlanningDto } from './dtos/create-cycle.dto';
import { CreateJourneyPlanDto } from './dtos/create-journey-plan.dto';
import { CreateWeekDto } from './dtos/create-week.dto';
import { ExportJourneyPlan, FetchJourneyPlanDto } from './dtos/fetch-journey-plan.dto';
import { JourneyPlanDto } from './dtos/journey-plan.dto';
import { CountAffectedOutletsDto, MissedReasonForOutletDto, MissedReasonForOutletsDto, MissedReasonForVisitsDto, MissedVisitReasonDto } from './dtos/missed-visit-reason.dto';
import { ReschedulePlanDto } from './dtos/reschedule-plan.dto';
import { UpdateJourneyPlanning } from './dtos/update-journey-plan.dto';
import { CancellationReasonType } from './enums/cancellation-reason-type.enum';
import { JourneyPlanType } from './enums/journey-plan-type.enum';
import { VisitStatus } from './enums/visit-status.enum';
import { WeekNameType } from './enums/week-type.enum';
import { JourneyPlanCycleService } from './services/journey-plan-cycle.service';
import { JourneyPlanWeekService } from './services/journey-plan-week.service';
import { OutletJourneyPlanningService } from './services/outlet-journey-planning.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { OpCos } from 'src/config';

@ApiTags('Journey Planning')
@ApiHeader({ name: 'locale', description: 'en' })
@Controller('api/journey-plannings')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class JourneyPlanningsController {
  constructor(
    private readonly _journeyPlanCycleService: JourneyPlanCycleService,
    private readonly _journeyPlanWeekService: JourneyPlanWeekService,
    private readonly _outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly _outletsService: OutletsService,
    private readonly _saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly _saleRepExecutionAvailabilityService: SaleRepExecutionAvailabilityService,
    private readonly _filesService: FilesService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _userService: UsersService,
    private readonly _baseJourneyPlanSettingService: BaseJourneyPlanSettingService,
    private readonly _distributorService: DistributorService,
    private readonly _baseJourneyPlanService: BaseJourneyPlanService,
    private readonly _saleRepStatisticService: SaleRepStatisticService,
  ) {}

  @Post('cycles')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  async createCycle(@Body() createCycleDto: CreateCycleDto) {
    const { cycleName, year } = createCycleDto;
    const existedCycle = await this._journeyPlanCycleService.findOne({ cycleName, year });
    if (existedCycle) {
      throw new BadRequestException('CYCLE_EXISTED');
    }
    return this._journeyPlanCycleService.create(createCycleDto);
  }

  @Post('correct-time-cycles')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  async correctTimeCycles() {
    await this._journeyPlanWeekService.migrateData();
    return new ApiResponse();
  }
  // @Get('cycles/:cycleId')
  // @ApiBadRequestResponse({ type: ApiException })
  // async getCycle(@Param('cycleId') cycleId: string, @I18n() i18n: I18nContext) {
  //   return this._journeyPlanCycleService.findOne({ _id: cycleId });
  // }

  @Get('weeks')
  @ApiBadRequestResponse({ type: ApiException })
  async getAllWeeks() {
    const allWeeks = await this._journeyPlanWeekService.getAllWeeks();
    const result = allWeeks.map((w) => {
      const { id, weekName, startTime, cycle } = w;
      return {
        id,
        weekName,
        startTime,
        cycleName: cycle.cycleName,
        cycleYear: cycle.year,
      };
    });
    return new ApiResponse(_.groupBy(result, 'cycleName'));
  }

  @Post('weeks')
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  async createWeek(@Body() createWeekDto: CreateWeekDto) {
    const { weekName, cycleId, startTime } = createWeekDto;
    const existedCycle = await this._journeyPlanCycleService.findOne({ _id: new Types.ObjectId(cycleId) });

    if (!existedCycle) {
      throw new BadRequestException('NOT_FOUND_CYCLE');
    }
    const existedWeek = await this._journeyPlanWeekService.findOne({
      cycle: new Types.ObjectId(cycleId),
      weekName,
    });
    if (existedWeek) {
      throw new BadRequestException('WEEK_EXISTED');
    }
    const startTimeYear = new Date(startTime).getFullYear();

    if (startTimeYear === existedCycle.year || startTimeYear === existedCycle.year + 1) {
      return new ApiResponse(
        await this._journeyPlanWeekService.create({
          weekName,
          startTime,
          cycle: existedCycle,
        }),
      );
    } else {
      throw new BadRequestException('INVALID_DAY');
    }
  }

  @Get('sale-rep/:saleRepId/today')
  @Roles(ConstantRoles.SALE_REP)
  @Serialize(JourneyPlanDto)
  @ApiBadRequestResponse({ type: ApiException })
  async getTodayJourneyPlans(@Param('saleRepId') saleRepId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    if (currentUser._id.toString() !== saleRepId) {
      throw new UnauthorizedException(await i18n.t('plan.unauthorized'));
    }
    if (process.env.OPCO == OpCos.Cambodia) {
      const result = await this._outletJourneyPlanningService.getJourneyPlansKH(saleRepId, JourneyPlanType.TODAY, '', currentUser);
      return new ApiResponse(result);
    } else {
      const result = await this._outletJourneyPlanningService.getJourneyPlans(saleRepId, JourneyPlanType.TODAY, '');
      return new ApiResponse(result);
    }
  }

  @Get('sale-rep/:saleRepId/upcoming')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @Serialize(JourneyPlanDto)
  async getUpcomingJourneyPlans(@Param('saleRepId') saleRepId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    if (currentUser._id.toString() !== saleRepId) {
      throw new UnauthorizedException(await i18n.t('plan.unauthorized'));
    }
    const upcomingPlans = await this._outletJourneyPlanningService.getJourneyPlans(saleRepId, JourneyPlanType.UPCOMING);
    // const groupByDay = _.groupBy(upcomingPlans, (item) => {
    //   return moment(item.day).format('DD/MM/YYYY');
    // });
    // const dayRange = {};
    // const currentDay = moment();
    // for (let i = 1; i <= 14; i++) {
    //   const day = currentDay.clone().add(i, 'days');
    //   if (moment(day).weekday() > 0) {
    //     const keyDay = day.format('DD/MM/YYYY');
    //     dayRange[keyDay] = groupByDay[keyDay] ?? [];
    //   }
    // }
    return new ApiResponse(upcomingPlans);
  }

  @Get('sale-rep/:saleRepId/:outletId/upcoming-in-current-week')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @Serialize(JourneyPlanDto)
  async getCurrentWeekJourneyPlans(@Param('saleRepId') saleRepId: string, @Param('outletId') outletId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    if (currentUser._id.toString() !== saleRepId) {
      throw new UnauthorizedException(await i18n.t('plan.unauthorized'));
    }
    const saleRepOutletAssociation = await this._saleRepOutletRelationService.findByOutletAndSaleRep(outletId, saleRepId);
    if (!saleRepOutletAssociation) {
      throw new BadRequestException('NOT_FOUND_ASSOCIATION');
    }
    const upcomingPlans = await this._outletJourneyPlanningService.getJourneyPlans(saleRepId, JourneyPlanType.UPCOMINGCURRENTWEEK, outletId);

    return new ApiResponse(upcomingPlans);
  }

  // @Patch(':journeyPlanId/visited')
  // @ApiBadRequestResponse({ type: ApiException })
  // @ApiExcludeEndpoint()
  // @ApiOperation({
  //   summary: 'Set visited for given journey plan',
  //   description: 'Set visited for given journey plan',
  // })
  // async setVisitedJourneyPlan(@Param('journeyPlanId') journeyPlanId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
  //   const journeyPlan = await this._outletJourneyPlanningService.findById(journeyPlanId);
  //   if (!journeyPlan) {
  //     throw new BadRequestException(await i18n.t('plan.not_found'));
  //   }
  //   if (journeyPlan.saleRep.toString() !== currentUser._id.toString()) {
  //     throw new UnauthorizedException(await i18n.t('plan.unauthorized'));
  //   }
  //   const result = await this._outletJourneyPlanningService.setVisitedJourneyPlan(journeyPlan);
  //   return new ApiResponse(result);
  // }

  @Put(':journeyPlanId/reschedule')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Reschedule a plan',
    description: 'Reschedule a plan',
  })
  @Serialize(JourneyPlanDto)
  async reschedulePlan(@Param('journeyPlanId') journeyPlanId: string, @Body() reschedulePlanDto: ReschedulePlanDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    const rescheduledDay = new Date(reschedulePlanDto.day);
    const journeyPlan = await this._outletJourneyPlanningService.findUnVisitedPlan(journeyPlanId);
    if (!journeyPlan) {
      throw new BadRequestException(await i18n.t('plan.not_found'));
    }
    if (journeyPlan.saleRep.toString() !== currentUser._id.toString()) {
      throw new UnauthorizedException(await i18n.t('plan.unauthorized'));
    }
    if (process.env.OPCO == OpCos.Cambodia && journeyPlan.missedReason && !journeyPlan.missedReason.reschedulable) {
      throw new BadRequestException(await i18n.t('plan.cannot_reschedule_uncontrollable_missed_visit'));
    }
    if (journeyPlan.cancel) {
      throw new BadRequestException(await i18n.t('plan.cannot_reschedule'));
    }
    const rescheduleWeek = await this._journeyPlanWeekService.getCurrentWeekOfGivenDay(rescheduledDay.toISOString());
    if (!rescheduleWeek) {
      throw new BadRequestException(await i18n.t('plan.week.not_found'));
    }
    if (rescheduleWeek._id.toString() !== journeyPlan.week._id.toString() || +new Date() - +rescheduledDay > 0) {
      throw new BadRequestException(await i18n.t('plan.invalid_schedule_day'));
    }
    const existedTimeSlot = await this._outletJourneyPlanningService.getPlanBySaleRepAndDay(journeyPlan.saleRep, rescheduledDay);
    if (existedTimeSlot) {
      throw new BadRequestException(await i18n.t('plan.existed_time_slot'));
    }
    const timestampStartOfDay = +moment().tz(process.env.TZ).startOf('day').toDate();
    const timestampEndOfDay = +moment().tz(process.env.TZ).endOf('day').toDate();
    const timestampDisplayDay = +new Date(journeyPlan.displayDay);
    if (journeyPlan.visitStatus === VisitStatus.IN_PROGRESS && timestampStartOfDay <= timestampDisplayDay && timestampDisplayDay <= timestampEndOfDay) {
      throw new BadRequestException(await i18n.t('plan.cannot_reschedule_in_progress_plan'));
    }
    const result = await this._outletJourneyPlanningService.update(
      journeyPlanId,
      {
        week: rescheduleWeek._id,
        rescheduledDay,
        rescheduled: true,
        visitedDay: null,
        visitStatus: VisitStatus.START_VISIT,
        $unset: {
          missedReason: 1,
          outletReport: 1,
        },
      },
      'outlet',
    );
    await this._saleRepExecutionAvailabilityService.deleteAvailabilityBySaleRepIdAndJourneyPlanId(currentUser._id.toString(), journeyPlanId);
    return new ApiResponse(result);
  }

  @Put(':journeyPlanId/missed-visit-reason')
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Set missed visit reason',
    description: 'Set missed visit reason',
  })
  @Serialize(JourneyPlanDto)
  async setMissedVisitReason(
    @Param('journeyPlanId') journeyPlanId: string,
    @Body() missedVisitReasonDto: MissedVisitReasonDto,
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser: any,
  ) {
    const journeyPlan = await this._outletJourneyPlanningService.findUnVisitedPlan(journeyPlanId);
    const cancellationReason = missedVisitReasonDto.reasonKey?.trim();

    if (currentUser.roles.some((r) => r === ConstantRoles.SALE_REP)) {
      if (!Object.values(CancellationReasonType).some((reason) => reason === cancellationReason)) {
        throw new BadRequestException('plan.not_found_reason');
      }
      if (journeyPlan.saleRep.toString() !== currentUser._id.toString()) {
        throw new UnauthorizedException('plan.unauthorized');
      }
      if (journeyPlan.cancel) {
        throw new BadRequestException('plan.cannot_set_reason_cancel_plan');
      }
      const canSetReason = this._outletJourneyPlanningService.isAbleToSetMissedVisitReason(journeyPlan.displayDay);
      if (!canSetReason) {
        throw new BadRequestException('plan.cannot_set_reason');
      }
    }
    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const disSalesReps = await this._distributorUserRelationService.findByUserId(journeyPlan.saleRep._id.toString());

      const disUserAdmins = await this._distributorUserRelationService.findManyByUserAdminId(currentUser._id.toString());

      if (!disUserAdmins?.map((item) => item.distributor._id.toString()).includes(disSalesReps?.distributor._id.toString())) {
        throw new UnauthorizedException('plan.unauthorized');
      }
      if (!this._outletJourneyPlanningService.isSameCurrentMonth(journeyPlan.displayDay)) {
        throw new BadRequestException('plan.cannot_set_reason');
      }
    }
    let result;
    if (!cancellationReason || cancellationReason === '-') {
      result = await this._outletJourneyPlanningService.update(
        journeyPlanId,
        {
          cancellationReason: null,
          cancel: false,
        },
        'outlet',
      );
      result.cancellationReason = null;
    } else {
      result = await this._outletJourneyPlanningService.update(
        journeyPlanId,
        {
          cancellationReason,
          cancel: true,
        },
        'outlet',
      );
      result.cancellationReason = await i18n.t(cancellationReason);
    }
    return new ApiResponse(result);
  }

  @Put('missed-reason-visits')
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Set missed reason for multiple visits',
    description: 'Set missed reason for multiple visits',
  })
  async setMissedReasonForVisits(@Body() dto: MissedReasonForVisitsDto, @CurrentUser() currentUser: User & { roles: string[] }, @I18n() i18n: I18nContext) {
    return this._outletJourneyPlanningService.setMissedReasonForVisits({
      dto,
      currentUser,
      i18n,
    });
  }

  @Put('missed-reason-outlet')
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Set missed reason for outlet',
    description: 'Set missed reason for outlet',
  })
  async setMissedReasonForOutlet(@Body() dto: MissedReasonForOutletDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User & { roles: string[] }) {
    if (process.env.OPCO == OpCos.Cambodia) {
      const res = await this._outletJourneyPlanningService.setMissedReasonForOutletKH(dto, i18n, currentUser);
      return new ApiResponse(res);
    } else {
      const res = await this._outletJourneyPlanningService.setMissedReasonForOutlet(dto, i18n);
      return new ApiResponse(res);
    }
  }

  @Put('unset-missed-reason-outlet')
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Set missed reason for outlet',
    description: 'Set missed reason for outlet',
  })
  async unsetMissedReasonForOutlet(@Body() dto: MissedReasonForOutletDto, @I18n() i18n: I18nContext) {
    const res = await this._outletJourneyPlanningService.unsetMissedReasonForOutlet(dto, i18n);
    return new ApiResponse(res);
  }

  @Put('missed-reason-outlets')
  @Roles(ConstantRoles.SALE_REP, ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Set missed reason for multiple outlets',
    description: 'Set missed reason for multiple outlets',
  })
  async setMissedReasonForOutlets(@Body() dto: MissedReasonForOutletsDto, @CurrentUser() currentUser: User) {
    const res = await this._outletJourneyPlanningService.setMissedReasonForOutlets(dto, currentUser);
    return new ApiResponse(res);
  }

  @Post('count-affected-outlets')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Count affected outlets when set missed reason',
    description: 'Count affected outlets when set missed reason',
  })
  async countAffectedOutlets(@Body() dto: CountAffectedOutletsDto, @CurrentUser() currentUser: User) {
    const numberOfAffectedOutlets = await this._outletJourneyPlanningService.countAffectedOutlets(dto, currentUser);
    return new ApiResponse({ numberOfAffectedOutlets });
  }

  @Get('list-absences')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Get list absences',
    description: 'Get list absences',
  })
  async getListAbsences(@Query() queries: PaginationDto, @CurrentUser() currentUser: User, @I18n() i18n: I18nContext) {
    if (process.env.OPCO == OpCos.Cambodia) {
      return this._outletJourneyPlanningService.getListAbsencesKH(queries, currentUser, i18n);
    } else {
      return this._outletJourneyPlanningService.getListAbsences(queries, currentUser, i18n);
    }
  }

  @Get('options-cycle')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  async optionsCycle() {
    try {
      //get all cycle and get currently cycle
      const allCycleWeek = await this._journeyPlanWeekService.getAllWeeks();
      const getCurrentCycle = await this._journeyPlanWeekService.getWeeksOfCurrentCycle();

      /*
    - Cook data follow by data set like: [Cycle X - yyyy (dd/mm/yyyy - dd/mm/yyyy)]

     */
      const dataCycle = allCycleWeek.reduce(
        (preData, currentWeek, currentIndex) => {
          //push item cycle data in preData if cycleId is difference
          if (preData[preData.length - 1].cycleId !== currentWeek.cycle._id) {
            const endOfCycleTime = moment(preData[preData.length - 1].endTime)
              .add(6, 'days')
              .tz(process.env.TZ)
              .endOf('day')
              .toISOString();
            preData[preData.length - 1].endTime = new Date(endOfCycleTime);
            preData.push({
              cycleName: currentWeek.cycle.cycleName,
              year: currentWeek.cycle.year,
              cycleId: currentWeek.cycle._id,
              startTime: currentWeek.startTime,
              weekName: currentWeek.weekName,
              endTime: currentWeek.startTime,
            });
            return preData;
          }
          // process if cycleId is the same previous cycleId
          else {
            preData[preData.length - 1] = {
              ...preData[preData.length - 1],
              cycleName: currentWeek.cycle.cycleName,
              year: currentWeek.cycle.year,
              cycleId: currentWeek.cycle._id,
              weekName: currentWeek.weekName,
              endTime: currentWeek.startTime,
            };
          }
          if (currentIndex === allCycleWeek.length - 1) {
            const endOfCycleTime = moment(preData[preData.length - 1].endTime)
              .add(6, 'days')
              .tz(process.env.TZ)
              .endOf('day')
              .toISOString();
            preData[preData.length - 1].endTime = new Date(endOfCycleTime);
          }
          return preData;
        },
        [
          {
            cycleName: allCycleWeek[0].cycle.cycleName,
            year: allCycleWeek[0].cycle.year,
            cycleId: allCycleWeek[0].cycle._id,
            startTime: allCycleWeek[0].startTime,
            weekName: allCycleWeek[0].weekName,
            endTime: allCycleWeek[0].startTime,
          },
        ],
      );

      const indexCurrentCycle = dataCycle.findIndex((el) => el?.cycleId.toString() === getCurrentCycle[0]?.cycle?.toString());
      const sliceData = dataCycle.slice(indexCurrentCycle - 3, indexCurrentCycle + 4);

      const result = sliceData.map((el) => {
        return {
          cycleName: el.cycleName,
          cycleId: el.cycleId,
          year: el.year,
          startTime: el.startTime,
          endTime: el.endTime,
        };
      });

      return new ApiResponse(result);
    } catch (error) {
      printLog('error', error);
    }
  }

  @Get('list')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiExcludeEndpoint()
  @ApiBadRequestResponse({ type: ApiException })
  async listJourneyPlaning(
    @Query() { offset, limit }: PaginationParams,
    @Query() { orderBy, orderDesc }: OrderParams,
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser,
    @Query() param: ListJourneyPlanningDto,
  ) {
    const { distributorId, cycleId } = param;

    if (!distributorId) {
      throw new HttpException(
        await i18n.translate(`common.required_field`, {
          args: { fieldName: 'distributorId' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!cycleId) {
      throw new HttpException(
        await i18n.translate(`common.required_field`, {
          args: { fieldName: 'cycleId' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }
    /* find week of cycle */

    const existedCycle = await this._journeyPlanCycleService.findOne({ _id: new Types.ObjectId(cycleId) });

    if (!existedCycle) {
      throw new BadRequestException('NOT_FOUND_CYCLE');
    }
    const existedWeek = await this._journeyPlanWeekService.getWeekOfCycle(cycleId);

    const listIdWeek = existedWeek.map((el) => el._id);

    /* - get list sales rep */

    const saleReps = await this._distributorUserRelationService.getDistributorUserRelation({
      'dis.distributorId': distributorId?.trim(),
      'ua._id': null,
    });

    const listSaleRepId = saleReps.map((el) => el.u?._id);
    const resultGetOutletJP = await this._outletJourneyPlanningService.getOutletJourneyPlanningSaleRep(listSaleRepId, listIdWeek, +limit, +offset || 0, orderBy, orderDesc);
    const [{ totalRecords, data }] = resultGetOutletJP;

    const newData = data
      .filter((el) => el?.displayDay)
      .map((el) => {
        return {
          ...el,
          weekName: Object.values(WeekNameType).findIndex((item) => item === el.weekName) + 1,
          displayDay: formatCurrentDay(el.displayDay, el.startTimeWeek),
          isEdited: isEditJourneyPlanning(el.displayDay, el.visitStatus, el.startTimeWeek, el.isEdited),
        };
      });

    return new ApiResponse(toListResponse([newData, totalRecords?.[0]?.total ?? 0]));
  }

  @Post('list')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get List Journey Plans',
  })
  async getListJourneyPlans(@Body() fetchJourneyPlanDto: FetchJourneyPlanDto, @CurrentUser() currentUser: any) {
    const { distributorId, cycleId, sort, skip, limit } = fetchJourneyPlanDto;
    let validDistributorId: string;
    const existedCycle = await this._journeyPlanCycleService.findByIdAndGetWeeks(cycleId);

    if (!existedCycle) {
      throw new BadRequestException('plan.not_found_cycle');
    }

    const weeks = existedCycle.weeks;

    if (!existedCycle.weeks.length) {
      throw new BadRequestException('plan.not_found_week');
    }

    const isDistributorAdmin = currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN);
    let distributor: Distributor;
    if (isDistributorAdmin) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId).distributor;
      if (!distributorAdmins || !distributorAdmins.length || !distributor) {
        throw new BadRequestException('plan.not_found_distributor');
      }
      validDistributorId = distributor.distributorId;
    } else {
      distributor = await this._distributorService.findOne({ distributorId });
      if (!distributor) {
        throw new BadRequestException('plan.not_found_distributor');
      }
      validDistributorId = distributor.distributorId;
    }
    const distributorSalesReps = await this._distributorUserRelationService.getAllSalesRepOfGivenDistributor(validDistributorId);
    const salesRepIds = distributorSalesReps.map((ds) => ds._id.toString());
    const soRelations = await this._saleRepOutletRelationService.getActiveSORByListSalesRepsIds(salesRepIds);
    const allJourneyPlans = await this._outletJourneyPlanningService.getJourneyPlansByConnectedSORAndCycleId({
      soRelations,
      weeks,
      sort,
      skip: parseInt(String(skip)),
      limit: parseInt(String(limit)),
    });
    allJourneyPlans.data = allJourneyPlans.data.map((e) => ({ ...e, distributorId: distributor.distributorId, distributorName: distributor.distributorName }));
    return new ApiResponse(allJourneyPlans);
  }

  @Post('export')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Export journey plan',
  })
  async exportJourneyPlan(@Body() exportJourneyPlanDto: ExportJourneyPlan, @CurrentUser() currentUser: any, @I18n() i18n: I18nContext) {
    const { distributorId, cycleId, sort } = exportJourneyPlanDto;
    let validDistributorId = null;
    let validDistributorName = null;
    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (distributor) {
        validDistributorId = distributor.distributor.distributorId;
        validDistributorName = distributor.distributor.distributorName;
      }
    } else {
      const distributor = await this._distributorService.findOne({ distributorId });
      if (!distributor) {
        throw new BadRequestException('plan.not_found_distributor');
      }
      validDistributorId = distributor.distributorId;
      validDistributorName = distributor.distributorName;
    }
    const existedCycle = await this._journeyPlanCycleService.findByIdAndGetWeeks(cycleId);

    if (!existedCycle) {
      throw new BadRequestException('plan.not_found_cycle');
    }

    const weeks = existedCycle.weeks;
    const distributorSalesReps = await this._distributorUserRelationService.getAllSalesRepOfGivenDistributor(validDistributorId);
    const salesRepIds = distributorSalesReps.map((ds) => ds._id.toString());
    const soRelations = await this._saleRepOutletRelationService.getActiveSORByListSalesRepsIds(salesRepIds);
    const allJourneyPlans = await this._outletJourneyPlanningService.getJourneyPlansByConnectedSORAndCycleId({
      soRelations,
      weeks,
      sort,
      skip: 0,
      limit: 9999,
    });
    let xlsxData: any = [
      {
        [i18n.translate(`importExport.ucc`)]: '',
        [i18n.translate(`importExport.outletName`)]: '',
        [i18n.translate(`importExport.outletClass`)]: '',
        [i18n.translate(`importExport.area`)]: '',
        [i18n.translate(`importExport.salesRepId`)]: '',
        [i18n.translate(`importExport.week`)]: '',
        [i18n.translate(`importExport.day`)]: '',
        [i18n.translate(`importExport.distributorId`)]: '',
        [i18n.translate(`importExport.distributorName`)]: '',
      },
    ];
    if (allJourneyPlans?.data?.length > 0) {
      xlsxData = allJourneyPlans.data.map((item) => {
        return {
          [i18n.translate(`importExport.ucc`)]: item.ucc,
          [i18n.translate(`importExport.outletName`)]: item.outletName,
          [i18n.translate(`importExport.outletClass`)]: item.outletClass,
          [i18n.translate(`importExport.area`)]: item.area,
          [i18n.translate(`importExport.salesRepId`)]: item.saleRepId,
          [i18n.translate(`importExport.week`)]: item.cycleWeek,
          [i18n.translate(`importExport.day`)]: item.weekDay,
          [i18n.translate(`importExport.distributorId`)]: validDistributorId,
          [i18n.translate(`importExport.distributorName`)]: validDistributorName,
        };
      });
    }
    const { year, cycleName } = existedCycle;
    const cycle = cycleName?.replace(' ', '') + `-${year}`;
    const distributorName = validDistributorName?.replace(' ', '');
    const fileName = `JourneyPlan_${distributorName}_${cycle}`;
    const result = await this._filesService.exportXLSXFile(fileName, xlsxData, 'Journey Plan', currentUser);
    return new ApiResponse(result);
  }

  @Post('base-journey-plan/export')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Export base journey plan',
  })
  async exportBaseJourneyPlan(@Body() exportJourneyPlanDto: ExportJourneyPlan, @CurrentUser() currentUser: any, @I18n() i18n: I18nContext) {
    const { distributorId, cycleId } = exportJourneyPlanDto;
    let validDistributorUUID = null;
    let validDistributorName = '';
    let validDistributorId = '';

    if (currentUser.roles.some((r) => r === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (distributor) {
        validDistributorUUID = distributor.distributor._id.toString();
        validDistributorName = distributor.distributor.distributorName;
        validDistributorId = distributor.distributor.distributorId;
      }
    } else {
      const distributor = await this._distributorService.findOne({ distributorId });
      if (!distributor) {
        throw new BadRequestException('plan.not_found_distributor');
      }
      validDistributorUUID = distributor._id.toString();
      validDistributorName = distributor.distributorName;
      validDistributorId = distributor.distributorId;
    }
    const existedCycle = await this._journeyPlanCycleService.findOne({ _id: new Types.ObjectId(cycleId) });
    if (existedCycle) {
      const allBaseJourneyPlans = await this._baseJourneyPlanService.getBaseJourneyPlansByCycleIdAndDistributorUUID(cycleId, validDistributorUUID);
      const xlsxData = allBaseJourneyPlans.map((item) => {
        return {
          [i18n.translate(`importExport.ucc`)]: item.outlet.ucc,
          [i18n.translate(`importExport.outletName`)]: item.outlet.name,
          [i18n.translate(`importExport.outletClass`)]: item.outlet.outletClass,
          [i18n.translate(`importExport.area`)]: item.outlet.area,
          [i18n.translate(`importExport.salesRepId`)]: item.saleRep.saleRepId,
          [i18n.translate(`importExport.week`)]: item.week,
          [i18n.translate(`importExport.day`)]: item.day,
          [i18n.translate(`importExport.distributorId`)]: validDistributorId,
          [i18n.translate(`importExport.distributorName`)]: validDistributorName,
        };
      });
      const { year, cycleName } = existedCycle;
      const cycle = cycleName?.replace(' ', '') + `-${year}`;
      const distributorName = validDistributorName?.replace(' ', '');
      const fileName = `BaseJourneyPlan_${distributorName}_${cycle}`;
      const result = await this._filesService.exportXLSXFile(fileName, xlsxData, 'Base Journey Plan', currentUser);
      return new ApiResponse(result);
    } else {
      throw new BadRequestException('plan.not_found_cycle');
    }
  }

  @Get(':journeyPlanningId')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  async getDetailJourneyPlaning(@I18n() i18n: I18nContext, @CurrentUser() currentUser, @Param('journeyPlanningId') journeyPlanningId: string) {
    if (!journeyPlanningId?.trim()) {
      throw new HttpException(
        await i18n.translate(`common.required_field`, {
          args: { fieldName: 'journeyPlanningId' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    /* - find journey planning */

    const existedOutletJourneyPlanning = await this._outletJourneyPlanningService.findOne({ _id: new Types.ObjectId(journeyPlanningId) });

    if (isEmptyObjectOrArray(existedOutletJourneyPlanning)) {
      throw new HttpException(
        await i18n.translate(`common.not_found`, {
          args: { fieldName: 'outlet journeyPlanning' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    const journeyPlanningDetail = await this._outletJourneyPlanningService.getDetailInformationJourneyPlanning(journeyPlanningId);
    const displayDay = !journeyPlanningDetail.rescheduled ? journeyPlanningDetail.day : journeyPlanningDetail.rescheduledDay;
    /* get all week of current cycle => calculate endOfCycleTime and startOfCycleTime */
    const allWeekOfCycle = await this._journeyPlanWeekService.getWeeksOfGivenCycle(journeyPlanningDetail?.week?.cycle?._id);
    const firstWeek = allWeekOfCycle.find((w) => w.weekName === WeekNameType.WEEK_1);
    const lastWeek = allWeekOfCycle.find((w) => w.weekName === WeekNameType.WEEK_4);
    const endOfCycleTime = moment(lastWeek.startTime).add(6, 'days').tz(process.env.TZ).endOf('day').toDate();
    const startOfCycleTime = firstWeek.startTime;

    const result = {
      outletUCC: journeyPlanningDetail?.outlet?.ucc,
      journeyPlanningId: journeyPlanningDetail?._id,
      saleRepId: journeyPlanningDetail?.saleRep?.saleRepId,
      week: Object.values(WeekNameType).findIndex((item) => item === journeyPlanningDetail?.week?.weekName) + 1,
      day: formatCurrentDay(displayDay, journeyPlanningDetail?.week?.startTime),
      cycleId: journeyPlanningDetail?.week?.cycle?._id,
      cycleName: journeyPlanningDetail?.week?.cycle?.cycleName,
      startCycle: startOfCycleTime,
      endCycle: endOfCycleTime,
      outletName: journeyPlanningDetail?.outlet?.name,
      year: journeyPlanningDetail?.week?.cycle?.year,
      outletId: journeyPlanningDetail?.outlet?._id,
      displayDay,
    };

    return new ApiResponse(result);
  }

  @Put('update')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  async updateJourneyPlaning(@I18n() i18n: I18nContext, @CurrentUser() currentUser, @Body() updateJourneyPlanning: UpdateJourneyPlanning) {
    const { cycleId, outletId, saleRepId, day, week, journeyPlanId, distributorId } = updateJourneyPlanning;
    await validateFields({ cycleId, outletId, saleRepId, day, week, journeyPlanId, distributorId }, `common.required_field`, i18n);

    const outlet = await this._outletsService.findOne({ _id: new Types.ObjectId(outletId), status: OutletStatus.ACTIVE });
    if (!outlet) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_outlet');
    }

    const existedSaleRep = await this._userService.findOne({ saleRepId: saleRepId });
    if (isEmptyObjectOrArray(existedSaleRep)) {
      throw new HttpException(
        await i18n.translate(`common.not_found`, {
          args: { fieldName: 'Sale Rep' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (existedSaleRep?.saleRepStatus != SalesRepStatus.ACTIVE) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_sale_rep');
    }

    /* - find journey planning */
    const existedOutletJourneyPlanning = await this._outletJourneyPlanningService.findOne({
      _id: new Types.ObjectId(journeyPlanId),
      outlet: new Types.ObjectId(outletId),
      saleRep: existedSaleRep._id,
    });
    await validateFields({ 'outlet journeyPlanning': existedOutletJourneyPlanning }, `common.not_found`, i18n);

    //check cycle
    const weekObj = await this._journeyPlanWeekService.findById(existedOutletJourneyPlanning?.week._id);
    if (weekObj.cycle._id != cycleId) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_cycle_id');
    }

    /* check format of week and day in body */
    if (!Object.values(Days).some((nd) => nd === Number(day))) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_day');
    }
    if (!Object.values(Weeks).some((nw) => nw === Number(week))) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_week');
    }

    /* get week name => find week */
    const keyOfWeek = Object.entries(Weeks).find(([, value]) => value === Number(week));

    const weekName = WeekNameType[keyOfWeek[0]];
    const findWeek = await this._journeyPlanWeekService.findOne({
      weekName: weekName,
      cycle: new Types.ObjectId(cycleId),
    });
    if (isEmptyObjectOrArray(findWeek)) {
      throw new HttpException(
        await i18n.translate(`common.not_found`, {
          args: { fieldName: 'week' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    /*
    - If status visit is completed => not update
    - If status visit is inprogress in today => not update
    */
    if (
      existedOutletJourneyPlanning.visitStatus === VisitStatus.COMPLETED ||
      (existedOutletJourneyPlanning.visitStatus === VisitStatus.IN_PROGRESS && !moment(existedOutletJourneyPlanning.day).diff(new Date(), 'days'))
    ) {
      throw new HttpException(await i18n.translate(`plan.cannot_reschedule_in_progress_plan`), HttpStatus.BAD_REQUEST);
    }
    /* Find exist journey planning in week */
    const existAnotherJourneyPlan = await this._outletJourneyPlanningService.findByOutletAndSalRepAndWeek(outletId, existedSaleRep._id.toString(), findWeek._id.toString());

    if (existAnotherJourneyPlan && existAnotherJourneyPlan._id.toString() !== journeyPlanId) {
      throw new BadRequestException('plan.existed_outlet_plan');
    }
    const distributor = await this._distributorService.findOne({
      distributorId,
    });
    if (!distributor) {
      throw new BadRequestException('plan.not_found_distributor');
    }
    const distributorRelation = await this._distributorUserRelationService.findOne({ user: existedSaleRep._id, distributor: distributor._id });
    if (!distributorRelation) {
      throw new BadRequestException('plan.not_found_relation_sales_rep_distributor');
    }
    const { startingTimeframe, timeInterval } = await this._baseJourneyPlanSettingService.getBaseJourneyPlanSettingByDistributor(distributorRelation.distributor._id.toString());
    const numberOfTimeSlotPerDay = this._baseJourneyPlanSettingService.calculateTimeSlotByTimeIntervalAndStartingTimeframe(startingTimeframe, timeInterval);

    const numberPlansOfSalesRepByWeekAndDay = await this._baseJourneyPlanService.countByGivenCondition({
      saleRep: existedSaleRep._id,
      week,
      day,
      distributor: distributorRelation?.distributor._id,
      cycle: weekObj.cycle._id,
    });

    if (numberPlansOfSalesRepByWeekAndDay + 1 > numberOfTimeSlotPerDay) {
      throw new BadRequestException(
        await i18n.t('distributor.base_journey_plan.invalid_number_plan_per_day', {
          args: { numberOfPlansPerDay: numberOfTimeSlotPerDay, week, day, salesRepId: saleRepId },
        }),
      );
    }
    const [hour, minute] = startingTimeframe.split(':');
    let plannedDate = moment(findWeek.startTime)
      .add(day - 1, 'days')
      .add(Number(hour), 'hours')
      .add(Number(minute), 'minutes')
      .toDate();
    const lastedJourneyPlan = await this._outletJourneyPlanningService.getLastDayJourneyPlanBySalesRepId(existedSaleRep._id, findWeek._id, plannedDate);

    if (!isEmptyObjectOrArray(lastedJourneyPlan)) {
      const displayDay = !lastedJourneyPlan.rescheduled ? lastedJourneyPlan.day : lastedJourneyPlan.rescheduledDay;
      plannedDate = moment(displayDay).add(timeInterval, 'minutes').toDate();
      const diffBetweenDate = moment(displayDay).tz(process.env.TZ).endOf('d').diff(moment(plannedDate).tz(process.env.TZ).endOf('d'), 'd');

      if (diffBetweenDate !== 0) {
        throw new HttpException(await i18n.translate(`plan.cannot_reschedule_in_progress_plan`), HttpStatus.BAD_REQUEST);
      }
    }
    if (this._outletJourneyPlanningService.isPastDay(plannedDate)) {
      throw new BadRequestException('plan.cannot_update_plan_to_past_week_day');
    }
    const updateJourneyPlaning = await this._outletJourneyPlanningService.update(existedOutletJourneyPlanning._id, {
      rescheduledDay: plannedDate,
      rescheduled: true,
      week: findWeek._id,
      visitStatus: VisitStatus.START_VISIT,
      $unset: {
        missedReason: 1,
        outletReport: 1,
      },
    });

    //PDH-2865
    await this._userService.addressChangedUpdate(existedSaleRep, true);
    this._saleRepStatisticService.calculateCallComplianceRateBySale({ salesRepId: existedSaleRep._id, date: moment().startOf('d').toDate() }).then();

    return new ApiResponse(updateJourneyPlaning);
  }

  @Post('')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Create a journey plan',
  })
  async createAJourneyPlan(@Body() createJourneyPlanDto: CreateJourneyPlanDto, @CurrentUser() currentUser: any, @I18n() i18n: I18nContext) {
    const { outletId, saleRepId, week, day, distributorId } = createJourneyPlanDto;
    const existedSaleRep = await this._userService.findOne({ saleRepId, saleRepStatus: SalesRepStatus.ACTIVE });
    if (!existedSaleRep) {
      throw new BadRequestException('plan.not_found_sales_rep');
    }
    const saleRepUUID = existedSaleRep._id.toString();
    const userAdminRelations = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id);
    const distributor = userAdminRelations.find((e) => e.distributor.distributorId === distributorId);
    if (!distributor) {
      throw new BadRequestException('plan.not_found_distributor');
    }
    const existedSalesRepDistributorRelation = await this._distributorUserRelationService.findOne({
      user: new Types.ObjectId(saleRepUUID),
      distributor: distributor.distributor._id,
    });
    if (!existedSalesRepDistributorRelation) {
      throw new BadRequestException('plan.not_found_relation_sales_rep_distributor');
    }
    const [existedSaleRepOutletRelation] = await this._saleRepOutletRelationService.filterActiveSaleRepOutletRelations({
      'o._id': new Types.ObjectId(outletId),
      'u._id': new Types.ObjectId(saleRepUUID),
    });
    if (!existedSaleRepOutletRelation) {
      throw new BadRequestException('plan.not_found_relation_sales_rep_outlet');
    }
    if (!Object.values(Days).some((nd) => nd === Number(day))) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_day');
    }
    if (!Object.values(Weeks).some((nw) => nw === Number(week))) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_week');
    }
    const currentCycle = await this._journeyPlanWeekService.getCycleByGivenDay();
    if (!currentCycle) {
      throw new BadRequestException('plan.not_found_cycle');
    }
    const existedBaseJourneyPlanOutletByGivenWeek = await this._baseJourneyPlanService.findOne({
      cycle: currentCycle._id,
      week,
      outlet: new Types.ObjectId(outletId),
      distributor: distributor.distributor._id,
    });
    if (existedBaseJourneyPlanOutletByGivenWeek) {
      throw new BadRequestException(
        await i18n.t('distributor.base_journey_plan.invalid_number_plan_per_week', {
          args: {
            week,
            ucc: existedSaleRepOutletRelation.o.ucc,
          },
        }),
      );
    }

    const { startingTimeframe, timeInterval } = await this._baseJourneyPlanSettingService.getBaseJourneyPlanSettingByDistributor(distributor.distributor._id.toString());
    const numberOfTimeSlotPerDay = this._baseJourneyPlanSettingService.calculateTimeSlotByTimeIntervalAndStartingTimeframe(startingTimeframe, timeInterval);

    const numberPlansOfSalesRepByWeekAndDay = await this._baseJourneyPlanService.countByGivenCondition({
      saleRep: new Types.ObjectId(saleRepUUID),
      week,
      day,
      distributor: distributor.distributor._id,
      cycle: currentCycle._id,
    });

    if (numberPlansOfSalesRepByWeekAndDay + 1 > numberOfTimeSlotPerDay) {
      throw new BadRequestException(
        await i18n.t('distributor.base_journey_plan.invalid_number_plan_per_day', {
          args: {
            numberOfPlansPerDay: numberOfTimeSlotPerDay,
            week,
            day,
            salesRepId: existedSaleRepOutletRelation.u.saleRepId,
          },
        }),
      );
    }

    const allWeekOfCurrentCycle = await this._journeyPlanWeekService.getWeeksOfGivenCycle(currentCycle._id.toString());
    const planedWeek = this._baseJourneyPlanService.getWeekByWeekNumber(week, allWeekOfCurrentCycle);

    const existedRealPlanOutletByGivenWeek = await this._outletJourneyPlanningService.findOne({
      outlet: new Types.ObjectId(outletId),
      week: planedWeek._id,
    });
    if (existedRealPlanOutletByGivenWeek) {
      throw new BadRequestException('plan.exceed_plan_per_outlet');
    }

    const plannedDate = moment(planedWeek.startTime)
      .add(day - 1, 'days')
      .toDate();

    if (this._outletJourneyPlanningService.isPastDay(plannedDate)) {
      throw new BadRequestException('plan.invalid_week_day');
    }

    const [latestPlan] = await this._outletJourneyPlanningService.getLatestPlanOfSalesRepByGivenDay(saleRepUUID, plannedDate);
    let planedDateTime: Date;
    if (latestPlan) {
      planedDateTime = moment(latestPlan.ojp.displayDay).add(timeInterval, 'minutes').toDate();
      if (!this._outletJourneyPlanningService.isSameDay(planedDateTime, latestPlan.ojp.displayDay)) {
        throw new BadRequestException('plan.exceeded_time');
      }
    } else {
      const [hour, minute] = startingTimeframe.split(':');
      planedDateTime = moment(plannedDate).add(Number(hour), 'hours').add(Number(minute), 'minutes').toDate();
    }

    await this._outletJourneyPlanningService.create({
      outlet: new Types.ObjectId(outletId),
      saleRep: new Types.ObjectId(saleRepUUID),
      week: planedWeek._id,
      day: planedDateTime,
    });

    const createBaseJourneyPlanDto = {
      week,
      day,
      outlet: new Types.ObjectId(outletId),
      saleRep: new Types.ObjectId(saleRepUUID),
      distributor: distributor.distributor._id,
      cycle: currentCycle._id,
    };
    await this._baseJourneyPlanService.create(createBaseJourneyPlanDto);

    if (process.env.OPCO == OpCos.Cambodia) {
      // PDH-2865 Change status to re-calculate route by GG Maps API
      await this._userService.update(saleRepUUID, { isAddressChanged: true });
    }
    this._saleRepStatisticService.calculateCallComplianceRateBySale({ salesRepId: existedSaleRep._id, date: moment().startOf('d').toDate() }).then();

    return new ApiResponse(createBaseJourneyPlanDto);
  }

  @Post('support-case')
  //@Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Support cases Create a journey plan',
  })
  async createAJourneyPlanSupportCase(
    @CurrentUser() currentUser: any,
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 })],
      }),
    )
    file: Express.Multer.File,
    @I18n() i18n: I18nContext,
  ) {
    const outletData = JSON.parse(file.buffer.toString());

    const results = [];
    for (const row of outletData) {
      const { UCC, saleRepId, week, day, distributorId } = row;
      if (!UCC || !saleRepId || !distributorId || !week || !day) {
        continue;
      }

      const outlet = await this._outletsService.findOne({ ucc: UCC });
      const outletId = outlet?._id;
      const existedSaleRep = await this._userService.findOne({ saleRepId, saleRepStatus: SalesRepStatus.ACTIVE });
      if (!existedSaleRep) {
        continue;
      }
      const saleRepUUID = existedSaleRep._id.toString();
      const userAdminRelations = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id);
      const distributor = userAdminRelations.find((e) => e.distributor.distributorId === distributorId.toString());
      if (!Object.values(Days).some((nd) => nd === Number(day))) {
        continue;
      }
      if (!Object.values(Weeks).some((nw) => nw === Number(week))) {
        continue;
      }
      const currentCycle = await this._journeyPlanWeekService.getCycleByGivenDay();
      if (!currentCycle) {
        continue;
      }

      const existedBaseJourneyPlanOutletByGivenWeek = await this._baseJourneyPlanService.findOne({
        cycle: currentCycle._id,
        week,
        outlet: new Types.ObjectId(outletId),
        distributor: distributor.distributor._id,
      });
      if (existedBaseJourneyPlanOutletByGivenWeek) {
        continue;
      }

      const { startingTimeframe, timeInterval } = await this._baseJourneyPlanSettingService.getBaseJourneyPlanSettingByDistributor(distributor.distributor._id.toString());
      const numberOfTimeSlotPerDay = this._baseJourneyPlanSettingService.calculateTimeSlotByTimeIntervalAndStartingTimeframe(startingTimeframe, timeInterval);

      const numberPlansOfSalesRepByWeekAndDay = await this._baseJourneyPlanService.countByGivenCondition({
        saleRep: new Types.ObjectId(saleRepUUID),
        week,
        day,
        distributor: distributor.distributor._id,
        cycle: currentCycle._id,
      });

      if (numberPlansOfSalesRepByWeekAndDay + 1 > numberOfTimeSlotPerDay) {
        continue;
      }

      const allWeekOfCurrentCycle = await this._journeyPlanWeekService.getWeeksOfGivenCycle(currentCycle._id.toString());
      const planedWeek = this._baseJourneyPlanService.getWeekByWeekNumber(week, allWeekOfCurrentCycle);

      const existedRealPlanOutletByGivenWeek = await this._outletJourneyPlanningService.findOne({
        outlet: new Types.ObjectId(outletId),
        week: planedWeek._id,
      });
      if (existedRealPlanOutletByGivenWeek) {
        continue;
      }

      const plannedDate = moment(planedWeek.startTime)
        .add(day - 1, 'days')
        .toDate();

      if (this._outletJourneyPlanningService.isPastDay(plannedDate)) {
        // printLog('plan.invalid_week_day');
        continue;
      }

      const [latestPlan] = await this._outletJourneyPlanningService.getLatestPlanOfSalesRepByGivenDay(saleRepUUID, plannedDate);
      let planedDateTime: Date;
      if (latestPlan) {
        planedDateTime = moment(latestPlan.ojp.displayDay).add(timeInterval, 'minutes').toDate();
        if (!this._outletJourneyPlanningService.isSameDay(planedDateTime, latestPlan.ojp.displayDay)) {
          // printLog('plan.exceeded_time');
          continue;
        }
      } else {
        const [hour, minute] = startingTimeframe.split(':');
        planedDateTime = moment(plannedDate).add(Number(hour), 'hours').add(Number(minute), 'minutes').toDate();
      }

      await this._outletJourneyPlanningService.create({
        outlet: new Types.ObjectId(outletId),
        saleRep: new Types.ObjectId(saleRepUUID),
        week: planedWeek._id,
        day: planedDateTime,
      });

      const createBaseJourneyPlanDto = {
        week,
        day,
        outlet: new Types.ObjectId(outletId),
        saleRep: new Types.ObjectId(saleRepUUID),
        distributor: distributor.distributor._id,
        cycle: currentCycle._id,
      };
      const result = await this._baseJourneyPlanService.create(createBaseJourneyPlanDto);
      results.push(result);
    }

    return new ApiResponse(results);
  }

  @Post('cycle/:cycleId')
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Create a journey plan by a cycle (For testing purpose)',
  })
  async createAJourneyPlanByCycle(
    @Body() createJourneyPlanDto: CreateJourneyPlanDto,
    @Param('cycleId') cycleId: string,
    @CurrentUser() currentUser: any,
    @I18n() i18n: I18nContext,
  ) {
    const { outletId, saleRepId, week, day } = createJourneyPlanDto;

    const [existedCycle, existedSaleRep, userDistributorRelation] = await Promise.all([
      this._journeyPlanCycleService.findOne({ _id: new Types.ObjectId(cycleId) }),
      this._userService.findOne({ saleRepId, saleRepStatus: SalesRepStatus.ACTIVE }),
      this._distributorUserRelationService.findOne({
        userAdmin: currentUser._id,
      }),
    ]);
    if (!existedSaleRep) {
      throw new BadRequestException('plan.not_found_sales_rep');
    }
    if (!existedCycle) {
      throw new BadRequestException('plan.not_found_cycle');
    }
    const saleRepUUID = existedSaleRep._id.toString();
    if (!userDistributorRelation) {
      throw new BadRequestException('plan.not_found_distributor');
    }
    const [existedSalesRepDistributorRelation, [existedSaleRepOutletRelation]] = await Promise.all([
      this._distributorUserRelationService.findOne({
        user: new Types.ObjectId(saleRepUUID),
        distributor: userDistributorRelation.distributor,
      }),
      this._saleRepOutletRelationService.filterActiveSaleRepOutletRelations({
        'o._id': new Types.ObjectId(outletId),
        'u._id': new Types.ObjectId(saleRepUUID),
      }),
    ]);
    if (!existedSaleRepOutletRelation) {
      throw new BadRequestException('plan.not_found_relation_sales_rep_outlet');
    }
    if (!existedSalesRepDistributorRelation) {
      throw new BadRequestException('plan.not_found_relation_sales_rep_distributor');
    }
    if (!Object.values(Days).some((nd) => nd === Number(day))) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_day');
    }
    if (!Object.values(Weeks).some((nw) => nw === Number(week))) {
      throw new BadRequestException('distributor.base_journey_plan.invalid_week');
    }

    const existedBaseJourneyPlanOutletByGivenWeek = await this._baseJourneyPlanService.findOne({
      cycle: existedCycle._id,
      week,
      outlet: new Types.ObjectId(outletId),
      distributor: userDistributorRelation.distributor,
    });
    if (existedBaseJourneyPlanOutletByGivenWeek) {
      throw new BadRequestException(
        await i18n.t('distributor.base_journey_plan.invalid_number_plan_per_week', {
          args: {
            week,
            ucc: existedSaleRepOutletRelation.o.ucc,
          },
        }),
      );
    }

    const { startingTimeframe, timeInterval } = await this._baseJourneyPlanSettingService.getBaseJourneyPlanSettingByDistributor(userDistributorRelation.distributor.toString());
    const numberOfTimeSlotPerDay = this._baseJourneyPlanSettingService.calculateTimeSlotByTimeIntervalAndStartingTimeframe(startingTimeframe, timeInterval);

    const numberPlansOfSalesRepByWeekAndDay = await this._baseJourneyPlanService.countByGivenCondition({
      saleRep: new Types.ObjectId(saleRepUUID),
      week,
      day,
      distributor: userDistributorRelation.distributor,
      cycle: existedCycle._id,
    });

    if (numberPlansOfSalesRepByWeekAndDay + 1 > numberOfTimeSlotPerDay) {
      throw new BadRequestException(
        await i18n.t('distributor.base_journey_plan.invalid_number_plan_per_day', {
          args: {
            numberOfPlansPerDay: numberOfTimeSlotPerDay,
            week,
            day,
            salesRepId: existedSaleRepOutletRelation.u.saleRepId,
          },
        }),
      );
    }

    const allWeekOfCurrentCycle = await this._journeyPlanWeekService.getWeeksOfGivenCycle(existedCycle._id.toString());
    const planedWeek = this._baseJourneyPlanService.getWeekByWeekNumber(week, allWeekOfCurrentCycle);

    const existedRealPlanOutletByGivenWeek = await this._outletJourneyPlanningService.findOne({
      outlet: new Types.ObjectId(outletId),
      week: planedWeek._id,
    });
    if (existedRealPlanOutletByGivenWeek) {
      throw new BadRequestException('plan.exceed_plan_per_outlet');
    }

    const plannedDate = moment(planedWeek.startTime)
      .add(day - 1, 'days')
      .toDate();

    if (this._outletJourneyPlanningService.isPastDay(plannedDate)) {
      throw new BadRequestException('plan.invalid_week_day');
    }

    const [latestPlan] = await this._outletJourneyPlanningService.getLatestPlanOfSalesRepByGivenDay(saleRepUUID, plannedDate);
    let planedDateTime: Date;
    if (latestPlan) {
      planedDateTime = moment(latestPlan.ojp.displayDay).add(timeInterval, 'minutes').toDate();
      if (!this._outletJourneyPlanningService.isSameDay(planedDateTime, latestPlan.ojp.displayDay)) {
        throw new BadRequestException('plan.exceeded_time');
      }
    } else {
      const [hour, minute] = startingTimeframe.split(':');
      planedDateTime = moment(plannedDate).add(Number(hour), 'hours').add(Number(minute), 'minutes').toDate();
    }

    await this._outletJourneyPlanningService.create({
      outlet: new Types.ObjectId(outletId),
      saleRep: new Types.ObjectId(saleRepUUID),
      week: planedWeek._id,
      day: planedDateTime,
    });

    const createBaseJourneyPlanDto = {
      week,
      day,
      outlet: new Types.ObjectId(outletId),
      saleRep: new Types.ObjectId(saleRepUUID),
      distributor: userDistributorRelation.distributor,
      cycle: existedCycle._id,
    };
    await this._baseJourneyPlanService.create(createBaseJourneyPlanDto);

    return new ApiResponse(createBaseJourneyPlanDto);
  }

  @Get(':planId/details')
  @Roles(ConstantRoles.SALE_REP)
  async getJourneyPlanVisibility(@Param('planId') planId: string, @CurrentUser() salesRep: User, @I18n() i18n: I18nContext) {
    const details = await this._outletJourneyPlanningService.getJourneyPlanDetails({ planId, salesRepId: String(salesRep._id), i18n });
    return new ApiResponse(details);
  }
}
