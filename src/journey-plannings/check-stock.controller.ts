import { Body, Controller, Get, Post, Query, UseGuards, Version } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { ApiException } from '../shared/api-exception.model';
import { Roles } from '../shared/decorator/roles.decorator';
import { RolesGuard } from '../shared/guards/roles.guard';
import { ConstantRoles } from 'src/utils/constants/role';
import { GetCheckStock, SyncCheckStock } from './dtos/check-stock.dto';
import { I18n, I18nContext } from 'nestjs-i18n';
import { ApiResponse } from 'src/shared/response/api-response';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { CheckStocksService } from './services/check-stock.service';
import { OmsService } from '../external/services/oms.service';
import { PriorityFilterSetting } from '../settings/constants/filter';

@ApiTags('Check Stock')
@ApiHeader({ name: 'locale', description: 'en' })
@Controller('api/check-stock')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CheckStockController {
  constructor(private checkStocksService: CheckStocksService, private omsService: OmsService) {}

  @Version(['3', '4', '5', '6'])
  @Get('get-data')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  async getCheckStockDataV3(@Query() query: GetCheckStock, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    const res = await this.checkStocksService.getCheckStockDataV3(query, i18n, currentUser);
    return new ApiResponse({ ...res, priorityFilter: await PriorityFilterSetting(i18n) });
  }

  @Get('get-data')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  async getCheckStockData(@Query() query: GetCheckStock, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    const res = await this.checkStocksService.getCheckStockData(query, i18n, currentUser);
    return new ApiResponse(res);
  }

  @Version(['3', '4', '5', '6'])
  @Post('sync-data')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  async syncCheckStockDataV3(@Body() body: SyncCheckStock, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    await this.checkStocksService.syncCheckStockData(body, i18n, currentUser);
    return new ApiResponse();
  }

  @Post('sync-data')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  async syncCheckStockData(@Body() body: SyncCheckStock, @I18n() i18n: I18nContext, @CurrentUser() currentUser) {
    await this.checkStocksService.syncCheckStockData(body, i18n, currentUser);
    return new ApiResponse();
  }
}
