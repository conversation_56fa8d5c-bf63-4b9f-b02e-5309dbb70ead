import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from '../auth/auth.module';
import { DistributorModule } from '../distributor/distributor.module';
import { FilesModule } from '../files/files.module';
import { OutletsModule } from '../outlets/outlets.module';
import { SaleRepModule } from '../sale-rep/sale-rep.module';
import { UsersModule } from '../users/users.module';
import { JourneyPlanningsController } from './journey-plannings.controller';
import { JourneyPlanMissedReasonHistory, JourneyPlanMissedReasonHistorySchema } from './schemas/journey-plan-absence.schema';
import { JourneyPlanCycle, JourneyPlanCycleSchema } from './schemas/journey-plan-cycle.schema';
import { JourneyPlanMissedReason, JourneyPlanMissedReasonSchema } from './schemas/journey-plan-missed-reason.schema';
import { JourneyPlanWeek, JourneyPlanWeekSchema } from './schemas/journey-plan-week.schema';
import { OutletJourneyPlanning, OutletJourneyPlanningSchema } from './schemas/outlet-journey-planning.schema';
import { JourneyPlanCycleService } from './services/journey-plan-cycle.service';
import { JourneyPlanMissedReasonHistoriesService } from './services/journey-plan-missed-reason-history.service';
import { JourneyPlanMissedReasonsService } from './services/journey-plan-missed-reasons.service';
import { JourneyPlanWeekService } from './services/journey-plan-week.service';
import { OutletJourneyPlanningService } from './services/outlet-journey-planning.service';
import { ThirdPartiesModule } from '../third-parties/third-parties.module';
import { MissReason, MissReasonSchema } from 'src/miss-reasons/schemas/miss-reason.schema';
import { MissReasonsModule } from 'src/miss-reasons/miss-reasons.module';
import { ColdStock, ColdStockSchema } from './schemas/cold-stock.schema';
import { ColdStockController } from './cold-stock.controller';
import { ColdStocksService } from './services/cold-stocks.service';
import { JourneyPlanVisibilityController } from './visibility.controller';
import { SaleRepExecutionVisibility, SaleRepExecutionVisibilitySchema } from 'src/sale-rep/schemas';
import { JourneyPlanVisibilityService } from './services/visibility.service';
import { JourneyPlanVisitedStepsController } from './visited-steps.controller';
import { JourneyPlanVisitedStepsService } from './services/visited-steps.service';
import { CheckStockController } from './check-stock.controller';
import { ExternalModule } from 'src/external/external.module';
import { VisibilityExecution, VisibilityExecutionSchema } from './schemas/visibility-execution.schema';
import { Outlet, OutletSchema } from 'src/outlets/schemas/outlet.schema';
import { Files, FilesSchema } from 'src/files/schemas';
import { CheckStocksService } from './services/check-stock.service';
import { User, UserSchema } from 'src/users/schemas/user.schema';
import { OmsModule } from 'src/oms/oms.module';
import { OrdersModule } from 'src/orders/orders.module';
import { SettingsModule } from '../settings/settings.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: JourneyPlanCycle.name, schema: JourneyPlanCycleSchema },
      { name: OutletJourneyPlanning.name, schema: OutletJourneyPlanningSchema },
      { name: JourneyPlanWeek.name, schema: JourneyPlanWeekSchema },
      { name: JourneyPlanMissedReasonHistory.name, schema: JourneyPlanMissedReasonHistorySchema },
      { name: JourneyPlanMissedReason.name, schema: JourneyPlanMissedReasonSchema },
      { name: MissReason.name, schema: MissReasonSchema },
      { name: ColdStock.name, schema: ColdStockSchema },
      { name: SaleRepExecutionVisibility.name, schema: SaleRepExecutionVisibilitySchema },
      { name: VisibilityExecution.name, schema: VisibilityExecutionSchema },
      { name: Outlet.name, schema: OutletSchema },
      { name: Files.name, schema: FilesSchema },
      { name: User.name, schema: UserSchema },
    ]),
    AuthModule,
    FilesModule,
    MissReasonsModule,
    OmsModule,
    forwardRef(() => ExternalModule),
    forwardRef(() => SaleRepModule),
    forwardRef(() => OutletsModule),
    forwardRef(() => UsersModule),
    forwardRef(() => DistributorModule),
    forwardRef(() => ThirdPartiesModule),
    forwardRef(() => OrdersModule),
    forwardRef(() => SettingsModule),
  ],
  providers: [
    JourneyPlanCycleService,
    OutletJourneyPlanningService,
    JourneyPlanWeekService,
    JourneyPlanMissedReasonHistoriesService,
    JourneyPlanMissedReasonsService,
    ColdStocksService,
    JourneyPlanVisibilityService,
    JourneyPlanVisitedStepsService,
    CheckStocksService,
  ],
  controllers: [JourneyPlanningsController, ColdStockController, JourneyPlanVisibilityController, JourneyPlanVisitedStepsController, CheckStockController],
  exports: [
    JourneyPlanCycleService,
    OutletJourneyPlanningService,
    JourneyPlanWeekService,
    JourneyPlanMissedReasonHistoriesService,
    JourneyPlanMissedReasonsService,
    ColdStocksService,
    CheckStocksService,
    JourneyPlanVisibilityService,
    JourneyPlanVisitedStepsService,
  ],
})
export class JourneyPlanningsModule {}
