import { Body, Controller, Get, HttpException, Post, Query, Request, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from '../shared/guards/roles.guard';
import { ConstantRoles } from 'src/utils/constants/role';
import { ApiException } from 'src/shared/api-exception.model';
import { I18n, I18nContext } from 'nestjs-i18n';
import { ApiResponse } from 'src/shared/response/api-response';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { GetOfflineData } from './dtos/get-offline-data.dto';
import { getListCompetitors } from 'src/journey-plannings/constants/competitor';
import { SyncOfflineData } from './dtos/sync-offline-data.dto';
import { OfflineModeV2Service } from './services/offline-mode-v2.service';
import { SaleRepStatisticService } from 'src/sale-rep/services';
import { OmsSalesRepStatisticsService } from 'src/oms/services/sales-rep-statistics.service';

@ApiTags('OfflineModeV2')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/offline-mode-v2')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class OfflineModeV2Controller {
  constructor(private readonly offlineModeV2Service: OfflineModeV2Service, private readonly omsSalesRepStatisticsService: OmsSalesRepStatisticsService) {}

  @Get('get-offline-data')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Get offline data',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getOfflineData(@Query() query: GetOfflineData, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      const [res, performance] = await Promise.all([
        this.offlineModeV2Service.getOfflineData(query, currentUser),
        this.omsSalesRepStatisticsService.getStatistics(currentUser._id),
      ]);
      return new ApiResponse({
        time: res || {},
        performance,
      });
    } catch (e) {
      throw new HttpException(e.message, e.status || 500);
    }
  }

  @Post('sync-offline-data')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'sync offline data',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async syncOfflineData(@Body() body: SyncOfflineData, @I18n() i18n: I18nContext, @CurrentUser() currentUser, @Request() request) {
    try {
      const res = await this.offlineModeV2Service.syncOfflineData(body, currentUser, i18n);
      return new ApiResponse(res);
    } catch (e) {
      throw new HttpException(e.message, e.status || 500);
    }
  }
}
