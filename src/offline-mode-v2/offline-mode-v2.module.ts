import { forwardRef, Module } from '@nestjs/common';
import { OfflineModeV2Service } from './services/offline-mode-v2.service';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';
import { ExternalModule } from '../external/external.module';
import { OutletsModule } from '../outlets/outlets.module';
import { SaleRepModule } from '../sale-rep/sale-rep.module';
import { DistributorModule } from '../distributor/distributor.module';
import { OfflineModeV2Controller } from './offline-mode-v2.controller';
import { JourneyPlanningsModule } from 'src/journey-plannings/journey-plannings.module';
import { OrdersModule } from 'src/orders/orders.module';
import { OmsModule } from 'src/oms/oms.module';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    forwardRef(() => UsersModule),
    ExternalModule,
    OutletsModule,
    SaleRepModule,
    DistributorModule,
    JourneyPlanningsModule,
    OrdersModule,
    OmsModule,
  ],
  controllers: [OfflineModeV2Controller],
  providers: [OfflineModeV2Service],
  exports: [OfflineModeV2Service],
})
export class OfflineModeV2Module {}
