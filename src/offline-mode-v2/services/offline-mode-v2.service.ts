import * as moment from 'moment';
import { Types } from 'mongoose';
import { SyncOfflineData } from '../dtos/sync-offline-data.dto';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { isEmptyObjectOrArray } from 'src/utils';
import { I18nContext } from 'nestjs-i18n';
import { OutletsService } from 'src/outlets/services/outlets.service';
import { UsersService } from 'src/users/services/users.service';
import { OmsService } from 'src/external/services/oms.service';
import { DistributorUserRelationService } from 'src/distributor/services';
import { SaleRepOutletRelationService } from 'src/outlets/services/sale-rep-outlet-relation.service';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { RoutePlanService } from 'src/sale-rep/services/route-plan.service';
import { ColdStocksService } from 'src/journey-plannings/services/cold-stocks.service';
import { GetOfflineData } from '../dtos/get-offline-data.dto';
import { User } from 'src/users/schemas/user.schema';
import { CheckStocksService } from 'src/journey-plannings/services/check-stock.service';
import { OrdersService } from 'src/orders/services/orders.service';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import { JourneyPlanVisibilityService } from 'src/journey-plannings/services/visibility.service';
import { JourneyPlanVisitedStepsService } from 'src/journey-plannings/services/visited-steps.service';
import { getListCompetitors } from 'src/journey-plannings/constants/competitor';
import { JourneyPlanVisitStep } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { OmsRepReportsCalculatorsService } from 'src/oms/services/rep-reports-calculator.service';
import { MockService } from 'src/external/services/mock.service';
import { ConstantCommons } from '../../utils/constants';

@Injectable()
export class OfflineModeV2Service {
  constructor(
    private readonly outletService: OutletsService,
    private readonly userService: UsersService,
    private readonly omsService: OmsService,
    private readonly distributorUserRelationService: DistributorUserRelationService,
    private readonly saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly routePlanService: RoutePlanService,
    private readonly coldStocksService: ColdStocksService,
    private readonly checkStocksService: CheckStocksService,
    private readonly ordersService: OrdersService,
    private readonly journeyPlanVisibilityService: JourneyPlanVisibilityService,
    private readonly journeyPlanVisitedStepsService: JourneyPlanVisitedStepsService,
    private readonly omsRepReportsCalculatorsService: OmsRepReportsCalculatorsService,
    private readonly mockService: MockService,
  ) {}

  async getAbsence(jp) {
    return jp?.missedReason ? await this.outletService.getAbsenceData(jp._id.toString()) : null;
  }

  async getOfflineData(query: GetOfflineData, currentUser: User) {
    let { from, to } = query;
    from = moment(from || new Date().toISOString())
      .tz(process.env.TZ)
      .startOf('day')
      .toISOString();
    to = moment(to || new Date().toISOString())
      .tz(process.env.TZ)
      .endOf('day')
      .toISOString();
    const salesRepId = currentUser._id;
    const listJp = await this.outletJourneyPlanningService.getOfflinePlanByOutlet(new Types.ObjectId(salesRepId), from, to);
    const startDate = moment(from).tz(process.env.TZ);
    const endDate = moment(to).tz(process.env.TZ);
    const res = {};
    while (startDate.isSameOrBefore(endDate)) {
      // filter list jp by day
      const tempDate = startDate.clone().endOf('day');
      const listJPFilteredRaw = listJp.filter((jp) => {
        if (jp.rescheduled) {
          const day = moment(jp.rescheduledDay);
          if (day.isSameOrAfter(startDate) && day.isSameOrBefore(tempDate)) {
            return true;
          }
        } else {
          const day = moment(jp.day);
          if (day.isSameOrAfter(startDate) && day.isSameOrBefore(tempDate)) {
            return true;
          }
        }
        return false;
      });
      // sort jp like api /api/sale-rep/home/<USER>
      const listJPFiltered = await this.routePlanService.transformTodayOutlets({
        plans: listJPFilteredRaw,
        salesRep: currentUser,
      });
      // add data like api /api/outlets/details/:id
      const resData = [];
      while (listJPFiltered.length > 0) {
        const temp = listJPFiltered.splice(0, 5);
        await Promise.all(
          temp.map(async (item) => {
            const jp = listJPFilteredRaw.find((e) => e._id.toString() == item.planId);
            if (jp) {
              const [coldStock, absence, visibility] = await Promise.all([
                this.coldStocksService.getColdStockByJpAndOulet({
                  outlet: jp.outlet._id,
                  planId: jp._id,
                }),
                this.getAbsence(jp),
                this.journeyPlanVisibilityService.getJourneyPlanVisibilityOffline({ plan: jp }),
              ]);
              let cachedData = null;
              if (currentUser.isTestAccount) {
                cachedData = await this.mockService.getCachedDataByOutlet(jp.outlet._id);
              }
              if (!currentUser.isTestAccount && jp?.outlet?.isOmsConnected) {
                cachedData = await this.omsService.getCachedDataByOutlet({
                  outletId: jp.outlet._id,
                  useDb: true,
                });
              }
              let isOmsConnected = false;
              if (cachedData) {
                isOmsConnected = true;
              }
              const startDateOrderFilter = moment().subtract(1, 'month').startOf('day');
              const endDateOrderFilter = moment().endOf('day');
              const orders =
                cachedData?.orders
                  ?.filter((order: any) => {
                    return moment(order.created_at).isBetween(startDateOrderFilter, endDateOrderFilter);
                  })
                  .slice(0, 5) || [];
              const productCached = cachedData?.products || [];
              const mustHaveSKUs = productCached.filter((p) => p.tagging == ConstantCommons.MUST_HAVE_SKU_LABEL);
              const listProducts = productCached.filter((p) => p.tagging != ConstantCommons.MUST_HAVE_SKU_LABEL);
              resData.push({
                ...item,
                skipReport: this.outletService.transformAbsence(absence),
                isOmsConnected,
                checkStock: jp.checkStock,
                visitedSteps: jp.visitedSteps,
                startedAt: jp.visitedDay,
                endedAt: jp.visitStatus === VisitStatus.COMPLETED ? jp.updatedAt : null,
                coldStock,
                latestOrder: cachedData?.latestOrder || null,
                orders: orders,
                promotions: cachedData?.promotions || [],
                products: listProducts,
                listCompetitors: getListCompetitors(),
                mustHaveSKUs: mustHaveSKUs,
                visibility,
              });
            }
          }),
        );
      }
      res[startDate.clone().format('DD/MM/YYYY')] = resData;
      startDate.add(1, 'day');
    }
    return res;
  }

  async syncOfflineData(body: SyncOfflineData, currentUser: User, i18n: I18nContext) {
    const jp = await this.outletJourneyPlanningService.findOne({
      _id: new Types.ObjectId(body.journeyPlanId),
      outlet: new Types.ObjectId(body.outletId),
      saleRep: currentUser._id,
    });
    if (isEmptyObjectOrArray(jp)) {
      throw new HttpException(
        await i18n.translate(`common.not_found`, {
          args: { fieldName: 'outlet journeyPlanning' },
        }),
        HttpStatus.BAD_REQUEST,
      );
    }

    const res = {};
    // sync executeVisit
    if (body.executeVisit) {
      const temp = await this.outletJourneyPlanningService.executeVisitOffline(body.outletId, body.executeVisit, currentUser, body.journeyPlanId);
      res[temp.key] = {
        success: temp.success,
        message: temp.message,
      };
    }

    const arrPromies = [];
    //sync check list
    if (body.checkList) {
      arrPromies.push(
        this.outletService.updateCheckListOffline({
          dto: { checklist: body.checkList },
          i18n,
          outletId: body.outletId,
        }),
      );
    }
    // sync skip outlet
    if (body.skipOutlet) {
      arrPromies.push(
        this.outletJourneyPlanningService.setMissedReasonForOutletOffline(
          {
            evidenceImages: body.skipOutlet.evidenceImages,
            planId: body.journeyPlanId,
            reasonId: body.skipOutlet.reasonId,
          },
          i18n,
        ),
      );
    }
    // sync order
    if (body.orders) {
      if (!currentUser.isTestAccount) {
        arrPromies.push(this.ordersService.createOrderOmsOffline(body.outletId, body.orders, i18n, currentUser));
      } else {
        arrPromies.push(this.mockService.createOrderOmsOffline(body.orders, body.outletId, `${currentUser.firstname} ${currentUser.lastname}`));
      }
    }
    const visitedSteps = body.visitedSteps || [];
    //sync check stock
    if (body.checkStock) {
      visitedSteps.push(JourneyPlanVisitStep.CHECK_STOCK);
      arrPromies.push(
        this.checkStocksService.syncCheckStockDataOffline(
          jp,
          {
            journeyPlanId: body.journeyPlanId,
            outletId: body.outletId,
            listCompetitorsChecked: body.checkStock.listCompetitorsChecked,
            listProductsChecked: body.checkStock.listProductsChecked,
          },
          i18n,
          currentUser,
        ),
      );
    }
    // sync cold stock
    if (body.coldStock) {
      visitedSteps.push(JourneyPlanVisitStep.COLD_STOCK);
      arrPromies.push(
        this.coldStocksService.updateColdStockByJourneyPlanOffline({
          plan: jp,
          dto: body.coldStock,
          i18n,
        }),
      );
    }
    // sync visibility
    if (body.visibility) {
      visitedSteps.push(JourneyPlanVisitStep.DO_VISIBILITY);
      arrPromies.push(
        this.journeyPlanVisibilityService.updateJourneyPlanVisibilityOffline({
          dto: body.visibility,
          plan: jp,
        }),
      );
    }
    // sync visited steps
    if (body.visitedSteps) {
      arrPromies.push(
        this.journeyPlanVisitedStepsService.updateVisitedStepOffline({
          dto: {
            steps: new Set(visitedSteps) as any,
          },
          plan: jp,
        }),
      );
    }

    const temp = await Promise.all(arrPromies);
    if (body.isLastItem) {
      this.omsRepReportsCalculatorsService.executeDSRStatistic({ salesRepId: currentUser.saleRepId }).then().catch();
    }
    for (const iterator of temp) {
      res[iterator.key] = {
        success: iterator.success,
        message: iterator.message,
        items: iterator.items,
      };
    }
    return res;
  }
}
