import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsArray, IsNotEmpty, isNumber, IsNumber, IsOptional, IsString, ValidateNested, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';

export class GetOfflineData {
  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  from: string;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  to: string;
}
