import { ApiProperty } from '@nestjs/swagger';
import { ArrayNotEmpty, IsArray, IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CheckStockData } from 'src/journey-plannings/dtos/check-stock.dto';
import { UpdateColdStockDto } from 'src/journey-plannings/dtos/update-cold-stock.dto';
import { ItemOrderOms } from 'src/orders/dtos/outlet-place-order.dto';
import { UpdateJourneyPlanVisibilityDto } from 'src/journey-plannings/dtos/update-visibility.dto';
import { JourneyPlanVisitStep } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { ExecuteVisitDto } from 'src/sale-rep/dtos';

class CheckStock {
  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CheckStockData)
  listProductsChecked: CheckStockData[];

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CheckStockData)
  listCompetitorsChecked: CheckStockData[];
}

class CheckList {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  label: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  checked: boolean;
}

export class MissedReasonForOutlet {
  @ApiProperty()
  @IsString()
  reasonId: string;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  evidenceImages: Array<string>;
}
export class OmsPlaceOrder {
  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ItemOrderOms)
  items: ItemOrderOms[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  couponCode: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  sourceSystem: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  deliveryInstruction?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  offlineOrderId: string;
}

export class SyncOfflineData {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  outletId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  journeyPlanId: string;

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ExecuteVisitDto)
  executeVisit: ExecuteVisitDto;

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CheckStock)
  checkStock: CheckStock;

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CheckList)
  checkList: CheckList[];

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdateColdStockDto)
  coldStock: UpdateColdStockDto;

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => MissedReasonForOutlet)
  skipOutlet: MissedReasonForOutlet;

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @IsArray()
  @Type(() => OmsPlaceOrder)
  orders: OmsPlaceOrder[];

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdateJourneyPlanVisibilityDto)
  visibility: UpdateJourneyPlanVisibilityDto;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsEnum(JourneyPlanVisitStep, { each: true })
  visitedSteps: JourneyPlanVisitStep[];

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isLastItem: boolean;
}
