import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ArrayNotEmpty, IsArray, IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CheckStockData } from 'src/journey-plannings/dtos/check-stock.dto';
import { UpdateColdStockDto } from 'src/journey-plannings/dtos/update-cold-stock.dto';
import { ItemOrderOms } from 'src/orders/dtos/outlet-place-order.dto';
import { UpdateJourneyPlanVisibilityDto } from 'src/journey-plannings/dtos/update-visibility.dto';
import { JourneyPlanVisitStep } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { ExecuteVisitDto } from 'src/sale-rep/dtos';

class CheckStock {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CheckStockData)
  listProductsChecked: CheckStockData[];

  @ApiModelProperty()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CheckStockData)
  listCompetitorsChecked: CheckStockData[];
}

class CheckList {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  label: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsBoolean()
  checked: boolean;
}

export class MissedReasonForOutlet {
  @ApiModelProperty()
  @IsString()
  reasonId: string;

  @ApiModelProperty()
  @IsOptional()
  @IsArray()
  evidenceImages: Array<string>;
}
export class OmsPlaceOrder {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ItemOrderOms)
  items: ItemOrderOms[];

  @ApiModelProperty()
  @IsOptional()
  @IsString()
  couponCode: string;

  @ApiModelProperty()
  @IsOptional()
  @IsString()
  deliveryInstruction?: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  offlineOrderId: string;
}

export class SyncOfflineData {
  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  outletId: string;

  @ApiModelProperty()
  @IsString()
  @IsNotEmpty()
  journeyPlanId: string;

  @ApiModelProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ExecuteVisitDto)
  executeVisit: ExecuteVisitDto;

  @ApiModelProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CheckStock)
  checkStock: CheckStock;

  @ApiModelProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CheckList)
  checkList: CheckList[];

  @ApiModelProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdateColdStockDto)
  coldStock: UpdateColdStockDto;

  @ApiModelProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => MissedReasonForOutlet)
  skipOutlet: MissedReasonForOutlet;

  @ApiModelProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @IsArray()
  @Type(() => OmsPlaceOrder)
  orders: OmsPlaceOrder[];

  @ApiModelProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdateJourneyPlanVisibilityDto)
  visibility: UpdateJourneyPlanVisibilityDto;

  @ApiModelProperty()
  @IsOptional()
  @IsArray()
  @IsEnum(JourneyPlanVisitStep, { each: true })
  visitedSteps: JourneyPlanVisitStep[];

  @ApiModelProperty()
  @IsOptional()
  @IsBoolean()
  isLastItem: boolean;
}
