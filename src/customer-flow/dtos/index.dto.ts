import { Type } from 'class-transformer';
import { ArrayMaxSize, ArrayMinSize, ArrayNotEmpty, IsArray, IsDateString, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { RequestStatus } from '../enums/index.enum';
import { ApiProperty } from '@nestjs/swagger';

export class GetListRequest {
  @ApiProperty()
  @IsNotEmpty()
  @Type(() => Number)
  offset: number;

  @ApiProperty()
  @IsNotEmpty()
  @Type(() => Number)
  limit: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(RequestStatus)
  status: RequestStatus;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  fromDate: Date;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  toDate: Date;
}

export class LocationRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  addressLine: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  city: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  region: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  street: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  houseNumber: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  latitude: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  longitude: number;
}

export class CreateRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  externalId?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  phoneNumber: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  businessChannel: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  businessSubChannel: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  images: Array<string>;

  @ApiProperty()
  @IsNotEmpty()
  @Type(() => LocationRequest)
  location: LocationRequest;
}
