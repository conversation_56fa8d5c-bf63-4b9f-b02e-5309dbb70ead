import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { Type } from 'class-transformer';
import { ArrayMaxSize, ArrayMinSize, ArrayNotEmpty, IsArray, IsDateString, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { RequestStatus } from '../enums/index.enum';

export class GetListRequest {
  @ApiModelProperty()
  @IsNotEmpty()
  @Type(() => Number)
  offset: number;

  @ApiModelProperty()
  @IsNotEmpty()
  @Type(() => Number)
  limit: number;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsEnum(RequestStatus)
  status: RequestStatus;

  @ApiModelProperty()
  @IsOptional()
  @IsDateString()
  fromDate: Date;

  @ApiModelProperty()
  @IsOptional()
  @IsDateString()
  toDate: Date;
}

export class LocationRequest {
  @ApiModelProperty()
  @IsOptional()
  @IsString()
  addressLine: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  city: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  region: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  street: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  houseNumber: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsNumber()
  latitude: number;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsNumber()
  longitude: number;
}

export class CreateRequest {
  @ApiModelProperty()
  @IsOptional()
  @IsString()
  externalId?: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  phoneNumber: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  businessChannel: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  businessSubChannel: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  images: Array<string>;

  @ApiModelProperty()
  @IsNotEmpty()
  @Type(() => LocationRequest)
  location: LocationRequest;
}
