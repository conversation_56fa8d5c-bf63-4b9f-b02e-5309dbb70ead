import { forwardRef, Module } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';
import { OmsModule } from 'src/oms/oms.module';
import { CustomerFlowController } from './customer-flow.controller';
import { CustomerFlowService } from './services/customer-flow.service';
import { ExternalModule } from 'src/external/external.module';
import { SettingsModule } from 'src/settings/settings.module';
import { DistributorModule } from 'src/distributor/distributor.module';
import { FilesModule } from 'src/files/files.module';
import { MasterDataModule } from '../master-data/master-data.module';
import { OutletsModule } from '../outlets/outlets.module';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    forwardRef(() => UsersModule),
    OmsModule,
    ExternalModule,
    SettingsModule,
    DistributorModule,
    FilesModule,
    forwardRef(() => OutletsModule),
    MasterDataModule,
  ],
  controllers: [CustomerFlowController],
  providers: [CustomerFlowService],
  exports: [CustomerFlowService],
})
export class CustomerFlowModule {}
