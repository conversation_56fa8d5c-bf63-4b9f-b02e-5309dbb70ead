import { Body, Controller, Get, HttpException, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from '../shared/guards/roles.guard';
import { ConstantRoles } from 'src/utils/constants/role';
import { ApiException } from 'src/shared/api-exception.model';
import { ApiResponse } from 'src/shared/response/api-response';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { CreateRequest, GetListRequest } from './dtos/index.dto';
import { CustomerFlowService } from './services/customer-flow.service';

@ApiTags('CustomerFlow')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/customer-flow')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CustomerFlowController {
  constructor(private customerFlowService: CustomerFlowService) {}

  @Get('list-request')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Get list request',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getListRequest(@Query() query: GetListRequest, @CurrentUser() currentUser) {
    try {
      const data = await this.customerFlowService.getListRequest(currentUser.saleRepId, query);
      return new ApiResponse(data);
    } catch (e) {
      throw new HttpException(e.message, e.status || 500);
    }
  }

  @Post('create-request')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'create new request',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async createNewRequest(@Body() body: CreateRequest, @CurrentUser() currentUser) {
    try {
      const res = await this.customerFlowService.createRequest(currentUser._id, body);
      return new ApiResponse(res);
    } catch (e) {
      throw new HttpException(e.message, e.status || 500);
    }
  }

  @Post('update-request')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'update existing request',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async updateExistingRequest(@Body() body: CreateRequest, @CurrentUser() currentUser) {
    try {
      const res = await this.customerFlowService.updateRequest(currentUser._id, body);
      return new ApiResponse(res);
    } catch (e) {
      throw new HttpException(e.message, e.status || 500);
    }
  }
}
