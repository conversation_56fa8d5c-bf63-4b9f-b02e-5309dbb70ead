import { BadRequestException, Injectable } from '@nestjs/common';
import { OmsService } from 'src/external/services/oms.service';
import { CreateRequest, GetListRequest } from '../dtos/index.dto';
import { SettingsService } from 'src/settings/settings.service';
import { DistributorUserRelationService } from 'src/distributor/services';
import { FilesService } from 'src/files/services';
import { FileFeature } from 'src/files/enums/feature.enum';
import { isEmptyObjectOrArray } from '../../utils';

@Injectable()
export class CustomerFlowService {
  constructor(
    private readonly omsService: OmsService,
    private readonly settingsService: SettingsService,
    private readonly distributorUserRelationService: DistributorUserRelationService,
    private readonly filesService: FilesService,
  ) {}

  async getListRequest(salesrep: string, params: GetListRequest) {
    return await this.omsService.getListRequest(salesrep, params);
  }

  async createRequest(salesrep: string, body: CreateRequest) {
    const distributorRelation = await this.distributorUserRelationService.findByUserId(salesrep);
    if (!distributorRelation || !distributorRelation.distributor) {
      throw new BadRequestException('distributor.not_found');
    }
    const depotId = distributorRelation.distributor?.depots?.length ? distributorRelation.distributor?.depots[0].id : null;
    if (!depotId) {
      throw new BadRequestException('depot.depot_notfound');
    }
    await this.filesService.updateByCondition(
      {
        path: { $in: body.images },
      },
      {
        expiredDate: null,
        feature: FileFeature.CUSTOMER_FLOW,
      },
    );

    return await this.omsService.createRequest(salesrep, depotId, body, body.images);
  }

  async updateRequest(salesrep: string, body: CreateRequest) {
    const distributorRelation = await this.distributorUserRelationService.findByUserId(salesrep);
    if (!distributorRelation || !distributorRelation.distributor) {
      throw new BadRequestException('distributor.not_found');
    }
    const depotId = distributorRelation.distributor?.depots?.length ? distributorRelation.distributor?.depots[0].id : null;
    if (!depotId) {
      throw new BadRequestException('depot.depot_notfound');
    }

    if (!isEmptyObjectOrArray(body.images)) {
      await this.filesService.updateByCondition(
        {
          path: { $in: body.images },
        },
        {
          expiredDate: null,
          feature: FileFeature.CUSTOMER_FLOW,
        },
      );
    }

    return await this.omsService.updateRequest(salesrep, depotId, body, body.images);
  }
}
