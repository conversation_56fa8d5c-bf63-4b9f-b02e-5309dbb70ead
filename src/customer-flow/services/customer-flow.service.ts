import { BadRequestException, Injectable } from '@nestjs/common';
import { OmsService } from 'src/external/services/oms.service';
import { CreateRequest, GetListRequest } from '../dtos/index.dto';
import { SettingsService } from 'src/settings/settings.service';
import { DistributorUserRelationService } from 'src/distributor/services';
import { FilesService } from 'src/files/services';
import { FileFeature } from 'src/files/enums/feature.enum';
import { getRandomCode, isEmptyObjectOrArray } from '../../utils';
import { BusinessPartnerRequestService } from '../../master-data/services/business-partner-request.service';
import { BusinessPartnerImageType, BusinessPartnerRelationCommunication, BusinessPartnerStatus, BusinessPartnerType } from '../../master-data/constants/business-partner.enum';
import { I18nContext } from 'nestjs-i18n';
import { BusinessPartnerRequestDto } from '../../master-data/dtos/business-partner-request.dto';
import { OutletsService } from '../../outlets/services/outlets.service';
import { BusinessPartnersContactService } from '../../master-data/services/business-partners-contact.service';
import { UsersService } from '../../users/services/users.service';
import { BusinessPartnerRequest } from '../../master-data/entities/business-partner/business-partner-request.entity';
import { BusinessPartnersService } from '../../master-data/services/business-partners.service';

@Injectable()
export class CustomerFlowService {
  private offset: number;
  constructor(
    private readonly omsService: OmsService,
    private readonly userService: UsersService,
    private readonly settingsService: SettingsService,
    private readonly distributorUserRelationService: DistributorUserRelationService,
    private readonly filesService: FilesService,
    private readonly _outletsService: OutletsService,
    private readonly _businessPartnerRequestService: BusinessPartnerRequestService,
    private readonly _businessPartnersContactService: BusinessPartnersContactService,
    private readonly _businessPartnersService: BusinessPartnersService,
  ) {}

  async getListRequest(salesrep: string, params: GetListRequest) {
    return await this.omsService.getListRequest(salesrep, params);
  }

  async getListCustomerRequest(currentUser: any, params: GetListRequest) {
    const contact = await this._businessPartnersContactService.findOne({ where: { businessPartnerContactKey: currentUser.saleRepId } });
    const requests = await this._businessPartnerRequestService.findAndCount(
      {
        isDeleted: false,
        businessPartnerContact: contact.id,
      },
      params.offset,
      params.limit,
      'createdAt',
      'desc',
    );

    const transformRequestData = await this._businessPartnerRequestService.transformBusinessPartnerRequests(requests[0]);
    return [transformRequestData, requests[1]];

    // return requests.map((item: BusinessPartnerRequest) => {
    //   const { geoGraphicalLocations, customers, details, communications, images } = item;
    //   const channel = !isEmptyObjectOrArray(customers) ? (customers as any)[0].customerChannelCode : '';
    //   const segment = !isEmptyObjectOrArray(customers) ? (customers as any)[0].businessSegment : '';
    //   const customerSalesOrganizations = !isEmptyObjectOrArray(customers) ? (customers as any)[0].customerSalesOrganizations : '';
    //   const sub_channel = !isEmptyObjectOrArray(customerSalesOrganizations) ? (customerSalesOrganizations as any)[0].customerSubChannel : '';
    //   const phone_number = !isEmptyObjectOrArray(communications)
    //     ? (communications as any).find((c: any) => c.communicationType === BusinessPartnerRelationCommunication.TEL)?.communicationValue
    //     : '';
    //   return {
    //     status: item.status || '',
    //     declined_reason: item.businessPartnerNote || '',
    //     name: item.businessPartnerName1 || item.businessPartnerName2 || '',
    //     phone_number,
    //     images: images || [],
    //     channel: {
    //       current_value: channel,
    //       new_value: channel,
    //     },
    //     sub_channel: {
    //       current_value: sub_channel,
    //       new_value: sub_channel,
    //     },
    //     segment,
    //     classifications: null,
    //     created_at: item.createdAt,
    //     id: item.id,
    //     display_id: item?.businessPartnerKey,
    //     external_id: item?.businessPartnerKey,
    //     outlet_id: item?.businessPartnerKey,
    //     location: {
    //       current_value: !isEmptyObjectOrArray(geoGraphicalLocations) ? geoGraphicalLocations[0] : null,
    //       new_value: !isEmptyObjectOrArray(geoGraphicalLocations) ? geoGraphicalLocations[0] : null,
    //     },
    //   };
    // });
  }

  async createRequest(salesrep: string, body: CreateRequest) {
    const distributorRelation = await this.distributorUserRelationService.findByUserId(salesrep);
    if (!distributorRelation || !distributorRelation.distributor) {
      throw new BadRequestException('distributor.not_found');
    }
    const depotId = distributorRelation.distributor?.depots?.length ? distributorRelation.distributor?.depots[0].id : null;
    if (!depotId) {
      throw new BadRequestException('depot.depot_notfound');
    }
    await this.filesService.updateByCondition(
      {
        path: { $in: body.images },
      },
      {
        expiredDate: null,
        feature: FileFeature.CUSTOMER_FLOW,
      },
    );

    return await this.omsService.createRequest(salesrep, depotId, body, body.images);
  }

  async updateRequest(salesrep: string, body: CreateRequest) {
    const distributorRelation = await this.distributorUserRelationService.findByUserId(salesrep);
    if (!distributorRelation || !distributorRelation.distributor) {
      throw new BadRequestException('distributor.not_found');
    }
    const depotId = distributorRelation.distributor?.depots?.length ? distributorRelation.distributor?.depots[0].id : null;
    if (!depotId) {
      throw new BadRequestException('depot.depot_notfound');
    }

    if (!isEmptyObjectOrArray(body.images)) {
      await this.filesService.updateByCondition(
        {
          path: { $in: body.images },
        },
        {
          expiredDate: null,
          feature: FileFeature.CUSTOMER_FLOW,
        },
      );
    }

    return await this.omsService.updateRequest(salesrep, depotId, body, body.images);
  }

  async createNewRequest(currentUser: any, body: CreateRequest, i18n: I18nContext) {
    const outletSaleRepRelations = await this._outletsService.getListOutletsBySalesRepIds([currentUser._id]);
    const contact = await this._businessPartnersContactService.findOne({ where: { businessPartnerContactKey: currentUser.saleRepId } });
    const outlet = body.externalId ? await this._businessPartnersService.findOne({ where: { businessPartnerKey: body.externalId } }) : null;
    await this.filesService.updateByCondition(
      {
        path: { $in: body.images },
      },
      {
        expiredDate: null,
        feature: FileFeature.CUSTOMER_FLOW,
      },
    );
    const externalId = body.externalId || `UCC_${getRandomCode()}`;
    const rowOutlet: BusinessPartnerRequestDto = {
      businessPartnerContact: contact?.id,
      businessPartnerContactAdmin: null,
      businessPartnerDepotKey: outletSaleRepRelations[0]?.depotId,
      businessPartnerNote: '',
      businessPartnerName1: body.name,
      businessPartnerName2: '',
      businessPartnerCurrentKey: outlet?.id,
      businessPartnerKey: externalId,
      businessPartnerDescription: '',
      businessPartnerType: BusinessPartnerType.OUTLET,
      status: BusinessPartnerStatus.PENDING,
      images: body.images?.map((img) => {
        return {
          imagePath: img,
          imageName: '',
          imageStatus: BusinessPartnerStatus.ACTIVE,
          businessPartnerType: BusinessPartnerType.OUTLET,
          server: '',
          imageType: BusinessPartnerImageType.EXTERNAL,
        };
      }),
      geoGraphicalLocations: [
        {
          street: body.location.street,
          houseNumber: body.location.houseNumber,
          region: body.location.region,
          postalCode: '',
          city: body.location.city,
          longitude: body.location.longitude?.toString(),
          latitude: body.location.latitude?.toString(),
          countryKey: '',
          businessPartnerType: BusinessPartnerType.OUTLET,
        },
      ],
      customers: [
        {
          businessPartnerKey: externalId,
          businessPartnerType: BusinessPartnerType.OUTLET,
          customerSalesOrganizations: [
            {
              deliveringSiteKey: '',
              customerSubChannel: body.businessSubChannel,
              customerSubChannelCode: body.businessSubChannel,
            },
          ],
          customerChannel: body.businessChannel,
          customerChannelCode: body.businessChannel,
          businessSegment: '',
          businessSegmentCode: '',
        },
      ],
      details: [
        {
          source: '',
          taxNumber: '',
          businessPartnerType: BusinessPartnerType.OUTLET,
          timezone: '',
          rawAddress: body.location.addressLine,
          customerCommercialHierarchy: {
            territory: '',
          },
        },
      ],
      communications: [
        {
          communicationName: '',
          communicationValue: body.phoneNumber,
          businessPartnerKey: externalId,
          communicationType: BusinessPartnerRelationCommunication.TEL,
          communicationDescription: '',
          communicationStatus: BusinessPartnerStatus.ACTIVE,
          businessPartnerType: BusinessPartnerType.OUTLET,
        },
      ],
    };

    if (body.externalId) {
      const request = await this._businessPartnerRequestService.findOne({ where: { businessPartnerKey: body.externalId } });
      if (!isEmptyObjectOrArray(request)) {
        return await this._businessPartnerRequestService.updateOutlet(request, rowOutlet, i18n);
      }
    }
    return await this._businessPartnerRequestService.createOutlet(rowOutlet, i18n);
  }
}
