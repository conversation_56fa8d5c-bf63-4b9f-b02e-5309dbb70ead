import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNumber, IsString } from 'class-validator';

export class CreateOmsTargetSettingDto {
  @ApiModelProperty()
  @IsString()
  salesRepId: string;

  @ApiModelProperty()
  @IsNumber()
  month: number;

  @ApiModelProperty()
  @IsNumber()
  year: number;

  @ApiModelProperty()
  @IsNumber()
  salesTarget: number;

  @ApiModelProperty()
  @IsNumber()
  maboTarget: number;

  @ApiModelProperty()
  @IsNumber()
  callComplianceRate: number;

  @ApiModelProperty()
  @IsNumber()
  callEffectiveness: number;

  @ApiModelProperty()
  @IsNumber()
  salesVolume: number;

  @ApiModelProperty()
  @IsNumber()
  availability: number;

  @ApiModelProperty()
  @IsNumber()
  visibility: number;
}
