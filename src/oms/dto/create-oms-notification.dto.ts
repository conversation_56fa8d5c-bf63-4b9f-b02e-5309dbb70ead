import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsArray, IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateOmsNotificationDto {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  depotId: string;

  @ApiModelProperty()
  @IsString()
  message: string;

  @ApiModelProperty()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }: TransformFnParams) => value.filter((item, index, self) => self.indexOf(item) === index))
  salesRepIds: string[];

  @ApiModelProperty()
  @IsBoolean()
  toEveryone: boolean;
}
