import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsNumber, IsOptional } from 'class-validator';

export class UpdateOmsTargetSettingDto {
  @ApiModelPropertyOptional()
  @IsOptional()
  @IsNumber()
  salesTarget: number;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsNumber()
  maboTarget: number;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsNumber()
  callComplianceRate: number;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsNumber()
  callEffectiveness: number;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsNumber()
  salesVolume: number;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsNumber()
  availability: number;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsNumber()
  visibility: number;
}
