import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, Types } from 'mongoose';
import { DSRTarget, DSRTargetDocument } from 'src/dsr-targets/schemas';
import { GetOmsTargetSettingsQueries } from '../queries/get-target-settings.query';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { Outlet, OutletDocument } from 'src/outlets/schemas/outlet.schema';
import { SaleRepOutletRelation, SaleRepOutletRelationDocument } from 'src/outlets/schemas/sale-rep-outlet-relation.schema';
import { CreateOmsTargetSettingDto } from '../dto/create-oms-target-setting.dto';
import { UpdateOmsTargetSettingDto } from '../dto/update-oms-target-setting.dto';
import { I18nContext } from 'nestjs-i18n';
import { FilesService } from 'src/files/services';
import { OmsFilesService } from './files.service';
import { ImportOmsTargetSettingsColumns } from 'src/admin/enums';
import { User, UserDocument } from 'src/users/schemas/user.schema';
import { OmsPlansService } from './plans.service';
import { roundToNearestInt } from 'src/utils';

@Injectable()
export class OmsTargetSettingsService {
  constructor(
    @InjectModel(DSRTarget.name) private readonly targetModel: Model<DSRTargetDocument>,
    @InjectModel(Outlet.name) private readonly outletModel: Model<OutletDocument>,
    @InjectModel(SaleRepOutletRelation.name) private readonly salesRepOutletRelationModel: Model<SaleRepOutletRelationDocument>,
    @InjectModel(User.name) private readonly userModel: Model<UserDocument>,
    private readonly filesService: FilesService,
    private readonly omsFilesService: OmsFilesService,
    private readonly omsPlansService: OmsPlansService,
  ) {}

  async getOmsTargetSettings({
    depotId,
    isExport,
    month,
    year,
    offset,
    limit,
    orderBy,
    orderDesc,
  }: GetOmsTargetSettingsQueries &
    PaginationParams &
    OrderParams & {
      isExport?: boolean;
    }) {
    const salesRepIds = await this.getDistributorAndSalesRepByDepotId(depotId);

    const condition: FilterQuery<DSRTargetDocument> = {
      month: +month,
      year: +year,
      saleRep: { $in: salesRepIds },
    };

    const countTargetQuery = this.targetModel.countDocuments(condition);

    const getTargetsQuery = this.targetModel
      .aggregate()
      .match(condition)
      .lookup({
        localField: 'saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'saleRep',
      })
      .unwind({
        path: '$saleRep',
        preserveNullAndEmptyArrays: false,
      })
      .project({
        id: '$_id',
        salesRep: {
          id: '$saleRep.saleRepId',
          name: '$saleRep.username',
        },
        callComplianceRate: {
          $ifNull: ['$callComplianceRate', 0],
        },
        callEffectiveness: {
          $ifNull: ['$callEffectiveness', 0],
        },
        salesVolume: {
          $ifNull: ['$salesVolume', 0],
        },
        salesValue: {
          $ifNull: ['$salesTarget', 0],
        },
        activeSellingOutlet: {
          $ifNull: ['$maboTarget', 0],
        },
        availability: {
          $ifNull: ['$availability', 0],
        },
        visibility: {
          $ifNull: ['$visibility', 0],
        },
        month: '$month',
        year: '$year',
      })
      .sort({
        [orderBy]: orderDesc === 'DESC' ? -1 : 1,
      });

    if (!isExport) {
      getTargetsQuery.skip(offset ? +offset : 0).limit(limit ? +limit : 10);
    }

    const [totalItem, targets] = await Promise.all([countTargetQuery, getTargetsQuery]);

    return {
      totalItem,
      targets,
    };
  }

  async createOmsTargetSetting(dto: CreateOmsTargetSettingDto) {
    const data = await this.targetModel
      .findOneAndUpdate(
        {
          saleRep: new Types.ObjectId(dto.salesRepId),
          month: dto.month,
          year: dto.year,
        },
        dto,
        {
          new: true,
          upsert: true,
        },
      )
      .populate('saleRep');

    return this.transformTargetSetting(data);
  }

  async getAverageOmsTargetSettings(salesRepIds: string[], month: number, year: number) {
    const data = await this.targetModel
      .find({
        saleRep: { $in: salesRepIds.map((id) => new Types.ObjectId(id)) },
        month,
        year,
      })
      .populate('saleRep');

    const saleRepTargetSettings = (data || []).map((ts) => this.transformTargetSetting(ts));
    const numberOfTargetSettings = saleRepTargetSettings.length;
    if (!numberOfTargetSettings) {
      return {
        availabilityTarget: 0,
        visibilityTarget: 0,
        activeSellingOutletTarget: 0,
        callComplianceRateTarget: 0,
        callEffectivenessTarget: 0,
        salesValueTarget: 0,
        salesVolumeTarget: 0,
      };
    }
    const { availabilityTarget, visibilityTarget, activeSellingOutletTarget, callComplianceRateTarget, callEffectivenessTarget, salesValueTarget, salesVolumeTarget } =
      saleRepTargetSettings.reduce(
        (acc, targetSetting) => {
          acc.activeSellingOutletTarget += Number(targetSetting.activeSellingOutlet) || 0;
          acc.availabilityTarget += Number(targetSetting.availability) || 0;
          acc.callComplianceRateTarget += Number(targetSetting.callComplianceRate) || 0;
          acc.callEffectivenessTarget += Number(targetSetting.callEffectiveness) || 0;
          acc.salesValueTarget += Number(targetSetting.salesValue) || 0;
          acc.salesVolumeTarget += Number(targetSetting.salesVolume) || 0;
          acc.visibilityTarget += Number(targetSetting.visibility) || 0;
          return acc;
        },
        {
          availabilityTarget: 0,
          visibilityTarget: 0,
          activeSellingOutletTarget: 0,
          callComplianceRateTarget: 0,
          callEffectivenessTarget: 0,
          salesValueTarget: 0,
          salesVolumeTarget: 0,
        },
      );
    return {
      availabilityTarget: roundToNearestInt(availabilityTarget / numberOfTargetSettings),
      visibilityTarget: roundToNearestInt(visibilityTarget / numberOfTargetSettings),
      activeSellingOutletTarget: roundToNearestInt(activeSellingOutletTarget / numberOfTargetSettings),
      callComplianceRateTarget: roundToNearestInt(callComplianceRateTarget / numberOfTargetSettings),
      callEffectivenessTarget: roundToNearestInt(callEffectivenessTarget / numberOfTargetSettings),
      salesValueTarget: roundToNearestInt(salesValueTarget / numberOfTargetSettings),
      salesVolumeTarget: roundToNearestInt(salesVolumeTarget / numberOfTargetSettings),
    };
  }

  async updateOmsTargetSetting(targetId: string, dto: UpdateOmsTargetSettingDto) {
    const data = await this.targetModel.findByIdAndUpdate(targetId, dto, { new: true }).populate('saleRep');

    return this.transformTargetSetting(data);
  }

  async getTemplate(i18n: I18nContext) {
    const xlsxData = [
      {
        [i18n.translate(`importExport.targetSetting.salesRepId`)]: 'Rep _ID_1',
        [i18n.translate(`importExport.targetSetting.month`)]: 6,
        [i18n.translate(`importExport.targetSetting.year`)]: 2024,
        [i18n.translate(`importExport.targetSetting.callComplianceRate`)]: 100,
        [i18n.translate(`importExport.targetSetting.callEffectiveness`)]: 100,
        [i18n.translate(`importExport.targetSetting.salesVolume`)]: 1000,
        [i18n.translate(`importExport.targetSetting.salesValue`)]: 1000,
        [i18n.translate(`importExport.targetSetting.activeSellingOutlet`)]: 100,
        [i18n.translate(`importExport.targetSetting.availability`)]: 100,
        [i18n.translate(`importExport.targetSetting.visibility`)]: 100,
      },
    ];

    const template = await this.filesService.exportXLSXFile('Target_Setting_Template', xlsxData, 'Target Setting', null);
    return template;
  }

  async exportOmsTargetSettings({ depotId, month, year, orderBy, orderDesc, i18n }: GetOmsTargetSettingsQueries & OrderParams & { i18n: I18nContext }) {
    const { targets } = await this.getOmsTargetSettings({
      depotId,
      year,
      month,
      orderBy,
      orderDesc,
      isExport: true,
    });

    let xlsxData = [
      {
        [i18n.translate(`importExport.targetSetting.salesRepId`)]: '',
        [i18n.translate(`importExport.targetSetting.salesRepName`)]: '',
        [i18n.translate(`importExport.targetSetting.month`)]: '',
        [i18n.translate(`importExport.targetSetting.year`)]: '',
        [i18n.translate(`importExport.targetSetting.callComplianceRate`)]: '',
        [i18n.translate(`importExport.targetSetting.callEffectiveness`)]: '',
        [i18n.translate(`importExport.targetSetting.salesVolume`)]: '',
        [i18n.translate(`importExport.targetSetting.salesValue`)]: '',
        [i18n.translate(`importExport.targetSetting.activeSellingOutlet`)]: '',
        [i18n.translate(`importExport.targetSetting.availability`)]: '',
        [i18n.translate(`importExport.targetSetting.visibility`)]: '',
      },
    ];

    if (targets.length) {
      xlsxData = targets.map((target) => ({
        [i18n.translate(`importExport.targetSetting.salesRepId`)]: target.salesRep.id,
        [i18n.translate(`importExport.targetSetting.salesRepName`)]: target.salesRep.name,
        [i18n.translate(`importExport.targetSetting.month`)]: target.month,
        [i18n.translate(`importExport.targetSetting.year`)]: target.year,
        [i18n.translate(`importExport.targetSetting.callComplianceRate`)]: target.callComplianceRate,
        [i18n.translate(`importExport.targetSetting.callEffectiveness`)]: target.callEffectiveness,
        [i18n.translate(`importExport.targetSetting.salesVolume`)]: target.salesVolume,
        [i18n.translate(`importExport.targetSetting.salesValue`)]: target.salesValue,
        [i18n.translate(`importExport.targetSetting.activeSellingOutlet`)]: target.activeSellingOutlet,
        [i18n.translate(`importExport.targetSetting.availability`)]: target.availability,
        [i18n.translate(`importExport.targetSetting.visibility`)]: target.visibility,
      }));
    }

    const result = await this.filesService.exportXLSXFile(`Target_Setting_Depot_${depotId}`, xlsxData, 'Target setting', null);
    return result;
  }

  async importTargetSettings({ file, i18n }: { file: Express.Multer.File; i18n: I18nContext }) {
    const excelData = await this.omsFilesService.handleUploadingFile(
      file,
      ImportOmsTargetSettingsColumns.map((e) => i18n.translate(`importExport.${e}`)),
      ImportOmsTargetSettingsColumns,
      i18n,
    );

    const numberedData = excelData.map((item, index) => ({
      row: index + 2,
      targetData: {
        salesRepId: item['targetSetting.salesRepId'],
        month: item['targetSetting.month'],
        year: item['targetSetting.year'],
        callComplianceRate: item['targetSetting.callComplianceRate'],
        callEffectiveness: item['targetSetting.callEffectiveness'],
        salesVolume: item['targetSetting.salesVolume'],
        salesValue: item['targetSetting.salesValue'],
        activeSellingOutlet: item['targetSetting.activeSellingOutlet'],
        availability: item['targetSetting.availability'],
        visibility: item['targetSetting.visibility'],
      },
    }));

    const groups = this.omsFilesService.splitData({ data: numberedData, size: 100 });

    const checkedData = await Promise.all(
      groups.map(async (group) => {
        const data = await this.validateTargetSettingData({ data: group, i18n });
        return data;
      }),
    );
    const { failure, validatedData } = checkedData.reduce(
      (pre, curr) => {
        const { failure, validatedData } = curr;
        const formattedFailure = Object.keys(failure).reduce((pre, key) => {
          return [...pre, { row: Number(key), reasons: failure[key] }];
        }, []);

        return {
          failure: [...pre.failure, ...formattedFailure],
          validatedData: [...pre.validatedData, ...validatedData],
        };
      },
      { failure: [], validatedData: [] } as {
        failure: Array<{
          row: number;
          reasons: string[];
        }>;
        validatedData: any[];
      },
    );

    if (failure.length) {
      return {
        failure,
      };
    }

    const existedTargets = await this.targetModel.find({
      $or: validatedData.map((item) => ({
        saleRep: new Types.ObjectId(item.saleRep),
        month: item.month,
        year: item.year,
      })),
    });

    const { insert, update } = await validatedData.reduce(
      (pre, curr) => {
        const existedTarget = existedTargets.find((item) => String(item.saleRep) === String(curr.saleRep) && +curr.month === +item.month && +curr.year === +item.year);

        if (existedTarget) {
          return {
            ...pre,
            update: [...pre.update, curr],
          };
        }

        return {
          ...pre,
          insert: [...pre.update, curr],
        };
      },
      {
        insert: [],
        update: [],
      },
    );

    await Promise.all([
      this.targetModel.create(insert),
      ...update.map(async (item) => {
        const updated = await this.targetModel.updateOne(
          {
            saleRep: item.saleRep,
            year: item.year,
            month: item.month,
          },
          item,
        );

        return updated;
      }),
    ]);

    return { failure: [] };
  }

  private async validateTargetSettingData({ data, i18n }: { data: any[]; i18n: I18nContext }) {
    // find outlets by all ucc of rows
    const salesRepIds = data.reduce((pre, curr) => {
      const { targetData } = curr;

      const salesRepId = targetData.salesRepId;

      if (!salesRepId?.trim()) {
        return pre;
      }

      return Array.from(new Set([...pre, salesRepId]));
    }, [] as string[]);

    const salesReps = await this.userModel.find({
      saleRepId: { $in: salesRepIds },
    });

    const salesRepIdMap = salesReps.reduce((pre, salesRep) => {
      return {
        ...pre,
        [salesRep.saleRepId]: salesRep,
      };
    }, {});

    const failure: Record<number, string[]> = {};
    const validatedData = [];

    for (const { row, targetData } of data) {
      const { salesRepId, month, year, callComplianceRate, callEffectiveness, salesVolume, salesValue, activeSellingOutlet, availability, visibility } = targetData;
      const reasons: string[] = [];

      if (!salesRepId) {
        reasons.push(i18n.translate('oms.targetSetting.error.invalidSalesRepId'));
      } else {
        const salesRep = salesRepIdMap[salesRepId];

        if (!salesRep) {
          reasons.push(i18n.translate('oms.targetSetting.error.salesRepNotFound'));
        }
      }

      if (Number(month) < 0) {
        reasons.push(i18n.translate('oms.targetSetting.error.invalidMonth'));
      }

      if (Number(year) < 0) {
        reasons.push(i18n.translate('oms.targetSetting.error.invalidYear'));
      }

      if (Number(callComplianceRate) < 0) {
        reasons.push(i18n.translate('oms.targetSetting.error.invalidCallComplianceRate'));
      }

      if (Number(callEffectiveness) < 0) {
        reasons.push(i18n.translate('oms.targetSetting.error.invalidCallEffectiveness'));
      }

      if (Number(salesVolume) < 0) {
        reasons.push(i18n.translate('oms.targetSetting.error.invalidSalesVolume'));
      }

      if (Number(salesValue) < 0) {
        reasons.push(i18n.translate('oms.targetSetting.error.invalidSalesValue'));
      }

      if (Number(activeSellingOutlet) < 0) {
        reasons.push(i18n.translate('oms.targetSetting.error.invalidActiveSellingOutlet'));
      }

      if (Number(availability) < 0) {
        reasons.push(i18n.translate('oms.targetSetting.error.invalidAvailability'));
      }

      if (Number(visibility) < 0) {
        reasons.push(i18n.translate('oms.targetSetting.error.invalidVisibility'));
      }

      if (reasons.length) {
        failure[row] = reasons;
        continue;
      }

      validatedData.push({
        saleRep: salesRepIdMap[salesRepId]._id,
        month: Number(month),
        year: Number(year),
        callComplianceRate: Number(callComplianceRate),
        callEffectiveness: Number(callEffectiveness),
        salesVolume: Number(salesVolume),
        salesTarget: Number(salesValue),
        maboTarget: Number(activeSellingOutlet),
        availability: Number(availability),
        visibility: Number(visibility),
      });
    }

    return {
      failure,
      validatedData,
    };
  }

  private transformTargetSetting(targetSetting: DSRTarget) {
    return {
      id: targetSetting._id,
      salesRep: {
        id: targetSetting.saleRep.saleRepId,
        name: targetSetting.saleRep.username,
      },
      callComplianceRate: targetSetting.callComplianceRate,
      callEffectiveness: targetSetting.callEffectiveness,
      salesVolume: targetSetting.salesVolume,
      salesValue: targetSetting.salesTarget,
      activeSellingOutlet: targetSetting.maboTarget,
      availability: targetSetting.availability,
      visibility: targetSetting.visibility,
    };
  }

  private async getSalesRepIdsByDepotId(depotId: string) {
    const outlets = await this.outletModel.find({ depotId }).select('_id');
    const outletIds = outlets.map((outlet) => new Types.ObjectId(outlet._id));

    const salesRepOutletRelations = await this.salesRepOutletRelationModel
      .find({
        disconnected: false,
        outlet: { $in: outletIds },
      })
      .select('saleRep');

    return salesRepOutletRelations.map((relation) => new Types.ObjectId(String(relation.saleRep)));
  }

  public async getDistributorAndSalesRepByDepotId(depotId: string) {
    const distributorAndSalesRep = await this.omsPlansService.getDistributorAndSalesRepByDepotId(depotId);
    return distributorAndSalesRep?.users;
  }
}
