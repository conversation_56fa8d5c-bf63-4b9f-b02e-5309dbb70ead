import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { OutletStatus } from 'src/outlets/enums/outlet-status.enum';
import { Outlet, OutletDocument } from 'src/outlets/schemas/outlet.schema';
import { SaleRepExecutionVisibility, SaleRepExecutionVisibilityDocument, SaleRepStatistic } from 'src/sale-rep/schemas';
import { SaleRepStatisticService } from 'src/sale-rep/services';
import { User, UserDocument } from 'src/users/schemas/user.schema';
import { printLog, sleep } from 'src/utils';
import { SalesRepExternalStatisticsService } from './sales-rep-external-statistics.service';
import { OutletsService } from '../../outlets/services/outlets.service';

@Injectable()
export class OmsRepReportsCalculatorsService {
  constructor(
    private readonly saleRepStatisticService: SaleRepStatisticService,
    @InjectModel(OutletJourneyPlanning.name) private readonly planModel: Model<OutletJourneyPlanningDocument>,
    @InjectModel(SaleRepExecutionVisibility.name) private readonly visibilityModel: Model<SaleRepExecutionVisibilityDocument>,
    @InjectModel(Outlet.name) private readonly outletModel: Model<OutletDocument>,
    @InjectModel(User.name) private readonly userModel: Model<UserDocument>,
    private readonly salesRepExternalStatisticsService: SalesRepExternalStatisticsService,
    private readonly _outletsService: OutletsService,
  ) {
    // this.executeDSRStatistic({
    //   salesRepId: 'Lem 740',
    // })
    //   .then()
    //   .catch();
  }

  async calculateCallComplianceRate({ salesRepId, date }: { salesRepId: string; date: Date }) {
    try {
      const { from, to } = this.transformTimeRange(date);
      const plans = await this.planModel
        .find({
          saleRep: new Types.ObjectId(salesRepId),
          $or: [
            { day: { $gte: from, $lte: to }, rescheduled: false },
            { rescheduledDay: { $gt: from, $lte: to }, rescheduled: true },
          ],
        })
        .select('_id visitStatus  missedReason cancel')
        .populate('missedReason');
      const plannedCall = plans.length;
      const completedCalls = plans.filter((plan) => plan.visitStatus === VisitStatus.COMPLETED).length;
      const uncontrollableMissCalls = plans.filter((plan) => plan.cancel || (plan.missedReason && !plan.missedReason.controllable)).length;
      return {
        current: completedCalls,
        target: plannedCall - uncontrollableMissCalls,
      };
    } catch (error) {
      printLog('Error when calculating call compliance rate', error);
      return {
        current: 0,
        target: 0,
      };
    }
  }

  async calculateActiveSellingOutlet({ salesRepId, date }: { salesRepId: string; date: Date }) {
    try {
      const { from, to } = this.transformTimeRange(date);
      const plans = await this.planModel
        .find({
          saleRep: new Types.ObjectId(salesRepId),
          $or: [
            { day: { $gte: from, $lte: to }, rescheduled: false },
            { rescheduledDay: { $gt: from, $lte: to }, rescheduled: true },
          ],
        })
        .select('_id outlet');
      const outletIds = Array.from(new Set(plans.map((plan) => String(plan.outlet))));
      const activeOutlets = await this.outletModel.find({ _id: { $in: outletIds }, status: OutletStatus.ACTIVE });
      const activeOutletIds = activeOutlets.map((outlet) => String(outlet._id));
      const activePlans = plans.filter((plan) => activeOutletIds.includes(String(plan.outlet)));
      const uniqueOutletIds = Array.from(new Set(activePlans.map((plan) => String(plan.outlet))));

      return {
        target: uniqueOutletIds.length,
      };
    } catch (error) {
      printLog('Error when calculating active selling outlet', error);
      return {
        current: 0,
        target: 0,
      };
    }
  }

  async calculateCallEffectiveness({ salesRepId, date }: { salesRepId: string; date: Date }) {
    try {
      const { from, to } = this.transformTimeRange(date);
      const plans = await this.planModel
        .find({
          saleRep: new Types.ObjectId(salesRepId),
          $or: [
            { day: { $gte: from, $lte: to }, rescheduled: false },
            { rescheduledDay: { $gte: from, $lte: to }, rescheduled: true },
          ],
          visitStatus: VisitStatus.COMPLETED,
        })
        .select('_id visitStatus visitedDay hasOrder');
      const visitedPlans = plans.filter((plan) => plan.visitStatus === VisitStatus.COMPLETED && !!plan.visitedDay);
      const callEffectiveness = visitedPlans.filter((plan) => plan.hasOrder).length;
      return {
        current: callEffectiveness,
        target: visitedPlans.length,
      };
    } catch (error) {
      printLog('Error when calculating call effectiveness', error);
      return {
        current: 0,
        target: 0,
      };
    }
  }

  async calculateVisibility({ salesRepId, date }: { salesRepId: string; date: Date }) {
    try {
      const { from, to } = this.transformTimeRange(date);
      const plans = await this.planModel
        .find({
          saleRep: new Types.ObjectId(salesRepId),
          $or: [
            { day: { $gte: from, $lte: to }, rescheduled: false },
            { rescheduledDay: { $gt: from, $lte: to }, rescheduled: true },
          ],
          visitStatus: VisitStatus.COMPLETED,
        })
        .select('_id');
      const visitedPlanIds = plans.map((plan) => plan._id);
      const visibilities = await this.visibilityModel.find({
        journeyPlan: { $in: visitedPlanIds },
      });

      const { current, target } = visibilities.reduce(
        (initial, visibility) => {
          const numberOfTask = Object.values(visibility.taskProps || {}).length || Object.keys(visibility.tasks || {}).length;
          // If there's visibility, that mean the plan is visited, no need to check visit status here
          const numberOfDoneTask =
            Object.values(visibility.taskProps || {}).filter((value: any) => value?.quantity > 0 || value?.imageIds?.length > 0).length ||
            Object.values(visibility.tasks || {}).filter((value: any) => value?.length > 0).length;
          return {
            current: initial.current + numberOfDoneTask,
            target: initial.target + numberOfTask,
          };
        },
        {
          current: 0,
          target: 0,
        } as { current: number; target: number },
      );

      return {
        current,
        target,
      };
    } catch (error) {
      printLog('Error when calculating visibility', error);
      return {
        current: 0,
        target: 0,
      };
    }
  }

  async calculateAvailability({ salesRepId, date }: { salesRepId: string; date: Date }) {
    try {
      const { from, to } = this.transformTimeRange(date);
      const visitedPlans = await this.planModel
        .find({
          saleRep: new Types.ObjectId(salesRepId),
          $or: [
            { day: { $gte: from, $lte: to }, rescheduled: false },
            { rescheduledDay: { $gt: from, $lte: to }, rescheduled: true },
          ],
          visitStatus: VisitStatus.COMPLETED,
        })
        .select('_id outlet checkStock')
        .populate(['outlet']);

      const { current, target } = visitedPlans.reduce(
        (initial, plan) => {
          const target = initial.target + (plan.outlet.totalProductAssigned || 0);
          return {
            target,
            current: initial.current + (plan.checkStock?.listProductsChecked || []).filter((product) => product.check_stock_quantity > 0 || product?.selling_price > 0).length,
          };
        },
        {
          current: 0,
          target: 0,
        },
      );
      return {
        current,
        target,
      };
    } catch (error) {
      printLog('Error when calculating availability', error);
      return {
        current: 0,
        target: 0,
      };
    }
  }

  private transformTimeRange(date: Date) {
    const from = moment(date).tz(process.env.TZ).startOf('month').startOf('date');
    const to = moment(date).tz(process.env.TZ).endOf('month').endOf('date');

    return {
      from,
      to,
    };
  }

  async executeDSRStatistic({ salesRepId, forceUpdate }: { salesRepId?: string; forceUpdate?: boolean }) {
    const match: any = {
      saleRepId: { $ne: null },
      isActive: true,
      mobilePhone: { $nin: ['0', ''] },
      password: { $ne: null },
    };
    if (salesRepId) {
      match.saleRepId = salesRepId;
    }

    const currentDate = moment().startOf('d').toDate();
    const salesReps = await this.userModel
      .aggregate()
      .match(match)
      .project({
        _id: 1,
      })
      .exec();
    const outletSaleRepRelations = await this._outletsService.getListOutletsBySalesRepIds(salesReps.map((sale) => String(sale._id)));
    for (const depot of outletSaleRepRelations) {
      const salesRepsId = depot.salesRepIds;
      while (salesRepsId.length) {
        const temp = salesRepsId.splice(0, 10);
        // get statistic data of sales rep
        await Promise.all([
          temp.map(async (salesRepId) => {
            // prepare cache data for promises all
            const data = await Promise.all([
              // Call compliance rate
              this.calculateCallComplianceRate({ salesRepId, date: currentDate }),
              // // Call effectiveness
              this.calculateCallEffectiveness({ salesRepId, date: currentDate }),
              // Active selling outlet
              this.calculateActiveSellingOutlet({ salesRepId, date: currentDate }),
              // // Availability
              this.calculateAvailability({ salesRepId, date: currentDate }),
              // // Visibility
              this.calculateVisibility({ salesRepId, date: currentDate }),
            ]);
            const [
              { current: cpsrValue, target: cpsrTarget },
              { current: callEffectivenessValue, target: callEffectivenessTarget },
              { target: maboTarget },
              { current: availabilityValue, target: availabilityTarget },
              { current: visibilityValue, target: visibilityTarget },
            ] = data;
            const statisticData: Partial<SaleRepStatistic> = {
              // Call compliance rate
              cpsrValue,
              cpsrTarget,
              // CallEffectiveness
              callEffectivenessValue,
              callEffectivenessTarget,
              // Active selling outlet
              maboTarget,
              // Availability
              avaibilityValue: availabilityValue,
              avaibilityTarget: availabilityTarget,
              availabilityValue,
              availabilityTarget,
              // Visibility
              visibilityValue,
              visibilityTarget,
            };
            await this.saleRepStatisticService.updateOrCreate(
              {
                statisticDay: moment(currentDate).tz(process.env.TZ).endOf('d').toDate(),
                saleRep: new Types.ObjectId(salesRepId),
              },
              statisticData,
            );
          }),
          await this.salesRepExternalStatisticsService.syncOmsMetrics({ salesRepIds: temp, depotId: depot.depotId }),
        ]);
        await sleep(1000);
      }
    }
  }
}
