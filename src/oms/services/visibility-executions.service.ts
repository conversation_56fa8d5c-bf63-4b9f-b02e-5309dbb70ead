import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { FilterQuery, Model, Types } from 'mongoose';
import { I18nContext } from 'nestjs-i18n';
import { ImportExportVisibilityExecutionColumns } from 'src/admin/enums';
import { FilesService } from 'src/files/services';
import { CreateVisibilityExecutionDto } from 'src/journey-plannings/dtos/create-visibility-execution.dto';
import { VisibilityExecution, VisibilityExecutionDocument, VisibilityExecutionStatus } from 'src/journey-plannings/schemas/visibility-execution.schema';
import { Outlet, OutletDocument } from 'src/outlets/schemas/outlet.schema';
import { normalizeQueryHelper } from 'src/shared/helpers';
import { OmsFilesService } from './files.service';
import { SettingsService } from 'src/settings/settings.service';

@Injectable()
export class OmsVisibilityExecutionsService {
  constructor(
    @InjectModel(VisibilityExecution.name)
    private readonly visibilityExecutionModel: Model<VisibilityExecutionDocument>,
    @InjectModel(Outlet.name)
    private readonly outletModel: Model<OutletDocument>,
    private readonly filesService: FilesService,
    private readonly omsFilesService: OmsFilesService,
    private readonly settingsService: SettingsService,
  ) {}

  async countVisibilityExecutionsByStatus(condition: FilterQuery<VisibilityExecutionDocument>) {
    const [active, inactive] = await Promise.all([
      this.visibilityExecutionModel.countDocuments({ ...condition, status: VisibilityExecutionStatus.ACTIVE }),
      this.visibilityExecutionModel.countDocuments({ ...condition, status: VisibilityExecutionStatus.INACTIVE }),
    ]);

    return { active, inactive };
  }

  async getVisibilityExecutions({
    search,
    depotId,
    status,
    limit,
    offset,
    orderBy,
    orderDesc,
  }: {
    search?: string;
    depotId: string;
    status: VisibilityExecutionStatus;
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderDesc?: string;
  }) {
    const condition: FilterQuery<VisibilityExecutionDocument> = {
      status,
      depotId,
    };

    if (search?.trim()) {
      condition['$or'] = [
        {
          name: { $regex: new RegExp(`^.*${normalizeQueryHelper(search.trim())}.*$`, 'i') },
        },
        {
          subHeading: { $regex: new RegExp(`^.*${normalizeQueryHelper(search.trim())}.*$`, 'i') },
        },
      ];
    }

    const countQuery = this.visibilityExecutionModel.countDocuments(condition);

    if (!orderBy || orderBy === 'name') {
      const [tasks, totalItem, counter] = await Promise.all([
        this.visibilityExecutionModel
          .find(condition)
          .sort(
            orderBy
              ? {
                  name: orderDesc === 'DESC' ? -1 : 1,
                  subHeading: orderDesc === 'DESC' ? -1 : 1,
                  startDate: orderDesc === 'DESC' ? -1 : 1,
                  endDate: orderDesc === 'DESC' ? -1 : 1,
                }
              : { updatedAt: -1, createdAt: -1 },
          )
          .skip(offset ? +offset : 0)
          .limit(limit ? +limit : 10),
        countQuery,
        this.countVisibilityExecutionsByStatus(condition),
      ]);

      return {
        totalItem,
        tasks: tasks.map(this.transformTask),
        counter,
      };
    }

    const {
      map: { modernOnTradeChannels, modernOffTradeChannels, traditionalOnTradeChannels, traditionalOffTradeChannels },
    } = await this.settingsService.getOutletChannels();

    const dataQuery = this.visibilityExecutionModel
      .aggregate()
      .match(condition)
      .addFields({
        outletIds: '$outlets',
      })
      .lookup({
        localField: 'outlets',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlets',
      })
      .unwind({
        path: '$outlets',
        preserveNullAndEmptyArrays: false,
      })
      .group({
        _id: {
          _id: '$_id',
          name: '$name',
          subHeading: '$subHeading',
          status: '$status',
          startDate: '$startDate',
          endDate: '$endDate',
          outletIds: '$outletIds',
          createdAt: '$createdAt',
          updatedAt: '$updatedAt',
        },
        modernOnTrade: {
          $sum: {
            $cond: [{ $in: [{ $toLower: '$outlets.channel' }, modernOnTradeChannels.map((channel) => channel.toLowerCase())] }, 1, 0],
          },
        },
        modernOffTrade: {
          $sum: {
            $cond: [{ $in: [{ $toLower: '$outlets.channel' }, modernOffTradeChannels.map((channel) => channel.toLowerCase())] }, 1, 0],
          },
        },
        traditionalOnTrade: {
          $sum: {
            $cond: [{ $in: [{ $toLower: '$outlets.channel' }, traditionalOnTradeChannels.map((channel) => channel.toLowerCase())] }, 1, 0],
          },
        },
        traditionalOffTrade: {
          $sum: {
            $cond: [{ $in: [{ $toLower: '$outlets.channel' }, traditionalOffTradeChannels.map((channel) => channel.toLowerCase())] }, 1, 0],
          },
        },
      })
      .sort({
        [orderBy]: orderDesc === 'DESC' ? -1 : 1,
      })
      .skip(offset ? +offset : 0)
      .limit(limit ? +limit : 10);

    const [data, total, counter] = await Promise.all([dataQuery, countQuery, this.countVisibilityExecutionsByStatus(condition)]);

    return {
      totalItem: total,
      tasks: data.map((item) => ({
        id: item._id._id,
        name: item._id.name,
        subHeading: item._id.subHeading,
        status: item._id.status,
        startDate: item._id.startDate,
        endDate: item._id.endDate,
        createdAt: item._id.createdAt,
        updatedAt: item._id.updatedAt,
        outletIds: item._id.outletIds,
      })),
      counter,
    };
  }

  async createVisibilityExecution({ dto: { outletIds, ...dto }, i18n }: { dto: CreateVisibilityExecutionDto; i18n: I18nContext }) {
    const startDate = moment(dto.startDate).tz(process.env.TZ);
    const endDate = moment(dto.endDate).tz(process.env.TZ);
    this.assertDateRange({ startDate, endDate, i18n });

    const visibilityExecutions = await this.visibilityExecutionModel.create(
      [
        {
          ...dto,
          startDate: startDate.startOf('date'),
          endDate: endDate.endOf('date'),
          outlets: outletIds.map((id) => new Types.ObjectId(id)),
        },
      ],
      {},
      {
        new: true,
      },
    );

    return this.transformTask(visibilityExecutions[0]);
  }

  async updateVisibilityExecution({ id, dto: { outletIds, ...dto }, i18n }: { id: string; dto: CreateVisibilityExecutionDto; i18n: I18nContext }) {
    const startDate = moment(dto.startDate).tz(process.env.TZ);
    const endDate = moment(dto.endDate).tz(process.env.TZ);
    this.assertDateRange({ startDate, endDate, i18n });

    const visibilityExecution = await this.visibilityExecutionModel.findByIdAndUpdate(
      id,
      {
        ...dto,
        startDate: moment(dto.startDate).tz(process.env.TZ).startOf('date'),
        endDate: moment(dto.endDate).tz(process.env.TZ).endOf('date'),
        outlets: outletIds.map((id) => new Types.ObjectId(id)),
      },
      {
        new: true,
        upsert: true,
      },
    );

    return this.transformTask(visibilityExecution);
  }

  async getTemplate({ i18n }: { i18n: I18nContext }) {
    const xlsxData = [
      {
        [i18n.translate(`importExport.taskName`)]: 'Task 1',
        [i18n.translate(`importExport.taskSubHeading`)]: 'Sub-heading 1',
        [i18n.translate(`importExport.status`)]: i18n.translate(`importExport.ACTIVE`),
        [i18n.translate(`importExport.startDate`)]: '01/01/2024',
        [i18n.translate(`importExport.endDate`)]: '01/02/2025',
        [i18n.translate(`importExport.ucc`)]: 'ucc1, ucc2, ucc3',
      },
      {
        [i18n.translate(`importExport.taskName`)]: 'Task 2',
        [i18n.translate(`importExport.taskSubHeading`)]: 'Sub-heading 2',
        [i18n.translate(`importExport.status`)]: i18n.translate(`importExport.INACTIVE`),
        [i18n.translate(`importExport.startDate`)]: '01/01/2024',
        [i18n.translate(`importExport.endDate`)]: '01/02/2025',
        [i18n.translate(`importExport.ucc`)]: 'ucc1, ucc2',
      },
    ];

    const template = await this.filesService.exportXLSXFile('Visibility_Execution_Template', xlsxData, 'Visibility Execution', null);
    return template;
  }

  async uploadTasks({ depotId, file, i18n }: { depotId: string; file: Express.Multer.File; i18n: I18nContext }) {
    const excelData = await this.omsFilesService.handleUploadingFile(
      file,
      ImportExportVisibilityExecutionColumns.map((e) => i18n.translate(`importExport.${e}`)),
      ImportExportVisibilityExecutionColumns,
      i18n,
    );

    const numberedData = excelData.map((item, index) => ({ row: index + 2, taskData: item }));
    const groups = this.omsFilesService.splitData({ data: numberedData, size: 20 });

    const checkedData = await Promise.all(
      groups.map(async (group) => {
        const data = await this.validateTasksData({ data: group, i18n });
        return data;
      }),
    );
    const { failure, validatedData } = checkedData.reduce(
      (pre, curr) => {
        const { failure, validatedData } = curr;
        const formattedFailure = Object.keys(failure).reduce((pre, key) => {
          return [...pre, { row: Number(key), reasons: failure[key] }];
        }, []);

        return {
          failure: [...pre.failure, ...formattedFailure],
          validatedData: [...pre.validatedData, ...validatedData],
        };
      },
      { failure: [], validatedData: [] } as {
        failure: Array<{
          row: number;
          reasons: string[];
        }>;
        validatedData: any[];
      },
    );

    if (failure.length) {
      return {
        failure,
      };
    }

    const session = await this.visibilityExecutionModel.startSession();
    try {
      await session.withTransaction(async () => {
        await this.visibilityExecutionModel.updateMany(
          { status: VisibilityExecutionStatus.ACTIVE },
          {
            $set: {
              status: VisibilityExecutionStatus.INACTIVE,
            },
          },
        );
        await this.visibilityExecutionModel.create(validatedData.map((item) => ({ ...item, depotId })));
      });
    } finally {
      session.endSession();
    }

    return { failure: [] };
  }

  async exportVisibilityExecutions({
    search,
    depotId,
    status,
    orderBy,
    orderDesc,
    i18n,
  }: {
    search?: string;
    depotId: string;
    status: VisibilityExecutionStatus;
    orderBy?: string;
    orderDesc?: string;
    i18n: I18nContext;
  }) {
    const condition: FilterQuery<VisibilityExecutionDocument> = {
      status,
      depotId,
    };

    if (search?.trim()) {
      condition.name = {
        $regex: new RegExp(`^.*${normalizeQueryHelper(search.trim())}.*$`, 'i'),
      };
    }

    let tasks = [];
    if (!orderBy || orderBy === 'name') {
      tasks = await this.visibilityExecutionModel
        .find(condition)
        .sort(
          orderBy
            ? {
                name: orderDesc === 'DESC' ? -1 : 1,
                subHeading: orderDesc === 'DESC' ? -1 : 1,
                startDate: orderDesc === 'DESC' ? -1 : 1,
                endDate: orderDesc === 'DESC' ? -1 : 1,
              }
            : { createdAt: -1 },
        )
        .populate({
          path: 'outlets',
          select: 'ucc',
        });
    } else {
      tasks = await this.visibilityExecutionModel.find(condition).populate({
        path: 'outlets',
        select: 'ucc channel',
      });

      const {
        map: { modernOnTradeChannels, modernOffTradeChannels, traditionalOnTradeChannels, traditionalOffTradeChannels },
      } = await this.settingsService.getOutletChannels();

      tasks = tasks.map((task) => ({
        ...task.toJSON(),
        modernOnTrade: task.outlets.filter((outlet) => modernOnTradeChannels.map((channel) => channel.toLowerCase()).includes(outlet.channel?.toLowerCase())).length,
        modernOffTrade: task.outlets.filter((outlet) => modernOffTradeChannels.map((channel) => channel.toLowerCase()).includes(outlet.channel?.toLowerCase())).length,
        traditionalOnTrade: task.outlets.filter((outlet) => traditionalOnTradeChannels.map((channel) => channel.toLowerCase()).includes(outlet.channel?.toLowerCase())).length,
        traditionalOffTrade: task.outlets.filter((outlet) => traditionalOffTradeChannels.map((channel) => channel.toLowerCase()).includes(outlet.channel?.toLowerCase())).length,
      }));

      tasks = tasks.sort((first, second) => {
        const firstStat = first[orderBy];
        const secondStat = second[orderBy];

        if (orderDesc === 'ASC') {
          return firstStat - secondStat;
        }

        return secondStat - firstStat;
      });
    }

    let xlsxData = [
      {
        [i18n.translate(`importExport.taskName`)]: '',
        [i18n.translate(`importExport.taskSubHeading`)]: '',
        [i18n.translate(`importExport.status`)]: '',
        [i18n.translate(`importExport.startDate`)]: '',
        [i18n.translate(`importExport.endDate`)]: '',
        [i18n.translate(`importExport.ucc`)]: '',
      },
    ];

    if (tasks.length) {
      xlsxData = tasks.map((task) => ({
        [i18n.translate(`importExport.taskName`)]: task.name,
        [i18n.translate(`importExport.taskSubHeading`)]: task.subHeading,
        [i18n.translate(`importExport.status`)]: i18n.translate(`importExport.${task.status}`),
        [i18n.translate(`importExport.startDate`)]: moment(task.startDate).tz(process.env.TZ).startOf('date').format('DD/MM/YYYY'),
        [i18n.translate(`importExport.endDate`)]: moment(task.endDate).tz(process.env.TZ).startOf('date').format('DD/MM/YYYY'),
        [i18n.translate(`importExport.ucc`)]: task.outlets.map((outlet) => outlet.ucc).join(', '),
      }));
    }

    const result = await this.filesService.exportXLSXFile('Visibility_Execution', xlsxData, 'Visibility Execution', null);
    return result;
  }

  private async validateTasksData({ data, i18n }: { data: any[]; i18n: I18nContext }) {
    // find outlets by all ucc of rows
    const listUcc = data.reduce((pre, curr) => {
      const { taskData } = curr;
      const listUcc = taskData.ucc
        .split(',')
        .map((ucc) => ucc.trim())
        .filter(Boolean);

      return Array.from(new Set([...pre, ...listUcc]));
    }, [] as string[]);

    const outlets = await this.outletModel.find({ ucc: { $in: listUcc } }).select('_id, ucc');

    // map ucc and outlet id
    const outletUccIdMap = listUcc.reduce((pre, ucc) => {
      const outlet = outlets.find((outlet) => outlet.ucc === ucc);
      if (!outlet) {
        return pre;
      }

      return {
        ...pre,
        [ucc]: String(outlet._id),
      };
    }, {});

    const failure: Record<number, string[]> = {};
    const validatedData = [];

    const translatedStatusMap = Object.values(VisibilityExecutionStatus).reduce((pre, curr) => {
      const translate = i18n.translate(`importExport.${curr.toUpperCase()}`);
      return {
        ...pre,
        [translate]: curr,
      };
    }, {});

    for (const { row, taskData } of data) {
      const { taskName, taskSubHeading, status, startDate, endDate, ucc: listUccString } = taskData;
      const reasons: string[] = [];
      // check name
      if (!taskName?.trim()) {
        reasons.push(i18n.translate('visibilityExecution.error.invalidName'));
      }

      // check sub heading
      if (!taskSubHeading?.trim()) {
        reasons.push(i18n.translate('visibilityExecution.error.invalidSubHeading'));
      }

      // check status
      const enumStatus = translatedStatusMap[status];
      if (!enumStatus) {
        reasons.push(i18n.translate('visibilityExecution.error.invalidStatus'));
      }

      // check start date & end date
      if (!startDate || !new RegExp(/^\d{2}\/\d{2}\/\d{4}$/g).test(startDate)) {
        reasons.push(i18n.translate('visibilityExecution.error.invalidStartDate'));
      }
      if (!endDate || !new RegExp(/^\d{2}\/\d{2}\/\d{4}$/g).test(endDate)) {
        reasons.push(i18n.translate('visibilityExecution.error.invalidEndDate'));
      }
      if (
        new RegExp(/^\d{2}\/\d{2}\/\d{4}$/g).test(startDate) &&
        new RegExp(/^\d{2}\/\d{2}\/\d{4}$/g).test(endDate) &&
        moment(endDate, 'DD/MM/YYYY').tz(process.env.TZ).isBefore(moment(startDate, 'DD/MM/YYYY').tz(process.env.TZ))
      ) {
        reasons.push(i18n.translate('visibilityExecution.error.endDateMustBeAfterStartDate'));
      }

      // check ucc
      const listUcc: string[] = listUccString
        .split(',')
        .map((ucc) => ucc.trim())
        .filter(Boolean);

      if (!listUcc.length) {
        reasons.push(i18n.translate('visibilityExecution.error.invalidUcc'));
      } else {
        const invalidListUcc = [];
        listUcc.forEach((ucc) => {
          const outletId = outletUccIdMap[ucc];
          if (!outletId) {
            invalidListUcc.push(ucc);
          }
        });

        if (invalidListUcc.length) {
          reasons.push(
            i18n.translate('visibilityExecution.error.notFoundUcc', {
              args: { listUcc: invalidListUcc.join(', ') },
            }),
          );
        }
      }

      if (reasons.length) {
        failure[row] = reasons;
        continue;
      }

      validatedData.push({
        name: taskName,
        subHeading: taskSubHeading,
        status: enumStatus,
        startDate: moment(startDate, 'DD/MM/YYYY').tz(process.env.TZ).startOf('day').toDate(),
        endDate: moment(endDate, 'DD/MM/YYYY').tz(process.env.TZ).endOf('day').toDate(),
        outlets: listUcc.map((ucc) => new Types.ObjectId(outletUccIdMap[ucc])),
      });
    }

    return {
      failure,
      validatedData,
    };
  }

  /**
   * Find visibility executions by IDs and return selected fields
   * @param taskIds - Array of task IDs to search for
   * @param selectFields - Fields to select (default: '_id name subHeading')
   * @returns Array of tasks with selected fields
   */
  async findTasksByIds(taskIds: string[], selectFields = '_id name subHeading') {
    if (!taskIds.length) {
      return [];
    }

    return this.visibilityExecutionModel
      .find({
        _id: { $in: taskIds.map((id) => new Types.ObjectId(id)) },
      })
      .select(selectFields)
      .lean()
      .exec();
  }

  private transformTask(task: VisibilityExecution) {
    return {
      id: task._id,
      name: task.name,
      subHeading: task.subHeading,
      status: task.status,
      startDate: task.startDate,
      endDate: task.endDate,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
      outletIds: task.outlets,
    };
  }

  private assertDateRange({ startDate, endDate, i18n }: { startDate: moment.Moment; endDate: moment.Moment; i18n: I18nContext }) {
    if (endDate.isBefore(startDate)) {
      throw new BadRequestException(i18n.translate('visibilityExecution.error.endDateMustBeAfterStartDate'));
    }
  }
}
