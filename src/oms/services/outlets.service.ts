import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { OutletStatus } from 'src/outlets/enums/outlet-status.enum';
import { Outlet, OutletDocument } from 'src/outlets/schemas/outlet.schema';
import { SaleRepOutletRelation, SaleRepOutletRelationDocument } from 'src/outlets/schemas/sale-rep-outlet-relation.schema';
import { SettingsService } from 'src/settings/settings.service';

@Injectable()
export class OmsOutletsService {
  constructor(
    @InjectModel(Outlet.name) private readonly outletModel: Model<OutletDocument>,
    @InjectModel(SaleRepOutletRelation.name) private readonly salesRepOutletRelationModel: Model<SaleRepOutletRelationDocument>,
    private readonly settingsService: SettingsService,
  ) {}

  async getOutletsByDepotId(depotId: string, isBelongToChannel = false) {
    const BATCH_SIZE = 1000;
    const query: any = {
      depotId,
      status: OutletStatus.ACTIVE,
    };

    if (isBelongToChannel) {
      const { channels } = await this.settingsService.getOutletChannels();
      query.channel = { $in: channels };
    }

    let skip = 0;
    let hasMore = true;
    const result: Array<{
      id: Types.ObjectId;
      name: string;
      channel: string;
      salesRepId: string;
    }> = [];

    while (hasMore) {
      const outlets = await this.outletModel.find(query).skip(skip).limit(BATCH_SIZE).select('_id name channel').lean();

      if (!outlets.length) {
        hasMore = false;
        break;
      }

      const outletIds = outlets.map((outlet) => outlet._id);
      const relations = await this.salesRepOutletRelationModel
        .find({
          disconnected: false,
          outlet: { $in: outletIds },
        })
        .lean();

      const outletIdSalesRepIdMap = relations.reduce((map, relation) => {
        map[String(relation.outlet)] = String(relation.saleRep);
        return map;
      }, {} as Record<string, string>);

      result.push(
        ...outlets.map((outlet) => ({
          id: outlet._id,
          name: outlet.name,
          channel: outlet.channel,
          salesRepId: outletIdSalesRepIdMap[String(outlet._id)],
        })),
      );

      skip += BATCH_SIZE;
    }

    return result;
  }
}
