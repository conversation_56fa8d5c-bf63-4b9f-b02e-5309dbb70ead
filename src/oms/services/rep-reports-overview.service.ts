import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { GetSalesRepReportsOverviewQuery } from '../queries/get-sales-rep-reports-overview.query';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { InjectModel } from '@nestjs/mongoose';
import { SaleRepStatistic, SaleRepStatisticDocument } from 'src/sale-rep/schemas';
import { Model, ObjectId, SortOrder } from 'mongoose';
import { OmsSalesRepsService } from './sales-reps.service';
import * as moment from 'moment-timezone';
import { I18nContext } from 'nestjs-i18n';
import { FilesService } from 'src/files/services';
import { CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { SalesRepExternalStatisticsService } from './sales-rep-external-statistics.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConstantEventName } from 'src/utils/events';
import { SharedEvent } from 'src/shared/events/event/shared.event';
import { Cache } from 'cache-manager';
import { Distributor, DistributorDocument } from '../../distributor/schemas';
import { OmsPlansService } from './plans.service';

@Injectable()
export class OmsSalesRepReportsOverviewService {
  constructor(
    @InjectModel(SaleRepStatistic.name) private readonly statisticModel: Model<SaleRepStatisticDocument>,
    private readonly omsSalesRepsService: OmsSalesRepsService,
    private readonly filesService: FilesService,
    private readonly salesRepExternalStatisticsService: SalesRepExternalStatisticsService,
    @InjectModel(Distributor.name) private readonly distributorModel: Model<DistributorDocument>,
    private readonly eventEmitter: EventEmitter2,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly omsPlansService: OmsPlansService,
  ) {}

  async getOverview({ depotId, month, year, limit: defaultLimit, offset: defaultOffset, orderBy, orderDesc }: GetSalesRepReportsOverviewQuery & PaginationParams & OrderParams) {
    const offset = defaultOffset ? +defaultOffset : 0;
    const limit = defaultLimit ? +defaultLimit : 10;

    const salesRepIds = await this.omsPlansService.getDistributorAndSalesRepByDepotId(depotId);
    const cacheKey = `OMS_METRICS_SYNC_${depotId}`;
    const requestNewDataStatus = await this.cacheManager.get(cacheKey);
    if (!requestNewDataStatus) {
      this.eventEmitter.emit(
        ConstantEventName.SHARED_EVENT,
        new SharedEvent({
          callback: async () => {
            await this.salesRepExternalStatisticsService.syncOmsMetrics({
              salesRepIds: salesRepIds.users.map((salesRep) => String(salesRep)),
              depotId,
            });
          },
        }),
      );
      await this.cacheManager.set(cacheKey, 1, { ttl: 43200 }); //12hs
    }
    const aggregation = this.createOverviewAggregation({
      depotId,
      month,
      year,
      salesRepIds: salesRepIds.users.map((salesRep: any) => salesRep),
    });

    let sort: Record<string, SortOrder> = {
      createdAt: -1,
    };
    if (orderBy) {
      sort = {
        [orderBy]: orderDesc === 'DESC' ? -1 : 1,
      };
    }

    const [{ totalRecords, data }] = await aggregation.sort(sort).facet({
      totalRecords: [
        {
          $count: 'total',
        },
      ],
      data: [
        {
          $skip: offset,
        },
        {
          $limit: limit,
        },
      ],
    });

    const totalReport = totalRecords.length ? totalRecords[0].total : 0;
    const distributor = await this.distributorModel.findOne({ 'depots.id': depotId });
    return {
      totalReport,
      reports:
        (await Promise.all(
          data.map((row) => {
            return {
              ...row,
              distributor: {
                distributorId: distributor?.distributorId,
                name: distributor?.distributorName,
              },
            };
          }),
        )) || [],
    };
  }

  async exportOverview({ query: { depotId, month, year, orderBy, orderDesc }, i18n }: { query: GetSalesRepReportsOverviewQuery & OrderParams; i18n: I18nContext }) {
    // const salesReps = await this.omsSalesRepsService.getByDepot(depotId);
    const salesRepIds = await this.omsPlansService.getDistributorAndSalesRepByDepotId(depotId);

    let sort: Record<string, SortOrder> = {
      createdAt: -1,
    };
    if (orderBy) {
      sort = {
        [orderBy]: orderDesc === 'DESC' ? -1 : 1,
      };
    }
    const distributor = await this.distributorModel.findOne({ 'depots.id': depotId });
    const reportsData = await this.createOverviewAggregation({
      depotId,
      month,
      year,
      salesRepIds: salesRepIds?.users?.map((salesRep: any) => salesRep),
    }).sort(sort);
    const reports = await Promise.all(
      reportsData.map((row) => {
        return {
          ...row,
          distributor: {
            distributorId: distributor?.distributorId,
            name: distributor?.distributorName,
          },
        };
      }),
    );

    let xlsxData = [
      {
        [i18n.translate(`importExport.repReports.repId`)]: '',
        [i18n.translate(`importExport.repReports.repName`)]: '',
        [i18n.translate(`importExport.repReports.distId`)]: '',
        [i18n.translate(`importExport.repReports.distName`)]: '',
        [i18n.translate(`importExport.repReports.callComplianceRate`)]: '',
        [i18n.translate(`importExport.repReports.callEffectiveness`)]: '',
        [i18n.translate(`importExport.repReports.salesVolume`)]: '',
        [i18n.translate(`importExport.repReports.salesValue`)]: '',
        [i18n.translate(`importExport.repReports.activeSellingOutlet`)]: '',
        [i18n.translate(`importExport.repReports.availability`)]: '',
        [i18n.translate(`importExport.repReports.visibility`)]: '',
      },
    ];

    if (reports.length) {
      xlsxData = reports.map((report) => ({
        [i18n.translate(`importExport.repReports.repId`)]: report.salesRep.salesRepId,
        [i18n.translate(`importExport.repReports.repName`)]: report.salesRep.name,
        [i18n.translate(`importExport.repReports.distId`)]: report.distributor.distributorId,
        [i18n.translate(`importExport.repReports.distName`)]: report.distributor.name,
        [i18n.translate(`importExport.repReports.callComplianceRate`)]: `${report.callComplianceRate.current} / ${report.callComplianceRate.target}`,
        [i18n.translate(`importExport.repReports.callEffectiveness`)]: `${report.callEffectiveness.current} / ${report.callEffectiveness.target}`,
        [i18n.translate(`importExport.repReports.salesVolume`)]: `${report.salesVolume.current} / ${report.salesVolume.target}`,
        [i18n.translate(`importExport.repReports.salesValue`)]: `${report.salesValues.current} / ${report.salesValues.target}`,
        [i18n.translate(`importExport.repReports.activeSellingOutlet`)]: `${report.activeSellingOutlet.current} / ${report.activeSellingOutlet.target}`,
        [i18n.translate(`importExport.repReports.availability`)]: `${report.availability.current} / ${report.availability.target}`,
        [i18n.translate(`importExport.repReports.visibility`)]: `${report.visibility.current} / ${report.visibility.target}`,
      }));
    }

    return await this.filesService.exportXLSXFile('Rep Reports Overview', xlsxData, 'Overview', null);
  }

  private createOverviewAggregation({ month, year, salesRepIds, depotId }: GetSalesRepReportsOverviewQuery & { salesRepIds: Array<ObjectId> }) {
    const today = moment()
      .set('month', month - 1)
      .set('year', year)
      .tz(process.env.TZ)
      .endOf('date');

    return (
      this.statisticModel
        .aggregate()
        .match({
          saleRep: { $in: salesRepIds },
          statisticDay: today.toDate(),
        })
        .lookup({
          localField: 'saleRep',
          from: 'users',
          foreignField: '_id',
          as: 'salesRep',
        })
        .unwind({
          preserveNullAndEmptyArrays: false,
          path: '$salesRep',
        })
        // .lookup({
        //   localField: 'outlet',
        //   from: 'outlets',
        //   foreignField: '_id',
        //   as: 'outlet',
        // })
        // .unwind({
        //   preserveNullAndEmptyArrays: false,
        //   path: '$outlet',
        // })
        // .match({
        //   'outlet.depotId': depotId,
        // })
        .project({
          id: '$_id',
          salesRep: {
            id: '$salesRep._id',
            salesRepId: '$salesRep.saleRepId',
            name: '$salesRep.username',
          },
          // distributor: {
          //   distributorId: '$distributor.distributorId',
          //   name: '$distributor.distributorName',
          // },
          callComplianceRate: {
            current: { $ifNull: ['$cpsrValue', 0] },
            target: { $ifNull: ['$cpsrTarget', 0] },
            percent: {
              $switch: {
                branches: [
                  {
                    case: { $eq: [{ $ifNull: ['$cpsrTarget', 0] }, 0] },
                    then: 0,
                  },
                ],
                default: {
                  $divide: [{ $ifNull: ['$cpsrValue', 0] }, { $ifNull: ['$cpsrTarget', 0] }],
                },
              },
            },
          },
          callEffectiveness: {
            current: { $ifNull: ['$callEffectivenessValue', 0] },
            target: { $ifNull: ['$callEffectivenessTarget', 0] },
            percent: {
              $switch: {
                branches: [
                  {
                    case: { $eq: [{ $ifNull: ['$callEffectivenessTarget', 0] }, 0] },
                    then: 0,
                  },
                ],
                default: {
                  $divide: [{ $ifNull: ['$callEffectivenessValue', 0] }, { $ifNull: ['$callEffectivenessTarget', 0] }],
                },
              },
            },
          },
          salesVolume: {
            current: { $ifNull: ['$salesVolumeValue', 0] },
            target: { $ifNull: ['$salesVolumeTarget', 0] },
            percent: { $ifNull: ['$salesVolumeValue', 0] },
          },
          availability: {
            current: { $ifNull: ['$availabilityValue', { $ifNull: ['$avaibilityValue', 0] }] },
            target: { $ifNull: ['$availabilityTarget', { $ifNull: ['$avaibilityTarget', 0] }] },
            percent: {
              $switch: {
                branches: [
                  {
                    case: {
                      $eq: [{ $ifNull: ['$availabilityTarget', { $ifNull: ['$avaibilityTarget', 0] }] }, 0],
                    },
                    then: 0,
                  },
                ],
                default: {
                  $divide: [{ $ifNull: ['$availabilityValue', { $ifNull: ['$avaibilityValue', 0] }] }, { $ifNull: ['$availabilityTarget', { $ifNull: ['$avaibilityTarget', 0] }] }],
                },
              },
            },
          },
          visibility: {
            current: { $ifNull: ['$visibilityValue', 0] },
            target: { $ifNull: ['$visibilityTarget', 0] },
            percent: {
              $switch: {
                branches: [
                  {
                    case: { $eq: [{ $ifNull: ['$visibilityTarget', 0] }, 0] },
                    then: 0,
                  },
                ],
                default: {
                  $divide: [{ $ifNull: ['$visibilityValue', 0] }, { $ifNull: ['$visibilityTarget', 0] }],
                },
              },
            },
          },
          activeSellingOutlet: {
            current: { $ifNull: ['$maboValue', 0] },
            target: { $ifNull: ['$maboTarget', 0] },
            percent: {
              $switch: {
                branches: [
                  {
                    case: { $eq: [{ $ifNull: ['$maboTarget', 0] }, 0] },
                    then: 0,
                  },
                ],
                default: {
                  $divide: [{ $ifNull: ['$maboValue', 0] }, { $ifNull: ['$maboTarget', 0] }],
                },
              },
            },
          },
          salesValues: {
            current: { $ifNull: ['$salesValue', 0] },
            target: { $ifNull: ['$salesTarget', 0] },
            percent: { $ifNull: ['$salesValue', 0] },
          },
        })
    );
  }
}
