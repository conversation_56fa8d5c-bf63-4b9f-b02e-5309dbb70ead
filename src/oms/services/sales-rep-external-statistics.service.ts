import { CACHE_MANAGER, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { OmsService } from 'src/external/services/oms.service';
import { Outlet, OutletDocument } from 'src/outlets/schemas/outlet.schema';
import { SaleRepStatistic, SaleRepStatisticDocument } from 'src/sale-rep/schemas';
import { User, UserDocument } from 'src/users/schemas/user.schema';
import { getRandomTTL, isEmptyObjectOrArray, printLog } from 'src/utils';
import { Cache } from 'cache-manager';

@Injectable()
export class SalesRepExternalStatisticsService {
  constructor(
    @InjectModel(SaleRepStatistic.name) private readonly salesRepStatisticModel: Model<SaleRepStatisticDocument>,
    @InjectModel(Outlet.name) private readonly outletModel: Model<OutletDocument>,
    @InjectModel(User.name) private readonly salesRepModel: Model<UserDocument>,
    @Inject(forwardRef(() => OmsService))
    private readonly omsService: OmsService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  canCallOmsMetrics = (): boolean => {
    const envFlag = process.env.SYNC_OMS_METRICS?.toString()?.trim() !== 'false';
    const hour = moment().tz(process.env.TZ).hour();
    const isAllowedTime = hour >= 18 || hour <= 7; // 18h PM to 7h AM

    return envFlag || isAllowedTime;
  };

  async syncOmsMetrics({ salesRepIds, depotId }: { salesRepIds: string[]; depotId?: string }) {
    try {
      if (salesRepIds?.length > 1 && !this.canCallOmsMetrics()) {
        return;
      }
      const now = moment().tz(process.env.TZ).endOf('date');
      const salesReps = await this.salesRepModel.find({ _id: { $in: salesRepIds.map((id) => new Types.ObjectId(id)) } }).select('saleRepId');
      const salesRepIdMap = salesReps.reduce(
        (initial, salesRep) => ({
          ...initial,
          [salesRep.saleRepId]: String(salesRep._id),
        }),
        {},
      );

      if (!depotId) {
        const outlet = await this.outletModel.findOne({
          saleRep: {
            $in: salesReps.map((salesRep) => salesRep._id),
          },
          depotId: {
            $exists: true,
            $ne: '',
          },
        });

        depotId = outlet?.depotId;
      }
      if (!depotId) {
        return;
      }

      // printLog('syncOmsMetrics', salesRepIdMap, depotId);
      let cacheKey = '';
      if (salesRepIds?.length === 1) {
        cacheKey = `salesRepSyncOmsMetrics-${salesRepIds[0]}-${depotId}`;
        const cacheRes = await this.cacheManager.get(cacheKey);
        if (!isEmptyObjectOrArray(cacheRes)) {
          return cacheRes;
        }
      }

      const startDate = now.clone().startOf('month').startOf('date').toDate();
      const endDate = now.clone().endOf('month').endOf('date').toDate();
      const response = await this.omsService.getSalesRepStatisticsOMS({
        salesRepIds: salesReps.map((salesRep) => salesRep.saleRepId),
        startDate,
        endDate,
        depotId,
      });

      if (cacheKey) {
        await this.cacheManager.set(cacheKey, response || [], { ttl: getRandomTTL() / 3 });
      }

      await this.salesRepStatisticModel.bulkWrite(
        response
          .map((item: any) => {
            const salesRepObjectId = salesRepIdMap[item.contact_external_id];
            if (!salesRepObjectId) {
              return null;
            }

            return {
              updateOne: {
                filter: {
                  saleRep: new Types.ObjectId(salesRepObjectId),
                  statisticDay: now.clone().toDate(),
                },
                update: {
                  salesValue: Number(item.sale_value),
                  salesVolumeValue: Number(item.sale_volume),
                  maboValue: Number(item.aso_total),
                },
              },
            };
          })
          .filter(Boolean),
      );

      return response;
    } catch (error) {
      printLog('Error syncing oms metrics', error);
    }
  }
}
