import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, SortOrder, Types } from 'mongoose';
import { NotificationLog, NotificationLogDocument, PushNotification, PushNotificationDocument } from 'src/message/schemas';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { CreateOmsNotificationDto } from '../dto/create-oms-notification.dto';
import { Distributor, DistributorDocument, DistributorUserRelation, DistributorUserRelationDocument } from 'src/distributor/schemas';
import { NotificationType } from 'src/message/enums';
import * as moment from 'moment-timezone';
import { MessageService } from 'src/message/services';
import { User, UserDocument } from 'src/users/schemas/user.schema';
import { SaleRepOutletRelation, SaleRepOutletRelationDocument } from 'src/outlets/schemas/sale-rep-outlet-relation.schema';
import { Outlet, OutletDocument } from 'src/outlets/schemas/outlet.schema';
import { UserDetailService } from '../../users/services/user-detail.service';

@Injectable()
export class OmsNotificationsService {
  constructor(
    @InjectModel(PushNotification.name) private readonly notificationModel: Model<PushNotificationDocument>,
    @InjectModel(NotificationLog.name) private readonly notificationLogModel: Model<NotificationLogDocument>,
    @InjectModel(Distributor.name) private readonly distributorModel: Model<DistributorDocument>,
    @InjectModel(User.name) private readonly userModel: Model<UserDocument>,
    @InjectModel(Outlet.name) private readonly outletModel: Model<OutletDocument>,
    @InjectModel(SaleRepOutletRelation.name) private readonly salesRepOutletRelationModel: Model<SaleRepOutletRelationDocument>,
    @InjectModel(DistributorUserRelation.name) private readonly salesRepDistributorRelationModel: Model<DistributorUserRelationDocument>,
    private readonly messagesService: MessageService,
    private readonly userDetailService: UserDetailService,
  ) {}

  async getNotifications({ depotId, limit, offset, orderBy, orderDesc }: { depotId: string } & PaginationParams & OrderParams) {
    const condition: FilterQuery<PushNotificationDocument> = {
      depotId,
    };

    let sort: Record<string, SortOrder> = {
      createdAt: -1,
    };
    if (orderBy) {
      sort = {
        [orderBy]: orderDesc === 'ASC' ? 1 : -1,
      };
    }

    const countNotificationsQuery = this.notificationModel.countDocuments(condition);
    const getNotificationsQuery = this.notificationModel
      .aggregate()
      .match(condition)
      .lookup({
        localField: 'distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: '_id',
        from: 'notificationlogs',
        foreignField: 'notification',
        as: 'logs',
      })
      .project({
        id: '$_id',
        toEveryone: '$toEveryone',
        message: '$message',
        sentAt: '$createdAt',
        distributorId: '$distributor.distributorId',
        distributorName: '$distributor.distributorName',
        logs: '$logs',
      })
      .sort(sort)
      .skip(offset ? +offset : 0)
      .limit(limit ? +limit : 10);

    const [totalItem, notifications] = await Promise.all([countNotificationsQuery, getNotificationsQuery]);
    const salesRepIds = notifications.reduce((pre, notification) => [...new Set([...pre, ...notification.logs.map((log) => String(log.user))])], []);
    const salesReps = await this.userModel.find({
      _id: { $in: salesRepIds.map((id) => new Types.ObjectId(id)) },
    });
    const salesRepMap = salesReps.reduce((pre, salesRep) => ({ ...pre, [String(salesRep._id)]: salesRep }), {});

    return {
      totalItem,
      notifications: notifications.map((notification) => ({
        id: notification._id,
        message: notification.message,
        toEveryone: notification.toEveryone,
        distributor: {
          id: notification.distributorId,
          name: notification.distributorName,
        },
        sentAt: notification.sentAt,
        recipients: notification.logs.map((log) => {
          const salesRep = salesRepMap[String(log.user)];

          return {
            id: salesRep.saleRepId,
            name: salesRep.username,
          };
        }),
      })),
    };
  }

  async createNotification({ actorId, dto }: { actorId: string; dto: CreateOmsNotificationDto }) {
    const [distributors, devices] = await Promise.all([this.distributorModel.find(), this.userDetailService.findAll({ userId: { $in: dto.salesRepIds } })]);

    let distributor = distributors.find((distributor) => distributor.depots.map((depot) => depot.id).includes(dto.depotId));

    if (!distributor) {
      const outlet = await this.outletModel.findOne({ depotId: dto.depotId });
      if (outlet?.distributorId) {
        distributor = await this.distributorModel.findById(outlet.distributorId);
      }
      if (!distributor) {
        const salesRepOutletRelation = await this.salesRepOutletRelationModel.findOne({ outlet: outlet._id, disconnected: false });
        if (salesRepOutletRelation) {
          const salesRepDistributorRelation = await this.salesRepDistributorRelationModel.findOne({ user: salesRepOutletRelation.saleRep });
          distributor = await this.distributorModel.findById(salesRepDistributorRelation.distributor);
        }
      }
    }

    const fcmTokens = devices.flatMap((user) => user.userDevice.map((token) => token.fcmToken));

    const session = await this.notificationModel.startSession();
    try {
      return session.withTransaction(async () => {
        const notification = await this.notificationModel.create({
          createdBy: new Types.ObjectId(actorId),
          distributor: distributor._id,
          message: dto.message,
          title: '',
          toEveryone: dto.toEveryone,
          depotId: dto.depotId,
        });

        const salesRepOutletRelations = await this.salesRepOutletRelationModel.find({
          saleRep: {
            $in: dto.salesRepIds.map((id) => new Types.ObjectId(id)),
          },
          disconnected: false,
        });
        const salesRepOutletMap = salesRepOutletRelations.reduce((pre, relation) => ({ ...pre, [String(relation.saleRep)]: String(relation.outlet) }), {});

        await this.notificationLogModel.create(
          Object.keys(salesRepOutletMap).map((salesRepId) => ({
            user: new Types.ObjectId(salesRepId),
            title: '',
            body: dto.message,
            outlet: new Types.ObjectId(salesRepOutletMap[salesRepId]),
            notification: notification._id,
            type: NotificationType.DISTRIBUTOR_ADMIN_PUSH_NOTIFICATION,
            createdAt: moment().tz(process.env.TZ),
            updatedAt: moment().tz(process.env.TZ),
          })),
        );

        // send notification to device
        if (fcmTokens.length) {
          const unusedTokens = await this.messagesService.sendMessageByFcmTokens(fcmTokens, '', dto.message);
          if (unusedTokens?.length) {
            await this.userDetailService.deleteAllUserDeviceByFcmToken(unusedTokens);
          }
        }
      });
    } finally {
      session.endSession();
    }
  }
}
