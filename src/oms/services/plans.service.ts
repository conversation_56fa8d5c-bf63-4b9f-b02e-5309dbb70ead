import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { OmsWeeksService } from './weeks.service';
import { JourneyPlanWeek, JourneyPlanWeekDocument } from 'src/journey-plannings/schemas/journey-plan-week.schema';
import * as moment from 'moment-timezone';
import { GetOmsPlansQuery } from '../queries/get-plans.query';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { I18nContext } from 'nestjs-i18n';
import { FilesService } from 'src/files/services';
import { JourneyPlanCycle, JourneyPlanCycleDocument } from 'src/journey-plannings/schemas/journey-plan-cycle.schema';
import { CreateOmsPlanDto } from '../dto/create-oms-plan.dto';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import { Outlet, OutletDocument } from 'src/outlets/schemas/outlet.schema';
import { Distributor, DistributorDocument, DistributorUserRelation, DistributorUserRelationDocument } from 'src/distributor/schemas';
import { UpdateOmsPlanDto } from '../dto/update-oms-plan.dto';
import { OmsFilesService } from './files.service';
import { ImportOmsPlansColumns } from 'src/admin/enums';
import { OmsCyclesService } from './cycles.service';
import { User, UserDocument } from 'src/users/schemas/user.schema';
import { UpdatePlanMissReasonDto } from '../dto/update-plan-miss-reason.dto';
import { OmsTimeFramesService } from './time-frames.service';
import { SaleRepStatisticService } from '../../sale-rep/services';
import { OutletsService } from '../../outlets/services/outlets.service';
import { isEmptyObjectOrArray } from '../../utils';

@Injectable()
export class OmsPlansService {
  constructor(
    @InjectModel(OutletJourneyPlanning.name) private readonly planModel: Model<OutletJourneyPlanningDocument>,
    @InjectModel(JourneyPlanCycle.name) private readonly cycleModel: Model<JourneyPlanCycleDocument>,
    @InjectModel(JourneyPlanWeek.name) private readonly weekModel: Model<JourneyPlanWeekDocument>,
    @InjectModel(Outlet.name) private readonly outletModel: Model<OutletDocument>,
    @InjectModel(User.name) private readonly salesRepModel: Model<UserDocument>,
    @InjectModel(DistributorUserRelation.name) private readonly salesRepDistributorRelationModel: Model<DistributorUserRelationDocument>,
    @InjectModel(Distributor.name) private readonly distributorModel: Model<DistributorDocument>,
    private readonly omsWeeksService: OmsWeeksService,
    private readonly filesService: FilesService,
    private readonly omsFilesService: OmsFilesService,
    private readonly omsCyclesService: OmsCyclesService,
    private readonly omsTimeFrameService: OmsTimeFramesService,
    private readonly _saleRepStatisticService: SaleRepStatisticService,
    private readonly _outletsService: OutletsService,
  ) {}

  public async getDistributorAndSalesRepByDepotId(depotId: string) {
    try {
      const BATCH_SIZE = 1000;
      const distributor = await this.distributorModel.findOne({ 'depots.id': depotId });
      if (isEmptyObjectOrArray(distributor)) {
        return null;
      }

      const users: any[] = [];
      let skip = 0;
      let hasMore = true;

      while (hasMore) {
        const batch = await this.salesRepDistributorRelationModel
          .find({ distributor: distributor._id, user: { $exists: true } })
          .select('user')
          .skip(skip)
          .limit(BATCH_SIZE)
          .lean();

        if (!batch.length) {
          hasMore = false;
          break;
        }

        for (const relation of batch) {
          if (relation.user) {
            users.push(relation.user);
          }
        }

        skip += BATCH_SIZE;
      }

      return { distributor, users };
    } catch (e) {
      return { distributor: null, users: null };
    }
  }

  async getPlans({ depotId, cycleId, limit: defaultLimit, offset: defaultOffset, orderBy, orderDesc }: GetOmsPlansQuery & PaginationParams & OrderParams, i18n: I18nContext) {
    const offset = defaultOffset ? +defaultOffset : 0;
    const limit = defaultLimit ? +defaultLimit : 10;
    const weeks = await this.omsWeeksService.getWeeksByCycleId(cycleId);

    return this.getPlanData({ depotId, weeks, limit, offset, orderBy, orderDesc }, i18n);
  }

  /*async getPlanOld({ depotId, cycleId, limit: defaultLimit, offset: defaultOffset, orderBy, orderDesc }: GetOmsPlansQuery & PaginationParams & OrderParams) {
    const offset = defaultOffset ? +defaultOffset : 0;
    const limit = defaultLimit ? +defaultLimit : 10;
    const weeks = await this.omsWeeksService.getWeeksByCycleId(cycleId);
    const weekDayNameMap = weeks.reduce(
      (initial, week) => ({
        ...initial,
        [String(week._id)]: this.omsWeeksService.getDateAndDayNameInWeek(week),
      }),
      {} as Record<string, Record<string, number>>,
    );

    const salesRepIds = await this.getDistributorAndSalesRepByDepotId(depotId);
    const aggregation = this.createGetPlansAggregation({ depotId, weeks, salesRepIds: salesRepIds?.users });

    if (orderBy === 'day') {
      return this.getPlansWhenOrderByDay({ aggregation, orderDesc, offset, limit, weekDayNameMap });
    }
    let sort: Record<string, SortOrder> = {
      createdAt: -1,
    };
    if (orderBy) {
      sort = {
        [orderBy]: orderDesc === 'DESC' ? -1 : 1,
      };
    }

    const [{ totalRecords, data }] = await aggregation.sort(sort).facet({
      totalRecords: [
        {
          $count: 'total',
        },
      ],
      data: [
        {
          $skip: offset,
        },
        {
          $limit: limit,
        },
      ],
    });
    const totalItem = totalRecords.length ? totalRecords[0].total : 0;

    return {
      totalItem,
      plans: data.map((item) => this.transformPlan({ data: item, weekDayNameMap })),
    };
  }*/

  async exportPlans({ query: { depotId, cycleId, orderBy, orderDesc }, i18n }: { query: GetOmsPlansQuery & OrderParams; i18n: I18nContext }) {
    const [cycle, weeks] = await Promise.all([this.cycleModel.findById(cycleId), this.omsWeeksService.getWeeksByCycleId(cycleId)]);
    const results = await this.getPlanData({ depotId, weeks, limit: 100000, offset: 0, orderBy, orderDesc }, i18n);
    let xlsxData = [
      {
        [i18n.translate(`importExport.plansOutletUcc`)]: '',
        [i18n.translate(`importExport.plansSalesRepId`)]: '',
        [i18n.translate(`importExport.plansWeek`)]: '',
        [i18n.translate(`importExport.plansDay`)]: '',
        [i18n.translate(`importExport.plansDistributorId`)]: '',
      },
    ];

    if (results.totalItem) {
      xlsxData = results.plans.map((plan) => ({
        [i18n.translate(`importExport.plansOutletUcc`)]: plan.outlet.ucc,
        [i18n.translate(`importExport.plansSalesRepId`)]: plan.salesRep.salesRepId,
        [i18n.translate(`importExport.plansWeek`)]: plan.week.name,
        [i18n.translate(`importExport.plansDay`)]: plan.dayInWeek,
        [i18n.translate(`importExport.plansDistributorId`)]: plan.distributor.distributorId,
      }));
    }

    const result = await this.filesService.exportXLSXFile(`Journey_Plannings_Depot_${depotId}_Cycle_${cycle.cycleName}_${cycle.year}`, xlsxData, 'Journey planning', null);
    return result;
  }

  async getTemplate({ i18n }: { i18n: I18nContext }) {
    const xlsxData = [
      {
        [i18n.translate(`importExport.plansOutletUcc`)]: 'outlet_ucc',
        [i18n.translate(`importExport.plansSalesRepId`)]: 'sales_rep_id',
        [i18n.translate(`importExport.plansWeek`)]: '3',
        [i18n.translate(`importExport.plansDay`)]: 1,
        [i18n.translate(`importExport.plansDistributorId`)]: 'distributor_id',
      },
    ];

    const result = await this.filesService.exportXLSXFile('Journey_Plannings_Template', xlsxData, 'Journey planning', null);
    return result;
  }

  async createPlan({ dto, i18n }: { dto: CreateOmsPlanDto; i18n: I18nContext }) {
    const existedPlanInWeek = await this.planModel.findOne({
      week: new Types.ObjectId(dto.weekId),
      saleRep: new Types.ObjectId(dto.salesRepId),
      outlet: new Types.ObjectId(dto.outletId),
    });

    if (existedPlanInWeek) {
      throw new BadRequestException(i18n.translate('plan.existed_outlet_plan'));
    }

    const [outlet, week] = await Promise.all([this.outletModel.findById(dto.outletId), this.weekModel.findById(dto.weekId)]);
    await this._outletsService.checkOutletSalesRepRelation({ outletId: outlet._id, salesRepId: dto.salesRepId, i18n });
    const availableSlotsMap = await this.getAvailableTimeSlots({
      salesRepIds: [dto.salesRepId],
      depotId: outlet.depotId,
      weekIds: [dto.weekId],
    });

    let day = this.omsWeeksService.getDayNameAndDateInWeek(week)[dto.day];
    const weekName = week.weekName.replace('Week ', '');

    const availableSlots = availableSlotsMap[dto.salesRepId][weekName][dto.day];
    if (!availableSlots || !availableSlots.length) {
      throw new BadRequestException(i18n.translate('plan.existed_outlet_plan'));
    }

    const { hour, minute } = availableSlots[0];
    day = moment(day).set('hours', hour).set('minutes', minute).toDate();

    const plan = await this.planModel.create({
      week: week._id,
      outlet: new Types.ObjectId(dto.outletId),
      saleRep: new Types.ObjectId(dto.salesRepId),
      day,
      visitStatus: VisitStatus.START_VISIT,
      planDate: day,
      depotId: outlet.depotId,
    });

    return this.getPlanDetails(plan);
  }

  async updatePlan({ id, dto, i18n }: { id: string; dto: UpdateOmsPlanDto; i18n: I18nContext }) {
    const plan = await this.planModel.findById(id);

    if (!plan) {
      throw new BadRequestException(i18n.translate('plan.not_found'));
    }
    await this._outletsService.checkOutletSalesRepRelation({ outletId: plan.outlet?.toString(), salesRepId: plan.saleRep?.toString(), i18n });
    const existedPlanInWeek = await this.planModel.findOne({
      _id: { $ne: plan._id },
      week: new Types.ObjectId(dto.weekId),
      saleRep: plan.saleRep,
      outlet: plan.outlet,
    });

    if (existedPlanInWeek) {
      throw new BadRequestException(i18n.translate('plan.existed_outlet_plan'));
    }

    const [outlet, week] = await Promise.all([this.outletModel.findById(plan.outlet), this.weekModel.findById(dto.weekId)]);
    const availableSlotsMap = await this.getAvailableTimeSlots({
      salesRepIds: [String(plan.saleRep)],
      depotId: outlet.depotId,
      weekIds: [dto.weekId],
    });

    let day = this.omsWeeksService.getDayNameAndDateInWeek(week)[dto.day];
    const weekName = week.weekName.replace('Week ', '');

    const availableSlots = availableSlotsMap[String(plan.saleRep)][weekName][dto.day];
    if (!availableSlots || !availableSlots.length) {
      throw new BadRequestException(i18n.translate('plan.existed_outlet_plan'));
    }

    const { hour, minute } = availableSlots[0];
    day = moment(day).set('hours', hour).set('minutes', minute).toDate();

    if (moment(day).tz(process.env.TZ).isBefore(moment().tz(process.env.TZ).startOf('date'))) {
      throw new BadRequestException('plan.invalid_schedule_day');
    }

    const updatedPlan = await this.planModel.findByIdAndUpdate(
      plan._id,
      {
        $set: {
          week: week._id,
          // day,
          rescheduled: true,
          rescheduledDay: day,
          visitStatus: VisitStatus.START_VISIT,
          depotId: outlet?.depotId,
        },
        $unset: {
          missedReason: 1,
        },
      },
      {
        new: true,
      },
    );

    return this.getPlanDetails(updatedPlan);
  }

  async importPlans({ file, cycleId, depotId, i18n }: { file: Express.Multer.File; cycleId: string; depotId: string; i18n: I18nContext }) {
    const [cycleTimeRange, weeks, excelData] = await Promise.all([
      this.omsCyclesService.getCycleTimeRange(cycleId),
      this.omsWeeksService.getWeeksByCycleId(cycleId),
      this.omsFilesService.handleUploadingFile(
        file,
        ImportOmsPlansColumns.map((e) => i18n.translate(`importExport.${e}`)),
        ImportOmsPlansColumns,
        i18n,
      ),
    ]);
    if (!cycleTimeRange) {
      throw new BadRequestException('plan.not_found_cycle');
    }

    if (cycleTimeRange.endTime.isBefore(moment().tz(process.env.TZ))) {
      throw new BadRequestException('plan.invalid_week_day');
    }

    const weekNameMap = weeks.reduce(
      (initial, week) => ({
        ...initial,
        [week.weekName.replace('Week ', '')]: String(week._id),
      }),
      {} as Record<string, string>,
    );

    // transform combo week-day to date
    const weekDayDateMap = weeks.reduce((initial, week) => {
      const dayInWeekAndDate = this.omsWeeksService.getDayNameAndDateInWeek(week);
      const weekName = week.weekName.replace('Week ', '');
      const data = Object.keys(dayInWeekAndDate).reduce((pre, dayInWeek) => {
        return {
          ...pre,
          [`${weekName}-${dayInWeek}`]: moment(dayInWeekAndDate[dayInWeek]).tz(process.env.TZ),
        };
      }, {} as Record<string, moment.Moment>);

      return {
        ...initial,
        ...data,
      };
    }, {} as Record<string, moment.Moment>);

    // get list outlet by ucc
    // get list sales rep
    const listOutletUcc = Array.from(new Set(excelData.map((item) => item.plansOutletUcc?.trim()).filter(Boolean)));
    const listSalesRepIds = Array.from(new Set(excelData.map((item) => item.plansSalesRepId?.trim()).filter(Boolean)));
    const listDistributorIds = Array.from(new Set(excelData.map((item) => item.plansDistributorId?.trim()).filter(Boolean)));
    const [outlets, salesReps, distributors] = await Promise.all([
      this.outletModel.find({ ucc: { $in: listOutletUcc } }),
      this.salesRepModel.find({ saleRepId: { $in: listSalesRepIds } }),
      this.distributorModel.find({
        distributorId: { $in: listDistributorIds },
      }),
    ]);

    const availableSlotsMap = await this.getAvailableTimeSlots({
      salesRepIds: salesReps.map((item) => item._id),
      depotId: depotId,
      weekIds: weeks.map((week) => week._id),
    });

    const uccOutletMap = outlets.reduce((initial, outlet) => ({ ...initial, [outlet.ucc]: outlet }), {} as Record<string, Outlet>);
    const salesRepIdMap = salesReps.reduce((initial, salesRep) => ({ ...initial, [salesRep.saleRepId]: salesRep }), {} as Record<string, User>);
    const distributorIdMap = distributors.reduce((initial, distributor) => ({ ...initial, [distributor.distributorId]: distributor }), {} as Record<string, Distributor>);

    const excelDataMap = excelData.reduce((initial, item) => {
      const { plansOutletUcc: ucc, plansSalesRepId: salesRepId, plansWeek: week } = item;
      const outletId = String(uccOutletMap[ucc]?._id);
      const key = `${salesRepId}-${outletId}-${week}`;

      const time = initial[key] ? initial[key] + 1 : 1;

      return {
        ...initial,
        [key]: time,
      };
    }, {} as Record<string, number>);

    const plans = await this.planModel.find({
      saleRep: { $in: salesReps.map((salesRep) => salesRep._id) },
      outlet: { $in: outlets.map((outlet) => outlet._id) },
      week: { $in: weeks.map((week) => week._id) },
    });

    const salesRepOutletWeekPlanMap = plans.reduce((initial, plan) => {
      const salesRepId = String(plan.saleRep);
      const outletId = String(plan.outlet);
      const weekId = String(plan.week);

      return {
        ...initial,
        [`${salesRepId}-${outletId}-${weekId}`]: plan,
      };
    }, {} as Record<string, OutletJourneyPlanning>);

    const failure: Array<{
      row: number;
      reasons: string[];
    }> = [];
    const validatedData: Array<{
      [key in keyof OutletJourneyPlanningDocument]?: any;
    }> = [];

    for (let i = 0; i < excelData.length; i++) {
      const row = i + 2;
      const reasons: string[] = [];
      const dataItem = excelData[i];
      const { plansOutletUcc: ucc, plansSalesRepId: salesRepId, plansDistributorId: distributorId, plansWeek: week, plansDay: day } = dataItem;

      const outlet = uccOutletMap[ucc];
      if (!outlet) {
        reasons.push(i18n.translate('plan.not_found_outlet'));
      }

      const salesRep = salesRepIdMap[salesRepId];
      if (!salesRep) {
        reasons.push(i18n.translate('plan.not_found_sales_rep'));
      }

      const distributor = distributorIdMap[distributorId];
      if (!distributor) {
        reasons.push(i18n.translate('plan.not_found_distributor'));
      }

      let invalidDateMessage;
      let planDate = weekDayDateMap[`${week}-${day}`];
      if (!planDate) {
        invalidDateMessage = i18n.translate('plan.invalid_schedule_day');
      } else {
        const today = moment().tz(process.env.TZ).startOf('date');
        if (planDate.isBefore(today)) {
          invalidDateMessage = i18n.translate('plan.invalid_schedule_day');
        }
      }

      if (!outlet || !salesRep) {
        failure.push({
          row,
          reasons,
        });
        continue;
      }

      const weekId = weekNameMap[week];
      const existedPlan = salesRepOutletWeekPlanMap[`${salesRep._id}-${String(outlet._id)}-${weekId}`];

      if (existedPlan || excelDataMap?.[`${salesRepId}-${String(outlet._id)}-${week}`] > 1) {
        invalidDateMessage = i18n.translate('plan.existed_time_slot');
      }

      if (existedPlan && existedPlan.visitStatus === VisitStatus.COMPLETED) {
        invalidDateMessage = i18n.translate('plan.cannot_update_executed_plan', {
          args: {
            ucc,
          },
        });
      }

      const availableSlots = availableSlotsMap?.[String(salesRep._id)]?.[week]?.[day];
      if (!availableSlots || !availableSlots.length) {
        invalidDateMessage = i18n.translate('plan.not_available_time_slot');
      }

      if (invalidDateMessage) {
        reasons.push(invalidDateMessage);
      }

      if (reasons.length) {
        failure.push({
          row,
          reasons,
        });
        continue;
      }

      const { hour, minute } = availableSlots[0];
      planDate = planDate.set('hours', hour).set('minutes', minute);

      availableSlotsMap[String(salesRep._id)][week][day] = availableSlots.filter((item) => item.hour !== hour || item.minute !== minute);

      validatedData.push({
        saleRep: salesRep._id,
        outlet: outlet._id,
        week: new Types.ObjectId(weekId),
        visitStatus: VisitStatus.START_VISIT,
        day: planDate.toDate(),
        depotId: outlet?.depotId,
      });
    }

    if (failure.length) {
      return {
        failure,
      };
    }

    await this.planModel.create(validatedData);

    return {
      failure,
    };
  }

  async updateMissReason({ planId, dto }: { planId: string; dto: UpdatePlanMissReasonDto }) {
    await this.planModel.updateOne(
      { _id: new Types.ObjectId(planId) },
      {
        $set: {
          missedReason: new Types.ObjectId(dto.missReasonId),
        },
      },
    );

    return true;
  }

  private async getPlanDetails(plan: OutletJourneyPlanning) {
    const [outlet, week, salesRepDistributorRelation] = await Promise.all([
      this.outletModel.findById(plan.outlet),
      this.weekModel.findById(plan.week),
      this.salesRepDistributorRelationModel.findOne({ user: plan.saleRep }).populate('distributor user'),
    ]);

    const day =
      this.omsWeeksService.getDateAndDayNameInWeek(week)[
        moment(plan.rescheduledDay || plan.day)
          .tz(process.env.TZ)
          .format('DD-MM-YYYY')
      ];
    this._saleRepStatisticService.calculateCallComplianceRateBySale({ salesRepId: plan.saleRep._id, date: moment().startOf('d').toDate() }).then();

    return {
      id: plan._id,
      createdAt: plan.createdAt,
      date: plan.rescheduledDay || plan.day,
      week: {
        id: week._id,
        name: week.weekName.replace('Week ', ''),
        startTime: week.startTime,
      },
      dayInWeek: day,
      outlet: {
        ucc: outlet.ucc,
        name: outlet.name,
      },
      salesRep: {
        salesRepId: salesRepDistributorRelation?.user?.saleRepId,
      },
      distributor: {
        distributorId: salesRepDistributorRelation?.distributor?.distributorId,
        name: salesRepDistributorRelation?.distributor?.distributorName,
      },
      depotId: plan.depotId,
    };
  }

  /*private async getPlansWhenOrderByDay({
    aggregation,
    orderDesc,
    offset,
    limit,
    weekDayNameMap,
  }: {
    aggregation: Aggregate<any[]>;
    orderDesc: string;
    offset: number;
    limit?: number;
    weekDayNameMap: Record<string, Record<string, number>>;
  }) {
    const data = await aggregation.exec();
    const totalItem = data.length;
    const transformedData = data.map((item) => this.transformPlan({ data: item, weekDayNameMap }));
    const sortedData = transformedData.sort((previous, next) => {
      if (orderDesc === 'ASC') {
        return previous.day - next.day;
      }

      return next.day - previous.day;
    });

    let paginatedData = sortedData;
    if (limit) {
      paginatedData = sortedData.slice(offset, offset + limit);
    }

    return {
      totalItem,
      plans: paginatedData,
    };
  }*/

  private transformPlan({ data, weekDayNameMap }: { data: any; weekDayNameMap: Record<string, Record<string, number>> }) {
    const day = moment(data.date).tz(process.env.TZ).format('DD-MM-YYYY');
    const dayName = weekDayNameMap[String(data.week.id)][day];

    return {
      ...data,
      week: {
        id: data.week.id,
        name: data.week.name.replace('Week ', ''),
        startTime: data.week.startTime,
      },
      dayInWeek: dayName,
      visitStatus: data.visitStatus,
    };
  }

  async getPlanData(
    {
      depotId,
      weeks,
      orderBy,
      orderDesc,
      offset,
      limit,
    }: {
      depotId: string;
      weeks: JourneyPlanWeek[];
      orderBy: string;
      orderDesc: string;
      offset: number;
      limit: number;
    },
    i18n: I18nContext,
  ) {
    if (!depotId) {
      return {
        totalItem: 0,
        plans: [],
      };
    }
    const distributorAndSalesRep = await this.getDistributorAndSalesRepByDepotId(depotId);
    const distributor = distributorAndSalesRep?.distributor;
    if (isEmptyObjectOrArray(distributor)) {
      throw new BadRequestException(i18n.translate('plan.not_found_depot'));
    }
    const users = distributorAndSalesRep?.users;
    if (isEmptyObjectOrArray(users)) {
      return {
        totalItem: 0,
        plans: [],
      };
    }
    let sort: any = {
      createdAt: -1,
    };
    if (orderBy) {
      sort = {
        [orderBy]: orderDesc === 'DESC' ? -1 : 1,
      };
    }
    const weekDayNameMap = weeks.reduce(
      (initial, week) => ({
        ...initial,
        [String(week._id)]: this.omsWeeksService.getDateAndDayNameInWeek(week),
      }),
      {} as Record<string, Record<string, number>>,
    );
    const planWiths: any = await this.planModel
      .aggregate([
        {
          $match: {
            week: { $in: weeks.map((w) => w._id) },
            saleRep: { $in: users },
          },
        },
        {
          $lookup: {
            from: 'outlets',
            localField: 'outlet',
            foreignField: '_id',
            as: 'outlet',
          },
        },
        { $unwind: { path: '$outlet', preserveNullAndEmptyArrays: false } },
        { $match: { 'outlet.depotId': depotId } },
        {
          $facet: {
            totalRecords: [{ $count: 'count' }],
            data: [
              { $sort: { ...sort } },
              { $skip: offset },
              { $limit: limit },
              {
                $lookup: {
                  from: 'users',
                  localField: 'saleRep',
                  foreignField: '_id',
                  as: 'salesRep',
                },
              },
              { $unwind: { path: '$salesRep', preserveNullAndEmptyArrays: true } },
              {
                $project: {
                  id: '$_id',
                  createdAt: 1,
                  outlet: {
                    ucc: '$outlet.ucc',
                    name: '$outlet.name',
                    status: '$outlet.status',
                  },
                  salesRep: {
                    salesRepId: '$salesRep.saleRepId',
                  },
                  week: 1,
                  depot: 1,
                  date: { $ifNull: ['$rescheduledDay', '$day'] },
                  visitStatus: 1,
                },
              },
            ],
          },
        },
      ])
      .allowDiskUse(true);

    const [{ totalRecords, data }] = planWiths;
    const planData = data?.map((plan) => {
      const week = weeks.find((w) => w._id.toString() === plan?.week?.toString());
      const planItem = {
        ...plan,
        distributor: { distributorId: distributor.distributorId, name: distributor.distributorName },
        week: {
          id: week._id,
          name: week.weekName,
          startTime: week.startTime,
        },
      };
      return this.transformPlan({ data: planItem, weekDayNameMap });
    });

    const totalItem = !isEmptyObjectOrArray(totalRecords) ? totalRecords[0].count : 0;

    return {
      totalItem,
      plans: planData || [],
    };
  }
  /*private createGetPlansAggregation({ depotId, weeks, salesRepIds }: { depotId: string; weeks: JourneyPlanWeek[]; salesRepIds: any[] }) {
    const weekIds = weeks.map((week) => week._id);

    return this.planModel
      .aggregate()
      .match({
        week: { $in: weekIds },
        saleRep: { $in: salesRepIds },
      })
      .lookup({
        localField: 'outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({
        path: '$outlet',
        preserveNullAndEmptyArrays: false,
      })
      .match({
        'outlet.depotId': depotId,
      })
      .lookup({
        localField: 'saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'salesRepDistributorRelation',
      })
      .unwind({
        path: '$salesRepDistributorRelation',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'salesRep',
      })
      .unwind({
        path: '$salesRep',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'salesRepDistributorRelation.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'distributor',
      })
      .unwind({
        path: '$distributor',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        localField: 'week',
        from: 'journeyplanweeks',
        foreignField: '_id',
        as: 'week',
      })
      .unwind({
        path: '$week',
        preserveNullAndEmptyArrays: false,
      })
      .project({
        id: '$_id',
        createdAt: '$createdAt',
        outlet: {
          ucc: '$outlet.ucc',
          name: '$outlet.name',
          status: '$outlet.status',
        },
        salesRep: {
          salesRepId: '$salesRep.saleRepId',
        },
        week: {
          id: '$week._id',
          name: '$week.weekName',
          startTime: '$week.startTime',
        },
        date: {
          $ifNull: ['$rescheduledDay', '$day'],
        },
        distributor: {
          distributorId: '$distributor.distributorId',
          name: '$distributor.distributorName',
        },
        visitStatus: '$visitStatus',
      });
  }*/

  private async getAvailableTimeSlots({ salesRepIds, weekIds, depotId }: { salesRepIds: string[]; weekIds: string[]; depotId: string }) {
    const weeks = await this.weekModel.find({ _id: { $in: weekIds.map((id) => new Types.ObjectId(id)) } });
    const timeFrame = await this.omsTimeFrameService.getTimeFrame(depotId);
    const { timeInterval, startingTimeFrame } = timeFrame || { timeInterval: 15, startingTimeFrame: '00:00' };
    const hour = Number(startingTimeFrame.slice(0, 2));
    const minute = Number(startingTimeFrame.slice(3, 5));

    const existedPlans = await this.planModel
      .find({
        saleRep: { $in: salesRepIds.map((id) => new Types.ObjectId(id)) },
        week: { $in: weeks.map((week) => week._id) },
      })
      .select('week saleRep day rescheduledDay');

    const today = moment().tz(process.env.TZ);
    // Data will have this shape
    // [salesRepId]: {
    //    [weekName]: {
    //        [day]: [
    //            {
    //              hour: number,
    //              minute: number
    //             }
    //        ]
    //     }
    //  }
    //
    //
    return salesRepIds.reduce((initialSalesRep, saleRepId) => {
      const salesRepData = weeks.reduce((initialWeek, week) => {
        const weekStartTime = moment(week.startTime).tz(process.env.TZ);
        if (weekStartTime.clone().add(7, 'days').isBefore(today)) {
          return initialWeek;
        }

        const plans = existedPlans.filter((plan) => String(plan.week) === String(week._id) && String(plan.saleRep) === String(saleRepId));

        const weekName = week.weekName.replace('Week ', '');

        const dayData = Array.from(Array(7).keys()).reduce((initialDay, day) => {
          const weekDate = weekStartTime.clone().add(day, 'days').startOf('date');

          const dayPlans = plans.filter((plan) =>
            moment(plan.rescheduledDay || plan.day)
              .tz(process.env.TZ)
              .startOf('date')
              .isSame(weekDate),
          );

          const end = 23 + (60 - timeInterval) / 60;
          const start = hour + minute / 60 - minute / 60;
          const availableSlots = Array.from(Array(((end - start) * 60 + timeInterval) / timeInterval).keys()).reduce((initialHour, item) => {
            const date = weekDate
              .clone()
              .add(hour, 'hours')
              .add(minute, 'minutes')
              .add(timeInterval * item, 'minutes');

            const hasPlanInSlot = dayPlans.some((plan) => {
              const planDate = moment(plan.rescheduledDay || plan.day).tz(process.env.TZ);
              const planHour = planDate.hour();
              const planMinute = planDate.minute();

              return planHour === date.hour() && planMinute === date.minute();
            });

            if (hasPlanInSlot) {
              return initialHour;
            }

            return [
              ...initialHour,
              {
                hour: date.hour(),
                minute: date.minute(),
              },
            ];
          }, [] as Array<{ hour: number; minute: number }>);

          return {
            ...initialDay,
            [day + 1]: availableSlots,
          };
        }, {} as Record<string, Array<{ hour: number; minute: number }>>);

        return {
          ...initialWeek,
          [weekName]: dayData,
        };
      }, {} as Record<string, Record<string, Array<{ hour: number; minute: number }>>>);

      return {
        ...initialSalesRep,
        [saleRepId]: salesRepData,
      };
    }, {} as Record<string, Record<string, Record<string, Array<{ hour: number; minute: number }>>>>);
  }
}
