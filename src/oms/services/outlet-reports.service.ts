import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, SortOrder, Types } from 'mongoose';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { GetOmsOutletReportsQuery, OmsOutletReportStatus } from '../queries/get-outlet-reports.query';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { OmsSalesRepsService } from './sales-reps.service';
import * as moment from 'moment-timezone';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import { normalizeQueryHelper } from 'src/shared/helpers';
import { DistributorUserRelation, DistributorUserRelationDocument } from 'src/distributor/schemas';
import { ColdStock, ColdStockDocument } from 'src/journey-plannings/schemas/cold-stock.schema';
import { Files, FilesDocument } from 'src/files/schemas';
import { VisibilityExecution, VisibilityExecutionDocument } from 'src/journey-plannings/schemas/visibility-execution.schema';
import { SaleRepExecutionVisibility, SaleRepExecutionVisibilityDocument } from 'src/sale-rep/schemas';
import { OmsCacheData, OmsCacheDataDocument } from 'src/external/schemas/oms-cache-data.schema';
import { OmsPlansService } from './plans.service';
import { isEmptyObjectOrArray } from '../../utils';
import { OutletStatus } from '../../outlets/enums/outlet-status.enum';
import { SalesRepStatus } from '../../users/enums';

@Injectable()
export class OmsOutletReportsService {
  constructor(
    @InjectModel(OutletJourneyPlanning.name) private readonly planModel: Model<OutletJourneyPlanningDocument>,
    @InjectModel(DistributorUserRelation.name) private readonly salesRepDistributorRelationModel: Model<DistributorUserRelationDocument>,
    @InjectModel(ColdStock.name) private readonly coldStockModel: Model<ColdStockDocument>,
    @InjectModel(SaleRepExecutionVisibility.name) private readonly visibilityModel: Model<SaleRepExecutionVisibilityDocument>,
    @InjectModel(VisibilityExecution.name) private readonly visibilityExecutionModel: Model<VisibilityExecutionDocument>,
    @InjectModel(Files.name) private readonly fileModel: Model<FilesDocument>,
    @InjectModel(OmsCacheData.name) private readonly omsCacheDataModel: Model<OmsCacheDataDocument>,
    private readonly omsSalesRepsService: OmsSalesRepsService,
    private readonly omsPlansService: OmsPlansService,
  ) {}

  transformTimeRangeQuery({ month, year }: Pick<GetOmsOutletReportsQuery, 'month' | 'year'>) {
    const queryMonth = Number(month - 1);

    const from = moment().set('month', queryMonth).set('year', year).tz(process.env.TZ).startOf('month').startOf('date').toDate();
    const to = moment().set('month', queryMonth).set('year', year).tz(process.env.TZ).endOf('month').endOf('date').toDate();
    return {
      from,
      to,
    };
  }

  async getOutletReports(query: GetOmsOutletReportsQuery & PaginationParams & OrderParams) {
    if (query.status === OmsOutletReportStatus.SKIPPED) {
      return this.getSkippedPlans(query);
    }

    return this.getVisitedPlans(query);
  }

  private async getVisitedPlans(query: GetOmsOutletReportsQuery & PaginationParams & OrderParams) {
    const offset = query.offset ? +query.offset : 0;
    const limit = query.limit ? +query.limit : 10;
    // const salesReps = await this.omsSalesRepsService.getByDepot(query.depotId);
    const salesRepIds = await this.omsPlansService.getDistributorAndSalesRepByDepotId(query.depotId);
    if (isEmptyObjectOrArray(salesRepIds)) {
      return {
        totalReport: 0,
        reports: [],
      };
    }

    let sort: Record<string, SortOrder> = {
      createdAt: -1,
    };
    if (query.orderBy) {
      sort = {
        [query.orderBy]: query.orderDesc === 'DESC' ? -1 : 1,
      };
    }

    const { from, to } = this.transformTimeRangeQuery({ month: query.month, year: query.year });

    const aggregation = this.planModel
      .aggregate()
      .allowDiskUse(true)
      .match({
        saleRep: { $in: salesRepIds.users.map((salesRep) => salesRep) },
        visitStatus: VisitStatus.COMPLETED,
        visitedDay: { $gte: from, $lte: to },
      })
      .lookup({
        localField: 'outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({
        preserveNullAndEmptyArrays: false,
        path: '$outlet',
      })
      .match({
        'outlet.depotId': query.depotId,
      });

    if (query.search) {
      const normalizedQuery = normalizeQueryHelper(query.search);
      aggregation.match({
        $or: [
          {
            'outlet.ucc': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
          {
            'outlet.name': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
        ],
      });
    }

    aggregation
      .lookup({
        localField: 'saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'salesRep',
      })
      .unwind({
        preserveNullAndEmptyArrays: false,
        path: '$salesRep',
      })
      .project({
        id: '$_id',
        status: OmsOutletReportStatus.VISITED,
        outlet: {
          name: '$outlet.name',
          ucc: '$outlet.ucc',
        },
        salesRep: {
          salesRepId: '$salesRep.saleRepId',
          name: '$salesRep.username',
        },
        plannedDate: { $ifNull: ['$rescheduledDay', '$day'] },
        visitedDate: '$visitedDay',
        startTime: {
          $dateToString: {
            format: '%H:%M',
            date: '$startVisitDate',
            timezone: process.env.TZ,
          },
        },
        endTime: {
          $dateToString: {
            format: '%H:%M',
            date: '$visitedDay',
            timezone: process.env.TZ,
          },
        },
      })
      .sort(sort)
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: offset,
          },
          {
            $limit: limit,
          },
        ],
      });

    const [{ totalRecords, data }] = await aggregation.exec();
    const totalReport = totalRecords.length ? totalRecords[0].total : 0;
    const reports = data?.map((row) => {
      return {
        ...row,
        distributor: { distributorId: salesRepIds.distributor.distributorId, name: salesRepIds.distributor.distributorName },
      };
    });
    return {
      totalReport,
      reports: reports || [],
    };
  }

  private async getSkippedPlans(query: GetOmsOutletReportsQuery & PaginationParams & OrderParams) {
    const offset = query.offset ? +query.offset : 0;
    const limit = query.limit ? +query.limit : 10;
    // const salesReps = await this.omsSalesRepsService.getByDepot(query.depotId);
    const salesRepIds = await this.omsPlansService.getDistributorAndSalesRepByDepotId(query.depotId);
    if (isEmptyObjectOrArray(salesRepIds)) {
      return {
        totalReport: 0,
        reports: [],
      };
    }

    let sort: Record<string, SortOrder> = {
      createdAt: -1,
    };
    if (query.orderBy) {
      sort = {
        [query.orderBy]: query.orderDesc === 'DESC' ? -1 : 1,
      };
    }

    const { from, to } = this.transformTimeRangeQuery({ month: query.month, year: query.year });

    const aggregation = this.planModel
      .aggregate()
      .allowDiskUse(true)
      .match({
        saleRep: { $in: salesRepIds.users.map((salesRep) => salesRep) },
        visitStatus: VisitStatus.START_VISIT,
        missedReason: { $exists: true },
        $or: [
          {
            rescheduled: false,
            day: {
              $gte: from,
              $lte: to,
            },
          },
          {
            rescheduled: true,
            rescheduledDay: {
              $gte: from,
              $lte: to,
            },
          },
        ],
      })
      .lookup({
        localField: 'outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({
        preserveNullAndEmptyArrays: false,
        path: '$outlet',
      })
      .match({
        'outlet.depotId': query.depotId,
      });

    if (query.search) {
      const normalizedQuery = normalizeQueryHelper(query.search);
      aggregation.match({
        $or: [
          {
            'outlet.ucc': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
          {
            'outlet.name': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
        ],
      });
    }

    aggregation
      .lookup({
        localField: 'saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'salesRep',
      })
      .unwind({
        preserveNullAndEmptyArrays: false,
        path: '$salesRep',
      })
      .lookup({
        localField: 'missedReason',
        from: 'missreasons',
        foreignField: '_id',
        as: 'missedReason',
      })
      .unwind({
        preserveNullAndEmptyArrays: true,
        path: '$missedReason',
      })
      .lookup({
        localField: '_id',
        from: 'journeyplanmissedreasonhistories',
        foreignField: 'journeyPlan',
        as: 'missReasonHistory',
      })
      .project({
        id: '$_id',
        status: OmsOutletReportStatus.SKIPPED,
        outlet: {
          name: '$outlet.name',
          ucc: '$outlet.ucc',
        },
        salesRep: {
          salesRepId: '$salesRep.saleRepId',
          name: '$salesRep.username',
        },
        plannedDate: { $ifNull: ['$rescheduledDay', '$day'] },
        missReason: {
          $cond: [
            {
              $or: [
                {
                  $eq: ['$missedReason.controllable', true],
                },
                {
                  $eq: ['$missedReason.controllable', false],
                },
              ],
            },
            {
              id: '$missedReason._id',
              uncontrollable: {
                $cond: [{ $eq: ['$missedReason.controllable', true] }, false, true],
              },
            },
            null,
          ],
        },
        missReasonHistory: 1,
        evidenceImages: 1,
      })
      .sort(sort)
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: offset,
          },
          {
            $limit: limit,
          },
        ],
      });

    const [{ totalRecords, data }] = await aggregation.exec();
    const totalReport = totalRecords.length ? totalRecords[0].total : 0;

    const reports = await Promise.all(
      data?.map(async ({ missReasonHistory, ...item }) => {
        const sortedMissReasonHistory = missReasonHistory.sort((pre, next) => +next.createdAt - +pre.createdAt);
        const latestMissReasonHistory = sortedMissReasonHistory[0];
        const files = await this.fileModel.find({ _id: { $in: latestMissReasonHistory.evidenceImages } });
        const evidences = files?.map((item) => {
          let url = item.path;
          if (!url.startsWith('https')) {
            url = `${process.env.BASE_URL}/${item.path}`;
          }
          return {
            id: String(item._id),
            url,
          };
        });

        return {
          ...item,
          evidences,
          distributor: { distributorId: salesRepIds.distributor.distributorId, name: salesRepIds.distributor.distributorName },
        };
      }),
    );

    return {
      totalReport,
      reports: reports || [],
    };
  }

  private initCounterAggregation(search?: string, match?: any) {
    const aggregation = this.planModel
      .aggregate()
      .match(match)
      .lookup({
        localField: 'outlet',
        from: 'outlets',
        foreignField: '_id',
        as: 'outlet',
      })
      .unwind({
        preserveNullAndEmptyArrays: false,
        path: '$outlet',
      });

    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      aggregation.match({
        $or: [
          {
            'outlet.ucc': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
          {
            'outlet.name': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
          },
        ],
      });
    }

    return aggregation;
  }

  async countVisitByStatusNew(query: GetOmsOutletReportsQuery & PaginationParams & OrderParams, reportData: any) {
    if (query.status === OmsOutletReportStatus.SKIPPED) {
      const visitedData = await this.getVisitedPlans(query);
      return {
        visited: visitedData?.totalReport || 0,
        skipped: reportData?.totalReport || 0,
      };
    } else {
      const skippedData = await this.getSkippedPlans(query);
      return {
        visited: reportData?.totalReport || 0,
        skipped: skippedData?.totalReport || 0,
      };
    }
  }

  async countVisitByStatus({ depotId, month, year, search }: GetOmsOutletReportsQuery) {
    // const salesReps = await this.omsSalesRepsService.getByDepot(depotId);
    const visitedTimeRange = this.transformTimeRangeQuery({ month, year });

    const countVisited = await this.initCounterAggregation(search, {
      // saleRep: { $in: salesReps.map((salesRep) => salesRep.id) },
      visitStatus: VisitStatus.COMPLETED,
      visitedDay: { $gte: visitedTimeRange.from, $lte: visitedTimeRange.to },
    }).count('id');

    const skippedTimeRange = this.transformTimeRangeQuery({ month, year });
    const countSkipped = await this.initCounterAggregation(search, {
      // saleRep: { $in: salesReps.map((salesRep) => salesRep.id) },
      visitStatus: VisitStatus.START_VISIT,
      missedReason: { $exists: true },
      $or: [
        {
          rescheduled: false,
          day: {
            $gte: skippedTimeRange.from,
            $lte: skippedTimeRange.to,
          },
        },
        {
          rescheduled: true,
          rescheduledDay: {
            $gte: skippedTimeRange.from,
            $lte: skippedTimeRange.to,
          },
        },
      ],
    }).count('id');

    return {
      visited: countVisited[0]?.id || 0,
      skipped: countSkipped[0]?.id || 0,
    };
  }

  async getOutletReportDetails(planId: string) {
    const plan = await this.planModel.findById(planId).populate([
      {
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      },
      {
        path: 'saleRep',
        match: { saleRepStatus: SalesRepStatus.ACTIVE },
      },
    ]);

    const [salesRepDistributorRelation, coldStock, visibility, omsData] = await Promise.all([
      this.salesRepDistributorRelationModel
        .findOne({
          user: plan.saleRep._id,
        })
        .populate('distributor'),
      this.coldStockModel
        .findOne({
          journeyPlan: plan._id,
        })
        .populate('images'),
      this.visibilityModel.findOne({
        journeyPlan: plan._id,
      }),
      this.omsCacheDataModel
        .findOne({
          outlet: plan.outlet._id,
        })
        .select(['products']),
    ]);

    const visibilityExecutionIds = Object.keys(visibility?.tasks || {}).map((id) => new Types.ObjectId(id));
    const fileIds = Object.values(visibility?.tasks || {}).reduce((pre, curr) => [...pre, ...curr.map((item) => new Types.ObjectId(item))], []);

    const [visibilityExecutions, files] = await Promise.all([
      this.visibilityExecutionModel.find({ _id: { $in: visibilityExecutionIds } }),
      this.fileModel.find({ _id: { $in: fileIds } }),
    ]);

    const products = (omsData?.products || []).map((product: any) => {
      const productData = {
        id: product.sku,
        name: product.title,
        image: product.thumbnail,
      };
      const checkStockProduct = plan.checkStock?.listProductsChecked.find((item) => item.sku === product.sku);
      if (checkStockProduct && (checkStockProduct.check_stock_quantity || checkStockProduct.selling_price)) {
        return { ...productData, stockCount: checkStockProduct.check_stock_quantity || 0, available: true, selling_price: checkStockProduct.selling_price || 0 };
      }
      return { ...productData, stockCount: 0, available: false, selling_price: 0 };
    });

    return {
      id: plan._id,
      status: plan.visitStatus === VisitStatus.COMPLETED ? OmsOutletReportStatus.VISITED : OmsOutletReportStatus.SKIPPED,
      plannedDate: plan.rescheduledDay || plan.day,
      visitedDate: plan.visitedDay,
      startTime: moment(plan.startVisitDate).tz(process.env.TZ).format('hh:mm'),
      endTime: moment(plan.visitedDay).tz(process.env.TZ).format('hh:mm'),
      location: plan.location ? `${plan.location.latitude}, ${plan.location.longitude}` : null,
      outlet: {
        id: plan.outlet._id,
        name: plan.outlet.name,
        ucc: plan.outlet.ucc,
      },
      salesRep: {
        id: plan.saleRep._id,
        salesRepId: plan.saleRep.saleRepId,
        name: plan.saleRep.username,
      },
      distributor: {
        distributorId: salesRepDistributorRelation.distributor.distributorId,
        name: salesRepDistributorRelation.distributor.distributorName,
      },
      checkStock: products,
      coldStock: {
        fridgeAvailable: coldStock?.availableFridge,
        dedicatedFridge: coldStock?.isDedicatedFridge,
        productsInDedicatedFridge: coldStock?.isProductsInDedicatedFridge,
        fridgeImages: (coldStock?.images || []).map((item) => {
          let url = item.path;
          if (!url.startsWith('https')) {
            url = `${process.env.BASE_URL}/${item.path}`;
          }

          return {
            id: String(item._id),
            url,
          };
        }),
      },
      visibility: visibilityExecutions.map((visibilityExecution) => {
        const visibilityExecutionId = String(visibilityExecution._id);
        const visibilityExecutionFiles = files.filter((file) => (visibility?.tasks || {})[visibilityExecutionId].map(String).includes(String(file._id)));

        return {
          id: visibilityExecutionId,
          title: visibilityExecution.name,
          subHeading: visibilityExecution.subHeading,
          images: visibilityExecutionFiles.map((item) => {
            let url = item.path;
            if (!url.startsWith('https')) {
              url = `${process.env.BASE_URL}/${item.path}`;
            }

            return {
              id: String(item._id),
              url,
            };
          }),
        };
      }),
    };
  }
}
