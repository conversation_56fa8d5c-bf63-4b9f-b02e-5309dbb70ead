import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { OpCos } from 'src/config';
import { SaleRepStatistic, SaleRepStatisticDocument } from 'src/sale-rep/schemas';
import { OutletJourneyPlanningService } from '../../journey-plannings/services/outlet-journey-planning.service';
import { SaleRepOutletRelationService } from '../../outlets/services/sale-rep-outlet-relation.service';
import { OrdersService } from '../../orders/services/orders.service';
import { isEmptyObjectOrArray } from '../../utils';
import { DistributorUserRelationService } from '../../distributor/services';
import { ChartReportingService } from '../../external/services/chart-reporting.service';
import { I18nContext } from 'nestjs-i18n';
import { ConstantCommons } from '../../utils/constants';
import { VisitStatus } from '../../journey-plannings/enums/visit-status.enum';

@Injectable()
export class OmsSalesRepStatisticsService {
  constructor(
    @InjectModel(SaleRepStatistic.name) private readonly salesRepStatisticModel: Model<SaleRepStatisticDocument>,
    @Inject(forwardRef(() => OutletJourneyPlanningService))
    private readonly _outletJourneyPlanningService: OutletJourneyPlanningService,
    @Inject(forwardRef(() => SaleRepOutletRelationService))
    private readonly _saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly _ordersService: OrdersService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    @Inject(forwardRef(() => ChartReportingService))
    private readonly _chartReportingService: ChartReportingService,
  ) {}

  async getStatistics(salesRepId: string) {
    const statisticDay = moment().tz(process.env.TZ).endOf('day');
    const month = statisticDay.clone().toDate().getMonth() + 1;
    const year = statisticDay.clone().toDate().getFullYear();

    const statisticQueryData = await this.salesRepStatisticModel
      .aggregate()
      .match({
        statisticDay: statisticDay.toDate(),
        saleRep: new Types.ObjectId(salesRepId),
      })
      .lookup({
        localField: 'saleRep',
        from: 'dsrtargets',
        foreignField: 'saleRep',
        as: 'dsrTarget',
      })
      .project({
        document: '$$ROOT',
        dsrTarget: {
          $filter: {
            input: '$dsrTarget',
            as: 'item',
            cond: {
              $and: [{ $eq: ['$$item.month', month] }, { $eq: ['$$item.year', year] }],
            },
          },
        },
      })
      .unwind({
        path: '$dsrTarget',
        preserveNullAndEmptyArrays: true,
      })
      .project({
        callComplianceRate: {
          numerator: { $ifNull: ['$document.cpsrValue', 0] },
          denominator: { $ifNull: ['$document.cpsrTarget', 0] },
          target: { $ifNull: ['$dsrTarget.callComplianceRate', 0] },
        },
        callEffectiveness: {
          numerator: { $ifNull: ['$document.callEffectivenessValue', 0] },
          denominator: { $ifNull: ['$document.callEffectivenessTarget', 0] },
          target: { $ifNull: ['$dsrTarget.callEffectiveness', 0] },
        },
        salesVolume: {
          current: { $ifNull: ['$document.salesVolumeValue', 0] },
          target: { $ifNull: ['$dsrTarget.salesVolume', 0] },
        },
        salesValue: {
          current: { $ifNull: ['$document.salesValue', 0] },
          target: { $ifNull: ['$dsrTarget.salesTarget', 0] },
        },
        activeSellingOutlet: {
          numerator: { $ifNull: ['$document.maboValue', 0] },
          denominator: { $ifNull: ['$document.maboTarget', 0] },
          target: { $ifNull: ['$dsrTarget.maboTarget', 0] },
        },
        availability: {
          numerator: { $ifNull: ['$document.availabilityValue', 0] },
          denominator: { $ifNull: ['$document.availabilityTarget', 0] },
          target: { $ifNull: ['$dsrTarget.availability', 0] },
        },
        visibility: {
          numerator: { $ifNull: ['$document.visibilityValue', 0] },
          denominator: { $ifNull: ['$document.visibilityTarget', 0] },
          target: { $ifNull: ['$dsrTarget.visibility', 0] },
        },
      });
    const { country, currency } = this.getCountryCurrency();

    if (!statisticQueryData.length) {
      const defaultPercentData = {
        numerator: 0,
        denominator: 0,
        target: 0,
        percent: 0,
      };
      const defaultAbsoluteNumberData = {
        current: 0,
        target: 0,
        percent: 0,
      };

      return {
        callComplianceRate: defaultPercentData,
        callEffectiveness: defaultPercentData,
        salesVolume: {
          ...defaultAbsoluteNumberData,
          unit: 'HL',
        },
        salesValue: { ...defaultAbsoluteNumberData, country, currency },
        activeSellingOutlet: defaultPercentData,
        availability: defaultPercentData,
        visibility: defaultPercentData,
      };
    }

    const { callComplianceRate, callEffectiveness, salesVolume, salesValue, activeSellingOutlet, availability, visibility } = statisticQueryData[0];

    return {
      callComplianceRate: {
        ...callComplianceRate,
        percent: this.calculatePercent(callComplianceRate.numerator, callComplianceRate.denominator),
      },
      callEffectiveness: {
        ...callEffectiveness,
        percent: this.calculatePercent(callEffectiveness.numerator, callEffectiveness.denominator),
      },
      salesVolume: {
        ...salesVolume,
        percent: this.calculatePercent(salesVolume.current, salesVolume.target),
        unit: 'HL',
      },
      salesValue: {
        ...salesValue,
        percent: this.calculatePercent(salesValue.current, salesValue.target),
        country,
        currency,
      },
      activeSellingOutlet: {
        ...activeSellingOutlet,
        percent: this.calculatePercent(activeSellingOutlet.numerator, activeSellingOutlet.denominator),
      },
      availability: {
        ...availability,
        percent: this.calculatePercent(availability.numerator, availability.denominator),
      },
      visibility: {
        ...visibility,
        percent: this.calculatePercent(visibility.numerator, visibility.denominator),
      },
    };
  }

  async getStatisticE360(salesRepId: string, fromDate: Date, toDate: Date, i18n: I18nContext) {
    const statisticDay = moment(toDate).tz(process.env.TZ).endOf('day');
    const month = statisticDay.clone().toDate().getMonth() + 1;
    const year = statisticDay.clone().toDate().getFullYear();
    const statisticQueryData = await this.salesRepStatisticModel
      .aggregate()
      .match({
        statisticDay: statisticDay.toDate(),
        saleRep: new Types.ObjectId(salesRepId),
      })
      .lookup({
        localField: 'saleRep',
        from: 'dsrtargets',
        foreignField: 'saleRep',
        as: 'dsrTarget',
      })
      .project({
        document: '$$ROOT',
        dsrTarget: {
          $filter: {
            input: '$dsrTarget',
            as: 'item',
            cond: {
              $and: [{ $eq: ['$$item.month', month] }, { $eq: ['$$item.year', year] }],
            },
          },
        },
      })
      .unwind({
        path: '$dsrTarget',
        preserveNullAndEmptyArrays: true,
      })
      .project({
        callComplianceRate: {
          numerator: { $ifNull: ['$document.cpsrValue', 0] },
          denominator: { $ifNull: ['$document.cpsrTarget', 0] },
          target: { $ifNull: ['$dsrTarget.callComplianceRate', 0] },
        },
        callEffectiveness: {
          numerator: { $ifNull: ['$document.callEffectivenessValue', 0] },
          denominator: { $ifNull: ['$document.callEffectivenessTarget', 0] },
          target: { $ifNull: ['$dsrTarget.callEffectiveness', 0] },
        },
        salesVolume: {
          current: { $ifNull: ['$document.salesVolumeValue', 0] },
          target: { $ifNull: ['$dsrTarget.salesVolume', 0] },
        },
        salesValue: {
          current: { $ifNull: ['$document.salesValue', 0] },
          target: { $ifNull: ['$dsrTarget.salesTarget', 0] },
        },
        activeSellingOutlet: {
          numerator: { $ifNull: ['$document.maboValue', 0] },
          denominator: { $ifNull: ['$document.maboTarget', 0] },
          target: { $ifNull: ['$dsrTarget.maboTarget', 0] },
        },
        availability: {
          numerator: { $ifNull: ['$document.availabilityValue', 0] },
          denominator: { $ifNull: ['$document.availabilityTarget', 0] },
          target: { $ifNull: ['$dsrTarget.availability', 0] },
        },
        visibility: {
          numerator: { $ifNull: ['$document.visibilityValue', 0] },
          denominator: { $ifNull: ['$document.visibilityTarget', 0] },
          target: { $ifNull: ['$dsrTarget.visibility', 0] },
        },
      });
    const { country, currency } = this.getCountryCurrency();

    if (!statisticQueryData.length) {
      const defaultPercentData = {
        numerator: 0,
        denominator: 0,
        target: 0,
        percent: 0,
      };
      const defaultAbsoluteNumberData = {
        current: 0,
        target: 0,
        percent: 0,
      };

      return {
        salesVolume: {
          ...defaultAbsoluteNumberData,
          unit: 'HL',
        },
        salesValue: { ...defaultAbsoluteNumberData, country, currency },
        activeSellingOutlet: {
          ...defaultPercentData,
          outletUniverse: { active: 0, inactive: 0, total: 0 },
        },
        callComplianceRate: { ...defaultPercentData, completedVisit: 0, remainingVisit: 0, missedOutlet: [] },
        callEffectiveness: defaultPercentData,
        availability: defaultPercentData,
        visibility: defaultPercentData,
        inVisitAndAdhocOrder: {},
      };
    }

    const { callComplianceRate, callEffectiveness, salesVolume, salesValue, activeSellingOutlet, availability, visibility } = statisticQueryData[0];
    const allVisit = await this.getStatisticOutletPlans(salesRepId, fromDate, toDate, ConstantCommons.ALL, 0, 5000, i18n, true);
    const completedVisit = allVisit.data.filter((jp) => jp.visitStatus === VisitStatus.COMPLETED);
    const missedOutlet = allVisit.data.filter((jp) => jp.isMissed);
    const outletUniverse = await this.getStatisticOutletUniverse(salesRepId, fromDate, toDate);
    return {
      salesVolume: {
        ...salesVolume,
        percent: this.calculatePercent(salesVolume.current, salesVolume.target),
        unit: 'HL',
      },
      salesValue: {
        ...salesValue,
        percent: this.calculatePercent(salesValue.current, salesValue.target),
        country,
        currency,
      },
      activeSellingOutlet: {
        ...activeSellingOutlet,
        denominator: outletUniverse.total,
        percent: this.calculatePercent(activeSellingOutlet.numerator, outletUniverse.total),
        outletUniverse,
      },
      callComplianceRate: {
        ...callComplianceRate,
        percent: this.calculatePercent(callComplianceRate.numerator, callComplianceRate.denominator),
        completedVisitTotal: completedVisit?.length,
        remainingVisitTotal: allVisit.totalRecords - (completedVisit?.length + missedOutlet?.length),
        missedOutletTotal: missedOutlet?.length,
        missedOutlet: { totalRecords: missedOutlet?.length, data: missedOutlet?.slice(0, 3) },
      },
      callEffectiveness: {
        ...callEffectiveness,
        percent: this.calculatePercent(callEffectiveness.numerator, callEffectiveness.denominator),
      },
      availability: {
        ...availability,
        percent: this.calculatePercent(availability.numerator, availability.denominator),
      },
      visibility: {
        ...visibility,
        percent: this.calculatePercent(visibility.numerator, visibility.denominator),
      },
      inVisitAndAdhocOrder: await this.getInVisitAndAdhocOrder(salesRepId, fromDate, toDate),
      dateFilter: { fromDate, toDate },
    };
  }

  private getCountryCurrency() {
    const opco = process.env.OPCO;

    switch (opco) {
      case OpCos.Malaysia: {
        return {
          country: 'MY',
          currency: 'MYR',
        };
      }
      case OpCos.Indonesia: {
        return {
          country: 'ID',
          currency: 'IDR',
        };
      }
      case OpCos.Cambodia: {
        return {
          country: 'KH',
          currency: 'KHR',
        };
      }
      case OpCos.Myanmar: {
        return {
          country: 'MM',
          currency: 'MMK',
        };
      }
    }
  }

  private calculatePercent(numerator: number, denominator: number) {
    if (!denominator) {
      return 0;
    }

    return (numerator * 100) / denominator;
  }

  async getStatisticInactiveOutlet(salesRepId: string, startDate: Date, endDate: Date, skip = 0, limit = 1000) {
    const outlets = await this._saleRepOutletRelationService.getOutletsBySaleRepUUIDs([salesRepId], skip, limit || 5000, { createdAt: -1 }, null, startDate, endDate);
    if (!isEmptyObjectOrArray(outlets)) {
      return this._ordersService.getInactiveOutlets(new Types.ObjectId(salesRepId), outlets[0].data, startDate, endDate);
    }
    return [];
  }

  async getStatisticOutletUniverse(salesRepId: string, startDate: Date, endDate: Date) {
    const outlets = await this._saleRepOutletRelationService.getOutletsBySaleRepUUIDs([salesRepId], 0, 5000, { createdAt: -1 }, null);
    const outletHasNotOrder = !isEmptyObjectOrArray(outlets)
      ? await this._ordersService.getOutletHasNotOrder(new Types.ObjectId(salesRepId), outlets[0].data, startDate, endDate)
      : [];
    return {
      total: !isEmptyObjectOrArray(outlets) ? outlets[0].data?.length : 0,
      active: !isEmptyObjectOrArray(outlets) ? outlets[0].data?.length - outletHasNotOrder?.length : 0,
      inactive: outletHasNotOrder?.length,
    };
  }

  async getStatisticMissedOutlet(salesRepId: string, skip = 0, limit = 5000, i18n: I18nContext) {
    const [userDisRelation] = await this._distributorUserRelationService.getDistributorUserRelation({
      'u._id': new Types.ObjectId(salesRepId),
    });
    return await this._outletJourneyPlanningService.findPlannedVisitedOutletBySaleRep(
      {
        saleRepId: salesRepId,
        distributorId: userDisRelation?.dis?._id,
        dayInMonth: new Date(),
        filterVisitedStatus: ConstantCommons.MISSED,
        skip: skip > 0 ? skip : 0,
        limit: limit > 0 ? limit : 5000,
      },
      i18n,
    );
  }

  async getInVisitAndAdhocOrder(salesRepId: string, startDate: Date, endDate: Date) {
    const allJP = await this._outletJourneyPlanningService.getAllPlansBySaleAndDate(salesRepId, startDate, endDate);
    if (!isEmptyObjectOrArray(allJP)) {
      const listSalesrep: any[] = Array.from(new Set(allJP.map((plan) => plan.saleRep._id)));
      return await this._chartReportingService.calculateOrders(listSalesrep, startDate, endDate);
    }
    return null;
  }

  async getStatisticOutletPlans(
    salesRepId: string,
    startDate: Date,
    endDate: Date,
    filterVisitedStatus = ConstantCommons.ALL,
    skip = 0,
    limit = 1000,
    i18n: I18nContext,
    noStatistic = false,
  ) {
    const [userDisRelation] = await this._distributorUserRelationService.getDistributorUserRelation({
      'u._id': new Types.ObjectId(salesRepId),
    });
    return await this._outletJourneyPlanningService.findPlannedVisitedOutletBySaleRep(
      {
        saleRepId: salesRepId,
        distributorId: userDisRelation?.dis?._id,
        firstDay: startDate,
        lastDay: endDate,
        filterVisitedStatus,
        skip: skip > 0 ? skip : 0,
        limit: limit > 0 ? limit : 5000,
      },
      i18n,
      noStatistic,
    );
  }
}
