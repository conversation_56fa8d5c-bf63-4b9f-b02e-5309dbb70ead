import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { OmsNotificationsService } from '../services/notifications.service';
import { PaginationParams } from 'src/shared/common-params/pagination.params';
import { OrderParams } from 'src/orders/dtos/order-filter.dto';
import { ApiResponse } from 'src/shared/response/api-response';
import { CreateOmsNotificationDto } from '../dto/create-oms-notification.dto';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { User } from 'src/users/schemas/user.schema';
import { JwtAuthGuard } from 'src/auth/decorator/guard/jwt-auth.guard';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { ConstantRoles } from 'src/utils/constants/role';

@Controller('api/oms/notifications')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
export class OmsNotificationsController {
  constructor(private readonly omsNotificationService: OmsNotificationsService) {}

  @Get()
  async getNotifications(@Query() query: { depotId: string } & PaginationParams & OrderParams) {
    const data = await this.omsNotificationService.getNotifications(query);
    return new ApiResponse(data);
  }

  @Post()
  async createNotification(@Body() dto: CreateOmsNotificationDto, @CurrentUser() user: User) {
    const data = await this.omsNotificationService.createNotification({ actorId: user._id, dto });
    return new ApiResponse(data);
  }
}
