import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { VisibilityExecution, VisibilityExecutionSchema } from 'src/journey-plannings/schemas/visibility-execution.schema';
import { Outlet, OutletSchema } from 'src/outlets/schemas/outlet.schema';
import { OmsAuthService } from './services/auth.service';
import { OmsAuthController } from './controllers/auth.controller';
import { HttpModule } from '@nestjs/axios';
import { UserAdmin, UserAdminSchema } from 'src/users/schemas/user-admin.schema';
import { OmsOutletsService } from './services/outlets.service';
import { OmsOutletsController } from './controllers/outlets.controller';
import { UsersModule } from 'src/users/users.module';
import { FilesModule } from 'src/files/files.module';
import { OmsVisibilityExecutionsService } from './services/visibility-executions.service';
import { OmsVisibilityExecutionsController } from './controllers/visibility-executions.controller';
import { DSRTarget, DSRTargetSchema } from 'src/dsr-targets/schemas';
import { SaleRepOutletRelation, SaleRepOutletRelationSchema } from 'src/outlets/schemas/sale-rep-outlet-relation.schema';
import { OmsTargetSettingsService } from './services/target-settings.service';
import { OmsTargetSettingsController } from './controllers/target-settings.controller';
import { OmsFilesService } from './services/files.service';
import { User, UserSchema } from 'src/users/schemas/user.schema';
import { NotificationLog, NotificationLogSchema, PushNotification, PushNotificationSchema } from 'src/message/schemas';
import { OmsNotificationsService } from './services/notifications.service';
import { OmsNotificationsController } from './controllers/notifications.controller';
import {
  BaseJourneyPlanSetting,
  BaseJourneyPlanSettingSchema,
  Distributor,
  DistributorSchema,
  DistributorUserRelation,
  DistributorUserRelationSchema,
} from 'src/distributor/schemas';
import { MessageModule } from 'src/message/message.module';
import { OmsSalesRepsService } from './services/sales-reps.service';
import { OmsSalesRepsController } from './controllers/sales-reps.controller';
import { OmsCyclesService } from './services/cycles.service';
import { OmsWeeksService } from './services/weeks.service';
import { OmsPlansService } from './services/plans.service';
import { OmsCyclesController } from './controllers/cycles.controller';
import { OmsWeeksController } from './controllers/weeks.controller';
import { OmsPlansController } from './controllers/plans.controller';
import { JourneyPlanCycle, JourneyPlanCycleSchema } from 'src/journey-plannings/schemas/journey-plan-cycle.schema';
import { JourneyPlanWeek, JourneyPlanWeekSchema } from 'src/journey-plannings/schemas/journey-plan-week.schema';
import { OutletJourneyPlanning, OutletJourneyPlanningSchema } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { OmsDistributorsService } from './services/distributors.service';
import { OmsTimeFramesService } from './services/time-frames.service';
import { OmsTimeFramesController } from './controllers/time-frames.controller';
import { OmsSalesRepReportsOverviewService } from './services/rep-reports-overview.service';
import { OmsSalesRepReportsChecklistsService } from './services/rep-reports-checklists.service';
import { OmsSalesRepReportsController } from './controllers/sales-rep-reports.controller';
import { SaleRepExecutionVisibility, SaleRepExecutionVisibilitySchema, SaleRepStatistic, SaleRepStatisticSchema } from 'src/sale-rep/schemas';
import { OmsOutletReportsService } from './services/outlet-reports.service';
import { OmsOutletReportsController } from './controllers/outlet-reports.controller';
import { MissReason, MissReasonSchema } from 'src/miss-reasons/schemas/miss-reason.schema';
import { OmsMissReasonsService } from './services/miss-reasons.service';
import { OmsMissReasonsController } from './controllers/miss-reasons.controller';
import { ColdStock, ColdStockSchema } from 'src/journey-plannings/schemas/cold-stock.schema';
import { Files, FilesSchema } from 'src/files/schemas';
import { OmsCacheData, OmsCacheDataSchema } from 'src/external/schemas/oms-cache-data.schema';
import { OmsExportOutletReportsService } from './services/export-outlet-reports.service';
import { OmsRepReportsCalculatorsService } from './services/rep-reports-calculator.service';
import { SaleRepModule } from 'src/sale-rep/sale-rep.module';
import { OmsSalesRepStatisticsService } from './services/sales-rep-statistics.service';
import { ExternalModule } from 'src/external/external.module';
import { SalesRepStatisticController } from './controllers/sales-rep-statistic.controller';
import { SalesRepExternalStatisticsService } from './services/sales-rep-external-statistics.service';
import { OrdersModule } from 'src/orders/orders.module';
import { SettingsModule } from 'src/settings/settings.module';
import { OutletsModule } from '../outlets/outlets.module';
import { JourneyPlanningsModule } from '../journey-plannings/journey-plannings.module';
import { DistributorModule } from '../distributor/distributor.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: VisibilityExecution.name,
        schema: VisibilityExecutionSchema,
      },
      {
        name: Outlet.name,
        schema: OutletSchema,
      },
      {
        name: SaleRepOutletRelation.name,
        schema: SaleRepOutletRelationSchema,
      },
      {
        name: UserAdmin.name,
        schema: UserAdminSchema,
      },
      {
        name: DSRTarget.name,
        schema: DSRTargetSchema,
      },
      {
        name: User.name,
        schema: UserSchema,
      },
      { name: PushNotification.name, schema: PushNotificationSchema },
      {
        name: NotificationLog.name,
        schema: NotificationLogSchema,
      },
      {
        name: Distributor.name,
        schema: DistributorSchema,
      },
      {
        name: JourneyPlanCycle.name,
        schema: JourneyPlanCycleSchema,
      },
      {
        name: JourneyPlanWeek.name,
        schema: JourneyPlanWeekSchema,
      },
      {
        name: OutletJourneyPlanning.name,
        schema: OutletJourneyPlanningSchema,
      },
      {
        name: BaseJourneyPlanSetting.name,
        schema: BaseJourneyPlanSettingSchema,
      },
      {
        name: DistributorUserRelation.name,
        schema: DistributorUserRelationSchema,
      },
      {
        name: SaleRepStatistic.name,
        schema: SaleRepStatisticSchema,
      },
      {
        name: MissReason.name,
        schema: MissReasonSchema,
      },
      {
        name: ColdStock.name,
        schema: ColdStockSchema,
      },
      {
        name: SaleRepExecutionVisibility.name,
        schema: SaleRepExecutionVisibilitySchema,
      },
      {
        name: VisibilityExecution.name,
        schema: VisibilityExecutionSchema,
      },
      {
        name: Files.name,
        schema: FilesSchema,
      },
      {
        name: OmsCacheData.name,
        schema: OmsCacheDataSchema,
      },
    ]),
    forwardRef(() => AuthModule),
    forwardRef(() => SaleRepModule),
    forwardRef(() => UsersModule),
    forwardRef(() => ExternalModule),
    forwardRef(() => OrdersModule),
    forwardRef(() => OutletsModule),
    forwardRef(() => JourneyPlanningsModule),
    forwardRef(() => DistributorModule),
    FilesModule,
    MessageModule,
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 50,
    }),
    SettingsModule,
  ],
  providers: [
    OmsAuthService,
    OmsOutletsService,
    OmsVisibilityExecutionsService,
    OmsTargetSettingsService,
    OmsFilesService,
    OmsNotificationsService,
    OmsSalesRepsService,
    OmsCyclesService,
    OmsWeeksService,
    OmsPlansService,
    OmsDistributorsService,
    OmsTimeFramesService,
    OmsSalesRepReportsOverviewService,
    OmsSalesRepReportsChecklistsService,
    OmsOutletReportsService,
    OmsMissReasonsService,
    OmsExportOutletReportsService,
    OmsRepReportsCalculatorsService,
    OmsSalesRepStatisticsService,
    SalesRepExternalStatisticsService,
  ],
  controllers: [
    OmsAuthController,
    OmsOutletsController,
    OmsVisibilityExecutionsController,
    OmsTargetSettingsController,
    OmsNotificationsController,
    OmsSalesRepsController,
    OmsCyclesController,
    OmsWeeksController,
    OmsPlansController,
    OmsTimeFramesController,
    OmsSalesRepReportsController,
    OmsOutletReportsController,
    OmsMissReasonsController,
    SalesRepStatisticController,
  ],
  exports: [OmsRepReportsCalculatorsService, OmsSalesRepStatisticsService],
})
export class OmsModule {}
