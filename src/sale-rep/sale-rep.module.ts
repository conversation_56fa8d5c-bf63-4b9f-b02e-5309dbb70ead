import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { OrdersModule } from '../orders/orders.module';

import { AuthModule } from '../auth/auth.module';
import { BrandModule } from '../brand/brand.module';
import { DistributorModule } from '../distributor/distributor.module';
import { DsrTargetsModule } from '../dsr-targets/dsr-targets.module';
import { FilesModule } from '../files/files.module';
import { JourneyPlanningsModule } from '../journey-plannings/journey-plannings.module';
import { MessageModule } from '../message/message.module';
import { OutletsModule } from '../outlets/outlets.module';
import { SettingsModule } from '../settings/settings.module';
import { UsersModule } from '../users/users.module';
import { SaleRepController } from './sale-rep.controller';
import {
  SaleRepExecutionAffordability,
  SaleRepExecutionAffordabilitySchema,
  SaleRepExecutionAvailability,
  SaleRepExecutionAvailabilitySchema,
  SaleRepExecutionVisibility,
  SaleRepExecutionVisibilitySchema,
  SaleRepFfcStore,
  SaleRepFfcStoreSchema,
  SaleRepStatistic,
  SaleRepStatisticSchema,
} from './schemas';
import { SaleRepExecutionAvailabilityService, SaleRepExecutionVisibilityService, SaleRepFfcStoreService, SaleRepStatisticService } from './services';
import { SaleRepExecutionAffordabilityService } from './services/salerep-execution-affordability.service';
import { SaleRepService } from './services/salerep.service';
import { ExternalModule } from '../external/external.module';
import { User, UserSchema } from 'src/users/schemas/user.schema';
import { MissReasonsModule } from 'src/miss-reasons/miss-reasons.module';
import { Distributor, DistributorSchema } from 'src/distributor/schemas';
import { OutletJourneyPlanning, OutletJourneyPlanningSchema } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { RoutePlanService } from './services/route-plan.service';
import { ThirdPartiesModule } from 'src/third-parties/third-parties.module';
import { OmsModule } from 'src/oms/oms.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: SaleRepFfcStore.name,
        schema: SaleRepFfcStoreSchema,
      },
      {
        name: SaleRepStatistic.name,
        schema: SaleRepStatisticSchema,
      },
      {
        name: SaleRepExecutionVisibility.name,
        schema: SaleRepExecutionVisibilitySchema,
      },
      {
        name: SaleRepExecutionAvailability.name,
        schema: SaleRepExecutionAvailabilitySchema,
      },
      {
        name: SaleRepExecutionAffordability.name,
        schema: SaleRepExecutionAffordabilitySchema,
      },
      {
        name: User.name,
        schema: UserSchema,
      },
      {
        name: Distributor.name,
        schema: DistributorSchema,
      },
      {
        name: OutletJourneyPlanning.name,
        schema: OutletJourneyPlanningSchema,
      },
    ]),
    forwardRef(() => AuthModule),
    SettingsModule,
    FilesModule,
    BrandModule,
    DsrTargetsModule,
    MissReasonsModule,
    ThirdPartiesModule,
    forwardRef(() => OmsModule),
    forwardRef(() => ExternalModule),
    forwardRef(() => DistributorModule),
    forwardRef(() => OutletsModule),
    forwardRef(() => JourneyPlanningsModule),
    forwardRef(() => MessageModule),
    forwardRef(() => UsersModule),
    forwardRef(() => OrdersModule),
  ],
  controllers: [SaleRepController],
  providers: [
    SaleRepFfcStoreService,
    SaleRepStatisticService,
    SaleRepExecutionVisibilityService,
    SaleRepExecutionAvailabilityService,
    SaleRepExecutionAffordabilityService,
    SaleRepService,
    RoutePlanService,
  ],
  exports: [
    SaleRepFfcStoreService,
    SaleRepStatisticService,
    SaleRepExecutionVisibilityService,
    SaleRepExecutionAvailabilityService,
    SaleRepExecutionAffordabilityService,
    SaleRepService,
    RoutePlanService,
  ],
})
export class SaleRepModule {}
