import { ApiModelProperty, ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

export class CreateUpdateSalesRepDto {
  @ApiModelProperty()
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string;

  @ApiModelProperty()
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  mobilePhone: string;

  @ApiModelProperty()
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  mobilePhoneCode: string;

  @ApiModelProperty()
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  saleRepId: string;

  @ApiModelProperty()
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  dotUsername: string;

  @ApiModelProperty()
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  status: string;

  @ApiModelProperty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  distributorId: string;

  @ApiModelProperty()
  @IsOptional()
  @IsBoolean()
  isTestAccount: boolean;

  @ApiModelPropertyOptional()
  @IsString()
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  geoAddress?: string;
}
