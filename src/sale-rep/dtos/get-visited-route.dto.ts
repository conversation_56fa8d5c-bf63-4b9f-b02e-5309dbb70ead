import { IsDateString, IsEnum, IsOptional, IsString } from 'class-validator';
import { ConstantCommons } from '../../utils/constants';
import { ApiProperty } from '@nestjs/swagger';

export class GetHomePerformanceDto {
  @ApiProperty()
  @IsDateString()
  @IsOptional()
  fromDate?: Date;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  toDate?: Date;
}

export class GetVisitedRouteDto {
  @ApiProperty()
  @IsEnum(
    {
      ALL: ConstantCommons.ALL,
      COMPLETED: ConstantCommons.COMPLETED,
      MISSED: ConstantCommons.MISSED,
    },
    {
      message: `filterVisitedStatus must be one of: ${Object.values({
        ALL: ConstantCommons.ALL,
        COMPLETED: ConstantCommons.COMPLETED,
        MISSED: ConstantCommons.MISSED,
      }).join(', ')}`,
    },
  )
  @IsString({
    message: `filterVisitedStatus must be one of: ${Object.values({
      ALL: ConstantCommons.ALL,
      COMPLETED: ConstantCommons.COMPLETED,
      MISSED: ConstantCommons.MISSED,
    }).join(', ')}`,
  })
  filterVisitedStatus: string;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  fromDate?: Date;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  toDate?: Date;
}
