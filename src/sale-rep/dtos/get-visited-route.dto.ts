import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsDateString, IsEnum, IsOptional, IsString } from 'class-validator';
import { ConstantCommons } from '../../utils/constants';

export class GetHomePerformanceDto {
  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  fromDate?: Date;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  toDate?: Date;
}

export class GetVisitedRouteDto {
  @ApiModelProperty()
  @IsEnum(
    {
      ALL: ConstantCommons.ALL,
      COMPLETED: ConstantCommons.COMPLETED,
      MISSED: ConstantCommons.MISSED,
    },
    {
      message: `filterVisitedStatus must be one of: ${Object.values({
        ALL: ConstantCommons.ALL,
        COMPLETED: ConstantCommons.COMPLETED,
        MISSED: ConstantCommons.MISSED,
      }).join(', ')}`,
    },
  )
  @IsString({
    message: `filterVisitedStatus must be one of: ${Object.values({
      ALL: ConstantCommons.ALL,
      COMPLETED: ConstantCommons.COMPLETED,
      MISSED: ConstantCommons.MISSED,
    }).join(', ')}`,
  })
  filterVisitedStatus: string;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  fromDate?: Date;

  @ApiModelProperty()
  @IsDateString()
  @IsOptional()
  toDate?: Date;
}
