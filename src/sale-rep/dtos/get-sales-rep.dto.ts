import { SortOrder } from 'mongoose';
import { IsOptional, IsString } from 'class-validator';
import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class SalesRepSortOrder extends SortOrderDto {
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  name: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  mobilePhone: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  dotUsername: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorId: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorName: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  status: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  updatedAt: SortOrder;
}

export class GetSalesRepDto extends PaginationDto {
  // search by sales rep id | name | phone
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({ type: () => SalesRepSortOrder })
  sort: SalesRepSortOrder;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  distributorId?: string;
}
