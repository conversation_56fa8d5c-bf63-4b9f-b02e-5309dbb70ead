import { SortOrder } from 'mongoose';

import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { IsOptional, IsString } from 'class-validator';
import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';

export class SalesRepSortOrder extends SortOrderDto {
  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  name: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  mobilePhone: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  dotUsername: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorId: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorName: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  saleRepId: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  status: SortOrder;

  @ApiModelPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  updatedAt: SortOrder;
}

export class GetSalesRepDto extends PaginationDto {
  // search by sales rep id | name | phone
  @ApiModelPropertyOptional()
  @IsString()
  @IsOptional()
  search?: string;

  @ApiModelPropertyOptional({ type: () => SalesRepSortOrder })
  sort: SalesRepSortOrder;

  @ApiModelPropertyOptional()
  @IsString()
  @IsOptional()
  distributorId?: string;
}
