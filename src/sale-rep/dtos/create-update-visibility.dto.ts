import { ArrayMaxSize, ArrayMinSize, IsArray, <PERSON><PERSON><PERSON>ber, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUpdateVisibilityDto {
  @ApiProperty()
  @IsArray()
  @ArrayMinSize(1, { message: 'execution.visibility.min_photo' })
  @ArrayMaxSize(5, { message: 'execution.visibility.max_photo' })
  imageIds: Array<string>;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  @Max(100)
  forwardStock: number;
}
