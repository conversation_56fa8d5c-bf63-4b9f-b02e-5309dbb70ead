import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsNumber, Max, <PERSON> } from 'class-validator';

export class CreateUpdateVisibilityDto {
  @ApiModelProperty()
  @IsArray()
  @ArrayMinSize(1, { message: 'execution.visibility.min_photo' })
  @ArrayMaxSize(5, { message: 'execution.visibility.max_photo' })
  imageIds: Array<string>;

  @ApiModelProperty()
  @IsNumber()
  @Min(0)
  @Max(100)
  forwardStock: number;
}
