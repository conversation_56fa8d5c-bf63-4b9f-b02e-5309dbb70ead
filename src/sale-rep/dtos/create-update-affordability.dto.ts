import { Transform, TransformFnParams } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUpdateAffordabilityDto {
  @ApiProperty({ default: 'Tiger' })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  brandName: string;

  @ApiProperty({ default: 'Pt' })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  brandType: string;

  @ApiProperty({ default: 'Tiger-Pt' })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  displayName: string;

  @ApiProperty({ default: 0 })
  @IsNumber()
  value: number;
}
