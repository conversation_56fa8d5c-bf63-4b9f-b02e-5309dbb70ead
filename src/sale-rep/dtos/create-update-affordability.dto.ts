import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

export class CreateUpdateAffordabilityDto {
  @ApiModelProperty({ default: 'Tiger' })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  brandName: string;

  @ApiModelProperty({ default: 'Pt' })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  brandType: string;

  @ApiModelProperty({ default: 'Tiger-Pt' })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  displayName: string;

  @ApiModelProperty({ default: 0 })
  @IsNumber()
  value: number;
}
