import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';
import { CreateUpdateAffordabilityDto } from './create-update-affordability.dto';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUpdateMultipleAffordabilitiesDto {
  @ApiProperty({ type: CreateUpdateAffordabilityDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateUpdateAffordabilityDto)
  affordabilityBrands: CreateUpdateAffordabilityDto[];
}
