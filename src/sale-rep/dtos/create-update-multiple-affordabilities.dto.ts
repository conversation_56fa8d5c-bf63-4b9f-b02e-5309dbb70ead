import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';
import { CreateUpdateAffordabilityDto } from './create-update-affordability.dto';

export class CreateUpdateMultipleAffordabilitiesDto {
  @ApiModelProperty({ type: CreateUpdateAffordabilityDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateUpdateAffordabilityDto)
  affordabilityBrands: CreateUpdateAffordabilityDto[];
}
