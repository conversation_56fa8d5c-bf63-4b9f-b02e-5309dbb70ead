import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsNumber, IsString, <PERSON>, <PERSON> } from 'class-validator';

export class ImageIdsWithHint {
  @ApiModelProperty()
  @IsString()
  imageId: string;

  @ApiModelProperty()
  @IsString()
  hintCode: string;
}

export class CreateUpdateVisibilityDto {
  @ApiModelProperty()
  @IsArray()
  @ArrayMinSize(1, { message: 'execution.visibility.min_photo' })
  @ArrayMaxSize(10, { message: 'execution.visibility.max_photo' })
  imageIds: Array<ImageIdsWithHint>;

  @ApiModelProperty()
  @IsNumber()
  @Min(0)
  @Max(100)
  forwardStock: number;
}
