import { ArrayMaxSize, ArrayMinSize, IsArray, IsNumber, IsString, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ImageIdsWithHint {
  @ApiProperty()
  @IsString()
  imageId: string;

  @ApiProperty()
  @IsString()
  hintCode: string;
}

export class CreateUpdateVisibilityDto {
  @ApiProperty()
  @IsArray()
  @ArrayMinSize(1, { message: 'execution.visibility.min_photo' })
  @ArrayMaxSize(10, { message: 'execution.visibility.max_photo' })
  imageIds: Array<ImageIdsWithHint>;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  @Max(100)
  forwardStock: number;
}
