import { Expose, Transform } from 'class-transformer';

import { getLastUpdatedOn } from '../transforms';
import { AffordabilityBandPrice } from '../schemas';
import { transformAffordabilityBrands } from '../transforms/affordability.transform';

export class FetchedAffordabilityDto {
  @Expose()
  @Transform(transformAffordabilityBrands)
  brands: AffordabilityBandPrice[];

  @Expose()
  @Transform(getLastUpdatedOn)
  lastUpdatedOn: Date;

  @Expose()
  updatedAt: Date;

  @Expose()
  readonly: boolean;
}
