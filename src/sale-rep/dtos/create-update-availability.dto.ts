import { Transform, TransformFnParams, Type } from 'class-transformer';
import { IsArray, IsBoolean, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AvailabilityDto {
  @ApiProperty({ default: 'Heineken' })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string;

  @ApiProperty({ default: false })
  @IsBoolean()
  hasQt: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  hasPt: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  hasCan: boolean;

  @ApiProperty({ default: false })
  @IsOptional()
  @IsBoolean()
  hasBcan?: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  hasKeg: boolean;
}

export class CreateUpdateAvailabilityDto {
  @ApiProperty({ type: AvailabilityDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AvailabilityDto)
  availabilityBrands: AvailabilityDto[];
}
