import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { IsArray, IsBoolean, IsOptional, IsString, ValidateNested } from 'class-validator';

export class AvailabilityDto {
  @ApiModelProperty({ default: 'Heineken' })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string;

  @ApiModelProperty({ default: false })
  @IsBoolean()
  hasQt: boolean;

  @ApiModelProperty({ default: false })
  @IsBoolean()
  hasPt: boolean;

  @ApiModelProperty({ default: false })
  @IsBoolean()
  hasCan: boolean;

  @ApiModelProperty({ default: false })
  @IsOptional()
  @IsBoolean()
  hasBcan?: boolean;

  @ApiModelProperty({ default: false })
  @IsBoolean()
  hasKeg: boolean;
}

export class CreateUpdateAvailabilityDto {
  @ApiModelProperty({ type: AvailabilityDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AvailabilityDto)
  availabilityBrands: AvailabilityDto[];
}
