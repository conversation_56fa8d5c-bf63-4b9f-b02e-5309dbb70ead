import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { Type } from "class-transformer";
import { IsDate, IsNumber, IsOptional, Max, Min, isDate } from 'class-validator';

export class ExecuteVisitDto {
  @ApiModelProperty()
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude: number;

  @ApiModelProperty()
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude: number;

  @ApiModelProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  time?: Date;
}
