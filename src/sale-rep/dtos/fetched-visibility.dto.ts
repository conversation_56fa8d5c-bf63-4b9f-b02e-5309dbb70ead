import { Expose, Transform } from 'class-transformer';

import { getLastUpdatedOn, transformVisibilityHints, transformVisibilityImages } from '../transforms';
import { Files } from '../../files/schemas';
import { HintClass } from '../../brand/schemas/hint.schema';

export class FetchedVisibilityDto {
  @Expose()
  @Transform(transformVisibilityImages)
  images: Partial<Files>[];

  @Expose()
  forwardStock: number;

  @Expose()
  updatedAt: Date;

  @Expose()
  @Transform(getLastUpdatedOn)
  lastUpdatedOn: Date;

  @Expose()
  readonly: boolean;

  @Expose()
  @Transform(transformVisibilityHints)
  hints: Partial<HintClass>[];

  @Expose()
  hasHint: boolean;
}
