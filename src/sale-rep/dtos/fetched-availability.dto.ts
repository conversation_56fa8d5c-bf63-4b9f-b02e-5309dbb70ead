import { Expose, Transform } from 'class-transformer';

import { getLastUpdatedOn, transformAvailabilityBrands } from '../transforms';
import { AvailabilityBand } from '../schemas';

export class FetchedAvailabilityDto {
  @Expose()
  @Transform(transformAvailabilityBrands)
  brands: AvailabilityBand[];

  @Expose()
  @Transform(getLastUpdatedOn)
  lastUpdatedOn: Date;

  @Expose()
  updatedAt: Date;

  @Expose()
  readonly: boolean;
}
