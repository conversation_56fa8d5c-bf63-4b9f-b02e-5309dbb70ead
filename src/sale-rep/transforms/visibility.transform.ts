import * as moment from 'moment-timezone';

export function transformVisibilityImages({ obj }) {
  if (!obj?.images?.length) {
    return [];
  }
  return obj.images.map((item) => {
    const createdAt = moment(item.createdAt).tz(process.env.TZ);
    const today = moment().tz(process.env.TZ).startOf('date');

    return {
      _id: item._id.toString(),
      name: item.name,
      path: item.path,
      originalName: item.originalName,
      type: item.type,
      compressImages: item.compressImages,
      isPastData: createdAt.isBefore(today),
    };
  });
}

export function getLastUpdatedOn({ obj }) {
  if (obj?.lastUpdatedOn) {
    return obj.lastUpdatedOn;
  }
  if (!obj?.journeyPlan) {
    return;
  }
  return obj.journeyPlan.updatedAt;
}

export function transformVisibilityHints({ obj }) {
  if (!obj?.hints) {
    return [];
  }
  return obj?.hints;
}
