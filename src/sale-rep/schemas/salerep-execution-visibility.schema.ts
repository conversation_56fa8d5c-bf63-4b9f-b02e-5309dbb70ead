import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MSchema, Types } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';
import { OutletJourneyPlanning } from '../../journey-plannings/schemas/outlet-journey-planning.schema';
import { Files } from '../../files/schemas';
import { Outlet } from '../../outlets/schemas/outlet.schema';

export type SaleRepExecutionVisibilityDocument = SaleRepExecutionVisibility & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class SaleRepExecutionVisibility extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({ type: Types.ObjectId, ref: OutletJourneyPlanning.name, index: true })
  journeyPlan: OutletJourneyPlanning;

  @Prop({ type: [Types.ObjectId], ref: Files.name })
  images: Files[];

  @Prop()
  forwardStock: number;

  // for 2.0 version
  // for visibility execution;
  @Prop({ type: MSchema.Types.Mixed, default: {} })
  tasks: object;

  @Prop({ type: MSchema.Types.Mixed, default: {} })
  taskProps: object;
}

export const SaleRepExecutionVisibilitySchema = SchemaFactory.createForClass(SaleRepExecutionVisibility);
