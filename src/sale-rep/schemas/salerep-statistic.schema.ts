import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';

export type SaleRepStatisticDocument = SaleRepStatistic & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class SaleRepStatistic extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  @Prop({ index: true })
  contactId: string;

  @Prop({ index: true })
  statisticDay: Date;

  @Prop()
  cpsr: number;

  // As Call compliance rate 20.0
  @Prop()
  cpsrValue: number;

  @Prop()
  cpsrTarget: number;

  // As Sales value 2.0
  @Prop()
  sales: number;

  @Prop()
  salesValue: number;

  @Prop()
  mabo: number;

  // As active selling outlet 2.0
  @Prop()
  maboValue: number;

  @Prop()
  maboTarget: number;

  // As availability 2.0
  @Prop()
  avaibilityValue: number;

  @Prop()
  avaibilityTarget: number;

  @Prop()
  availabilityValue: number;

  @Prop()
  availabilityTarget: number;

  // 2.0 version
  @Prop()
  callEffectivenessValue: number;

  @Prop()
  callEffectivenessTarget: number;

  @Prop()
  salesVolumeValue: number;

  @Prop()
  visibilityValue: number;

  @Prop()
  visibilityTarget: number;
}

export const SaleRepStatisticSchema = SchemaFactory.createForClass(SaleRepStatistic);
