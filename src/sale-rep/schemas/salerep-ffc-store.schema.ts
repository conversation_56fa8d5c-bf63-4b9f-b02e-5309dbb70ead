import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from '../../shared/schemas/base.schema';
import { Types, Document } from 'mongoose';
import { User } from '../../users/schemas/user.schema';

export type SaleRepFfcStoreDocument = SaleRepFfcStore & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class SaleRepFfcStore extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  @Prop({ index: true })
  storeId: string;

  @Prop()
  fulfillmentCenterId: string;

  @Prop()
  phones: string;

  @Prop()
  customerUserName: string;
}

export const SaleRepFfcStoreSchema = SchemaFactory.createForClass(SaleRepFfcStore);
