import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';
import { OutletJourneyPlanning } from '../../journey-plannings/schemas/outlet-journey-planning.schema';
import { Outlet } from '../../outlets/schemas/outlet.schema';

export class AffordabilityBandPrice {
  brandName: string;
  brandType: string;
  displayName: string;
  value: number;
}

export type SaleRepExecutionAffordabilityDocument = SaleRepExecutionAffordability & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class SaleRepExecutionAffordability extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({ type: Types.ObjectId, ref: OutletJourneyPlanning.name, index: true })
  journeyPlan: OutletJourneyPlanning;

  @Prop([AffordabilityBandPrice])
  brands: AffordabilityBandPrice[];
}

export const SaleRepExecutionAffordabilitySchema = SchemaFactory.createForClass(SaleRepExecutionAffordability);
