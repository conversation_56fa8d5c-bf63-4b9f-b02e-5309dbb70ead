import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

import { OutletJourneyPlanning } from '../../journey-plannings/schemas/outlet-journey-planning.schema';
import { Outlet } from '../../outlets/schemas/outlet.schema';
import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';

export class AvailabilityBand {
  name: string;
  hasQt: boolean;
  hasPt: boolean;
  hasCan: boolean;
  hasBcan: boolean;
  hasKeg: boolean;
}

export type SaleRepExecutionAvailabilityDocument = SaleRepExecutionAvailability & Document;

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    virtuals: true,
  },
})
export class SaleRepExecutionAvailability extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  saleRep: User;

  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({ type: Types.ObjectId, ref: OutletJourneyPlanning.name, index: true })
  journeyPlan: OutletJourneyPlanning;

  @Prop([AvailabilityBand])
  brands: AvailabilityBand[];
}

export const SaleRepExecutionAvailabilitySchema = SchemaFactory.createForClass(SaleRepExecutionAvailability);
