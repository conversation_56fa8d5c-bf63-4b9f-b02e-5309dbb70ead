/**
 * This function remove duplicate outlet and get the oldest plan of this outlet
 * @param unPlanned
 */
import { IOutletSearching } from '../interfaces';

export function convertPlanData(planData: Array<IOutletSearching>, isCurrentCycle = false) {
  const groupByOutletId = {};
  planData.forEach((item) => {
    const outletId = item.o._id.toString();
    const { _id, day, week, cancel, rescheduled, rescheduledDay, cancellationReason, displayDay, missedReason, visitStatus } = item.ojp;
    const scheduleDay = {
      _id,
      day,
      week,
      cancel,
      rescheduled,
      rescheduledDay,
      cancellationReason,
      displayDay,
      visitStatus,
      missedReason,
    };
    if (groupByOutletId[outletId]) {
      groupByOutletId[outletId].scheduleDays = [...groupByOutletId[outletId].scheduleDays, { ...scheduleDay }];
    } else {
      groupByOutletId[outletId] = {
        outlet: item.o,
        scheduleDays: [
          {
            ...scheduleDay,
          },
        ],
      };
    }
  });
  const outletArr = Object.values(groupByOutletId) as any;
  return outletArr.map((o) => {
    const { outlet, scheduleDays } = o;
    const days = scheduleDays
      .map((item) => ({
        ...item,
        diff: +new Date() - +new Date(item.displayDay),
      }))
      .sort((item1, item2) => item1.diff - item2.diff);
    if (isCurrentCycle) {
      const futureDay = days.find((d) => d.diff >= 0);
      if (futureDay) {
        return { ...futureDay, outlet };
      } else {
        return { ...days[0], outlet };
      }
    }
    return { ...days[0], outlet };
  });
}
