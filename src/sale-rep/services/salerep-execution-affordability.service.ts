import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { I18nContext } from 'nestjs-i18n';
import { BrandService } from 'src/brand/services';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { BaseService } from '../../shared/services/base-service';
import { isEmptyObjectOrArray } from '../../utils';
import { BRAND_TYPES, ConstantCommons } from '../../utils/constants';
import { CreateUpdateMultipleAffordabilitiesDto } from '../dtos/create-update-multiple-affordabilities.dto';
import { SaleRepExecutionAvailability } from '../schemas';
import { SaleRepExecutionAffordability, SaleRepExecutionAffordabilityDocument } from '../schemas/salerep-execution-affordability.schema';
import { OpCos } from 'src/config';

@Injectable()
export class SaleRepExecutionAffordabilityService extends BaseService<SaleRepExecutionAffordability> {
  constructor(
    @InjectModel(SaleRepExecutionAffordability.name)
    private readonly _model: Model<SaleRepExecutionAffordabilityDocument>,
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly brandService: BrandService,
  ) {
    super();
    this.model = _model;
  }

  async getAffordabilityBySaleRepIdAndOutletId(saleRepId: string, outletId: string) {
    return this._model
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
      })
      .sort({ updatedAt: -1 })
      .limit(1)
      .populate('saleRep outlet journeyPlan')
      .exec();
  }

  async deleteAffordabilityBySaleRepIdAndJourneyPlanId(saleRepId: string, journeyPlanId: string) {
    return this._model
      .deleteOne({
        saleRep: new Types.ObjectId(saleRepId),
        journeyPlan: new Types.ObjectId(journeyPlanId),
      })
      .exec();
  }

  async getAffordabilityById(affordabilityId: string) {
    return this._model
      .findOne({
        _id: new Types.ObjectId(affordabilityId),
      })
      .populate('saleRep outlet journeyPlan')
      .exec();
  }

  affordabilityList(availabilities: SaleRepExecutionAvailability = null, result: SaleRepExecutionAffordability = null) {
    const brandData = [];
    if (isEmptyObjectOrArray(availabilities?.brands)) {
      return null;
    }
    for (const brand of availabilities.brands) {
      if (brand.hasQt) {
        brandData.push({
          brandName: brand.name?.trim(),
          brandType: BRAND_TYPES.QT,
          displayName: brand.name?.trim() + '-' + BRAND_TYPES.QT,
          value: Number(result?.brands?.find((v) => v.brandName === brand.name && v.brandType === BRAND_TYPES.QT)?.value),
        });
      }

      if (brand.hasPt) {
        brandData.push({
          brandName: brand.name?.trim(),
          brandType: BRAND_TYPES.PT,
          displayName: brand.name?.trim() + '-' + BRAND_TYPES.PT,
          value: Number(result?.brands?.find((v) => v.brandName === brand.name && v.brandType === BRAND_TYPES.PT)?.value),
        });
      }

      if (brand.hasCan) {
        brandData.push({
          brandName: brand.name?.trim(),
          brandType: BRAND_TYPES.CAN,
          displayName: brand.name?.trim() + '-' + BRAND_TYPES.CAN,
          value: Number(result?.brands?.find((v) => v.brandName === brand.name && v.brandType === BRAND_TYPES.CAN)?.value),
        });
      }

      if (brand.hasBcan) {
        brandData.push({
          brandName: brand.name?.trim(),
          brandType: BRAND_TYPES.BCAN,
          displayName: brand.name?.trim() + '-' + BRAND_TYPES.BCAN,
          value: Number(result?.brands?.find((v) => v.brandName === brand.name && v.brandType === BRAND_TYPES.BCAN)?.value),
        });
      }

      if (brand.hasKeg) {
        brandData.push({
          brandName: brand.name?.trim(),
          brandType: BRAND_TYPES.KEG,
          displayName: brand.name?.trim() + '-' + BRAND_TYPES.KEG,
          value: Number(result?.brands?.find((v) => v.brandName === brand.name && v.brandType === BRAND_TYPES.KEG)?.value),
        });
      }
    }

    return brandData;
  }

  async createOrUpdateAffordabilities(salesRepId: string, outletId: string, dto: CreateUpdateMultipleAffordabilitiesDto, i18n: I18nContext) {
    const { affordabilityBrands } = dto;

    if (!affordabilityBrands || !affordabilityBrands.length) {
      throw new BadRequestException(await i18n.t('plan.no_data_to_execute'));
    }

    const existedTodayPlan = await this.outletJourneyPlanningService.getTodayPlanBySaleRepIdAndOutletId(salesRepId, outletId);
    const readonly = !existedTodayPlan || [VisitStatus.START_VISIT].includes(existedTodayPlan?.visitStatus as VisitStatus);
    if (readonly) {
      throw new BadRequestException(await i18n.t('plan.cannot_edit_data'));
    }
    let allBrands = [];
    if ([OpCos.Cambodia, OpCos.Indonesia].includes(process.env.OPCO)) {
      allBrands = await this.brandService.findAll({ isActive: true });
    } else {
      allBrands = await this.brandService.findAll({});
    }

    const notExistedBrands = affordabilityBrands.filter((brand) => !allBrands.map((item) => item.name).includes(brand.brandName));
    if (notExistedBrands.length) {
      throw new BadRequestException(await i18n.t('brand.not_found', { args: { brandName: notExistedBrands.map((item) => item.brandName).join(';') } }));
    }

    if (affordabilityBrands.some(({ value }) => value <= 0 || value > ConstantCommons.AFFORDABILITY_MAX_VALUE)) {
      throw new BadRequestException(await i18n.t('brand.must_be_great_than_zero'));
    }

    const currentDate = new Date();
    const [closestAffordability] = await this.getAffordabilityBySaleRepIdAndOutletId(salesRepId, outletId);

    if (closestAffordability) {
      const oldBrands = closestAffordability.brands || [];
      affordabilityBrands.forEach((brand) => {
        const index = oldBrands.findIndex((item) => item.brandName === brand.brandName && item.brandType === brand.brandType);
        if (index > -1) {
          oldBrands[index] = brand;
        } else {
          oldBrands.push(brand);
        }
      });

      // if exist visit, then update
      if (existedTodayPlan._id.toString() === closestAffordability.journeyPlan._id.toString()) {
        return this.update(closestAffordability._id, {
          updatedAt: currentDate,
          brands: oldBrands,
        });
      }
      return this.create({
        saleRep: new Types.ObjectId(salesRepId),
        outlet: new Types.ObjectId(outletId),
        journeyPlan: existedTodayPlan._id,
        brands: oldBrands,
      });
    }

    return this.create({
      saleRep: new Types.ObjectId(salesRepId),
      outlet: new Types.ObjectId(outletId),
      journeyPlan: existedTodayPlan._id,
      brands: affordabilityBrands,
    });
  }
}
