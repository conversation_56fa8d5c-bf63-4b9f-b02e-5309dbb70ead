import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';

import { DistributorUserRelationService } from '../../distributor/services';
import { BaseService } from '../../shared/services/base-service';
import { SalesRepStatus } from '../../users/enums';
import { SaleRepStatistic, SaleRepStatisticDocument } from '../schemas';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import { OpCos } from 'src/config';
import { printLog } from '../../utils';

@Injectable()
export class SaleRepStatisticService extends BaseService<SaleRepStatistic> {
  constructor(
    @InjectModel(SaleRepStatistic.name)
    private readonly _model: Model<SaleRepStatisticDocument>,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    @InjectModel(OutletJourneyPlanning.name)
    private readonly journeyPlanModel: Model<OutletJourneyPlanningDocument>,
  ) {
    super();
    this.model = _model;
  }

  async getStatisticBySaleRepIdsAndGivenTime(saleRepId: string, statisticDay: string): Promise<SaleRepStatistic> {
    const currentDate = moment(statisticDay);

    return this._model.findOne({
      saleRep: new Types.ObjectId(saleRepId),
      statisticDate: {
        $gte: currentDate.clone().startOf('date'),
        $lte: currentDate.clone().endOf('date'),
      },
    });
  }

  createDSRReportAggregate(distributorIds: string[], statisticDay: Date, sort: Record<string, any> = { name: 1 }) {
    const month = statisticDay.getMonth() + 1;
    const year = statisticDay.getFullYear();

    const currentDate = moment(statisticDay).format('YYYY-MM-DD');

    if (sort.cpsr) {
      sort.cpsrPercent = sort.cpsr;
      delete sort.cpsr;
    }

    if (sort.mabo) {
      sort.maboPercent = sort.mabo;
      delete sort.mabo;
    }

    if (sort.sales) {
      sort.salesPercent = sort.sales;
      delete sort.sales;
    }

    return this._model
      .aggregate()
      .addFields({
        statisticDate: { $dateToString: { format: '%Y-%m-%d', date: '$statisticDay' } },
      })
      .match({
        statisticDate: currentDate,
      })
      .lookup({
        localField: 'saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .match({
        'u.saleRepStatus': SalesRepStatus.ACTIVE,
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'dur',
      })
      .unwind({
        path: '$dur',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        localField: 'dur.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'dis',
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: true,
      })
      .match({
        'dis.distributorId': { $in: distributorIds },
      })
      .lookup({
        localField: 'saleRep',
        from: 'dsrtargets',
        foreignField: 'saleRep',
        as: 'dtar',
      })
      .unwind({
        path: '$dtar',
        preserveNullAndEmptyArrays: true,
      })
      .match({
        'dtar.month': {
          $in: [month, undefined],
        },
        'dtar.year': {
          $in: [year, undefined],
        },
      })
      .project({
        _id: '$u._id',
        name: '$u.username',
        saleRepId: '$u.saleRepId',
        cpsr: { $ifNull: ['$cpsrValue', 0] },
        cpsrTarget: { $ifNull: ['$cpsrTarget', 0] },
        sales: { $ifNull: ['$salesValue', 0] },
        salesTarget: { $ifNull: ['$dtar.salesTarget', 0] },
        mabo: { $ifNull: ['$maboValue', 0] },
        maboTarget: { $ifNull: ['$maboTarget', 0] },
        distributorId: '$dis.distributorId',
        distributorName: '$dis.distributorName',
      })
      .addFields({
        cpsrPercent: {
          $switch: {
            branches: [
              { case: { $eq: [{ $ifNull: ['$cpsrTarget', 0] }, 0] }, then: -1 },
              { case: { $ne: [{ $ifNull: ['$cpsrTarget', 0] }, 0] }, then: { $divide: ['$cpsr', '$cpsrTarget'] } },
            ],
            default: -1,
          },
        },
        maboPercent: {
          $switch: {
            branches: [
              { case: { $eq: [{ $ifNull: ['$maboTarget', 0] }, 0] }, then: -1 },
              { case: { $ne: [{ $ifNull: ['$maboTarget', 0] }, 0] }, then: { $divide: ['$mabo', '$maboTarget'] } },
            ],
            default: -1,
          },
        },
        salesPercent: {
          $switch: {
            branches: [
              { case: { $eq: [{ $ifNull: ['$salesTarget', 0] }, 0] }, then: -1 },
              { case: { $ne: [{ $ifNull: ['$salesTarget', 0] }, 0] }, then: { $divide: ['$sales', '$salesTarget'] } },
            ],
            default: -1,
          },
        },
      })
      .sort(sort)
      .project({
        cpsrPercent: 0,
        maboPercent: 0,
        salesPercent: 0,
      })
      .collation({ locale: 'en' });
  }

  async getDSRReportID(distributorIds: string[], statisticDay: Date, sort: Record<string, any> = { name: 1 }) {
    const reportData = await this.createDSRReportAggregate(distributorIds, statisticDay, sort);

    const salesRepIds = reportData.map((item) => String(item._id));
    const strikeRateSalesRepMap = await this.getStrikeRateBySalesRep({ salesRepIds, statisticDay });

    return reportData
      .map((item) => {
        const strikeRateData = strikeRateSalesRepMap[String(item._id)] || { totalStrikeRate: 0, totalVisitedOutlets: 0 };

        return {
          ...item,
          ...strikeRateData,
        };
      })
      .sort((previous, next) => {
        if (!sort.strikeRate) {
          return 0;
        }

        const previousStrikeRatePercent = previous.totalVisitedOutlets ? previous.totalStrikeRate / previous.totalVisitedOutlets : 0;
        const nextStrikeRatePercent = next.totalVisitedOutlets ? next.totalStrikeRate / next.totalVisitedOutlets : 0;

        if (sort.strikeRate === 'asc') {
          return previousStrikeRatePercent - nextStrikeRatePercent;
        }

        return nextStrikeRatePercent - previousStrikeRatePercent;
      });
  }

  async getDSRReport(distributorIds: string[], statisticDay: Date, sort: Record<string, any> = { name: 1 }) {
    return this.createDSRReportAggregate(distributorIds, statisticDay, sort).exec();
  }

  async getDefaultDSRReport(distributorIds: string[], sort: Record<string, any> = { name: 1 }) {
    return this._distributorUserRelationService
      .createDistributorUserRelationAggregation()
      .match({
        'dis.distributorId': { $in: distributorIds },
        'dur.userAdmin': null,
      })
      .project({
        _id: '$u._id',
        name: '$u.username',
        saleRepId: '$u.saleRepId',
        distributorId: '$dis.distributorId',
        distributorName: '$dis.distributorName',
      })
      .addFields({
        cpsr: 0,
        cpsrTarget: 0,
        sales: 0,
        salesTarget: 0,
        mabo: 0,
        maboTarget: 0,
        totalStrikeRate: 0,
        totalVisitedOutlets: 0,
      })
      .sort(sort)
      .collation({ locale: 'en' });
  }

  async getDSRReportWithPagination(distributorIds: string[], statisticDay: Date, skip: number, limit: number, sort: Record<string, any> = { name: 1 }) {
    return this.createDSRReportAggregate(distributorIds, statisticDay, sort)
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      })
      .exec();
  }

  async getDSRReportWithPaginationID(distributorIds: string[], statisticDay: Date, skip: number, limit: number, sort: Record<string, any> = { name: 1 }) {
    const skipRecord = skip || 0;
    const limitRecord = limit || 1;

    if (sort.strikeRate) {
      const reportData = await this.createDSRReportAggregate(distributorIds, statisticDay, sort);

      const totalRecords = [{ total: reportData.length }];
      const salesRepIds = reportData.map((item) => String(item._id));
      const strikeRateSalesRepMap = await this.getStrikeRateBySalesRep({ salesRepIds, statisticDay });

      return [
        {
          totalRecords,
          data: reportData
            .map((item) => {
              const strikeRateData = strikeRateSalesRepMap[String(item._id)] || { totalStrikeRate: 0, totalVisitedOutlets: 0 };

              return {
                ...item,
                ...strikeRateData,
              };
            })
            .sort((previous, next) => {
              const previousStrikeRatePercent = previous.totalVisitedOutlets ? previous.totalStrikeRate / previous.totalVisitedOutlets : 0;
              const nextStrikeRatePercent = next.totalVisitedOutlets ? next.totalStrikeRate / next.totalVisitedOutlets : 0;

              if (sort.strikeRate === 'asc') {
                return previousStrikeRatePercent - nextStrikeRatePercent;
              }

              return nextStrikeRatePercent - previousStrikeRatePercent;
            })
            .slice(skipRecord, skipRecord + limitRecord),
        },
      ];
    }

    const [{ totalRecords, data }] = await this.createDSRReportAggregate(distributorIds, statisticDay, sort)
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skipRecord,
          },
          {
            $limit: limitRecord,
          },
        ],
      })
      .exec();

    const salesRepIds = data.map((item) => String(item._id));
    const strikeRateSalesRepMap = await this.getStrikeRateBySalesRep({ salesRepIds, statisticDay });

    return [
      {
        totalRecords,
        data: data.map((item) => {
          const strikeRateData = strikeRateSalesRepMap[String(item._id)] || { totalStrikeRate: 0, totalVisitedOutlets: 0 };

          return {
            ...item,
            ...strikeRateData,
          };
        }),
      },
    ];
  }

  async getStrikeRateBySalesRep({ salesRepIds, statisticDay }: { salesRepIds: string[]; statisticDay: Date }) {
    const startOfMonth = moment(statisticDay).tz(process.env.TZ).startOf('month');
    const endOfMonth = moment(statisticDay).tz(process.env.TZ).endOf('month');

    const completedJourneyPlans = await this.journeyPlanModel.find({
      saleRep: { $in: salesRepIds.map((item) => new Types.ObjectId(item)) },
      visitStatus: VisitStatus.COMPLETED,
      visitedDay: {
        $gte: startOfMonth.toDate(),
        $lte: endOfMonth.toDate(),
      },
    });

    const salesRepOutletsMap = completedJourneyPlans.reduce((pre, curr) => {
      const salesRepId = String(curr.saleRep);
      const outletId = String(curr.outlet);

      const previousData = pre[salesRepId];
      if (!previousData || !previousData.length) {
        return {
          ...pre,
          [salesRepId]: [outletId],
        };
      }

      if (previousData.includes(outletId)) {
        return pre;
      }

      return {
        ...pre,
        [salesRepId]: [...previousData, outletId],
      };
    }, {});

    const outletSalesRepMap = completedJourneyPlans.reduce((pre, curr) => {
      const salesRepId = String(curr.saleRep);
      const outletId = String(curr.outlet);

      return {
        ...pre,
        [outletId]: salesRepId,
      };
    }, {});

    const salesRepOutletVisitedDatesMap = completedJourneyPlans.reduce((pre, curr) => {
      const salesRepId = String(curr.saleRep);
      const outletId = String(curr.outlet);
      const key = `${salesRepId}-${outletId}`;

      const previousData = pre[key];
      const visitedDate = moment(curr.visitedDay).tz(process.env.TZ).format('DD/MM/YYYY');
      if (!previousData || !previousData.length) {
        return {
          ...pre,
          [key]: [visitedDate],
        };
      }

      return {
        ...pre,
        [key]: [...previousData, visitedDate],
      };
    }, {});

    const outletVisitedDateMap = completedJourneyPlans.reduce((pre, curr) => {
      const outletId = String(curr.outlet);

      const previousValue = pre[outletId] || [];

      return {
        ...pre,
        [outletId]: [...previousValue, curr.visitedDay],
      };
    }, {});

    const outletIds = Object.keys(outletSalesRepMap);

    return salesRepIds.reduce((pre, salesRepId) => {
      const outletIds = salesRepOutletsMap[salesRepId];
      if (!outletIds || !outletIds.length) {
        return {
          ...pre,
          [salesRepId]: {
            totalStrikeRate: 0,
            totalVisitedOutlets: 0,
          },
        };
      }

      const totalVisitedOutlets = outletIds.length;
      const totalStrikeRate = 0;
      return {
        ...pre,
        [salesRepId]: {
          totalStrikeRate,
          totalVisitedOutlets,
        },
      };
    }, {});
  }

  getStatisticDayByDiffMonths(diffMonths: number): Date {
    const momentCurrentTime = moment().tz(process.env.TZ);
    if (diffMonths === 0) {
      return momentCurrentTime.clone().endOf('day').toDate();
    }
    return momentCurrentTime.clone().subtract(Math.abs(diffMonths), 'months').endOf('month').toDate();
  }

  createSalesRepStatisticAggregation() {
    return this._model
      .aggregate()
      .project({
        ss: '$$ROOT',
      })
      .lookup({
        localField: 'ss.saleRep',
        from: 'users',
        foreignField: '_id',
        as: 'u',
      })
      .lookup({
        localField: 'ss.saleRep',
        from: 'distributoruserrelations',
        foreignField: 'user',
        as: 'dur',
      })
      .lookup({
        localField: 'dur.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'dis',
      })
      .lookup({
        localField: 'ss.saleRep',
        from: 'dsrtargets',
        foreignField: 'saleRep',
        as: 'dtar',
      })
      .unwind({
        path: '$u',
        preserveNullAndEmptyArrays: true,
      })
      .unwind({
        path: '$dur',
        preserveNullAndEmptyArrays: true,
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: true,
      })
      .unwind({
        path: '$dtar',
        preserveNullAndEmptyArrays: true,
      });
  }

  async getDefaultStatisticDataByDistributor(distributorIds: string[], skip: number, limit: number, sort: Record<string, any> = { name: 1 }) {
    return this._distributorUserRelationService
      .createDistributorUserRelationAggregation()
      .match({
        'dis.distributorId': { $in: distributorIds },
        'dur.userAdmin': null,
      })
      .project({
        _id: '$u._id',
        name: '$u.username',
        saleRepId: '$u.saleRepId',
        distributorId: '$dis.distributorId',
        distributorName: '$dis.distributorName',
      })
      .addFields({
        cpsr: 0,
        cpsrTarget: 0,
        sales: 0,
        salesTarget: 0,
        mabo: 0,
        maboTarget: 0,
        totalStrikeRate: 0,
        totalVisitedOutlets: 0,
      })
      .sort(sort)
      .collation({ locale: 'en' })
      .facet({
        totalRecords: [
          {
            $count: 'total',
          },
        ],
        data: [
          {
            $skip: skip >= 0 ? skip : 0,
          },
          {
            $limit: limit >= 1 ? limit : 1,
          },
        ],
      })
      .exec();
  }

  async getCurrentPerformanceDataBySalesrep(salesRepId: string) {
    const statisticDay = new Date();
    const month = statisticDay.getMonth() + 1;
    const year = statisticDay.getFullYear();
    const currentDate = moment(statisticDay).format('YYYY-MM-DD');
    const res = await this._model
      .aggregate()
      .addFields({
        statisticDate: { $dateToString: { format: '%Y-%m-%d', date: '$statisticDay' } },
      })
      .match({
        statisticDate: currentDate,
        saleRep: salesRepId,
      })
      .lookup({
        localField: 'saleRep',
        from: 'dsrtargets',
        foreignField: 'saleRep',
        as: 'dtar',
      })
      .unwind({
        path: '$dtar',
        preserveNullAndEmptyArrays: true,
      })
      .match({
        'dtar.month': {
          $in: [month, undefined],
        },
        'dtar.year': {
          $in: [year, undefined],
        },
      })
      .project({
        cpsr: { $ifNull: ['$cpsrValue', 0] },
        cpsrTarget: { $ifNull: ['$cpsrTarget', 0] },
        sales: { $ifNull: ['$salesValue', 0] },
        salesTarget: { $ifNull: ['$dtar.salesTarget', 0] },
        mabo: { $ifNull: ['$maboValue', 0] },
        maboTarget: { $ifNull: ['$maboTarget', 0] },
        avaibility: { $ifNull: ['$avaibilityValue', 0] },
        avaibilityTarget: { $ifNull: ['$avaibilityTarget', 0] },
      });
    if (res.length) {
      return res[0];
    }
    return null;
  }

  async getPerformance(salesRepId: string) {
    let unit = 'RM';
    if (process.env.OPCO == OpCos.Indonesia) {
      unit = 'HL';
    }
    let data = {
      cpsr: {
        total: 0,
        current: 0,
        percent: 0,
      },
      saleValue: {
        total: 0,
        current: 0,
        percent: 0,
        unit,
      },
      activeSellingOutlet: {
        total: 0,
        current: 0,
        percent: 0,
      },
      avaibility: {
        total: 0,
        current: 0,
        percent: 0,
      },
    };
    const currentPerformance = await this.getCurrentPerformanceDataBySalesrep(salesRepId);
    if (currentPerformance) {
      //Set CPSR
      data.cpsr.current = currentPerformance.cpsr;
      data.cpsr.total = currentPerformance.cpsrTarget;
      if (data.cpsr.total) {
        data.cpsr.percent = (data.cpsr.current / (data.cpsr.total || 1)) * 100;
      }

      //Set saleValue
      data.saleValue.current = currentPerformance.sales;
      data.saleValue.total = currentPerformance.salesTarget;
      if (data.saleValue.total) {
        data.saleValue.percent = (data.saleValue.current / (data.saleValue.total || 1)) * 100;
      }

      //Set activeSellingOutlet
      data.activeSellingOutlet.current = currentPerformance.mabo;
      data.activeSellingOutlet.total = currentPerformance.maboTarget;
      if (data.activeSellingOutlet.total) {
        data.activeSellingOutlet.percent = (data.activeSellingOutlet.current / (data.activeSellingOutlet.total || 1)) * 100;
      }

      //Set avaibility
      data.avaibility.current = currentPerformance.avaibility;
      data.avaibility.total = currentPerformance.avaibilityTarget;
      if (data.avaibility.total) {
        data.avaibility.percent = (data.avaibility.current / (data.avaibility.total || 1)) * 100;
      }
    }
    return data;
  }

  async updateOrCreate(filter: any, data: any) {
    return await this.model.updateOne(filter, data, { upsert: true });
  }

  /**
   *
   * @param salesRepId
   * @param date: moment().startOf('d').toDate();
   */
  async calculateCallComplianceRateBySale({ salesRepId, date }: { salesRepId: string; date: Date }) {
    try {
      const from = moment(date).tz(process.env.TZ).startOf('month').startOf('date');
      const to = moment(date).tz(process.env.TZ).endOf('month').endOf('date');
      const plans = await this.journeyPlanModel
        .find({
          saleRep: new Types.ObjectId(salesRepId),
          $or: [
            { day: { $gte: from, $lte: to }, rescheduled: false },
            { rescheduledDay: { $gt: from, $lte: to }, rescheduled: true },
          ],
        })
        .select('_id visitStatus  missedReason cancel')
        .populate('missedReason');
      const plannedCall = plans.length;
      const completedCalls = plans.filter((plan) => plan.visitStatus === VisitStatus.COMPLETED).length;
      const uncontrollableMissCalls = plans.filter((plan) => plan.cancel || (plan.missedReason && !plan.missedReason.controllable)).length;
      return await this.updateOrCreate(
        {
          statisticDay: moment(date).tz(process.env.TZ).endOf('d').toDate(),
          saleRep: new Types.ObjectId(salesRepId),
        },
        { cpsrValue: completedCalls, cpsrTarget: plannedCall - uncontrollableMissCalls },
      );
    } catch (error) {
      printLog('Error when calculating call compliance rate by sale', error);
      return {
        current: 0,
        target: 0,
      };
    }
  }
}
