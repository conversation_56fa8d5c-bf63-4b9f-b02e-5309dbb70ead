import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { BaseService } from '../../shared/services/base-service';
import { SaleRepExecutionAvailability, SaleRepExecutionAvailabilityDocument } from '../schemas';

@Injectable()
export class SaleRepExecutionAvailabilityService extends BaseService<SaleRepExecutionAvailability> {
  constructor(
    @InjectModel(SaleRepExecutionAvailability.name)
    private readonly _model: Model<SaleRepExecutionAvailabilityDocument>,
  ) {
    super();
    this.model = _model;
  }

  async getAvailabilityBySaleRepIdAndOutletId(saleRepId: string, outletId: string) {
    return this._model
      .find({
        saleRep: new Types.ObjectId(saleRepId),
        outlet: new Types.ObjectId(outletId),
      })
      .sort({ updatedAt: -1 })
      .limit(1)
      .populate('saleRep outlet journeyPlan')
      .exec();
  }

  async getAvailabilityByJourneyPlan(journeyPlanId: string) {
    return this._model
      .findOne({
        journeyPlan: new Types.ObjectId(journeyPlanId),
      })
      .populate('saleRep outlet journeyPlan')
      .sort({ updatedAt: -1 });
  }

  async deleteAvailabilityBySaleRepIdAndJourneyPlanId(saleRepId: string, journeyPlanId: string) {
    return this._model
      .deleteOne({
        saleRep: new Types.ObjectId(saleRepId),
        journeyPlan: new Types.ObjectId(journeyPlanId),
      })
      .exec();
  }

  async getAvailabilityById(availabilitiesId: string) {
    return this._model
      .findOne({
        _id: new Types.ObjectId(availabilitiesId),
      })
      .populate('saleRep outlet journeyPlan')
      .exec();
  }
}
