import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { BaseService } from '../../shared/services/base-service';
import { SaleRepExecutionVisibility, SaleRepExecutionVisibilityDocument } from '../schemas';
import { printLog } from '../../utils';

@Injectable()
export class SaleRepExecutionVisibilityService extends BaseService<SaleRepExecutionVisibility> {
  constructor(
    @InjectModel(SaleRepExecutionVisibility.name)
    private readonly _model: Model<SaleRepExecutionVisibilityDocument>,
  ) {
    super();
    this.model = _model;
  }

  async getVisibilityBySaleRepIdAndOutletId(saleRepId: string, outletId: string) {
    try {
      return this._model
        .find({
          saleRep: new Types.ObjectId(saleRepId),
          outlet: new Types.ObjectId(outletId),
        })
        .sort({ updatedAt: -1 })
        .limit(1)
        .populate('saleRep outlet journeyPlan images')
        .exec();
    } catch (e) {
      printLog(e);
    }
  }

  async getVisibilityByJourneyPlan(journeyPlanId: string) {
    return this._model
      .findOne({
        journeyPlan: new Types.ObjectId(journeyPlanId),
      })
      .populate('saleRep outlet journeyPlan images')
      .sort({ updatedAt: -1 });
  }

  async getVisibilityById(visibilitiesId: string) {
    const result = this._model
      .findOne({
        _id: new Types.ObjectId(visibilitiesId),
      })
      .populate('images')
      .exec();
    return result;
  }
}
