import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { OutletJourneyPlanning, OutletJourneyPlanningDocument } from 'src/journey-plannings/schemas/outlet-journey-planning.schema';
import * as moment from 'moment-timezone';
import { VisitStatus } from 'src/journey-plannings/enums/visit-status.enum';
import { User } from 'src/users/schemas/user.schema';
import { findClosetCoordinates } from 'src/utils';
import { GoogleMapsService } from 'src/third-parties/google-maps-services';
import { UsersService } from 'src/users/services/users.service';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { OmsService } from 'src/external/services/oms.service';
import { OutletStatus } from 'src/outlets/enums/outlet-status.enum';

@Injectable()
export class RoutePlanService {
  constructor(
    @InjectModel(OutletJourneyPlanning.name)
    private readonly planModel: Model<OutletJourneyPlanningDocument>,
    private readonly googleMapsService: GoogleMapsService,
    private readonly usersService: UsersService,
    private readonly plansService: OutletJourneyPlanningService,
    private readonly omsService: OmsService,
  ) {}

  async getRoutePlanData({ salesRep }: { salesRep: User }) {
    const startOfCurrencyDate = moment().tz(process.env.TZ).startOf('day').toDate();
    const endOfCurrencyDate = moment().tz(process.env.TZ).endOf('day').toDate();

    let plans = await this.planModel
      .find({
        saleRep: new Types.ObjectId(String(salesRep._id)),
        $or: [
          {
            rescheduled: false,
            day: {
              $lte: endOfCurrencyDate,
              $gte: startOfCurrencyDate,
            },
          },
          {
            rescheduled: true,
            rescheduledDay: {
              $lte: endOfCurrencyDate,
              $gte: startOfCurrencyDate,
            },
          },
        ],
      })
      .populate({
        path: 'outlet',
        match: { status: OutletStatus.ACTIVE },
      });
    plans = plans.filter((o) => o.outlet?.status === OutletStatus.ACTIVE);
    /*this.omsService
      .checkOutletsConnected(plans.map((plan) => plan.outlet))
      .then((listOutletConnected) => {
        const list = listOutletConnected.filter((e) => e.visitStatus !== VisitStatus.COMPLETED).splice(0, 10);
        this.omsService
          .getCachedDataByOutlets(list.map((outlet) => new Types.ObjectId(outlet._id)))
          .then()
          .catch();
      })
      .catch();*/

    const todayProgress = this.transformTodayProgress(plans);
    const todayOutlets = await this.transformTodayOutlets({ plans: plans.map((plan) => plan.toJSON()) as OutletJourneyPlanning[], salesRep });
    return {
      todayProgress,
      todayOutlets,
    };
  }

  private transformTodayProgress(plans: OutletJourneyPlanning[]) {
    return plans.reduce(
      (initial, plan) => {
        const { inPlan, skipped, left } = initial;

        if (plan.visitStatus === VisitStatus.COMPLETED) {
          return {
            ...initial,
            inPlan: inPlan + 1,
          };
        }

        if (plan.visitStatus === VisitStatus.IN_PROGRESS) {
          return {
            ...initial,
            left: left + 1,
          };
        }

        if (plan.missedReason) {
          return {
            ...initial,
            skipped: skipped + 1,
          };
        }

        return {
          ...initial,
          left: left + 1,
        };
      },
      {
        inPlan: 0,
        skipped: 0,
        left: 0,
      },
    );
  }

  async transformTodayOutlets({ plans, salesRep }: { plans: OutletJourneyPlanning[]; salesRep: User }) {
    // use object to avoid duplicate outlet
    const outlets = Object.values(
      plans.reduce((initial, plan) => {
        const outlet = plan.outlet;
        const outletId = String(outlet._id);

        return {
          ...initial,
          [outletId]: this.transformOutlet(plan),
        };
      }, {} as Record<string, any>),
    ).sort((current, next) => (current.plannedDate.isBefore(next.plannedDate) ? -1 : 1));

    if (!process.env.GOOGLE_MAPS_API_KEY) {
      return outlets;
    }

    //Get lat,lng of sales rep
    const originAddress = salesRep?.geoAddress;

    //get lat,lng of outlets
    const outletAddress = plans.map((p) => p.outlet.address)?.filter((address) => address !== null && address !== undefined);

    const priorityPlansCount = plans.every((p) => p.priority === 0);

    if (!originAddress || outletAddress?.length !== plans?.length || (!salesRep.isAddressChanged && !priorityPlansCount)) {
      return outlets;
    }

    //get Destination
    const destinations = await this.googleMapsService.getDirections(originAddress, outletAddress);

    if (destinations?.length) {
      const { startTimeFrame, timeInterval } = await this.plansService.getJourneyPlanSettingBySalesRep(salesRep._id);

      let result = [];
      const coordinates = outletAddress.map((item: any) => {
        const [lat, lng] = item.replace(/\s/g, '').split(',');
        return { lat: parseFloat(lat), lng: parseFloat(lng) };
      });
      const destinationsCoordinates = destinations.map((item: any) => {
        const [lat, lng, priority, distance] = [item.endLatLong.lat, item.endLatLong.lng, item.priority, item.distance];
        return { lat: parseFloat(lat), lng: parseFloat(lng), priority, distance };
      });
      const sortedCoors = findClosetCoordinates(coordinates, destinationsCoordinates);

      let priority = 0;
      const currentDay = new Date().toISOString().split('T')[0];
      const startTime = moment(`${currentDay}T${startTimeFrame}`);
      for (const sortItem of sortedCoors) {
        const objPlan = plans.find((p) => p.outlet.address.replace(/\s/g, '') === `${sortItem.lat},${sortItem.lng}` && !result.find((r) => r.ucc === p.outlet.ucc));
        if (objPlan) {
          ++priority;
          const displayDay = priority === 1 ? startTime : startTime.add(timeInterval, 'minutes');
          result.push({ ...objPlan, priority: sortItem.priority ? sortItem.priority : priority, distance: sortItem.distance, displayDay: displayDay.toDate() });
        }
      }

      //Check plans are missing
      const missedPlans = plans.filter((plan1: any) => !result.some((plan2: any) => String(plan1._id) === String(plan2._id)));
      if (missedPlans.length) {
        result = [
          ...result,
          ...missedPlans.map((m) => {
            return { ...m, priority: ++priority, distance: undefined };
          }),
        ];
      }

      //Update isAddressChanged and Priority
      await Promise.all([this.usersService.addressChangedUpdate(salesRep, false), this.plansService.updateOutletJourneyPlanPriorities(result)]);

      return result.sort((p1, p2) => p1.priority - p2.priority).map(this.transformOutlet);
    }

    await this.planModel.updateMany(
      { _id: { $in: plans.map((plan) => new Types.ObjectId(plan._id)) } },
      {
        $set: {
          priority: 0,
        },
        $unset: {
          distance: 1,
        },
      },
    );

    return outlets;
  }

  private transformOutlet(plan: OutletJourneyPlanning) {
    const outlet = plan.outlet;
    const outletId = String(outlet._id);

    const plannedDate = plan.displayDay || plan.rescheduledDay || plan.day;

    return {
      id: outletId,
      outletId,
      planId: String(plan._id),
      name: outlet.name,
      address: outlet.address,
      contactName: outlet.contactName,
      contactNumber: outlet.contactNumber,
      ucc: outlet.ucc,
      channel: outlet.channel,
      subChannel: outlet.subChannel,
      class: outlet.outletClass,
      checklist: outlet.checklist || [],
      lastUpdatedChecklist: outlet.lastUpdatedChecklist,
      visitStatus: plan.visitStatus,
      isSkipped: !!plan.missedReason,
      plannedDate: moment(plannedDate).tz(process.env.TZ),
      priority: plan.priority,
      distance: plan.distance,
    };
  }
}
