import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { BaseService } from '../../shared/services/base-service';
import { SaleRepFfcStore, SaleRepFfcStoreDocument } from '../schemas';

@Injectable()
export class SaleRepFfcStoreService extends BaseService<SaleRepFfcStore> {
  constructor(
    @InjectModel(SaleRepFfcStore.name)
    private readonly _modelSaleRepFfcStore: Model<SaleRepFfcStoreDocument>,
  ) {
    super();
    this.model = _modelSaleRepFfcStore;
  }

  async findBySaleRepId(userId: string): Promise<SaleRepFfcStoreDocument[]> {
    return this._modelSaleRepFfcStore.find({ saleRep: new Types.ObjectId(userId) });
  }

  async findOneBySaleRepId(userId: string): Promise<SaleRepFfcStoreDocument> {
    return this._modelSaleRepFfcStore.findOne({ saleRep: new Types.ObjectId(userId) });
  }

  async updateBySaleRepUUID(saleRepId: string, update: Record<string, any>) {
    return this._modelSaleRepFfcStore.updateOne(
      { saleRep: new Types.ObjectId(saleRepId) },
      {
        ...update,
        updatedAt: new Date(),
      },
    );
  }
}
