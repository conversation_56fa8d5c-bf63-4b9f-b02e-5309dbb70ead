import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';
import { BrandService } from 'src/brand/services';
import { HintService } from 'src/brand/services/hint.service';
import { BaseJourneyPlanService, DistributorService, DistributorUploadTrackingService, DistributorUserRelationService } from 'src/distributor/services';
import { OutletJourneyPlanningService } from 'src/journey-plannings/services/outlet-journey-planning.service';
import { OutletsService } from 'src/outlets/services/outlets.service';
import { SaleRepOutletRelationService } from 'src/outlets/services/sale-rep-outlet-relation.service';
import { clearCurrentUserTokenCache, isEmptyObjectOrArray, isPhoneNumberValidation, standardPhoneNumber } from 'src/utils';
import { SaleRepExecutionAffordabilityService } from './salerep-execution-affordability.service';
import { SaleRepExecutionAvailabilityService } from './salerep-execution-availability.service';
import { SaleRepExecutionVisibilityService } from './salerep-execution-visibility.service';
import { SaleRepFfcStoreService } from './salerep-ffc-store.service';
import { User, UserDocument } from 'src/users/schemas/user.schema';
import { ImportExportSalesRepColumns, ImportExportSalesRepColumnsID, ImportExportSalesRepColumnsKH } from 'src/admin/enums';
import * as _ from 'lodash';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DistributorUploadTypes } from 'src/distributor/enums';
import { isValidGeoAddress } from 'src/utils/helpers/address';
import { parsePhoneNumberWithError } from 'libphonenumber-js';
import { SalesRepStatus } from 'src/users/enums';
import { listLangSupportImport } from 'src/i18n';
import { ConstantUser } from 'src/utils/constants/user';
import { ConstantRoles } from 'src/utils/constants/role';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConstantEventName } from 'src/utils/events';
import { SharedEvent } from 'src/shared/events/event/shared.event';
import { Cache } from 'cache-manager';
import { DsrTargetService } from 'src/dsr-targets/services';
import { UsersService } from 'src/users/services/users.service';
import { UserDetailService } from '../../users/services/user-detail.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

@Injectable()
export class SaleRepService {
  constructor(
    private readonly outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly brandsService: BrandService,
    private readonly dsrTargetService: DsrTargetService,
    private readonly saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly distributorUserRelationService: DistributorUserRelationService,
    private readonly distributorService: DistributorService,
    private readonly outletsService: OutletsService,
    private readonly saleRepFfcStoreService: SaleRepFfcStoreService,
    private readonly hintService: HintService,
    private readonly salesRepExecutionVisibilityService: SaleRepExecutionVisibilityService,
    private readonly brandSevice: BrandService,
    private readonly salesRepExecutionAvailabilityService: SaleRepExecutionAvailabilityService,
    private readonly salesRepExecutionAffordability: SaleRepExecutionAffordabilityService,
    private readonly _saleRepFfcStoreService: SaleRepFfcStoreService,
    private readonly _usersService: UsersService,
    @InjectModel(User.name)
    private readonly userModel: Model<UserDocument>,
    private readonly _distributorUploadTrackingService: DistributorUploadTrackingService,
    private readonly _baseJourneyPlanService: BaseJourneyPlanService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private eventEmitter: EventEmitter2,
    private readonly userDetailService: UserDetailService,
  ) {}

  async importSalesRepID({ distributorId, currentUser, i18n, file }: { distributorId: string; currentUser: User; i18n: I18nContext; file: Express.Multer.File }) {
    const failure = [];
    const success = [];
    const excelData = await this.distributorService.handleUploadingFile(
      file,
      ImportExportSalesRepColumnsID.map((e) => i18n.translate(`importExport.${e}`)),
      ImportExportSalesRepColumnsID,
      i18n,
    );
    const groupBySalesRepId: Record<string, any> = _.groupBy(excelData, 'salesRepId');
    if (Object.values(groupBySalesRepId).some((v) => v?.length > 1)) {
      throw new BadRequestException('distributor.sales_rep_master_data.duplicated_data');
    }

    const salesRepIds = Object.keys(groupBySalesRepId);
    const salesRep = await this.userModel.find({ saleRepId: { $in: salesRepIds } });
    const salesRepMap = salesRep.reduce((pre, curr) => ({ ...pre, [curr.saleRepId]: curr }), {});

    const validDistributors = (await this.distributorUserRelationService.findAllByUserAdminId(currentUser._id)).map((e) => e.distributor);
    for (let i = 0; i < excelData.length; i++) {
      const { dotUsername, salesRepId } = excelData[i];

      if (!dotUsername || !salesRepId) {
        let fieldName = '';
        for (const key of ImportExportSalesRepColumnsID) {
          if (!excelData[i][key]) {
            fieldName = i18n.translate(`importExport.${key}`);
            break;
          }
        }
        failure.push({
          ...excelData[i],
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.missing_fields', { args: { fieldName, rowNumber: i + 1 } }),
          },
        });
        break;
      }

      const salesRep = salesRepMap[salesRepId];
      if (!salesRep) {
        failure.push({
          ...excelData[i],
          reason: {
            [await i18n.t('distributor.sales_rep_master_data.not_found_sales_rep', { args: { rowNumber: i + 1 } })]: salesRepId,
          },
        });
        continue;
      }

      const distributor = validDistributors.find((e) => e.distributorId === distributorId);
      if (!distributor) {
        failure.push({
          ...excelData[i],
          reason: {
            [await i18n.t('distributor.sales_rep_master_data.invalid_distributor', { args: { rowNumber: i + 1 } })]: distributorId,
          },
        });
        continue;
      }

      const [existedDistributorSaleRepRelation] = await this.distributorUserRelationService.getDistributorUserRelation({
        'u.saleRepId': salesRepId,
      });
      if (existedDistributorSaleRepRelation && existedDistributorSaleRepRelation.dis.distributorId !== distributor.distributorId) {
        failure.push({
          ...excelData[i],
          reason: {
            [await i18n.t('distributor.sales_rep_master_data.invalid_distributor', { args: { rowNumber: i + 1 } })]: distributorId,
          },
        });
        continue;
      }
      if (existedDistributorSaleRepRelation) {
        try {
          await this.saleRepFfcStoreService.updateBySaleRepUUID(existedDistributorSaleRepRelation.u._id, { customerUserName: dotUsername });

          success.push({ ...excelData[i] });
        } catch (e) {
          failure.push({
            ...excelData[i],
            reason: {
              [i18n.t('importExport.message')]: e?.message,
            },
          });
        }
      }
    }
    await this._distributorUploadTrackingService.create({
      distributor: distributorId,
      uploadedBy: currentUser._id,
      uploadedAt: new Date(),
      type: DistributorUploadTypes.SALES_REP_MASTER_DATA,
    });
    return {
      success,
      failure,
    };
  }

  async importSalesRepKH({ distributorId, currentUser, i18n, file }: { distributorId: string; currentUser: User; i18n: I18nContext; file: Express.Multer.File }) {
    const failure = [];
    const success = [];
    const newSalesRepObjectIds = [];
    const excelData = await this.distributorService.handleUploadingFile(
      file,
      ImportExportSalesRepColumnsKH.map((e) => i18n.translate(`importExport.${e}`)),
      ImportExportSalesRepColumnsKH,
      i18n,
    );
    const groupBySalesRepId: Record<string, any> = _.groupBy(excelData, 'salesRepId');
    if (Object.values(groupBySalesRepId).some((v) => v?.length > 1)) {
      throw new BadRequestException('distributor.sales_rep_master_data.duplicated_data');
    }
    const validDistributors = (await this.distributorUserRelationService.findAllByUserAdminId(currentUser._id)).map((e) => e.distributor);
    const addedDistributor = {};
    for (let i = 0; i < excelData.length; i++) {
      const { name, dotUsername, salesRepId, geoAddress } = excelData[i];
      let { mobilePhoneCode, phoneNumber, status } = excelData[i];
      if (!isValidGeoAddress(geoAddress)) {
        failure.push({
          ...excelData[i],
          reason: {
            [await i18n.t('saleRep.invalid_geo_address', { args: { rowNumber: i + 1 } })]: geoAddress,
          },
        });
        continue;
      }

      if (!isPhoneNumberValidation(phoneNumber, mobilePhoneCode)) {
        failure.push({
          ...excelData[i],
          reason: {
            [await i18n.t('distributor.sales_rep_master_data.invalid_phone_number', { args: { rowNumber: i + 1 } })]: phoneNumber,
          },
        });
        continue;
      }
      const mobilePhoneParse = parsePhoneNumberWithError(standardPhoneNumber(phoneNumber, mobilePhoneCode));
      phoneNumber = mobilePhoneParse.number;
      mobilePhoneCode = `+${mobilePhoneParse.countryCallingCode}`;
      if (name && mobilePhoneCode && phoneNumber && dotUsername && salesRepId && status && distributorId && geoAddress) {
        const distributor = validDistributors.find((e) => e.distributorId === distributorId);
        if (!distributor) {
          failure.push({
            ...excelData[i],
            reason: {
              [await i18n.t('distributor.sales_rep_master_data.invalid_distributor', { args: { rowNumber: i + 1 } })]: distributorId,
            },
          });
          continue;
        }
        let listStatus: any = [...Object.values(SalesRepStatus)];
        listLangSupportImport.forEach((lang) => {
          listStatus = [...listStatus, ...Object.values(SalesRepStatus).map((e) => i18n.translate(`importExport.${e}`, { lang }))];
        });
        if (!listStatus.includes(status)) {
          failure.push({
            ...excelData[i],
            reason: {
              [await i18n.t('distributor.sales_rep_master_data.invalid_sales_rep_status', { args: { rowNumber: i + 1 } })]: status,
            },
          });
          continue;
        }
        status = status.toUpperCase();
        listStatus = [...Object.values(SalesRepStatus)];
        for (const iterator of listStatus) {
          const listStatusTranslate = listLangSupportImport.map((lang) => i18n.translate(`importExport.${iterator}`, { lang }).toUpperCase());
          if (listStatusTranslate.includes(status)) {
            status = iterator;
            break;
          }
        }

        const [existedDistributorSaleRepRelation] = await this.distributorUserRelationService.getDistributorUserRelation({
          'u.saleRepId': salesRepId,
        });
        if (existedDistributorSaleRepRelation && existedDistributorSaleRepRelation.dis.distributorId !== distributor.distributorId) {
          failure.push({
            ...excelData[i],
            reason: {
              [await i18n.t('distributor.sales_rep_master_data.invalid_sales_rep_status', { args: { rowNumber: i + 1 } })]: status,
            },
          });
          continue;
        }
        const existedUserPhoneNumber = await this._usersService.findOne({
          mobilePhone: phoneNumber,
          saleRepId: { $ne: salesRepId },
        });
        if (existedUserPhoneNumber) {
          failure.push({
            ...excelData[i],
            reason: {
              [await i18n.t('distributor.sales_rep_master_data.phone_existed', { args: { rowNumber: i + 1 } })]: phoneNumber,
            },
          });
          continue;
        }
        if (existedDistributorSaleRepRelation) {
          if (status === SalesRepStatus.INACTIVE && existedDistributorSaleRepRelation.u.saleRepStatus === SalesRepStatus.ACTIVE) {
            const inProgressPlan = await this.outletJourneyPlanningService.getInProgressTodayVisitBySaleRepIdAndExcludeOutletId(
              existedDistributorSaleRepRelation.u._id.toString(),
              null,
            );
            if (inProgressPlan) {
              failure.push({
                ...excelData[i],
                reason: {
                  [i18n.t('importExport.message')]: await i18n.t('sales_rep.cannot_inactive'),
                },
              });
              continue;
            }
          }
          try {
            // PDH-2865 Change status to re-calculate route by GG Maps API
            const isAddressChanged = existedDistributorSaleRepRelation.u.geoAddress !== geoAddress;
            const mobilePhoneParse = parsePhoneNumberWithError(standardPhoneNumber(phoneNumber, mobilePhoneCode));
            const user = await this._usersService.update(existedDistributorSaleRepRelation.u._id, {
              mobilePhone: mobilePhoneParse.number,
              mobilePhoneCode: `+${mobilePhoneParse.countryCallingCode}`,
              username: name,
              saleRepStatus: status,
              status: status === SalesRepStatus.ACTIVE ? ConstantUser.IS_ACTIVE : ConstantUser.BLOCKED,
              geoAddress,
              isAddressChanged,
            });
            const isSentPasswordLink = await this._usersService.isSentPasswordLink(user._id.toString(), 'salesRep');
            if (!isSentPasswordLink && user.saleRepStatus === SalesRepStatus.ACTIVE) {
              this._usersService
                .sendCreatePasswordUser(
                  {
                    mobilePhone: phoneNumber,
                    user: user,
                  },
                  i18n,
                )
                .then();
            }
            await this.saleRepFfcStoreService.updateBySaleRepUUID(existedDistributorSaleRepRelation.u._id, { customerUserName: dotUsername });

            if (status == SalesRepStatus.INACTIVE && existedDistributorSaleRepRelation?.u?._id) {
              const [tokensData] = await Promise.all([
                this.userDetailService.findOne({
                  userId: existedDistributorSaleRepRelation.u._id.toString(),
                  isUserAdmin: true,
                }),
                this._baseJourneyPlanService.deleteByCondition({ saleRep: existedDistributorSaleRepRelation.u._id }),
                this.outletJourneyPlanningService.deleteUnExecutedPlannedVisits(existedDistributorSaleRepRelation.u._id),
              ]);

              if (!isEmptyObjectOrArray(tokensData)) {
                const { userToken } = tokensData;
                for (const token of userToken) {
                  clearCurrentUserTokenCache(token?.accessToken, this.cacheManager).then();
                }
                await this.userDetailService.addOrUpdate({
                  userId: existedDistributorSaleRepRelation.u._id,
                  isUserAdmin: true,
                  userToken: [],
                });
              }
            }

            success.push({ ...excelData[i] });
          } catch (e) {
            failure.push({
              ...excelData[i],
              reason: {
                [i18n.t('importExport.message')]: e?.message,
              },
            });
          }
        } else {
          let createdUser;
          let createdUserRole;
          let createdSaleRepFFCStore;
          let createdDistributorSalesRepRelation;
          try {
            createdUser = await this._usersService.create({
              mobilePhone: phoneNumber,
              mobilePhoneCode,
              username: name,
              roleId: [ConstantRoles.SALE_REP],
              saleRepId: salesRepId,
              status: status === SalesRepStatus.ACTIVE ? ConstantUser.IS_ACTIVE : ConstantUser.BLOCKED,
              saleRepStatus: status,
              geoAddress,
              // PDH-2865 Change status to re-calculate route by GG Maps API
              isAddressChanged: true,
            });

            createdUserRole = await this.userDetailService.addOrUpdate({
              userId: createdUser._id,
              isUserAdmin: false,
              userRole: [{ roleKey: ConstantRoles.SALE_REP }],
            });

            createdSaleRepFFCStore = await this.saleRepFfcStoreService.create({
              saleRep: createdUser._id,
              customerUserName: dotUsername,
            });
            const distributor = validDistributors.find((e) => e.distributorId === distributorId);
            createdDistributorSalesRepRelation = await this.distributorUserRelationService.create({
              user: createdUser._id,
              distributor: distributor._id,
            });
            if (createdUser.saleRepStatus === SalesRepStatus.ACTIVE) {
              await this._usersService.sendCreatePasswordUser(
                {
                  mobilePhone: phoneNumber,
                  user: createdUser,
                },
                i18n,
              );
            }
            addedDistributor[distributorId] = distributor;
            newSalesRepObjectIds.push(createdUser._id.toString());
            success.push({ ...excelData[i] });
          } catch (e) {
            if (createdUser) {
              await this._usersService.delete(createdUser._id);
            }
            if (createdUserRole) {
              await this._usersService.delete(createdUserRole._id);
            }
            if (createdSaleRepFFCStore) {
              await this.saleRepFfcStoreService.delete(createdSaleRepFFCStore._id);
            }
            if (createdDistributorSalesRepRelation) {
              await this.distributorUserRelationService.delete(createdDistributorSalesRepRelation._id);
            }
            failure.push({
              ...excelData[i],
              reason: {
                [i18n.t('importExport.message')]: e?.message,
              },
            });
          }
        }
      } else {
        let fieldName = '';
        for (const key of ImportExportSalesRepColumnsKH) {
          if (!excelData[i][key]) {
            fieldName = i18n.translate(`importExport.${key}`);
            break;
          }
        }
        failure.push({
          ...excelData[i],
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.missing_fields', { args: { fieldName, rowNumber: i + 1 } }),
          },
        });
      }
    }

    this.eventEmitter.emit(
      ConstantEventName.SHARED_EVENT,
      new SharedEvent({
        callback: async () => {
          await this.dsrTargetService.createTargetForSaleRep(newSalesRepObjectIds);
        },
      }),
    );
    for (const iterator of Object.values(addedDistributor)) {
      await this._distributorUploadTrackingService.create({
        distributor: iterator,
        uploadedBy: currentUser._id,
        uploadedAt: new Date(),
        type: DistributorUploadTypes.SALES_REP_MASTER_DATA,
      });
    }

    return {
      success,
      failure,
    };
  }

  async importSalesRep({ distributorId, currentUser, i18n, file }: { distributorId: string; currentUser: User; i18n: I18nContext; file: Express.Multer.File }) {
    const failure = [];
    const success = [];
    const newSalesRepObjectIds = [];
    const excelData = await this.distributorService.handleUploadingFile(
      file,
      ImportExportSalesRepColumns.map((e) => i18n.translate(`importExport.${e}`)),
      ImportExportSalesRepColumns,
      i18n,
    );
    const groupBySalesRepId: Record<string, any> = _.groupBy(excelData, 'salesRepId');
    if (Object.values(groupBySalesRepId).some((v) => v?.length > 1)) {
      throw new BadRequestException('distributor.sales_rep_master_data.duplicated_data');
    }
    const validDistributors = (await this.distributorUserRelationService.findAllByUserAdminId(currentUser._id)).map((e) => e.distributor);
    const addedDistributor = {};
    for (let i = 0; i < excelData.length; i++) {
      const { name, dotUsername, salesRepId } = excelData[i];
      let { mobilePhoneCode, phoneNumber, status } = excelData[i];
      if (!isPhoneNumberValidation(phoneNumber, mobilePhoneCode)) {
        failure.push({
          ...excelData[i],
          reason: {
            [await i18n.t('distributor.sales_rep_master_data.invalid_phone_number', { args: { rowNumber: i + 1 } })]: phoneNumber,
          },
        });
        continue;
      }
      const mobilePhoneParse = parsePhoneNumberWithError(standardPhoneNumber(phoneNumber, mobilePhoneCode));
      phoneNumber = mobilePhoneParse.number;
      mobilePhoneCode = `+${mobilePhoneParse.countryCallingCode}`;
      if (name && mobilePhoneCode && phoneNumber && dotUsername && salesRepId && status && distributorId) {
        const distributor = validDistributors.find((e) => e.distributorId === distributorId);
        if (!distributor) {
          failure.push({
            ...excelData[i],
            reason: {
              [await i18n.t('distributor.sales_rep_master_data.invalid_distributor', { args: { rowNumber: i + 1 } })]: distributorId,
            },
          });
          continue;
        }
        let listStatus: any = [...Object.values(SalesRepStatus)];
        listLangSupportImport.forEach((lang) => {
          listStatus = [...listStatus, ...Object.values(SalesRepStatus).map((e) => i18n.translate(`importExport.${e}`, { lang }))];
        });
        if (!listStatus.includes(status)) {
          failure.push({
            ...excelData[i],
            reason: {
              [await i18n.t('distributor.sales_rep_master_data.invalid_sales_rep_status', { args: { rowNumber: i + 1 } })]: status,
            },
          });
          continue;
        }
        status = status.toUpperCase();
        listStatus = [...Object.values(SalesRepStatus)];
        for (const iterator of listStatus) {
          const listStatusTranslate = listLangSupportImport.map((lang) => i18n.translate(`importExport.${iterator}`, { lang }).toUpperCase());
          if (listStatusTranslate.includes(status)) {
            status = iterator;
            break;
          }
        }

        const [existedDistributorSaleRepRelation] = await this.distributorUserRelationService.getDistributorUserRelation({
          'u.saleRepId': salesRepId,
        });
        if (existedDistributorSaleRepRelation && existedDistributorSaleRepRelation.dis.distributorId !== distributor.distributorId) {
          failure.push({
            ...excelData[i],
            reason: {
              [await i18n.t('distributor.sales_rep_master_data.invalid_sales_rep_status', { args: { rowNumber: i + 1 } })]: status,
            },
          });
          continue;
        }
        const existedUserPhoneNumber = await this._usersService.findOne({
          mobilePhone: phoneNumber,
          saleRepId: { $ne: salesRepId },
        });
        if (existedUserPhoneNumber) {
          failure.push({
            ...excelData[i],
            reason: {
              [await i18n.t('distributor.sales_rep_master_data.phone_existed', { args: { rowNumber: i + 1 } })]: phoneNumber,
            },
          });
          continue;
        }
        if (existedDistributorSaleRepRelation) {
          if (status === SalesRepStatus.INACTIVE && existedDistributorSaleRepRelation.u.saleRepStatus === SalesRepStatus.ACTIVE) {
            const inProgressPlan = await this.outletJourneyPlanningService.getInProgressTodayVisitBySaleRepIdAndExcludeOutletId(
              existedDistributorSaleRepRelation.u._id.toString(),
              null,
            );
            if (inProgressPlan) {
              failure.push({
                ...excelData[i],
                reason: {
                  [i18n.t('importExport.message')]: await i18n.t('sales_rep.cannot_inactive'),
                },
              });
              continue;
            }
          }
          try {
            const mobilePhoneParse = parsePhoneNumberWithError(standardPhoneNumber(phoneNumber, mobilePhoneCode));
            const user = await this._usersService.update(existedDistributorSaleRepRelation.u._id, {
              mobilePhone: mobilePhoneParse.number,
              mobilePhoneCode: `+${mobilePhoneParse.countryCallingCode}`,
              username: name,
              saleRepStatus: status,
              status: status === SalesRepStatus.ACTIVE ? ConstantUser.IS_ACTIVE : ConstantUser.BLOCKED,
            });
            const isSentPasswordLink = await this._usersService.isSentPasswordLink(user._id.toString(), 'salesRep');
            if (!isSentPasswordLink && user.saleRepStatus === SalesRepStatus.ACTIVE) {
              this._usersService
                .sendCreatePasswordUser(
                  {
                    mobilePhone: phoneNumber,
                    user: user,
                  },
                  i18n,
                )
                .then();
            }
            await this._saleRepFfcStoreService.updateBySaleRepUUID(existedDistributorSaleRepRelation.u._id, { customerUserName: dotUsername });

            if (status == SalesRepStatus.INACTIVE && existedDistributorSaleRepRelation?.u?._id) {
              //Delete un-executed JP of Inactive Sales Rep
              await this.outletJourneyPlanningService.deleteUnExecutedPlannedVisits(existedDistributorSaleRepRelation.u._id);
              const tokensData = await this.userDetailService.findOne({
                userId: existedDistributorSaleRepRelation.u._id.toString(),
                isUserAdmin: true,
              });
              if (!isEmptyObjectOrArray(tokensData)) {
                const { userToken } = tokensData;
                for (const token of userToken) {
                  clearCurrentUserTokenCache(token?.accessToken, this.cacheManager).then();
                }
                await this.userDetailService.addOrUpdate({
                  userId: existedDistributorSaleRepRelation.u._id,
                  isUserAdmin: true,
                  userToken: [],
                });
              }
            }

            success.push({ ...excelData[i] });
          } catch (e) {
            failure.push({
              ...excelData[i],
              reason: {
                [i18n.t('importExport.message')]: e?.message,
              },
            });
          }
        } else {
          let createdUser;
          let createdUserRole;
          let createdSaleRepFFCStore;
          let createdDistributorSalesRepRelation;
          try {
            createdUser = await this._usersService.create({
              mobilePhone: phoneNumber,
              mobilePhoneCode,
              username: name,
              roleId: [ConstantRoles.SALE_REP],
              saleRepId: salesRepId,
              status: status === SalesRepStatus.ACTIVE ? ConstantUser.IS_ACTIVE : ConstantUser.BLOCKED,
              saleRepStatus: status,
            });

            createdUserRole = await this.userDetailService.addOrUpdate({
              userId: createdUser._id,
              isUserAdmin: false,
              userRole: [{ roleKey: ConstantRoles.SALE_REP }],
            });
            createdSaleRepFFCStore = await this._saleRepFfcStoreService.create({
              saleRep: createdUser._id,
              customerUserName: dotUsername,
            });
            const distributor = validDistributors.find((e) => e.distributorId === distributorId);
            createdDistributorSalesRepRelation = await this.distributorUserRelationService.create({
              user: createdUser._id,
              distributor: distributor._id,
            });
            if (createdUser.saleRepStatus === SalesRepStatus.ACTIVE) {
              await this._usersService.sendCreatePasswordUser(
                {
                  mobilePhone: phoneNumber,
                  user: createdUser,
                },
                i18n,
              );
            }
            addedDistributor[distributorId] = distributor;
            newSalesRepObjectIds.push(createdUser._id.toString());
            success.push({ ...excelData[i] });
          } catch (e) {
            if (createdUser) {
              await this._usersService.delete(createdUser._id);
            }
            if (createdUserRole) {
              await this.userDetailService.addOrUpdate({
                userId: createdUser._id,
                isUserAdmin: false,
                userRole: [],
              });
            }
            if (createdSaleRepFFCStore) {
              await this._saleRepFfcStoreService.delete(createdSaleRepFFCStore._id);
            }
            if (createdDistributorSalesRepRelation) {
              await this.distributorUserRelationService.delete(createdDistributorSalesRepRelation._id);
            }
            failure.push({
              ...excelData[i],
              reason: {
                [i18n.t('importExport.message')]: e?.message,
              },
            });
          }
        }
      } else {
        let fieldName = '';
        for (const key of ImportExportSalesRepColumns) {
          if (!excelData[i][key]) {
            fieldName = i18n.translate(`importExport.${key}`);
            break;
          }
        }
        failure.push({
          ...excelData[i],
          reason: {
            [i18n.t('importExport.message')]: i18n.t('distributor.missing_fields', { args: { fieldName, rowNumber: i + 1 } }),
          },
        });
      }
    }

    this.eventEmitter.emit(
      ConstantEventName.SHARED_EVENT,
      new SharedEvent({
        callback: async () => {
          await this.dsrTargetService.createTargetForSaleRep(newSalesRepObjectIds);
        },
      }),
    );
    for (const iterator of Object.values(addedDistributor)) {
      await this._distributorUploadTrackingService.create({
        distributor: iterator,
        uploadedBy: currentUser._id,
        uploadedAt: new Date(),
        type: DistributorUploadTypes.SALES_REP_MASTER_DATA,
      });
    }

    return {
      success,
      failure,
    };
  }
}
