import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UnauthorizedException,
  UseGuards,
  Version,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { Model, Types } from 'mongoose';
import { I18n, I18nContext } from 'nestjs-i18n';
import { Cache } from 'cache-manager';
import { parsePhoneNumberWithError } from 'libphonenumber-js';
import * as moment from 'moment-timezone';
import { isMissedJourneyPlan } from 'src/shared/helpers';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { BrandService } from '../brand/services';
import { HintService } from '../brand/services/hint.service';
import { BaseJourneyPlanService, DistributorService, DistributorUserRelationService } from '../distributor/services';
import { DsrTargetService } from '../dsr-targets/services';
import { FilesService } from '../files/services';
import { JourneyPlanDto } from '../journey-plannings/dtos/journey-plan.dto';
import { VisitStatus } from '../journey-plannings/enums/visit-status.enum';
import { OutletJourneyPlanningService } from '../journey-plannings/services/outlet-journey-planning.service';
import { NotificationLogService } from '../message/services';
import { CommonSearchDto } from '../outlets/dtos/common-search.dto';
import { OutletClassType } from '../outlets/enums/outlet-class.enum';
import { OutletStatus } from '../outlets/enums/outlet-status.enum';
import { OutletsService } from '../outlets/services/outlets.service';
import { SaleRepOutletRelationService } from '../outlets/services/sale-rep-outlet-relation.service';
import { SettingsService } from '../settings/settings.service';
import { ApiException } from '../shared/api-exception.model';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { Roles } from '../shared/decorator/roles.decorator';
import { PaginationDto } from '../shared/dtos/pagination.dto';
import { SharedEvent } from '../shared/events/event/shared.event';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Serialize } from '../shared/interceptor/serialize.interceptor';
import { ApiResponse } from '../shared/response/api-response';
import { SalesRepStatus, SalesRepStatusValue } from '../users/enums';
import { User } from '../users/schemas/user.schema';
import { UsersService } from '../users/services/users.service';
import { clearCurrentUserTokenCache, generateOutletKey, isEmptyObjectOrArray, isPhoneNumberValidation, standardPhoneNumber, toListResponse } from '../utils';
import { ConstantCommons } from '../utils/constants';
import { ConstantRoles } from '../utils/constants/role';
import { ConstantUser } from '../utils/constants/user';
import { ConstantEventName } from '../utils/events';
import { MAX_LENGTH_NOTE, X_CURRENCY_NUMBER_FORMAT } from './constants';
import {
  CreateNoteOutletDto,
  CreateUpdateAvailabilityDto,
  CreateUpdateSalesRepDto,
  CreateUpdateVisibilityDto,
  ExecuteVisitDto,
  FetchedAvailabilityDto,
  FetchedVisibilityDto,
  GetSalesRepDto,
} from './dtos';
import { CreateUpdateAffordabilityDto } from './dtos/create-update-affordability.dto';
import { CreateUpdateMultipleAffordabilitiesDto } from './dtos/create-update-multiple-affordabilities.dto';
import { FetchedAffordabilityDto } from './dtos/fetched-affordability.dto';
import { convertPlanData } from './helpers';
import { SaleRepExecutionAvailabilityService, SaleRepExecutionVisibilityService, SaleRepFfcStoreService, SaleRepStatisticService } from './services';
import { SaleRepExecutionAffordabilityService } from './services/salerep-execution-affordability.service';
import { SaleRepService } from './services/salerep.service';
import { JourneyPlanMissedReasonHistoriesService } from 'src/journey-plannings/services/journey-plan-missed-reason-history.service';
import { isValidGeoAddress } from 'src/utils/helpers/address';
import { JourneyPlanType } from '../journey-plannings/enums/journey-plan-type.enum';
import { MissReasonsService } from 'src/miss-reasons/miss-reasons.service';
import { OpCos } from 'src/config';
import { Distributor, DistributorDocument } from 'src/distributor/schemas';
import { InjectModel } from '@nestjs/mongoose';
import { RoutePlanService } from './services/route-plan.service';
import { UserDetailService } from '../users/services/user-detail.service';
import { OmsSalesRepStatisticsService } from 'src/oms/services/sales-rep-statistics.service';
import { GoogleMapsService } from 'src/third-parties/google-maps-services';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { GetHomePerformanceDto, GetVisitedRouteDto } from './dtos/get-visited-route.dto';
import { UserActionsService } from '../users/services/user-actions.service';

@ApiTags('Sale Rep')
@Controller('api/sale-rep')
@ApiHeader({ name: 'locale', description: 'en' })
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class SaleRepController {
  constructor(
    private readonly _saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly _outletJourneyPlanningService: OutletJourneyPlanningService,
    private readonly _notificationLogService: NotificationLogService,
    private readonly _saleRepFfcStoreService: SaleRepFfcStoreService,
    private readonly _userService: UsersService,
    private readonly _outletService: OutletsService,
    private readonly _settingService: SettingsService,
    private eventEmitter: EventEmitter2,
    private readonly _saleRepExecutionVisibilityService: SaleRepExecutionVisibilityService,
    private readonly _saleRepExecutionAvailabilityService: SaleRepExecutionAvailabilityService,
    private readonly _saleRepExecutionAffordabilityService: SaleRepExecutionAffordabilityService,
    private readonly _hintService: HintService,
    private readonly _filesServices: FilesService,
    private readonly _brandService: BrandService,
    private readonly _saleRepStatisticService: SaleRepStatisticService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _dsrTargetService: DsrTargetService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly _salerepService: SaleRepService,
    private readonly _distributorService: DistributorService,
    private readonly _journeyPlanMissedReasonHistoriesService: JourneyPlanMissedReasonHistoriesService,
    private readonly missReasonsService: MissReasonsService,
    @InjectModel(Distributor.name)
    private readonly distributorModel: Model<DistributorDocument>,
    private readonly _baseJourneyPlanService: BaseJourneyPlanService,
    private readonly routePlanService: RoutePlanService,
    private readonly _userDetailService: UserDetailService,
    private readonly omsSalesRepStatisticsService: OmsSalesRepStatisticsService,
    private readonly googleMapsService: GoogleMapsService,
    private readonly _userActionsService: UserActionsService,
  ) {}

  /**
   *
   * @param saleRepId
   * @param i18n
   * @param currentUser
   */
  @Get(':saleRepId/overview')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  async getOverview(@Param('saleRepId') saleRepId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    if (currentUser._id.toString() !== saleRepId) {
      throw new UnauthorizedException(await i18n.t('plan.unauthorized'));
    }
    let allTodayPlansOfSaleRep;
    if (process.env.OPCO == OpCos.Cambodia) {
      allTodayPlansOfSaleRep = await this._outletJourneyPlanningService.getJourneyPlansKH(saleRepId, JourneyPlanType.TODAY, '', currentUser);
    } else {
      allTodayPlansOfSaleRep = await this._outletJourneyPlanningService.getJourneyPlans(saleRepId);
    }
    const todayPlans = plainToInstance(JourneyPlanDto, allTodayPlansOfSaleRep, {
      excludeExtraneousValues: true,
    });
    const statisticDay = moment().tz(process.env.TZ).endOf('d').toISOString();
    const [statisticRecord, cpsr, dsrTarget, totalNotifications] = await Promise.all([
      this._saleRepStatisticService.getStatisticBySaleRepIdsAndGivenTime(saleRepId, statisticDay),
      this._outletJourneyPlanningService.calculateCPSR(saleRepId),
      this._dsrTargetService.getCurrentDSRTargetBySaleRepId(saleRepId),
      this._notificationLogService.countUnreadNotification(saleRepId),
    ]);
    const cpsrValue = cpsr.current ?? 0;
    const cpsrTarget = cpsr.target ?? 1;
    const salesValue = statisticRecord?.salesValue ?? 0;
    const maboValue = statisticRecord?.maboValue ?? 0;
    const maboStatisticTarget = statisticRecord?.maboTarget ?? 1;
    let salesTarget = 0;
    let maboTarget = 100;
    if (dsrTarget) {
      salesTarget = dsrTarget.salesTarget;
      maboTarget = dsrTarget.maboTarget;
    }
    let sales = {
      unit: 'RM',
      minValue: 0,
      maxValue: salesTarget * X_CURRENCY_NUMBER_FORMAT,
      currentValue: parseInt((salesValue * X_CURRENCY_NUMBER_FORMAT).toFixed(0)),
    };
    if (process.env.OPCO == OpCos.Indonesia) {
      maboTarget = 0;
      sales = {
        unit: 'HL',
        minValue: 0,
        maxValue: Math.round(salesTarget * 1000) / 1000,
        currentValue: Math.round(salesValue * 1000) / 1000,
      };
    }
    const thisMonth = {
      cpsr: {
        unit: '%',
        minValue: 0,
        maxValue: 100,
        currentValue: Math.round(Number(cpsrValue / (cpsrTarget || 1)) * 100),
      },
      sales: sales,
      mabo: {
        unit: '%',
        minValue: 0,
        maxValue: maboTarget,
        currentValue: Math.round((maboValue / (maboStatisticTarget || 1)) * 100),
      },
    };
    return new ApiResponse({ totalNotifications, thisMonth, today: todayPlans });
  }

  @Version(['3', '4', '5', '6'])
  @Get('home/performance')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Get data performance tab DSR 2.0',
  })
  async getPerformanceE360(@Query() dto: GetHomePerformanceDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    let { fromDate, toDate } = dto;
    if (fromDate) {
      fromDate = moment(fromDate).tz(process.env.TZ).startOf('day').toDate();
    } else {
      fromDate = moment().tz(process.env.TZ).startOf('month').toDate();
    }

    if (toDate) {
      toDate = moment(toDate).tz(process.env.TZ).endOf('day').toDate();
    } else {
      toDate = moment().tz(process.env.TZ).endOf('month').toDate();
    }

    const data = await this.omsSalesRepStatisticsService.getStatisticE360(String(currentUser._id), fromDate, toDate, i18n);
    return new ApiResponse(data);
  }

  @Get('home/performance')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Get data performance tab DSR 2.0',
  })
  async getPerformance(@I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    const data = await this.omsSalesRepStatisticsService.getStatistics(String(currentUser._id));
    return new ApiResponse(data);
  }

  @Version(['3', '4', '5', '6'])
  @Get('home/performance/inactive-outlet')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Get data performance tab DSR 2.0 inactive-outlet',
  })
  async getPerformanceInactiveOutlet(@Query() dto: GetHomePerformanceDto, @Query() { skip, limit }: PaginationDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    let { fromDate, toDate } = dto;
    if (fromDate) {
      fromDate = moment(fromDate).tz(process.env.TZ).startOf('day').toDate();
    } else {
      fromDate = moment().tz(process.env.TZ).startOf('month').toDate();
    }

    if (toDate) {
      toDate = moment(toDate).tz(process.env.TZ).endOf('day').toDate();
    } else {
      toDate = moment().tz(process.env.TZ).endOf('month').toDate();
    }

    const data = await this.omsSalesRepStatisticsService.getStatisticInactiveOutlet(String(currentUser._id), fromDate, toDate, Number(skip), Number(limit));
    return new ApiResponse(data);
  }

  @Version(['3', '4', '5', '6'])
  @Get('home/performance/visited-route')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Get data performance visited route 3tabs REP 2.0',
  })
  async getPerformanceVisitedRoute(@Query() dto: GetVisitedRouteDto, @Query() { skip, limit }: PaginationDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    const { filterVisitedStatus } = dto;
    let { fromDate, toDate } = dto;
    if (fromDate) {
      fromDate = moment(fromDate).tz(process.env.TZ).startOf('day').toDate();
    } else {
      fromDate = moment().tz(process.env.TZ).startOf('month').toDate();
    }

    if (toDate) {
      toDate = moment(toDate).tz(process.env.TZ).endOf('day').toDate();
    } else {
      toDate = moment().tz(process.env.TZ).endOf('month').toDate();
    }
    const data = await this.omsSalesRepStatisticsService.getStatisticOutletPlans(String(currentUser._id), fromDate, toDate, filterVisitedStatus, Number(skip), Number(limit), i18n);
    return new ApiResponse(data);
  }

  @Get('home/notifications/unread')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Get number of unread notification',
  })
  async getNumberOfUnreadNotifiation(@I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    const data = await this._notificationLogService.countUnreadNotification(currentUser._id);
    return new ApiResponse(data);
  }

  @Put('outlet-connected/:saleRepId/:outletId')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  async updateNote(
    @Param('saleRepId') saleRepId: string,
    @Param('outletId') outletId: string,
    @Body() createNoteOutletDto: CreateNoteOutletDto,
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser: User,
  ) {
    const { note } = createNoteOutletDto;
    if (currentUser._id.toString() !== saleRepId) {
      throw new UnauthorizedException(await i18n.t('plan.unauthorized'));
    }
    const saleRepOutletConnected = await this._saleRepOutletRelationService.findByOutletConnected(saleRepId, outletId);
    if (!saleRepOutletConnected) {
      throw new HttpException('NOT_FOUND_OUTLET', HttpStatus.BAD_REQUEST);
    }
    if (note?.length > MAX_LENGTH_NOTE) {
      throw new BadRequestException(await i18n.t('saleRep.invalid_note', { args: { numberOfCharacters: MAX_LENGTH_NOTE } }));
    }

    const noteUpdatedAt = createNoteOutletDto.note !== undefined && createNoteOutletDto.note !== saleRepOutletConnected.note ? new Date() : saleRepOutletConnected.noteUpdatedAt;

    await this._saleRepOutletRelationService.update(saleRepOutletConnected._id, { ...createNoteOutletDto, noteUpdatedAt });
    return new ApiResponse({
      ucc: saleRepOutletConnected?.outlet?.ucc,
      name: saleRepOutletConnected?.outlet?.name,
      area: saleRepOutletConnected?.outlet?.area,
      outletClass: saleRepOutletConnected?.outlet?.outletClass ?? OutletClassType.B1,
      type: saleRepOutletConnected?.outlet?.type,
      address: saleRepOutletConnected?.outlet?.address,
      contactName: saleRepOutletConnected?.outlet?.contactName,
      contactNumber: saleRepOutletConnected?.outlet?.contactNumber,
      note: createNoteOutletDto.note || saleRepOutletConnected?.createdAt,
      noteUpdatedAt: noteUpdatedAt,
    });
  }

  @Get(':saleRepId/missed-outlets')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Get missed outlets of sales rep',
  })
  async getMissedOutlets(@Param('saleRepId') saleRepId: string, @Query() { skip, limit }: PaginationDto, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    if (currentUser._id.toString() !== saleRepId) {
      throw new UnauthorizedException(await i18n.t('plan.unauthorized'));
    }
    const missedPlans = await this._outletJourneyPlanningService.getAllMissedOutletOfSaleRep(saleRepId, Number(skip), Number(limit), i18n);

    const result = await Promise.all(
      missedPlans.map(async (mp) => {
        const missReason = mp.missedReason;

        return {
          ...plainToInstance(JourneyPlanDto, mp, {
            excludeExtraneousValues: true,
          }),
          cancel: missReason ? !missReason.reschedulable : !!mp.cancel,
          cancellationReason: missReason ? missReason.reason : mp.cancellationReason ? await i18n.t(mp.cancellationReason) : '',
          reschedulable: !missReason || !Object.keys(missReason).length || missReason?.reschedulable,
        };
      }),
    );

    return new ApiResponse(result);
  }

  @Get(':saleRepId/outlets/searching')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SALE_REP)
  @Serialize(JourneyPlanDto)
  @ApiOperation({
    summary: 'Search outlet by name and UCC',
  })
  async searchOutlet(@Param('saleRepId') saleRepId: string, @Query() searchOutletDto: CommonSearchDto) {
    const { query, skip, limit } = searchOutletDto;
    const activatedRelation = await this._saleRepOutletRelationService.getActiveSaleRepOutletRelationsV2(saleRepId, query);
    const listActivateOutlet = activatedRelation.map((ar) => ar?.outlet?._id);
    const inCurrentCycleData = await this._outletJourneyPlanningService.searchOutletByNameOrUCCV2(saleRepId, true, listActivateOutlet);
    const convertedInCurrentCycleData = convertPlanData(inCurrentCycleData, true);
    const excludeOutlets = convertedInCurrentCycleData.map((item) => item.outlet._id);
    const listRemainingActivatedOutlet = listActivateOutlet.filter((item) => !excludeOutlets.some((ex) => ex.toString() === item.toString()));
    const notInCurrentCycleData = await this._outletJourneyPlanningService.searchOutletByNameOrUCCV2(saleRepId, false, listRemainingActivatedOutlet);
    const convertedNotInCurrentCycleData = convertPlanData(notInCurrentCycleData);
    const excludeOutletsNInC = convertedNotInCurrentCycleData.map((item) => item.outlet._id);

    //Outlet dont have JP
    const activatedRelationNoJP = listActivateOutlet.filter((item) => ![...excludeOutlets, ...excludeOutletsNInC].some((ex) => ex.toString() === item.toString()));
    let outlets = [];

    if (activatedRelationNoJP?.length) {
      const outletsData = await this._outletService.findAll({
        _id: { $in: activatedRelationNoJP },
        status: OutletStatus.ACTIVE,
      });

      outlets = await Promise.all(
        outletsData.map(async (outlet) => {
          return {
            _id: outlet._id,
            day: '',
            rescheduled: false,
            rescheduledDay: '',
            cancel: false,
            outlet: {
              _id: outlet._id,
              ucc: outlet.ucc,
              name: outlet.name,
              area: outlet.area,
              type: outlet.type,
              outletClass: outlet.outletClass,
              address: outlet.address,
              contactName: outlet.contactName,
              contactNumber: outlet.contactNumber,
            },
            outletDataKey: generateOutletKey(outlet),
            visitStatus: '',
          };
        }),
      );
    }

    const allData = [...convertedInCurrentCycleData, ...convertedNotInCurrentCycleData, ...outlets];

    const totalItem = allData.length;
    let paginationData = [];
    if (+skip >= 0 && +limit > 0) {
      paginationData = [...allData].slice(parseInt(String(skip)), parseInt(String(skip)) + parseInt(String(limit)));
    } else {
      paginationData = allData;
    }
    const today = moment().tz(process.env.TZ);

    paginationData = await Promise.all(
      paginationData.map(async (p) => {
        p.outletDataKey = generateOutletKey(p?.outlet);
        p.rescheduledDay = !p.rescheduled ? '' : p.rescheduledDay; //Remove timestamp for search
        p.day = p.rescheduled ? '' : p.day; //Remove timestamp for search
        p.isWillMiss = p.visitStatus !== VisitStatus.COMPLETED && !isMissedJourneyPlan(p.rescheduledDay || p.day) && !!p.missedReason;
        return p;
      }),
    );

    return new ApiResponse({ totalItem, data: paginationData });
  }

  @Get(':saleRepId/outlets')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Get all outlets of sales rep',
  })
  @ApiBadRequestResponse({ type: ApiException })
  async getAllOutletsOfSaleRep(@Param('saleRepId') saleRepId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    if (currentUser._id.toString() !== saleRepId) {
      throw new UnauthorizedException(await i18n.t('plan.unauthorized'));
    }
    const associationList = await this._saleRepOutletRelationService.findAllActiveOutletBySaleRepId(saleRepId);
    const result = associationList.map((associationItem) => {
      const { _id, ucc, name, area, type, address, contactName, contactNumber, status, channel, subChannel, businessSegment } = associationItem.outlet;
      return {
        _id,
        ucc,
        name,
        area,
        type,
        address,
        contactName,
        contactNumber,
        status,
        channel,
        subChannel,
        businessSegment,
      };
    });
    return new ApiResponse(result);
  }

  async getVisibilityID({ salesRepId, outletId }: { salesRepId: string; outletId: string }) {
    const existedTodayPlan = await this._outletJourneyPlanningService.getTodayPlanBySaleRepIdAndOutletId(salesRepId, outletId);
    const readonly = !existedTodayPlan || [VisitStatus.START_VISIT].includes(existedTodayPlan?.visitStatus as VisitStatus);

    //Get Hints
    const outlet = await this._outletService.findById(outletId);
    const hintsObj = await this._hintService.findAllHintsByChannelAndSubChannel(outlet.businessSegment, outlet.subChannel);
    const hints = hintsObj?.hints?.map((h) => h.name)?.join('\n');
    const [result] = await this._saleRepExecutionVisibilityService.getVisibilityBySaleRepIdAndOutletId(salesRepId, outletId);
    if (!result) {
      return new ApiResponse({
        forwardStock: 0,
        images: [],
        readonly,
        hasHint: !!hints,
        hints,
      });
    }
    return new ApiResponse({ ...result.toObject(), readonly, hasHint: !!hints, hints, lastUpdatedOn: result.updatedAt });
  }

  @Get('outlets/:outletId/execution/visibility')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Get visibility',
  })
  @Serialize(FetchedVisibilityDto)
  async getVisibility(@Param('outletId') outletId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    if (process.env.OPCO == OpCos.Indonesia) {
      return this.getVisibilityID({ salesRepId: currentUser._id, outletId });
    }

    const existedTodayPlan = await this._outletJourneyPlanningService.getTodayPlanBySaleRepIdAndOutletId(currentUser._id, outletId);
    const readonly = !existedTodayPlan || [VisitStatus.START_VISIT].includes(existedTodayPlan?.visitStatus as VisitStatus);

    //Get Hints
    const outlet = await this._outletService.findById(outletId);
    const hintsObj = await this._hintService.findAllHintsByChannelAndSubChannel(outlet.businessSegment, outlet.subChannel);
    const hints = hintsObj?.hints?.map((h) => h.name)?.join('\n');

    const defaultVisibility = {
      forwardStock: 0,
      images: [],
      readonly,
      hasHint: !!hints,
      hints,
    };

    if (!existedTodayPlan) {
      return new ApiResponse(defaultVisibility);
    }

    const todayVisibility = await this._saleRepExecutionVisibilityService.getVisibilityByJourneyPlan(existedTodayPlan._id);

    if (!todayVisibility) {
      return new ApiResponse(defaultVisibility);
    }
    return new ApiResponse({ ...todayVisibility.toObject(), readonly, hasHint: !!hints, hints });
  }

  @Put('outlets/:outletId/execution/visibility')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Create or update visibility of today plan',
  })
  @Serialize(FetchedVisibilityDto)
  async createOrUpdateVisibility(@Param('outletId') outletId: string, @Body() createUpdateVisibility: CreateUpdateVisibilityDto, @CurrentUser() currentUser: User) {
    const existedTodayPlan = await this._outletJourneyPlanningService.getTodayPlanBySaleRepIdAndOutletId(currentUser._id, outletId);
    const readonly = !existedTodayPlan || [VisitStatus.START_VISIT].includes(existedTodayPlan?.visitStatus as VisitStatus);
    if (readonly) {
      throw new BadRequestException('execution.cannot_edit_data');
    }
    const { imageIds, forwardStock } = createUpdateVisibility;
    const [closestVisibility] = await this._saleRepExecutionVisibilityService.getVisibilityBySaleRepIdAndOutletId(currentUser._id, outletId);
    const listImages = await this._filesServices.findAll({ _id: { $in: imageIds.map((imgId) => new Types.ObjectId(imgId)) } });
    let result = null;

    if (!closestVisibility) {
      result = await this._saleRepExecutionVisibilityService.create({
        saleRep: new Types.ObjectId(currentUser._id),
        outlet: new Types.ObjectId(outletId),
        journeyPlan: existedTodayPlan._id,
        images: listImages,
        forwardStock,
      });
    } else {
      if (closestVisibility.journeyPlan && existedTodayPlan && closestVisibility.journeyPlan._id.toString() === existedTodayPlan._id.toString()) {
        result = await this._saleRepExecutionVisibilityService.update(closestVisibility._id, {
          forwardStock,
          images: listImages,
          updatedAt: Date.now(),
        });
        // Remove images
        const deletedImages = closestVisibility.images.filter((img) => !listImages.some((i) => i._id.toString() === img._id.toString()));
        await this._filesServices.deleteFiles(deletedImages);
        // End of removing images
      } else {
        // Avoid upload images of the closest plan
        if (closestVisibility.images.some((img) => imageIds.some((imgId) => imgId === img._id.toString()))) {
          throw new BadRequestException('execution.visibility.invalid_images');
        }
        result = await this._saleRepExecutionVisibilityService.create({
          saleRep: new Types.ObjectId(currentUser._id),
          outlet: new Types.ObjectId(outletId),
          journeyPlan: existedTodayPlan._id,
          images: listImages,
          forwardStock,
        });
      }
    }
    await this._filesServices.removeExpiredDate(imageIds);

    const [closestAvailability] = await this._saleRepExecutionAvailabilityService.getAvailabilityBySaleRepIdAndOutletId(currentUser._id, outletId);
    if (!closestAvailability || existedTodayPlan._id.toString() !== closestAvailability.journeyPlan._id.toString()) {
      let allBrandsFromDB;
      if ([OpCos.Cambodia, OpCos.Indonesia].includes(process.env.OPCO)) {
        allBrandsFromDB = await this._brandService.findAll({ isActive: true }, null, { sort: { updatedAt: 1 } });
      } else {
        allBrandsFromDB = await this._brandService.findAll({});
      }
      // sort list closestAvailability like allBrandsFromDB
      let brands = allBrandsFromDB.map((b) => ({ name: b.name, hasQt: b.hasQt, hasPt: b.hasPt, hasCan: b.hasCan, hasKeg: b.hasKeg, hasBcan: b.hasBcan }));
      if (closestAvailability?.brands?.length) {
        brands = [];
        for (const iterator of allBrandsFromDB) {
          const exited = closestAvailability.brands.find((b) => b.name === iterator.name);
          if (exited) brands.push(exited);
        }
      }
      await this._saleRepExecutionAvailabilityService.create({
        saleRep: currentUser._id,
        outlet: new Types.ObjectId(outletId),
        journeyPlan: existedTodayPlan._id,
        brands,
      });
    }

    await this._outletJourneyPlanningService.update(existedTodayPlan._id, {
      visitStatus: VisitStatus.COMPLETED,
      updatedAt: Date.now(),
    });
    this._outletJourneyPlanningService.checkHasOrder(String(existedTodayPlan.saleRep._id), String(existedTodayPlan.outlet._id)).then().catch();

    //Get Hints
    const outlet = await this._outletService.findById(outletId);
    const hintsObj = await this._hintService.findAllHintsByChannelAndSubChannel(outlet.businessSegment, outlet.subChannel);
    const hints = hintsObj?.hints?.map((h) => h.name)?.join('\n');

    return new ApiResponse({
      images: listImages,
      forwardStock,
      updatedAt: result.updatedAt,
      lastUpdatedOn: result.updatedAt,
      readonly,
      hints,
      hasHint: !!hints,
    });
  }

  @Get('outlets/:outletId/execution/availability')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @Serialize(FetchedAvailabilityDto)
  @ApiOperation({
    summary: 'Get availability',
  })
  async getAvailability(@Param('outletId') outletId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    const existedTodayPlan = await this._outletJourneyPlanningService.getTodayPlanBySaleRepIdAndOutletId(currentUser._id, outletId);
    let allBrandsFromDB;
    if ([OpCos.Cambodia, OpCos.Indonesia].includes(process.env.OPCO)) {
      allBrandsFromDB = await this._brandService.findAll({ isActive: true }, null, { sort: { updatedAt: 1 } });
    } else {
      allBrandsFromDB = await this._brandService.findAll({});
    }
    const brands = allBrandsFromDB.map((b) => ({ name: b.name, hasQt: b.hasQt, hasPt: b.hasPt, hasCan: b.hasCan, hasKeg: b.hasKeg, hasBcan: b.hasBcan }));
    const readonly = !existedTodayPlan || [VisitStatus.START_VISIT].includes(existedTodayPlan?.visitStatus as VisitStatus);
    const [result] = await this._saleRepExecutionAvailabilityService.getAvailabilityBySaleRepIdAndOutletId(currentUser._id, outletId);
    if (!result) {
      return new ApiResponse({
        brands,
        readonly,
      });
    }
    const replace: any = {
      updatedAt: result?.updatedAt,
      lastUpdatedOn: result?.updatedAt,
    };
    if (existedTodayPlan) {
      replace['updatedAt'] = existedTodayPlan.updatedAt;
      replace['lastUpdatedOn'] = existedTodayPlan.updatedAt;
    }
    return new ApiResponse({ ...result.toObject(), readonly, ...replace });
  }

  @Put('outlets/:outletId/execution/availability')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Create or update availability of today plan',
  })
  @Serialize(FetchedAvailabilityDto)
  async createOrUpdateAvailability(
    @Param('outletId') outletId: string,
    @Body() createUpdateAvailabilityDto: CreateUpdateAvailabilityDto,
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser: User,
  ) {
    const existedTodayPlan = await this._outletJourneyPlanningService.getTodayPlanBySaleRepIdAndOutletId(currentUser._id, outletId);
    const readonly = !existedTodayPlan || [VisitStatus.START_VISIT].includes(existedTodayPlan?.visitStatus as VisitStatus);
    if (readonly) {
      throw new BadRequestException(await i18n.t('plan.cannot_edit_data'));
    }
    let allBrandsFromDB;
    if ([OpCos.Cambodia, OpCos.Indonesia].includes(process.env.OPCO)) {
      allBrandsFromDB = await this._brandService.findAll({ isActive: true }, null, { sort: { updatedAt: 1 } });
    } else {
      allBrandsFromDB = await this._brandService.findAll({});
    }
    let { availabilityBrands } = createUpdateAvailabilityDto;

    for (const b of availabilityBrands) {
      if (!allBrandsFromDB.some((ab) => ab.name === b.name)) {
        throw new BadRequestException(await i18n.t('brand.not_found', { args: { brandName: b.name } }));
      }
    }

    const brandNameSet = new Set(availabilityBrands.map((b) => b.name));
    if (availabilityBrands.length !== brandNameSet.size) {
      throw new BadRequestException(await i18n.t('brand.duplicate_item'));
    }
    let result = null;

    // sort and filter list Availability like allBrandsFromDB
    const brands = allBrandsFromDB.map((b) => ({ name: b.name, hasQt: b.hasQt, hasPt: b.hasPt, hasCan: b.hasCan, hasKeg: b.hasKeg, hasBcan: b.hasBcan }));
    if (availabilityBrands?.length) {
      for (const b of availabilityBrands) {
        const index = brands.findIndex((e) => e.name === b.name);
        if (index !== -1) brands[index] = { name: b.name, hasQt: b.hasQt, hasPt: b.hasPt, hasCan: b.hasCan, hasKeg: b.hasKeg, hasBcan: b.hasBcan };
      }
    }
    availabilityBrands = brands;

    const [closestAvailability] = await this._saleRepExecutionAvailabilityService.getAvailabilityBySaleRepIdAndOutletId(currentUser._id, outletId);
    if (closestAvailability && closestAvailability?.journeyPlan) {
      if (existedTodayPlan._id.toString() === closestAvailability?.journeyPlan?._id?.toString()) {
        result = await this._saleRepExecutionAvailabilityService.update(closestAvailability._id, {
          brands: availabilityBrands,
          updatedAt: Date.now(),
        });
      } else {
        result = await this._saleRepExecutionAvailabilityService.create({
          saleRep: new Types.ObjectId(currentUser._id),
          outlet: new Types.ObjectId(outletId),
          journeyPlan: existedTodayPlan._id,
          brands: availabilityBrands,
        });
      }
    } else {
      result = await this._saleRepExecutionAvailabilityService.create({
        saleRep: new Types.ObjectId(currentUser._id),
        outlet: new Types.ObjectId(outletId),
        journeyPlan: existedTodayPlan._id,
        brands: availabilityBrands,
      });
    }
    const journeyPlan = await this._outletJourneyPlanningService.update(existedTodayPlan._id, {
      updatedAt: Date.now(),
    });
    return new ApiResponse({
      brands: availabilityBrands,
      updatedAt: journeyPlan.updatedAt,
      lastUpdatedOn: journeyPlan.updatedAt,
      readonly,
    });
  }

  @Get('outlets/:outletId/execution/affordability')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @Serialize(FetchedAffordabilityDto)
  @ApiOperation({
    summary: 'Get affordability',
  })
  async getAffordability(@Param('outletId') outletId: string, @I18n() i18n: I18nContext, @CurrentUser() currentUser: User) {
    const existedTodayPlan = await this._outletJourneyPlanningService.getTodayPlanBySaleRepIdAndOutletId(currentUser._id, outletId);
    const readonly = !existedTodayPlan || [VisitStatus.START_VISIT].includes(existedTodayPlan?.visitStatus as VisitStatus);

    const [availabilities] = await this._saleRepExecutionAvailabilityService.getAvailabilityBySaleRepIdAndOutletId(currentUser._id, outletId);
    if (!availabilities) {
      return new ApiResponse({
        brands: [],
        readonly,
      });
    }

    const [result] = await this._saleRepExecutionAffordabilityService.getAffordabilityBySaleRepIdAndOutletId(currentUser._id, outletId);
    const brandData = await this._saleRepExecutionAffordabilityService.affordabilityList(availabilities, result);
    const replace: any = {
      updatedAt: result?.updatedAt,
      lastUpdatedOn: result?.updatedAt,
    };
    if (existedTodayPlan) {
      replace['updatedAt'] = existedTodayPlan.updatedAt;
      replace['lastUpdatedOn'] = existedTodayPlan.updatedAt;
    }
    return new ApiResponse({ ...result?.toObject(), brands: brandData, readonly, ...replace });
  }

  @Put('outlets/:outletId/execution/affordability')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Create or update affordability of today plan',
  })
  @Serialize(FetchedAffordabilityDto)
  async createOrUpdateAffordability(
    @Param('outletId') outletId: string,
    @Body() createUpdateAffordabilityDto: CreateUpdateAffordabilityDto,
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser: User,
  ) {
    const existedTodayPlan = await this._outletJourneyPlanningService.getTodayPlanBySaleRepIdAndOutletId(currentUser._id, outletId);
    const readonly = !existedTodayPlan || [VisitStatus.START_VISIT].includes(existedTodayPlan?.visitStatus as VisitStatus);
    if (readonly) {
      throw new BadRequestException(await i18n.t('plan.cannot_edit_data'));
    }
    let allBrandsFromDB;
    if ([OpCos.Cambodia, OpCos.Indonesia].includes(process.env.OPCO)) {
      allBrandsFromDB = await this._brandService.findAll({ isActive: true }, null, { sort: { updatedAt: 1 } });
    } else {
      allBrandsFromDB = await this._brandService.findAll({});
    }

    if (!allBrandsFromDB.some((_afford) => _afford.name === createUpdateAffordabilityDto.brandName)) {
      throw new BadRequestException(await i18n.t('brand.not_found', { args: { brandName: createUpdateAffordabilityDto.brandName } }));
    }

    if (createUpdateAffordabilityDto.value <= 0 || createUpdateAffordabilityDto.value > ConstantCommons.AFFORDABILITY_MAX_VALUE) {
      throw new BadRequestException(await i18n.t('brand.must_be_great_than_zero'));
    }

    let result = null;
    const [closestAffordability] = await this._saleRepExecutionAffordabilityService.getAffordabilityBySaleRepIdAndOutletId(currentUser._id, outletId);
    if (closestAffordability) {
      const brandData = closestAffordability.brands || [];
      if (!isEmptyObjectOrArray(brandData)) {
        const existedIndex = brandData.findIndex((v) => v.brandName === createUpdateAffordabilityDto.brandName && v.brandType === createUpdateAffordabilityDto.brandType);
        if (existedIndex > -1) {
          brandData[existedIndex] = createUpdateAffordabilityDto;
        } else {
          brandData.push(createUpdateAffordabilityDto);
        }
      }

      if (existedTodayPlan?._id?.toString() === closestAffordability?.journeyPlan?._id?.toString()) {
        result = await this._saleRepExecutionAffordabilityService.update(closestAffordability._id, {
          brands: brandData,
          updatedAt: Date.now(),
        });
      } else {
        result = await this._saleRepExecutionAffordabilityService.create({
          saleRep: new Types.ObjectId(currentUser._id),
          outlet: new Types.ObjectId(outletId),
          journeyPlan: existedTodayPlan._id,
          brands: brandData,
        });
      }
    } else {
      result = await this._saleRepExecutionAffordabilityService.create({
        saleRep: new Types.ObjectId(currentUser._id),
        outlet: new Types.ObjectId(outletId),
        journeyPlan: existedTodayPlan._id,
        brands: [createUpdateAffordabilityDto],
      });
    }
    const journeyPlan = await this._outletJourneyPlanningService.update(existedTodayPlan._id, {
      updatedAt: Date.now(),
    });

    const [availabilities] = await this._saleRepExecutionAvailabilityService.getAvailabilityBySaleRepIdAndOutletId(currentUser._id, outletId);
    const brandData = await this._saleRepExecutionAffordabilityService.affordabilityList(availabilities, result);

    return new ApiResponse({
      brands: brandData,
      lastUpdatedOn: journeyPlan.updatedAt,
      updatedAt: journeyPlan.updatedAt,
      readonly,
    });
  }

  @Put('outlets/:outletId/execution/affordabilities')
  async createOrUpdateAffordabilities(
    @Param('outletId') outletId: string,
    @Body() dto: CreateUpdateMultipleAffordabilitiesDto,
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser: User,
  ) {
    const result = await this._saleRepExecutionAffordabilityService.createOrUpdateAffordabilities(currentUser._id.toString(), outletId, dto, i18n);
    return new ApiResponse(result);
  }

  @Post('outlets/:outletId/execute-visit')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBadRequestResponse({ type: ApiException })
  @Serialize(JourneyPlanDto)
  @ApiOperation({
    summary: 'Execute visit',
  })
  async executeVisit(@Param('outletId') outletId: string, @Body() executeVisitDto: ExecuteVisitDto, @CurrentUser() currentUser: User, @I18n() i18n: I18nContext) {
    const existedTodayPlan = await this._outletJourneyPlanningService.getTodayPlanBySaleRepIdAndOutletId(currentUser._id, outletId);
    if (isEmptyObjectOrArray(existedTodayPlan)) {
      throw new HttpException(await i18n.t('plan.not_found_today'), HttpStatus.BAD_REQUEST);
    }
    const anotherInProgressVisit = await this._outletJourneyPlanningService.getInProgressTodayVisitBySaleRepIdAndExcludeOutletId(currentUser._id, outletId);
    if (anotherInProgressVisit) {
      await this._outletJourneyPlanningService.update(anotherInProgressVisit._id, {
        visitStatus: VisitStatus.START_VISIT,
        updatedAt: new Date(),
        $unset: {
          missedReason: 1,
          outletReport: 1,
        },
      });
      await this._saleRepExecutionAvailabilityService.deleteAvailabilityBySaleRepIdAndJourneyPlanId(currentUser._id, anotherInProgressVisit._id.toString());
    }
    const { longitude, latitude, time } = executeVisitDto;
    let locationRange = null;
    if (!existedTodayPlan.locationRange && latitude && longitude && existedTodayPlan.outlet.address) {
      const address1 = `${latitude}, ${longitude}`;
      const outletAddress = existedTodayPlan.outlet.address;
      //get Destination
      const destinations = await this.googleMapsService.getDirection(address1, outletAddress);
      if (destinations?.length) {
        locationRange = destinations[0].distance.value;
      }
    }

    let visitStatus = VisitStatus.IN_PROGRESS;
    let startVisitDate = new Date();
    if (existedTodayPlan.visitStatus === VisitStatus.COMPLETED) {
      if (!existedTodayPlan.startVisitDate || moment(existedTodayPlan.startVisitDate).isAfter(moment(existedTodayPlan.visitedDay))) {
        const correctLog = await this._userActionsService.getPlanFirstAction(
          [`/api/sale-rep/outlets/${existedTodayPlan.outlet._id.toString()}/execute-visit`, `/api/journey-plans/${existedTodayPlan._id.toString()}/visited-steps`],
          ['post', 'put'],
          moment().format('YYYY-MM-DD'),
        );
        startVisitDate = correctLog?.createdAt;
        await this._outletJourneyPlanningService.update(existedTodayPlan._id, { startVisitDate });
      }
      visitStatus = VisitStatus.COMPLETED;
    } else if (time) {
      startVisitDate = new Date(time) > new Date() ? new Date(time) : new Date();
    }
    const result = await this._outletJourneyPlanningService.update(existedTodayPlan._id, {
      location: {
        latitude: latitude,
        longitude: longitude,
      },
      locationRange,
      visitStatus,
      startVisitDate: startVisitDate,
      updatedAt: new Date(),
      $unset: {
        missedReason: 1,
        outletReport: 1,
      },
    });

    return new ApiResponse(result);
  }

  /**
   * Need to improve the performance
   * @param getSalesRepDto
   * @param currentUser
   */
  @Post('list')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.DISTRIBUTOR_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get Sales Rep',
  })
  async getSalesRep(@Body() getSalesRepDto: GetSalesRepDto, @CurrentUser() currentUser: User) {
    const { skip, limit, sort, search, distributorId } = getSalesRepDto;
    const userRole = await this._userDetailService.findUserRoles({ userId: currentUser._id.toString(), isUserAdmin: true });
    let result = [{ totalRecords: [{ total: 0 }], data: null }];
    let lastUpdatedSalesRep = null;
    if (userRole.some((r) => r.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (!distributorAdmins || !distributorAdmins.length || !distributor) {
        return new ApiResponse({
          ...toListResponse([[], 0]),
          lastUpdatedOn: null,
        });
      }

      [result, lastUpdatedSalesRep] = await Promise.all([
        this._distributorUserRelationService.getSalesRep([], [distributor.distributor.distributorId], skip, limit, sort, search),
        this._distributorUserRelationService.getLastUpdatedSalesRep(
          [],
          distributorAdmins.map((e) => e.distributor.distributorId),
        ),
      ]);
    } else {
      [result, lastUpdatedSalesRep] = await Promise.all([
        this._distributorUserRelationService.getSalesRep([], [distributorId], skip, limit, sort, search),
        this._distributorUserRelationService.getLastUpdatedSalesRep([], [distributorId]),
      ]);
    }

    const [{ totalRecords, data }] = result;
    return new ApiResponse({
      ...toListResponse([data, totalRecords?.[0]?.total ?? 0]),
      lastUpdatedOn: lastUpdatedSalesRep?.updatedAt,
    });
  }

  @Post('list/export')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.DISTRIBUTOR_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get Sales Rep',
  })
  async exportSalesRep(@Body() getSalesRepDto: GetSalesRepDto, @CurrentUser() currentUser: User, @I18n() i18n: I18nContext) {
    const { skip, limit, sort, search, distributorId } = getSalesRepDto;
    const userRole = await this._userDetailService.findUserRoles({ userId: currentUser._id.toString(), isUserAdmin: true });
    let result = [{ totalRecords: [{ total: 0 }], data: [] }];

    let distributorName = '';
    if (userRole.some((r) => r.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (distributor) {
        result = await this._distributorUserRelationService.getSalesRep([], [distributor.distributor.distributorId], skip, limit, sort, search);
        distributorName = distributor.distributor.distributorName;
      }
    } else {
      result = await this._distributorUserRelationService.getSalesRep([], [distributorId], skip, limit, sort, search);
      const distributor = await this._distributorService.findOne({ distributorId });
      distributorName = distributor.distributorName;
    }
    const [{ data }] = result;
    const xlsxData = data?.map((item) => {
      const temp = {
        [i18n.translate(`importExport.name`)]: item.name,
        [i18n.translate(`importExport.mobilePhoneCode`)]: item.mobilePhoneCode,
        [i18n.translate(`importExport.phoneNumber`)]: item.mobilePhone?.replace(item.mobilePhoneCode, '0'),
        [i18n.translate(`importExport.dotUsername`)]: item.dotUsername,
        [i18n.translate(`importExport.distributorId`)]: item.distributorId,
        [i18n.translate(`importExport.distributorName`)]: item.distributorName,
        [i18n.translate(`importExport.salesRepId`)]: item.saleRepId,
        [i18n.translate(`importExport.status`)]: i18n.translate(`importExport.${item.status}`),
      };
      if (process.env.OPCO === OpCos.Cambodia) {
        temp[i18n.translate(`importExport.geoAddress`)] = item.geoAddress;
      }
      return temp;
    });

    const fileName = `SalesRep_Data_${distributorName}`;
    const file = await this._filesServices.exportXLSXFile(fileName, xlsxData, 'SalesRep_Data', currentUser, { wch: 20 });
    return new ApiResponse(file);
  }

  @Post('option-list')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get Sales Rep',
  })
  async getSalesRepOptionList(@Body() body, @CurrentUser() currentUser: User) {
    const { skip, limit, distributorId } = body;
    const sort: any = { saleRepId: 1 };
    const allSalesRep = await this._userService.findAll({ isActive: true });
    const salesRepIds = allSalesRep.map((sr) => sr._id);
    const userRole = await this._userDetailService.findUserRoles({ userId: currentUser._id.toString(), isUserAdmin: true });
    let result = [{ totalRecords: [{ total: 0 }], data: [] }];

    if (userRole.some((r) => r.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id);
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId.toString() === distributorId.toString());
      if (distributorAdmins && distributorAdmins.length && distributor) {
        result = await this._distributorUserRelationService.getSalesRep(salesRepIds, [distributor.distributor.distributorId], skip, limit, sort);
      }
    } else {
      let distributorIds = null;
      if (distributorId) {
        const distributor = await this.distributorModel.findOne({ distributorId });
        if (distributor) {
          distributorIds = [distributor.distributorId];
        }
      }
      result = await this._distributorUserRelationService.getSalesRep(salesRepIds, distributorIds, 0, 1000, sort);
    }

    const [{ totalRecords, data }] = result;
    const dataSaleRep = data.map((s) => {
      return {
        saleRepId: s.saleRepId,
        saleRepName: s.name,
        saleRepFullName: s.name,
        updatedAt: s.updatedAt,
        _id: s._id,
      };
    });
    const lastUpdatedAt = new Date(
      Math.max(
        ...dataSaleRep.map((s) => {
          return new Date(s.updatedAt).getTime();
        }),
      ),
    );

    return new ApiResponse({ ...toListResponse([dataSaleRep, totalRecords?.[0]?.total ?? 0]), lastUpdatedAt });
  }

  @Get(':saleRepUUID')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.SUPER_USER, ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiOperation({
    summary: 'Get Sales Rep Detail by Sales Rep UUID',
  })
  async getSalesRepDetail(@Param('saleRepUUID') id: string, @CurrentUser() currentUser: User) {
    const userRole = await this._userDetailService.findUserRoles({ userId: currentUser._id.toString(), isUserAdmin: true });
    let result;
    if (userRole.some((r) => r.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      if (distributorAdmins && distributorAdmins.length) {
        result = await this._distributorUserRelationService.getSalesRep(
          [id],
          distributorAdmins.map((e) => e.distributor.distributorId),
        );
      }
    } else {
      result = await this._distributorUserRelationService.getSalesRep([id], null);
    }
    const [{ data }] = result;
    return new ApiResponse(data?.[0] ?? null);
  }

  @Put(':saleRepUUID')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiOperation({
    summary: 'Update a sales rep',
  })
  async updateGivenSalesRep(@Param('saleRepUUID') id: string, @Body() updateSalesRepDto: CreateUpdateSalesRepDto, @CurrentUser() currentUser: User, @I18n() i18n: I18nContext) {
    const userRole = await this._userDetailService.findUserRoles({ userId: currentUser._id.toString(), isUserAdmin: true });
    const { saleRepId, name, dotUsername, status, isTestAccount, distributorId, geoAddress } = updateSalesRepDto;
    if (process.env.OPCO == OpCos.Cambodia && geoAddress && !isValidGeoAddress(geoAddress)) {
      throw new BadRequestException('saleRep.invalid_geo_address');
    }

    let { mobilePhone, mobilePhoneCode } = updateSalesRepDto;
    if (mobilePhone && !isPhoneNumberValidation(mobilePhone, mobilePhoneCode)) {
      throw new BadRequestException('user.phone_invalid_field');
    }
    let mobilePhoneParse;
    if (mobilePhone?.trim()) {
      mobilePhoneParse = parsePhoneNumberWithError(standardPhoneNumber(mobilePhone, mobilePhoneCode));
      mobilePhone = mobilePhoneParse.number;
      mobilePhoneCode = `+${mobilePhoneParse.countryCallingCode}`;
    }

    if (userRole.some((r) => r.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id.toString());
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (distributorAdmins && distributorAdmins.length && distributor) {
        {
          if (status == SalesRepStatus.INACTIVE) {
            const inProgressPlan = await this._outletJourneyPlanningService.getInProgressTodayVisitBySaleRepIdAndExcludeOutletId(id, null);
            if (inProgressPlan) {
              throw new BadRequestException('saleRep.cannot_update');
            }
          }

          if (mobilePhone?.trim()) {
            const existedUserPhoneNumber = await this._userService.findOne({
              mobilePhone,
              _id: { $ne: new Types.ObjectId(id) },
            });
            if (existedUserPhoneNumber) {
              throw new BadRequestException('saleRep.phone_existed');
            }
          }

          if (saleRepId?.trim()) {
            const existedSaleRepId = await this._userService.findOne({
              saleRepId: saleRepId?.trim(),
              _id: { $ne: new Types.ObjectId(id) },
            });
            if (existedSaleRepId) {
              throw new BadRequestException('saleRep.sales_rep_id_existed');
            }
          }
          const updateData: any = {};

          if (name?.trim()) {
            updateData.username = name?.trim();
          }
          if (saleRepId?.trim()) {
            updateData.saleRepId = saleRepId?.trim();
          }
          if (mobilePhoneCode?.trim()) {
            updateData.mobilePhoneCode = mobilePhoneCode;
          }
          if (mobilePhone?.trim()) {
            updateData.mobilePhone = standardPhoneNumber(mobilePhone?.trim());
          }
          if (status) {
            if (!Object.values(SalesRepStatus).some((v) => v === status)) {
              throw new BadRequestException('distributor.invalid_status');
            }

            updateData.status = SalesRepStatusValue[status];
            updateData.saleRepStatus = status;
          }
          if (isTestAccount === true || isTestAccount === false) {
            updateData.isTestAccount = isTestAccount;
          }
          if (geoAddress) {
            updateData.geoAddress = geoAddress;
          }
          // PDH-2865 Change status to re-calculate route by GG Maps API
          const salesRep = await this._userService.findById(id);
          if (salesRep.geoAddress !== geoAddress) {
            updateData.isAddressChanged = true;
          }

          const user = await this._userService.update(id, updateData);
          if (!user) {
            throw new BadRequestException('saleRep.invalid_request');
          }
          const isSentPasswordLink = await this._userService.isSentPasswordLink(user._id.toString(), 'salesRep');
          if (!isSentPasswordLink && user.saleRepStatus === SalesRepStatus.ACTIVE) {
            await this._userService.sendCreatePasswordUser(
              {
                mobilePhone: user.mobilePhone,
                user: user,
              },
              i18n,
            );
          }
          await this._saleRepFfcStoreService.updateBySaleRepUUID(id, { customerUserName: dotUsername });
          await this._distributorUserRelationService.updateByCondition(
            { user: new Types.ObjectId(id) },
            {
              distributor: distributor.distributor._id,
            },
          );
          if (status == SalesRepStatus.INACTIVE) {
            // Delete base journey plans of Inactive Sales Rep
            await this._baseJourneyPlanService.deleteByCondition({ saleRep: id });

            //Delete un-executed JP of Inactive Sales Rep
            await this._outletJourneyPlanningService.deleteUnExecutedPlannedVisits(id);

            //Delete token cache
            const tokensData = await this._userDetailService.findOne({ userId: id, isUserAdmin: false });
            if (!isEmptyObjectOrArray(tokensData)) {
              const { userToken } = tokensData;
              for (const token of userToken) {
                clearCurrentUserTokenCache(token?.accessToken, this.cacheManager).then();
              }
              await this._userDetailService.addOrUpdate({ userId: id, isUserAdmin: false, userToken: [] });
            }
          }
          return new ApiResponse({
            distributorId: distributor.distributor.distributorId,
            distributorName: distributor.distributor.distributorName,
            dotUsername,
            isTestAccount: user.isTestAccount,
            mobilePhone: user.mobilePhone,
            mobilePhoneCode: user.mobilePhoneCode,
            name: user.username,
            saleRepId: user.saleRepId,
            status: user.saleRepStatus,
            _id: user._id,
            geoAddress: user.geoAddress,
            updatedAt: new Date(),
          });
        }
      } else {
        throw new BadRequestException('saleRep.invalid_request');
      }
    } else {
      throw new BadRequestException('saleRep.invalid_request');
    }
  }

  @Post('')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiOperation({
    summary: 'Create a sales rep',
  })
  async createSalesRep(@Body() createSalesRepDto: CreateUpdateSalesRepDto, @CurrentUser() currentUser: User, @I18n() i18n: I18nContext) {
    const userRole = await this._userDetailService.findUserRoles({ userId: currentUser._id.toString(), isUserAdmin: true });
    const { saleRepId, name, dotUsername, status, distributorId, geoAddress } = createSalesRepDto;
    if (process.env.OPCO == OpCos.Cambodia && !isValidGeoAddress(geoAddress)) {
      throw new BadRequestException('saleRep.invalid_geo_address');
    }

    let { mobilePhone, mobilePhoneCode } = createSalesRepDto;
    if (!isPhoneNumberValidation(mobilePhone, mobilePhoneCode)) {
      throw new BadRequestException('user.phone_invalid_field');
    }
    let mobilePhoneParse;
    if (mobilePhone?.trim()) {
      mobilePhoneParse = parsePhoneNumberWithError(standardPhoneNumber(mobilePhone, mobilePhoneCode));
      mobilePhone = mobilePhoneParse.number;
      mobilePhoneCode = `+${mobilePhoneParse.countryCallingCode}`;
    }

    if (!Object.values(SalesRepStatus).some((v) => v === status)) {
      throw new BadRequestException('distributor.invalid_status');
    }
    if (userRole.some((r) => r.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorAdmins = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id);
      const distributor = distributorAdmins.find((e) => e.distributor.distributorId === distributorId);
      if (distributor) {
        const existedUserPhoneNumber = await this._userService.findOne({
          mobilePhone,
        });
        if (existedUserPhoneNumber) {
          throw new BadRequestException('saleRep.phone_existed');
        }
        const existedSaleRepId = await this._userService.findOne({
          saleRepId: saleRepId?.trim(),
        });
        if (existedSaleRepId) {
          throw new BadRequestException('saleRep.sales_rep_id_existed');
        }

        let createdUser: User;
        let createdUserRole;
        let createdDisUserRelation;
        let createdSaleRepFFCStore;
        try {
          createdUser = await this._userService.create({
            mobilePhone,
            mobilePhoneCode,
            saleRepId: saleRepId?.trim(),
            username: name?.trim(),
            roleId: [ConstantRoles.SALE_REP],
            status: ConstantUser.IS_ACTIVE,
            saleRepStatus: status,
            geoAddress,
            // PDH-2865 Change status to re-calculate route by GG Maps API
            isAddressChanged: true,
          });

          createdUserRole = await this._userDetailService.addOrUpdate({
            userId: createdUser._id,
            mobilePhone: createdUser.mobilePhone,
            isUserAdmin: false,
            userRole: [{ roleKey: ConstantRoles.SALE_REP }],
          });

          createdSaleRepFFCStore = await this._saleRepFfcStoreService.create({
            saleRep: createdUser._id,
            customerUserName: dotUsername,
          });

          createdDisUserRelation = await this._distributorUserRelationService.create({
            user: createdUser._id,
            distributor: distributor.distributor._id,
          });
          if (createdUser.saleRepStatus === SalesRepStatus.ACTIVE) {
            await this._userService.sendCreatePasswordUser(
              {
                mobilePhone,
                user: createdUser,
              },
              i18n,
            );
          }
        } catch (e) {
          if (createdUser) {
            await this._userService.delete(createdUser._id);
          }
          if (createdUserRole) {
            await this._userDetailService.addOrUpdate({ userId: createdUser._id, isUserAdmin: false, userRole: [] });
          }

          if (createdSaleRepFFCStore) {
            await this._saleRepFfcStoreService.delete(createdSaleRepFFCStore._id);
          }

          if (createdDisUserRelation) {
            await this._distributorUserRelationService.delete(createdDisUserRelation._id);
          }
          throw new InternalServerErrorException(e);
        }

        this.eventEmitter.emit(
          ConstantEventName.SHARED_EVENT,
          new SharedEvent({
            callback: async () => {
              await this._dsrTargetService.createTargetForSaleRep([createdUser._id.toString()]);
            },
          }),
        );

        return new ApiResponse({
          _id: createdUser._id.toString(),
          name,
          saleRepId,
          mobilePhone,
          mobilePhoneCode,
          dotUsername,
          status,
          createdAt: createdUser.createdAt,
        });
      } else {
        throw new BadRequestException('saleRep.invalid_request');
      }
    } else {
      throw new BadRequestException('saleRep.invalid_request');
    }
  }

  @Get('home/route-plan')
  @Roles(ConstantRoles.SALE_REP)
  @ApiOperation({
    summary: 'Get route plan data of home screen',
  })
  async getRoutePlanData(@CurrentUser() user: User) {
    const data = await this.routePlanService.getRoutePlanData({ salesRep: user });
    return new ApiResponse(data);
  }
}
