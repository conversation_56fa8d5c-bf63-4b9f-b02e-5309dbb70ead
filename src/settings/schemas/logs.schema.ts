import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';
import { Document } from 'mongoose';
import { BaseSchema } from '../../shared/schemas/base.schema';

export type LogsDocument = Logs & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
})
export class Logs extends BaseSchema {
  @Prop({ unique: false, index: true })
  key: string;

  @Prop({ unique: false, index: true })
  feature: string;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  request: any;

  @Prop({ type: mongoose.Schema.Types.Mixed, index: true })
  response: any;

  @Prop()
  code: string;

  @Prop({ default: null })
  userId: string;

  @Prop({ default: false })
  isUserAdmin: boolean;

  @Prop({ default: true })
  isActive: boolean;
}

export const LogsSchema = SchemaFactory.createForClass(Logs);
LogsSchema.index({ 'response.error.message': 'text' });
