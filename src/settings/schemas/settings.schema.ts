import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from '../../shared/schemas/base.schema';
import { Document } from 'mongoose';

export type SettingDocument = Settings & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
})
export class Settings extends BaseSchema {
  @Prop({ unique: true })
  key: string;

  @Prop()
  values: string;

  @Prop({ default: true })
  isActive: boolean;
}

export const SettingsSchema = SchemaFactory.createForClass(Settings);
