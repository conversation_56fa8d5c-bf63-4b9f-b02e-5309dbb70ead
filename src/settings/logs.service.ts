import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { FilesService } from 'src/files/services';
import { API_KEY_FEATURE_NAME_MAPPING } from 'src/shared/constants';
import { User } from 'src/users/schemas/user.schema';
import { normalizeQueryHelper } from '../shared/helpers';
import { BaseService } from '../shared/services/base-service';
import { ExportLogsDto } from './dtos/export-logs.dto';
import { SearchLogsDto } from './dtos/searchlights.dto';
import { LogColumns } from './enums/log-columns.enum';
import { Logs, LogsDocument } from './schemas/logs.schema';
import * as moment from 'moment-timezone';
import { printLog, sleep } from '../utils';
import {
  DOT_API_ADD_TO_CART,
  DOT_API_CART_INFO,
  DOT_API_CATALOG_SUGGESTIONS,
  DOT_API_CREATE_ORDER,
  DOT_API_FULFILLMENT_CENTERS,
  DOT_API_GET_INVOICE_ORDER,
  DOT_API_ORDER,
  DOT_API_PRODUCT_SEARCH,
  DOT_API_PROMOTION,
  DOT_API_SEARCH_CUSTOMER_ORDER,
  DOT_API_SEARCH_MEMBER,
  DOT_API_STORE_SETTING,
  DOT_API_USER_INFO,
  DOT_API_USER_LOGIN,
} from '../external/constants';
import { UserDetailService } from '../users/services/user-detail.service';

@Injectable()
export class LogsService extends BaseService<Logs> {
  constructor(
    @InjectModel(Logs.name)
    private readonly _modelLog: Model<LogsDocument>,
    @Inject(forwardRef(() => UserDetailService))
    private readonly _userDetailService: UserDetailService,
    private readonly _filesService: FilesService,
  ) {
    super();
    this.model = _modelLog;
  }

  /**
   * Delete old log records in batches to avoid performance issues.
   * @params none
   * @returns {Promise<void>}
   */
  async clearOldData(): Promise<void> {
    const time = moment().subtract(2, 'month').toISOString();
    const batchSize = 200;
    let count = 0;
    while (true) {
      // Find a batch of IDs to delete
      const oldDocs = await this.model
        .find({ createdAt: { $lte: time } })
        .select('_id')
        .limit(batchSize)
        .lean();

      if (!oldDocs?.length || ++count > batchSize * 2000) break;

      const ids = oldDocs.map((doc) => doc._id);
      await this.model.deleteMany({ _id: { $in: ids } });
      printLog(ids);
      await sleep(500);
    }
  }

  async saveActionLog(token: string = null, actionKey: string, data: any): Promise<any> {
    try {
      if (!actionKey) {
        return;
      }

      const userToken = await this._userDetailService.findUserTokenByAccessToken(token?.trim());

      const method = data?.request?.method?.toLowerCase();
      const featureName = this.getFeatureNameByApiKeyAndParams(actionKey, method, data?.request?.params);
      delete data?.request?.method;

      await this.create({
        userId: userToken?.userId,
        isUserAdmin: userToken?.isUserAdmin || false,
        key: actionKey,
        feature: featureName,
        code: data.code,
        request: { ...data.request, token },
        response: data.response,
      });
    } catch (e) {
      return false;
    }
  }

  async saveLogByKey(actionKey: string, data: any): Promise<any> {
    try {
      if (!actionKey) {
        return;
      }

      await this.create({
        userId: data?.userId,
        isUserAdmin: data?.isUserAdmin,
        key: actionKey,
        feature: data?.featureName,
        code: data?.code,
        request: data?.request,
        response: data?.response,
      });
    } catch (e) {
      return false;
    }
  }

  public getFeatureNameByApiKeyAndParams = (apiKey: string, method: string, params: Record<string, any>) => {
    try {
      if (
        !params ||
        [
          DOT_API_USER_LOGIN,
          DOT_API_USER_INFO,
          DOT_API_ORDER,
          DOT_API_CREATE_ORDER,
          DOT_API_PROMOTION,
          DOT_API_FULFILLMENT_CENTERS,
          DOT_API_PRODUCT_SEARCH,
          DOT_API_CART_INFO,
          DOT_API_ADD_TO_CART,
          DOT_API_STORE_SETTING,
          DOT_API_CATALOG_SUGGESTIONS,
          DOT_API_SEARCH_CUSTOMER_ORDER,
          DOT_API_SEARCH_MEMBER,
          DOT_API_GET_INVOICE_ORDER,
        ].includes(apiKey)
      ) {
        return API_KEY_FEATURE_NAME_MAPPING[apiKey][method];
      }

      const keyName = Object.entries(params).reduce((pre, curr) => {
        const [key, value] = curr;
        return pre.replace(value, `:${key}`);
      }, apiKey);

      return API_KEY_FEATURE_NAME_MAPPING[keyName][method];
    } catch (e) {
      return apiKey;
    }
  };

  async searchLogs(filters: SearchLogsDto, orderBy: string = null, orderDesc: string = null): Promise<any> {
    const params: any = {};
    let sort: any = { createdAt: -1 };

    if (filters.search) {
      const normalizedQuery = normalizeQueryHelper(filters.search);
      params['$or'] = [
        {
          key: new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
        },
        {
          feature: new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
        },
        {
          'response.error.message': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
        },
      ];
    }
    if (filters.key) {
      params.key = filters.key;
    }
    if (filters.code) {
      params.code = filters.code;
    }
    if (filters.userId) {
      params.userId = filters.userId;
    }
    if (filters.startTime) {
      params.createdAt = {
        $gte: moment(filters.startTime).tz(process.env.TZ).toDate(),
        $lte: moment(filters.endTime || new Date())
          .tz(process.env.TZ)
          .toDate(),
      };
    }

    orderDesc = orderDesc?.toLowerCase();
    if (orderBy && ['asc', 'desc'].indexOf(orderDesc) > -1) {
      sort = { [orderBy]: orderDesc == 'asc' ? 1 : -1 };
    }

    return (
      this._modelLog
        .aggregate()
        .match(params)
        .sort(sort)
        .collation({ locale: 'en' })
        //.skip(filters.skip || 0)
        //.limit(filters.limit || 100)
        .facet({
          totalRecords: [
            {
              $count: 'total',
            },
          ],
          data: [
            {
              $skip: Number(filters.skip),
            },
            {
              $limit: Number(filters.limit),
            },
          ],
        })
    );
  }

  async exportLogs(user: User, { search, orderBy, orderDesc }: ExportLogsDto) {
    const params = {};
    if (search) {
      const normalizedQuery = normalizeQueryHelper(search);
      params['$or'] = [
        {
          key: new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
        },
        {
          feature: new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
        },
        {
          'response.error.message': new RegExp(`^.*${normalizedQuery}.*$`, 'i'),
        },
      ];
    }

    let sort: any = { createdAt: -1 };
    if (orderBy) {
      sort = {
        [orderBy]: orderDesc === 'asc' ? 1 : -1,
      };
    }

    const records: Logs[] = await this._modelLog.aggregate().match(params).sort(sort).collation({ locale: 'en' });
    const xlsxData = records.map((item) => ({
      [LogColumns.CODE]: item.code,
      [LogColumns.MESSAGE]: item.response.error.message,
      [LogColumns.FEATURE]: item.feature,
      [LogColumns.KEY]: item.key,
      [LogColumns.CREATED_AT]: item.createdAt,
      [LogColumns.USER_ADMIN]: item.isUserAdmin,
      [LogColumns.ACTIVE]: item.isActive,
    }));
    const fileName = 'Logs';
    const file = await this._filesService.exportXLSXFile(fileName, xlsxData, 'Logs', user, { wch: 25 });

    return file;
  }
}
