import { Body, Controller, Get, HttpCode, HttpStatus, Post, Query, Request, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiTags } from '@nestjs/swagger';
import { I18n, I18nContext } from 'nestjs-i18n';
import { JourneyPlanMissedReasonsService } from 'src/journey-plannings/services/journey-plan-missed-reasons.service';
import { CurrentUser } from 'src/shared/decorator/current-user.decorator';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { CancellationReasonType } from '../journey-plannings/enums/cancellation-reason-type.enum';
import { OrderParams } from '../orders/dtos/order-filter.dto';
import { OrdersService } from '../orders/services/orders.service';
import { SaleOrderStatisticService } from '../orders/services/sale-order-statistic.service';
import { OutletLabelStatus } from '../outlets/enums/outlet-status.enum';
import { OutletsService } from '../outlets/services/outlets.service';
import { SaleRepOutletRelationService } from '../outlets/services/sale-rep-outlet-relation.service';
import { SaleRepStatisticService } from '../sale-rep/services';
import { ApiException } from '../shared/api-exception.model';
import { PaginationParams } from '../shared/common-params/pagination.params';
import { Roles } from '../shared/decorator/roles.decorator';
import { ApiResponse } from '../shared/response/api-response';
import { UserAdminsService } from '../users/services/user-admins.service';
import { UsersService } from '../users/services/users.service';
import { isEmptyObjectOrArray, printLog, standardPhoneNumber, toListResponse } from '../utils';
import { APP_SETTINGS } from '../utils/constants';
import { ConstantRoles } from '../utils/constants/role';
import { CacheUpdateDto } from './dtos/cache-update.dto';
import { ExportLogsDto } from './dtos/export-logs.dto';
import { SearchLogsDto } from './dtos/searchlights.dto';
import { SettingSmsDto } from './dtos/setting-sms.dto';
import { SettingUpdateDto } from './dtos/setting-update.dto';
import { LogsService } from './logs.service';
import { SettingsService } from './settings.service';
import { BrandService } from 'src/brand/services';
import { BE_VERSION, OpCos } from 'src/config';
import { MissReasonsService } from 'src/miss-reasons/miss-reasons.service';
import { MissReasonLocation } from 'src/miss-reasons/enums/miss-reason-location.enum';
import { RoleData } from '../users/enums';
import { PriorityFilterSetting } from './constants/filter';
import { OmsService } from 'src/external/services/oms.service';

@ApiTags('Settings')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/setting')
export class SettingsController {
  constructor(
    private readonly settingsService: SettingsService,
    private readonly logsService: LogsService,
    private readonly userService: UsersService,
    private readonly userAdminService: UserAdminsService,
    private readonly orderService: OrdersService,
    private readonly saleOrderService: SaleOrderStatisticService,
    private readonly saleRepStatisticService: SaleRepStatisticService,
    private readonly outletsService: OutletsService,
    private readonly saleRepOutletRelationService: SaleRepOutletRelationService,
    private readonly journeyPlanMissedReasonsService: JourneyPlanMissedReasonsService,
    private readonly brandService: BrandService,
    private readonly missReasonsService: MissReasonsService,
    private readonly omsService: OmsService,
  ) {}

  @Get('configs')
  @ApiBearerAuth()
  // @UseGuards(JwtAuthGuard)
  @ApiBadRequestResponse({ type: ApiException })
  async configs(@I18n() i18n: I18nContext): Promise<any> {
    try {
      const listMissedVisitReason = await Promise.all(
        Object.keys(CancellationReasonType).map(async (i) => ({
          key: CancellationReasonType[i],
          value: await i18n.t(CancellationReasonType[i]),
        })),
      );
      const [brands, { missReasons, displayedLocations }, { map: outletChannelMap }] = await Promise.all([
        this.brandService.findAll({}),
        this.missReasonsService.getConfiguration(),
        this.settingsService.getOutletChannels(),
      ]);
      const missReasonsMap = this.missReasonsService.convertMissReasonsToMap(missReasons);
      const missReasonsResponse = Object.keys(displayedLocations).reduce(
        (pre, location) => {
          const missReasonIds = displayedLocations[location].map(String);
          const missReasonsByLocation = missReasonIds.map((id) => {
            const missReason = missReasonsMap[id];
            const translation =
              missReason.translations.find((item) => item.language.toUpperCase() === i18n.lang.toUpperCase()) ||
              missReason.translations.find((item) => item.isDefault) ||
              missReason.translations[0];

            return {
              id,
              reason: translation.reason,
              warning: translation.warning,
              locations: missReason.locations,
              requireEvidence: missReason.requireEvidenceSettings?.[location],
            };
          });

          return {
            ...pre,
            [location]: missReasonsByLocation,
          };
        },
        {} as {
          [key in MissReasonLocation]?: Array<{ id: string; reason: string; warning: string; locations: MissReasonLocation[] }>;
        },
      );

      const locationKeyMap = {
        [MissReasonLocation.REPORT_ABSENCES]: 'reportAbsences',
        [MissReasonLocation.REPORT_OUTLET]: 'reportOutlets',
      };
      const excelConfig: any = await this.settingsService.getConfigExcel();
      const brandsFilter = await this.omsService.getConfigBrands();
      const result: any = {
        timezone: process.env.TZ,
        pageSizeDot: 10,
        outletChannelMap,
        plan: {
          missReasons: Object.keys(missReasonsResponse).reduce((pre, location) => ({ ...pre, [locationKeyMap[location]]: missReasonsResponse[location] }), {}),
          missedVisitReasons: listMissedVisitReason,
          outletMissedReasons: Object.keys(missReasonsResponse).reduce((pre, location) => {
            const missReasons = (missReasonsResponse[location] || []).map((item) => ({
              key: item.id,
              value: item.reason,
              message: item.warning,
              isForMultipleOutlets: location === MissReasonLocation.REPORT_ABSENCES,
            }));

            return [...pre, ...missReasons];
          }, []),
        },
        roles: RoleData,
        classifications: {
          G: 'Gold',
          S: 'Silver',
          B: 'Bronze',
        },
        classificationLevels: [1, 2, 3],
        outletStatus: OutletLabelStatus,
        templateFilePaths: {
          customerMasterData: `files/customer_master_data_${i18n.lang}.xlsx`,
          salesRepMasterData: process.env.OPCO == OpCos.Cambodia ? `files/sales_rep_master_data_kh_${i18n.lang}.xlsx` : `files/sales_rep_master_data_${i18n.lang}.xlsx`,
          baseJourneyPlanData: `files/base_journey_plan_data_${i18n.lang}.xlsx`,
          distributorData: `files/distributor_data_${i18n.lang}.xlsx`,
        },
        saleRepLastUpdatedAt: (await this.userService.findOne({ isActive: true }, 'updatedAt', { sort: { updatedAt: -1 } }))?.updatedAt,
        logFileUrl: `${process.env.BASE_URL}/logs/`,
        phoneCountryCodes: this.settingsService.getPhoneCountryCode(),
        version: process.env.npm_package_version,
        env: process.env.NODE_ENV,
        appVersion: {},
        brands,
        brandsFilter,
        packageTypeFilter: excelConfig?.packageTypeFilter || [],
        listBusinessChannel: excelConfig?.listBusinessChannel || [],
        listBusinessSubChannel: excelConfig?.listBusinessSubChannel || [],
        listBusinessSegment: excelConfig?.listBusinessSegment || [],
        maps: '',
        priorityFilter: await PriorityFilterSetting(i18n),
      };

      const appVersion: any = await this.settingsService.findByKey(APP_SETTINGS.APP_VERSION);
      const appVersionSetting: any = !isEmptyObjectOrArray(appVersion) ? JSON.parse(appVersion?.values) : null;
      if (!isEmptyObjectOrArray(appVersionSetting)) {
        result.appVersion = {
          ios: {
            versionName: appVersionSetting.ios.versionName,
            versionCode: Number(appVersionSetting.ios.versionCode),
            isForceUpdate: appVersionSetting.ios.isForceUpdate.toString() === 'true',
            desc: await i18n.translate('common.APP_VERSION.IOS.MESSAGE'),
            title: await i18n.translate('common.APP_VERSION.IOS.TITLE'),
            link: appVersionSetting.ios.link,
          },
          android: {
            versionName: appVersionSetting.android.versionName,
            versionCode: Number(appVersionSetting.android.versionCode),
            isForceUpdate: appVersionSetting.android.isForceUpdate.toString() === 'true',
            desc: await i18n.translate('common.APP_VERSION.ANDROID.MESSAGE'),
            title: await i18n.translate('common.APP_VERSION.ANDROID.TITLE'),
            link: appVersionSetting.android.link,
          },
        };
      }

      return new ApiResponse(result);
    } catch (e) {
      printLog(e);
    }
  }

  @Get('search/logs')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @UsePipes(
    new ValidationPipe({
      transform: true,
    }),
  )
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBadRequestResponse({ type: ApiException })
  async searchLogs(@Query() filters: SearchLogsDto, @Query() { orderBy, orderDesc }: OrderParams): Promise<any> {
    let result = [{ totalRecords: [{ total: 0 }], data: null }];

    result = await this.logsService.searchLogs(filters, orderBy, orderDesc);
    const [{ totalRecords, data }] = result;
    return new ApiResponse({
      ...toListResponse([data, totalRecords?.[0]?.total ?? 0]),
    });
  }

  @Post('logs/export')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(
    new ValidationPipe({
      transform: true,
    }),
  )
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBearerAuth()
  @ApiBadRequestResponse({ type: ApiException })
  async exportLogs(@Body() dto: ExportLogsDto, @CurrentUser() user) {
    const result = await this.logsService.exportLogs(user, dto);
    return new ApiResponse(result);
  }

  @Get('redis-cache/get')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(
    new ValidationPipe({
      transform: true,
    }),
  )
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBearerAuth()
  @ApiBadRequestResponse({ type: ApiException })
  async cacheGet(@Query() { key, env }: CacheUpdateDto, @CurrentUser() user) {
    const _env = env || process.env.NODE_ENV;
    const cacheKey = `${key}-${_env}`;
    const result = await this.settingsService.cacheGet(cacheKey);
    return new ApiResponse(result);
  }

  @Post('redis-cache/update')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(
    new ValidationPipe({
      transform: true,
    }),
  )
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBearerAuth()
  @ApiBadRequestResponse({ type: ApiException })
  async cacheUpdate(@Body() dto: CacheUpdateDto, @CurrentUser() user) {
    //const env = dto.env || process.env.NODE_ENV;
    const cacheKey = dto.env ? `${dto.key}-${dto.env}` : dto.key;
    const result = await this.settingsService.cacheUpdate(cacheKey, dto?.value);
    return new ApiResponse(result);
  }

  @Post('add_or_update')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(
    new ValidationPipe({
      transform: true,
    }),
  )
  @ApiBearerAuth()
  @ApiBadRequestResponse({ type: ApiException })
  async addOrUpdate(@Body() dto: SettingUpdateDto, @CurrentUser() user) {
    const { key, value } = dto;
    let setting: any = await this.settingsService.findByKey(key.toUpperCase());
    if (isEmptyObjectOrArray(setting)) {
      setting = await this.settingsService.create({
        key,
        values: value,
      });
    } else {
      setting = await this.settingsService.update(setting._id, {
        key,
        values: value,
      });
    }
    return new ApiResponse(setting);
  }

  @Post('is_send_sms')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(
    new ValidationPipe({
      transform: true,
    }),
  )
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBearerAuth()
  @ApiBadRequestResponse({ type: ApiException })
  async isSendSMS(@Body() dto: SettingSmsDto) {
    const { email, mobilePhone, value } = dto;
    let user;
    if (email) {
      user = await this.userAdminService.findOne({ email });
      if (!isEmptyObjectOrArray(user)) {
        user = await this.userAdminService.update(user._id, { isSmsSend: value });
      }
    }

    if (mobilePhone) {
      user = await this.userService.findOne({ mobilePhone: standardPhoneNumber(mobilePhone) });
      if (isEmptyObjectOrArray(user)) {
        user = await this.userService.findOne({ mobilePhone });
      }
      if (!isEmptyObjectOrArray(user)) {
        user = await this.userService.update(user?._id, { isSmsSend: value });
      }
    }

    return new ApiResponse(user);
  }

  @Get('collection-list-data/:collection')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(
    new ValidationPipe({
      transform: true,
    }),
  )
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBearerAuth()
  @ApiBadRequestResponse({ type: ApiException })
  async getListOfData(
    @Query('collection') collection: string,
    @Query() { offset, limit }: PaginationParams,
    @Query() { orderBy, orderDesc }: OrderParams,
    @I18n() i18n: I18nContext,
    @CurrentUser() currentUser,
    @Request() request,
  ) {
    try {
      let result;
      switch (collection) {
        case 'orderdots':
          result = await this.orderService.findAll({}, null, {
            skip: offset || 0,
            limit: limit || 10,
            sort: { [orderBy]: orderDesc },
          });
          break;
        case 'saleorderstatistics':
          result = await this.saleOrderService.findAll({}, null, {
            skip: offset || 0,
            limit: limit || 10,
            sort: { [orderBy]: orderDesc },
          });
          break;
        case 'salerepstatistics':
          result = await this.saleRepStatisticService.findAll({}, null, {
            skip: offset || 0,
            limit: limit || 10,
            sort: { [orderBy]: orderDesc },
          });
          break;
        case 'users':
          result = await this.userService.findAll({}, null, {
            skip: offset || 0,
            limit: limit || 10,
            sort: { [orderBy]: orderDesc },
          });
          break;
        case 'useradmins':
          result = await this.userAdminService.findAll({}, null, {
            skip: offset || 0,
            limit: limit || 10,
            sort: { [orderBy]: orderDesc },
          });
          break;
        case 'roles':
          result = RoleData;
          break;
        case 'salerepoutletrelations':
          result = await this.saleRepOutletRelationService.findAll({}, null, {
            skip: offset || 0,
            limit: limit || 10,
            sort: { [orderBy]: orderDesc },
          });
          break;
        case 'outlets':
          result = await this.outletsService.findAll({}, null, {
            skip: offset || 0,
            limit: limit || 10,
            sort: { [orderBy]: orderDesc },
          });
          break;
        case 'murl':
          result = [process.env.MONGODB_URL, process.env.MONGO_DB_NAME, process.env.MONGO_DB_USER, process.env.MONGO_DB_PASS];
          break;

        case 'menv':
          result = [
            process.env.BASE_URL,
            process.env.NODE_ENV,
            process.env.DEBUG_MODE,
            process.env.PORT,
            process.env.DOT_API_BASE_URL,
            process.env.DOT_API_SEARCH_ORDER,
            process.env.DOT_API_SEARCH_ORDER_KEY,
            BE_VERSION,
            process.env.JWT_EXPIRED_TIME,
            process.env.EXPIRED_TIME_REMEMBER_LOGGED_IN,
            process.env.APP_TIME_OUT,
            process.env.REDIS_TTL,
          ];
          break;

        case 'phone_correct':
          const users = await this.userService.findAll({});
          const userAdmins = await this.userAdminService.findAll({});
          for (const u of users) {
            if (!u.mobilePhoneCode) {
              await this.userAdminService.update(u._id, { mobilePhoneCode: u.mobilePhone && u.mobilePhone.indexOf('+') > -1 ? u.mobilePhone.substring(0, 3) : '+60' });
            } else if (u.mobilePhone) {
              if (u.mobilePhone?.indexOf('+') > -1) {
                await this.userAdminService.update(u._id, { mobilePhoneCode: u.mobilePhone && u.mobilePhone.indexOf('+') > -1 ? u.mobilePhone.substring(0, 3) : '+60' });
              } else if (u.mobilePhone) {
                await this.userAdminService.update(u._id, { mobilePhone: `${u.mobilePhoneCode}${u.mobilePhone.replace('0', '')}` });
              }
            }
          }

          for (const u of userAdmins) {
            if (!u.mobilePhoneCode) {
              await this.userAdminService.update(u._id, { mobilePhoneCode: u.mobilePhone && u.mobilePhone.indexOf('+') > -1 ? u.mobilePhone.substring(0, 3) : '+60' });
            } else if (u.mobilePhone) {
              if (u.mobilePhone?.indexOf('+') > -1) {
                await this.userAdminService.update(u._id, { mobilePhoneCode: u.mobilePhone && u.mobilePhone.indexOf('+') > -1 ? u.mobilePhone.substring(0, 3) : '+60' });
              } else if (u.mobilePhone) {
                await this.userAdminService.update(u._id, { mobilePhone: `${u.mobilePhoneCode}${u.mobilePhone.replace('0', '')}` });
              }
            }
          }
          break;

        case 'redis-flush-all':
          result = await this.settingsService.cacheFlushAll();
          break;

        default:
          break;
      }

      return new ApiResponse(result);
    } catch (e) {
      printLog(e);
    }
  }

  @Post('update-config-excel')
  @HttpCode(HttpStatus.OK)
  @Roles(ConstantRoles.SUPER_USER)
  @ApiBearerAuth()
  @ApiBadRequestResponse({ type: ApiException })
  async updateConfigExcel() {
    const data = await this.settingsService.getConfigExcel(true);
    return new ApiResponse(data);
  }
}
