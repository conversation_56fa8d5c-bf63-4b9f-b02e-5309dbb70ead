import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FilesModule } from 'src/files/files.module';
import { JourneyPlanningsModule } from 'src/journey-plannings/journey-plannings.module';
import { AuthModule } from '../auth/auth.module';
import { OrdersModule } from '../orders/orders.module';
import { OutletsModule } from '../outlets/outlets.module';
import { SaleRepModule } from '../sale-rep/sale-rep.module';
import { UsersModule } from '../users/users.module';
import { LogsService } from './logs.service';
import { Logs, LogsSchema } from './schemas/logs.schema';
import { Settings, SettingsSchema } from './schemas/settings.schema';
import { SettingsController } from './settings.controller';
import { SettingsService } from './settings.service';
import { BrandModule } from 'src/brand/brand.module';
import { MissReasonsModule } from 'src/miss-reasons/miss-reasons.module';
import { ExternalModule } from 'src/external/external.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Settings.name, schema: SettingsSchema },
      { name: Logs.name, schema: LogsSchema },
    ]),
    AuthModule,
    forwardRef(() => UsersModule),
    forwardRef(() => SaleRepModule),
    forwardRef(() => OrdersModule),
    forwardRef(() => OutletsModule),
    forwardRef(() => JourneyPlanningsModule),
    forwardRef(() => MissReasonsModule),
    forwardRef(() => ExternalModule),
    forwardRef(() => FilesModule),
    forwardRef(() => MissReasonsModule),
    BrandModule,
  ],
  controllers: [SettingsController],
  exports: [SettingsService, LogsService],
  providers: [SettingsService, LogsService],
})
export class SettingsModule {}
