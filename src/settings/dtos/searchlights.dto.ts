import { ApiModelProperty, ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { PaginationMongooseParams } from '../../shared/common-params/pagination-mongoose.params';

export class SearchLogsDto extends PaginationMongooseParams {
  @ApiModelPropertyOptional()
  search?: string;

  @ApiModelProperty({ required: false })
  key: string;

  @ApiModelProperty({ required: false })
  userId: string;

  @ApiModelProperty({ required: false })
  startTime: Date;

  @ApiModelProperty({ required: false, default: new Date().toISOString() })
  endTime: Date;

  @ApiModelProperty({ required: false })
  code: string;
}
