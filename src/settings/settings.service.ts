import { Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseService } from '../shared/services/base-service';
import { SettingDocument, Settings } from './schemas/settings.schema';
import { isNil } from '@nestjs/common/utils/shared.utils';
import { OrderStatusModel } from '../configs/dot/my/order-status.model';
import { MyConfigs } from '../configs/dot/my/my-config';
import { SettingsStoreModel } from '../configs/dot/my/settings-store.model';
import { DOTPageName } from './enums/dot-page-name.enum';
import { OrderStatus } from '../configs/dot/my/order-status.enum';
import { Cache } from 'cache-manager';
import { ConstantCommons, DOT_SETTINGS } from '../utils/constants';
import { OutletChannel } from 'src/journey-plannings/schemas/visibility-execution.schema';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { FilesService } from 'src/files/services';

@Injectable()
export class SettingsService extends BaseService<Settings> {
  constructor(
    @InjectModel(Settings.name)
    private readonly _modelSetting: Model<SettingDocument>,
    @Inject(CACHE_MANAGER) private _cacheManager: Cache,
    private readonly filesService: FilesService,
  ) {
    super();
    this.model = _modelSetting;
    if (!process.env.NODE_APP_INSTANCE || parseInt(process.env.NODE_APP_INSTANCE) === 0) {
      this.getConfigExcel(true).then().catch();
    }
  }

  async findByKey(key: string): Promise<SettingDocument> {
    try {
      return this._modelSetting.findOne({ key });
    } catch (e) {
      return null;
    }
  }

  async currencyFormatDOT(): Promise<{ showDecimalDigits: boolean; decimalDigits: number; decimalSeparator: '.'; groupSeparator: ','; groupSize: number }> {
    const cacheKey = `currencyFormatDOT-${process.env.NODE_ENV}-${process.env.OPCO}`;
    const cacheValue: any = await this.cacheGet(cacheKey);
    if (cacheValue) {
      try {
        return await JSON.parse(cacheValue);
      } catch (e) {}
    }
    const setting = await this.findByKey(DOT_SETTINGS.DOT_OPCO_MY_STORE_SETTING);
    const settingValues: SettingsStoreModel = JSON.parse(setting?.values);
    this.cacheUpdate(cacheKey, JSON.stringify(settingValues?.currencyFormat), 3600).then();
    return settingValues.currencyFormat as { showDecimalDigits: boolean; decimalDigits: number; decimalSeparator: '.'; groupSeparator: ','; groupSize: number };
  }

  async getDotOrderStatus(orderStatus: string): Promise<OrderStatusModel> {
    const normalizedExtendedStatus: string = orderStatus ? orderStatus.replace(/ /g, '-').toLowerCase() : '';
    const currentStatus = MyConfigs.orderStatuses.find((_status: OrderStatusModel) => _status.key === normalizedExtendedStatus);
    if (!isNil(currentStatus)) {
      return currentStatus;
    }
    const code: string = normalizedExtendedStatus.split('.')[0];
    return MyConfigs.orderStatuses.find((_status: OrderStatusModel) => _status.key === code);
  }

  async getDotOrderStatusLabel(orderStatus: string): Promise<string> {
    const normalizedExtendedStatus: string = orderStatus ? orderStatus.replace(/ /g, '-').toLowerCase() : 'pending.distributor.default';
    return `dot.order-status.${normalizedExtendedStatus}`;
  }

  isGift(promotion: any, product: any) {
    return promotion?.isSet && !!(promotion?.productSetLimit && product?.rewardProduct);
  }

  isOutOfStock(product: any): boolean {
    if (product?.isInStock) {
      return !product?.isInStock;
    }
    return product?.stockStatus && !product?.hasReachedLimitedQuantity;
  }

  isBuyerChoice(product: any, pageName = DOTPageName.OrderModal) {
    return product?.isFeatured && product?.isInStock && !product?.isUnavailableForCustomers && pageName !== DOTPageName.Promotions;
  }

  getDotSkuPromotionStatus(settingsStore: SettingsStoreModel, product: any, pageName = DOTPageName.OrderModal): boolean {
    const isValidPromo = this.isValidPromo(product, settingsStore);
    const promotionStatementValue = this.getPromoStatementValue(product);
    switch (pageName) {
      case DOTPageName.OrderModal:
        return promotionStatementValue || isValidPromo;
      case DOTPageName.Brand:
      case DOTPageName.Promotions:
      case DOTPageName.CartSidebar:
        return (product.isPromo || isValidPromo || promotionStatementValue) && !product.showPromoTimer;
      default:
        break;
    }
  }

  private isValidPromo(product: any, settingsStore: SettingsStoreModel) {
    if (product.hasInvoice) {
      return product.hasDiscount;
    }
    if (settingsStore.productPromotionDiscountThreshold > 0) {
      if (product.isPromo && !isNil(product.listPrice) && !isNil(product.placedPrice)) {
        return product.listPrice - product.placedPrice >= product.listPrice * (settingsStore.productPromotionDiscountThreshold / 100);
      } else if (product.isPromo && !isNil(product.listPrice) && !isNil(product.salePrice)) {
        return product.listPrice - product.salePrice >= product.listPrice * (settingsStore.productPromotionDiscountThreshold / 100);
      }
      return false;
    }

    if (product.placedPrice && product.isPromo) {
      return true;
    }
    if (product.hasInvoice) {
      return product.isGift;
    } else if (!product.placedPrice && !isNil(product.listPrice) && !isNil(product.salePrice)) {
      return product.listPrice !== product.salePrice;
    }
    return false;
  }

  private getPromoStatementValue(product: any) {
    const promoStatements = product?.promoStatements || product?.product?.promoStatements;
    if (promoStatements === undefined) {
      return false;
    } else return promoStatements.length == 0;
  }

  public isPromotionalTotal(order: any, settings: SettingsStoreModel) {
    return order?.promotionalDiscountAmount && ((order?.hasInvoice && !settings?.showOnlyInvoiceTotals) || !order?.hasInvoice) && !settings?.hideOrderDiscountTotal;
  }

  public isCouponDiscountTotal(order: any, settings: SettingsStoreModel) {
    return order?.couponDiscountAmount && ((order?.hasInvoice && !settings?.showOnlyInvoiceTotals) || !order?.hasInvoice) && settings?.showOrderCouponDiscount;
  }

  public isPromotionalTotalCart(carts: any, settings: SettingsStoreModel) {
    return carts?.promotionalDiscountAmount > 0;
  }

  public isCouponDiscountTotalCart(carts: any, settings: SettingsStoreModel) {
    return carts?.couponDiscountAmount > 0;
  }

  async subTotalOrder(order: any, settingsStore: SettingsStoreModel) {
    if (((order?.hasInvoice && !settingsStore.showOnlyInvoiceTotals) || !order?.hasInvoice) && order?.subTotal && !settingsStore.hideOrderSubtotal) {
      return await this.formatDotPrice(order.subTotal);
    }

    if (order?.extendedPriceTotal && !settingsStore.hideOrderExtendedPriceTotal) {
      return await this.formatDotPrice(order.extendedPriceTotal);
    }

    return await this.formatDotPrice(order.subTotal);
  }

  async totalOrder(order: any, settingsStore: SettingsStoreModel) {
    // if (
    //   (order?.discountTotal && !settingsStore?.hideOrderDiscountTotal) ||
    //   (settingsStore?.showOrderCouponDiscount && order?.couponDiscountAmount)
    // ) {
    //   return order.total + order.discountTotal;
    // }

    return await this.formatDotPrice(order?.hasInvoice ? order?.invoiceTotalAmount || 0 : order?.total || 0);
  }

  public displayOrderNumber(order: any, settingsStore: SettingsStoreModel) {
    // return order?.hasInvoice ? order?.invoiceNumber : order?.number;
    return order?.number ? order?.number : order?.invoiceNumber;
  }

  async getOrderDisplayedTotalAmount(order: any, storeSettings: SettingsStoreModel) {
    let displayedAmount = null;

    if (!!storeSettings) {
      if (order.hasInvoice) {
        if (storeSettings.totalInvoiceAmountAfterOrderIsDelivered && (order.status === OrderStatus.Delivered || order.status === OrderStatus.ConfirmedDelivery)) {
          displayedAmount = order.invoiceTotalAmount;
        } else {
          switch (storeSettings.orderTotalColumnSourceProperty) {
            case 'TotalNetAmount':
              displayedAmount = order.invoiceNetTotal;
              break;
            case 'TotalInvoiceAmount':
              displayedAmount = order.invoiceTotalAmount;
              break;
          }
        }
      }

      if (!displayedAmount) {
        if (storeSettings.showSubtotalInOrderList) {
          displayedAmount = order.extendedPriceTotal ? order.extendedPriceTotal : order.subTotal;
        } else {
          displayedAmount = order.total;
        }
      }
    }

    return await this.formatDotPrice(displayedAmount);
  }

  public getOldPrice(salePrice: number, listPrice: number) {
    if (!salePrice && !listPrice) {
      return 0;
    }

    if (salePrice !== 0 && salePrice > listPrice) {
      return salePrice;
    } else {
      return listPrice;
    }
  }

  async getOrderItemPrice(item) {
    if (item?.isReturnableEmpty) {
      return await this.formatDotPrice(item?.emptiesReturn * item?.quantity);
    }

    return await this.formatDotPrice(this.getOldPrice(item?.salePrice, item?.listPrice) * (item?.quantity || 1));
  }

  async getExtendedPrice(item) {
    if (item?.isReturnableEmpty) {
      return item.listPrice;
    }
    return await this.formatDotPrice(item.extendedPrice);
  }

  public cacheUpdate(key: string, value: string = null, ttl = 0) {
    return this._cacheManager.set(key, value, ttl > 0 ? ttl : Number(process.env.REDIS_TTL));
  }

  async cacheGet(key: string): Promise<any> {
    return await this._cacheManager.get(key);
  }

  public async cacheFlushAll() {
    try {
      return await this._cacheManager.reset();
    } catch (e) {}
  }

  public getPhoneCountryCode = () => {
    const phoneCountryCodes = process.env.PHONE_COUNTRY_CODES;
    const defaultPhoneCountryCode = process.env.PHONE_COUNTRY_CODE_DEFAULT;
    return phoneCountryCodes.split(',').map((item) => {
      const [country, code] = item.split('|');

      return {
        country,
        code,
        imageUrl: `images/flags/${country}.png`,
        isDefault: code === defaultPhoneCountryCode,
      };
    });
  };

  public formatDotPrice = async (price: number = null) => {
    if (!price) {
      return 0;
    }
    if (process.env.OPCO === 'ID') {
      const currencyFormatDOT = await this.currencyFormatDOT();
      if (currencyFormatDOT.showDecimalDigits && currencyFormatDOT.decimalDigits > 0) {
        const divisor = Math.pow(10, currencyFormatDOT.decimalDigits);
        return price / divisor;
      }
    }
    return price || 0;
  };

  async getOutletChannels() {
    const settingKey = 'OUTLET_CHANNEL';

    const outletChannelSettings = await this._modelSetting.findOne({
      key: settingKey,
    });

    const defaultValue = {
      modernOnTradeChannels: [OutletChannel.MODERN_ON_TRADE, OutletChannel.MODERN_ON_TRADE_N, OutletChannel.MODERN_ON_TRADE_N_T],
      modernOffTradeChannels: [OutletChannel.MODERN_OFF_TRADE, OutletChannel.MODERN_OFF_TRADE_N, OutletChannel.MODERN_OFF_TRADE_N_T],
      traditionalOnTradeChannels: [OutletChannel.TRADITIONAL_ON_TRADE, OutletChannel.TRADITIONAL_ON_TRADE_N, OutletChannel.TRADITIONAL_ON_TRADE_N_T],
      traditionalOffTradeChannels: [OutletChannel.TRADITIONAL_OFF_TRADE, OutletChannel.TRADITIONAL_OFF_TRADE_N, OutletChannel.TRADITIONAL_OFF_TRADE_N_T],
    };

    if (!outletChannelSettings) {
      await this._modelSetting.create({
        key: settingKey,
        values: JSON.stringify(defaultValue),
      });
      return {
        map: defaultValue,
        channels: Object.values(defaultValue).reduce((initial, current) => [...initial, ...current], [] as OutletChannel[]),
      };
    }

    const outletChannelSettingValues = JSON.parse(outletChannelSettings.values);

    return {
      map: outletChannelSettingValues as unknown as {
        modernOnTradeChannels: OutletChannel[];
        modernOffTradeChannels: OutletChannel[];
        traditionalOnTradeChannels: OutletChannel[];
        traditionalOffTradeChannels: OutletChannel[];
      },
      channels: (Object.values(outletChannelSettingValues) as OutletChannel[]).reduce((initial, current) => [...initial, ...current], []) as OutletChannel[],
    };
  }

  /**
   *
   * @param getNewData
   */
  async getConfigExcel(getNewData = false): Promise<
    | {
        packageTypeFilter: [];
        listBusinessChannel: null;
        listBusinessSegment: [];
        listBusinessSubChannel: [];
        convertToCarton: [];
      }
    | any
  > {
    const returnData: any = {
      packageTypeFilter: [],
      listBusinessChannel: null,
      listBusinessSegment: [],
      listBusinessSubChannel: [],
      convertToCarton: [],
    };
    const key = ConstantCommons.CONFIG_EXEL;
    if (!getNewData) {
      const cached = await this._cacheManager.get(key);
      if (cached) {
        return cached;
      }
    }
    const url = process.env.CONFIG_EXCEL_URL;
    const data = await this.filesService.readXlsxFromLink(url);
    if (data && data.length) {
      returnData.packageTypeFilter =
        data
          .find((e) => e.sheetName == 'PackageTypeFilter')
          ?.data?.map((e: any) => e?.List)
          .filter((e) => e != null) || [];
      returnData.listBusinessChannel =
        data
          .find((e) => e.sheetName == 'CustomerRequest')
          ?.data?.reduce((acc: any[], curr: any) => {
            const existingChannel = acc.find((item) => item.channel === curr?.Business_Channel);

            if (existingChannel) {
              if (!existingChannel.subChannel.includes(curr?.Business_Sub_Channel)) {
                existingChannel.subChannel.push(curr?.Business_Sub_Channel);
              }
            } else {
              acc.push({
                channel: curr?.Business_Channel?.trim(),
                subChannel: curr?.Business_Sub_Channel?.trim() ? [curr?.Business_Sub_Channel?.trim()] : [],
              });
            }

            return acc;
          }, []) || [];

      returnData.listBusinessSegment =
        data
          .find((e) => e.sheetName == 'CustomerRequest')
          ?.data?.map((e: any) => e?.Business_Segment?.trim())
          .filter((e) => e != null) || [];

      returnData.convertToCarton =
        data
          .find((e) => e.sheetName == 'ConverterToCarton')
          ?.data?.map((e: any) => {
            return {
              pcs: e.PCS,
              pcsMpsToCarton: e.PcsMpsToCarton,
            };
          })
          .filter((e) => e != null) || [];
      await this._cacheManager.set(key, returnData || [], 60 * 60 * 24); //24 hours
    }
    console.log('🚀 ~ ConfigExcel:', returnData);
    return returnData;
  }
}
