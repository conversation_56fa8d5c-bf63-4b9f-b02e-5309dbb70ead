import { ConstantCommons } from '../../utils/constants';
import { I18nContext } from 'nestjs-i18n';

export const PackageTypeFilterSetting = ['Can', 'Bottles', 'Keg', 'Crates'];
export const PriorityFilterSetting = async (i18n: I18nContext) => {
  return [
    { label: await i18n.t('oms.filter.mustHave'), tagging: ConstantCommons.MUST_HAVE_SKU_LABEL },
    { label: await i18n.t('oms.filter.optional'), tagging: ConstantCommons.OPTIONAL_SKU_LABEL },
  ];
};
