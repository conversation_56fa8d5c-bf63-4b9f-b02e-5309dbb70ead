import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

import { Outlet } from '../../outlets/schemas/outlet.schema';
import { BaseSchema } from '../../shared/schemas/base.schema';
import { User } from '../../users/schemas/user.schema';
import { NotificationType } from '../enums';
import { PushNotification } from './push-notification.schema';

export type NotificationLogDocument = NotificationLog & Document;

@Schema()
export class NotificationLog extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: User.name, index: true })
  user: User;

  @Prop()
  title: string;

  @Prop()
  body: string;

  @Prop({ type: Types.ObjectId, ref: Outlet.name, index: true })
  outlet: Outlet;

  @Prop({ enum: NotificationType, default: NotificationType.MISSED_VISITED_OUTLET, index: true })
  type: string;

  @Prop({ default: false, index: true })
  read: boolean;

  @Prop({ default: false, index: true })
  deleted: boolean;

  @Prop({ type: Types.ObjectId, ref: PushNotification.name, index: true })
  notification: PushNotification;
}

export const NotificationLogSchema = SchemaFactory.createForClass(NotificationLog);
