import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

import { BaseSchema } from '../../shared/schemas/base.schema';
import { UserAdmin } from '../../users/schemas/user-admin.schema';
import { AdminPushNotificationType } from '../enums';
import { Distributor } from 'src/distributor/schemas';

export type PushNotificationDocument = PushNotification & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class PushNotification extends BaseSchema {
  @Prop({ type: Types.ObjectId, ref: UserAdmin.name, index: true })
  createdBy: UserAdmin;

  @Prop({ type: Types.ObjectId, ref: Distributor.name })
  distributor: Distributor;

  @Prop()
  depotId: string;

  @Prop()
  title: string;

  @Prop()
  message: string;

  @Prop({ enum: AdminPushNotificationType, default: AdminPushNotificationType.CUSTOM_MESSAGE, index: true })
  type: string;

  @Prop({ index: true })
  toEveryone?: boolean;
}

export const PushNotificationSchema = SchemaFactory.createForClass(PushNotification);
