import { Injectable } from '@nestjs/common';
import * as admin from 'firebase-admin';

import { User } from '../../users/schemas/user.schema';
import { UserDetailService } from '../../users/services/user-detail.service';

@Injectable()
export class MessageService {
  constructor(private readonly _userDetailService: UserDetailService) {}

  async sendMessage(fcmToken: string, notification?: { title?: string; body?: string; imageUrl?: string }, data?: { [key: string]: string }) {
    const message = {
      data,
      notification,
      token: fcmToken,
    };
    return admin.messaging().send(message);
  }

  async sendMessageToUser(user: User, title: string, body: string, data = {}) {
    const userDevices = await this._userDetailService.findUserDevices({ userId: user._id, isUserAdmin: false });
    const tokens = userDevices.map((ud) => ud.fcmToken);
    if (tokens?.length) {
      try {
        const result = await admin.messaging().sendEachForMulticast({ tokens, notification: { title, body }, data });
        const failureIndexes = result.responses.filter((item) => !item.success).map((_, i) => i);
        if (failureIndexes?.length) {
          const listUnUsedTokens = tokens.filter((_, i) => failureIndexes.some((fI) => fI === i));
          this._userDetailService
            .addOrUpdate({
              userId: user._id,
              mobilePhone: user.mobilePhone,
              isUserAdmin: false,
              userDevice: userDevices?.filter((d) => !listUnUsedTokens.includes(d.fcmToken)) || [],
            })
            .then();
        }
      } catch (e) {
        console.log('sendMessageToUser -> e', e);
      }
    }
  }

  async sendMessageByFcmTokens(tokens: string[], title: string, body: string, data = {}): Promise<string[]> {
    try {
      const result = await admin.messaging().sendEachForMulticast({ tokens, notification: { title, body }, data });
      const failureIndexes = result.responses.filter((item) => !item.success).map((_, i) => i);
      const listUnUsedTokens = tokens.filter((_, i) => failureIndexes.some((fI) => fI === i));
      return listUnUsedTokens;
    } catch (e) {
      console.log('sendMessageToUser -> e', e);
    }
  }
}
