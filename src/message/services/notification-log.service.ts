import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';

import { Outlet } from '../../outlets/schemas/outlet.schema';
import { BaseService } from '../../shared/services/base-service';
import { User } from '../../users/schemas/user.schema';
import { NotificationGroupQuery, NotificationType } from '../enums';
import { NotificationLog, NotificationLogDocument } from '../schemas';

@Injectable()
export class NotificationLogService extends BaseService<NotificationLog> {
  constructor(
    @InjectModel(NotificationLog.name)
    private readonly _notificationLogDocumentModel: Model<NotificationLogDocument>,
  ) {
    super();
    this.model = _notificationLogDocumentModel;
  }

  async createMissedVisitOutletNotificationLog(user: User, outlet: Outlet, title: string, body: string) {
    return this.create({ user, outlet, title, body });
  }

  async countUnreadNotification(userId: string) {
    return this._notificationLogDocumentModel.count({ user: new Types.ObjectId(userId), read: false, deleted: false });
  }

  async countUnreadNotificationByType(userId: string, types: NotificationType[]) {
    return this._notificationLogDocumentModel.count({ user: new Types.ObjectId(userId), read: false, deleted: false, type: { $in: types } });
  }

  async getNotificationLogByUserId(userId: string, skip: number, limit: number, types?: NotificationGroupQuery[]) {
    let query = null;
    if (types && types.length) {
      query = this._notificationLogDocumentModel.find({ user: new Types.ObjectId(userId), deleted: false, type: { $in: types } });
    } else {
      query = this._notificationLogDocumentModel.find({ user: new Types.ObjectId(userId), deleted: false });
    }
    return query.populate('outlet user').sort({ createdAt: 'desc' }).skip(skip).limit(limit);
  }

  async deleteMissedVisitedOutletNotifications() {
    const currentTime = moment().toISOString();
    return this._notificationLogDocumentModel.updateMany(
      {
        deleted: false,
        type: NotificationType.MISSED_VISITED_OUTLET,
        createdAt: { $lte: currentTime },
      },
      {
        deleted: true,
        updatedAt: moment().tz(process.env.TZ),
      },
    );
  }

  async createMany(userIds: string[], notificationId: string, title: string, body: string, type: NotificationType) {
    await this._notificationLogDocumentModel.insertMany(
      userIds.map((userId) => ({
        user: new Types.ObjectId(userId),
        title,
        body,
        type,
        notification: new Types.ObjectId(notificationId),
        createdAt: moment().tz(process.env.TZ),
        updatedAt: moment().tz(process.env.TZ),
      })),
    );
  }

  async getRecipientsOfNotification(notificationId: string) {
    const raw = await this.model
      .aggregate()
      .match({
        notification: new Types.ObjectId(notificationId),
      })
      .lookup({
        localField: 'user',
        from: 'users',
        foreignField: '_id',
        as: 'recipient',
      })
      .unwind({
        path: '$recipient',
        preserveNullAndEmptyArrays: true,
      })
      .project({
        id: '$recipient._id',
        username: '$recipient.username',
      })
      .exec();

    return raw.map((item) => ({
      id: item._id,
      name: item.username || item._id,
    }));
  }
}
