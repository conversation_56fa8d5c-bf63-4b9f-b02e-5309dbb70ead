import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { I18nContext } from 'nestjs-i18n';
import { DistributorUserRelationService } from 'src/distributor/services';
import { UsersService } from 'src/users/services/users.service';

import { BaseService } from '../../shared/services/base-service';
import { isEmptyObject, isEmptyObjectOrArray } from '../../utils';
import { AdminCustomMessageDto, PushNotificationSortOrder } from '../dtos';
import { NotificationType } from '../enums';
import { PushNotification, PushNotificationDocument } from '../schemas';
import { MessageService } from './message.service';
import { NotificationLogService } from './notification-log.service';
import { UserDetailService } from '../../users/services/user-detail.service';

@Injectable()
export class PushNotificationService extends BaseService<PushNotification> {
  constructor(
    @InjectModel(PushNotification.name)
    private readonly _model: Model<PushNotificationDocument>,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _notificationLogService: NotificationLogService,
    private readonly _messageService: MessageService,
    private readonly _userService: UsersService,
    private readonly _userDetailService: UserDetailService,
  ) {
    super();
    this.model = _model;
  }

  async getPushNotificationList(filter = {}, offset: number, limit: number, sort: PushNotificationSortOrder) {
    const total = await this._model.count(filter).exec();
    const data = await this._model.find(filter).sort(sort).skip(offset).limit(limit).exec();
    return [data, total];
  }

  async getPushNotificationListWithDistributor(distributor_Id: string, skip: number, limit: number, sort: PushNotificationSortOrder) {
    const aggregation = this._model
      .aggregate()
      .project({
        _id: 0,
        pn: '$$ROOT',
      })
      .match({
        'pn.distributor': distributor_Id,
      })
      .lookup({
        localField: 'pn.distributor',
        from: 'distributors',
        foreignField: '_id',
        as: 'dis',
      })
      .lookup({
        localField: 'pn.createdBy',
        from: 'useradmins',
        foreignField: '_id',
        as: 'ua',
      })
      .unwind({
        path: '$ua',
        preserveNullAndEmptyArrays: false,
      })
      .unwind({
        path: '$dis',
        preserveNullAndEmptyArrays: false,
      })
      .project({
        _id: '$pn._id',
        message: '$pn.message',
        createdAt: '$pn.createdAt',
        type: '$pn.type',
        toEveryone: '$pn.toEveryone',
        distributorId: '$dis.distributorId',
        distributorName: '$dis.distributorName',
      });
    if (sort && !isEmptyObject(sort)) {
      aggregation.sort(sort).collation({ locale: 'en' });
    }
    return aggregation.facet({
      totalRecords: [
        {
          $count: 'total',
        },
      ],
      data: [
        {
          $skip: skip >= 0 ? skip : 0,
        },
        {
          $limit: limit >= 1 ? limit : 1,
        },
      ],
    });
  }

  async adminPushNotification(adminId: string, dto: AdminCustomMessageDto, i18n: I18nContext) {
    // check has distributor or not
    const distributorRelations = await this._distributorUserRelationService.findAllByUserAdminId(adminId);
    const distributor = distributorRelations.find((e) => e.distributor.distributorId === dto.distributorId);
    if (!distributor) {
      throw new BadRequestException(i18n.t('distributor.not_found'));
    }

    const { message, salesRepIds: salesRepIdsDto } = dto;

    // get all sales rep in distributor
    const allSalesRepObjectIds = await this._distributorUserRelationService.getSalesRepObjectIdsInDistributor(distributor.distributor._id);
    // if dto has no salesRepIds, then target sales rep is all sales rep in distributor
    let targetSalesRepIds = allSalesRepObjectIds;

    if (!targetSalesRepIds?.length) {
      return {};
    }

    // check each sales rep in dto exists in distributor or not
    if (salesRepIdsDto) {
      targetSalesRepIds = await this._userService.getSalesRepObjectIdsByIds(salesRepIdsDto);
      const notExistInDistributor = targetSalesRepIds.some((item) => !allSalesRepObjectIds.includes(item));
      if (notExistInDistributor) {
        throw new BadRequestException(i18n.t('saleRep.not_in_distributor'));
      }
    }

    const tokenData = await this._userDetailService.findAll({ userId: { $in: targetSalesRepIds } });
    if (isEmptyObjectOrArray(tokenData)) {
      return;
    }
    const fcmTokens = tokenData.flatMap((user) => user.userDevice.map((token) => token.fcmToken));

    const session = await this._model.startSession();
    let notification: PushNotification;

    try {
      await session.withTransaction(async () => {
        notification = await this._model.create({
          createdBy: new Types.ObjectId(adminId),
          distributor: distributor.distributor._id,
          message,
          title: '',
          toEveryone: !salesRepIdsDto || allSalesRepObjectIds.length === salesRepIdsDto.length,
        });

        await this._notificationLogService.createMany(targetSalesRepIds, notification._id, '', message, NotificationType.DISTRIBUTOR_ADMIN_PUSH_NOTIFICATION);

        // send notification to device
        if (fcmTokens.length) {
          const unusedTokens = await this._messageService.sendMessageByFcmTokens(fcmTokens, '', message);
          if (unusedTokens?.length) {
            await this._userDetailService.deleteAllUserDeviceByFcmToken(unusedTokens);
          }
        }
      });
    } finally {
      session.endSession();
    }
    return notification;
  }

  async getDetail(notificationId: string) {
    const notification = await this.findOne({ _id: new Types.ObjectId(notificationId) });

    if (notification.toEveryone) {
      return notification;
    }

    const recipients = await this._notificationLogService.getRecipientsOfNotification(notificationId);

    return {
      ...(notification as PushNotificationDocument).toObject(),
      recipients,
    };
  }
}
