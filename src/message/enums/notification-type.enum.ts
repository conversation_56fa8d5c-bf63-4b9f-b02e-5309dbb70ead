export enum NotificationType {
  MISSED_VISITED_OUTLET = 'MISSED_VISITED_OUTLET',
  ADMIN_PUSH_NOTIFICATION = 'ADMIN_PUSH_NOTIFICATION',
  DISTRIBUTOR_ADMIN_PUSH_NOTIFICATION = 'DISTRIBUTOR_ADMIN_PUSH_NOTIFICATION',
}

export enum NotificationGroupQuery {
  admin = 'admin',
  system = 'system',
}

export const NotificationGroupType = {
  admin: [NotificationType.ADMIN_PUSH_NOTIFICATION, NotificationType.DISTRIBUTOR_ADMIN_PUSH_NOTIFICATION],
  system: [NotificationType.MISSED_VISITED_OUTLET],
};
