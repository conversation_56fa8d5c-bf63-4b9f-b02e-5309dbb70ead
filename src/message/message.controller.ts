import { Body, Controller, Get, HttpCode, HttpStatus, NotFoundException, Param, Patch, Post, Query, UseGuards, Version } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { I18n, I18nContext } from 'nestjs-i18n';

import { RolesGuard } from 'src/shared/guards/roles.guard';
import { JwtAuthGuard } from '../auth/decorator/guard/jwt-auth.guard';
import { DistributorService, DistributorUserRelationService } from '../distributor/services';
import { ApiException } from '../shared/api-exception.model';
import { CurrentUser } from '../shared/decorator/current-user.decorator';
import { Roles } from '../shared/decorator/roles.decorator';
import { PaginationDto } from '../shared/dtos/pagination.dto';
import { Serialize } from '../shared/interceptor/serialize.interceptor';
import { ApiResponse } from '../shared/response/api-response';
import { User } from '../users/schemas/user.schema';
import { toListResponse } from '../utils';
import { ConstantRoles } from '../utils/constants/role';
import { AdminCustomMessageDto, FetchedPushNotificationDto, PushNotificationDto, PushNotificationListDto } from './dtos';
import { NotificationGroupQuery, NotificationGroupType, NotificationType } from './enums';
import { MessageService, NotificationLogService, PushNotificationService } from './services';
import { UserDetailService } from '../users/services/user-detail.service';

@ApiTags('Message')
@ApiHeader({ name: 'locale', description: 'en' })
@ApiHeader({ name: 'version', description: '1' })
@Controller('api/message')
@UseGuards(JwtAuthGuard, RolesGuard)
export class MessageController {
  constructor(
    private readonly _messageService: MessageService,
    private readonly _notificationLogService: NotificationLogService,
    private readonly _pushNotificationService: PushNotificationService,
    private readonly _distributorUserRelationService: DistributorUserRelationService,
    private readonly _distributorService: DistributorService,
    private readonly _userDetailService: UserDetailService,
  ) {}

  @Post('send-push-notification')
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'For testing push notification purpose',
    description: 'For testing push notification purpose',
  })
  async sendMessage(@Body() pushNotificationData: PushNotificationDto) {
    const { fcmToken, notification, data } = pushNotificationData;
    try {
      return new ApiResponse(await this._messageService.sendMessage(fcmToken, notification, data));
    } catch (e) {
      return new ApiResponse(null, e);
    }
  }
  @Version(['2', '3', '4', '5', '6'])
  @Get('users/:userId/logs')
  @ApiBearerAuth()
  @ApiBadRequestResponse({ type: ApiException })
  async getMessageLogv2(@Param('userId') userId: string, @Query() { skip, limit }: PaginationDto, @Query() { type }: { type: NotificationGroupQuery }, @I18n() i18n: I18nContext) {
    let typeQuery = null;
    if (type && type === NotificationGroupQuery.system) {
      typeQuery = NotificationGroupType.system;
    }
    if (type && type === NotificationGroupQuery.admin) {
      typeQuery = NotificationGroupType.admin;
    }
    const logs = await this._notificationLogService.getNotificationLogByUserId(userId, skip, limit, typeQuery);
    const result = {
      data: [],
      unReadAdmin: 0,
      unReadSystem: 0,
    };
    result.unReadAdmin = await this._notificationLogService.countUnreadNotificationByType(userId, NotificationGroupType.admin);
    result.unReadSystem = await this._notificationLogService.countUnreadNotificationByType(userId, NotificationGroupType.system);

    for (const item of logs) {
      const { _id, title, body, outlet, type, read, createdAt } = item;
      if (type === NotificationType.MISSED_VISITED_OUTLET) {
        const i18nTitle = await i18n.t(title);
        const i18nBody = await i18n.t(body, { args: { outletName: outlet.name } });
        result.data.push({ _id, title: i18nTitle, body: i18nBody, type, outletId: outlet._id, read, createdAt });
      } else {
        result.data.push({ _id, title, body, type, outletId: null, read, createdAt });
      }
    }
    return new ApiResponse(result);
  }

  @Get('users/:userId/logs')
  @ApiBearerAuth()
  @ApiBadRequestResponse({ type: ApiException })
  async getMessageLog(@Param('userId') userId: string, @Query() { skip, limit }: PaginationDto, @I18n() i18n: I18nContext) {
    const logs = await this._notificationLogService.getNotificationLogByUserId(userId, skip, limit);
    const result = [];
    for (const item of logs) {
      const { _id, title, body, outlet, type, read, createdAt } = item;
      if (type === NotificationType.MISSED_VISITED_OUTLET) {
        const i18nTitle = await i18n.t(title);
        const i18nBody = await i18n.t(body, { args: { outletName: outlet.name } });
        result.push({ _id, title: i18nTitle, body: i18nBody, type, outletId: outlet._id, read, createdAt });
      } else {
        result.push({ _id, title, body, type, outletId: null, read, createdAt });
      }
    }
    return new ApiResponse(result);
  }

  @Patch('users/:userId/logs/:logId/read')
  @Roles(ConstantRoles.SALE_REP)
  @ApiBearerAuth()
  @ApiBadRequestResponse({ type: ApiException })
  @ApiOperation({
    summary: 'Set a notification is read',
  })
  async setReadStatusForGivenLog(@Param('userId') userId: string, @Param('logId') logId: string, @I18n() i18n: I18nContext) {
    const log = await this._notificationLogService.findOne({
      user: new Types.ObjectId(userId),
      _id: logId,
    });
    if (!log) {
      throw new NotFoundException(i18n.t('notification.logs.not_found'));
    }
    log.read = true;
    await this._notificationLogService.update(logId, log);
    const unReadAdmin = await this._notificationLogService.countUnreadNotificationByType(userId, NotificationGroupType.admin);
    const unReadSystem = await this._notificationLogService.countUnreadNotificationByType(userId, NotificationGroupType.system);

    return new ApiResponse({ _id: logId, read: true, unReadAdmin, unReadSystem });
  }

  @Post('push-notification/send')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Serialize(FetchedPushNotificationDto)
  @ApiOperation({
    summary: 'Send push notification',
  })
  async sendPushNotification(@Body() dto: AdminCustomMessageDto, @CurrentUser() currentUser: User, @I18n() i18n: I18nContext) {
    const result = await this._pushNotificationService.adminPushNotification(currentUser._id, dto, i18n);
    return new ApiResponse(result);
  }

  @Post('push-notification/list')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get push notification list',
  })
  async getPushNotificationList(@Body() pushNotificationListDto: PushNotificationListDto, @CurrentUser() currentUser: User) {
    const userRole = await this._userDetailService.findUserRoles({ userId: currentUser._id.toString(), isUserAdmin: true });
    const { skip, limit, sort, distributorId } = pushNotificationListDto;
    if (!distributorId) {
      return new ApiResponse(toListResponse([[], 0]));
    }
    if (userRole.some((r) => r.roleKey === ConstantRoles.DISTRIBUTOR_ADMIN)) {
      const distributorRelations = await this._distributorUserRelationService.findAllByUserAdminId(currentUser._id);
      const distributor = distributorRelations.find((e) => e.distributor.distributorId === distributorId);
      if (!distributor) {
        return new ApiResponse(toListResponse([[], 0]));
      }
      const [{ totalRecords, data }] = await this._pushNotificationService.getPushNotificationListWithDistributor(distributor.distributor._id, skip, limit, sort);
      return new ApiResponse(toListResponse([data, totalRecords?.[0]?.total ?? 0]));
    } else {
      const distributor = await this._distributorService.findOne({
        distributorId,
      });
      if (!distributor) {
        return new ApiResponse(toListResponse([[], 0]));
      }
      const [{ totalRecords, data }] = await this._pushNotificationService.getPushNotificationListWithDistributor(distributor._id, skip, limit, sort);
      return new ApiResponse(toListResponse([data, totalRecords?.[0]?.total ?? 0]));
    }
  }

  @Get('push-notification/:pushNotificationId')
  @ApiBadRequestResponse({ type: ApiException })
  @Roles(ConstantRoles.DISTRIBUTOR_ADMIN, ConstantRoles.MARKET_DEVELOPMENT_SUPERVISOR, ConstantRoles.SUPER_USER)
  @ApiBearerAuth()
  @Serialize(FetchedPushNotificationDto)
  @ApiOperation({
    summary: 'Get push notification detail',
  })
  async getPushNotificationDetail(@Param('pushNotificationId') pushNotificationId: string) {
    const result = await this._pushNotificationService.getDetail(pushNotificationId);
    return new ApiResponse(result);
  }
}
