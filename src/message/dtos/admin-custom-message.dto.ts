import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class AdminCustomMessageDto {
  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  message: string;

  @ApiModelProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  distributorId: string;

  @ApiModelProperty()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value.filter((item, index, self) => self.indexOf(item) === index))
  salesRepIds?: string[];
}
