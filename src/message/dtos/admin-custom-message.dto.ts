import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class AdminCustomMessageDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  message: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  distributorId: string;

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value.filter((item, index, self) => self.indexOf(item) === index))
  salesRepIds?: string[];
}
