import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class NotificationDto {
  @ApiPropertyOptional()
  title: string;

  @ApiPropertyOptional()
  body: string;

  @ApiPropertyOptional()
  imageUrl: string;
}

export class PushNotificationDto {
  @ApiProperty()
  readonly fcmToken: string;
  @ApiPropertyOptional()
  readonly notification?: NotificationDto;
  @ApiPropertyOptional()
  readonly data: { [key: string]: string };
}
