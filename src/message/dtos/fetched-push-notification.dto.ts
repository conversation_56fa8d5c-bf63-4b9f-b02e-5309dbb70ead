import { Expose, Transform } from 'class-transformer';

export class FetchedPushNotificationDto {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  createdAt: string;

  @Expose()
  message: string;

  @Expose()
  type: string;

  @Expose()
  toEveryone?: boolean;

  @Expose()
  recipients?: Recipient[];
}

export class Recipient {
  @Expose()
  id: string;

  @Expose()
  name: string;
}
