import { ApiPropertyOptional } from '@nestjs/swagger';
import { SortOrder } from 'mongoose';

import { PaginationDto } from '../../shared/dtos/pagination.dto';
import { SortOrderDto } from '../../shared/dtos/sort-order.dto';

export class PushNotificationSortOrder extends SortOrderDto {
  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  createdAt: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  message: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorId: SortOrder;

  @ApiPropertyOptional({ default: 1, description: 'ASC or 1, DESC or -1' })
  distributorName: SortOrder;
}

export class PushNotificationListDto extends PaginationDto {
  @ApiPropertyOptional()
  sort: PushNotificationSortOrder;

  @ApiPropertyOptional()
  distributorId?: string;
}
