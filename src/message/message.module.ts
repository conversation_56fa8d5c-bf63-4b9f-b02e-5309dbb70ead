import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { MessageController } from './message.controller';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';
import { NotificationLog, NotificationLogSchema, PushNotification, PushNotificationSchema } from './schemas';
import { MessageService, NotificationLogService, PushNotificationService } from './services';
import { DistributorModule } from '../distributor/distributor.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: NotificationLog.name,
        schema: NotificationLogSchema,
      },
      {
        name: PushNotification.name,
        schema: PushNotificationSchema,
      },
    ]),
    AuthModule,
    forwardRef(() => DistributorModule),
    // DistributorModule,
    forwardRef(() => UsersModule),
  ],
  providers: [MessageService, NotificationLogService, PushNotificationService],
  exports: [MessageService, NotificationLogService, PushNotificationService],
  controllers: [MessageController],
})
export class MessageModule {}
