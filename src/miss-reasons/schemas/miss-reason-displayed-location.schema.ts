import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from 'src/shared/schemas/base.schema';
import { MissReasonLocation } from '../enums/miss-reason-location.enum';
import { MissReasonTranslation } from '../dtos/create-miss-reason.dto';
import { MissReason } from './miss-reason.schema';

export type MissReasonDisplayedLocationDocument = MissReasonDisplayedLocation & Document;

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class MissReasonDisplayedLocation extends BaseSchema {
  @Prop({
    enum: MissReasonLocation,
    unique: true,
  })
  location: MissReasonLocation;

  @Prop({
    type: [Types.ObjectId],
    ref: MissReason.name,
  })
  missReasons: MissReason[];
}

export const MissReasonDisplayedLocationSchema = SchemaFactory.createForClass(MissReasonDisplayedLocation);
