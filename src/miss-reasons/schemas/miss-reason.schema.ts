import {Prop, Schema, SchemaFactory} from '@nestjs/mongoose';
import {Document} from 'mongoose';
import {BaseSchema} from 'src/shared/schemas/base.schema';
import {MissReasonLocation} from '../enums/miss-reason-location.enum';
import {MissReasonTranslation} from '../dtos/create-miss-reason.dto';

export type MissReasonDocument = MissReason & Document;

export type RequireEvidenceSettings = {
  [key in MissReasonLocation]?: boolean;
};

@Schema({
  toJSON: {
    virtuals: true,
  },
  timestamps: true,
  versionKey: false,
})
export class MissReason extends BaseSchema {
  @Prop()
  controllable: boolean;

  @Prop()
  reschedulable: boolean;

  @Prop({
    default: true,
  })
  isActive: boolean;

  @Prop()
  locations: MissReasonLocation[];

  @Prop({
    obj: MissReasonTranslation,
  })
  translations: MissReasonTranslation[];

  @Prop({
    type: Object,
  })
  requireEvidenceSettings: RequireEvidenceSettings;
}

export const MissReasonSchema = SchemaFactory.createForClass(MissReason);

MissReasonSchema.index(
    { updatedAt: -1, createdAt: -1 }
);
