import { ApiModelProperty, ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ArrayNotEmpty, IsBoolean, IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import { MissReasonLocation } from '../enums/miss-reason-location.enum';
import { Transform, Type } from 'class-transformer';
import { RequireEvidenceSettings } from '../schemas/miss-reason.schema';

export class MissReasonTranslation {
  @ApiModelProperty()
  @IsString()
  language: string;

  @ApiModelPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;

  @ApiModelProperty()
  @IsString()
  reason: string;

  @ApiModelProperty()
  @IsString()
  warning: string;
}

export class CreateMissReasonDto {
  @ApiModelProperty()
  @IsBoolean()
  controllable: boolean;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsEnum(MissReasonLocation, { each: true })
  locations: MissReasonLocation[];

  @ApiModelProperty()
  @ValidateNested({ each: true })
  @ArrayNotEmpty()
  @Type(() => MissReasonTranslation)
  translations: MissReasonTranslation[];

  @ApiModelProperty()
  requireEvidenceSettings: RequireEvidenceSettings;
}
