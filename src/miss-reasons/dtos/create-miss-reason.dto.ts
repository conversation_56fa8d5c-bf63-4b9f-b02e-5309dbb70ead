import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayNotEmpty, IsBoolean, IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import { MissReasonLocation } from '../enums/miss-reason-location.enum';
import { Type } from 'class-transformer';
import { RequireEvidenceSettings } from '../schemas/miss-reason.schema';

export class MissReasonTranslation {
  @ApiProperty()
  @IsString()
  language: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;

  @ApiProperty()
  @IsString()
  reason: string;

  @ApiProperty()
  @IsString()
  warning: string;
}

export class CreateMissReasonDto {
  @ApiProperty()
  @IsBoolean()
  controllable: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(MissReasonLocation, { each: true })
  locations: MissReasonLocation[];

  @ApiProperty()
  @ValidateNested({ each: true })
  @ArrayNotEmpty()
  @Type(() => MissReasonTranslation)
  translations: MissReasonTranslation[];

  @ApiProperty()
  requireEvidenceSettings: RequireEvidenceSettings;
}
