import { ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayNotEmpty, IsBoolean, IsEnum, IsOptional, ValidateNested } from 'class-validator';
import { MissReasonLocation } from '../enums/miss-reason-location.enum';
import { Type } from 'class-transformer';
import { MissReasonTranslation } from './create-miss-reason.dto';
import { RequireEvidenceSettings } from '../schemas/miss-reason.schema';

export class UpdateMissReasonDto {
  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  controllable?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(MissReasonLocation, { each: true })
  @IsOptional()
  locations?: MissReasonLocation[];

  @ApiPropertyOptional()
  @ValidateNested({ each: true })
  @ArrayNotEmpty()
  @Type(() => MissReasonTranslation)
  @IsOptional()
  translations?: MissReasonTranslation[];

  @ApiPropertyOptional()
  requireEvidenceSettings?: RequireEvidenceSettings;
}
