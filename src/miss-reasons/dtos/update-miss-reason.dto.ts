import { ApiModelPropertyOptional } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ArrayNotEmpty, IsBoolean, IsEnum, IsOptional, ValidateNested } from 'class-validator';
import { MissReasonLocation } from '../enums/miss-reason-location.enum';
import { Type } from 'class-transformer';
import { MissReasonTranslation } from './create-miss-reason.dto';
import { RequireEvidenceSettings } from '../schemas/miss-reason.schema';

export class UpdateMissReasonDto {
  @ApiModelPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiModelPropertyOptional()
  @IsBoolean()
  @IsOptional()
  controllable?: boolean;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsEnum(MissReasonLocation, { each: true })
  @IsOptional()
  locations?: MissReasonLocation[];

  @ApiModelPropertyOptional()
  @ValidateNested({ each: true })
  @ArrayNotEmpty()
  @Type(() => MissReasonTranslation)
  @IsOptional()
  translations?: MissReasonTranslation[];

  @ApiModelPropertyOptional()
  requireEvidenceSettings?: RequireEvidenceSettings;
}
