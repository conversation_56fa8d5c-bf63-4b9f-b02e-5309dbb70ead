import { ApiProperty } from '@nestjs/swagger';
import { ArrayNotEmpty, IsEnum, IsString } from 'class-validator';
import { MissReasonLocation } from '../enums/miss-reason-location.enum';

export class UpdateMissReasonDisplayedLocationDto {
  @ApiProperty()
  @IsEnum(MissReasonLocation)
  location: MissReasonLocation;

  @ApiProperty()
  @IsString({ each: true })
  @ArrayNotEmpty()
  missReasonIds: string[];
}
