import { ApiModelProperty } from '@nestjs/swagger/dist/decorators/api-model-property.decorator';
import { ArrayNotEmpty, IsEnum, IsString } from 'class-validator';
import { MissReasonLocation } from '../enums/miss-reason-location.enum';

export class UpdateMissReasonDisplayedLocationDto {
  @ApiModelProperty()
  @IsEnum(MissReasonLocation)
  location: MissReasonLocation;

  @ApiModelProperty()
  @IsString({ each: true })
  @ArrayNotEmpty()
  missReasonIds: string[];
}
