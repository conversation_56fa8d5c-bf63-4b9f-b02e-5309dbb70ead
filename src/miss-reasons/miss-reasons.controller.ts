import { Body, Controller, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { MissReasonsService } from './miss-reasons.service';
import { JwtAuthGuard } from 'src/auth/decorator/guard/jwt-auth.guard';
import { Roles } from 'src/shared/decorator/roles.decorator';
import { ConstantRoles } from 'src/utils/constants/role';
import { RolesGuard } from 'src/shared/guards/roles.guard';
import { CreateMissReasonDto } from './dtos/create-miss-reason.dto';
import { UpdateMissReasonDto } from './dtos/update-miss-reason.dto';
import { I18n, I18nContext } from 'nestjs-i18n';
import { UpdateMissReasonDisplayedLocationDto } from './dtos/update-miss-reason-displayed-location.dto';

@Controller('api/miss-reasons')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(ConstantRoles.SUPER_USER)
export class MissReasonsController {
  constructor(private readonly service: MissReasonsService) {}

  @Get('configuration')
  getMissReasonConfiguration() {
    return this.service.getConfiguration();
  }

  @Post()
  createMissReason(@Body() dto: CreateMissReasonDto) {
    return this.service.create(dto);
  }

  @Put('update/:id')
  updateMissReason(@Param('id') id: string, @Body() dto: UpdateMissReasonDto, @I18n() i18n: I18nContext) {
    return this.service.update(id, dto, i18n);
  }

  @Put('displayed-location')
  updateMissReasonDisplayedLocation(@Body() dto: UpdateMissReasonDisplayedLocationDto, @I18n() i18n: I18nContext) {
    return this.service.updateDisplayedLocation(dto, i18n);
  }

  @Get('migrate-data')
  migrateData(@I18n() i18n: I18nContext) {
    return this.service.migrateOldData(i18n);
  }
}
