import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateMissReasonDto, MissReasonTranslation } from './dtos/create-miss-reason.dto';
import { UpdateMissReasonDto } from './dtos/update-miss-reason.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { MissReason } from './schemas/miss-reason.schema';
import { I18nContext } from 'nestjs-i18n';
import { UpdateMissReasonDisplayedLocationDto } from './dtos/update-miss-reason-displayed-location.dto';
import { MissReasonDisplayedLocation } from './schemas/miss-reason-displayed-location.schema';
import { MissReasonLocation } from './enums/miss-reason-location.enum';
import { JourneyPlanMissedReason } from 'src/journey-plannings/schemas/journey-plan-missed-reason.schema';

@Injectable()
export class MissReasonsService {
  constructor(
    @InjectModel(MissReason.name)
    private readonly missReasonModel: Model<MissReason>,
    @InjectModel(MissReasonDisplayedLocation.name)
    private readonly missReasonDisplayedLocationModel: Model<MissReasonDisplayedLocation>,
    @InjectModel(JourneyPlanMissedReason.name)
    private readonly journeyPlanMissedReasonModel: Model<JourneyPlanMissedReason>,
  ) {}

  async getMissReasonByIds(ids?: string[]) {
    const condition = {};
    if (ids && ids.length) {
      condition['_id'] = {
        $in: ids.map((id) => new Types.ObjectId(id)),
      };
    }

    return this.missReasonModel.find(condition);
  }

  convertMissReasonsToMap(missReasons: MissReason[]) {
    return missReasons.reduce((pre, curr) => ({ ...pre, [String(curr._id)]: curr }), {} as Record<string, MissReason>);
  }

  getMissReasonTranslation(translations: MissReasonTranslation[], language: string) {
    if (!translations.length) {
      return {
        reason: '',
        warning: '',
      };
    }

    const translation =
      translations.find((translation) => translation.language === language.toUpperCase()) || translations.find((translation) => translation.isDefault) || translations[0];

    return {
      reason: translation.reason,
      warning: translation.warning,
    };
  }

  async getConfiguration() {
    const missReasonsQuery = this.missReasonModel.find().sort({ updatedAt: -1, createdAt: -1 });
    const missReasonDisplayedLocationsQuery = this.missReasonDisplayedLocationModel.find();

    const [missReasons, displayedLocations] = await Promise.all([missReasonsQuery, missReasonDisplayedLocationsQuery]);

    return {
      missReasons,
      displayedLocations: Object.values(MissReasonLocation).reduce((acc, location) => {
        const displayedLocation = displayedLocations.find((item) => item.location === location);

        const missReasonIds = displayedLocation?.missReasons || [];
        const filteredMissReasons = missReasonIds.filter((id) => missReasons.find((item) => String(item._id) === String(id))?.locations?.includes(location));

        return {
          ...acc,
          [location]: filteredMissReasons,
        };
      }, {}),
    };
  }

  async create(dto: CreateMissReasonDto) {
    if (dto.translations) {
      dto.translations = dto.translations.map((e) => ({ ...e, language: e.language.toUpperCase() }));
    }
    const missReason = await this.missReasonModel.create({
      ...dto,
      reschedulable: dto.controllable,
    });

    if (dto.locations && dto.locations.length) {
      const defaultDisplayedLocations = Object.values(MissReasonLocation);
      const displayedLocations = await this.missReasonDisplayedLocationModel.find({
        location: {
          $in: missReason.locations,
        },
      });
      const displayedLocationsMapByLocation = defaultDisplayedLocations.reduce((acc, location) => {
        const displayedLocation = displayedLocations.find((displayedLocation) => displayedLocation.location === location);
        const missReasons = displayedLocation ? displayedLocation.missReasons.map((item) => String(item)) : [];

        return {
          ...acc,
          [location]: missReasons,
        };
      }, {} as Record<MissReasonLocation, string[]>);

      await Promise.all(
        dto.locations.map((location) =>
          this.missReasonDisplayedLocationModel.updateOne(
            { location },
            { $set: { missReasons: [...displayedLocationsMapByLocation[location], missReason._id] } },
            { upsert: true },
          ),
        ),
      );
    }

    return missReason;
  }

  async update(id: string, dto: UpdateMissReasonDto, i18n: I18nContext) {
    if (dto.translations) {
      this.ensureNotDuplicateTranslation(dto.translations, i18n);
      dto.translations = dto.translations.map((e) => ({ ...e, language: e.language.toUpperCase() }));
    }

    const missReason = await this.missReasonModel.findById(id);

    if (!missReason) {
      throw new NotFoundException(i18n.t('missReason.notFound'));
    }

    const updatedMissReason = await this.missReasonModel.findByIdAndUpdate(id, { ...dto, reschedulable: dto.controllable });

    await this.updateLocationWhenUpdateMissReason(missReason, dto);

    if (dto.isActive === false) {
      // TODO: remove missedReason in all JP, Absence
    }

    return {
      ...updatedMissReason.toJSON(),
      ...dto,
    };
  }

  async updateDisplayedLocation(dto: UpdateMissReasonDisplayedLocationDto, i18n: I18nContext) {
    // const missReasons = await this.missReasonModel.find({
    //   locations: dto.location,
    // });

    // const missReasonIds = missReasons.map((missReason) => String(missReason._id));

    // const isValidPayload = missReasonIds.length === dto.missReasonIds.length && missReasonIds.every((id) => dto.missReasonIds.includes(id));
    // if (!isValidPayload) {
    //   throw new BadRequestException(i18n.t('missReason.dataDoesNotMatchToDatabase'));
    // }

    const displayedLocation = await this.missReasonDisplayedLocationModel.findOneAndUpdate(
      {
        location: dto.location,
      },
      { missReasons: dto.missReasonIds },
      { upsert: true },
    );

    return {
      ...displayedLocation.toJSON(),
      missReasons: dto.missReasonIds,
    };
  }

  async migrateOldData(i18n: I18nContext) {
    const oldMissReasons = await this.journeyPlanMissedReasonModel.find();
    const formattedOldMissReasons = oldMissReasons.map((missReason) => {
      const translations = this.getTranslations(missReason, i18n);
      const locations = missReason.isForMultipleOutlets ? [MissReasonLocation.REPORT_ABSENCES] : [MissReasonLocation.REPORT_OUTLET];

      return {
        id: missReason._id,
        isActive: true,
        requireEvidenceSettings: missReason.isForMultipleOutlets
          ? {}
          : {
              [MissReasonLocation.REPORT_OUTLET]: true,
            },
        controllable: missReason.controllable,
        reschedulable: missReason.controllable,
        locations,
        translations,
        priority: missReason.priority,
      };
    });

    const locations = Object.values(MissReasonLocation).map((location) => {
      const missReasons = formattedOldMissReasons.filter((reason) => reason.locations.includes(location));

      return {
        location,
        missReasons: missReasons.sort((a, b) => a.priority - b.priority).map((item) => new Types.ObjectId(item.id)),
      };
    });

    await Promise.all(
      formattedOldMissReasons.map(({ id, controllable, isActive, requireEvidenceSettings, reschedulable, locations, translations }) =>
        this.missReasonModel.updateOne(
          { _id: id },
          {
            controllable,
            isActive,
            requireEvidenceSettings,
            reschedulable,
            locations,
            translations,
          },
          { upsert: true },
        ),
      ),
    );
    await Promise.all(
      locations.map((item) =>
        this.missReasonDisplayedLocationModel.updateOne(
          { location: item.location },
          {
            missReasons: item.missReasons,
          },
          { upsert: true },
        ),
      ),
    );
  }

  private getTranslations(missReason: JourneyPlanMissedReason, i18n: I18nContext) {
    const reasonKey = missReason.reasonKey;
    const warningKey = missReason.warningMessageKey;
    let listLang = new Set([process.env.DEFAULT_LANGUAGE, 'en', 'my', 'id', 'km']);
    const translations = [];
    for (const lang of listLang.values()) {
      const { reason: reasonEN, warning: warningEN } = this.getTranslation({ reasonKey, warningKey, language: 'en', i18n });
      const { reason, warning } = this.getTranslation({ reasonKey, warningKey, language: lang, i18n });
      if (reason) {
        translations.push({
          language: lang.toUpperCase(),
          reason: reason,
          warning: warning,
          isDefault: true,
        });
      } else if (reasonEN) {
        translations.push({
          language: lang.toUpperCase(),
          reason: reasonEN,
          warning: warningEN,
          isDefault: true,
        });
      }
    }

    return translations;
  }

  private getTranslation({ reasonKey, warningKey, language, i18n }: { reasonKey: string; warningKey: string; language: string; i18n: I18nContext }) {
    let reason = '';
    let warning = '';
    try {
      reason = i18n.t(reasonKey, { lang: language });
      warning = i18n.t(warningKey, { lang: language });
    } catch {
      // do nothing
    }
    return {
      reason,
      warning,
    };
  }

  private async updateLocationWhenUpdateMissReason(missReason: MissReason, dto: UpdateMissReasonDto) {
    if (!dto.locations || (!dto.locations.length && missReason.locations.length)) {
      return;
    }

    const missReasonId = String(missReason._id);
    const addedLocations = dto.locations.filter((location) => !missReason.locations.includes(location));
    const removedLocations = missReason.locations.filter((location) => !dto.locations.includes(location));

    const defaultDisplayedLocations = Object.values(MissReasonLocation);
    const displayedLocations = await this.missReasonDisplayedLocationModel.find();

    const displayedLocationsMapByLocation = defaultDisplayedLocations.reduce((acc, location) => {
      const displayedLocation = displayedLocations.find((item) => item.location === location);
      const missReasons = displayedLocation ? displayedLocation.missReasons.map((item) => String(item)) : [];

      return {
        ...acc,
        [location]: missReasons,
      };
    }, {} as Record<MissReasonLocation, string[]>);

    const promises = [];
    addedLocations.map(async (location) => {
      const oldMissReasons = displayedLocationsMapByLocation[location] || [];
      if (!oldMissReasons.includes(missReasonId)) {
        promises.push(
          this.missReasonDisplayedLocationModel.updateOne(
            {
              location: location,
            },
            {
              $set: {
                missReasons: [...oldMissReasons, missReasonId],
              },
            },
            {
              upsert: true,
            },
          ),
        );
      }
    });

    removedLocations.map(async (location) => {
      const oldMissReasons = displayedLocationsMapByLocation[location] || [];

      if (oldMissReasons.includes(missReasonId)) {
        promises.push(
          this.missReasonDisplayedLocationModel.updateOne(
            {
              location: location,
            },
            {
              $set: {
                missReasons: oldMissReasons.filter((item) => item !== missReasonId),
              },
            },
          ),
        );
      }
    });

    if (!promises.length) {
      return;
    }

    await Promise.all(promises);
  }

  private ensureNotDuplicateTranslation(translations: MissReasonTranslation[], i18n: I18nContext) {
    const appeareanceTime = translations.reduce((acc, curr) => ({ ...acc, [curr.language]: (acc[curr.language] || 0) + 1 }), {} as Record<string, number>);

    if (Object.values(appeareanceTime).some((count) => count > 1)) {
      throw new BadRequestException(i18n.t('missReason.translationLanguageDuplicated'));
    }
  }
}
