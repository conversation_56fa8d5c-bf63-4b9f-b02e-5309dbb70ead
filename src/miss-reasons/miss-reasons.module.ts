import { Module, forwardRef } from '@nestjs/common';
import { MissReasonsController } from './miss-reasons.controller';
import { MissReasonsService } from './miss-reasons.service';
import { MissReason, MissReasonSchema } from './schemas/miss-reason.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { UsersModule } from 'src/users/users.module';
import { MissReasonDisplayedLocation, MissReasonDisplayedLocationSchema } from './schemas/miss-reason-displayed-location.schema';
import { JourneyPlanMissedReason, JourneyPlanMissedReasonSchema } from 'src/journey-plannings/schemas/journey-plan-missed-reason.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: MissReason.name, schema: MissReasonSchema },
      { name: MissReasonDisplayedLocation.name, schema: MissReasonDisplayedLocationSchema },
      { name: JourneyPlanMissedReason.name, schema: JourneyPlanMissedReasonSchema },
    ]),
    forwardRef(() => UsersModule),
    AuthModule,
  ],
  controllers: [MissReasonsController],
  exports: [MissReasonsService],
  providers: [MissReasonsService],
})
export class MissReasonsModule {}
