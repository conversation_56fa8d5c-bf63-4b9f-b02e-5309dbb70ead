declare global {
  namespace Express {
    interface Request {
      currentUser?: User;
    }
  }
  namespace NodeJS {
    interface ProcessEnv {
      VERSION: string;
      NODE_ENV: string;
      DEBUG_MODE: string;
      TZ: string;
      DEFAULT_LANGUAGE: string;
      MIGRATION_MODE: string;
      PORT: string;
      APP_TIME_OUT: string;
      JWT_PRIVATE_KEY: string;
      JWT_EXPIRED_TIME: string;
      BASE_URL: string;
      PN_PROJECT_ID: string;
      PN_PRIVATE_KEY: string;
      PN_CLIENT_EMAIL: string;
      CACHE_MODE: string;
      REDIS_HOST: string;
      REDIS_PORT: string;
      REDIS_PASSWORD: string;
      REDIS_TTL: string;
      COUNTRY_CODE: string;
      PHONE_COUNTRY_CODE_DEFAULT: string;
      PHONE_COUNTRY_CODES: string;
      MONGODB_URL: string;
      MONGO_DB_NAME: string;
      MONGO_DB_USER: string;
      MON<PERSON><PERSON>_DB_PASS: string;
      EXPIRED_TIME_REMEMBER_LOGGED_IN: string;
      // Job expression
      MISSED_VISIT_OUTLET_EXPRESSION: string;
      CREATE_STATISTIC_SALES_EXPRESSION: string;
      CLONE_JOURNEY_PLAN_EXPRESSION: string;
      DELETE_EXPIRED_FILES_EXPRESSION: string;
      CREATE_CYCLE_TIME: string;
      // Solace
      SOLACE_URL: string;
      SOLACE_VPN_NAME: string;
      SOLACE_USERNAME: string;
      SOLACE_PASSWORD: string;
      SOLACE_OUTLET_QUEUE_NAME: string;
      SOLACE_SALES_REP_QUEUE_NAME: string;
      SOLACE_TOPIC_NAME: string;
      SOLACE_PUSH_URL: string;
      SOLACE_PUSH_VPN_NAME: string;
      SOLACE_PUSH_USERNAME: string;
      SOLACE_PUSH_PASSWORD: string;
      SOLACE_PUSH_TOPIC_NAME: string;
      SOLACE_BUSINESS_PARTNER_TOPIC_NAME: string;
      SOLACE_BUSINESS_PARTNER_CONTACT_TOPIC_NAME: string;
      OPCO: string;
      // Azure storage
      AZURE_STORAGE_SAS_KEY: string;
      AZURE_STORAGE_ACCOUNT: string;
      AZURE_STORAGE_CONTAINER_NAME: string;
      OMS_API_BASE_URL: string;
      OMS_EMAIL: string;
      OMS_PASSWORD: string;
      MUST_HAVE_SKU_EXCEL_URL: string;
      SMSPOH_ENDPOINT_URL: string;
      SMSPOH_SENDER_NAME: string;
      SMSPOH_API_KEY: string;
      SMSPOH_SECRET: string;
      SENDGRID_API_KEY: string;
      BUSINESS_PARTNER_REQUEST_TARGET_EMAILS: string;
      POSTGRES_POOL_SIZE: string;
      MONGO_POOL_SIZE: string;
      TWILIO_ACCOUNT_SID: string;
      TWILIO_AUTH_TOKEN: string;
      TWILIO_PHONE_NUMBER: string;
      TWILIO_TWIML_APP_ID: string;
      TWILIO_API_TOKEN: string;
      TWILIO_API_SECRET: string;
      TWILIO_RECORD_STATUS_CALLBACK: string;
      TWILIO_TWIML_CALLBACK: string;
      TWILIO_ACTION_URL: string;
      OOREDOO_SBC_IP: string;
      OOREDOO_SBC_REGION: string;
      SYNC_OMS_METRICS: string;
    }
  }
}

export {};
