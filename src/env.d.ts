declare global {
  namespace Express {
    interface Request {
      currentUser?: User;
    }
  }
  namespace NodeJS {
    interface ProcessEnv {
      VERSION: string;
      NODE_ENV: string;
      DEBUG_MODE: string;
      TZ: string;
      DEFAULT_LANGUAGE: string;
      MIGRATION_MODE: string;
      PORT: string;
      APP_TIME_OUT: string;
      JWT_PRIVATE_KEY: string;
      JWT_EXPIRED_TIME: string;
      BASE_URL: string;
      PN_PROJECT_ID: string;
      PN_PRIVATE_KEY: string;
      PN_CLIENT_EMAIL: string;
      CACHE_MODE: string;
      REDIS_HOST: string;
      REDIS_PORT: string;
      REDIS_PASSWORD: string;
      REDIS_TTL: string;
      COUNTRY_CODE: string;
      PHONE_COUNTRY_CODE_DEFAULT: string;
      PHONE_COUNTRY_CODES: string;
      MONGODB_URL: string;
      MONGO_DB_NAME: string;
      MONGO_DB_USER: string;
      MON<PERSON><PERSON>_DB_PASS: string;
      EXPIRED_TIME_REMEMBER_LOGGED_IN: string;
      // Job expression
      MISSED_VISIT_OUTLET_EXPRESSION: string;
      CREATE_STATISTIC_SALES_EXPRESSION: string;
      CLONE_JOURNEY_PLAN_EXPRESSION: string;
      DELETE_EXPIRED_FILES_EXPRESSION: string;
      CREATE_CYCLE_TIME: string;
      // Solace
      SOLACE_URL: string;
      SOLACE_VPN_NAME: string;
      SOLACE_USERNAME: string;
      SOLACE_PASSWORD: string;
      SOLACE_OUTLET_QUEUE_NAME: string;
      SOLACE_SALES_REP_QUEUE_NAME: string;
      SOLACE_TOPIC_NAME: string;
      OPCO: string;
      // Azure storage
      AZURE_STORAGE_SAS_KEY: string;
      AZURE_STORAGE_ACCOUNT: string;
      AZURE_STORAGE_CONTAINER_NAME: string;
      OMS_API_BASE_URL: string;
      OMS_EMAIL: string;
      OMS_PASSWORD: string;
      MUST_HAVE_SKU_EXCEL_URL: string;
      SMSPOH_ENDPOINT_URL: string;
      SMSPOH_SENDER_NAME: string;
      SMSPOH_API_KEY: string;
      SMSPOH_SECRET: string;
      SYNC_OMS_METRICS: string;
    }
  }
}

export {};
