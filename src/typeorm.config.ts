import { DataSource, DataSourceOptions } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';
import * as fs from 'fs';

config();
const configService = new ConfigService();
const poolSize = Number(configService.get('POSTGRES_POOL_SIZE') || 5);
const isDevelop = !['production', 'development', 'staging'].includes(configService.get<string>('NODE_ENV') ?? '');

console.log({
  env: configService.get('NODE_ENV'),
  certPath: configService.get('POSTGRES_CERT_PATH'),
  password: configService.get('POSTGRES_PASSWORD'),
  isDevelop,
});

const certPath = configService.get<string>('POSTGRES_CERT_PATH');
if (!isDevelop && !certPath) {
  throw new Error('POSTGRES_CERT_PATH is required in non-dev environments');
}

const typeOrmConfig: DataSourceOptions = {
  type: 'postgres',
  host: configService.get<string>('POSTGRES_HOST'),
  port: Number(configService.get<string>('POSTGRES_PORT') || 5432),
  username: configService.get<string>('POSTGRES_USER'),
  password: configService.get<string>('POSTGRES_PASSWORD'),
  database: configService.get<string>('POSTGRES_DATABASE'),
  entities: configService.get<string>('MIGRATION_MODE') === 'true' ? ['**/*.entity.ts'] : ['**/*.entity.js'],
  ssl: isDevelop
    ? false
    : {
        rejectUnauthorized: true,
        ca: fs.readFileSync(certPath!).toString(),
      },
  synchronize: isDevelop,
  migrations: ['src/migrations/db/*.ts'],
  connectTimeoutMS: 90000,
  maxQueryExecutionTime: 90000,
  extra: {
    max: poolSize,
  },
  cache:
    configService.get<string>('CACHE_MODE') === 'true'
      ? {
          type: 'redis',
          options: {
            host: configService.get<string>('REDIS_HOST'),
            port: Number(configService.get<string>('REDIS_PORT') || 6379),
            auth_pass: configService.get<string>('REDIS_PASSWORD'),
            password: configService.get<string>('REDIS_PASSWORD'),
            tls:
              configService.get<string>('REDIS_TLS') === 'true'
                ? {
                    host: configService.get<string>('REDIS_HOST'),
                  }
                : false,
            retryStrategy: (times: number) => {
              const delay = Math.min(times * 50, 2000);
              return delay;
            },
            maxRetriesPerRequest: 3,
            connectTimeout: 10000,
            commandTimeout: 10000,
            enableOfflineQueue: true,
            showFriendlyErrorStack: true,
          },
          ignoreErrors: true,
        }
      : false,
};

const AppDataSource = new DataSource(typeOrmConfig);

export { AppDataSource, typeOrmConfig };
