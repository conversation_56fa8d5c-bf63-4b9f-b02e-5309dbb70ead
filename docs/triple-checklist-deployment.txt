-----------------------------------------------------------------------------------------
PREPARE: (first time)
1. Setup software
2. Artifact Feed
3. Azure CLI
-----------------------------------------------------------------------------------------
1.1: Software
    - NodeJS (All):
        + Install nvm (node version manager)
        + Install node 16 lts
    - Install PM2 manager (API)
    - Install Redis server (API)
    - Install Webserver (All): Nginx
    - Install MongoDB 6.x
1.2: Setup
    Webserver: Nginx:
    -------------------
    - Domain and SSL setup
    - Setup host config file
    - Check and Test Nginx config (nginx.conf):
        + Check forwarding client IP
        + Max File Size Settings
        + Max request
        + Max upload

    API:
    ----
    - Make folder for Backend API app: [path]DSRBackend
      + Correct .env setting: [path]/DSRBackend/.env (please copy from .env.example file)
      + Correct database setting
      + npm install
      + Nginx config sample:
      server {
              server_name dsr-be-domain;

              location / {
                  gzip_static on;
                  rewrite ^/route/?(.*)$ /$1 break;
                  proxy_pass http://localhost:8000;
                  proxy_http_version 1.1;
                  proxy_set_header X-Forwarded-Host $host;
                  proxy_set_header X-Forwarded-Server $host;
                  proxy_set_header X-Real-IP $remote_addr;
                  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                  proxy_set_header X-Forwarded-Proto $scheme;
                  proxy_set_header Host $http_host;
                  proxy_set_header Upgrade $http_upgrade;
                  proxy_set_header Connection "Upgrade";
                  proxy_pass_request_headers on;
              }
              ............other configs
     }
    Web:
    ----
    - Make folder for Web React app: [path]/DSRFrontend
      + Correct .env setting: [path]/DSRFrontend/.env (please copy from .env.example file)
      + npm install
      + Nginx config
      server {
            server_name dsr-fe-domain;

            location / {
                gzip_static on;
                rewrite ^/route/?(.*)$ /$1 break;
                proxy_pass http://localhost:8080;
                proxy_http_version 1.1;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Forwarded-Server $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header Host $http_host;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "Upgrade";
                proxy_pass_request_headers on;
            }
            ............other configs
      }

2. Artifact Feed
    - Triple
3. Azure CLI
    - Niteco have to permission to access to Artifact Feed
    + Login via Azure CLI
    + Upload package via Azure CLI
-----------------------------------------------------------------------------------------
DEPLOYMENT:
-----------------------------------------------------------------------------------------
API:
+ Clone source code from git repo https://heineken.visualstudio.com/DSR%20App/_git/DSR%20-%20Backend
+ Checkout to prod branch
Web:
+ Clone source code from git repo https://heineken.visualstudio.com/DSR%20App/_git/DSR%20-%20Web
+ Checkout to prod branch

- Make sure the .env file is correct with place holder data
- Compress to zip file
- Connect to Artifact Feed via Azure CLI
- Upload package to Feed:
 + correct the version
 + add a description


